package com.bilibili.mgk.material.center.service.creative.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.github.pagehelper.Page;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/**
 * @ClassName Pagination
 * <AUTHOR>
 * @Date 2022/6/19 3:59 下午
 * @Version 1.0
 **/
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class Pagination<E> {

    private static final Pagination EMPTY = new Pagination<>(1, 0, Collections.emptyList());
    private Integer page;
    private Integer total_count;
    private E data;

    public Pagination(Integer page, Integer total_count, E data) {
        this.page = page;
        this.total_count = total_count;
        this.data = data;
    }

    public static <E> Pagination<E> emptyPagination() {
        return EMPTY;
    }

    public static <T> Pagination<List<T>> fromPageHelper(Page<T> page) {

        return new Pagination<List<T>>(
                page.getPageNum(),
                (int) page.getTotal(),
                page.getResult()
        );
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getTotal_count() {
        return total_count;
    }

    public void setTotal_count(Integer total_count) {
        this.total_count = total_count;
    }

    public E getData() {
        return data;
    }

    public void setData(E data) {
        this.data = data;
    }

    public <T> Pagination<T> map(Function<E, T> mapping) {
        return new Pagination<>(this.page, this.total_count,

                Optional.ofNullable(data)
                        .map(mapping)
                        .orElse(null)
        );
    }

}

