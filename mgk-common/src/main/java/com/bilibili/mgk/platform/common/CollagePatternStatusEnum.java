package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018/11/15
 **/
@AllArgsConstructor
public enum CollagePatternStatusEnum {

    USING(1, "有效") {
        @Override
        public boolean validateStatus(CollagePatternStatusEnum toStatus) {
            return DELETED.equals(toStatus);
        }
    },
    DELETED(0, "无效") {
        @Override
        public boolean validateStatus(CollagePatternStatusEnum toStatus) {
            return USING.equals(toStatus);
        }
    };

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static CollagePatternStatusEnum getByCode(int code) {
        for (CollagePatternStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("未知的模版状态类型: " + code);
    }

    public abstract boolean validateStatus(CollagePatternStatusEnum toStatus);
}
