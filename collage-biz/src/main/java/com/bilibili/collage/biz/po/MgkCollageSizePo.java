package com.bilibili.collage.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkCollageSizePo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 宽
     */
    private Integer width;

    /**
     * 高
     */
    private Integer height;

    /**
     * 示意图
     */
    private String sketch;

    /**
     * 适合广告位
     */
    private String des;

    /**
     * 状态: 1-有效 0-无效
     */
    private Integer status;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 贴图尺寸大小版本 0-旧版本 1-新版本
     */
    private Integer edition;

    /**
     * 尺寸类型 0-模板 1-gif
     */
    private Integer sizeType;

    private static final long serialVersionUID = 1L;
}