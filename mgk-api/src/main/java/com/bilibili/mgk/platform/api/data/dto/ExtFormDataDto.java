package com.bilibili.mgk.platform.api.data.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtFormDataDto implements Serializable{
    private static final long serialVersionUID = 4686930796269806937L;
    /**
     * 表单ID
     */
    private Long formId;


    /**
     * count
     */
    private Integer formDataCount;

    /**
     * 最近提交数据的时间
     */
    private Timestamp recentSubmitTime;
}
