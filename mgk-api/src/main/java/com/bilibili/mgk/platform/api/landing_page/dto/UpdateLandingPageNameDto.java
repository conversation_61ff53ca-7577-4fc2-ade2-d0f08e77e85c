package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName UpdateLandingPageNameDto
 * <AUTHOR>
 * @Date 2022/12/23 5:02 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateLandingPageNameDto implements Serializable {

    private Integer accountId;
    private Long pageId;
    private String pageName;

}
