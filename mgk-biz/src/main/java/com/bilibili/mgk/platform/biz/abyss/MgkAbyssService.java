package com.bilibili.mgk.platform.biz.abyss;

import com.alibaba.fastjson.JSON;
import com.bilibili.bcg.abyss.sdk.AbyssPlot;
import com.bilibili.bcg.abyss.sdk.AbyssRichPath;
import com.bilibili.bcg.abyss.sdk.AbyssSampleRequest;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.mgk.platform.api.abyss.IMgkAbyssService;
import com.bilibili.mgk.platform.api.abyss.bo.PageConfigQueryBO;
import com.bilibili.mgk.platform.api.abyss.bo.PageConfigResBO;
import com.bilibili.mgk.platform.biz.redis.RedisService;
import com.bilibili.mgk.platform.common.H5DownloadStrategyEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.mysema.commons.lang.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class MgkAbyssService implements IMgkAbyssService {

    @Autowired
    private AbyssPlot launchAbyssPlot;

    @Autowired
    private AbyssPlot allAbyssPlot;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ISoaAccountLabelService crmAccountLabelService;

    @Value("${mgk.download.extra.link.download.button:873}")
    private Integer downLoadExtraLinkDownloadButton;

    @Override
    public PageConfigResBO queryPageConfig(PageConfigQueryBO queryBO) {
        try {
            AbyssSampleRequest request = new AbyssSampleRequest();
            request.setBuvid(queryBO.getBuvid());
            request.setMid(queryBO.getMid());
            AbyssPlot.Request request1 = AbyssPlot.Request.builder().build();
            AbyssRichPath path = launchAbyssPlot.selectPathByPojo(request1, request);

            /*
             * 推荐使用，性能最优
             * common 是 module key
             * hit 是module body 里的key
             */
            Optional<JsonNode> hitOptional = path.getValueAsJsonNode("common", "hit");
            int exp_hit = hitOptional.map(JsonNode::asInt).orElse(0);

            Optional<JsonNode> preloadHitOptional = path.getValueAsJsonNode("common", "preload_hit");
            int preload_hit = preloadHitOptional.map(JsonNode::asInt).orElse(0);
            return PageConfigResBO.builder().exp_hit(exp_hit).preload_hit(preload_hit).build();
        } catch (Exception e){
            log.error("queryPageConfig error [{}]", ExceptionUtils.getSubStringMsg(e));
        }

        return PageConfigResBO.builder().build();
    }

    @Override
    public int needAddBottomDownloadButton(int accountId, int customer) {
        if (crmAccountLabelService.checkAccountWithLabel(accountId, downLoadExtraLinkDownloadButton)) {
            return WhetherEnum.NO.getCode();
        }

        try {
            Pair<Boolean, Integer> check1 = checkH5DownloadStrategy(H5DownloadStrategyEnum.ENABLE, accountId, customer);
            if(check1.getFirst()){
                return check1.getSecond();
            }

            Pair<Boolean, Integer> check2 = checkH5DownloadStrategy(H5DownloadStrategyEnum.DISABLE_BY_ACCOUNT, accountId, customer);
            if(check2.getFirst()){
                return check2.getSecond();
            }

            Pair<Boolean, Integer> check3 = checkH5DownloadStrategy(H5DownloadStrategyEnum.DISABLE_BY_CUSTOMER, accountId, customer);
            if(check3.getFirst()){
                return check3.getSecond();
            }
        } catch (Exception e){
            log.error("queryPageConfig error [{}]", ExceptionUtils.getSubStringMsg(e));
        }
        return WhetherEnum.YES.getCode();
    }


    /*
     *h5小程序实验过滤
     * @Return 是否经过了处理-处理结果
     */
    private Pair<Boolean, Integer> checkH5DownloadStrategy(H5DownloadStrategyEnum strategy, int accountId, int customerId){
        String cache = redisService.getCache(strategy.getRedisKey());
        log.info("cache = {}, strategy={}", cache,strategy);
        int compare = strategy.getCode().equals(H5DownloadStrategyEnum.DISABLE_BY_CUSTOMER.getCode())
                ? customerId : accountId;
        if(!StringUtils.isEmpty(cache)){
            String[] items = cache.split(",");
            for (String item : items){
                if(Integer.parseInt(item) == compare){
                    return Pair.of(true, strategy.getH5DownloadHit());
                }
            }
        }else {
            AbyssSampleRequest request = new AbyssSampleRequest();
            AbyssPlot.Request plotRequest = AbyssPlot.Request.builder().build();
            AbyssRichPath path = allAbyssPlot.selectPathByPojo(plotRequest, request);
            Optional<JsonNode> optional = path.getValueAsJsonNode("common", strategy.getAbyssKey());
            if (optional.isPresent()) {
                List<Integer> itemList = new ArrayList<>();
                Iterator<JsonNode> iterator = optional.get().elements();
                while (iterator.hasNext()) {
                    itemList.add(iterator.next().asInt());
                }
                log.info("itemList = {},strategy={}", JSON.toJSONString(itemList),strategy);
                redisService.addCache(strategy.getRedisKey(), StringUtils.join( itemList, ","), 600);
                if (itemList.contains(compare)) {
                    return Pair.of(true, strategy.getH5DownloadHit());
                }
            }
        }
        return Pair.of(false, null);
    }

}
