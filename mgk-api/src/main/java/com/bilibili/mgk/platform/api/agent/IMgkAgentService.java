package com.bilibili.mgk.platform.api.agent;


import com.bilibili.mgk.platform.api.agent.dto.AgentDto;
import com.bilibili.mgk.platform.api.agent.dto.SecondaryAgentDto;

import java.util.List;
import java.util.Map;

/**
 * @ClassName IMgkAgentService
 * <AUTHOR>
 * @Date 2022/6/20 11:42 下午
 * @Version 1.0
 **/
public interface IMgkAgentService {

    AgentDto getBuAgentByAccountId(Integer accountId);

    SecondaryAgentDto getSecondaryAgentSimpleDtoById(Integer id, Long bid) throws Exception;

    List<Integer> getSecondaryAgentAccountIds(Integer secondaryAgentId);

    AgentDto getBuAgentByAgentId(Integer agentId);

    Map<Integer, AgentDto> getAgentMapInIds(List<Integer> agentIds);
}
