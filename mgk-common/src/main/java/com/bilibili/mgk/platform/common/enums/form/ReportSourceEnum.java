package com.bilibili.mgk.platform.common.enums.form;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/5/23
 **/
public enum ReportSourceEnum {
    CREATIVE_CENTER(1, "创作中心"),
    MY(2, "我的"),
    MUST_HOT(3, "必火banner"),
    MUST_HOT_POP_UP(4, "必火弹窗"),
    VIDEO_BLUE_LINK(5, "视频蓝链"),
    PRIVACY_BANNER(6, "私域打击"),
    UP_MESSAGE_ASS(7, "up小助手私信"),
    DYNAMIC_CONTENT(8, "动态内容"),
    MANAGER_ASS(9, "经营助手平台"),
    ;

    /**
     * id
     */
    private Integer Id;

    /**
     * desc
     */
    private String desc;

    ReportSourceEnum(Integer id, String desc) {
        Id = id;
        this.desc = desc;
    }

    public Integer getId() {
        return Id;
    }

    public String getDesc() {
        return desc;
    }
}
