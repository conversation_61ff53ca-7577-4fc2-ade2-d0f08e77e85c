<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.ext.ExtMgkPageBizMappingDao">
    <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkPageBizMappingPo">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="page_id" jdbcType="BIGINT" property="pageId" />
        <result column="biz_id" jdbcType="INTEGER" property="bizId" />
        <result column="biz_index" jdbcType="INTEGER" property="bizIndex" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, page_id, biz_id, biz_index, is_deleted, ctime, mtime
    </sql>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into
        mgk_page_biz_mapping
        (page_id,biz_id,biz_index,is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.pageId,jdbcType=BIGINT},
                #{item.bizId,jdbcType=INTEGER},
                #{item.bizIndex,jdbcType=INTEGER},
                #{item.isDeleted,jdbcType=TINYINT},
            </trim>
        </foreach>
    </insert>
    <insert id="insertUpdateBatch" parameterType="java.util.List">
        insert into
        mgk_page_biz_mapping
        (page_id,biz_id,biz_index,is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.pageId,jdbcType=BIGINT},
                #{item.bizId,jdbcType=INTEGER},
                #{item.bizIndex,jdbcType=INTEGER},
                #{item.isDeleted,jdbcType=TINYINT},
            </trim>
        </foreach>
        <trim prefix="on duplicate key update" suffixOverrides=",">
            page_id = values(page_id),
            biz_id = values(biz_id),
            biz_index = values(biz_index),
            is_deleted = values(is_deleted),
        </trim>
    </insert>
</mapper>