package com.bilibili.mgk.platform.common.video_library;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/06/02
 */
@AllArgsConstructor
public enum MgkVideoLogOperateType {

    /**
     * 未知
     */
    UNKNOWN(99,"未知"),
    /**
     * 新建视频
     */
    ADD(1, "新建视频"),
    /**
     * 修改视频
     */
    UPDATE(2, "修改视频"),
    /**
     * 删除视频
     */
    DELETE(3, "删除视频"),
    /**
     * 修改视频状态
     */
    UPDATE_STATUS(4, "修改视频状态");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static MgkVideoLogOperateType getByCode(Integer code) {
        for (MgkVideoLogOperateType bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return UNKNOWN;
    }
}
