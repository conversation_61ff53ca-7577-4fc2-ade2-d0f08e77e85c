package com.bilibili.collage.biz.utils;

import com.bilibili.collage.api.annotation.Document;
import com.bilibili.mgk.platform.common.enums.AggregationTypeEnum;
import io.searchbox.core.Count;
import io.searchbox.core.Search;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.util.Assert;


public class Es5Utils {

    private Es5Utils() {
        throw new IllegalStateException("Utility class");
    }

    public static final String SCROLL_WINDOW_TIME = "1m";

    public static class AggregationQuery {
        private String fieldName;
        private String resultName;
        private AggregationTypeEnum type;

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getResultName() {
            return resultName;
        }

        public void setResultName(String resultName) {
            this.resultName = resultName;
        }

        public AggregationTypeEnum getType() {
            return type;
        }

        public void setType(AggregationTypeEnum type) {
            this.type = type;
        }
    }


    public static SearchSourceBuilder buildSearchSourceBuilderWithPage(SearchQueryInfo searchQueryInfo, int from, int size) {
        QueryBuilder qb = QueryBuilders.boolQuery()
                .must(searchQueryInfo.getQueryBuilder())
                .filter(searchQueryInfo.getFilterBuilder());

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(qb)
                .from(from)
                .size(size);

        for (SortBuilder sortBuilder : searchQueryInfo.getSortBuilders()) {
            searchSourceBuilder.sort(sortBuilder);
        }

        return searchSourceBuilder;
    }

    public static SearchSourceBuilder buildSearchSourceBuilderWithoutPage(SearchQueryInfo searchQueryInfo) {
        QueryBuilder qb = QueryBuilders.boolQuery()
                .must(searchQueryInfo.getQueryBuilder())
                .must(searchQueryInfo.getFilterBuilder());

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(qb);

        return searchSourceBuilder;
    }


    public static <T> Search buildQueryForPage(SearchQueryInfo searchQueryInfo, int pageNum, int size, Class<T> entityClazz) {
        Assert.isTrue(pageNum > 0, "pageNum must gt zero");
        Document annotation = entityClazz.getAnnotation(Document.class);
        int from = (pageNum - 1) * size;
        String query = buildSearchSourceBuilderWithPage(searchQueryInfo, from, size).toString();
        return new Search.Builder(query)
                .addIndex(annotation.indexName())
                .addType(annotation.type())
                .build();
    }

    public static <T> Count buildCount(SearchQueryInfo searchQueryInfo, Class<T> entityClazz) {
        Document annotation = entityClazz.getAnnotation(Document.class);

        String query = buildSearchSourceBuilderWithoutPage(searchQueryInfo).toString();
        return new Count.Builder()
                .query(query)
                .addIndex(annotation.indexName())
                .addType(annotation.type())
                .build();
    }


}
