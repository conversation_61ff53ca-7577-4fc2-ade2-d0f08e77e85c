package com.bilibili.mgk.platform.biz.ad.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccAccountMidPo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 主站mid
     */
    private Long mid;

    /**
     * 用户属性 0个人用户 1机构用户 2个人起飞用户
     */
    private Integer userType;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除 0 否 1是
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}