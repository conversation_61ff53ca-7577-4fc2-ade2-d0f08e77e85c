package com.bilibili.mgk.platform.api.landing_page_group.dto;

import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingSaveDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName LandingPageGroupUpdateDto
 * <AUTHOR>
 * @Date 2023/5/17 2:40 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupUpdateDto {

    private Long groupId;

    private String name;

    private Integer auditCreativeId;

    private LandingPageGroupMappingSaveDto mappingSaveDto;
}
