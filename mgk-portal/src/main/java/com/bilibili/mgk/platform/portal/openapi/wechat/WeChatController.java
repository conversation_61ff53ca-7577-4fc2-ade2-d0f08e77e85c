package com.bilibili.mgk.platform.portal.openapi.wechat;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.util.WeChatSign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/07/15
 **/
@Controller
@RequestMapping("/open_api/v1/wechat_sign")
@Api(value = "/wechat_sign", description = "微信签名接口")
public class WeChatController {

    @Autowired
    private IMgkLandingPageService mgkLandingPageService;

    @ApiOperation(value = "微信分享签名")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Map<String, String>> weChatSign(@ApiIgnore Context context,
                                             @RequestParam(value = "url") String url) throws ServiceException {
        String ticket = mgkLandingPageService.getWeChatSign();
        Map<String, String> sign = WeChatSign.sign(ticket, url);
        return Response.SUCCESS(sign);
    }

    public static void main(String[] args) {
        Map<String, String> sing = new HashMap<>();
        sing.put("aa", "11");
        sing.put("ab", "21");
        System.out.println(sing);
    }
}
