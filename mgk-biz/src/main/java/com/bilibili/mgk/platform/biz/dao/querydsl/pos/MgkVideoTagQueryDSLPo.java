package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * MgkVideoTagQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class MgkVideoTagQueryDSLPo {

    private java.sql.Timestamp ctime;

    private java.sql.Timestamp firstUploadTime;

    private Integer id;

    private String md5;

    private java.sql.Timestamp mtime;

    private Integer provider;

    private String rawMd5;

    private Integer source;

    private Integer style;

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public java.sql.Timestamp getFirstUploadTime() {
        return firstUploadTime;
    }

    public void setFirstUploadTime(java.sql.Timestamp firstUploadTime) {
        this.firstUploadTime = firstUploadTime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getProvider() {
        return provider;
    }

    public void setProvider(Integer provider) {
        this.provider = provider;
    }

    public String getRawMd5() {
        return rawMd5;
    }

    public void setRawMd5(String rawMd5) {
        this.rawMd5 = rawMd5;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getStyle() {
        return style;
    }

    public void setStyle(Integer style) {
        this.style = style;
    }

    @Override
    public String toString() {
         return "ctime = " + ctime + ", firstUploadTime = " + firstUploadTime + ", id = " + id + ", md5 = " + md5 + ", mtime = " + mtime + ", provider = " + provider + ", rawMd5 = " + rawMd5 + ", source = " + source + ", style = " + style;
    }

}

