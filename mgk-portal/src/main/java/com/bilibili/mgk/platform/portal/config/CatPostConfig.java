package com.bilibili.mgk.platform.portal.config;

import com.bilibili.bjcom.disconf.cat.spring.CatUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.CatConstants;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.plexus.component.repository.exception.ComponentLookupException;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;

/**
 * Created by fanwen<PERSON> on 2017/8/2.
 */
@Slf4j
@Component
public class CatPostConfig implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        File configFile;
        try {
            configFile = new ClassPathResource("cat.xml").getFile();
            System.setProperty(CatConstants.PROPERTY_CLIENT_CONFIG, configFile.getAbsolutePath());
            CatUtils.reInit(false);

            Cat.initialize(configFile);
        } catch (IOException e) {
            log.error("ClassPathResource.cat.xml failed", e);
        } catch (ComponentLookupException e) {
            log.error("CatUtils.reInit failed", e);
        }
    }
}
