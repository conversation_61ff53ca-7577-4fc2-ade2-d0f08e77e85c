package com.bilibili.mgk.material.center.service.sanlian;

import com.bilibili.databus.base.Message;
import com.bilibili.databus.core.DataBusClient;
import com.bilibili.mgk.material.center.service.sanlian.model.OperationLogLongContextBo;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.util.HashMap;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/27
 */
@Slf4j
@Service
public class SanlianGeneralLogService {


    @Resource
    private DataBusClient cpcOperationLogPub;


    public void log(OperationLogLongContextBo logContext) {

        Message message = new Message(String.valueOf(logContext.getObjId()),
                JsonUtil.writeValueAsString(Lists.newArrayList(logContext)).getBytes(),
                new HashMap<>());

        Try.run(() -> cpcOperationLogPub.pub(message)
        ).onFailure(t -> {
            log.error("cpcOperationLogPub pub error, msg={}", logContext, t);
        }).onSuccess(r -> {
            log.info("Success to pub message for cpcOperationLogPub, dynamicId={}", logContext.getObjId());
        });

    }

}
