package com.bilibili.collage.biz.service.school.doctree.vo;

import com.bilibili.collage.biz.service.school.doctree.model.SanlianSchoolTreeType;
import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/9
 */
@Data
@Accessors(chain = true)
public class SanlianSubTreeReq implements SnakeCaseBody {

    @ApiModelProperty(value = "必选，树类型,product_manual:产品说明中心,ad_guidance:行业投放指南,common_question:常见问题,"
            + "commercial_info:商业信息,latest_info:最新动态")
    private SanlianSchoolTreeType treeType;

    @ApiModelProperty(value = "无需提供，登录态账号id")
    private String accountId;

    @ApiModelProperty(value = "可选，是否查询文档内容， 默认否")
    private Boolean retrieveDoc;

    @ApiModelProperty(value = "可选，是否查询已删除节点，不传则表示全部")
    private Boolean isDeleted;

    @ApiModelProperty(value = "可选，是否查询不显示节点，不传则表示全部")
    private Boolean isShow;


    @ApiModelProperty(value = "可选，查询深度，不传则表示全部")
    private Integer depth;

    public void validate() {

        Assert.notNull(treeType, "treeType can not be null");

    }

}
