package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018/6/28
 **/
@AllArgsConstructor
public enum MgkJumpTypeEnum {
    PAGE_ID(5, "页面ID");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static MgkJumpTypeEnum getByCode(int code) {
        for (MgkJumpTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkJumpTypeEnum " + code);
    }
}
