package com.bilibili.mgk.platform.portal.webapi.material;

import com.bilibili.mgk.material.center.service.creative.UpRankService;
import com.bilibili.mgk.material.center.service.creative.model.UpRankInfo;
import com.bilibili.mgk.material.center.service.creative.model.UpRankVideoInfo;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankMidQuery;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankQuery;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankVideoQuery;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/up_rank")
@Api(value = "/material/up_rank", tags = "material-center")
public class UpRankController extends BasicController {


    @Resource
    private UpRankService upRankService;


    @ApiOperation("up主榜单分页搜索")
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    @FreeLogin
    public Response<Pagination<List<UpRankInfo>>> list(
            @ApiIgnore Context context,
            @RequestBody UpRankQuery upRankQuery) {

        upRankQuery.setAccountId(
                Optional.ofNullable(context).map(Context::getAccountId).map(Integer::longValue).orElse(null));

        return Response.SUCCESS(upRankService.page(upRankQuery));

    }


    @ApiOperation("up主榜单信息明细，该接口用于明细查看、收藏查看、批量对比, 注意该接口的返回顺序是与请求的mid保持一致的。")
    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    @FreeLogin
    public Response<List<UpRankInfo>> detail(
            @ApiIgnore Context context,
            @RequestBody UpRankMidQuery upRankQuery) {

        upRankQuery.setAccountId(
                Optional.ofNullable(context).map(Context::getAccountId).map(Integer::longValue).orElse(null));
        return Response.SUCCESS(upRankService.detail(upRankQuery));
    }


    @ApiOperation("up主关联视频-非分页")
    @RequestMapping(value = "/video/list", method = {RequestMethod.POST})
    @FreeLogin
    public Response<List<UpRankVideoInfo>> list(
            @ApiIgnore Context context,
            @RequestBody UpRankVideoQuery upRankQuery) {

        return Response.SUCCESS(upRankService.listUpVideos(upRankQuery));

    }

}
