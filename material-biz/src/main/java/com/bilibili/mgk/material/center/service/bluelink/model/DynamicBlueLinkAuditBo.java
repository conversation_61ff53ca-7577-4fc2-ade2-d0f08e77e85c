package com.bilibili.mgk.material.center.service.bluelink.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicBlueLinkAuditBo {
    private List<String> dynamicIds;
    private String reason;
    private Integer auditStatus;
    private Long operatorId;
    private String operatorName;
}
