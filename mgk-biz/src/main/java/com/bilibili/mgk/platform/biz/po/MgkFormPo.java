package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkFormPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 表单ID
     */
    private Long formId;

    /**
     * 表单名称
     */
    private String name;

    /**
     * 状态: 1-使用中 2-已删除
     */
    private Integer status;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 表单类型 0-普通表单 1-视频浮层表单 2-标准化浮层表单 3-评论浮层表单
     */
    private Integer formType;

    /**
     * 落地页标题-仅在评论转化组件创建假落地页的时候使用
     */
    private String pageTitle;

    private static final long serialVersionUID = 1L;
}