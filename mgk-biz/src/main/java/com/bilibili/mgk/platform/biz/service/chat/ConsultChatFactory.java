package com.bilibili.mgk.platform.biz.service.chat;


import com.bilibili.mgk.platform.api.landing_page.service.IMgkConsultChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/20 14:34
 */
@Slf4j
@Component
public class ConsultChatFactory {
    private static final Map<String, AbstractMgkConsultChatServiceImpl> services = new HashMap<>();

    @Autowired
    private ApplicationContext applicationContext; // 注入 Spring 容器

    /**
     * 根据 thirdPartyId 获取对应的服务，懒加载方案
     */
    public AbstractMgkConsultChatServiceImpl getService(String thirdPartyCode) {
        if (services.containsKey(thirdPartyCode)) {
            return services.get(thirdPartyCode);
        }
        AbstractMgkConsultChatServiceImpl service = applicationContext.getBean(thirdPartyCode, AbstractMgkConsultChatServiceImpl.class);
        if (service == null) {
            throw new IllegalArgumentException("未知的第三方: " + thirdPartyCode);
        }
        services.put(thirdPartyCode, service);
        return service;
    }
}
