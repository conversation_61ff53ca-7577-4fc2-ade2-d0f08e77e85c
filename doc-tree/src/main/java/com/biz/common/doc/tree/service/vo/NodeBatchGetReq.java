package com.biz.common.doc.tree.service.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/5
 */
@Data
@Accessors(chain = true)
public class NodeBatchGetReq implements SnakeCaseBody, DocRetrieveSupport {


    @ApiModelProperty("根节点id")
    private List<Long> rootId;

    @ApiModelProperty("节点id列表")
    private List<Long> nodeIds;


    /**
     * 是否回表查询文档内存，如果文档存在时
     */

    @ApiModelProperty("是否回表查询文档")
    private Boolean retrieveDoc;

    @ApiModelProperty("可选，是否查询节点的祖先节点, 默认否")
    private Boolean retrieveAncestors;

    private String accountId;


    @ApiModelProperty("是否查询收藏状态, 默认否, 当查询时accountId需要提供")
    private Boolean retrieveFavorite = false;




    /**
     * 是否使用docType对节点进行
     */
    @ApiModelProperty("可选，是否使用docType对节点进行")
    private List<String> docType;

    private List<String> nodeType;


    private Boolean isDeleted;

    private Boolean isShow;








    public void validate() {
        Assert.notEmpty(nodeIds, "nodeId不能为空");
    }

}
