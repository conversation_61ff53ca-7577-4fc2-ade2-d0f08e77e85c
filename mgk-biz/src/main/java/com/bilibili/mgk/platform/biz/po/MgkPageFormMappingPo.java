package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkPageFormMappingPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 页面id
     */
    private Long pageId;

    /**
     * 表单id
     */
    private Long formId;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}