package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.util.Page;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.mgk.platform.api.hot_ads.dto.QueryHotAdsDto;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoCollectDto;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoCollectService;
import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import com.bilibili.mgk.platform.biz.config.ClickHouseJDBCClient;
import com.bilibili.mgk.platform.biz.dao.MgkHotVideoCollectDao;
import com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * @file: HotAdsServiceDelegateTest
 * @author: gaoming
 * @date: 2021/01/14
 * @version: 1.0
 * @description:
 **/
public class HotAdsServiceDelegateTest extends BaseMockitoTest {

    @InjectMocks
    private HotAdsServiceDelegate hotAdsServiceDelegate;

    @Mock
    private IHotVideoCollectService hotVideoCollectService;

    @Mock
    private ClickHouseJDBCClient clickHouseJDBCClient;

    @Mock
    private MgkHotVideoCollectDao hotVideoCollectDao;



    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testGetHotAdsDtos() {
        try {
            when(clickHouseJDBCClient.submitQuery(any())).thenReturn(Lists.newArrayList(new HashMap<>()));
            hotAdsServiceDelegate.getHotAdsDtos(operator, QueryHotAdsDto.builder()
                    .blackStatus(1)
                    .industryList(Lists.newArrayList("1"))
                    .creativeId("1")
                    .adType("1")
                    .creativeTitles(Lists.newArrayList("1"))
                    .dateType(1)
                    .orderType(1)
                    .page(Page.valueOf(1, 15))
                    .styleAbility(1)
                    .blackList(Lists.newArrayList("1"))
                    .build());
        } catch (Exception ignore) {
        }
    }

    @Test
    public void testGetHotAdsDetail() {
        try {
            when(clickHouseJDBCClient.submitQuery(any())).thenReturn(Lists.newArrayList(new HashMap<>()));
            hotAdsServiceDelegate.getHotAdsDetail("creative_id", 1);
        } catch (Exception e) {

        }

    }

    @Test
    public void testDoCollect() {
        hotAdsServiceDelegate.doCollect(operator, "1",
                "1", 1, null, null);
    }

    @Test
    public void testCancelCollect() {
        MgkHotVideoCollectPo po = BeanTestUtils.initSimpleFields(MgkHotVideoCollectPo.builder().build());
        HotVideoCollectDto dto = BeanTestUtils.initSimpleFields(HotVideoCollectDto.builder().build());
        when(hotVideoCollectDao.selectByExample(any())).thenReturn(Lists.newArrayList(po));
        when(hotVideoCollectService.getCollects(any())).thenReturn(Lists.newArrayList(dto));
        hotAdsServiceDelegate.cancelCollect(operator, "1", 1);
    }

    @Test
    public void testGetHotAdsDtosByAdTypeCreativeIds() {
        try {
            hotAdsServiceDelegate.getHotAdsDtosByAdTypeCreativeIds(Lists.newArrayList("1"));
        } catch (Exception e) {
        }

    }
}