package com.biz.common.doc.tree.util;

import com.biz.common.doc.tree.repository.entity.DocContentEsEntity;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/16
 */
public class CamelCaseJsonUtilTest {


    @Test
    public void test() {

        System.out.println(CamelCaseJsonUtil.writeValueAsString(new DocContentEsEntity()
                .setNodeId(1L)
                .setIsDeleted(true)));
    }

}