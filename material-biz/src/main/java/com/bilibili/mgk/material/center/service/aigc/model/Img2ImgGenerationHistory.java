package com.bilibili.mgk.material.center.service.aigc.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import java.util.ArrayList;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class Img2ImgGenerationHistory extends Img2ImgProgressRecord implements SnakeCaseBody {


    public void hideAuditDeniedImg(Boolean errorSeenAsDeny) {

        Optional.ofNullable(this.getGeneratedImgs())
                .orElse(new ArrayList<>())
                .forEach(img -> {
                    if (img.fetchAuditDeniedAndInterceptAuditError(errorSeenAsDeny)) {
                        img.setGenImgUrl("");
                    }
                });

    }

}
