package com.bilibili.mgk.material.center.service.course.model;

import com.bapis.archive.service.Arc;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.mgk.material.center.service.course.CreativeCourseConvertor;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.biz.common.doc.tree.common.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import io.vavr.control.Try;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */

@Data
@Accessors(chain = true)
public class CreativeCourse implements SnakeCaseBody {

    ////// 文档树直接映射字段
    @ApiModelProperty("课程id")
    private Long courseId;


    @ApiModelProperty("节点类型")
    private String nodeType;


    @ApiModelProperty("课程课程类型")
    private CourseDocType courseType;

    @ApiModelProperty("作者，这里直接复用为课程讲师")
    private String author;

    @ApiModelProperty("课程标题")
    private String docTitle;


    @ApiModelProperty("课程摘要")
    private String docSummary;

    @ApiModelProperty("课程内容")
    private String docContent;

    @ApiModelProperty("排序优先级")
    private Integer sortPriority;


    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime ctime;

    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime mtime;


    @ApiModelProperty("是否可见")
    private Boolean isShow;


    @ApiModelProperty("一级目录id")
    private Long firstDirectoryId;

    @ApiModelProperty("一级目录名称")
    private String firstDirectoryName;

    @ApiModelProperty("二级目录id")
    private Long secondDirectoryId;

    @ApiModelProperty("二级目录名称")
    private String secondDirectoryName;

    @ApiModelProperty("父节点目录id")
    private Long parentDirectoryId;


    @ApiModelProperty("父节点目录名称")
    private String parentDirectoryName;



    //////////// 文档树透传字段，使用biz_extra透传，无法用于检索

    @ApiModelProperty("业务额外字段, json保存")
    private CourseExtra courseExtra;

    @ApiModelProperty("无论视频文本课程都需要， 课程额外信息, 封面")
    private String coverUrl;

    @ApiModelProperty("无论视频文本课程都需要， 课程额外信息, 封面md5")
    private String coverMd5;

    @ApiModelProperty("视频课程需要，avid")
    private Long avid;

    @ApiModelProperty("视频课程需要， bvid")
    private String bvid;

    @ApiModelProperty("视频课程需要， 视频标题")
    private String videoTitle;

    @ApiModelProperty("视频课程需要， 视频时长, 秒")
    private Integer videoDuration;


    @ApiModelProperty("视频课程需要， 视频封面")
    private String videoCoverUrl;

    @ApiModelProperty("视频课程需要， up主id")
    private Long upMid;

    @ApiModelProperty("是否启用跳转")
    private Boolean JumpEnabled;

    @ApiModelProperty("跳转链接")
    private String jumpUrl;

    ////////// 第三方逻辑构造数据
    @ApiModelProperty("收藏键")
    private String favoriteKey;

    @ApiModelProperty("是否收藏")
    private Boolean favorite;


    @JsonIgnore
    @ApiModelProperty("节点路径，不需要返回给前端")
    private List<Long> ancestorIds;

    public CreativeCourse buildCourseExtra() {

        CourseExtra extra = CreativeCourseConvertor.instance.course2Extra(this);

        this.setCourseExtra(extra);

        return this;
    }

    public CourseExtra lazyGetCourseExtra(String bizExtra) {

        if (courseExtra == null) {
            this.courseExtra = Optional.ofNullable(CourseExtra.deserialize(bizExtra)).orElse(new CourseExtra());

        }

        return courseExtra;

    }

    public void buildVideoCourseInfoByArchiveInfo(Arc archiveInfo) {

        if (courseExtra != null) {

            courseExtra.setAvid(archiveInfo.getAid());
            courseExtra.setBvid(Try.of(() -> BVIDUtils.avToBv(archiveInfo.getAid())).getOrNull());
            courseExtra.setVideoDuration((int) archiveInfo.getDuration());
            courseExtra.setVideoCoverUrl(archiveInfo.getCover43());
            courseExtra.setVideoTitle(archiveInfo.getTitle());
//            courseExtra.setCoverUrl(archiveInfo.getCover43());
//            courseExtra.setCoverMd5("");
            courseExtra.setUpMid(archiveInfo.getAuthor().getMid());
        }

        this.setAvid(archiveInfo.getAid());
        this.setBvid(Try.of(() -> BVIDUtils.avToBv(archiveInfo.getAid())).getOrNull());
        this.setVideoDuration((int) archiveInfo.getDuration());
        this.setVideoCoverUrl(archiveInfo.getCover43());
        this.setVideoTitle(archiveInfo.getTitle());
//        this.setCoverUrl(archiveInfo.getCover43());
//        this.setCoverMd5("");
        this.setUpMid(archiveInfo.getAuthor().getMid());

    }

    @Data
    @Accessors(chain = true)
    public static class CourseExtra implements SnakeCaseBody {

        @ApiModelProperty("无论视频文本课程都需要， 课程额外信息, 课程封面")
        private String coverUrl;

        @ApiModelProperty("无论视频文本课程都需要， 课程额外信息, 封面md5")
        private String coverMd5;

        @ApiModelProperty("视频课程需要，avid")
        private Long avid;

        @ApiModelProperty("视频课程需要， bvid")
        private String bvid;

        @ApiModelProperty("视频课程需要， 视频标题")
        private String videoTitle;

        @ApiModelProperty("视频课程需要， 视频时长, 秒")
        private Integer videoDuration;

        @ApiModelProperty("视频课程需要， 视频封面")
        private String videoCoverUrl;


        @ApiModelProperty("视频课程需要， up主id")
        private Long upMid;

        @ApiModelProperty("是否启用跳转")
        private Boolean JumpEnabled;

        @ApiModelProperty("跳转链接")
        private String jumpUrl;

        public static CourseExtra deserialize(String extra) {
            return JsonUtil.readValue(extra, CourseExtra.class);
        }

        public String serialize() {
            return JsonUtil.writeValueAsString(this);
        }


    }






}
