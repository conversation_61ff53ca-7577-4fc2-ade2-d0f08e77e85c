package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.api.ad_complain.dto.CreateAdComplainDto;
import com.bilibili.mgk.platform.common.complain.ComplainTypeEnum;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2018/11/28
 **/
@Service
@Slf4j
public class AdComplainValidService {
    public void validCreateAdComplain (CreateAdComplainDto dto) {
        Assert.notNull(dto.getCreativeId(), "创意id不能为空");
        if (!dto.isPc()) {
            // 非pc场景，需要校验email
            Assert.hasText(dto.getEmail(), "用户邮箱不能为空");
        }

        Assert.isTrue(dto.getEmail().length() <= 64, "用户邮箱长度超长");
        Assert.notNull(dto.getComplainType(), "投诉缘由不能为空");
        Assert.isTrue(dto.getComplainText() == null || dto.getComplainText().length() <= 400, "投诉内容长度不能超过400个字");
        if (dto.getComplainType().equals(ComplainTypeEnum.OTHER.getCode()) && !dto.isPc()) {
            Assert.hasText(dto.getComplainText(), "投诉内容不能为空");
        }
        if (!StringUtils.isEmpty(dto.getComplainText())) {
            dto.setComplainText(dto.getComplainText().replaceAll("[^\\u0000-\\uFFFF]", "[emoji?]"));//处理UTF-16的emoji表情
            Assert.isTrue(dto.getComplainText().length() <= 440, "投诉内容长度不能超过400个字");
            dto.setComplainText(EmojiParser.parseToAliases(dto.getComplainText()));
        }
        Assert.isTrue(dto.getComplainAttachs() == null || dto.getComplainAttachs().size() <= 5, "图片附件最多只支持上传5张");
    }
}
