package com.bilibili.mgk.platform.portal.service;

import com.bilibili.adp.common.enums.AppPackageStatus;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.passport.api.dto.ArchiveBase;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.biz.common.ArchiveState;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.cpt.platform.util.SsaUtils;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.mgk.platform.api.account.IMgkAccountService;
import com.bilibili.mgk.platform.api.audit.dto.PreviewKeyDto;
import com.bilibili.mgk.platform.api.audit.service.IMgkAuditPageService;
import com.bilibili.mgk.platform.api.download.dto.MgkPageDownloadComponentHeightDto;
import com.bilibili.mgk.platform.api.landing_page.dto.*;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.biz.component.UposComponent;
import com.bilibili.mgk.platform.biz.grpc.MgkGrpcManager;
import com.bilibili.mgk.platform.biz.redis.RedisService;
import com.bilibili.mgk.platform.common.*;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.converter.GameConverter;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.exception.WebApiExceptionCode;
import com.bilibili.mgk.platform.portal.util.GifDecoder;
import com.bilibili.mgk.platform.portal.util.ImageUtils;
import com.bilibili.mgk.platform.portal.util.UnitConversionUtils;
import com.bilibili.mgk.platform.portal.webapi.archive.vo.UposVideoVo;
import com.bilibili.mgk.platform.portal.webapi.consult_page.vo.ConsultTokenVo;
import com.bilibili.mgk.platform.portal.webapi.consult_page.vo.MgkConsultPageVo;
import com.bilibili.mgk.platform.portal.webapi.landing_page.vo.*;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaUposVideoDto;
import com.bilibili.ssa.platform.common.enums.SsaSplashScreenVideoStatus;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.common.LandingPageStatusEnum.CONSULT_NOT_PUBLISH_STATUS_LIST;
import static com.bilibili.mgk.platform.common.LandingPageStatusEnum.NOT_PUBLISH_STATUS_LIST;
import static com.bilibili.mgk.platform.common.MgkConstants.*;
import static com.bilibili.mgk.platform.portal.service.WebCollageService.getFileTypeByFile;

/**
 * <AUTHOR>
 * @date 2018/1/22
 **/
@Service
@Slf4j
public class WebLandingPageService {

    @Value("${mgk.consult.token.valide_time:20}")
    private Integer tokenValideTime;

    @Value("${mgk.shadow.need.publish.hint:当前落地页有仅保存未发布版本}")
    private String mgkShadowNeedPublishHint;

    @Value("#{'${mgk.test.audit.account.id:@}'.split(',')}")
    private List<Integer> testMgkAuditAccountIdList;

    @Value("${mgk.environment:1}")
    private Integer mgkEnvironment;

    @Value("${mgk.download.over.limit.hint:1}")
    private Integer mgkDownloadOverLimitHint;

    //允许创建2XX落地页的白名单
    @Value("${mgk.allow.create.un.audit.page.label:712}")
    private Integer allowCreateUnAuditPageLabelId;

    @Autowired
    private MgkGrpcManager mgkGrpcManager;

    private final static Set<String> STATIC_FILE_TYPE_SET = Sets.newHashSet("js", "css", "html");

    @Autowired
    private ArchiveManager archiveManager;

    @Autowired
    private IMgkLandingPageService mgkLandingPageService;

    @Autowired
    private ISoaAppPackageService soaAppPackageService;

    @Autowired
    private ISoaAccountLabelService crmAccountLabelService;

    @Autowired
    private UposComponent uposComponent;

    @Autowired
    private IMgkAuditPageService mgkAuditPageService;
    @Resource
    private IMgkAccountService iMgkAccountService;

    @Autowired
    private RedisService redisService;

    private final static Integer FORBID_LOGIN_MGK_LABEL_ID = 690;

    public void validateUpdatePermission(Integer accountId, Integer templateStyle){
        Boolean forbidLogin = crmAccountLabelService.checkAccountWithLabel(accountId,
                FORBID_LOGIN_MGK_LABEL_ID);
        Assert.isTrue(!forbidLogin, "当前账号命中了账号黑名单,请联系运营");

        if(TemplateStyleEnum.NOT_AUDIT_TEMPLATE_STYLE_LIST.contains(templateStyle)){
            Boolean allowCreateUnAuditPage = crmAccountLabelService.checkAccountWithLabel(accountId,
                    allowCreateUnAuditPageLabelId);
            Assert.isTrue(allowCreateUnAuditPage, "当前账号不允许创建发布巨幕落地页,请联系运营配置白名单!");
        }
    }

    public void validateAppPackage(Integer accountId, Integer appPackageId) {
        if (appPackageId != null && appPackageId > 0) {
            AppPackageDto appPackage = soaAppPackageService.load(appPackageId);
            Assert.isTrue(accountId.equals(appPackage.getAccountId()), "该appId不存在");
            Assert.isTrue(AppPackageStatus.VALID.getCode() == appPackage.getStatus(), "该APP已被删除");
        }
    }

    public void validatePermission(Context context, List<Long> pageIds) {
        Assert.isTrue(context.getAccountId() != null, "账户ID不可为空");
        Assert.notEmpty(pageIds, "落地页页面ID不可空");
        Assert.isTrue(!CollectionUtils.isEmpty(pageIds), "落地页id不可为空");
        List<MgkLandingPageDto> pageDtos = mgkLandingPageService.getLandingPageDtosInPageIds(pageIds);
        pageDtos.forEach(pageDto->
                Assert.isTrue(pageDto.getAccountId().equals(context.getAccountId()), "不允许操作不属于您的落地页!"));
    }

    public void checkCopyOriginPageValid(Long originPageId) {
        MgkLandingPageDto originPageDto = mgkLandingPageService.getLandingPageDtoByPageId(originPageId);
        Assert.notNull(originPageDto, "被复制的落地页不存在");
    }

    public boolean checkPageIsForAudit(Integer accountId, Long pageId) {
        MgkLandingPageDto pageDto = mgkLandingPageService.getLandingPageDtoByPageId(pageId);
        if (LandingPageTypeEnum.NATIVE.getCode().equals(pageDto.getType())) {
            return false;
        }
        return checkAccountIsForAudit(accountId);
    }




    public ConsultTokenDto genToken(String accountId){
        String token = generateToken(accountId);
        Instant now = Instant.now();
        long valideTime = now.plus(tokenValideTime, ChronoUnit.MINUTES).toEpochMilli();
        String value = accountId + "_" + String.valueOf(valideTime);
        redisService.addCache(token , value, tokenValideTime*60);
        return ConsultTokenDto.builder().token(token).validateTime(valideTime).build();
    }

    public static String generateToken(String accountId) {
        long timestamp = System.currentTimeMillis();
        String input = accountId + timestamp;
        byte[] inputBytes = input.getBytes(StandardCharsets.UTF_8);
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(inputBytes);
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            String token = hexString.toString().substring(0, 16);
            return token;
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not found", e);
        }
    }
    public boolean checkPageIsForAuditByType(Integer accountId, Integer pageType) {
        if (LandingPageTypeEnum.NATIVE.getCode().equals(pageType)) {
            return false;
        }
        return checkAccountIsForAudit(accountId);
    }

    public boolean checkAccountIsForAudit(Integer accountId) {
        Assert.isTrue(Utils.isPositive(accountId), "账户id不可为空");
        // 测试用账户 无条件推审落地页
        boolean isForTestAudit = testMgkAuditAccountIdList.contains(accountId);
        if (isForTestAudit) {
            return true;
        }
        // 品牌账户 不推审落地页
        if (mgkGrpcManager.checkBrandAccount(accountId)) {
            return false;
        }

        return true;
    }

    public List<MgkLandingPageVo> convertLandingPageDtos2Vos(List<MgkLandingPageDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        List<List<Long>> avidList = dtos.stream().map(MgkLandingPageDto::getAvIds)
                .filter(avIds -> !CollectionUtils.isEmpty(avIds)).collect(Collectors.toList());
        Map<Long, ArchiveDetail> archiveMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(avidList)) {
            List<Long> avidS = avidList.stream().flatMap(Collection::stream)
                    .distinct().collect(Collectors.toList());
            archiveMap = this.getAvId2ArchiveMapInAvIds(avidS);
        }

        Map<Long, ArchiveDetail> finalArchiveMap = archiveMap;

        List<Long> pageIds = dtos.stream().map(MgkLandingPageDto::getPageId).collect(Collectors.toList());
        List<Long> waitingAuditPageIds = mgkAuditPageService.queryWaitingAuditPageIds(pageIds);

        List<Integer> copySourceAccIds = dtos.stream().map(MgkLandingPageDto::getCopySourceAccountId)
                .distinct().collect(Collectors.toList());
        Map<Integer, String> copyAccountNameMap = iMgkAccountService.getAccountNameMapByAccountIds(copySourceAccIds);
        return dtos.stream().map(dto -> this.convertLandingPageDto2Vo(dto,
                this.getArchiveInfos(dto.getAvIds(), finalArchiveMap),
                waitingAuditPageIds, copyAccountNameMap))
                .collect(Collectors.toList());
    }

    private Map<Long, ArchiveDetail> getAvId2ArchiveMapInAvIds(List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyMap();
        }

        try {
            return archiveManager.getArchivesByAids(avids);
        } catch (Exception e) {
            log.error("getArchivesByAids failed", e);
            return Collections.emptyMap();
        }
    }

    private List<ArchiveInfoVo> getArchiveInfos(List<Long> avids, final Map<Long, ArchiveDetail> archiveMap) {
        if (CollectionUtils.isEmpty(avids) || CollectionUtils.isEmpty(archiveMap)) {
            return Collections.emptyList();
        }

        return avids.stream().sorted(Long::compareTo).map(avid -> {
            ArchiveDetail archiveDetail = archiveMap.get(avid);
            if (archiveDetail == null || archiveDetail.getArchive() == null) {
                return ArchiveInfoVo.builder().aid(avid)
                        .state(404)
                        .state_desc("稿件不存在")
                        .show_state(ArchiveShowStateEnum.NON_EXISTENT.getCode())
                        .show_state_desc(ArchiveShowStateEnum.NON_EXISTENT.getDesc())
                        .build();
            } else {
                ArchiveBase archive = archiveDetail.getArchive();
                ArchiveState archiveState = ArchiveState.getByCode(archive.getState());
                ArchiveShowStateEnum showStateEnum = ArchiveShowStateEnum.getByArchiveState(archive.getState());
                return ArchiveInfoVo.builder()
                        .aid(avid)
                        .title(archive.getTitle())
                        .reject_reason(archive.getReject_reason())
                        .state(archive.getState())
                        .state_desc(archiveState == null ? "未知状态" : archiveState.getName())
                        .show_state(showStateEnum.getCode())
                        .show_state_desc(showStateEnum.getDesc())
                        .order_id(archive.getOrder_id())
                        .flow_id(archive.getFlow_id())
                        .dtime(archive.getDtime() == null ? "" : Utils.getTimestamp2String(new Timestamp(archive.getDtime()), "yyyy-MM-dd HH:mm:ss"))
                        .ptime(archive.getPtime() == null ? "" : Utils.getTimestamp2String(new Timestamp(archive.getPtime()), "yyyy-MM-dd HH:mm:ss"))
                        .ctime(archive.getCtime() == null ? "" : (Utils.getTimestamp2String(new Timestamp(archive.getCtime()), "yyyy-MM-dd HH:mm:ss")))
                        .build();
            }
        }).collect(Collectors.toList());
    }

    public MgkLandingPageVo convertLandingPageDto2Vo(MgkLandingPageDto dto,
                                                     List<ArchiveInfoVo> archiveInfoVos,
                                                     List<Long> waitingAuditPageIds, Map<Integer, String> copyAccountNameMap) {
        MgkLandingPageVo.MgkLandingPageVoBuilder voBuilder =  MgkLandingPageVo.builder()
                .id(dto.getId())
                .account_id(dto.getAccountId())
                .username("")
                .page_id(String.valueOf(dto.getPageId()))
                .name(dto.getName())
                .title(dto.getTitle())
                .form_ids(dto.getFormIds())
                .status(dto.getStatus())
                .status_desc(LandingPageStatusEnum.getByCode(dto.getStatus()).getDesc())
                .shadow_status(dto.getShadowStatus())
                .shadow_status_desc(LandingPageStatusEnum.WAIT_AUDIT.getCode().equals(dto.getShadowStatus()) ?
                        "" : mgkShadowNeedPublishHint)
                .template_style(dto.getTemplateStyle())
                .template_style_desc(TemplateStyleEnum.getByCode(dto.getTemplateStyle()).getDesc())
                .creator(dto.getCreator())
                .reason(dto.getReason())
                .access_url(dto.getAccessUrl())
                .effective_start_time(Utils.getTimestamp2String(dto.getEffectiveStartTime()))
                .effective_end_time(Utils.getTimestamp2String(dto.getEffectiveEndTime()))
                .mtime(Utils.getTimestamp2String(dto.getMtime(), YYYY_MM_DD_HH_MM_SS))
                .page_type(dto.getType())
                .show_urls(dto.getShowUrls())
                .av_ids(CollectionUtils.isEmpty(dto.getAvIds()) ? Collections.emptyList() : dto.getAvIds().stream().map(String::valueOf).collect(Collectors.toList()))
                .archive_infos(archiveInfoVos)
                .model_id(String.valueOf(dto.getModelId()))
                .model_name(Strings.isNullOrEmpty(dto.getModelName()) ? "自定义" : dto.getModelName())
                .is_pc(dto.getIsPc())
                .page_cover(dto.getPageCover())
                .mobile_launch_url(dto.getMobileLaunchUrl())
                .pc_launch_url(dto.getPcLaunchUrl())
                .page_bg_url(dto.getPageBgUrl())
                .page_bg_color(dto.getPageBgColor())
                .pv(UnitConversionUtils.thousandth(dto.getPv()))
                .ctr(UnitConversionUtils.thousandth(dto.getCtr()))
                .pv_ctr_rate(UnitConversionUtils.convertPercentage(dto.getPvCtrRate()))
                .has_dpa_goods(dto.getHasDpaGoods())
                .env(mgkEnvironment)
                .consume(dto.getConsume() == null ? BigDecimal.ZERO : dto.getConsume())
                .contain_auditing_version(waitingAuditPageIds.contains(dto.getPageId()))
                .copy_source_account_id(dto.getCopySourceAccountId())
                .copy_source_account_name(copyAccountNameMap.getOrDefault(dto.getCopySourceAccountId(), ""))
                .has_welcome_words(dto.getIsConsultPage() != null &&dto.getIsConsultPage() && dto.getConsultLandingPageDto() != null? dto.getConsultLandingPageDto().getHasWelcomeWords() == null?0: dto.getConsultLandingPageDto().getHasWelcomeWords(): null)
                ;
        Integer has_welcome_words = 0;
        String welcome_words = "";
        Integer is_leave_data = 0;
        Integer has_consult_faq = 0;
        String consult_faq = "";
        Integer customer_service_type = 0;
        String customer_service_name = "";
        String profile = "";
        String connect_type = "";
        if (dto.getIsConsultPage() != null && dto.getIsConsultPage()) {
            if (dto.getConsultLandingPageDto() != null) {
                has_welcome_words = dto.getConsultLandingPageDto().getHasWelcomeWords() == null ? 0 : dto.getConsultLandingPageDto().getHasWelcomeWords() ;
                welcome_words = dto.getConsultLandingPageDto().getWelcomeWords() == null ? "" : dto.getConsultLandingPageDto().getWelcomeWords() ;
                is_leave_data = dto.getConsultLandingPageDto().getIsLeaveData() == null ? 0 : dto.getConsultLandingPageDto().getIsLeaveData() ;
                has_consult_faq = dto.getConsultLandingPageDto().getHasConsultFaq() == null ? 0 : dto.getConsultLandingPageDto().getHasConsultFaq() ;
                consult_faq = dto.getConsultLandingPageDto().getConsultFaq() == null ? "" : dto.getConsultLandingPageDto().getConsultFaq() ;
                customer_service_type = dto.getConsultLandingPageDto().getCustomerServiceType() == null ? 0 : dto.getConsultLandingPageDto().getCustomerServiceType() ;
                customer_service_name = dto.getConsultLandingPageDto().getCustomerServiceType() == null ? "" : ConsultPageCustomerServiceEnum.getNameByCode(dto.getConsultLandingPageDto().getCustomerServiceType()) ;
                profile = dto.getConsultLandingPageDto().getProfile() == null ? "" : dto.getConsultLandingPageDto().getProfile() ;
                connect_type = dto.getConsultLandingPageDto().getConnectType() == null ? "onMessage" : dto.getConsultLandingPageDto().getConnectType() ;
            }
        }
        voBuilder.has_welcome_words(has_welcome_words).welcome_words(welcome_words).is_leave_data(is_leave_data)
                .has_consult_faq(has_consult_faq).consult_faq(consult_faq).customer_service_type(customer_service_type).customer_service_name(customer_service_name)
                .profile(profile).connect_type(connect_type);
        return voBuilder.build();

    }

    public MgkLandingPageVo convertLandingPageConfigDto2Vo(LandingPageConfigDto dto) {
        MgkLandingPageVo.MgkLandingPageVoBuilder voBuilder = MgkLandingPageVo.builder()
                .id(dto.getId())
                .account_id(dto.getAccountId())
                .page_id(String.valueOf(dto.getPageId()))
                .name(dto.getName())
                .title(dto.getTitle())
                .form_ids(dto.getFormIds().stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList()))
                .status(dto.getStatus())
                .status_desc(LandingPageStatusEnum.getByCode(dto.getStatus()).getDesc())
                .template_style(dto.getTemplateStyle())
                .template_style_desc(TemplateStyleEnum.getByCode(dto.getTemplateStyle()).getDesc())
                .creator(dto.getCreator())
                .reason(dto.getReason())
                .access_url(String.format(LANDING_PAGE_URL_PREFIXX, "https", dto.getPageId()))
                .effective_start_time(Utils.getTimestamp2String(dto.getEffectiveStartTime()))
                .effective_end_time(Utils.getTimestamp2String(dto.getEffectiveEndTime()))
                .config(dto.getConfig())
                .page_type(dto.getType())
                .av_ids(CollectionUtils.isEmpty(dto.getAvIds()) ? Collections.emptyList() : dto.getAvIds().stream().map(String::valueOf).collect(Collectors.toList()))
                .show_urls(dto.getShowUrls())
                .privacy_name(dto.getPrivacyName())
                .privacy_url(dto.getPrivacyUrl())
                .mini_game_ids(dto.getMiniGameIds())
                .wechat_is_manual(dto.getWechatIsManual())
                .is_model(dto.getIsModel())
                .model_id(String.valueOf(dto.getModelId()))
                .is_pc(dto.getIsPc())
                .mobile_launch_url(dto.getMobileLaunchUrl())
                .pc_launch_url(dto.getPcLaunchUrl())
                .page_bg_color(dto.getPageBgColor())
                .page_bg_url(dto.getPageBgUrl())
                .page_cover(dto.getPageCover())
                .has_dpa_goods(dto.getHasDpaGoods())
                .is_video_page(dto.getIsVideoPage())
                .is_download_button_over_limit(dto.getIsDownloadOverLimit())
                .download_button_over_limit_hint(mgkDownloadOverLimitHint)
                .customer_acquisition_link_ids(dto.getCustomerAcquisitionLinkIds())
                .games(GameConverter.MAPPER.toGameVo(dto.getGames()))
                ;
        Integer has_welcome_words = 0;
        String welcome_words = "";
        Integer is_leave_data = 0;
        Integer has_consult_faq = 0;
        String consult_faq = "";
        Integer customer_service_type = 0;
        String customer_service_name = "";
        String profile = "";
        String connect_type = "";
        if (dto.getIsConsultPage() != null && dto.getIsConsultPage()) {
            if (dto.getConsultLandingPageDto() != null) {
                has_welcome_words = dto.getConsultLandingPageDto().getHasWelcomeWords() == null ? 0 : dto.getConsultLandingPageDto().getHasWelcomeWords() ;
                welcome_words = dto.getConsultLandingPageDto().getWelcomeWords() == null ? "" : dto.getConsultLandingPageDto().getWelcomeWords() ;
                is_leave_data = dto.getConsultLandingPageDto().getIsLeaveData() == null ? 0 : dto.getConsultLandingPageDto().getIsLeaveData() ;
                has_consult_faq = dto.getConsultLandingPageDto().getHasConsultFaq() == null ? 0 : dto.getConsultLandingPageDto().getHasConsultFaq() ;
                consult_faq = dto.getConsultLandingPageDto().getConsultFaq() == null ? "" : dto.getConsultLandingPageDto().getConsultFaq() ;
                customer_service_type = dto.getConsultLandingPageDto().getCustomerServiceType() == null ? 0 : dto.getConsultLandingPageDto().getCustomerServiceType() ;
                customer_service_name = dto.getConsultLandingPageDto().getCustomerServiceType() == null ? "" : ConsultPageCustomerServiceEnum.getNameByCode(dto.getConsultLandingPageDto().getCustomerServiceType()) ;
                profile = dto.getConsultLandingPageDto().getProfile() == null ? "" : dto.getConsultLandingPageDto().getProfile() ;
                connect_type = dto.getConsultLandingPageDto().getConnectType() == null ? "onMessage" : dto.getConsultLandingPageDto().getConnectType() ;
            }
        }
        voBuilder.has_welcome_words(has_welcome_words).welcome_words(welcome_words).is_leave_data(is_leave_data)
                .has_consult_faq(has_consult_faq).consult_faq(consult_faq).customer_service_type(customer_service_type).customer_service_name(customer_service_name)
                .profile(profile).connect_type(connect_type);
        return voBuilder.build();

    }

    public MgkConsultPageVo convertConsultPageDto2Vo(MgkLandingPageDto dto,
                                                     List<ArchiveInfoVo> archiveInfoVos,
                                                     List<Long> waitingAuditPageIds, Map<Integer, String> copyAccountNameMap) {
        MgkConsultPageVo.MgkConsultPageVoBuilder voBuilder = MgkConsultPageVo.builder()
                .id(dto.getId())
                .account_id(dto.getAccountId())
                .username("")
                .page_id(String.valueOf(dto.getPageId()))
                .name(dto.getName())
                .title(dto.getTitle())
                .status(dto.getStatus())
                .status_desc(LandingPageStatusEnum.getByCode(dto.getStatus()).getDesc())
                .shadow_status(dto.getShadowStatus())
                .shadow_status_desc(LandingPageStatusEnum.WAIT_AUDIT.getCode().equals(dto.getShadowStatus()) ?
                        "" : mgkShadowNeedPublishHint)
                .template_style(dto.getTemplateStyle())
                .template_style_desc(TemplateStyleEnum.getByCode(dto.getTemplateStyle()).getDesc())
                .creator(dto.getCreator())
                .reason(dto.getReason())
                .access_url(dto.getAccessUrl())
                .effective_start_time(Utils.getTimestamp2String(dto.getEffectiveStartTime()))
                .effective_end_time(Utils.getTimestamp2String(dto.getEffectiveEndTime()))
                .mtime(Utils.getTimestamp2String(dto.getMtime(), YYYY_MM_DD_HH_MM_SS))
                .page_type(dto.getType())
                .show_urls(dto.getShowUrls())
                .av_ids(CollectionUtils.isEmpty(dto.getAvIds()) ? Collections.emptyList() : dto.getAvIds().stream().map(String::valueOf).collect(Collectors.toList()))
                .archive_infos(archiveInfoVos)
                .model_id(String.valueOf(dto.getModelId()))
                .model_name(Strings.isNullOrEmpty(dto.getModelName()) ? "自定义" : dto.getModelName())
                .is_pc(dto.getIsPc())
                .page_cover(dto.getPageCover())
                .mobile_launch_url(dto.getMobileLaunchUrl())
                .pc_launch_url(dto.getPcLaunchUrl())
                .page_bg_url(dto.getPageBgUrl())
                .page_bg_color(dto.getPageBgColor())
                .pv(UnitConversionUtils.thousandth(dto.getPv()))
                .ctr(UnitConversionUtils.thousandth(dto.getCtr()))
                .pv_ctr_rate(UnitConversionUtils.convertPercentage(dto.getPvCtrRate()))
                .has_dpa_goods(dto.getHasDpaGoods())
                .env(mgkEnvironment)
                .consume(dto.getConsume() == null ? BigDecimal.ZERO : dto.getConsume())
                .contain_auditing_version(waitingAuditPageIds.contains(dto.getPageId()))
                .copy_source_account_id(dto.getCopySourceAccountId())
                .copy_source_account_name(copyAccountNameMap.getOrDefault(dto.getCopySourceAccountId(), ""))
                ;
        Integer has_welcome_words = 0;
        String welcome_words = "";
        Integer is_leave_data = 0;
        Integer has_consult_faq = 0;
        String consult_faq = "";
        Integer customer_service_type = 0;
        String customer_service_name = "";
        String profile = "";
        String connect_type = "";
        if (dto.getIsConsultPage() != null && dto.getIsConsultPage()) {
            if (dto.getConsultLandingPageDto() != null) {
                has_welcome_words = dto.getConsultLandingPageDto().getHasWelcomeWords() == null ? 0 : dto.getConsultLandingPageDto().getHasWelcomeWords() ;
                welcome_words = dto.getConsultLandingPageDto().getWelcomeWords() == null ? "" : dto.getConsultLandingPageDto().getWelcomeWords() ;
                is_leave_data = dto.getConsultLandingPageDto().getIsLeaveData() == null ? 0 : dto.getConsultLandingPageDto().getIsLeaveData() ;
                has_consult_faq = dto.getConsultLandingPageDto().getHasConsultFaq() == null ? 0 : dto.getConsultLandingPageDto().getHasConsultFaq() ;
                consult_faq = dto.getConsultLandingPageDto().getConsultFaq() == null ? "" : dto.getConsultLandingPageDto().getConsultFaq() ;
                customer_service_type = dto.getConsultLandingPageDto().getCustomerServiceType() == null ? 0 : dto.getConsultLandingPageDto().getCustomerServiceType() ;
                customer_service_name = dto.getConsultLandingPageDto().getCustomerServiceType() == null ? "" : ConsultPageCustomerServiceEnum.getNameByCode(dto.getConsultLandingPageDto().getCustomerServiceType()) ;
                profile = dto.getConsultLandingPageDto().getProfile() == null ? "" : dto.getConsultLandingPageDto().getProfile() ;
                connect_type = dto.getConsultLandingPageDto().getConnectType() == null ? "onMessage" : dto.getConsultLandingPageDto().getConnectType() ;
            }
        }
        voBuilder.has_welcome_words(has_welcome_words).welcome_words(welcome_words).is_leave_data(is_leave_data)
                .has_consult_faq(has_consult_faq).consult_faq(consult_faq).customer_service_type(customer_service_type).customer_service_name(customer_service_name)
                .profile(profile).connect_type(connect_type);
        return voBuilder.build();
    }

    public NewLandingPageDto convertNewLandingPageVo2Dto(Integer accountId, NewLandingPageVo newLandingPageVo) {
        Assert.notNull(newLandingPageVo, "落地页信息不可为空");
        return NewLandingPageDto.builder()
                .originPageId(newLandingPageVo.getOrigin_page_id() == null ? 0L : Long.valueOf(newLandingPageVo.getOrigin_page_id()))
                .accountId(accountId)
                .pageVersion(newLandingPageVo.getPage_version())
                .name(newLandingPageVo.getName())
                .title(newLandingPageVo.getTitle())
                .templateStyle(newLandingPageVo.getTemplate_style())
                .config(newLandingPageVo.getConfig())
                .effectiveStartTime(newLandingPageVo.getEffective_start_time() == null ? Utils.getToday() : new Timestamp(newLandingPageVo.getEffective_start_time()))
                .effectiveEndTime(newLandingPageVo.getEffective_end_time() == null ? Utils.getToday() : new Timestamp(newLandingPageVo.getEffective_end_time()))
                .type(newLandingPageVo.getPage_type())
                .avIds(newLandingPageVo.getAv_ids())
                .showUrls(newLandingPageVo.getShow_urls())
                .formId(Long.valueOf(newLandingPageVo.getForm_id()))
                .formIds(CollectionUtils.isEmpty(newLandingPageVo.getForm_ids()) ? Collections.emptyList() : newLandingPageVo.getForm_ids().stream().map(Long::valueOf).collect(Collectors.toList()))
                .miniGameIds(CollectionUtils.isEmpty(newLandingPageVo.getMini_game_ids()) ? Collections.emptyList() : newLandingPageVo.getMini_game_ids())
                .wechatPackageId(newLandingPageVo.getWechat_package_id())
                .appPackageId(newLandingPageVo.getApp_package_id())
                .appPackageIds(CollectionUtils.isEmpty(newLandingPageVo.getApp_package_ids()) ? Collections.emptyList() : newLandingPageVo.getApp_package_ids())
                .isModel(newLandingPageVo.getIs_model() == null ? 0 : newLandingPageVo.getIs_model())
                .modelId(newLandingPageVo.getModel_id() == null ? 0 : newLandingPageVo.getModel_id())
                .bizId(newLandingPageVo.getBiz_id())
                .bizIds(newLandingPageVo.getBiz_ids() == null ?
                        Collections.emptyList() : newLandingPageVo.getBiz_ids().stream().filter(bizId -> bizId > 0).collect(Collectors.toList()))
                .isPc(newLandingPageVo.getIs_pc() == null ? 0 : newLandingPageVo.getIs_pc())
                .header(newLandingPageVo.getHeader() == null ? 0 : newLandingPageVo.getHeader())
                .hasTransition(newLandingPageVo.getHas_transition() == null ? 0 : newLandingPageVo.getHas_transition())
                .transition(newLandingPageVo.getTransition())
                .pageCover(newLandingPageVo.getPage_cover())
                .isFormScroll(newLandingPageVo.getIs_form_scroll() == null ? 0 : newLandingPageVo.getIs_form_scroll())
                .hasDpaGoods(newLandingPageVo.getHas_dpa_goods() == null ? 0 : newLandingPageVo.getHas_dpa_goods())
                .totalBlockSize(StringUtils.isEmpty(newLandingPageVo.getTotal_block_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(newLandingPageVo.getTotal_block_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .totalDownloadComponentSize(StringUtils.isEmpty(newLandingPageVo.getTotal_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(newLandingPageVo.getTotal_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .totalFirstScreenDownloadComponentSize(StringUtils.isEmpty(newLandingPageVo.getTotal_first_screen_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(newLandingPageVo.getTotal_first_screen_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .maxDownloadComponentSize(StringUtils.isEmpty(newLandingPageVo.getMax_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(newLandingPageVo.getMax_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .customerAcquisitionLinkIds(newLandingPageVo.getCustomer_acquisition_link_ids())
                .isGameFormReserve(newLandingPageVo.getIs_game_form_reserve())
                .games(GameConverter.MAPPER.toGameDto(newLandingPageVo.getGames()))
                .isConsultPage(newLandingPageVo.getCustomer_service_type() != null)
                .consultLandingPageDto(newLandingPageVo.getCustomer_service_type() != null?ConsultLandingPageDto.builder().formId(Long.valueOf(newLandingPageVo.getForm_id()))
                        .hasWelcomeWords(newLandingPageVo.getHas_welcome_words())
                        .welcomeWords(newLandingPageVo.getWelcome_words())
                        .isLeaveData(newLandingPageVo.getIs_leave_data())
                        .hasConsultFaq(newLandingPageVo.getIs_leave_data())
                        .consultFaq(newLandingPageVo.getConsult_faq())
                        .customerServiceType(newLandingPageVo.getCustomer_service_type())
                        .profile(newLandingPageVo.getProfile())
                        .connectType(newLandingPageVo.getConnect_type())
                        .build() : new ConsultLandingPageDto())
                .build();    }


    public UpdateLandingPageDto convertUpdateLandingPageVo2Dto(UpdateLandingPageVo updateLandingPageVo) {
        Assert.notNull(updateLandingPageVo, "落地页信息不可为空");
        return UpdateLandingPageDto.builder()
                .pageId(Long.valueOf(updateLandingPageVo.getPage_id()))
                .name(updateLandingPageVo.getName())
                .title(updateLandingPageVo.getTitle())
                .config(updateLandingPageVo.getConfig())
                .templateStyle(updateLandingPageVo.getTemplate_style())
                .effectiveStartTime(updateLandingPageVo.getEffective_start_time() == null ? Utils.getToday() : new Timestamp(updateLandingPageVo.getEffective_start_time()))
                .effectiveEndTime(updateLandingPageVo.getEffective_end_time() == null ? Utils.getToday() : new Timestamp(updateLandingPageVo.getEffective_end_time()))
                .avIds(updateLandingPageVo.getAv_ids())
                .templateStyle(updateLandingPageVo.getTemplate_style())
                .showUrls(updateLandingPageVo.getShow_urls())
                .formId(updateLandingPageVo.getForm_id() != null? Long.valueOf(updateLandingPageVo.getForm_id()) : !CollectionUtils.isEmpty(updateLandingPageVo.getForm_ids()) ? Long.valueOf(updateLandingPageVo.getForm_ids().get(0)) : null)
                .formIds(CollectionUtils.isEmpty(updateLandingPageVo.getForm_ids()) ? Collections.emptyList() : updateLandingPageVo.getForm_ids().stream().map(Long::valueOf).collect(Collectors.toList()))
                .miniGameIds(CollectionUtils.isEmpty(updateLandingPageVo.getMini_game_ids()) ? Collections.emptyList() : updateLandingPageVo.getMini_game_ids().stream().map(Integer::valueOf).collect(Collectors.toList()))
                .wechatPackageId(updateLandingPageVo.getWechat_package_id())
                .appPackageId(updateLandingPageVo.getApp_package_id())
                .appPackageIds(CollectionUtils.isEmpty(updateLandingPageVo.getApp_package_ids()) ? Collections.emptyList() : updateLandingPageVo.getApp_package_ids())
                .bizId(updateLandingPageVo.getBiz_id())
                .bizIds(updateLandingPageVo.getBiz_ids() == null ?
                        Collections.emptyList() : updateLandingPageVo.getBiz_ids().stream().filter(bizId -> bizId > 0).collect(Collectors.toList()))
                .isPc(updateLandingPageVo.getIs_pc())
                .pageVersion(updateLandingPageVo.getPage_version())
                .isModel(updateLandingPageVo.getIs_model())
                .modelId(updateLandingPageVo.getModel_id())
                .modelVersion(updateLandingPageVo.getModel_version())
                .header(updateLandingPageVo.getHeader())
                .hasTransition(updateLandingPageVo.getHas_transition())
                .transition(updateLandingPageVo.getTransition())
                .pageCover(updateLandingPageVo.getPage_cover())
                .pageType(updateLandingPageVo.getPage_type())
                .pageBgColor(updateLandingPageVo.getPage_bg_color())
                .pageBgUrl(updateLandingPageVo.getPage_bg_url())
                .isRefreshLog(0)
                .isFormScroll(updateLandingPageVo.getIs_form_scroll())
                .hasDpaGoods(updateLandingPageVo.getHas_dpa_goods())
                .totalBlockSize(StringUtils.isEmpty(updateLandingPageVo.getTotal_block_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(updateLandingPageVo.getTotal_block_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .totalDownloadComponentSize(StringUtils.isEmpty(updateLandingPageVo.getTotal_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(updateLandingPageVo.getTotal_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .totalFirstScreenDownloadComponentSize(StringUtils.isEmpty(updateLandingPageVo.getTotal_first_screen_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(updateLandingPageVo.getTotal_first_screen_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .maxDownloadComponentSize(StringUtils.isEmpty(updateLandingPageVo.getMax_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(updateLandingPageVo.getMax_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .isForPublish(updateLandingPageVo.getIs_for_publish())
                .isWriteBack(WhetherEnum.NO.getCode())
                .customerAcquisitionLinkIds(updateLandingPageVo.getCustomer_acquisition_link_ids())
                .isGameFormReserve(updateLandingPageVo.getIs_game_form_reserve())
                .games(GameConverter.MAPPER.toGameDto(updateLandingPageVo.getGames()))
                .status(updateLandingPageVo.getStatus())
                .isConsultPage(updateLandingPageVo.getCustomer_service_type() != null)
                .consultLandingPageDto(updateLandingPageVo.getCustomer_service_type() != null?ConsultLandingPageDto.builder().formId(Long.valueOf(updateLandingPageVo.getForm_id()))
                        .hasWelcomeWords(updateLandingPageVo.getHas_welcome_words())
                        .welcomeWords(updateLandingPageVo.getWelcome_words())
                        .isLeaveData(updateLandingPageVo.getIs_leave_data())
                        .hasConsultFaq(updateLandingPageVo.getIs_leave_data())
                        .consultFaq(updateLandingPageVo.getConsult_faq())
                        .customerServiceType(updateLandingPageVo.getCustomer_service_type())
                        .profile(updateLandingPageVo.getProfile())
                        .connectType(updateLandingPageVo.getConnect_type())
                        .build() : new ConsultLandingPageDto())
                .build();
    }

    public UpdateLandingPageNameDto convertUpdateNameVo2Dto(UpdateLandingPageNameVo updateVo, Integer accountId) {
        return UpdateLandingPageNameDto.builder()
                .accountId(accountId)
                .pageId(updateVo.getPage_id())
                .pageName(updateVo.getPage_name())
                .build();
    }

    public String validateImageUpload(String widthHeightRatio, MultipartFile multipartFile, File file) throws IOException {
        Assert.notNull(multipartFile, "文件信息不可为空");
        Assert.isTrue(multipartFile.getContentType().contains("image"), "该文件不是图片类型");
        String fileTypeByFile = getFileTypeByFile(file);
        Assert.isTrue(!Strings.isNullOrEmpty(fileTypeByFile), "文件格式不符合要求");

        CollageMediaTypeEnum imageGifType = "gif".equals(fileTypeByFile) ? CollageMediaTypeEnum.GIF_FILE : CollageMediaTypeEnum.PIC_FILE;

        ImageResolution imageInfo = ImageResolution.builder().build();

        if (imageGifType.getCode().equals(CollageMediaTypeEnum.GIF_FILE.getCode())) {
            GifDecoder.GifImage gif = GifDecoder.read(readInputStream(new FileInputStream(file)));
            imageInfo.setHeight(gif.getHeight());
            imageInfo.setWidth(gif.getWidth());
        } else {
            imageInfo = ImageUtils.getImageResolution(file);
        }
        Assert.isTrue(imageInfo.getWidth() > 0, "图片宽度必须大于0");
        Assert.isTrue(imageInfo.getHeight() > 0, "图片高度必须大于0");

        if (!Strings.isNullOrEmpty(widthHeightRatio)) {
            Assert.isTrue(widthHeightRatio.equals(this.getImageRatio(imageInfo.getWidth(), imageInfo.getHeight())), "图片尺寸不符合比例要求");
        }
        //@640w_400h.webp
        return String.format(IMAGE_COMPRESSION_SUFFIX, imageInfo.getWidth(), imageInfo.getHeight());
    }

    public static byte[] readInputStream(InputStream inStream) throws IOException {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = 0;
        while ((len = inStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, len);
        }
        inStream.close();
        return outStream.toByteArray();

    }

    public String getFileTypeFromPath(String fileName) {
        Assert.hasText(fileName, "文件名称不可为空");
        String suffix = StringUtils.trimWhitespace(fileName.substring(fileName.lastIndexOf(".") + 1)).toLowerCase();
        Assert.isTrue(STATIC_FILE_TYPE_SET.contains(suffix), "无效的静态文件类型");
        return String.format(MgkConstants.BFS_DIR_SUFFIX, suffix);
    }



    private String getImageRatio(int width, int height) {
        int cd = this.getGreatestCommonDivisor(width, height);
        return String.format("%d:%d", width / cd, height / cd);
    }

    private int getGreatestCommonDivisor(int num1, int num2) {
        int d;
        if (num1 < num2) {
            d = num1;
        } else {
            d = num2;
        }
        while (d >= 1) {
            if (num1 % d == 0 && num2 % d == 0) {
                break;
            }
            d--;
        }
        return d;
    }

    public List<UposVideoVo> convertUposVideoDtos2Vos(List<Integer> bizIds, Map<Integer, SsaUposVideoDto> uposVideoMap) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return Collections.emptyList();
        }

        return bizIds.stream().map(bizId -> {
            UposVideoVo vo = UposVideoVo.builder()
                    .biz_id(bizId)
                    .status(SsaSplashScreenVideoStatus.TRANS_CODING.getCode())
                    .status_desc(SsaSplashScreenVideoStatus.TRANS_CODING.getDesc())
                    .build();
            if (!CollectionUtils.isEmpty(uposVideoMap)
                    && uposVideoMap.containsKey(bizId)) {
                SsaUposVideoDto uposVideoDto = uposVideoMap.get(bizId);
                if (uposVideoDto != null) {
                    vo.setFile_name(uposVideoDto.getFileName());
                    vo.setUpos_url(uposVideoDto.getUposUrl());
                    vo.setXcode_upos_url(StringUtils.isEmpty(uposVideoDto.getXcodeUposUrl()) ? null
                            : uposComponent.getXcodeVideoUrl(uposVideoDto.getXcodeUposUrl()));
                    vo.setXcode_md5(uposVideoDto.getXcodeMd5());
                    vo.setXcode_width(uposVideoDto.getXcodeWidth());
                    vo.setXcode_height(uposVideoDto.getXcodeHeight());
                    vo.setStatus(uposVideoDto.getStatus());
                    vo.setStatus_desc(SsaSplashScreenVideoStatus.getByCode(uposVideoDto.getStatus()).getDesc());
                    vo.setXcode_size(uposVideoDto.getXcodeSize() == null ? BigDecimal.ZERO : BigDecimal.valueOf(uposVideoDto.getXcodeSize() / 1024 / 1024));
                    vo.setXcode_duration(SsaUtils.getVideoSecDuration(uposVideoDto.getXcodeDuration()));
                    vo.setVideo_cover_urls(uposVideoDto.getVideoCoverUrls());
                    vo.setVideo_cover_url(uposVideoDto.getCover());
                }
            }
            return vo;
        }).collect(Collectors.toList());
    }


    public List<MgkHotLandingPageVo> convertHotPageDtos2Vos(List<MgkHotLandingPageDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return records.stream().map(this::convertHotPageDto2Vo).collect(Collectors.toList());
    }

    public MgkHotLandingPageVo convertHotPageDto2Vo(MgkHotLandingPageDto dto) {
        return MgkHotLandingPageVo.builder()
                .page_id(String.valueOf(dto.getPageId()))
                .name(dto.getName())
                .title(dto.getTitle())
                .page_cover(dto.getPageCover())
//                .ctr(String.valueOf(dto.getCtr()))
                .ctr_rank(dto.getCtrRank())
                .ctr_rank_desc(dto.getCtrRankDesc())
                .hot_page_url(dto.getHotPageUrl())
                .hot_page_category(dto.getHotPageCategory())
                .build();
    }

    public MgkPageDownloadComponentHeightDto convertDownloadComponentVo2Dto(PageComponentVo vo) {
        return MgkPageDownloadComponentHeightDto.builder()
                .totalBlockSize(StringUtils.isEmpty(vo.getTotal_block_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(vo.getTotal_block_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .totalDownloadComponentSize(StringUtils.isEmpty(vo.getTotal_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(vo.getTotal_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .totalFirstScreenDownloadComponentSize(StringUtils.isEmpty(vo.getTotal_first_screen_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(vo.getTotal_first_screen_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .maxDownloadComponentSize(StringUtils.isEmpty(vo.getMax_download_component_size()) ? 0 : BigDecimal.valueOf(Double.parseDouble(vo.getMax_download_component_size())).setScale(0, RoundingMode.HALF_UP).intValue())
                .build();
    }

    public PreviewKeyVo convertPreviewKeyDto2Vo(PreviewKeyDto previewKeyDto) {
        return PreviewKeyVo.builder()
                .page_id(previewKeyDto.getPageId().toString())
                .preview_key(previewKeyDto.getPreviewKey())
                .shadow_version(previewKeyDto.getShadowVersion().toString())
                .build();
    }

    public List<LandingPageHotPointVo> dtoToVo(List<LandingPageHotPointDto> landingPageHotPointDtos) {
        return landingPageHotPointDtos.stream().map(
                r -> LandingPageHotPointVo.builder()
                        .page_id(r.getPageId())
                        .account_id(r.getAccountId())
                        .page_type(r.getPageType())
                        .click_cnt(r.getClickCnt())
                        .page_x(r.getPageX())
                        .page_y(r.getPageY())
                        .build()
        ).collect(Collectors.toList());
    }

    public ConsultTokenVo convertConsultTokenDto2Vo(ConsultTokenDto previewKeyDto) {
        return ConsultTokenVo.builder()
                .token(previewKeyDto.getToken())
                .validate_time(previewKeyDto.getValidateTime())
                .build();
    }
}
