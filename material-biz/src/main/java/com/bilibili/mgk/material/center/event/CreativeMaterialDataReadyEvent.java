package com.bilibili.mgk.material.center.event;

import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/9
 */
@Getter
@Setter
@Accessors(chain = true)
public class CreativeMaterialDataReadyEvent extends ApplicationEvent {

    private String logdate;

    private LocalDate logdateDate;

    public CreativeMaterialDataReadyEvent(Object source) {
        super(source);
    }
}
