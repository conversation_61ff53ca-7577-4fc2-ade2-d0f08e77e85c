package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 */

@AllArgsConstructor
public enum AddWayEnum {
    //https://developer.work.weixin.qq.com/document/path/92114#%E6%9D%A5%E6%BA%90%E5%AE%9A%E4%B9%89

    CUSTOMER_ACQUISITION(16, "获客链接"),

    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static AddWayEnum getByCode(int code) {
        for (AddWayEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code AddWayEnum " + code);
    }

}
