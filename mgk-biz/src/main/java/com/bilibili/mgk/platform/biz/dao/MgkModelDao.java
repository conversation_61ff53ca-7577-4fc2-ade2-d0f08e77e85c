package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkModelPo;
import com.bilibili.mgk.platform.biz.po.MgkModelPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkModelDao {
    long countByExample(MgkModelPoExample example);

    int deleteByExample(MgkModelPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkModelPo record);

    int insertBatch(List<MgkModelPo> records);

    int insertUpdateBatch(List<MgkModelPo> records);

    int insert(MgkModelPo record);

    int insertUpdateSelective(MgkModelPo record);

    int insertSelective(MgkModelPo record);

    List<MgkModelPo> selectByExample(MgkModelPoExample example);

    MgkModelPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkModelPo record, @Param("example") MgkModelPoExample example);

    int updateByExample(@Param("record") MgkModelPo record, @Param("example") MgkModelPoExample example);

    int updateByPrimaryKeySelective(MgkModelPo record);

    int updateByPrimaryKey(MgkModelPo record);
}