package com.bilibili.mgk.platform.portal.webapi.archive.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: ArchiveBizIdAvidMappingVo
 * @author: gaoming
 * @date: 2021/12/29
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArchiveBizIdAvidMappingVo {
    @ApiModelProperty(value = "bizId")
    private Integer biz_id;

    @ApiModelProperty(value = "avid和cid")
    private AidCidVo aid_cid;
}
