package com.bilibili.mgk.platform.api.material.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/22
 */
@Data
@Accessors(chain = true)
public class MaterialQueryProfilesDTO implements Serializable {

    /**
     * 行业信息
     */

    private List<ProfileInfoDTO> industryInfos;


    /**
     *
     */
    private List<ProfileInfoDTO> promotionPurposeTypeInfos;


    /**
     *
     */
    private List<ProfileInfoDTO> gameCategoryInfos;


    /**
     *
     */
    private List<ProfileInfoDTO> itemSourceInfos;

    /**
     *
     */
    private List<ProfileInfoDTO> daihuoFirstCategoryInfos;


    /**
     * firstCategoryId-> secondCatories
     */
    private Map<String, List<ProfileInfoDTO>> daihuoSecondCategoryInfos;


    private List<ProfileInfoDTO> biliVideoBussInterestInfos;


    /**
     * b站热门视频一级分区
     */
    private List<ProfileInfoDTO> biliVideoFirstCategoryInfos;

    private List<ProfileInfoDTO> materialTypeInfos;

    /**
     * 一级行业
     */
    private List<ProfileInfoDTO> commerceFirstCategoryInfos;

    /**
     * 二级行业
     */
    private Map<String, List<ProfileInfoDTO>> commerceSecondCategoryInfos;


    private List<ProfileInfoDTO> inspirationCaseArchTypeInfos;


    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProfileInfoDTO implements Serializable {

        private String id;
        private String name;

    }
}
