package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenFormDataDto {


    //base64处理过的手机号
    private String pn;

    @NotNull
    private String appkey;

    private String verify_code;
    /**
     * 输入手机号验证码
     * 判断验证码是否正确
     */
    private String ttcode;

    //1-用户提交 2-本机号码 3-从用户的登陆信息里获取
    private Integer phone_channel;

    //当phone_channel等于3的时候，用mid，不用pn(此时的pn手机号是主站打码的手机号150****2456这种)
    private String mid;
}
