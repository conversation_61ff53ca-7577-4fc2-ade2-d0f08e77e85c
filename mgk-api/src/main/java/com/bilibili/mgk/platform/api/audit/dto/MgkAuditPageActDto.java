package com.bilibili.mgk.platform.api.audit.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName MgkAuditPageActDto
 * <AUTHOR>
 * @Date 2022/12/13 7:14 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MgkAuditPageActDto implements Serializable {
    private static final long serialVersionUID = 10121210L;

    private Long pageId;

    private String auditorName;

    private String reason;

    private Long shadowVersion;
}
