package com.bilibili.mgk.platform.portal.webapi.account.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/1/26
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccountVo {
    @ApiModelProperty(value = "账号ID")
    private Integer account_id;
    @ApiModelProperty(value = "用户名称")
    private String username;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "公司名称")
    private String company_name;
    @ApiModelProperty(value = "客户名称")
    private String customer_name;
    @ApiModelProperty(value = "推广域名")
    private String brand_domain;
    @ApiModelProperty(value = "代理商名称")
    private String agent_name;
}
