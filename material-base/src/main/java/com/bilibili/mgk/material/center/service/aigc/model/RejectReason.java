package com.bilibili.mgk.material.center.service.aigc.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/8
 */
@Data
@EqualsAndHashCode
public class RejectReason {


    private String reason;


    private String key;


    public RejectReason(String reason) {

        this.reason = reason;
        this.key = DigestUtils.md5Hex(reason);
    }


}
