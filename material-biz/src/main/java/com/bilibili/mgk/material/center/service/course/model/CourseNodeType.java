package com.bilibili.mgk.material.center.service.course.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/18
 */
@RequiredArgsConstructor
@Getter
public enum CourseNodeType {

    directory("directory"),  // 目录

    course("course"),

    ;


    private final String nodeType;


    public static CourseNodeType fromNodeType(String nodeType) {
        for (CourseNodeType value : values()) {
            if (value.nodeType.equals(nodeType)) {
                return value;
            }
        }
        return null;
    }

}
