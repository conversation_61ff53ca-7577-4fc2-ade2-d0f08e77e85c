package com.bilibili.mgk.platform.portal.webapi.school.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchResultVo {
    @ApiModelProperty(value = "搜索结果类型")
    private Byte type;

    @ApiModelProperty(value = "搜索结果标题")
    private String title;

    @ApiModelProperty(value = "搜索结果显示内容")
    private String content;

    @ApiModelProperty(value = "文章id")
    private String articleId;
}
