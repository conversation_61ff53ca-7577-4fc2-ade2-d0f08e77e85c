package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkHotVideoCollectPo implements Serializable {
    /**
     * 自增Id
     */
    private Integer id;

    /**
     * 收藏Id
     */
    private Long collectId;

    /**
     * 用户Id
     */
    private Integer accountId;

    /**
     * bvid
     */
    private String bvid;

    /**
     * 视频标题
     */
    private String title;

    /**
     * 收藏类型 0-热门视频 1-热门广告
     */
    private Integer collectType;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 广告类型和创意Id
     */
    private String adTypeCreativeId;

    /**
     * 数据日期
     */
    private String logDate;

    /**
     * 0-7d 1-30d 现在仅带货热门广告使用
     */
    private Integer dayType;

    private static final long serialVersionUID = 1L;
}