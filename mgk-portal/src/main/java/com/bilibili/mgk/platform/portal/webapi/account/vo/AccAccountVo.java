package com.bilibili.mgk.platform.portal.webapi.account.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: AccAccountVo
 * @author: gaoming
 * @date: 2021/10/18
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccAccountVo {

    @ApiModelProperty(value = "是否含有落地页表单 0-不包含 1-包含")
    private Integer has_mgk_form;

    @ApiModelProperty(value = "建站隐私政策是否同意 0-未设置 1-同意 2 不同意")
    private Integer mgk_form_privacy_policy;
}
