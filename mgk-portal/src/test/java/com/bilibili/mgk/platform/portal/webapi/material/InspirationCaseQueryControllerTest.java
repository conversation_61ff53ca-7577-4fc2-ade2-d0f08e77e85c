package com.bilibili.mgk.platform.portal.webapi.material;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/23
 */
public class InspirationCaseQueryControllerTest {


    @Test
    public void test() {

        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM");

        YearMonth test = YearMonth.parse("2024-04", pattern);

        System.out.println(test);
    }


}