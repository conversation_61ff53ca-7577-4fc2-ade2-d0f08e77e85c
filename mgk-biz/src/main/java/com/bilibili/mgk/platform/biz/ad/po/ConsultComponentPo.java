package com.bilibili.mgk.platform.biz.ad.po;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
public class ConsultComponentPo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 商业账号id
     */
    private Long accountId;

    /**
     * 文案
     */
    private String content;

    /**
     * 工具id
     */
    private Long toolId;

    /**
     * 状态，0是未生效，1已生效
     */
    private Byte toolStatus;

    /**
     * 添加时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getToolId() {
        return toolId;
    }

    public void setToolId(Long toolId) {
        this.toolId = toolId;
    }

    public Byte getToolStatus() {
        return toolStatus;
    }

    public void setToolStatus(Byte toolStatus) {
        this.toolStatus = toolStatus;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}