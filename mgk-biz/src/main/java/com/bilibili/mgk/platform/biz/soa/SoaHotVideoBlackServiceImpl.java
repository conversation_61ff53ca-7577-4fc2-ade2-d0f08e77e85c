package com.bilibili.mgk.platform.biz.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoBlackDto;
import com.bilibili.mgk.platform.api.hot_video.dto.NewHotVideoBlackDto;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoBlackService;
import com.bilibili.mgk.platform.api.hot_video.soa.ISoaHotVideoBlackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @file: SoaHotVideoBlackServiceImpl
 * @author: gaoming
 * @date: 2020/12/23
 * @version: 1.0
 * @description:
 **/
@Service
@Slf4j
public class SoaHotVideoBlackServiceImpl implements ISoaHotVideoBlackService {

    @Autowired
    private IHotVideoBlackService hotVideoBlackService;

    @Override
    public List<HotVideoBlackDto> getBlackList(Integer blackType) {
        return hotVideoBlackService.getBlackList(blackType);
    }

    @Override
    public Integer insert(Operator operator, NewHotVideoBlackDto dto) {
        return hotVideoBlackService.insert(operator, dto);
    }

    @Override
    public void enable(Operator operator, String bvid, Integer blackType) {
        hotVideoBlackService.enable(operator, bvid, blackType);
    }

    @Override
    public void disable(Operator operator, String keyId, Integer blackType) {
        hotVideoBlackService.disable(operator, keyId, blackType);
    }
}
