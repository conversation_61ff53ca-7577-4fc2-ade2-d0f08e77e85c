package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoCollectDto;
import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;

/**
 * @file: MgkValidServiceTest
 * @author: gaoming
 * @date: 2021/01/15
 * @version: 1.0
 * @description:
 **/
public class MgkValidServiceTest extends BaseMockitoTest {

    @InjectMocks
    MgkValidService mgkValidService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testValidCollectDto() {
        mgkValidService.validCollectDto(HotVideoCollectDto.builder()
                .adTypeCreativeId("1")
                .collectType(1)
                .title("1")
                .isDeleted(1)
                .accountId(1)
                .bvid("1")
                .collectId(1L)
                .id(1)
                .build());
    }
}