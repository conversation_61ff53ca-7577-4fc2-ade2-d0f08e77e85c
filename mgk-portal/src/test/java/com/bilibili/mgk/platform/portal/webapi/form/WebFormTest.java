package com.bilibili.mgk.platform.portal.webapi.form;

import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.launch.api.soa.ISoaCreativeService;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.mgk.platform.api.data.dto.ReportDataDto;
import com.bilibili.mgk.platform.api.data.service.IMgkDataService;
import com.bilibili.mgk.platform.api.form.service.IMgkCmService;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.openapi.form.ChinaMobileChoseController;
import com.bilibili.mgk.platform.portal.openapi.form.vo.PhoneInfoQueryReqVo;
import com.bilibili.mgk.platform.portal.openapi.form.vo.PhoneLockUpdateReqVo;
import com.bilibili.mgk.platform.portal.openapi.form.vo.PhoneNoOrderVo;
import com.bilibili.mgk.platform.portal.openapi.form.vo.ReportDataVo;
import com.bilibili.mgk.platform.portal.service.WebFormService;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * @file: HotAdsControllerTest
 * @author: gaoming
 * @date: 2021/01/15
 * @version: 1.0
 * @description:
 **/
public class WebFormTest extends BaseMockitoTest {

    @InjectMocks
    private WebFormService formService;

    @Mock
    private ISoaCreativeService soaCreativeService;
    @Mock
    private IMgkLandingPageService mgkLandingPageService;


    private Context context;

    @Before
    public void setUp() throws Exception {
        context = new Context();
        context.setAccountId(10005);
        context.setType(OperatorType.SYSTEM.getCode());
        context.setProxyName("SYS_TEST");
        context.setSalesType(SalesType.CPM.getCode());
        context.setUsername("bilibili_user");
        context.setProxyId(1);

    }

    @Test
    public void convertFormDataDtos2Vos() throws ServiceException {
        formService.convertFormDataDtos2Vos(Lists.newArrayList(BeanTestUtils
                .initSimpleFields(ReportDataDto.builder().build())));
    }

    @Test
    public void collectingDataFromH5(){
        formService.collectingDataFromH5(1L, BeanTestUtils.initSimpleFields(ReportDataVo.builder().build()));
    }


}