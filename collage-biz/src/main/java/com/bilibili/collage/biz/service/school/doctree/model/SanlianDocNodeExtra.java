package com.bilibili.collage.biz.service.school.doctree.model;

import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeCreateReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeUpdateReq;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.biz.common.doc.tree.common.SnakeCaseBody;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/6
 */
@Data
@Accessors(chain = true)
public class SanlianDocNodeExtra implements SnakeCaseBody {

    ///// infos for article

    /**
     * 行业指南的logo from {@link AdvertiseGuidanceExtra}
     */
    private String image;


    /**
     * 以下 from {@link ArticleExtra}
     */
    @Deprecated
    private String rawArticleId;

    private String cover;

    private Integer markdown;

    private Integer contentType;

    /**
     * 注意是article的url不是commercial的jump_url
     */
    private String url;

    /**
     * 以下 from  {@link  CommercialInfoExtra}
     */
    private String jumpUrl;

    /**
     * 以下from {@link  CommonQuestionExtra}
     */
    private Long articleId;

    private Integer articleType;

    private String articleTitle;

    private Long articleParentId;

    private String articleParentName;

    private Integer sanlianOrderNum;



    /**
     * 以下from {@link  LatestInfoExtra}
     */
    private Long linkNodeId;

    /**
     * 链接挂链的树类型
     */
    private SanlianSchoolTreeType linkType;


    public static SanlianDocNodeExtra of(SanlianNodeCreateReq req) {

        return new SanlianDocNodeExtra()
                .setImage(req.getImage())
                .setRawArticleId(req.getRawArticleId())
                .setCover(req.getCover())
                .setMarkdown(req.getMarkdown())
                .setContentType(req.getContentType())
                .setUrl(req.getUrl())
                .setJumpUrl(req.getJumpUrl())
                .setArticleId(req.getArticleId())
                .setArticleType(req.getArticleType())
                .setArticleTitle(req.getArticleTitle())
                .setArticleParentId(req.getArticleParentId())
                .setArticleParentName(req.getArticleParentName())
                .setSanlianOrderNum(req.getSanlianOrderNum())
                .setLinkNodeId(req.getLinkNodeId())
                .setLinkType(req.getLinkType());
    }

    public static SanlianDocNodeExtra of(SanlianNodeUpdateReq req) {

        return new SanlianDocNodeExtra()
                .setImage(req.getImage())
                .setRawArticleId(req.getRawArticleId())
                .setCover(req.getCover())
                .setMarkdown(req.getMarkdown())
                .setContentType(req.getContentType())
                .setUrl(req.getUrl())
                .setJumpUrl(req.getJumpUrl())
                .setArticleId(req.getArticleId())
                .setArticleType(req.getArticleType())
                .setArticleTitle(req.getArticleTitle())
                .setArticleParentId(req.getArticleParentId())
                .setArticleParentName(req.getArticleParentName())
                .setSanlianOrderNum(req.getSanlianOrderNum())
                .setLinkNodeId(req.getLinkNodeId())
                .setLinkType(req.getLinkType());
    }

    public static SanlianDocNodeExtra fromBizExtra(String extra) {

        return Optional.ofNullable(JsonUtil.readValue(extra, SanlianDocNodeExtra.class))
                .orElse(new SanlianDocNodeExtra());
    }

    public String toBizExtra() {

        return JsonUtil.writeValueAsString(this);
    }


}
