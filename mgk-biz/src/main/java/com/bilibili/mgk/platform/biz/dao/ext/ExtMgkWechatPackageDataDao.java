package com.bilibili.mgk.platform.biz.dao.ext;

import com.bilibili.mgk.platform.biz.po.ExtMgkWechatAccountDataPo;
import com.bilibili.mgk.platform.biz.po.ExtMgkWechatPackageDataPo;
import com.bilibili.mgk.platform.biz.po.ExtMgkWorkWechatDataPo;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName ExtMgkWechatPackageDataDao
 * <AUTHOR>
 * @Date 2022/6/18 4:16 下午
 * @Version 1.0
 **/
public interface ExtMgkWechatPackageDataDao {

    List<ExtMgkWechatAccountDataPo> getWechatAccountDataList(@Param("wechatAccountIds")List<Integer> wechatAccountIds);

    List<ExtMgkWechatAccountDataPo> getWechatAccountDataListWithoutCheat(@Param("wechatAccountIds")List<Integer> wechatAccountIds);

    List<ExtMgkWechatPackageDataPo> getWechatPackageDataList(@Param("wechatPackageIds")List<Integer> wechatPackageIds);

    List<ExtMgkWorkWechatDataPo> getWorkWechatDataList(@Param("linkDataIds")List<Long> linkDataIds,
                                                       @Param("startTime") Timestamp startTime,
                                                       @Param("endTime")Timestamp endTime);

    List<ExtMgkWorkWechatDataPo> getWorkWechatDataListWithoutCheat(@Param("linkDataIds")List<Long> linkDataIds,
                                                                   @Param("startTime") Timestamp startTime,
                                                                   @Param("endTime")Timestamp endTime);
}