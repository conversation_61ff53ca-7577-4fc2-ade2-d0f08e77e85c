//package com.bilibili.mgk.platform.biz.es;
//
//import com.dianping.cat.Cat;
//import com.dianping.cat.message.Transaction;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.google.common.collect.Lists;
//import lombok.RequiredArgsConstructor;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.elasticsearch.action.bulk.BulkRequest;
//import org.elasticsearch.action.index.IndexRequest;
//import org.elasticsearch.action.search.SearchRequest;
//import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.client.RestHighLevelClient;
//import org.elasticsearch.common.unit.Fuzziness;
//import org.elasticsearch.common.xcontent.XContentType;
//import org.elasticsearch.index.query.BoolQueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.index.query.RangeQueryBuilder;
//import org.elasticsearch.index.query.functionscore.RandomScoreFunctionBuilder;
//import org.elasticsearch.search.SearchHit;
//import org.elasticsearch.search.aggregations.AggregationBuilder;
//import org.elasticsearch.search.aggregations.AggregationBuilders;
//import org.elasticsearch.search.aggregations.Aggregations;
//import org.elasticsearch.search.builder.SearchSourceBuilder;
//import org.elasticsearch.search.sort.ScoreSortBuilder;
//import org.elasticsearch.search.sort.SortOrder;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//
//import java.text.MessageFormat;
//import java.util.LinkedList;
//import java.util.List;
//
//import static com.bilibili.mgk.platform.common.MgkConstants.MGK_HOT_PAGE_MAX_NUM;
//
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class RestEsService {
//    public static final String ID = "RestEsService";
//    private static final String DEFAULT_TYPE = "doc";
//    private static final String ES_WRITE = "ES_WRITE";
//    private static final String ES_SEARCH = "ES_SEARCH";
//    private static final ObjectMapper om = new ObjectMapper();
//
////    private final RestHighLevelClient restHighLevelClient;
//
//    public<T extends HasId> void batchIndex(String index, List<T> bos) {
//        batchIndex(index, DEFAULT_TYPE, bos);
//    }
//
//    @SneakyThrows
//    public<T extends HasId> void batchIndex(String index, String type, List<T> bos) {
//        if (CollectionUtils.isEmpty(bos)) return;
//
//        for (List<T> list : Lists.partition(bos, 1000)) {
//            final BulkRequest bulkReq = new BulkRequest();
//            for (T bo : list) {
//                final byte[] bytes = om.writeValueAsBytes(bo);
//                final IndexRequest indexReq = new IndexRequest(index, type, bo.getId())
//                        .source(bytes, XContentType.JSON);
//                bulkReq.add(indexReq);
//            }
//            batchSaveOperation(bulkReq, index, list.size());
//        }
//    }
//
//    private void batchSaveOperation(BulkRequest bulkReq, String index, int count) {
//        final Transaction transaction = Cat.newTransaction(ES_WRITE, index);
//        try {
//            restHighLevelClient.bulk(bulkReq);
//            Cat.logMetricForCount("ES_WRITE_REQUEST_COUNT");
//            Cat.logMetricForSum("ES_WRITE_DOC_COUNT", count);
//            transaction.setStatus(Transaction.SUCCESS);
//        } catch (Throwable t) {
//            log.error(MessageFormat.format("{0}: 批量更新ES失败", ID), t);
//            transaction.setStatus(t);
//        } finally {
//            transaction.complete();
//        }
//    }
//
//    public<T> List<T> search(String index, SearchParams searchParams, Class<T> clazz) {
//        return search(index, DEFAULT_TYPE, searchParams, clazz);
//    }
//
//    @SneakyThrows
//    public<T> List<T> search(String index, String type, SearchParams searchParams, Class<T> clazz) {
//        final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        consumeSearchParams(searchSourceBuilder, searchParams);
//        final SearchRequest req = new SearchRequest(index)
//                .types(type)
//                .source(searchSourceBuilder);
//        final SearchResponse resp = searchOperation(req, index);
//        return extractFromHits(resp, clazz);
//    }
//
//    @SneakyThrows
//    public static <T> List<T> extractFromHits(SearchResponse searchResponse, Class<T> clazz) {
//        final List<T> list = new LinkedList<>();
//        for (SearchHit hit : searchResponse.getHits()) {
//            final T t = om.readValue(hit.getSourceRef().streamInput(), clazz);
//            list.add(t);
//        }
//        return list;
//    }
//
//    @SneakyThrows
//    public<T> List<T> agg(String index, String type, SearchParams searchParams, BucketAggInterpreter<T> aggInterpreter) {
//        final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        searchParams.setSize(1);
//        consumeSearchParams(searchSourceBuilder, searchParams);
//        searchSourceBuilder.aggregation(aggInterpreter.getRootAggBuilder());
//        final SearchRequest req = new SearchRequest(index)
//                .types(type)
//                .source(searchSourceBuilder);
//        log.info("RestEsService Agg Query: " + req.source().toString());
//        final SearchResponse resp = searchOperation(req, index);
//        return aggInterpreter.extract(resp);
//    }
//
//    public Aggregations agg(String index, String type, SearchParams searchParams, AggregationBuilder aggregationBuilder) {
//        final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        consumeSearchParams(searchSourceBuilder, searchParams);
//        searchSourceBuilder.aggregation(aggregationBuilder);
//        final SearchRequest req = new SearchRequest(index)
//                .types(type)
//                .source(searchSourceBuilder);
//        log.info("RestEsService Agg Query: " + req.source().toString());
//        final SearchResponse resp = searchOperation(req, index);
//        return resp.getAggregations();
//    }
//
//    private void consumeSearchParams(SearchSourceBuilder searchSourceBuilder, SearchParams searchParams) {
//        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//        searchParams.getRanges().forEach((k, v) -> {
//            final RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(k);
//            rangeQueryBuilder.from(v.getMin(), v.isMinInclusive());
//            rangeQueryBuilder.to(v.getMax(), v.isMaxInclusive());
//            boolQueryBuilder.must(rangeQueryBuilder);
//        });
//        searchParams.getTerms().forEach((k, v) -> {
//            boolQueryBuilder.must(QueryBuilders.termsQuery(k, v.toArray()));
//        });
//        searchParams.getNumberTerms().forEach((k, v) -> {
//            final BoolQueryBuilder innerBoolQueryBuilder = QueryBuilders.boolQuery().minimumShouldMatch(1);
//            v.forEach(x -> {
//                final RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(k);
//                rangeQueryBuilder.from(x.getMin(), x.isMinInclusive());
//                rangeQueryBuilder.to(x.getMax(), x.isMaxInclusive());
//                innerBoolQueryBuilder.should(rangeQueryBuilder);
//            });
//            boolQueryBuilder.must(innerBoolQueryBuilder);
//        });
//        searchParams.getMatches().forEach((k, v) -> {
//            boolQueryBuilder.must(QueryBuilders.matchQuery(k, v).fuzziness(Fuzziness.AUTO));
//        });
//        if (searchParams.isRandomResult()) {
//            searchSourceBuilder.query(QueryBuilders.functionScoreQuery(boolQueryBuilder,
//                    new RandomScoreFunctionBuilder().seed(System.currentTimeMillis())));
//        } else {
//            searchSourceBuilder.query(boolQueryBuilder);
//        }
//        searchSourceBuilder.from(searchParams.getPage());
//        searchSourceBuilder.size(searchParams.getSize());
//        if(StringUtils.hasText(searchParams.getSortName())){
//            searchSourceBuilder.sort(searchParams.getSortName(), searchParams.getSortOrder());
//        }
//    }
//
//    @SneakyThrows
//    private SearchResponse searchOperation(SearchRequest searchReq, String index) {
//        final Transaction transaction = Cat.newTransaction(ES_SEARCH, index);
//        try {
//            log.info("RestEsService searchOperation query: " + searchReq.source().toString());
//            final SearchResponse response = restHighLevelClient.search(searchReq);
//            Cat.logMetricForCount("ES_SEARCH_REQUEST_COUNT");
//            Cat.logMetricForSum("ES_SEARCH_DOC_COUNT", response.getHits().totalHits);
//            transaction.setStatus(Transaction.SUCCESS);
//            return response;
//        } catch (Throwable t) {
//            log.error(MessageFormat.format("{0}: 搜索ES失败", ID), t);
//            transaction.setStatus(t);
//            throw t;
//        } finally {
//            transaction.complete();
//        }
//    }
//}
