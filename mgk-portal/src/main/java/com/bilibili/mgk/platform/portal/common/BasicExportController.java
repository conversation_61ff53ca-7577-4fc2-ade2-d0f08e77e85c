package com.bilibili.mgk.platform.portal.common;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.portal.annotation.CsvHeader;
import com.bilibili.mgk.platform.portal.annotation.ExcelResources;
import com.bilibili.mgk.platform.portal.excel.ExcelUtil;
import com.bilibili.mgk.platform.portal.exception.WebApiExceptionCode;
import com.bilibili.mgk.platform.portal.webapi.wechat.vo.WechatReportDataVo;
import com.google.common.collect.Lists;
import com.mysema.commons.lang.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.supercsv.io.CsvBeanWriter;
import org.supercsv.io.ICsvBeanWriter;
import org.supercsv.prefs.CsvPreference;

import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller
public class BasicExportController<T> extends BasicController {

    protected List<String> buildExcelHeaders(Class<T> clazz) {
        List<String> headers = Lists.newArrayList();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelResources.class)) {
                ExcelResources excelResources = field.getAnnotation(ExcelResources.class);
                headers.add(excelResources.title());
            }
        }
        return headers;
    }


    protected List<String> buildExcelHeaders(Class<T> clazz, List<String> itemLabels) {
        List<String> headers = Lists.newArrayList();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelResources.class)) {
                ExcelResources excelResources = field.getAnnotation(ExcelResources.class);
                headers.add(excelResources.title());
            }
        }

        if (!CollectionUtils.isEmpty(itemLabels)) {
            headers.addAll(itemLabels);
        }
        return headers;
    }

    protected List<List<String>> getExcelDatas(List<T> reportDataVos) {
        if (CollectionUtils.isEmpty(reportDataVos)) {
            return Collections.emptyList();
        }
        Field[] fields = WechatReportDataVo.class.getDeclaredFields();
        List<List<String>> datas = Lists.newArrayList();
        for (T t : reportDataVos) {
            List<String> rowData = Lists.newArrayList();
            for (Field field : fields) {
                if (field.isAnnotationPresent(ExcelResources.class)) {
                    try {
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), WechatReportDataVo.class);
                        Method getMethod = pd.getReadMethod();
                        Object result = getMethod.invoke(t, null);
                        rowData.add(String.valueOf(result));
                    } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                        log.error("getExcelDatas failed", e);
                        throw new IllegalStateException(e);
                    }
                }
            }
            datas.add(rowData);
        }
        return datas;
    }


    public void csvWrite(HttpServletResponse response, List data, String fileName) throws IOException, ServiceException {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException(WebApiExceptionCode.NOT_FOUND);
        }

        Object object = data.get(0);
        Class clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        String[] header = new String[fields.length];
        String[] showHeader = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            showHeader[i] = fields[i].getAnnotation(CsvHeader.class).value();
            header[i] = fields[i].getName();
        }

        response.setContentType("text/csv;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        ICsvBeanWriter csvWriter = new CsvBeanWriter(response.getWriter(),
                CsvPreference.EXCEL_PREFERENCE);
        csvWriter.writeHeader(showHeader);

        for (Object obj : data) {
            csvWriter.write(obj, header);
        }
        csvWriter.close();
    }

    public void exportExcelWithMutilSheet(HttpServletResponse response, Map<String, List> datas, Class clazz, String fileName) throws ServiceException, IOException {
        long time = System.currentTimeMillis();
        ByteArrayOutputStream os = null;

        InputStream is = null;
        try {
            os = new ByteArrayOutputStream();
            ExcelUtil.getInstance().exportObj2ExcelWithMutilSheet(os, datas, clazz, false);

            byte[] content = os.toByteArray();
            is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[65535];
            int length;
            while ((length = is.read(b)) > 0) {
                out.write(b, 0, length);
            }
            os.flush();
        } finally {
            if (null != os) {
                os.close();
            }
            if (null != is) {
                is.close();
            }
        }
    }

    public void exportExcel(HttpServletResponse response, List datas, Class clazz, String fileName) throws ServiceException, IOException {
        long time = System.currentTimeMillis();
        ByteArrayOutputStream os = null;

        InputStream is = null;
        try {
            os = new ByteArrayOutputStream();
            ExcelUtil.getInstance().exportObj2Excel(os, datas, clazz, false);

            byte[] content = os.toByteArray();
            is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[65535];
            int length;
            while ((length = is.read(b)) > 0) {
                out.write(b, 0, length);
            }
            os.flush();
        } finally {
            if (null != os) {
                os.close();
            }
            if (null != is) {
                is.close();
            }
        }
    }

    public void exportList2Excel(HttpServletResponse response, List<String> headers, List<List<String>> datas, String fileName) throws ServiceException, IOException {
        ByteArrayOutputStream os = null;
        InputStream is = null;
        try {
            os = new ByteArrayOutputStream();
            ExcelUtil.getInstance().exportList2Excel(os, headers, datas, false);

            byte[] content = os.toByteArray();
            is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[65535];
            int length;
            while ((length = is.read(b)) > 0) {
                out.write(b, 0, length);
            }
            os.flush();
        } finally {
            if (null != os) {
                os.close();
            }
            if (null != is) {
                is.close();
            }
        }
    }

    public void export2ManySheetExcel(HttpServletResponse response,
                                      Map<String, Pair<List<String>, List<List<String>>>> sheet2HeaderWithData,
                                      String fileName) throws ServiceException, IOException {
        ByteArrayOutputStream os = null;
        InputStream is = null;
        try {
            os = new ByteArrayOutputStream();
            ExcelUtil.getInstance().exportManySheetList2Excel(os, sheet2HeaderWithData, false);

            byte[] content = os.toByteArray();
            is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[65535];
            int length;
            while ((length = is.read(b)) > 0) {
                out.write(b, 0, length);
            }
            os.flush();
        } finally {
            if (null != os) {
                os.close();
            }
            if (null != is) {
                is.close();
            }
        }
    }
}
