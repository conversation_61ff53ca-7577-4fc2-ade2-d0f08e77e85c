package com.bilibili.mgk.platform.portal.webapi.account.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/1/26
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccountProfileVo {
    @ApiModelProperty(value = "账号ID")
    private Integer account_id;
    @ApiModelProperty(value = "是否允许代理商获取表单数据：0-禁止 1-允许")
    private Integer allow_agent_get_form_data;
}
