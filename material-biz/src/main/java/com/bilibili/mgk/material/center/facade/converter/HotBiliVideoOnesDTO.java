package com.bilibili.mgk.material.center.facade.converter;

import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideo;
import com.bilibili.mgk.material.center.service.creative.model.VerifyType;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/15
 */
@Data
@Accessors(chain = true)
public class HotBiliVideoOnesDTO {


    /**
     * 时间窗 1d 7d 30d
     */
    private String dataType;


    private String logDate;


    /**
     *
     */
    //     max(buss_interest) as `buss_interest,
    private String bussInterest;

    //     max(avid) as `avid`,
    private Long avid;

    private String bvid;
    //     max(up_name) as `up_name`,
    private String upName;

    //     max(up_mid) as `up_mid`,
    private Long upMid;


    //     max (tname) as `tname`,
    private String tname;

    //      max(sub_tname) as `sub_tname`,
    private String subTname;

    /**
     * {@link VerifyType}
     */
    //     max(verify_type) as `verify_type`,
    private Integer verifyType;

    private Integer isVerticalScreen;


    // max(pubtime) as `pubtime`,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pubtime;

    //     max(title) as `title`,
    private String title;

    //     max(cover) as `cover`,
    private String cover;

    //     max(tag) as `tag`,
    private String tag;


    //     max(duration) as `duration`,
    private Long duration;


    //     max(play) as `play`,
    //    max(reply) as `reply`,
    //    max(fav) as `fav`,
    //    max (coin) as `coin`,
    //    max(danmu) as `danmu`,
    //    max(share) as `share`,
    //    max(likes) as `likes`,
    private Long play;

    private Long likes;

    private Long coin;


    private Long share;

    private Long fav;


    private Long reply;

    private Long danmu;

    //    sum(play_daily) as `play_daily`,
    //    sum(reply_daily) as `reply_daily`,
    //    sum(fav_daily) as `fav_daily`,
    //    sum(coin_daily) as `coin_daily`,
    //    sum(danmu_daily) as `danmu_daily`,
    //    sum(share_daily) as `share_daily`,
    //    sum(likes_daily) as `likes_daily`,
    private Long playDaily;

    private Long replyDaily;

    private Long favDaily;

    private Long coinDaily;


    private Long danmuDaily;

    private Long shareDaily;

    private Long likesDaily;

    public static HotBiliVideoOnesDTO fromMapValue(Map<String, String> mapValue) {
        return JsonUtil.fromJson(mapValue, HotBiliVideoOnesDTO.class);
    }

    public HotBiliVideo toBO() {
        return OneServiceDTOConverter.converter.toHotBiliVideoBO(this);
    }


}
