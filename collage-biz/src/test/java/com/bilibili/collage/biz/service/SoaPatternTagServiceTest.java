package com.bilibili.collage.biz.service;

import com.bilibili.collage.api.service.IPatternTagService;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.bilibili.collage.biz.soa.SoaPatternTagServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @date 2019/4/2
 **/
@Slf4j
public class SoaPatternTagServiceTest extends BaseMockitoTest {

    @InjectMocks
    private SoaPatternTagServiceImpl testObject;

    @Mock
    private IPatternTagService patternTagService;

    @Test
    public void queryPatternTag () {

        testObject.queryPatternTag("");
    }

    @Test
    public void getPatternTagByIds () {

        testObject.getPatternTagByIds(Lists.newArrayList(1));
    }

    @Test
    public void getPatternTagById () {

        testObject.getPatternTagById(1);
    }

    @Test
    public void deletePatternTag () {

        testObject.deletePatternTag("");
    }
}
