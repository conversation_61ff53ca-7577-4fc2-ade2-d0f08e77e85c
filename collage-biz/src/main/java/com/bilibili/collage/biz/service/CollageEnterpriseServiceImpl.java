package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.CollageEnterpriseDto;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoDto;
import com.bilibili.collage.api.dto.QueryCollageEnterpriseDto;
import com.bilibili.collage.api.service.ICollageEnterpriseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.support.Assert;

import java.util.List;

/**
 * @file: CollageEnterpriseServiceImpl
 * @author: gaoming
 * @date: 2020/12/09
 * @version: 1.0
 * @description:
 **/
@Service
public class CollageEnterpriseServiceImpl implements ICollageEnterpriseService {

    @Autowired
    private CollageEnterpriseServiceDelegate collageEnterpriseServiceDelegate;

    @Autowired
    private CollageValidService collageValidService;

    @Override
    public PageResult<CollageEnterpriseVideoDto> getArchiveVideos(QueryCollageEnterpriseDto queryDto) {

        collageValidService.validQueryEnterpriseDto(queryDto);

        return collageEnterpriseServiceDelegate.getArchiveVideos(queryDto);
    }

    @Override
    public List<CollageEnterpriseDto> getEnterpriseByAccount(Integer accountId) {
        Assert.isTrue(Utils.isPositive(accountId), "账户id不可为空");
        return collageEnterpriseServiceDelegate.getEnterpriseByAccount(accountId);
    }
}
