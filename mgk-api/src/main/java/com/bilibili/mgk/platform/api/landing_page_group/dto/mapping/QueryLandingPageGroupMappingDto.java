package com.bilibili.mgk.platform.api.landing_page_group.dto.mapping;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName QueryLandingPageGroupMappingDto
 * <AUTHOR>
 * @Date 2023/5/18 2:31 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryLandingPageGroupMappingDto {

    private List<Long> pageGroupIdList;

    private List<Long> pageIdList;

    private List<Integer> statusList;

    private Integer groupSource;

    private Integer accountId;
}
