package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName RefreshAppletLandingPageDownloadLimitJob
 * <AUTHOR>
 * @Date 2022/8/16 10:37 上午
 * @Version 1.0
 **/
@Component
@JobHandler("RefreshLandingPageDownloadLimitJob")
public class RefreshLandingPageDownloadLimitJob extends IJobHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IMgkLandingPageService mgkLandingPageService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        LOGGER.info("refresh RefreshLandingPageDownloadLimitJob start.......");

        try {
            mgkLandingPageService.refreshLandingPageDownloadComponentLimit(null);
        } catch (Exception e) {
            LOGGER.info("RefreshLandingPageDownloadLimitJob encounter an exception:{}", e);
        }

        LOGGER.info("refresh RefreshLandingPageDownloadLimitJob finish.......");
        return SUCCESS;
    }
}
