package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * AccAccountQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class AccAccountQueryDSLPo {

    private Integer accountId;

    private Integer accountStatus;

    private Integer accountType;

    private java.sql.Timestamp activeTime;

    private Integer adStatus;

    private java.sql.Date agentAuthExpireDate;

    private Integer agentType;

    private Integer allowCashPay;

    private Integer allowFlowTicketPay;

    private Integer allowFlyCoinPay;

    private Integer allowIncentiveBonusPay;

    private Integer allowSigningBonusPay;

    private Integer areaId;

    private String auditRemark;

    private Integer autoUpdateLabel;

    private String bank;

    private String brandDomain;

    private String businessLicenceCode;

    private java.sql.Date businessLicenceExpireDate;

    private Integer businessRoleId;

    private Integer categoryFirstId;

    private Integer categorySecondId;

    private Integer commerceCategoryFirstId;

    private Integer commerceCategorySecondId;

    private String companyName;

    private String creator;

    private Integer crmCustomerId;

    private java.sql.Timestamp ctime;

    private Integer customerId;

    private Integer departmentId;

    private Integer dependencyAgentId;

    private Integer financeType;

    private Integer firstIndustryTagId;

    private Integer gdStatus;

    private Integer groupId;

    private String icpInfoImage;

    private String icpRecordNumber;

    private java.sql.Timestamp idcardExpireDate;

    private String idcardNumber;

    private Integer idcardType;

    private String internalLinkman;

    private Integer isAgent;

    private Integer isBusinessLicenceIndefinite;

    private Integer isDeleted;

    private Integer isIdcardIndefinite;

    private Integer isInner;

    private Integer isLegalPersonIdcardIndefinite;

    private Integer isSupportContent;

    private Integer isSupportDpa;

    private Integer isSupportFly;

    private Integer isSupportGame;

    private Integer isSupportLocalAd;

    private Integer isSupportMas;

    private Integer isSupportPickup;

    private Integer isSupportSeller;

    private java.sql.Date legalPersonIdcardExpireDate;

    private String legalPersonName;

    private String linkmanAddress;

    private String linkmanEmail;

    private Integer mid;

    private String mobile;

    private java.sql.Timestamp mtime;

    private String name;

    private Integer orderType;

    private Integer passwordStrength;

    private Integer paymentPeriod;

    private String personalAddress;

    private String personalName;

    private String phoneNumber;

    private Integer productId;

    private String productLine;

    private Integer productLineId;

    private Integer qualificationId;

    private String remark;

    private String salt;

    private String saltPassword;

    private Integer secondIndustryTagId;

    private Integer status;

    private Integer thirdIndustryTagId;

    private String username;

    private Integer userType;

    private Integer version;

    private String websiteName;

    private String weibo;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public java.sql.Timestamp getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(java.sql.Timestamp activeTime) {
        this.activeTime = activeTime;
    }

    public Integer getAdStatus() {
        return adStatus;
    }

    public void setAdStatus(Integer adStatus) {
        this.adStatus = adStatus;
    }

    public java.sql.Date getAgentAuthExpireDate() {
        return agentAuthExpireDate;
    }

    public void setAgentAuthExpireDate(java.sql.Date agentAuthExpireDate) {
        this.agentAuthExpireDate = agentAuthExpireDate;
    }

    public Integer getAgentType() {
        return agentType;
    }

    public void setAgentType(Integer agentType) {
        this.agentType = agentType;
    }

    public Integer getAllowCashPay() {
        return allowCashPay;
    }

    public void setAllowCashPay(Integer allowCashPay) {
        this.allowCashPay = allowCashPay;
    }

    public Integer getAllowFlowTicketPay() {
        return allowFlowTicketPay;
    }

    public void setAllowFlowTicketPay(Integer allowFlowTicketPay) {
        this.allowFlowTicketPay = allowFlowTicketPay;
    }

    public Integer getAllowFlyCoinPay() {
        return allowFlyCoinPay;
    }

    public void setAllowFlyCoinPay(Integer allowFlyCoinPay) {
        this.allowFlyCoinPay = allowFlyCoinPay;
    }

    public Integer getAllowIncentiveBonusPay() {
        return allowIncentiveBonusPay;
    }

    public void setAllowIncentiveBonusPay(Integer allowIncentiveBonusPay) {
        this.allowIncentiveBonusPay = allowIncentiveBonusPay;
    }

    public Integer getAllowSigningBonusPay() {
        return allowSigningBonusPay;
    }

    public void setAllowSigningBonusPay(Integer allowSigningBonusPay) {
        this.allowSigningBonusPay = allowSigningBonusPay;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public Integer getAutoUpdateLabel() {
        return autoUpdateLabel;
    }

    public void setAutoUpdateLabel(Integer autoUpdateLabel) {
        this.autoUpdateLabel = autoUpdateLabel;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBrandDomain() {
        return brandDomain;
    }

    public void setBrandDomain(String brandDomain) {
        this.brandDomain = brandDomain;
    }

    public String getBusinessLicenceCode() {
        return businessLicenceCode;
    }

    public void setBusinessLicenceCode(String businessLicenceCode) {
        this.businessLicenceCode = businessLicenceCode;
    }

    public java.sql.Date getBusinessLicenceExpireDate() {
        return businessLicenceExpireDate;
    }

    public void setBusinessLicenceExpireDate(java.sql.Date businessLicenceExpireDate) {
        this.businessLicenceExpireDate = businessLicenceExpireDate;
    }

    public Integer getBusinessRoleId() {
        return businessRoleId;
    }

    public void setBusinessRoleId(Integer businessRoleId) {
        this.businessRoleId = businessRoleId;
    }

    public Integer getCategoryFirstId() {
        return categoryFirstId;
    }

    public void setCategoryFirstId(Integer categoryFirstId) {
        this.categoryFirstId = categoryFirstId;
    }

    public Integer getCategorySecondId() {
        return categorySecondId;
    }

    public void setCategorySecondId(Integer categorySecondId) {
        this.categorySecondId = categorySecondId;
    }

    public Integer getCommerceCategoryFirstId() {
        return commerceCategoryFirstId;
    }

    public void setCommerceCategoryFirstId(Integer commerceCategoryFirstId) {
        this.commerceCategoryFirstId = commerceCategoryFirstId;
    }

    public Integer getCommerceCategorySecondId() {
        return commerceCategorySecondId;
    }

    public void setCommerceCategorySecondId(Integer commerceCategorySecondId) {
        this.commerceCategorySecondId = commerceCategorySecondId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getCrmCustomerId() {
        return crmCustomerId;
    }

    public void setCrmCustomerId(Integer crmCustomerId) {
        this.crmCustomerId = crmCustomerId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getDependencyAgentId() {
        return dependencyAgentId;
    }

    public void setDependencyAgentId(Integer dependencyAgentId) {
        this.dependencyAgentId = dependencyAgentId;
    }

    public Integer getFinanceType() {
        return financeType;
    }

    public void setFinanceType(Integer financeType) {
        this.financeType = financeType;
    }

    public Integer getFirstIndustryTagId() {
        return firstIndustryTagId;
    }

    public void setFirstIndustryTagId(Integer firstIndustryTagId) {
        this.firstIndustryTagId = firstIndustryTagId;
    }

    public Integer getGdStatus() {
        return gdStatus;
    }

    public void setGdStatus(Integer gdStatus) {
        this.gdStatus = gdStatus;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getIcpInfoImage() {
        return icpInfoImage;
    }

    public void setIcpInfoImage(String icpInfoImage) {
        this.icpInfoImage = icpInfoImage;
    }

    public String getIcpRecordNumber() {
        return icpRecordNumber;
    }

    public void setIcpRecordNumber(String icpRecordNumber) {
        this.icpRecordNumber = icpRecordNumber;
    }

    public java.sql.Timestamp getIdcardExpireDate() {
        return idcardExpireDate;
    }

    public void setIdcardExpireDate(java.sql.Timestamp idcardExpireDate) {
        this.idcardExpireDate = idcardExpireDate;
    }

    public String getIdcardNumber() {
        return idcardNumber;
    }

    public void setIdcardNumber(String idcardNumber) {
        this.idcardNumber = idcardNumber;
    }

    public Integer getIdcardType() {
        return idcardType;
    }

    public void setIdcardType(Integer idcardType) {
        this.idcardType = idcardType;
    }

    public String getInternalLinkman() {
        return internalLinkman;
    }

    public void setInternalLinkman(String internalLinkman) {
        this.internalLinkman = internalLinkman;
    }

    public Integer getIsAgent() {
        return isAgent;
    }

    public void setIsAgent(Integer isAgent) {
        this.isAgent = isAgent;
    }

    public Integer getIsBusinessLicenceIndefinite() {
        return isBusinessLicenceIndefinite;
    }

    public void setIsBusinessLicenceIndefinite(Integer isBusinessLicenceIndefinite) {
        this.isBusinessLicenceIndefinite = isBusinessLicenceIndefinite;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsIdcardIndefinite() {
        return isIdcardIndefinite;
    }

    public void setIsIdcardIndefinite(Integer isIdcardIndefinite) {
        this.isIdcardIndefinite = isIdcardIndefinite;
    }

    public Integer getIsInner() {
        return isInner;
    }

    public void setIsInner(Integer isInner) {
        this.isInner = isInner;
    }

    public Integer getIsLegalPersonIdcardIndefinite() {
        return isLegalPersonIdcardIndefinite;
    }

    public void setIsLegalPersonIdcardIndefinite(Integer isLegalPersonIdcardIndefinite) {
        this.isLegalPersonIdcardIndefinite = isLegalPersonIdcardIndefinite;
    }

    public Integer getIsSupportContent() {
        return isSupportContent;
    }

    public void setIsSupportContent(Integer isSupportContent) {
        this.isSupportContent = isSupportContent;
    }

    public Integer getIsSupportDpa() {
        return isSupportDpa;
    }

    public void setIsSupportDpa(Integer isSupportDpa) {
        this.isSupportDpa = isSupportDpa;
    }

    public Integer getIsSupportFly() {
        return isSupportFly;
    }

    public void setIsSupportFly(Integer isSupportFly) {
        this.isSupportFly = isSupportFly;
    }

    public Integer getIsSupportGame() {
        return isSupportGame;
    }

    public void setIsSupportGame(Integer isSupportGame) {
        this.isSupportGame = isSupportGame;
    }

    public Integer getIsSupportLocalAd() {
        return isSupportLocalAd;
    }

    public void setIsSupportLocalAd(Integer isSupportLocalAd) {
        this.isSupportLocalAd = isSupportLocalAd;
    }

    public Integer getIsSupportMas() {
        return isSupportMas;
    }

    public void setIsSupportMas(Integer isSupportMas) {
        this.isSupportMas = isSupportMas;
    }

    public Integer getIsSupportPickup() {
        return isSupportPickup;
    }

    public void setIsSupportPickup(Integer isSupportPickup) {
        this.isSupportPickup = isSupportPickup;
    }

    public Integer getIsSupportSeller() {
        return isSupportSeller;
    }

    public void setIsSupportSeller(Integer isSupportSeller) {
        this.isSupportSeller = isSupportSeller;
    }

    public java.sql.Date getLegalPersonIdcardExpireDate() {
        return legalPersonIdcardExpireDate;
    }

    public void setLegalPersonIdcardExpireDate(java.sql.Date legalPersonIdcardExpireDate) {
        this.legalPersonIdcardExpireDate = legalPersonIdcardExpireDate;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getLinkmanAddress() {
        return linkmanAddress;
    }

    public void setLinkmanAddress(String linkmanAddress) {
        this.linkmanAddress = linkmanAddress;
    }

    public String getLinkmanEmail() {
        return linkmanEmail;
    }

    public void setLinkmanEmail(String linkmanEmail) {
        this.linkmanEmail = linkmanEmail;
    }

    public Integer getMid() {
        return mid;
    }

    public void setMid(Integer mid) {
        this.mid = mid;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getPasswordStrength() {
        return passwordStrength;
    }

    public void setPasswordStrength(Integer passwordStrength) {
        this.passwordStrength = passwordStrength;
    }

    public Integer getPaymentPeriod() {
        return paymentPeriod;
    }

    public void setPaymentPeriod(Integer paymentPeriod) {
        this.paymentPeriod = paymentPeriod;
    }

    public String getPersonalAddress() {
        return personalAddress;
    }

    public void setPersonalAddress(String personalAddress) {
        this.personalAddress = personalAddress;
    }

    public String getPersonalName() {
        return personalName;
    }

    public void setPersonalName(String personalName) {
        this.personalName = personalName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductLine() {
        return productLine;
    }

    public void setProductLine(String productLine) {
        this.productLine = productLine;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public Integer getQualificationId() {
        return qualificationId;
    }

    public void setQualificationId(Integer qualificationId) {
        this.qualificationId = qualificationId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getSaltPassword() {
        return saltPassword;
    }

    public void setSaltPassword(String saltPassword) {
        this.saltPassword = saltPassword;
    }

    public Integer getSecondIndustryTagId() {
        return secondIndustryTagId;
    }

    public void setSecondIndustryTagId(Integer secondIndustryTagId) {
        this.secondIndustryTagId = secondIndustryTagId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getThirdIndustryTagId() {
        return thirdIndustryTagId;
    }

    public void setThirdIndustryTagId(Integer thirdIndustryTagId) {
        this.thirdIndustryTagId = thirdIndustryTagId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getWebsiteName() {
        return websiteName;
    }

    public void setWebsiteName(String websiteName) {
        this.websiteName = websiteName;
    }

    public String getWeibo() {
        return weibo;
    }

    public void setWeibo(String weibo) {
        this.weibo = weibo;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", accountStatus = " + accountStatus + ", accountType = " + accountType + ", activeTime = " + activeTime + ", adStatus = " + adStatus + ", agentAuthExpireDate = " + agentAuthExpireDate + ", agentType = " + agentType + ", allowCashPay = " + allowCashPay + ", allowFlowTicketPay = " + allowFlowTicketPay + ", allowFlyCoinPay = " + allowFlyCoinPay + ", allowIncentiveBonusPay = " + allowIncentiveBonusPay + ", allowSigningBonusPay = " + allowSigningBonusPay + ", areaId = " + areaId + ", auditRemark = " + auditRemark + ", autoUpdateLabel = " + autoUpdateLabel + ", bank = " + bank + ", brandDomain = " + brandDomain + ", businessLicenceCode = " + businessLicenceCode + ", businessLicenceExpireDate = " + businessLicenceExpireDate + ", businessRoleId = " + businessRoleId + ", categoryFirstId = " + categoryFirstId + ", categorySecondId = " + categorySecondId + ", commerceCategoryFirstId = " + commerceCategoryFirstId + ", commerceCategorySecondId = " + commerceCategorySecondId + ", companyName = " + companyName + ", creator = " + creator + ", crmCustomerId = " + crmCustomerId + ", ctime = " + ctime + ", customerId = " + customerId + ", departmentId = " + departmentId + ", dependencyAgentId = " + dependencyAgentId + ", financeType = " + financeType + ", firstIndustryTagId = " + firstIndustryTagId + ", gdStatus = " + gdStatus + ", groupId = " + groupId + ", icpInfoImage = " + icpInfoImage + ", icpRecordNumber = " + icpRecordNumber + ", idcardExpireDate = " + idcardExpireDate + ", idcardNumber = " + idcardNumber + ", idcardType = " + idcardType + ", internalLinkman = " + internalLinkman + ", isAgent = " + isAgent + ", isBusinessLicenceIndefinite = " + isBusinessLicenceIndefinite + ", isDeleted = " + isDeleted + ", isIdcardIndefinite = " + isIdcardIndefinite + ", isInner = " + isInner + ", isLegalPersonIdcardIndefinite = " + isLegalPersonIdcardIndefinite + ", isSupportContent = " + isSupportContent + ", isSupportDpa = " + isSupportDpa + ", isSupportFly = " + isSupportFly + ", isSupportGame = " + isSupportGame + ", isSupportLocalAd = " + isSupportLocalAd + ", isSupportMas = " + isSupportMas + ", isSupportPickup = " + isSupportPickup + ", isSupportSeller = " + isSupportSeller + ", legalPersonIdcardExpireDate = " + legalPersonIdcardExpireDate + ", legalPersonName = " + legalPersonName + ", linkmanAddress = " + linkmanAddress + ", linkmanEmail = " + linkmanEmail + ", mid = " + mid + ", mobile = " + mobile + ", mtime = " + mtime + ", name = " + name + ", orderType = " + orderType + ", passwordStrength = " + passwordStrength + ", paymentPeriod = " + paymentPeriod + ", personalAddress = " + personalAddress + ", personalName = " + personalName + ", phoneNumber = " + phoneNumber + ", productId = " + productId + ", productLine = " + productLine + ", productLineId = " + productLineId + ", qualificationId = " + qualificationId + ", remark = " + remark + ", salt = " + salt + ", saltPassword = " + saltPassword + ", secondIndustryTagId = " + secondIndustryTagId + ", status = " + status + ", thirdIndustryTagId = " + thirdIndustryTagId + ", username = " + username + ", userType = " + userType + ", version = " + version + ", websiteName = " + websiteName + ", weibo = " + weibo;
    }

}

