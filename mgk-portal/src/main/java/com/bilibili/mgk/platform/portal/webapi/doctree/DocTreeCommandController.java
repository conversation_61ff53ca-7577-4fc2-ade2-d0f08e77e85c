package com.bilibili.mgk.platform.portal.webapi.doctree;

import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.biz.common.doc.tree.model.DocFlattenNode;
import com.biz.common.doc.tree.service.DocNodeCommandService;
import com.biz.common.doc.tree.service.vo.NodeCreateReq;
import com.biz.common.doc.tree.service.vo.NodeDeleteReq;
import com.biz.common.doc.tree.service.vo.NodeMoveReq;
import com.biz.common.doc.tree.service.vo.NodeUpdateReq;
import io.swagger.annotations.Api;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/13
 */
@RestController
@FreeLogin
@Api(value = "/doc/tree", tags = "文档树")
@RequestMapping("/web_api/v1/doc/tree/command")
@RequiredArgsConstructor
public class DocTreeCommandController extends BasicController {

    @Resource
    private DocNodeCommandService docNodeCommandService;

    @FreeLogin
    @RequestMapping(value = "/node/create", method = RequestMethod.POST)
    public Response<DocFlattenNode> createNode(
            @ApiIgnore Context context,
            @RequestBody NodeCreateReq req) {

        return Response.SUCCESS(docNodeCommandService.newNode(req));

    }


    /**
     * 删除节点
     *
     * @param req
     * @return
     */
    @FreeLogin
    @RequestMapping(value = "/node/delete", method = RequestMethod.POST)
    public Response<DocFlattenNode> deleteNode(

            @RequestBody NodeDeleteReq req) {

        return Response.SUCCESS(docNodeCommandService.deleteNode(req));
    }


    /***
     * 只允许更新节点内容， 不允许更新节点位置信息
     */

    @FreeLogin
    @RequestMapping(value = "/node/update", method = RequestMethod.POST)
    public Response<DocFlattenNode> updateNodeContent(
            @RequestBody NodeUpdateReq req) {

        return Response.SUCCESS(docNodeCommandService.updateNodeContent(req));
    }


    /**
     * 谨慎开放，会影响节点的所有子节点，
     * TODO 二期开放
     *
     * @return
     */
    @FreeLogin
    @RequestMapping(value = "/node/move", method = RequestMethod.POST)
    public Response<DocFlattenNode> moveNode(
            @RequestBody NodeMoveReq req) {

        return Response.SUCCESS(docNodeCommandService.moveNode(req));
    }


}
