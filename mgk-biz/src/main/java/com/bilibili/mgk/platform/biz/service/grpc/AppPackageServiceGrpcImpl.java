package com.bilibili.mgk.platform.biz.service.grpc;

import com.bapis.ad.mgk.app.AppPackageInfo;
import com.bapis.ad.mgk.app.AppPackageListReq;
import com.bapis.ad.mgk.app.AppPackageListRes;
import com.bilibili.mgk.platform.api.app_package.dto.QueryResAppPackageDto;
import com.bilibili.mgk.platform.api.app_package.dto.ResAppPackageDto;
import com.bilibili.mgk.platform.api.app_package.service.IResAppPackageService;
import com.bilibili.mgk.platform.biz.service.converter.AppPackageGrpcConverter;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18
 */
@Service
@Slf4j
public class AppPackageServiceGrpcImpl extends com.bapis.ad.mgk.app.AppPackageServiceGrpc.AppPackageServiceImplBase {
    @Autowired
    private IResAppPackageService resAppPackageService;

    @Override
    public void appPackageList(AppPackageListReq request, StreamObserver<AppPackageListRes> responseObserver) {
        AppPackageListRes.Builder builder = AppPackageListRes.newBuilder();
        try {
            List<ResAppPackageDto> resAppPackageList = resAppPackageService.queryList(QueryResAppPackageDto.builder()
                    .accountIds(request.getAccountIdList())
                    .ids(request.getAppIdList())
                    .build());
            List<AppPackageInfo> grpcAppPackageInfoList = AppPackageGrpcConverter.MAPPER.toGrpcAppPackageInfoList(resAppPackageList);
            builder.addAllAppList(grpcAppPackageInfoList);
        } catch (Exception e) {
            log.warn("获取应用包信息时发生异常:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        }finally{
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }
}
