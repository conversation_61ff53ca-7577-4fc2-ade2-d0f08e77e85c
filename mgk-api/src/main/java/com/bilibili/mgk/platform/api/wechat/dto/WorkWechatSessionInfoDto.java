package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WorkWechatSessionInfoDto implements Serializable {


    private static final long serialVersionUID = -1271131797608176913L;
    /**
     * 允许进行授权的应用id，如1、2、3， 不填或者填空数组都表示允许授权套件内所有应用（仅旧的多应用套件可传此参数，新开发者可忽略
     */
    private List<Integer> appid;

    /**
     * 授权类型：0 正式授权， 1 测试授权。 默认值为0。注意，请确保应用在正式发布后的授权类型为“正式授权”
     */
    private Integer auth_type;

}
