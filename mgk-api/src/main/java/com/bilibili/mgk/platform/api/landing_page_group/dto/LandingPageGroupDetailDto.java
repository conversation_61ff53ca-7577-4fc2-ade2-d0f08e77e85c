package com.bilibili.mgk.platform.api.landing_page_group.dto;

import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName LandingPageGroupDetailDto
 * <AUTHOR>
 * @Date 2023/5/23 7:00 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupDetailDto {

    private Long groupId;

    private String name;

    private Integer accountId;

    private Integer groupSource;

    private String groupSourceDesc;

    private Integer groupStatus;

    private String groupStatusDesc;

    private Integer auditCreativeId;

    private Long modifyVersion;

    private List<LandingPageGroupMappingDto> mappingList;

    private Timestamp ctime;

    private Timestamp mtime;
}
