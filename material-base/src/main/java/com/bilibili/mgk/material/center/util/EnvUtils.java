package com.bilibili.mgk.material.center.util;


import java.net.Inet4Address;
import java.net.UnknownHostException;
import java.util.Optional;
import javax.annotation.Nullable;
import pleiades.component.env.Environment;
import pleiades.component.env.EnvironmentKeys;
import pleiades.component.env.Environments;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/7/18
 */
public class EnvUtils {


    public static String getDatabusZone(@Nullable String databusZonePresent) {

        return Optional
                .ofNullable(databusZonePresent)
                .filter(var -> !var.isEmpty())
                .orElseGet(() -> Environment
                        .ofNullable(Environment
                                .keyWithFallback("databus_zone", Environments.STRING_MARSHALLER, EnvironmentKeys.ZONE))
                        .orElseThrow(() -> new IllegalArgumentException("No databus zone found")));

    }

    public static String getAppId(@Nullable String appIdPresent) {

        return Optional
                .ofNullable(appIdPresent)
                .filter(var -> !var.isEmpty())
                .orElseGet(() -> Environment
                        .of(EnvironmentKeys.APP_ID)
                        .orElseThrow(() -> new IllegalArgumentException("No databus appId found")));

    }

    public static String getDatabusEnv(@Nullable String envPresent) {

        return Optional.ofNullable(envPresent)
                .filter(var -> !var.isEmpty())
                .orElseGet(() ->
                        Environment.ofNullable(
                                Environment.keyWithFallback("databus_environment", Environments.STRING_MARSHALLER,
                                        EnvironmentKeys.DEPLOY_ENV)).orElse("dev"));

    }

    public static String getDiscoveryEnv(@Nullable String envPresent) {
        return Optional.ofNullable(envPresent)
                .filter(var -> !var.isEmpty())
                .orElseGet(() ->
                        Environment.ofNullable(
                                Environment.keyWithFallback(
                                        "discovery_environment", Environments.STRING_MARSHALLER,
                                        EnvironmentKeys.DEPLOY_ENV)).orElse("dev"));
    }

    public static String getDatabusToken(@Nullable String tokenPresent) {
        return Optional.ofNullable(tokenPresent)
                .filter(var -> !var.isEmpty())
                .orElseGet(() -> Environment
                        .ofNullable(Environment.key("databus_token", Environments.STRING_MARSHALLER))
                        .orElseThrow(() -> new IllegalArgumentException("No databus token found")));
    }


    public static String getDiscoveryZone(@Nullable String discoverZonePresent) {

        return Optional.ofNullable(discoverZonePresent)
                .filter(var -> !var.isEmpty())
                .orElseGet(() -> Environment
                        .of(EnvironmentKeys.DISCOVERY_ZONE)
                        .orElseThrow(() -> new IllegalArgumentException("No discovery zone found")));
    }

    public static String getHostName() {
        return Environment.of(EnvironmentKeys.HOSTNAME)
                .orElseGet(() -> {
                    try {
                        return Inet4Address.getLocalHost().getHostAddress();
                    } catch (UnknownHostException e) {
                        throw new RuntimeException(e);
                    }
                });
    }
}
