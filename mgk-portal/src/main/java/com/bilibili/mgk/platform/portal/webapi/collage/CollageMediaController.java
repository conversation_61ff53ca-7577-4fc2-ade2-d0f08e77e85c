package com.bilibili.mgk.platform.portal.webapi.collage;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.dto.CollageMediaUpdateDto;
import com.bilibili.collage.api.dto.MediaPushDto;
import com.bilibili.collage.api.dto.MediaPushResultV2Dto;
import com.bilibili.collage.api.service.ICollageMediaService;
import com.bilibili.mgk.material.center.service.creative.MaterialIdService;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdRegistry;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdType;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Pagination;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebCollageService;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.CollageMediaDeleteResultVo;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.CollageMediaListVo;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.CollageMediaVo;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.MediaPushResultV2Vo;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.MediaPushV2Vo;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.MediaPushVo;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.QueryCollageMediaVo;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.UpdateMediaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @date 2020/09/09
 * 创意中心
 **/

@Slf4j
@RestController
@RequestMapping("/web_api/v1/collage/media")
@Api(value = "/collage/media", description = "创意中心-媒体管理")
public class CollageMediaController extends BasicController {


    @Autowired
    private WebCollageService webCollageService;

    @Autowired
    private ICollageMediaService collageMediaService;

    @Resource
    private MaterialIdService materialIdService;


    // 素材尺寸 16:10 , 16:9 , 4:3 ,145:12 , 1:1 ,250:126;
    private static final List<Integer> VALID_RATIO = Arrays.asList(16000, 17777, 13333, 120833, 10000, 19841);

    @ApiOperation(value = "查询媒体列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public Response<Pagination<List<CollageMediaVo>>> getList(@ApiIgnore Context context,
            QueryCollageMediaVo queryCollageMediaVo) throws Exception {
        PageResult<CollageMediaDto> pageResult = collageMediaService.getMediaList(
                webCollageService.vo2dto(context.getAccountId(), queryCollageMediaVo));

        this.fillingMaterialId(pageResult.getRecords());

        List<CollageMediaVo> vos = webCollageService.dto2vo(pageResult.getRecords());
        return Response.SUCCESS(new Pagination<>(queryCollageMediaVo.getPage(), pageResult.getTotal(), vos));
    }

    @ApiOperation(value = "本地上传文件信息")
    @RequestMapping(value = "/info", method = RequestMethod.POST)
    public Response<Object> mediaInfoUpload(@ApiIgnore Context context, @RequestBody CollageMediaListVo vos) {
        List<CollageMediaDto> collageMediaDtos = webCollageService.convertMediaVos2Dtos(vos);
        collageMediaService.insert(getOperator(context), collageMediaDtos);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "本地上传媒体")
    @RequestMapping(value = "", method = RequestMethod.POST)
    public Response<CollageMediaVo> mediaUpload(@ApiIgnore Context context, @RequestParam("file") MultipartFile multipartFile) throws IOException {
//        String url = webCollageService.upload(multipartFile);
        File file = webCollageService.multipartToFile(multipartFile);
        String url = webCollageService.uploadWithName(file);
//        CollageMediaVo collageMediaVo = webCollageService.convertFile2Vo(multipartFile, url);
        CollageMediaVo collageMediaVo = webCollageService.convertFile2Vo(file, url, 0);

        //校验图片尺寸 ,CollageMediaVo.media_ratio =Width * 10000 / Height
        Assert.isTrue(VALID_RATIO.contains(collageMediaVo.getMedia_ratio()), "图片尺寸不合法");
        return Response.SUCCESS(collageMediaVo);
    }

    @ApiOperation(value = "本地上传媒体 文件大小限制")
    @RequestMapping(value = "/limit", method = RequestMethod.POST)
    public Response<CollageMediaVo> mediaLimitUpload(@ApiIgnore Context context,
                                                     @RequestParam("file") MultipartFile multipartFile,
                                                     @ApiParam(value = "文件大小限制") @RequestParam(value = "limit", defaultValue = "409600") Integer limit) throws IOException {
        File file = webCollageService.multipartToFile(multipartFile);
        String url = webCollageService.uploadWithName(file);
        CollageMediaVo collageMediaVo = webCollageService.convertFile2Vo(file, url, limit);
        return Response.SUCCESS(collageMediaVo);
    }

    @ApiOperation(value = "编辑媒体名称")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Response<Object> editName(@ApiIgnore Context context, @RequestBody UpdateMediaVo updateMediaVo) {
        collageMediaService.updateMedia(getOperator(context), CollageMediaUpdateDto.builder()
                .id(updateMediaVo.getId())
                .mediaName(updateMediaVo.getMedia_name())
                .build());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "删除媒体")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    public Response<Object> batchDeleted(@ApiIgnore Context context, @RequestParam("ids") List<Integer> ids) {
        List<Integer> failIds = collageMediaService.batchDeleted(getOperator(context), ids);
        CollageMediaDeleteResultVo resultVo = CollageMediaDeleteResultVo.builder()
                .failImageIds(failIds)
                .build();
        return Response.SUCCESS(resultVo);
    }

    @ApiOperation(value = "素材推送")
    @RequestMapping(value = "/batch_push", method = RequestMethod.POST)
    public Response<MediaPushResultV2Vo> batchPush(@ApiIgnore Context context, @RequestBody MediaPushVo mediaPushVo) {
        MediaPushDto mediaPushDto = webCollageService.dto2vo(mediaPushVo);
        Operator operator = getOperator(context);
        mediaPushDto.setCurAccountId(operator.getOperatorId());
        MediaPushResultV2Dto resultDto = collageMediaService.batchPush(operator, mediaPushDto);
        MediaPushResultV2Vo resultVo = webCollageService.pushResultV2Dto2Vo(resultDto);
        return Response.SUCCESS(resultVo);
    }

    @ApiOperation(value = "素材推送V2-字段对齐字节")
    @RequestMapping(value = "/batch_push/v2", method = RequestMethod.POST)
    public Response<MediaPushResultV2Vo> batchPushV2(@ApiIgnore Context context,
            @RequestBody MediaPushV2Vo mediaPushVo) {
        MediaPushDto mediaPushDto = webCollageService.vo2Dto(mediaPushVo);
        Operator operator = getOperator(context);
        mediaPushDto.setCurAccountId(operator.getOperatorId());
        mediaPushDto.setOpenApi(false);
        MediaPushResultV2Dto resultDto = collageMediaService.batchPush(operator, mediaPushDto);
        MediaPushResultV2Vo resultVo = webCollageService.pushResultV2Dto2Vo(resultDto);
        return Response.SUCCESS(resultVo);
    }

    @ApiOperation(value = "删除单一媒体")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    public Response<Object> singleDeleted(@ApiIgnore Context context, @PathVariable("id") Integer id) {
        collageMediaService.singleDeleted(getOperator(context), id);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "列表其他")
    @RequestMapping(value = "/other", method = RequestMethod.GET)
    public Response<Pagination<List<CollageMediaVo>>> getOther(@ApiIgnore Context context,
            @RequestBody QueryCollageMediaVo queryCollageMediaVo) {
        PageResult<CollageMediaDto> mediaOther = collageMediaService.getMediaOther(
                webCollageService.vo2dto(context.getAccountId(), queryCollageMediaVo));
        List<CollageMediaVo> vos = webCollageService.dto2vo(mediaOther.getRecords());
        return Response.SUCCESS(new Pagination<>(queryCollageMediaVo.getPage(), mediaOther.getTotal(), vos));
    }


    private void fillingMaterialId(List<CollageMediaDto> media) {

        Map<String, MaterialIdRegistry> materialIds = materialIdService.findByTypeAndUks(
                MaterialIdType.img.name(),

                media.stream().map(m -> m.getMediaMd5()).collect(Collectors.toList())
        );

        media.forEach(m -> {

            Optional.ofNullable(materialIds.get(m.getMediaMd5()))
                    .map(MaterialIdRegistry::getMaterialId)
                    .ifPresent(materialId -> {
                        m.setMaterialId(materialId);
                    });

        });

    }
}
