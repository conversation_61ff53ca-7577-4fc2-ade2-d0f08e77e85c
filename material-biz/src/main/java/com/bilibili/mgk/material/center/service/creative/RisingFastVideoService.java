package com.bilibili.mgk.material.center.service.creative;

import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideo;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideoCurve;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.PaginationExt;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoCurveQuery;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoIdQuery;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoPageExtra;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoQuery;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/27
 */
public interface RisingFastVideoService {


    /**
     * 根据日榜周榜月榜，根据数据就绪的时间，获得下拉的日期选项
     * @param dayType
     * @return
     */
    RisingFastVideoPageExtra candidateDaySelections(DateAggregation dayType);


    /**
     * 视频维度的日榜、周榜、月榜分页数据
     *
     * @param query
     * @return
     */
    PaginationExt<List<RisingFastVideo>, RisingFastVideoPageExtra> page(RisingFastVideoQuery query);


    /**
     * 视频的每日曲线数据
     *
     * @param query
     * @return
     */
    RisingFastVideoCurve curve(RisingFastVideoCurveQuery query);

    /**
     * 视频的聚合数据详情
     *
     * @param id
     * @return
     */
    RisingFastVideo detail(RisingFastVideoIdQuery id);


}
