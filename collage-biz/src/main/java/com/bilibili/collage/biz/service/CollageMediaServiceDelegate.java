package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.RequestTypeEnum;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.CollageCoverDto;
import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.dto.CollageMediaUpdateDto;
import com.bilibili.collage.api.dto.CollageSizeDto;
import com.bilibili.collage.api.dto.MediaPushDto;
import com.bilibili.collage.api.dto.MediaPushResultV2Dto;
import com.bilibili.collage.api.dto.NewCollageOperationLogDto;
import com.bilibili.collage.api.dto.QueryCollageCoverDto;
import com.bilibili.collage.api.dto.QueryCollageMediaDto;
import com.bilibili.collage.api.dto.QueryCollageSizeDto;
import com.bilibili.collage.api.service.ICollageCoverService;
import com.bilibili.collage.api.service.ICollageLogService;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.collage.biz.dao.MgkCollageMediaDao;
import com.bilibili.collage.biz.po.MgkCollageMediaPo;
import com.bilibili.collage.biz.po.MgkCollageMediaPoExample;
import com.bilibili.mgk.material.center.service.creative.MaterialIdService;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdRegistry;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdType;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceSearchReq;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.util.IteratorHelper;
import com.bilibili.mgk.platform.common.CollageCoverTypeEnum;
import com.bilibili.mgk.platform.common.CollageLogObjFlagEnum;
import com.bilibili.mgk.platform.common.CollageOperateTypeEnum;
import com.bilibili.mgk.platform.common.CollagePatternEditionEnum;
import com.bilibili.mgk.platform.common.DataSourceTypeEnum;
import com.bilibili.mgk.platform.common.collage.CollageSizeStatusEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/09/15
 **/
@Slf4j
@Service
public class CollageMediaServiceDelegate {

    @Autowired
    private MgkCollageMediaDao mgkCollageMediaDao;

    @Autowired
    private ICollageLogService collageLogService;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private ICollageSizeService collageSizeService;

    @Autowired
    private ICollageCoverService collageCoverService;
    @Autowired
    private CollageMediaPushSyncer collageMediaPushSyncer;

    private static final Set<String> VALID_ORDER_BY_FIELDS = new HashSet<>(Arrays.asList("mtime", "ctime"));

    @Value("${mgk.collage.media.query.limit:10000}")
    private Integer queryLimit;

    @Value("${mgk.collage.media.query.limit.day:7}")
    private Integer queryLimitDays;
    @Resource
    private MaterialIdService materialIdService;
    @Value("${material.id.register.source:sycpb.cpm.mgk-portal}")
    private String materialIdSource;

    @Value("${material.id.search.max-size:2000}")
    private Integer materialIdSearchMaxSize;


    private ExecutorService mediaRegisterExecutor = Executors.newFixedThreadPool(10);


    public PageResult<CollageMediaDto> getMediaList(QueryCollageMediaDto dto) {

        List<CollageMediaDto> dtos = new ArrayList<>();
        Long total = 0L;

        if (!StringUtils.isEmpty(dto.getSearchWord())) {

            // TODO 如果searchWord和其他过滤条件同时存在，那么总页数将不准确；
            Pagination<List<MaterialIdReference>> referencePage = materialIdService.searchReference(
                    new MaterialReferenceSearchReq()
                            .setAccountIds(Lists.newArrayList(
                                    dto.getAccountIds().stream().map(String::valueOf).collect(Collectors.toList())))
                            .setPn(1)
                            .setPs(materialIdSearchMaxSize)
                            .setSearchWord(dto.getSearchWord())
            );

            if (referencePage.getTotal_count() > materialIdSearchMaxSize) {
                log.error("Search result over max_size, some result will be miss");
            }

            List<Integer> idIn = referencePage.getData().stream()
                    .map(reference -> MgkCollageMediaPo.fetchPrimaryIdFromReferenceUk(
                            reference.getReferenceUk()))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(idIn)) {
                return PageResult.emptyPageResult();
            }

            dto.setIds(Stream
                    .concat(Optional.ofNullable(dto.getIds())
                            .orElse(new ArrayList<>()).stream(), idIn.stream())
                    .collect(Collectors.toList()));

        }

        MgkCollageMediaPoExample example = getMgkCollageMediaPoExample(dto);
        total = mgkCollageMediaDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        dtos = queryByExample(example);

        // 设置封面
        this.setMediaCover(dtos);

        this.fillingMaterialId(dtos);

        return PageResult.<CollageMediaDto>builder()
                .total(total.intValue())
                .records(dtos)
                .build();
    }

    private void setMediaCover(List<CollageMediaDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<Integer> mediaIds = dtos.stream().map(CollageMediaDto::getId).collect(Collectors.toList());
        // 获取封面
        List<CollageCoverDto> coverDtos = collageCoverService.getCoverDtos(QueryCollageCoverDto.builder()
                .objIds(mediaIds)
                .objTypes(Lists.newArrayList(CollageCoverTypeEnum.MEDIA_COVER.getCode()))
                .build());
        Map<Integer, CollageCoverDto> coverMap =
                coverDtos.stream()
                        .collect(Collectors.toMap(CollageCoverDto::getObjId, Function.identity(), (a, b) -> a));

        dtos.forEach(dto -> {
            CollageCoverDto cover = coverMap.getOrDefault(dto.getId(), CollageCoverDto.builder().build());
            dto.setCover(cover);
        });
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void updateMedia(Operator operator, CollageMediaUpdateDto dto) {
        List<MgkCollageMediaPo> pos = getCollageMediaById(operator.getOperatorId(), Lists.newArrayList(dto.getId()));
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "媒体不存在");
        MgkCollageMediaPo po = MgkCollageMediaPo.builder()
                .id(dto.getId())
                .mediaName(dto.getMediaName())
                .build();
        int res = Try.of(() -> {
            return mgkCollageMediaDao.updateByPrimaryKeySelective(po);
        }).onSuccess(r -> {
            this.onCollageMediaUpdated(pos, dto.getMediaName(), null);
        }).get();
        Assert.isTrue(Utils.isPositive(res), "更新媒体文件失败");

        // log
        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .operatorUsername(operator.getOperatorName())
                .operatorType(operator.getOperatorType().getCode())
                .oldValue("")
                .newValue("")
                .objId(dto.getId().longValue())
                .operateType(CollageOperateTypeEnum.COLLAGE_MEDIA_MODIFY.getCode())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_MEDIA.getCode())
                .accountId(operator.getOperatorId())
                .build());
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public List<Integer> batchDeleted(Operator operator, List<Integer> ids) {
        Assert.isTrue(ids.size() <= 100, "单次批量删除图片最大100条");
        List<MgkCollageMediaPo> pos = getCollageMediaById(operator.getOperatorId(), ids);
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "媒体文件不存在或不属于当前账户");

        Set<Integer> validIdSet = pos.stream()
                .map(MgkCollageMediaPo::getId)
                .collect(Collectors.toSet());

        List<Integer> failImageIds = ids.stream()
                .filter(id -> !validIdSet.contains(id))
                .collect(Collectors.toList());

        // 软删
        MgkCollageMediaPoExample example = new MgkCollageMediaPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdEqualTo(operator.getOperatorId())
                .andIdIn(ids);
        int res = Try.of(() -> {

            List<MgkCollageMediaPo> deleteTarget = mgkCollageMediaDao.selectByExample(example);

            int deleteCnt = mgkCollageMediaDao.updateByExampleSelective(
                    MgkCollageMediaPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build(), example);

            return Tuple.of(deleteTarget, deleteCnt);
        }).onSuccess(r -> {
            this.onCollageMediaDeleted(r._1);
        }).map(r -> {
            return r._2;
        }).get();

        Assert.isTrue(Utils.isPositive(res), "删除媒体文件失败");

        // log
        List<NewCollageOperationLogDto> collageOperationLogDtos = ids.stream().map(id -> {
            return NewCollageOperationLogDto.builder()
                    .operatorUsername(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .oldValue("")
                    .newValue("")
                    .objId(id.longValue())
                    .operateType(CollageOperateTypeEnum.COLLAGE_MEDIA_DELETED.getCode())
                    .objFlag(CollageLogObjFlagEnum.COLLAGE_MEDIA.getCode())
                    .accountId(operator.getOperatorId())
                    .build();
        }).collect(Collectors.toList());
        collageLogService.batchInsert(collageOperationLogDtos);

        return failImageIds;
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void singleDeleted(Operator operator, Integer id) {
        List<MgkCollageMediaPo> pos = getCollageMediaById(operator.getOperatorId(), Lists.newArrayList(id));
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "媒体文件不存在");

        int res = Try
                .of(() -> mgkCollageMediaDao.updateByPrimaryKeySelective(
                        MgkCollageMediaPo.builder().id(id).isDeleted(IsDeleted.DELETED.getCode()).build()))
                .onSuccess(r -> {
                    this.onCollageMediaDeleted(pos);
                })
                .get();
        Assert.isTrue(Utils.isPositive(res), "删除媒体文件失败");

        // 日志
        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .operatorUsername(operator.getOperatorName())
                .operatorType(operator.getOperatorType().getCode())
                .oldValue("")
                .newValue("")
                .objId(id.longValue())
                .operateType(CollageOperateTypeEnum.COLLAGE_MEDIA_DELETED.getCode())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_MEDIA.getCode())
                .accountId(operator.getOperatorId())
                .build());
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void insert(Operator operator, List<CollageMediaDto> collageMediaDtos) {

        if (CollectionUtils.isEmpty(collageMediaDtos)) {
            return;
        }

        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        List<Long> mediaIds = new ArrayList<>();
        collageMediaDtos.forEach(dto -> {
            dto.setAccountId(operator.getOperatorId());
            dto.setMtime(nowTime);
            dto.setCtime(nowTime);
            // 生成 mediaId 唯一的
            long mediaId = snowflakeIdWorker.nextId();
            mediaIds.add(mediaId);
            dto.setMediaId(mediaId);
            dto.setPatternId(0);
            dto.setWorksId(0);
            dto.setIsDeleted(IsDeleted.VALID.getCode());
            dto.setTotalDuration(0);
            dto.setDurationPerFrame(0);
            dto.setFrames(0);
            dto.setRoundsNumber(0);
            dto.setDataSourceType(DataSourceTypeEnum.SELF.getCode());
            dto.setFromAccountId(0);
        });

        List<MgkCollageMediaPo> pos = convertMediaDtos2Pos(collageMediaDtos);
        int result = this.insertBatchOrUpdateDuplicated(pos);
        Assert.isTrue(Utils.isPositive(result), "插入贴图媒体文件失败");

        // 日志
        List<MgkCollageMediaPo> mediaPoByMediaId =
                getMediaPoByMediaId(pos.stream().map(po -> po.getMediaId()).distinct().collect(Collectors.toList()));
        List<Integer> ids = mediaPoByMediaId.stream().map(MgkCollageMediaPo::getId).collect(Collectors.toList());
        List<NewCollageOperationLogDto> operationLogDtos = ids.stream().map(id -> {
            return NewCollageOperationLogDto.builder()
                    .operatorUsername(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .oldValue("")
                    .newValue("")
                    .objId(id.longValue())
                    .operateType(CollageOperateTypeEnum.COLLAGE_MEDIA_ADD.getCode())
                    .objFlag(CollageLogObjFlagEnum.COLLAGE_MEDIA.getCode())
                    .accountId(operator.getOperatorId())
                    .build();
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(operationLogDtos)) {
            collageLogService.batchInsert(operationLogDtos);
        }

    }

    private List<MgkCollageMediaPo> getMediaPoByMediaId(List<Long> mediaIds) {
        MgkCollageMediaPoExample example = new MgkCollageMediaPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andMediaIdIn(mediaIds);
        return mgkCollageMediaDao.selectByExample(example);
    }

    public Integer insert(Operator operator, CollageMediaDto collageMediaDto) {
        final MgkCollageMediaPo po = insertAndGetPo(operator, collageMediaDto);
        return po.getId();
    }

    public Long insertAndGetMediaId(Operator operator, CollageMediaDto collageMediaDto) {
        final MgkCollageMediaPo po = insertAndGetPo(operator, collageMediaDto);
        return po.getMediaId();
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public MgkCollageMediaPo insertAndGetPo(Operator operator, CollageMediaDto collageMediaDto) {
        MgkCollageMediaPo po = convertMediaDto2Po(collageMediaDto);
        // soa会有mediaId还没有生成的情况
        if (Objects.isNull(po.getMediaId()) || po.getMediaId() == 0) {
            po.setMediaId(snowflakeIdWorker.nextId());
        }
        int result = this.insertBatchOrUpdateDuplicated(Lists.newArrayList(po));

        Assert.isTrue(Utils.isPositive(result), "插入贴图媒体文件失败");

        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .operatorUsername(operator.getOperatorName())
                .operatorType(operator.getOperatorType().getCode())
                .oldValue("")
                .newValue("")
                .objId(po.getId().longValue())
                .operateType(CollageOperateTypeEnum.COLLAGE_MEDIA_ADD.getCode())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_MEDIA.getCode())
                .accountId(operator.getOperatorId())
                .build());
        return po;
    }


    public int insertBatchOrUpdateDuplicated(List<MgkCollageMediaPo> records) {

        Map<Integer, List<MgkCollageMediaPo>> account2Medias = records.stream()
                .collect(Collectors.groupingBy(r -> r.getAccountId()));

        // tuple<existed，update>
        List<Tuple2<MgkCollageMediaPo, MgkCollageMediaPo>> existed = new ArrayList<>();

        List<MgkCollageMediaPo> absents = new ArrayList<>();

        account2Medias.entrySet().stream()
                .collect(Collectors.toMap(entry -> entry.getKey(),
                        entry -> {

                            MgkCollageMediaPoExample accountIdEqAndMd5In = new MgkCollageMediaPoExample();
                            accountIdEqAndMd5In.createCriteria()
                                    .andAccountIdEqualTo(entry.getKey())
                                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                                    .andMediaMd5In(entry.getValue().stream().map(MgkCollageMediaPo::getMediaMd5)
                                            .distinct()
                                            .collect(Collectors.toList()));

                            List<MgkCollageMediaPo> values = mgkCollageMediaDao.selectByExample(accountIdEqAndMd5In);

                            // 需要注意的是，存量的可能有重复的， 那么重复的情况下更新名称只更新最新的，existed会去重
                            Map<String, MgkCollageMediaPo> existedMapping = values
                                    .stream()
                                    .sorted(new Comparator<MgkCollageMediaPo>() {
                                        // 从小到大排序
                                        @Override
                                        public int compare(MgkCollageMediaPo o1, MgkCollageMediaPo o2) {
                                            return o1.getMtime().compareTo(o2.getMtime());
                                        }

                                    })
                                    .collect(Collectors.toMap(v -> v.getMediaMd5(), v -> v, (v1, v2) -> v2));

                            // 更新提交表单的id，可能存在重复的注意
                            Map<String, MgkCollageMediaPo> updateMapping = entry.getValue().stream()
                                    .map(updateSelective -> {
                                        Optional.ofNullable(existedMapping.get(updateSelective.getMediaMd5()))
                                                .ifPresent(existItem -> {
                                                    String mediaName = updateSelective.getMediaName();
                                                    // 复制所有已存在的参数
                                                    BeanUtils.copyProperties(existItem, updateSelective);
                                                    updateSelective.setMediaName(mediaName);
                                                });
                                        return updateSelective;
                                    })
                                    .collect(Collectors.toMap(v -> v.getMediaMd5(), v -> v, (v1, v2) -> v1));

                            absents.addAll(entry.getValue().stream()
                                    .filter(r -> !existedMapping.containsKey(r.getMediaMd5()))
                                    .collect(Collectors.toList()));

                            existed.addAll(new ArrayList<>(existedMapping.entrySet()
                                    .stream().map(e -> Tuple.of(e.getValue(), updateMapping.get(e.getKey()))).collect(
                                            Collectors.toList())));

                            return values;
                        }));

        // 已经存在的只更新标题和更新时间，逐个执行；
        existed.stream().forEach(v -> {

            Try.run(() -> {

                mgkCollageMediaDao.updateByPrimaryKeySelective(
                        MgkCollageMediaPo.builder()
                                .id(v._1.getId())
                                .mediaName(v._2.getMediaName())
                                .mtime(new Timestamp(System.currentTimeMillis()))
                                .build()

                );
                this.onCollageMediaUpdated(Lists.newArrayList(v._1), v._2.getMediaName(), null);

            });

        });

        // 批量插入不存在的
        if (!CollectionUtils.isEmpty(absents)) {

            if (absents.size() == 1) {
                mgkCollageMediaDao.insertSelective(absents.get(0));
            } else {
                mgkCollageMediaDao.insertBatch(absents);


            }
            this.onCollageMediaCreated(absents);
        }

        return existed.size() + absents.size();
    }


    private List<MgkCollageMediaPo> convertMediaDtos2Pos(List<CollageMediaDto> collageMediaDtos) {
        return collageMediaDtos.stream().map(this::convertMediaDto2Po).collect(Collectors.toList());
    }

    private MgkCollageMediaPo convertMediaDto2Po(CollageMediaDto dto) {
        MgkCollageMediaPo po = MgkCollageMediaPo.builder().build();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    public PageResult<CollageMediaDto> getMediaOther(QueryCollageMediaDto dto) {
        // 获取所有的collage_size
        List<CollageSizeDto> collageSizeDtos = collageSizeService.queryCollageSize(QueryCollageSizeDto.builder()
                .edition(CollagePatternEditionEnum.NEW_PATTERN_EDITION.getCode())
                .status(CollageSizeStatusEnum.USING.getCode())
                .build());
        List<Integer> radios = collageSizeDtos.stream().map(sizeDto -> {
            return Math.round(sizeDto.getWidth() * 10000 / sizeDto.getHeight());
        }).collect(Collectors.toList());

        // 获取用户使用的所有的radio
        List<Integer> radiosUsed = queryByExample(getMgkCollageMediaPoExample(QueryCollageMediaDto.builder()
                .accountIds(Lists.newArrayList(dto.getAccountIds()))
                .build())).stream().map(CollageMediaDto::getMediaRatio).collect(Collectors.toList());

        // 其他的radio
        List<Integer> otherRadios = radiosUsed.stream().filter(r -> !radios.contains(r)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(otherRadios)) {
            return PageResult.emptyPageResult();
        }
        return getMediaList(QueryCollageMediaDto.builder()
                .mediaRadios(otherRadios)
                .pageInfo(dto.getPageInfo())
                .build());
    }

    private List<MgkCollageMediaPo> getCollageMediaById(Integer accountId, List<Integer> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        MgkCollageMediaPoExample example = new MgkCollageMediaPoExample();
        MgkCollageMediaPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdEqualTo(accountId);
        if (ids.size() == 1) {
            criteria.andIdEqualTo(ids.get(0));
        } else {
            criteria.andIdIn(ids);
        }
        return mgkCollageMediaDao.selectByExample(example);
    }

    private List<CollageMediaDto> queryByExample(MgkCollageMediaPoExample example) {

        List<MgkCollageMediaPo> pos = mgkCollageMediaDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos2vos(pos);
    }

    private List<CollageMediaDto> pos2vos(List<MgkCollageMediaPo> pos) {
        return pos.stream().map(this::po2vo).collect(Collectors.toList());
    }

    private CollageMediaDto po2vo(MgkCollageMediaPo po) {
        CollageMediaDto dto = CollageMediaDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }


    private MgkCollageMediaPoExample getMgkCollageMediaPoExample(QueryCollageMediaDto dto) {
        MgkCollageMediaPoExample example = new MgkCollageMediaPoExample();

        if (dto == null) {
            return example;
        }

        MgkCollageMediaPoExample.Criteria criteria = example.or();
        ObjectUtils.setList(dto::getIds, criteria::andIdIn);
        ObjectUtils.setList(dto::getMediaIds, criteria::andMediaIdIn);
        ObjectUtils.setList(dto::getAccountIds, criteria::andAccountIdIn);
        // 素材类型
        ObjectUtils.setList(dto::getMediaTypes, criteria::andMediaTypeIn);
        // 比例
        ObjectUtils.setList(dto::getMediaRadios, criteria::andMediaRatioIn);
        // 媒体来源
        ObjectUtils.setList(dto::getMediaOrigins, criteria::andMediaOriginIn);
        ObjectUtils.setObject(dto::getWidth, criteria::andWidthEqualTo);
        ObjectUtils.setObject(dto::getHeight, criteria::andHeightEqualTo);
        ObjectUtils.setObject(dto::getPatternIds, criteria::andPatternIdIn);
        ObjectUtils.setObject(dto::getWorksIds, criteria::andWorksIdIn);
        ObjectUtils.setObject(dto::getDataSourceTypes, criteria::andDataSourceTypeIn);
        ObjectUtils.setObject(dto::getStartMtime, criteria::andMtimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(dto::getEndMtime, criteria::andMtimeLessThanOrEqualTo);

        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        if (Utils.isPositive(dto.getMediaSize())) {
            criteria.andMediaSizeLessThanOrEqualTo(dto.getMediaSize());
        }

        if (!Strings.isNullOrEmpty(dto.getMediaName())) {
            criteria.andMediaNameLike("%" + dto.getMediaName() + "%");
        }
        if (!StringUtils.isEmpty(dto.getMediaMd5())) {
            criteria.andMediaMd5EqualTo(dto.getMediaMd5());
        }

        Page page = dto.getPageInfo();

        if (page != null) {
            example.setLimit(page.getLimit());
            example.setOffset(page.getOffset());
            if(RequestTypeEnum.OPEN_API.getId().equals(dto.getRequestType()) && page.getOffset() > queryLimit){
                Assert.notNull(dto.getStartMtime(), "移量一万以内正常查询 之后更新时间范围必传 范围最大支持七天");
                Assert.notNull(dto.getEndMtime(), "移量一万以内正常查询 之后更新时间范围必传 范围最大支持七天");

                Assert.isTrue(!dto.getEndMtime().before(dto.getStartMtime()), "更新时间范围,开始时间必须要小于结束时间");
                Assert.isTrue(Utils.getDateSpace(dto.getStartMtime(), dto.getEndMtime()) <= queryLimitDays ,
                        "移量一万以内正常查询 之后更新时间范围必传 范围最大支持七天");
            }
        }
        String orderBy;

        if(!StringUtils.isEmpty(dto.getOrderBy()) && VALID_ORDER_BY_FIELDS.contains(dto.getOrderBy().split(" ")[0])) {
//            orderBy = dto.getOrderBy().split(" ")[0];
            if(dto.getOrderBy().contains("asc")){
                orderBy = "id + 0 asc";
            }else if(dto.getOrderBy().contains("desc")){
                orderBy = "id + 0 desc";
            } else {
                orderBy = "id + 0 desc";
            }
            example.setOrderByClause(orderBy);
        }else{
            example.setOrderByClause("id + 0 desc");
        }

        return example;
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void updateMediaUrl(Operator operator, Integer mediaId, String rendered) {
        MgkCollageMediaPo po = MgkCollageMediaPo.builder()
                .id(mediaId)
                .accountId(operator.getOperatorId())
                .mediaUrl(rendered)
                .build();
        int res = Try.of(() -> {
            return mgkCollageMediaDao.updateByPrimaryKeySelective(po);
        }).onSuccess(r -> {
            MgkCollageMediaPoExample example = new MgkCollageMediaPoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andAccountIdEqualTo(operator.getOperatorId())
                    .andIdEqualTo(mediaId);

            List<MgkCollageMediaPo> target = mgkCollageMediaDao.selectByExample(example);

            this.onCollageMediaUpdated(target, null, rendered);

        }).get();
        Assert.isTrue(Utils.isPositive(res), "更新媒体链接失败");
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public MediaPushResultV2Dto batchPush(Operator operator, MediaPushDto mediaPushDto) {
        return collageMediaPushSyncer.batchPush(operator, mediaPushDto);
    }


    public void scanAndRegisterAllExistedCollageMediaToMaterialCenter(
            @Nullable LocalDateTime startTime, @Nullable Integer accountId
    ) {

        mediaRegisterExecutor.submit(() -> {

            long startTs = System.currentTimeMillis();

            AtomicInteger totalCount = new AtomicInteger(0);

            AtomicInteger roundCnt = new AtomicInteger();

            Iterator<List<MgkCollageMediaPo>> iterator = scanCollageMedia(startTime, accountId);

            log.info(">>>>> Start to do scanAndRegisterAllExistedCollageMediaToMaterialCenter ");

            while (iterator.hasNext()) {
                roundCnt.incrementAndGet();
                Try.run(() -> {

                    List<MgkCollageMediaPo> batch = iterator.next();

                    // 针对存量的不做重复性的检查
                    onCollageMediaCreated(batch);

                    totalCount.addAndGet(batch.size());

                }).onFailure(t -> {
                    log.warn("Fail to run round={}, ", roundCnt.get(), t);
                }).onSuccess(r -> {
                    log.info("Complete round={}, process cnt={}", roundCnt.get(), totalCount.get());
                });
            }

            log.info(">>>>> Complete scanAndRegisterAllExistedCollageMediaToMaterialCenter cost={}, scan records={}",

                    System.currentTimeMillis() - startTs, totalCount.get());
        });


    }


    public Iterator<List<MgkCollageMediaPo>> scanCollageMedia(LocalDateTime startTime, Integer accountId) {
        return IteratorHelper.buildIterator(
                (id, limit) -> mgkCollageMediaDao.selectByIdAndLimit(id, limit,
                        Optional.ofNullable(startTime)
                                .map(time -> new Timestamp(time.toInstant(ZoneOffset.of("+8")).toEpochMilli()))
                                .orElse(null),
                        accountId),
                MgkCollageMediaPo::getId,
                1000);

    }


    // TODO 对于增量的素材如果发生重复，则不做新增，而是更新名称和标题；
    public void onCollageMediaCreated(List<MgkCollageMediaPo> collageMediaPo) {

        log.info("Start to register material-ids, on collage media created, batch size={}", collageMediaPo.size());

        collageMediaPo.forEach(po -> {

            Try.run(() -> {
                // TODO 批量的同步可能对性能有影响注意, 注意异步
                materialIdService.register(po.toMaterialIdRegisterReq(materialIdSource));
            }).onFailure(t -> {
                log.error("Fail to register material={} ", po, t);
            });

        });

    }


    public void onCollageMediaDeleted(List<MgkCollageMediaPo> deleteTargets) {
        log.info("Start to delete material-reference, on collage media deleted, batch-size={}", deleteTargets.size());

        deleteTargets.forEach(target -> {

            Try.run(() -> {
                materialIdService.deleteReference(target.toMaterialReferenceDeleteReq());
            }).onFailure(t -> {
                log.error("Fail to delete material reference, media={}", target, t);
            });
        });

    }


    public void onCollageMediaUpdated(List<MgkCollageMediaPo> updateTargets, String mediaName, String mediaUrl) {
        log.info("Start to delete material-reference, on collage media deleted, batch-size={}", updateTargets.size());

        updateTargets.forEach(target -> {
            Try.run(() -> {
                materialIdService.updateReference(target.toMaterialReferenceUpdateReq(mediaName, mediaUrl));
            }).onFailure(t -> {
                log.error("Fail to delete material reference, media={}", target, t);
            });
        });

    }


    private void fillingMaterialId(List<CollageMediaDto> media) {

        Try.run(() -> {
            Map<String, MaterialIdRegistry> materialIds = materialIdService.findByTypeAndUks(
                    MaterialIdType.img.name(),

                    media.stream().map(m -> m.getMediaMd5()).collect(Collectors.toList())
            );

            media.forEach(m -> {

                Optional.ofNullable(materialIds.get(m.getMediaMd5()))
                        .map(MaterialIdRegistry::getMaterialId)
                        .ifPresent(materialId -> {
                            m.setMaterialId(materialId);
                        });

            });


        });

    }

}
