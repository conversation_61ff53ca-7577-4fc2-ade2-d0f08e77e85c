package com.bilibili.mgk.platform.biz.service.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CoverOcrModel {

   private OcrValue value;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static
    class OcrValue{

       private List<OcrData> ocr_data;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OcrData{

        //识别出的文本内容
        private String text;

    }
}
