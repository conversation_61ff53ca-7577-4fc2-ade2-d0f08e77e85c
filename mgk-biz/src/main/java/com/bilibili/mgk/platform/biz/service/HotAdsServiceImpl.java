package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.QueryHotAdsDto;
import com.bilibili.mgk.platform.api.hot_ads.service.IHotAdsService;
import com.bilibili.mgk.platform.api.hot_video.dto.QueryHotVideoCollectDto;
import com.mysema.commons.lang.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @file: HotAdsServiceImpl
 * @author: gaoming
 * @date: 2021/01/07
 * @version: 1.0
 * @description:
 **/
@Service
public class HotAdsServiceImpl implements IHotAdsService {

    @Autowired
    private HotAdsServiceDelegate hotAdsServiceDelegate;

    @Autowired
    private HotVideoCollectServiceDelegate hotVideoCollectServiceDelegate;

    @Override
    public PageResult<HotAdsDto> getHotAdsDtos(Operator operator, QueryHotAdsDto queryHotAdsDto) {
        return hotAdsServiceDelegate.getHotAdsDtos(operator, queryHotAdsDto);
    }

    @Override
    public List<HotAdsDto> getHotAdsDetail(String adTypeCreativeId, Integer dateType) {
        return hotAdsServiceDelegate.getHotAdsDetail(adTypeCreativeId, dateType);
    }

    @Override
    public void doCollect(Operator operator, String adTypeCreativeId, String creativeTitle,
                          Integer collectType, String logDate, Integer dayType) {
        hotAdsServiceDelegate.doCollect(operator, adTypeCreativeId, creativeTitle, collectType, logDate, dayType);
    }

    @Override
    public void cancelCollect(Operator operator, String adTypeCreativeId, Integer collectType) {
        hotAdsServiceDelegate.cancelCollect(operator, adTypeCreativeId, collectType);
    }

    @Override
    public List<HotAdsDto> getHotAdsDtosByAdTypeCreativeIds(List<String> adTypeCreativeIds) {
        return hotAdsServiceDelegate.getHotAdsDtosByAdTypeCreativeIds(adTypeCreativeIds);
    }

    @Override
    public PageResult<Pair<String, String>> getCollectAdsByPage(QueryHotVideoCollectDto queryDto) {
        return hotVideoCollectServiceDelegate.getCollectAdsByPage(queryDto);
    }
}
