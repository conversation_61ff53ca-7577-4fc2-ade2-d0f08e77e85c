package com.bilibili.mgk.platform.api.address.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName CityInfo
 * <AUTHOR>
 * @Date 2022/9/7 2:36 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CityInfo implements Serializable {
    private static final long serialVersionUID = 38901222826L;

    /**
     * 省份
     */
    String province;

    /**
     * 城市
     */
    String city;

    /**
     * 经度
     */
    private double longitude;

    /**
     * 纬度
     */
    private double latitude;

    /**
     * 边界点集合
     */
    private List<AOI> shape;
}
