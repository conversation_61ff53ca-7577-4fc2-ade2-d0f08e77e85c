package com.bilibili.mgk.material.center.service.aigc.impl;

import com.bilibili.mgk.material.center.http.dto.Img2ImgResultDTO;
import com.bilibili.mgk.material.center.http.dto.Img2ImgSubmitRespDTO;
import com.bilibili.mgk.material.center.http.dto.Img2TxtRespDTO;
import com.bilibili.mgk.material.center.service.aigc.AIGenImg2ImgRemoteHttpService;
import com.google.common.base.Splitter;
import java.util.Random;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/22
 */
@Deprecated
public class AIGenImg2ImgRemoteHttpServiceMockImpl extends AIGenImg2ImgRemoteHttpService {


    @Value("${material.aigc.img2img.mock-img2txt:sunset over mountains}")
    private String mockTxt;

    @Value("${material.aigc.img2img.mock-img2img-urls:https://uat-i0.hdslb.com/bfs/sycp/mgk/material/jpg/202405/04a01a2b35a61f43fb3be5c826dbd163.jpg,https://i0.hdslb.com/bfs/sycp/creative_img/202407/d27da1a6afd22f56be272b7eef48bab2.jpg}")
    private String mockUrls;


    @Override
    public Img2TxtRespDTO img2txt(String imgUrl) {
        return new Img2TxtRespDTO()
                .setCaption(mockTxt);
    }

    @Override
    public Img2ImgSubmitRespDTO img2imgSubmit(String imgUrl, String prompt, Double relevance, Integer quantity,
            Boolean keepText, String shape) {
        return new Img2ImgSubmitRespDTO()
                .setTaskId("123456");
    }

    @Override
    public Img2ImgResultDTO img2imgProgress(String taskId, String host) {

        Random random = new Random();

        int dice = random.nextInt(10);

        if (dice == 1) {

            return new Img2ImgResultDTO()
                    .setStatus(Img2ImgResultDTO.STATUS_COMPLETED)
                    .setResults(
                            Splitter.on(",")
                                    .splitToList(mockUrls)
                                    .stream().collect(Collectors.toList())
                                    .stream().map(url -> new Img2ImgResultDTO.UrlMd5().setUrl(url).setMd5(url))
                                    .collect(Collectors.toList())
                    );
        }

        return new Img2ImgResultDTO()
                .setStatus(Img2ImgResultDTO.STATUS_PROCESSING);
    }
}
