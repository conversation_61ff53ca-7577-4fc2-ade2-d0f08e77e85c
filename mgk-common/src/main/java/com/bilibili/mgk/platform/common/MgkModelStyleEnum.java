package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/09
 **/
@Getter
@AllArgsConstructor
public enum MgkModelStyleEnum {
    /**
     * H5
     */
    H5(0, "H5"),
    // 1-9 表示老的巨幕模板类型
    /**
     * 全屏图片
     */
    FULL_SCREEN_IMAGE(1, "全屏图片"),

    /**
     * 非全屏图片
     */
    HALF_SCREEN_IMAGE(2, "非全屏图片"),

    /**
     * 全屏视频
     */
    FULL_SCREEN_VIDEO(3, "全屏视频"),

    /**
     * 非全屏视频
     */
    HALF_SCREEN_VIDEO(4, "非全屏视频"),

    /**
     * 非全屏视频-ios
     */
    HALF_SCREEN_VIDEO_IOS(5, "非全屏视频-ios"),

    /**
     * 非全屏视频-android
     */
    HALF_SCREEN_VIDEO_ANDROID(6, "非全屏视频-android"),

    /**
     * 竖屏全屏视频-H5
     */
    VERTICAL_FULL_SCREEN_VIDEO_H5(7, "竖屏全屏视频-H5"),

    /**
     * 开屏视频
     */
    FULL_SCREEN_OPEN_SCREEN(8, "开屏视频"),


    /**
     * 全屏视频-开屏双跳
     */
    FULL_SCREEN_OPEN_DOUBLE_JUMP_VIDEO(9, "全屏视频-开屏双跳"),

    //10-999 表示新的H5模板类型
    /**
     * H5
     */
    NATIVE(10, "H5"),

    // 1001-1999 表示新的原生类型
    /**
     * 原生
     */
    CUSTOM_NATIVE(1001, "原生"),

    /**
     * 吸顶视频
     */
    CEILING_VIDEO(1002, "吸顶视频"),

    /**
     * 彩蛋
     */
    COLORED_EGGS(1003, "彩蛋"),

    // 2000- 表示小程序类型
    /**
     * 小程序
     */
    APPLETS(2000, "小程序");
    private Integer code;
    private String desc;

    public static List<Integer> MODEL_STYLE_CODE = Arrays.stream(values()).map(MgkModelStyleEnum::getCode).collect(Collectors.toList());

    public static MgkModelStyleEnum getByCode(int code) {
        for (MgkModelStyleEnum bean : values()) {
            if (bean.getCode() == code) return bean;
        }
        throw new IllegalArgumentException("unknown code MgkModelStyleEnum " + code);
    }
}
