package com.bilibili.mgk.platform.portal.webapi.landing_page.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/1/22
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SsrInfoVo {
    //文件拼接规则 bucket + / + file_path + / + page_id + / + original_file_name

    @ApiModelProperty(value = "文件路径")
    private String file_path;

    @ApiModelProperty(value = "页面id")
    private String page_id;

    @ApiModelProperty(value = "文件名称")
    private String original_file_name;

    @ApiModelProperty(value = "文件内容")
    private String content;


}
