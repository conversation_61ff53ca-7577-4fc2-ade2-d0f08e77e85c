package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkPageDownloadComponentHeightPo implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 总模块大小
     */
    private Integer totalBlockSize;

    /**
     * 下载按钮组件总大小
     */
    private Integer totalDownloadComponentSize;

    /**
     * 最大的下载按钮组件的大小
     */
    private Integer maxDownloadComponentSize;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否被删除 0-正常 1-被删除
     */
    private Integer isDeleted;

    /**
     * 首屏下载按钮总大小
     */
    private Integer totalFirstScreenDownloadComponentHeight;

    private static final long serialVersionUID = 1L;
}