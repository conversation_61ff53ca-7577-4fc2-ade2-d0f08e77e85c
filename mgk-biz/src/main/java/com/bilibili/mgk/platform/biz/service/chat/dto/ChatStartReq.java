package com.bilibili.mgk.platform.biz.service.chat.dto;


import com.bilibili.mgk.platform.api.landing_page.dto.MessageDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/21 16:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatStartReq {

    // 业务ID
    private String app_id;

    private String advertiser_id;

    // 会话ID
    private String chat_id;

    private String chat_start_type;

    private String chat_his;

    private Map<String , String> customer_info;
}
