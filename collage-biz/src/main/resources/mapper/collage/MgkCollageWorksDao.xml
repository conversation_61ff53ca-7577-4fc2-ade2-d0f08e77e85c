<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.collage.biz.dao.MgkCollageWorksDao">
  <resultMap id="BaseResultMap" type="com.bilibili.collage.biz.po.MgkCollageWorksPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="works_id" jdbcType="BIGINT" property="worksId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="collage_size_id" jdbcType="INTEGER" property="collageSizeId" />
    <result column="render_image" jdbcType="VARCHAR" property="renderImage" />
    <result column="pattern_id" jdbcType="INTEGER" property="patternId" />
    <result column="works_origin" jdbcType="INTEGER" property="worksOrigin" />
    <result column="is_synchro" jdbcType="TINYINT" property="isSynchro" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="works_radio" jdbcType="INTEGER" property="worksRadio" />
    <result column="works_md5" jdbcType="VARCHAR" property="worksMd5" />
    <result column="works_size" jdbcType="BIGINT" property="worksSize" />
    <result column="total_duration" jdbcType="INTEGER" property="totalDuration" />
    <result column="duration_per_frame" jdbcType="INTEGER" property="durationPerFrame" />
    <result column="frames" jdbcType="INTEGER" property="frames" />
    <result column="rounds_number" jdbcType="INTEGER" property="roundsNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, works_id, name, account_id, collage_size_id, render_image, pattern_id, works_origin, 
    is_synchro, is_deleted, ctime, mtime, works_radio, works_md5, works_size, total_duration, 
    duration_per_frame, frames, rounds_number
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_collage_works
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_collage_works
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_collage_works
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPoExample">
    delete from mgk_collage_works
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_works (works_id, name, account_id, 
      collage_size_id, render_image, pattern_id, 
      works_origin, is_synchro, is_deleted, 
      ctime, mtime, works_radio, 
      works_md5, works_size, total_duration, 
      duration_per_frame, frames, rounds_number
      )
    values (#{worksId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{accountId,jdbcType=INTEGER}, 
      #{collageSizeId,jdbcType=INTEGER}, #{renderImage,jdbcType=VARCHAR}, #{patternId,jdbcType=INTEGER}, 
      #{worksOrigin,jdbcType=INTEGER}, #{isSynchro,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{worksRadio,jdbcType=INTEGER}, 
      #{worksMd5,jdbcType=VARCHAR}, #{worksSize,jdbcType=BIGINT}, #{totalDuration,jdbcType=INTEGER}, 
      #{durationPerFrame,jdbcType=INTEGER}, #{frames,jdbcType=INTEGER}, #{roundsNumber,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_works
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="worksId != null">
        works_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="collageSizeId != null">
        collage_size_id,
      </if>
      <if test="renderImage != null">
        render_image,
      </if>
      <if test="patternId != null">
        pattern_id,
      </if>
      <if test="worksOrigin != null">
        works_origin,
      </if>
      <if test="isSynchro != null">
        is_synchro,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="worksRadio != null">
        works_radio,
      </if>
      <if test="worksMd5 != null">
        works_md5,
      </if>
      <if test="worksSize != null">
        works_size,
      </if>
      <if test="totalDuration != null">
        total_duration,
      </if>
      <if test="durationPerFrame != null">
        duration_per_frame,
      </if>
      <if test="frames != null">
        frames,
      </if>
      <if test="roundsNumber != null">
        rounds_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="worksId != null">
        #{worksId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="collageSizeId != null">
        #{collageSizeId,jdbcType=INTEGER},
      </if>
      <if test="renderImage != null">
        #{renderImage,jdbcType=VARCHAR},
      </if>
      <if test="patternId != null">
        #{patternId,jdbcType=INTEGER},
      </if>
      <if test="worksOrigin != null">
        #{worksOrigin,jdbcType=INTEGER},
      </if>
      <if test="isSynchro != null">
        #{isSynchro,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="worksRadio != null">
        #{worksRadio,jdbcType=INTEGER},
      </if>
      <if test="worksMd5 != null">
        #{worksMd5,jdbcType=VARCHAR},
      </if>
      <if test="worksSize != null">
        #{worksSize,jdbcType=BIGINT},
      </if>
      <if test="totalDuration != null">
        #{totalDuration,jdbcType=INTEGER},
      </if>
      <if test="durationPerFrame != null">
        #{durationPerFrame,jdbcType=INTEGER},
      </if>
      <if test="frames != null">
        #{frames,jdbcType=INTEGER},
      </if>
      <if test="roundsNumber != null">
        #{roundsNumber,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPoExample" resultType="java.lang.Long">
    select count(*) from mgk_collage_works
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_collage_works
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.worksId != null">
        works_id = #{record.worksId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.collageSizeId != null">
        collage_size_id = #{record.collageSizeId,jdbcType=INTEGER},
      </if>
      <if test="record.renderImage != null">
        render_image = #{record.renderImage,jdbcType=VARCHAR},
      </if>
      <if test="record.patternId != null">
        pattern_id = #{record.patternId,jdbcType=INTEGER},
      </if>
      <if test="record.worksOrigin != null">
        works_origin = #{record.worksOrigin,jdbcType=INTEGER},
      </if>
      <if test="record.isSynchro != null">
        is_synchro = #{record.isSynchro,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.worksRadio != null">
        works_radio = #{record.worksRadio,jdbcType=INTEGER},
      </if>
      <if test="record.worksMd5 != null">
        works_md5 = #{record.worksMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.worksSize != null">
        works_size = #{record.worksSize,jdbcType=BIGINT},
      </if>
      <if test="record.totalDuration != null">
        total_duration = #{record.totalDuration,jdbcType=INTEGER},
      </if>
      <if test="record.durationPerFrame != null">
        duration_per_frame = #{record.durationPerFrame,jdbcType=INTEGER},
      </if>
      <if test="record.frames != null">
        frames = #{record.frames,jdbcType=INTEGER},
      </if>
      <if test="record.roundsNumber != null">
        rounds_number = #{record.roundsNumber,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_collage_works
    set id = #{record.id,jdbcType=INTEGER},
      works_id = #{record.worksId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=INTEGER},
      collage_size_id = #{record.collageSizeId,jdbcType=INTEGER},
      render_image = #{record.renderImage,jdbcType=VARCHAR},
      pattern_id = #{record.patternId,jdbcType=INTEGER},
      works_origin = #{record.worksOrigin,jdbcType=INTEGER},
      is_synchro = #{record.isSynchro,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      works_radio = #{record.worksRadio,jdbcType=INTEGER},
      works_md5 = #{record.worksMd5,jdbcType=VARCHAR},
      works_size = #{record.worksSize,jdbcType=BIGINT},
      total_duration = #{record.totalDuration,jdbcType=INTEGER},
      duration_per_frame = #{record.durationPerFrame,jdbcType=INTEGER},
      frames = #{record.frames,jdbcType=INTEGER},
      rounds_number = #{record.roundsNumber,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPo">
    update mgk_collage_works
    <set>
      <if test="worksId != null">
        works_id = #{worksId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="collageSizeId != null">
        collage_size_id = #{collageSizeId,jdbcType=INTEGER},
      </if>
      <if test="renderImage != null">
        render_image = #{renderImage,jdbcType=VARCHAR},
      </if>
      <if test="patternId != null">
        pattern_id = #{patternId,jdbcType=INTEGER},
      </if>
      <if test="worksOrigin != null">
        works_origin = #{worksOrigin,jdbcType=INTEGER},
      </if>
      <if test="isSynchro != null">
        is_synchro = #{isSynchro,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="worksRadio != null">
        works_radio = #{worksRadio,jdbcType=INTEGER},
      </if>
      <if test="worksMd5 != null">
        works_md5 = #{worksMd5,jdbcType=VARCHAR},
      </if>
      <if test="worksSize != null">
        works_size = #{worksSize,jdbcType=BIGINT},
      </if>
      <if test="totalDuration != null">
        total_duration = #{totalDuration,jdbcType=INTEGER},
      </if>
      <if test="durationPerFrame != null">
        duration_per_frame = #{durationPerFrame,jdbcType=INTEGER},
      </if>
      <if test="frames != null">
        frames = #{frames,jdbcType=INTEGER},
      </if>
      <if test="roundsNumber != null">
        rounds_number = #{roundsNumber,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPo">
    update mgk_collage_works
    set works_id = #{worksId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=INTEGER},
      collage_size_id = #{collageSizeId,jdbcType=INTEGER},
      render_image = #{renderImage,jdbcType=VARCHAR},
      pattern_id = #{patternId,jdbcType=INTEGER},
      works_origin = #{worksOrigin,jdbcType=INTEGER},
      is_synchro = #{isSynchro,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      works_radio = #{worksRadio,jdbcType=INTEGER},
      works_md5 = #{worksMd5,jdbcType=VARCHAR},
      works_size = #{worksSize,jdbcType=BIGINT},
      total_duration = #{totalDuration,jdbcType=INTEGER},
      duration_per_frame = #{durationPerFrame,jdbcType=INTEGER},
      frames = #{frames,jdbcType=INTEGER},
      rounds_number = #{roundsNumber,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_works (works_id, name, account_id, 
      collage_size_id, render_image, pattern_id, 
      works_origin, is_synchro, is_deleted, 
      ctime, mtime, works_radio, 
      works_md5, works_size, total_duration, 
      duration_per_frame, frames, rounds_number
      )
    values (#{worksId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{accountId,jdbcType=INTEGER}, 
      #{collageSizeId,jdbcType=INTEGER}, #{renderImage,jdbcType=VARCHAR}, #{patternId,jdbcType=INTEGER}, 
      #{worksOrigin,jdbcType=INTEGER}, #{isSynchro,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{worksRadio,jdbcType=INTEGER}, 
      #{worksMd5,jdbcType=VARCHAR}, #{worksSize,jdbcType=BIGINT}, #{totalDuration,jdbcType=INTEGER}, 
      #{durationPerFrame,jdbcType=INTEGER}, #{frames,jdbcType=INTEGER}, #{roundsNumber,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      works_id = values(works_id),
      name = values(name),
      account_id = values(account_id),
      collage_size_id = values(collage_size_id),
      render_image = values(render_image),
      pattern_id = values(pattern_id),
      works_origin = values(works_origin),
      is_synchro = values(is_synchro),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      works_radio = values(works_radio),
      works_md5 = values(works_md5),
      works_size = values(works_size),
      total_duration = values(total_duration),
      duration_per_frame = values(duration_per_frame),
      frames = values(frames),
      rounds_number = values(rounds_number),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_collage_works
      (works_id,name,account_id,collage_size_id,render_image,pattern_id,works_origin,is_synchro,is_deleted,ctime,mtime,works_radio,works_md5,works_size,total_duration,duration_per_frame,frames,rounds_number)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.worksId,jdbcType=BIGINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=INTEGER},
        #{item.collageSizeId,jdbcType=INTEGER},
        #{item.renderImage,jdbcType=VARCHAR},
        #{item.patternId,jdbcType=INTEGER},
        #{item.worksOrigin,jdbcType=INTEGER},
        #{item.isSynchro,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.worksRadio,jdbcType=INTEGER},
        #{item.worksMd5,jdbcType=VARCHAR},
        #{item.worksSize,jdbcType=BIGINT},
        #{item.totalDuration,jdbcType=INTEGER},
        #{item.durationPerFrame,jdbcType=INTEGER},
        #{item.frames,jdbcType=INTEGER},
        #{item.roundsNumber,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_collage_works
      (works_id,name,account_id,collage_size_id,render_image,pattern_id,works_origin,is_synchro,is_deleted,ctime,mtime,works_radio,works_md5,works_size,total_duration,duration_per_frame,frames,rounds_number)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.worksId,jdbcType=BIGINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=INTEGER},
        #{item.collageSizeId,jdbcType=INTEGER},
        #{item.renderImage,jdbcType=VARCHAR},
        #{item.patternId,jdbcType=INTEGER},
        #{item.worksOrigin,jdbcType=INTEGER},
        #{item.isSynchro,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.worksRadio,jdbcType=INTEGER},
        #{item.worksMd5,jdbcType=VARCHAR},
        #{item.worksSize,jdbcType=BIGINT},
        #{item.totalDuration,jdbcType=INTEGER},
        #{item.durationPerFrame,jdbcType=INTEGER},
        #{item.frames,jdbcType=INTEGER},
        #{item.roundsNumber,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      works_id = values(works_id),
      name = values(name),
      account_id = values(account_id),
      collage_size_id = values(collage_size_id),
      render_image = values(render_image),
      pattern_id = values(pattern_id),
      works_origin = values(works_origin),
      is_synchro = values(is_synchro),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      works_radio = values(works_radio),
      works_md5 = values(works_md5),
      works_size = values(works_size),
      total_duration = values(total_duration),
      duration_per_frame = values(duration_per_frame),
      frames = values(frames),
      rounds_number = values(rounds_number),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.collage.biz.po.MgkCollageWorksPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_works
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="worksId != null">
        works_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="collageSizeId != null">
        collage_size_id,
      </if>
      <if test="renderImage != null">
        render_image,
      </if>
      <if test="patternId != null">
        pattern_id,
      </if>
      <if test="worksOrigin != null">
        works_origin,
      </if>
      <if test="isSynchro != null">
        is_synchro,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="worksRadio != null">
        works_radio,
      </if>
      <if test="worksMd5 != null">
        works_md5,
      </if>
      <if test="worksSize != null">
        works_size,
      </if>
      <if test="totalDuration != null">
        total_duration,
      </if>
      <if test="durationPerFrame != null">
        duration_per_frame,
      </if>
      <if test="frames != null">
        frames,
      </if>
      <if test="roundsNumber != null">
        rounds_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="worksId != null">
        #{worksId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="collageSizeId != null">
        #{collageSizeId,jdbcType=INTEGER},
      </if>
      <if test="renderImage != null">
        #{renderImage,jdbcType=VARCHAR},
      </if>
      <if test="patternId != null">
        #{patternId,jdbcType=INTEGER},
      </if>
      <if test="worksOrigin != null">
        #{worksOrigin,jdbcType=INTEGER},
      </if>
      <if test="isSynchro != null">
        #{isSynchro,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="worksRadio != null">
        #{worksRadio,jdbcType=INTEGER},
      </if>
      <if test="worksMd5 != null">
        #{worksMd5,jdbcType=VARCHAR},
      </if>
      <if test="worksSize != null">
        #{worksSize,jdbcType=BIGINT},
      </if>
      <if test="totalDuration != null">
        #{totalDuration,jdbcType=INTEGER},
      </if>
      <if test="durationPerFrame != null">
        #{durationPerFrame,jdbcType=INTEGER},
      </if>
      <if test="frames != null">
        #{frames,jdbcType=INTEGER},
      </if>
      <if test="roundsNumber != null">
        #{roundsNumber,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="worksId != null">
        works_id = values(works_id),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="collageSizeId != null">
        collage_size_id = values(collage_size_id),
      </if>
      <if test="renderImage != null">
        render_image = values(render_image),
      </if>
      <if test="patternId != null">
        pattern_id = values(pattern_id),
      </if>
      <if test="worksOrigin != null">
        works_origin = values(works_origin),
      </if>
      <if test="isSynchro != null">
        is_synchro = values(is_synchro),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="worksRadio != null">
        works_radio = values(works_radio),
      </if>
      <if test="worksMd5 != null">
        works_md5 = values(works_md5),
      </if>
      <if test="worksSize != null">
        works_size = values(works_size),
      </if>
      <if test="totalDuration != null">
        total_duration = values(total_duration),
      </if>
      <if test="durationPerFrame != null">
        duration_per_frame = values(duration_per_frame),
      </if>
      <if test="frames != null">
        frames = values(frames),
      </if>
      <if test="roundsNumber != null">
        rounds_number = values(rounds_number),
      </if>
    </trim>
  </insert>
</mapper>