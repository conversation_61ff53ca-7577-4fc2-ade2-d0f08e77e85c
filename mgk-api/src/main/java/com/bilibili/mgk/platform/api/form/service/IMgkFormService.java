package com.bilibili.mgk.platform.api.form.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.data.dto.ExtFormDataDto;
import com.bilibili.mgk.platform.api.form.dto.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018/1/17
 **/
public interface IMgkFormService {
    long create(Operator operator, NewMgkFormDto newMgkFormDto);

    void update(Operator operator, UpdateMgkFormDto updateMgkFormDto);

    void delete(Operator operator, Long formId);

    boolean canUpdateItem(Long formId);

    MgkFormDto getFormDtoByFormIdWithCache(Long formId, boolean needRefreshFormSubmitCache);

    MgkFormDto getFormDtoByFormId(Long formId);

    List<MgkFormDto> getFormDropboxByAccountId(Integer accountId, Integer formType, Set<Integer> formTypeSet);

    List<MgkFormDto> getFormDtosInFormIds(List<Long> formIds);

    Map<Long, MgkFormDto> getFormMapInFormIds(List<Long> formIds);

    List<Long> getFormIdsByPageId(Long pageId);

    List<MgkFormDto> getFormDtos(QueryFormParamDto queryFormParamDto);

    MgkFormDto getBaseFormDtoByFormId(Long formId);

    List<MgkFormDto> getBaseFormDtosInFormIds(List<Long> formIds);

    PageResult<MgkFormDto> getPageFormDtos(QueryFormParamDto queryFormParamDto);

    Map<Long, List<Long>> getFormId2SortedItemIdMapInformIds(List<Long> formIds);

    MgkFormItemDto getFormItemDtoByFormIdAndItemId(Long formId, Long formItemId);

    List<MgkFormItemDto> getFormItemDtosInItemIds(List<Long> formItemIds);

    List<MgkFormItemDto> getFormItemDtosByFormId(Long formId);

    /**
     * 从缓存中获取表单项 目前不包含下拉组件配置
     *
     * @param formId
     * @return {@link List}<{@link MgkFormItemDto}>
     */
    List<MgkFormItemDto> getFormItemDtosByFormIdFromCache(Long formId);

    void refreshFormItemCache();

    void refreshFormItemCacheByFormIds(List<Long> formIds);

    Integer getFormTypeByFormId(Long formId);

    Map<Long, Integer> getFormTypeMapByFormIds(List<Long> formIds);

    List<AreaTreeDto> getAreasTreeFromCache();

    Map<Long, ExtFormDataDto> getFormCount(List<Long> formIds);

    /**
     * 根据formId从缓存中获取表单提交的姓名和手机号
     *
     * @param formId
     * @return
     */
    List<MgkFormSubmitInfoDto> getSubmitInfo(Long formId);

    /**
     * 刷新表单缓存
     */
    Long refreshFormCache();

    /**
     * 刷新表单提交的数据
     */
    void refreshFormSubmitInfo();

    void refreshFormSubmitInfo(List<Long> formIds);

    /**
     * 目前一般是吉利定制表单更新专用 后门调
     */
    Integer updateOptions(String options, Long formId, Long formItemId);

    Long copyForm(Long originFormId, Integer accountId, String formName);
}
