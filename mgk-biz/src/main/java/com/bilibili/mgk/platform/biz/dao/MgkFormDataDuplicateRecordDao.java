package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkFormDataDuplicateRecordPo;
import com.bilibili.mgk.platform.biz.po.MgkFormDataDuplicateRecordPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MgkFormDataDuplicateRecordDao {
    long countByExample(MgkFormDataDuplicateRecordPoExample example);

    int deleteByExample(MgkFormDataDuplicateRecordPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkFormDataDuplicateRecordPo record);

    int insertBatch(List<MgkFormDataDuplicateRecordPo> records);

    int insertUpdateBatch(List<MgkFormDataDuplicateRecordPo> records);

    int insert(MgkFormDataDuplicateRecordPo record);

    int insertUpdateSelective(MgkFormDataDuplicateRecordPo record);

    int insertSelective(MgkFormDataDuplicateRecordPo record);

    List<MgkFormDataDuplicateRecordPo> selectByExample(MgkFormDataDuplicateRecordPoExample example);

    MgkFormDataDuplicateRecordPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkFormDataDuplicateRecordPo record, @Param("example") MgkFormDataDuplicateRecordPoExample example);

    int updateByExample(@Param("record") MgkFormDataDuplicateRecordPo record, @Param("example") MgkFormDataDuplicateRecordPoExample example);

    int updateByPrimaryKeySelective(MgkFormDataDuplicateRecordPo record);

    int updateByPrimaryKey(MgkFormDataDuplicateRecordPo record);
}