package com.bilibili.mgk.platform.api.wechat.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountCreateDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountListDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountQueryDto;

import java.util.List;

/**
 * @ClassName IWechatAccountService
 * <AUTHOR>
 * @Date 2022/5/31 12:12 下午
 * @Version 1.0
 **/
public interface IMgkWechatAccountService {

    Integer create(WechatAccountCreateDto createDto);

    Integer update(WechatAccountCreateDto updateDto, Operator operator);

    PageResult<WechatAccountListDto> queryByPage(WechatAccountQueryDto queryDto);

    List<WechatAccountListDto> queryList(WechatAccountQueryDto queryDto);

    WechatAccountDto getValidDtoById(Integer id);

    Integer deleteById(Integer id, Operator operator);

    List<Integer> getLastAccountWechatPackageIds(Integer id);

    List<Integer> getAccountWechatPackageIds(Integer id);
}
