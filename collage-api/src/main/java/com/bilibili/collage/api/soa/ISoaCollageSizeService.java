package com.bilibili.collage.api.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.CollageSizeDto;
import com.bilibili.collage.api.dto.QueryCollageSizeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/22
 **/
public interface ISoaCollageSizeService {

    PageResult<CollageSizeDto> queryCollageSizeByPage(QueryCollageSizeDto queryParam);

    List<CollageSizeDto> queryCollageSize(QueryCollageSizeDto queryParam);

    CollageSizeDto getCollageSizeById(Integer sizeId);

    List<CollageSizeDto> getCollageSizeByIds (List<Integer> sizeIds);

    void createCollageSize (CollageSizeDto collageSizeDto);

    void updateCollageSize (CollageSizeDto collageSizeDto);

    void deleteCollageSize (Integer sizeId);

    void updateStatus(Operator operator, Integer sizeId, Integer status);
}
