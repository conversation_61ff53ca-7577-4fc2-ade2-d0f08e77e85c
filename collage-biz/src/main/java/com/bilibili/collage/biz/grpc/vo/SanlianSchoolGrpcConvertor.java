package com.bilibili.collage.biz.grpc.vo;

import com.bapis.ad.mgk.material.GrpcNodeDeleteReq;
import com.bapis.ad.mgk.material.GrpcSanlianDocNode;
import com.bapis.ad.mgk.material.GrpcSanlianDocNode.Builder;
import com.bapis.ad.mgk.material.GrpcSanlianDocTreeNode;
import com.bapis.ad.mgk.material.GrpcSanlianNodeBatchGetReq;
import com.bapis.ad.mgk.material.GrpcSanlianNodeCreateReq;
import com.bapis.ad.mgk.material.GrpcSanlianNodeGetReq;
import com.bapis.ad.mgk.material.GrpcSanlianNodeListReq;
import com.bapis.ad.mgk.material.GrpcSanlianNodeMovReq;
import com.bapis.ad.mgk.material.GrpcSanlianNodePageReq;
import com.bapis.ad.mgk.material.GrpcSanlianNodeUpdateReq;
import com.bapis.ad.mgk.material.GrpcSanlianSubTreeReq;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocNode;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocTreeNode;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianSchoolNodeType;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianSchoolTreeType;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeBatchGetReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeCreateReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeGetReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeListReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeMoveReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodePageReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeUpdateReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianSubTreeReq;
import com.bilibili.collage.biz.utils.ProtobufValueWrapUtils;
import com.biz.common.doc.tree.service.vo.NodeDeleteReq;
import com.biz.common.doc.tree.service.vo.NodeSortType;
import io.vavr.control.Try;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/17
 */
@Mapper
public interface SanlianSchoolGrpcConvertor {

    SanlianSchoolGrpcConvertor instance = Mappers.getMapper(SanlianSchoolGrpcConvertor.class);

    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    default SanlianNodeCreateReq grpcRequest2BO(GrpcSanlianNodeCreateReq request) {

        return new SanlianNodeCreateReq()
                .setTreeType(SanlianSchoolTreeType.fromName(request.getTreeType()))
                .setTitle(ProtobufValueWrapUtils.fromStringValue(request.hasTitle(), request.getTitle()))
                .setNodeName(ProtobufValueWrapUtils.fromStringValue(request.hasNodeName(), request.getNodeName()))
                .setParentId(ProtobufValueWrapUtils.fromInt64Value(request.hasParentId(), request.getParentId()))
                .setNodeType(ProtobufValueWrapUtils.fromStringValue(request.hasNodeType(), request.getNodeType()))
                .setOrderNum(ProtobufValueWrapUtils.fromInt32Value(request.hasOrderNum(), request.getOrderNum()))
                .setHasDoc(ProtobufValueWrapUtils.fromBoolValue(request.hasHasDoc(), request.getHasDoc()))
                .setBrief(ProtobufValueWrapUtils.fromStringValue(request.hasBrief(), request.getBrief()))
                .setContent(ProtobufValueWrapUtils.fromStringValue(request.hasContent(), request.getContent()))
                .setIsShow(ProtobufValueWrapUtils.fromBoolValue(request.hasIsShow(), request.getIsShow()))
                .setIsDeleted(ProtobufValueWrapUtils.fromBoolValue(request.hasIsDeleted(), request.getIsDeleted()))
                .setDocType(ProtobufValueWrapUtils.fromStringValue(request.hasDocType(), request.getDocType()))
                .setImage(ProtobufValueWrapUtils.fromStringValue(request.hasImage(), request.getImage()))
                .setRawArticleId(
                        ProtobufValueWrapUtils.fromStringValue(request.hasRawArticleId(), request.getRawArticleId()))
                .setCover(ProtobufValueWrapUtils.fromStringValue(request.hasCover(), request.getCover()))
                .setMarkdown(ProtobufValueWrapUtils.fromInt32Value(request.hasMarkdown(), request.getMarkdown()))
                .setContentType(
                        ProtobufValueWrapUtils.fromInt32Value(request.hasContentType(), request.getContentType()))
                .setUrl(ProtobufValueWrapUtils.fromStringValue(request.hasUrl(), request.getUrl()))
                .setJumpUrl(ProtobufValueWrapUtils.fromStringValue(request.hasJumpUrl(), request.getJumpUrl()))
                .setArticleId(ProtobufValueWrapUtils.fromInt64Value(request.hasArticleId(), request.getArticleId()))
                .setArticleTitle(
                        ProtobufValueWrapUtils.fromStringValue(request.hasArticleTitle(), request.getArticleTitle()))
                .setArticleType(
                        ProtobufValueWrapUtils.fromInt32Value(request.hasArticleType(), request.getArticleType()))
                .setArticleParentId(ProtobufValueWrapUtils.fromInt64Value(request.hasArticleParentId(),
                        request.getArticleParentId()))
                .setArticleParentName(ProtobufValueWrapUtils.fromStringValue(request.hasArticleParentName(),
                        request.getArticleParentName()))
                .setSanlianOrderNum(ProtobufValueWrapUtils.fromInt32Value(request.hasSanlianOrderNum(),
                        request.getSanlianOrderNum()))
                .setLinkNodeId(ProtobufValueWrapUtils.fromInt64Value(request.hasLinkNodeId(), request.getLinkNodeId()))
                .setLinkType(Try.of(() -> Optional
                                .ofNullable(ProtobufValueWrapUtils.fromStringValue(request.hasLinkType(),
                                        request.getLinkType()))
                                .map(SanlianSchoolTreeType::fromName)
                                .orElse(null))
                        .getOrElseThrow(t -> {
                            throw new IllegalArgumentException("unknown linkType: " + request.getLinkType());
                        }));

    }


    default GrpcSanlianDocTreeNode treeNode2GrpcResp(SanlianDocTreeNode r) {

        List<GrpcSanlianDocTreeNode> children = Optional.ofNullable(r.getChildren()).orElse(new ArrayList<>())
                .stream().map(c -> treeNode2GrpcResp(c))
                .collect(Collectors.toList());

        GrpcSanlianDocNode nodeInfo = bo2GrpcResp(r);

        return GrpcSanlianDocTreeNode.newBuilder()
                .addAllChildren(children)
                .setNodeInfo(nodeInfo)
                .build();

    }


    default GrpcSanlianDocNode bo2GrpcResp(SanlianDocNode r) {

        Builder builder = GrpcSanlianDocNode.newBuilder();

        builder.setTreeType(Optional.ofNullable(r.getTreeType()).map(SanlianSchoolTreeType::name).orElse(""));
        builder.setId(Optional.ofNullable(r.getId()).orElse(0L));
        builder.setNodeId(Optional.ofNullable(r.getNodeId()).orElse(0L));
        builder.setTitle(Optional.ofNullable(r.getTitle()).orElse(""));
        builder.setNodeName(Optional.ofNullable(r.getNodeName()).orElse(""));
        builder.setParentId(Optional.ofNullable(r.getParentId()).orElse(0L));

//        builder.setParentName(r.getParentName());

        builder.setNodeType(Optional.ofNullable(r.getNodeType()).orElse(""));
        builder.setOrderNum(Optional.ofNullable(r.getOrderNum()).orElse(0));
        builder.setHasDoc(Optional.ofNullable(r.getHasDoc()).orElse(false));
        builder.setBrief(Optional.ofNullable(r.getBrief()).orElse(""));
        builder.setContent(Optional.ofNullable(r.getContent()).orElse(""));
        builder.setSortPriority(Optional.ofNullable(r.getSortPriority()).orElse(0));
        builder.setIsShow(Optional.ofNullable(r.getIsShow()).orElse(true));
        builder.setIsDeleted(Optional.ofNullable(r.getIsDeleted()).orElse(false));
        builder.setDocType(Optional.ofNullable(r.getDocType()).orElse(""));
        builder.setCtime(Optional.ofNullable(r.getCtime()).map(df::format).orElse(""));
        builder.setMtime(Optional.ofNullable(r.getMtime()).map(df::format).orElse(""));
        builder.setImage(Optional.ofNullable(r.getImage()).orElse(""));
        builder.setRawArticleId(Optional.ofNullable(r.getRawArticleId()).orElse(""));
        builder.setCover(Optional.ofNullable(r.getCover()).orElse(""));
        builder.setMarkdown(Optional.ofNullable(r.getMarkdown()).orElse(0));
        builder.setContentType(Optional.ofNullable(r.getContentType()).orElse(0));
        builder.setUrl(Optional.ofNullable(r.getUrl()).orElse(""));
        builder.setJumpUrl(Optional.ofNullable(r.getJumpUrl()).orElse(""));
        builder.setArticleId(Optional.ofNullable(r.getArticleId()).orElse(0L));
        builder.setArticleTitle(Optional.ofNullable(r.getArticleTitle()).orElse(""));
        builder.setArticleType(Optional.ofNullable(r.getArticleType()).orElse(0));
        builder.setArticleParentId(Optional.ofNullable(r.getArticleParentId()).orElse(0L));
        builder.setArticleParentName(Optional.ofNullable(r.getArticleParentName()).orElse(""));
        builder.setSanlianOrderNum(Optional.ofNullable(r.getSanlianOrderNum()).orElse(0));
        builder.setLinkNodeId(Optional.ofNullable(r.getLinkNodeId()).orElse(0L));
        builder.setLinkType(Optional.ofNullable(r.getLinkType()).map(SanlianSchoolTreeType::name).orElse(""));
        builder.setPath(Optional.ofNullable(r.getPath()).orElse(""));
        return builder.build();
    }


    default SanlianNodeUpdateReq grpcRequest2BO(GrpcSanlianNodeUpdateReq request) {

        return new SanlianNodeUpdateReq()
                .setNodeId(request.getNodeId())
                .setTitle(ProtobufValueWrapUtils.fromStringValue(request.hasTitle(), request.getTitle()))
                .setNodeName(ProtobufValueWrapUtils.fromStringValue(request.hasNodeName(), request.getNodeName()))
                .setNodeType(ProtobufValueWrapUtils.fromStringValue(request.hasNodeType(), request.getNodeType()))
                .setOrderNum(ProtobufValueWrapUtils.fromInt32Value(request.hasOrderNum(), request.getOrderNum()))
                .setHasDoc(ProtobufValueWrapUtils.fromBoolValue(request.hasHasDoc(), request.getHasDoc()))
                .setBrief(ProtobufValueWrapUtils.fromStringValue(request.hasBrief(), request.getBrief()))
                .setContent(ProtobufValueWrapUtils.fromStringValue(request.hasContent(), request.getContent()))
                .setIsShow(ProtobufValueWrapUtils.fromBoolValue(request.hasIsShow(), request.getIsShow()))
                .setDocType(ProtobufValueWrapUtils.fromStringValue(request.hasDocType(), request.getDocType()))
                .setImage(ProtobufValueWrapUtils.fromStringValue(request.hasImage(), request.getImage()))
                .setRawArticleId(
                        ProtobufValueWrapUtils.fromStringValue(request.hasRawArticleId(), request.getRawArticleId()))
                .setCover(ProtobufValueWrapUtils.fromStringValue(request.hasCover(), request.getCover()))
                .setMarkdown(ProtobufValueWrapUtils.fromInt32Value(request.hasMarkdown(), request.getMarkdown()))
                .setContentType(
                        ProtobufValueWrapUtils.fromInt32Value(request.hasContentType(), request.getContentType()))
                .setUrl(ProtobufValueWrapUtils.fromStringValue(request.hasUrl(), request.getUrl()))
                .setJumpUrl(ProtobufValueWrapUtils.fromStringValue(request.hasJumpUrl(), request.getJumpUrl()))
                .setArticleId(ProtobufValueWrapUtils.fromInt64Value(request.hasArticleId(), request.getArticleId()))
                .setArticleTitle(
                        ProtobufValueWrapUtils.fromStringValue(request.hasArticleTitle(), request.getArticleTitle()))
                .setArticleType(
                        ProtobufValueWrapUtils.fromInt32Value(request.hasArticleType(), request.getArticleType()))
                .setArticleParentId(ProtobufValueWrapUtils.fromInt64Value(request.hasArticleParentId(),
                        request.getArticleParentId()))
                .setArticleParentName(ProtobufValueWrapUtils.fromStringValue(request.hasArticleParentName(),
                        request.getArticleParentName()))
                .setSanlianOrderNum(ProtobufValueWrapUtils.fromInt32Value(request.hasSanlianOrderNum(),
                        request.getSanlianOrderNum()))
                .setLinkNodeId(ProtobufValueWrapUtils.fromInt64Value(request.hasLinkNodeId(), request.getLinkNodeId()))
                .setLinkType(Try.of(() -> Optional
                                .ofNullable(ProtobufValueWrapUtils.fromStringValue(request.hasLinkType(),
                                        request.getLinkType()))
                                .map(SanlianSchoolTreeType::fromName)
                                .orElse(null))
                        .getOrElseThrow(t -> {
                            throw new IllegalArgumentException("unknown linkType: " + request.getLinkType());
                        }));

    }

    default SanlianNodeMoveReq grpcRequest2BO(GrpcSanlianNodeMovReq request) {

        SanlianNodeMoveReq req = new SanlianNodeMoveReq()
                .setTreeType(SanlianSchoolTreeType.fromName(request.getTreeType()))
                .setNodeId(request.getNodeId())
                .setTargetParentId(
                        ProtobufValueWrapUtils.fromInt64Value(request.hasTargetParentId(), request.getTargetParentId()))
                .setForce(ProtobufValueWrapUtils.fromBoolValue(request.hasForce(), request.getForce()));

        SanlianNodeUpdateReq updateSelective = new SanlianNodeUpdateReq();

        updateSelective.setOrderNum(
                ProtobufValueWrapUtils.fromInt32Value(request.hasOrderNum(), request.getOrderNum()));
        updateSelective.setIsShow(ProtobufValueWrapUtils.fromBoolValue(request.hasIsShow(), request.getIsShow()));

        boolean isEmptyUpdateSelective = Objects.equals(updateSelective, new SanlianNodeUpdateReq());

        if (!isEmptyUpdateSelective) {
            req.setWithUpdateReq(updateSelective);
            updateSelective.setNodeId(request.getNodeId());
        }

        return req;


    }

    default NodeDeleteReq grpcRequest2BO(GrpcNodeDeleteReq request) {

        return new NodeDeleteReq()
                .setNodeId(request.getNodeId())
                .setForce(ProtobufValueWrapUtils.fromBoolValue(request.hasForce(), request.getForce()));
    }

    default SanlianSubTreeReq grpcRequest2BO(GrpcSanlianSubTreeReq request) {

        return new SanlianSubTreeReq()
                .setTreeType(SanlianSchoolTreeType.fromName(request.getTreeType()))
                .setRetrieveDoc(
                        ProtobufValueWrapUtils.fromBoolValue(request.hasRetrieveDoc(), request.getRetrieveDoc()))
                .setIsDeleted(ProtobufValueWrapUtils.fromBoolValue(request.hasIsDeleted(), request.getIsDeleted()))
                .setIsShow(ProtobufValueWrapUtils.fromBoolValue(request.hasIsShow(), request.getIsShow()));

    }


    default SanlianNodeListReq grpcRequest2BO(GrpcSanlianNodeListReq request) {

        return new SanlianNodeListReq()
                .setTreeType(SanlianSchoolTreeType.fromName(request.getTreeType()))
                .setNodeId(ProtobufValueWrapUtils.fromInt64Value(request.hasNodeId(), request.getNodeId()))
                .setAccountId(ProtobufValueWrapUtils.fromStringValue(request.hasAccountId(), request.getAccountId()))
                .setDepthStrategy(
                        ProtobufValueWrapUtils.fromStringValue(request.hasDepthStrategy(), request.getDepthStrategy()))
                .setDepth(ProtobufValueWrapUtils.fromInt32Value(request.hasDepth(), request.getDepth()))
                .setIsDeleted(ProtobufValueWrapUtils.fromBoolValue(request.hasIsDeleted(), request.getIsDeleted()))
                .setIsShow(ProtobufValueWrapUtils.fromBoolValue(request.hasIsShow(), request.getIsShow()))
                .setNodeTypes(
                        request.getNodeTypesList()
                                .stream().map(SanlianSchoolNodeType::fromName)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList())
                )
                .setSort(NodeSortType.fromName(
                        ProtobufValueWrapUtils.fromStringValue(request.hasSort(), request.getSort())));

    }

    default SanlianNodePageReq grpcRequest2BO(GrpcSanlianNodePageReq request) {

        return new SanlianNodePageReq()
                .setPn(request.getPn())
                .setPs(request.getPs())
                .setTreeType(SanlianSchoolTreeType.fromName(request.getTreeType()))
                .setNodeId(ProtobufValueWrapUtils.fromInt64Value(request.hasNodeId(), request.getNodeId()))
                .setAccountId(ProtobufValueWrapUtils.fromStringValue(request.hasAccountId(), request.getAccountId()))
                .setDepthStrategy(
                        ProtobufValueWrapUtils.fromStringValue(request.hasDepthStrategy(), request.getDepthStrategy()))
                .setDepth(ProtobufValueWrapUtils.fromInt32Value(request.hasDepth(), request.getDepth()))
                .setIsDeleted(ProtobufValueWrapUtils.fromBoolValue(request.hasIsDeleted(), request.getIsDeleted()))
                .setIsShow(ProtobufValueWrapUtils.fromBoolValue(request.hasIsShow(), request.getIsShow()))
                .setNodeTypes(
                        request.getNodeTypesList()
                                .stream().map(SanlianSchoolNodeType::fromName)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList())
                )
                .setSort(NodeSortType.fromName(
                        ProtobufValueWrapUtils.fromStringValue(request.hasSort(), request.getSort())));
    }


    default SanlianNodeGetReq grpcRequest2BO(GrpcSanlianNodeGetReq request) {

        return new SanlianNodeGetReq()
                .setNodeId(request.getNodeId())
                .setAccountId(ProtobufValueWrapUtils.fromStringValue(request.hasAccountId(), request.getAccountId()))
                .setIsDeleted(ProtobufValueWrapUtils.fromBoolValue(request.hasIsDeleted(), request.getIsDeleted()))
                .setIsShow(ProtobufValueWrapUtils.fromBoolValue(request.hasIsShow(), request.getIsShow()));
    }

    default SanlianNodeBatchGetReq grpcRequest2BO(GrpcSanlianNodeBatchGetReq request) {
        return new SanlianNodeBatchGetReq()
                .setNodeIds(new ArrayList<>(request.getNodeIdsList()))
                .setAccountId(ProtobufValueWrapUtils.fromStringValue(request.hasAccountId(), request.getAccountId()))
                .setIsDeleted(ProtobufValueWrapUtils.fromBoolValue(request.hasIsDeleted(), request.getIsDeleted()))
                .setIsShow(ProtobufValueWrapUtils.fromBoolValue(request.hasIsShow(), request.getIsShow()));
    }

}
