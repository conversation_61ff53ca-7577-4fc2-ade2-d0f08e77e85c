package com.bilibili.mgk.platform.common;


import com.bilibili.adp.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;


/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/19 20:13
 */
@AllArgsConstructor
@Slf4j
public enum ConsultPageCustomerServiceEnum {

    SHANGWUTONG(1, "SHANGWUTONG", "商务通") {


        @Override
        public String decryptAuthReq(String scrKey ,String authStr) throws NoSuchPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, BadPaddingException, InvalidKeyException, UnsupportedEncodingException {
            // 将密钥转换为 SecretKeySpec 对象
            log.info("[decryptAuthReq] scrKey:{},authStr:{}", scrKey,authStr);
            SecretKey secretKey = new SecretKeySpec(scrKey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(authStr));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }
    },

    NETEASE(2, "Netease", "网易七鱼") {
        @Override
        public String decryptAuthReq(String scrKey ,String authStr) throws NoSuchPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, BadPaddingException, InvalidKeyException, UnsupportedEncodingException {
            // 将密钥转换为 SecretKeySpec 对象
            log.info("[decryptAuthReq] scrKey:{},authStr:{}", scrKey,authStr);
            SecretKey secretKey = new SecretKeySpec(scrKey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(authStr));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }
    },

    MEI_QIA(3, "MEI_QIA", "美洽") {
        @Override
        public String decryptAuthReq(String scrKey, String authStr) throws NoSuchPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, BadPaddingException, InvalidKeyException, UnsupportedEncodingException {
            // 将密钥转换为 SecretKeySpec 对象
            log.info("[decryptAuthReq] scrKey:{},authStr:{}", scrKey, authStr);
            SecretKey secretKey = new SecretKeySpec(scrKey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(authStr));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }
    },
    WU_SAN(4, "WU_SAN", "53快服") {
        @Override
        public String decryptAuthReq(String scrKey, String authStr) throws NoSuchPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, BadPaddingException, InvalidKeyException, UnsupportedEncodingException {
            // 将密钥转换为 SecretKeySpec 对象
            log.info("[decryptAuthReq] scrKey:{},authStr:{}", scrKey, authStr);
            SecretKey secretKey = new SecretKeySpec(scrKey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(authStr));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }
    },
    YUN_QUE(5, "YUN_QUE", "云雀客服") {
        @Override
        public String decryptAuthReq(String scrKey, String authStr) throws NoSuchPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, BadPaddingException, InvalidKeyException, UnsupportedEncodingException {
            // 将密钥转换为 SecretKeySpec 对象
            log.info("[decryptAuthReq] scrKey:{},authStr:{}", scrKey, authStr);
            SecretKey secretKey = new SecretKeySpec(scrKey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(authStr));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }
    },
    KUAI_SHANG_TONG(6, "KUAI_SHANG_TONG", "快商通") {
        @Override
        public String decryptAuthReq(String scrKey, String authStr) throws NoSuchPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, BadPaddingException, InvalidKeyException, UnsupportedEncodingException {
            // 将密钥转换为 SecretKeySpec 对象
            log.info("[decryptAuthReq] scrKey:{},authStr:{}", scrKey, authStr);
            SecretKey secretKey = new SecretKeySpec(scrKey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(authStr));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }
    }
    ;



    @Getter
    private final Integer code;
    @Getter
    private final String thirdPartCode;
    @Getter
    private final String name;

    public static ConsultPageCustomerServiceEnum getByCode(int code) throws ServiceException {
        for (ConsultPageCustomerServiceEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new com.bilibili.adp.common.exception.ServiceException(MgkWebExceptionCode.NOT_EXSIST_SERVICE_CODE);
    }


    public static ConsultPageCustomerServiceEnum getByThirdPartCode(String code) {
        for (ConsultPageCustomerServiceEnum bean : values()) {
            if (Objects.equals(bean.getThirdPartCode(), code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code ConsultPageCustomerServiceEnum " + code);
    }

    public static String getNameByCode(int code) {
        for (ConsultPageCustomerServiceEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean.getName();
            }
        }
        return "未知";
    }


    public abstract String decryptAuthReq(String scrKey, String authStr) throws NoSuchPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, BadPaddingException, InvalidKeyException, UnsupportedEncodingException;

}
