CREATE TABLE `mgk_node_favorite`
(
    `id`         BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',

    -- 收藏原生属性
    `account_id` VARCHAR(150) NOT NULL DEFAULT '' COMMENT '账户ID',
    `node_id`    BIGINT(20)   NOT NULL DEFAULT 0 COMMENT '节点id',
    `favorite`   TINYINT(4)   NOT NULL DEFAULT 0 COMMENT '是否收藏',
    `ctime`      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，排序键',
    `mtime`      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间，排序键',

    -- follow节点属性
    `root_id`    BIGINT(20)   NOT NULL DEFAULT 0 COMMENT '根节点ID',
    `path`       VARCHAR(150) NOT NULL DEFAULT '' COMMENT '从根到该节点的路径',
    `depth`      INT(11)      NOT NULL DEFAULT 0 COMMENT '节点深度',
    `parent_id`  BIGINT(20)   NOT NULL DEFAULT 0 COMMENT '父节点ID，根节点时为0',
    `node_type` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '节点类型，一版用于保存是目录文档类型等',
    `doc_type` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '文档类型',
    `is_deleted` TINYINT(4)   NOT NULL DEFAULT 0 COMMENT '是否删除，0表示未删除，1表示已删除',
    `is_show`    TINYINT(4)   NOT NULL DEFAULT 1 COMMENT '是否展示，0表示不展示，1表示展示',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_account_node` (`account_id`, `node_id`) USING BTREE,
    KEY `idx_root_id` (`root_id`) USING BTREE,
    KEY `idx_path` (`path`) USING BTREE,
    KEY `idx_node_id` (`node_id`) USING BTREE,
    KEY `idx_account_id` (`account_id`) USING BTREE,
    KEY `idx_mtime` (`mtime`) USING BTREE,
    KEY `idx_ctime` (`ctime`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '文档收藏信息';

