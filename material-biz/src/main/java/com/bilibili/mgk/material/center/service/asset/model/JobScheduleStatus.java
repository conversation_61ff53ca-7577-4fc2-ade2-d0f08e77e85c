package com.bilibili.mgk.material.center.service.asset.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/16
 */
@Getter
@RequiredArgsConstructor
public enum JobScheduleStatus {

    /**
     *
     */
    scheduling(0, "定时调度中"),

    /**
     *
     */
    success(1, "调度成功"),

    /**
     *
     */
    fail(2, "调度失败"),

    cancel(3, "取消调度"),

    ;

    private final int code;


    private final String desc;








}
