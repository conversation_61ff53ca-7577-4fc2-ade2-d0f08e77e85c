package com.bilibili.mgk.platform.portal.webapi.consult_page;


import com.alibaba.fastjson.JSON;
import com.bapis.account.service.v2.UserInfo;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceException;
//import com.bilibili.mgk.platform.api.landing_page.dto.ConsultAuthDto;
import com.bilibili.mgk.material.center.service.mainsite.BiliAccountService;
import com.bilibili.mgk.platform.api.landing_page.dto.ConsultAuthDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryLandingPageParamDto;
import com.bilibili.mgk.platform.api.landing_page.dto.UserAssetDto;
import com.bilibili.mgk.platform.biz.service.chat.AbstractMgkConsultChatServiceImpl;
import com.bilibili.mgk.platform.biz.utils.BossUtils;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.MgkWebExceptionCode;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.common.OpenApiBasicController;
import com.bilibili.mgk.platform.portal.core.Pagination;
import com.bilibili.mgk.platform.portal.core.Response;
//import com.bilibili.mgk.platform.portal.service.WebMgkConsultPageService;
import com.bilibili.mgk.platform.portal.openapi.bean.OpenApiResponse;
import com.bilibili.mgk.platform.portal.service.WebMgkConsultPageService;
import com.bilibili.mgk.platform.portal.webapi.consult_page.vo.*;
import com.bilibili.mgk.platform.portal.webapi.landing_page.vo.BiliUserInfoVo;
import com.bilibili.mgk.platform.portal.webapi.landing_page.vo.MgkLandingPageVo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description:
 * <AUTHOR>
 * @date 2025/2/18 18:00
 */
@Controller
@RequestMapping("/open_api/v1/consult_pages")
@Api(value = "/consult_pages", tags = "咨询页相关")
@Slf4j
public class ConsultPageOpenController extends OpenApiBasicController {

    private static final int MAX_QUALIFICATION_BYTES = 1024*1024*5;

    @Value("${consult.chat.pic.boss-path:consult-chat}")
    private String bossPath;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private SystemType systemType;
    @Autowired
    private BossUtils bossUtils;
    @Autowired
    private WebMgkConsultPageService webMgkConsultPageService;
    @Resource
    private BiliAccountService biliAccountService;


    @ApiOperation(value = "外部授权")
    @RequestMapping(value = "/auth", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Object> auth(@ApiIgnore Context context,
                          @RequestBody AuthDetailVo authDetailVo) throws ServiceException {
        log.info("[auth] authDetailVo={}", JSON.toJSONString(authDetailVo));
        ConsultAuthDto authDto = webMgkConsultPageService.decryptAuthReqAndVerifySign(authDetailVo.getSourceCode() ,authDetailVo.getAuthRequestInfo() ,authDetailVo.getSignature());
        webMgkConsultPageService.verifyToken(authDto);
        webMgkConsultPageService.insertCustomerServiceAuthTokenPage(authDto , authDetailVo.getSourceCode());
        return Response.SUCCESS(null);
    }


    @ApiOperation(value = "根据mid查询用户信息")
    @RequestMapping(value = "/query_bili_user_info", method = RequestMethod.GET)
    @ResponseBody
    public OpenApiResponse<List<BiliUserInfoVo>> queryBiliUserInfo(@ApiIgnore Context context,
                                                            @ApiParam("b站uid") @RequestParam("mid") String mid) {
        List<Long> midList = Arrays.stream(mid.split(",")).map(Long::parseLong).collect(Collectors.toList());
        Map<Long, UserInfo> basicUserInfoMap = biliAccountService.findBasicUserInfo(midList, Boolean.FALSE);
        List<BiliUserInfoVo> result = new ArrayList<>();
        for (Long singleMid : midList) {
            UserInfo userInfo = basicUserInfoMap.get(singleMid);
            long queryMid = userInfo.getBaseInfo().getMid();
            if (queryMid == 0L) {
                BiliUserInfoVo biliUserInfoVo = new BiliUserInfoVo();
                biliUserInfoVo.setUid(String.valueOf(singleMid));
                biliUserInfoVo.setExist(Boolean.FALSE);
                biliUserInfoVo.setFace("");
                biliUserInfoVo.setName("");
                result.add(biliUserInfoVo);
            } else {
                BiliUserInfoVo biliUserInfoVo = new BiliUserInfoVo();
                biliUserInfoVo.setUid(String.valueOf(singleMid));
                biliUserInfoVo.setExist(Boolean.TRUE);
                biliUserInfoVo.setFace(userInfo.getBaseInfo().getFace());
                biliUserInfoVo.setName(userInfo.getBaseInfo().getName());
                result.add(biliUserInfoVo);
            }
        }

        return OpenApiResponse.SUCCESS(result);
    }


    @ApiOperation(value = "消息推送ws服务")
    @RequestMapping(value = "/message/push", method = RequestMethod.POST)
    @ResponseBody
    public Response<Object> pushMessage(@ApiIgnore Context context,
                                        @RequestBody ThirdPartMessagePushVo thirdPartMessagePushVo
                                 ) throws ServiceException {
        MessageDto messageDto = MessageDto.builder()
                .sourceCode(thirdPartMessagePushVo.getSource_code())
                .chatId(thirdPartMessagePushVo.getChat_id())
                .thirdChatId(thirdPartMessagePushVo.getThird_chat_id())
                .msgContent(thirdPartMessagePushVo.getMsg_content())
                .msgId(thirdPartMessagePushVo.getMsg_id())
                .msgStyle(thirdPartMessagePushVo.getMsg_style())
                .msgType(thirdPartMessagePushVo.getMsg_type())
                .timeStamp(thirdPartMessagePushVo.getTime_stamp())
                .build();
        Assert.isTrue(!thirdPartMessagePushVo.getThird_chat_id().isEmpty(), "thirdChatId缺失");
        Assert.isTrue(!thirdPartMessagePushVo.getSignature().isEmpty(), "signature缺失");
        webMgkConsultPageService.checkSigniture(messageDto, thirdPartMessagePushVo.getSignature());
        webMgkConsultPageService.pushMessage(messageDto);
        return Response.SUCCESS(null);
    }

    @ApiOperation("上传聊天图片")
    @PostMapping(value = "/upload/image")
    @ResponseBody
    public OpenApiResponse<String> uploadImage(@ApiIgnore Context ctx,
                                                  @ApiParam("聊天ID") @RequestParam(value = "chat_id", defaultValue = "") String chatId,
                                                  @ApiParam("上传文件") @RequestParam("file") MultipartFile file) throws ServiceException {
        // 校验chat是否存活
        RBucket<String> bucket = redissonClient.getBucket(AbstractMgkConsultChatServiceImpl.CHAT_ID_PREFIX + chatId);
        if (!bucket.isExists()) {
            throw new ServiceException(MgkWebExceptionCode.CHAT_NOT_EXIST);
        }
        try {
            final byte[] bytes = file.getBytes();
            Assert.isTrue(bytes.length < MAX_QUALIFICATION_BYTES, "图片大小不能超过5M");
            String url = bossUtils.uploadByte(bossPath, getFileName(chatId , file), bytes);
            return OpenApiResponse.SUCCESS(url);
        } catch (IOException e) {
            log.error("upload failed", e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 查询用户咨询页一键留资组件信息
     * @param context
     * @param
     * @return
     * @throws ServiceException
     */
    @ApiOperation(value = "查询用户咨询页一键留资组件信息")
    @RequestMapping(value = "/query_asset_info", method = RequestMethod.GET)
    @ResponseBody
    public OpenApiResponse<UserAssetVo> getUserAssetInfo(
            @ApiIgnore Context context,
            @ApiParam("b站uid") @RequestParam("mid") String mid) throws ServiceException {
        String decodeMid = new String(Base64.decodeBase64(mid), StandardCharsets.UTF_8);
        UserAssetDto userAssetDto = webMgkConsultPageService.getUserAssetInfo(decodeMid);
        log.info("[query_asset_info] userAssetDto={}", JSON.toJSONString(userAssetDto));

        return OpenApiResponse.SUCCESS(webMgkConsultPageService.convertUserAssetDto2Vo(userAssetDto));
    }

    private String getFileName(String chatId , MultipartFile file){
        return  chatId + "_" +System.currentTimeMillis() + "_" +  file.getOriginalFilename();
    }

}
