package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/14
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LayerVo {

    @ApiModelProperty("图层id")
    private Integer id;

    @ApiModelProperty("图层命名")
    private String name;

    @ApiModelProperty("图层种类")
    private Integer category;

    @ApiModelProperty("图层种类描述")
    private String category_desc;

    @ApiModelProperty("图层基础类型")
    private Integer type;

    @ApiModelProperty("图层width")
    private Integer width;

    @ApiModelProperty("图层height")
    private Integer height;

    @ApiModelProperty("x坐标")
    private Integer x;

    @ApiModelProperty("y坐标")
    private Integer y;

    @ApiModelProperty("旋转角度（顺时针方向为正）")
    private Integer rotate;

    @ApiModelProperty("透明度")
    private Float opacity;

    @ApiModelProperty("顺序号")
    private Integer seq;

    @ApiModelProperty("图层颜色")
    private String bg_color;

    @ApiModelProperty("图片图层--图片url")
    private String image_url;

    @ApiModelProperty("图片图层--图片宽高锁定")
    private Integer image_lock;

    @ApiModelProperty("文本图层--文本")
    private String text;

    @ApiModelProperty("文本图层--字体库id")
    private Integer font_family_id;

    @ApiModelProperty("文本图层--字体库名称")
    private String font_family_name;

    @ApiModelProperty("文本图层--字体库url")
    private String font_family_url;

    @ApiModelProperty("文本图层--文本大小")
    private Integer font_size;

    @ApiModelProperty("文本图层--文本样式")
    private String font_style;

    @ApiModelProperty("文本图层--文字颜色")
    private String text_color;

    @ApiModelProperty("文本图层--对齐")
    private String text_align;

    @ApiModelProperty("文本图层--粗细")
    private String font_weight;

    @ApiModelProperty("文本图层--下划线")
    private Integer underline;

    @ApiModelProperty("文本图层--中划线")
    private Integer linethrough;

    @ApiModelProperty(value = "文本垂直对齐 0-居中 1-上 2-下")
    private Integer text_cross;

    @ApiModelProperty(value = "0-横排文本 1-竖排文本")
    private Integer text_orient;

    @ApiModelProperty(value = "阴影x")
    private Integer shadow_x;

    @ApiModelProperty(value = "阴影y")
    private Integer shadow_y;

    @ApiModelProperty(value = "阴影描边")
    private Integer shadow_blur;

    @ApiModelProperty(value = "阴影宽度")
    private Integer shadow_width;

    @ApiModelProperty(value = "阴影颜色")
    private String shadow_color;

    @ApiModelProperty(value = "描边")
    private List<StrokeVo> stroke;

}
