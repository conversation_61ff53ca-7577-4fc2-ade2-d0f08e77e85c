package com.biz.common.doc.tree.util;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.vavr.control.Try;
import java.net.InetSocketAddress;
import java.net.Proxy;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.Retrofit.Builder;
import retrofit2.converter.jackson.JacksonConverterFactory;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/9
 */
@Slf4j
public class DocHttpApiUtil {


    private static final String DISCOVERY_PREFIX = "discovery://";

    private static final String HTTP_PREFIX = "http://";

//
//    /**
//     * @param httpBaseUrl
//     * @param httpDiscoveryZone
//     * @param sock5ProxyEnabled 注意下socket参数仅用于本地调试，生产是false
//     * @param sock5Host
//     * @param sock5Port
//     * @param <T>
//     * @return
//     */
//
//    public static <T> T buildApiViaDiscovery(
//            Class<T> apiClass,
//            String httpBaseUrl,
//            String httpDiscoveryZone,
//            Boolean sock5ProxyEnabled,
//            String sock5Host,
//            Integer sock5Port, Interceptor... otherInterceptors) {
//
//        ObjectMapper om = new ObjectMapper()
//                .registerModule(new JavaTimeModule())
//                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
//                .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
//                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
//                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
//                .setSerializationInclusion(Include.NON_NULL);
//
//        OkHttpClient.Builder client = new OkHttpClient.Builder()
//                .followRedirects(true);
//
//        if (sock5ProxyEnabled) {
//
//            client.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(
//                    sock5Host, sock5Port
//            ))).followRedirects(true);
//        }
//
//        HttpUrl baseUrl;
//        if (httpBaseUrl.startsWith(DISCOVERY_PREFIX)) {
//            // fix http prefix
//            String url = httpBaseUrl.replaceFirst(DISCOVERY_PREFIX, HTTP_PREFIX);
//            baseUrl = HttpUrl.get(url);
//            NamingResolver resolver = NamingClientRegistry.namingClient()
//                    .resolveForReady(baseUrl.host(), Namings.Scheme.HTTP, null);
//            client.addInterceptor(new HttpClientDiscoveryInterceptor(EnvUtils.getDiscoveryZone(
//                    httpDiscoveryZone
//            ), resolver));
//        } else {
//            baseUrl = HttpUrl.get(httpBaseUrl);
//        }
//
//        // Custom interceptor to handle 307 redirects
//        Interceptor redirectInterceptor = new Interceptor() {
//            @Override
//            public okhttp3.Response intercept(Chain chain) throws IOException {
//                Request request = chain.request();
//                okhttp3.Response response = chain.proceed(request);
//
//                if (response.code() == 307) {
//                    // Get the new URL from the Location header
//                    String newUrl = response.header("Location");
//
//                    // Create a new request with the new URL
//                    if (newUrl != null) {
//                        Request newRequest = request.newBuilder()
//                                .url(newUrl)
//                                .build();
//                        response.close();  // Close the original response
//                        return chain.proceed(newRequest);  // Execute the new request
//                    }
//                }
//
//                return response;
//            }
//        };
//
//        client.addInterceptor(redirectInterceptor);
//        client.addInterceptor(new HttpMetricInterceptor());
//
//        for (Interceptor interceptor : otherInterceptors) {
//            client.addInterceptor(interceptor);
//        }
//
//        Retrofit retrofit = new Builder()
//                .baseUrl(baseUrl)
//                .client(client.build())
//                .addConverterFactory(
//                        JacksonConverterFactory.create(om)
//                )
//                .build();
//
//        T httpApi = retrofit.create(apiClass);
//
//        return httpApi;
//
//
//    }


    public static <T> T buildApi(
            Class<T> apiClass,
            String httpBaseUrl,
            Boolean sock5ProxyEnabled,
            String sock5Host,
            Integer sock5Port) {

        ObjectMapper om = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                .setSerializationInclusion(Include.NON_NULL);

        OkHttpClient.Builder client = new OkHttpClient.Builder()
                .followRedirects(true);

        if (sock5ProxyEnabled) {

            client.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(
                    sock5Host, sock5Port
            ))).followRedirects(true);
        }

        client.addInterceptor(new DocHttpMetricInterceptor());

        Retrofit retrofit = new Builder()
                .baseUrl(httpBaseUrl)
                .client(client.build())
                .addConverterFactory(
                        JacksonConverterFactory.create(om)
                )
                .build();

        T httpApi = retrofit.create(apiClass);

        return httpApi;


    }


    public static <T> T call(Call<T> call) {

        try {
            Response<T> r = call.execute();

            if (!r.isSuccessful()) {
                String message = Try.of(() -> {
                    try (ResponseBody errorBody = r.errorBody()) {
                        return errorBody.string();
                    }
                }).getOrElse("");

                throw new RuntimeException(
                        "调用http服务失败:" + message + ", code:" + r.code());
            }

            T data = r.body();

            if (data == null) {
                throw new RuntimeException("调用http服务失败:结果为空");
            }

            return data;

        } catch (Exception e) {
            log.error("Fail to call http service, ", e);

            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            throw new RuntimeException("调用http服务失败:" + e.getMessage(), e);
        }

    }

}
