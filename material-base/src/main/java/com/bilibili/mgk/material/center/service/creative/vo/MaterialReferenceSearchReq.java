package com.bilibili.mgk.material.center.service.creative.vo;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/14
 */
@Data
@Accessors(chain = true)
public class MaterialReferenceSearchReq implements SnakeCaseBody {

    private String name;

    private List<String> materialIds;


    private String searchWord;

    private List<String> accountIds;

    private Integer pn;


    private Integer ps;



}
