/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.mgk.platform.portal.webapi.archive.bo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UgcV2Bo {
    private Long mid;
    private String avid;
    private String cid;
    private String title;
    private List<String> tags;
    private Integer catOne;
    private Integer catTwo;
}
