package com.bilibili.mgk.platform.common.utils.excel;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class ExcelReadUtil {

    private static final int NUMERIC = 0;
    private static final int STRING = 1;
    private static final int FORMULA = 2;
//    private static final int BLANK = 3;
    private static final int BOOLEAN = 4;
    private static final int ERROR = 5;

    /**
     * 1、将单元格的内容转换为字符串
     *
     * @param cell 单元格
     * @return 返回转换后的字符串
     */
    private static String convertCellValueToString(CellValue cell) {
        //1.1、判断单元格的数据是否为空
        if (cell == null) {
            return null;
        }
        //1.2、设置单元格数据的初始值
        String cellValue = null;
        //1.3、获取单元格数据的类型
        switch (cell.getCellType()) {
            case NUMERIC:
                //1.3.2、处理普通数字格式
                DecimalFormat format = new DecimalFormat("0");
                double numericCellValue = cell.getNumberValue();
                cellValue = format.format(numericCellValue);
                break;
            case STRING:
                //处理字符串类型
                cellValue = cell.getStringValue();
                break;
            case BOOLEAN:
                //处理布尔类型
                boolean booleanCellValue = cell.getBooleanValue();
                cellValue = Boolean.toString(booleanCellValue);
                break;
            case ERROR:
                byte errorCellValue = cell.getErrorValue();
                cellValue = Byte.toString(errorCellValue);
                break;
            default:
                break;
        }
        return cellValue;
    }

    /**
     * 2、处理合并单元格里面的数据
     *
     * @param sheet 工作薄
     * @return 返回合并单元格后里面的数据
     */
    public static List<CellRangeAddress> getCombineCell(Sheet sheet) {
        List<CellRangeAddress> list = new ArrayList<>();
        //2.1、获得一个 sheet 中合并单元格的数量
        int sheetMergerCount = sheet.getNumMergedRegions();
        //2.2、遍历合并单元格
        for (int i = 0; i < sheetMergerCount; i++) {
            //2.2.1、获得合并单元格加入list中
            CellRangeAddress rangeAddress = sheet.getMergedRegion(i);
            list.add(rangeAddress);
        }
        return list;
    }

    /**
     * 3、判断单元格是否为合并单元格
     *
     * @param listCombineCell 存放合并单元格的list
     * @param cell            需要判断的单元格
     * @param sheet           sheet
     */
//    public static String isCombineCell(List<CellRangeAddress> listCombineCell, Cell cell, Sheet sheet, CellValue cellValue) {
//        //3.1、设置第一个单元格和最后一个单元格的值
//        int firstColumn;
//        int lastColumn;
//        //3.2、设置第一个单元格和最后一个行的值
//        int firstRow;
//        int lastRow;
//        //3.3、初始化单元格值
//        String value = null;
//        for (CellRangeAddress rangeAddress : listCombineCell) {
//            //3.3.1、获得合并单元格的起始行, 结束行, 起始列, 结束列
//            firstColumn = rangeAddress.getFirstColumn();
//            lastColumn = rangeAddress.getLastColumn();
//            firstRow = rangeAddress.getFirstRow();
//            lastRow = rangeAddress.getLastRow();
//            //3.3.2、判断是不是合并单元格
//            if (cell.getRowIndex() >= firstRow && cell.getRowIndex() <= lastRow) {
//                if (cell.getColumnIndex() >= firstColumn && cell.getColumnIndex() <= lastColumn) {
//                    //*******、获取行数据
//                    Row fRow = sheet.getRow(firstRow);
//                    //*******、获取单元格数据
//                    Cell fCell = fRow.getCell(firstColumn);
//                    //*******、对有合并单元格的数据进行格式处理
//                    value = convertCellValueToString(fCell);
//                    break;
//                }
//            } else {
//                //3.3.3、对没有合并单元格的数据进行格式处理
//                value = convertCellValueToString(cell);
//            }
//        }
//        //3.4、返回处理后的单元格数据
//        return value;
//    }

    /**
     * 4、判断sheet页中是否有合并单元格
     *
     * @param sheet sheet
     * @return 返回值
     */
    private static boolean hasMerged(Sheet sheet) {
        int numMergedRegions = sheet.getNumMergedRegions();
        return numMergedRegions > 0;
    }

    /**
     * 5、读取excel文件内容
     *
     * @param file 文件
     * @return 返回值
     */
    public static List<Object[]> readExcel(File file) throws FileNotFoundException {
        InputStream inputStream = new FileInputStream(file);
        //5.1、定义一个集合用来存储Object数据
        List<Object[]> list = new ArrayList<>();
        try {
            //5.2、创建工作薄
            Workbook workbook = WorkbookFactory.create(inputStream);
            //5.3、获取工作薄里面sheet的个数
            int sheetNum = workbook.getNumberOfSheets();
            //5.4、遍历每一个sheet
            for (int i = 0; i < sheetNum; i++) {
                readSheet(workbook, i, list);
            }
            log.info("导入文件解析成功！");
        } catch (Exception e) {
            log.info("导入文件解析失败！");
            e.printStackTrace();
            return null;
        };
        //5.5、返回List集合
        return list;
    }

    /**
     * 6、读取sheet的excel文件内容
     *
     * @param file 文件
     * @param sheetIndex 页码
     * @return 返回值
     */
    public static List<Object[]> readExcelBySheetIndex(File file, int sheetIndex) throws FileNotFoundException {
        InputStream inputStream = new FileInputStream(file);
        //6.1、定义一个集合用来存储Object数据
        List<Object[]> list = new ArrayList<>();
        try {
            //6.2、创建工作薄
            Workbook workbook = WorkbookFactory.create(inputStream);
            //6.3、获取工作薄里面sheet的个数
            int sheetNum = workbook.getNumberOfSheets();
            Assert.isTrue(sheetNum >= sheetIndex+1, "out of sheet index");
            //6.4、读取sheet内容
            readSheet(workbook, sheetIndex, list);

            log.info("导入文件解析成功！");
        } catch (Exception e) {
            log.info("导入文件解析失败！");
            e.printStackTrace();
            return null;
        };
        //5.5、返回List集合
        return list;
    }

    private static void readSheet(Workbook workbook, int sheetIndex, List<Object[]> list){
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
        //5.4.1、获取sheet中有数据的行数
        int rows = sheet.getPhysicalNumberOfRows();
        for (int j = 0; j < rows; j++) {
            //5.4.1.1、过滤掉文件的表头（视文件表头情况而定）
//            if (j == 0) {
//                continue;
//            }
            //5.4.1.2、获取每一行的数据
            Row row = sheet.getRow(j);
            if (row == null) {
                System.out.println("row is null");
            } else {
                //5.4.1.3、得到每一行中有效单元格的数据
                int cells = row.getPhysicalNumberOfCells();
                //5.4.1.4、定义一个Object数组用来存储读取单元格的数据
                Object[] objects = new Object[cells];
                //5.4.1.5、初始化对象数组的下标
                int index = 0;
                //*******、遍历每一个有效的单元格数据
                for (int k = 0; k < cells; k++) {
                    //*******.1、获取每一个单元格的数据
                    Cell cell = row.getCell(k);
                    CellValue cellValue = evaluator.evaluate(cell);
                    //*******.2、判断当前sheet页是否合并有单元格
//                    boolean b = hasMerged(sheet);
//                    if (b) {
//                        //*******.2.1、判断当前单元格是不是合并单元格，如果是则输出合并单元格的数据，不是则直接输出
//                        List<CellRangeAddress> listCombineCell = getCombineCell(sheet);
//                        String combineCell = isCombineCell(listCombineCell, cell, sheet);
//                        //*******.2.1.2、对单元格的数据进行处理
//                        objects[index] = combineCell;
//                    } else {
                    String cellValueToString = convertCellValueToString(cellValue);
                    objects[index] = cellValueToString;
//                    }
                    //*******.3、下标累加
                    index++;
                }
                //*******、将对象数组里面的数据添加到list集合中去
                list.add(objects);
            }
        }
    }

}
