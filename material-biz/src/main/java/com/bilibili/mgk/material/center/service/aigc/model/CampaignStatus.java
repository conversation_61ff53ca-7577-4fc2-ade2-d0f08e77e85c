package com.bilibili.mgk.material.center.service.aigc.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/30
 */
@Getter
@RequiredArgsConstructor
public enum CampaignStatus {


    effective("投放中"),


    ineffective("非投放中"),

    ;


    private final String desc;


    public static CampaignStatus fromPandoraCode(Integer code) {

        if (code == null) {
            return ineffective;
        }

        if (code == 1) {
            return effective;
        } else {
            return ineffective;
        }

    }


}
