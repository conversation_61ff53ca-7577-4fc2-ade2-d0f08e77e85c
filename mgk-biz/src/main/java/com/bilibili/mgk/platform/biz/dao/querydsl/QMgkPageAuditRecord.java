package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkPageAuditRecordQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkPageAuditRecord is a Querydsl query type for MgkPageAuditRecordQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkPageAuditRecord extends com.querydsl.sql.RelationalPathBase<MgkPageAuditRecordQueryDSLPo> {

    private static final long serialVersionUID = -**********;

    public static final QMgkPageAuditRecord mgkPageAuditRecord = new QMgkPageAuditRecord("mgk_page_audit_record");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final StringPath auditorName = createString("auditorName");

    public final DateTimePath<java.sql.Timestamp> auditTime = createDateTime("auditTime", java.sql.Timestamp.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> operateType = createNumber("operateType", Integer.class);

    public final StringPath pageContent = createString("pageContent");

    public final NumberPath<Long> pageId = createNumber("pageId", Long.class);

    public final StringPath reason = createString("reason");

    public final DateTimePath<java.sql.Timestamp> sendAuditTime = createDateTime("sendAuditTime", java.sql.Timestamp.class);

    public final NumberPath<Long> workOrderId = createNumber("workOrderId", Long.class);

    public final com.querydsl.sql.PrimaryKey<MgkPageAuditRecordQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkPageAuditRecord(String variable) {
        super(MgkPageAuditRecordQueryDSLPo.class, forVariable(variable), "null", "mgk_page_audit_record");
        addMetadata();
    }

    public QMgkPageAuditRecord(String variable, String schema, String table) {
        super(MgkPageAuditRecordQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkPageAuditRecord(String variable, String schema) {
        super(MgkPageAuditRecordQueryDSLPo.class, forVariable(variable), schema, "mgk_page_audit_record");
        addMetadata();
    }

    public QMgkPageAuditRecord(Path<? extends MgkPageAuditRecordQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_page_audit_record");
        addMetadata();
    }

    public QMgkPageAuditRecord(PathMetadata metadata) {
        super(MgkPageAuditRecordQueryDSLPo.class, metadata, "null", "mgk_page_audit_record");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(auditorName, ColumnMetadata.named("auditor_name").withIndex(5).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(auditTime, ColumnMetadata.named("audit_time").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(9).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(11).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(10).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(operateType, ColumnMetadata.named("operate_type").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(pageContent, ColumnMetadata.named("page_content").withIndex(8).ofType(Types.LONGVARCHAR).withSize(65535).notNull());
        addMetadata(pageId, ColumnMetadata.named("page_id").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(reason, ColumnMetadata.named("reason").withIndex(12).ofType(Types.VARCHAR).withSize(255).notNull());
        addMetadata(sendAuditTime, ColumnMetadata.named("send_audit_time").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(workOrderId, ColumnMetadata.named("work_order_id").withIndex(13).ofType(Types.BIGINT).withSize(20).notNull());
    }

}

