package com.bilibili.mgk.platform.biz.mongo.pojo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.persistence.Id;


/**
 * @file: MongoLandPageConfigPo
 * @author: gaoming
 * @date: 2021/03/02
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "mgk_landing_page_config")
public class MongoLandPageConfigPo {
    @Id()
    private Long pageId;

    @Field("config")
    private Object config;

    @Field("accountId")
    private Integer accountId;

}
