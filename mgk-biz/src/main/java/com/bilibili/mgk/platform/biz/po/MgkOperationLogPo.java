package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class MgkOperationLogPo implements Serializable {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 对象ID
     */
    private Long objId;

    /**
     * 操作对象类型: 1-站点
     */
    private Integer objFlag;

    /**
     * 操作类型:1-新建 2-删除 3-修改 4-复制 5-发布 6-下线 7-管理员驳回
     */
    private Integer operateType;

    /**
     * 操作人
     */
    private String operatorUsername;

    /**
     * 操作人类型: 0-广告主 1-运营 2-系统 4-代理商 5-代理商的系统管理员 6-代理商的投放管理员 7-二级代理运营人员 100-内部LDAP登陆
     */
    private Integer operatorType;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public Integer getObjFlag() {
        return objFlag;
    }

    public void setObjFlag(Integer objFlag) {
        this.objFlag = objFlag;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getOperatorUsername() {
        return operatorUsername;
    }

    public void setOperatorUsername(String operatorUsername) {
        this.operatorUsername = operatorUsername;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }
}