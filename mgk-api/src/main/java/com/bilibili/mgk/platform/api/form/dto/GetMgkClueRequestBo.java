package com.bilibili.mgk.platform.api.form.dto;

import com.bilibili.mgk.platform.api.common.PageBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetMgkClueRequestBo {
    private Integer advertiserId;
    private PageBo pageBo;
    private Timestamp startTs;
    private Timestamp endTs;
}
