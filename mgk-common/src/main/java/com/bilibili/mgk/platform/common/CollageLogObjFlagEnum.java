package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/09/10
 **/
@AllArgsConstructor
public enum CollageLogObjFlagEnum {
    COLLAGE_PATTERN(1, "贴图模板"),
    COLLAGE_WORKS(2, "贴图作品"),
    COLLAGE_MEDIA(3, "贴图媒体");

    @Getter
    private Integer code;

    @Getter
    private String desc;

    public static CollageLogObjFlagEnum getByCode(int code) {
        for (CollageLogObjFlagEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code CollageLogObjFlagEnum " + code);
    }

}
