package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.LauThirdPartyLandingPageComponentQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauThirdPartyLandingPageComponent is a Querydsl query type for LauThirdPartyLandingPageComponentQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauThirdPartyLandingPageComponent extends com.querydsl.sql.RelationalPathBase<LauThirdPartyLandingPageComponentQueryDSLPo> {

    private static final long serialVersionUID = -**********;

    public static final QLauThirdPartyLandingPageComponent lauThirdPartyLandingPageComponent = new QLauThirdPartyLandingPageComponent("lau_third_party_landing_page_component");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final NumberPath<Long> bizId = createNumber("bizId", Long.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Byte> isDeleted = createNumber("isDeleted", Byte.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final StringPath name = createString("name");

    public final NumberPath<Byte> status = createNumber("status", Byte.class);

    public final NumberPath<Long> uid = createNumber("uid", Long.class);

    public final StringPath url = createString("url");

    public final com.querydsl.sql.PrimaryKey<LauThirdPartyLandingPageComponentQueryDSLPo> primary = createPrimaryKey(id);

    public QLauThirdPartyLandingPageComponent(String variable) {
        super(LauThirdPartyLandingPageComponentQueryDSLPo.class, forVariable(variable), "null", "lau_third_party_landing_page_component");
        addMetadata();
    }

    public QLauThirdPartyLandingPageComponent(String variable, String schema, String table) {
        super(LauThirdPartyLandingPageComponentQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauThirdPartyLandingPageComponent(String variable, String schema) {
        super(LauThirdPartyLandingPageComponentQueryDSLPo.class, forVariable(variable), schema, "lau_third_party_landing_page_component");
        addMetadata();
    }

    public QLauThirdPartyLandingPageComponent(Path<? extends LauThirdPartyLandingPageComponentQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_third_party_landing_page_component");
        addMetadata();
    }

    public QLauThirdPartyLandingPageComponent(PathMetadata metadata) {
        super(LauThirdPartyLandingPageComponentQueryDSLPo.class, metadata, "null", "lau_third_party_landing_page_component");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(4).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(bizId, ColumnMetadata.named("biz_id").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(8).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(10).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(9).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(name, ColumnMetadata.named("name").withIndex(5).ofType(Types.VARCHAR).withSize(255).notNull());
        addMetadata(status, ColumnMetadata.named("status").withIndex(7).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(uid, ColumnMetadata.named("uid").withIndex(3).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(url, ColumnMetadata.named("url").withIndex(6).ofType(Types.VARCHAR).withSize(1024).notNull());
    }

}

