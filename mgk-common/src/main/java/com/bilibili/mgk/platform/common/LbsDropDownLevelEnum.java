package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 */

@AllArgsConstructor
public enum LbsDropDownLevelEnum {

    PRODUCT(1, "产品类目"),

    PROVINCE(2, "门店所在省份"),

    CITY(3, "门店所在市"),

    SHOP_NAME(4, "门店名称"),

    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static LbsDropDownLevelEnum getByCode(int code) {
        for (LbsDropDownLevelEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code LbsDropDownLevelEnum " + code);
    }

}
