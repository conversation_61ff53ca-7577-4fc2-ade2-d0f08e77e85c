package com.biz.common.doc.tree.repository.redis;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/5/19
 */
public class DocContentRedisCacheTest {


    //    String before = "<h1 class=\"paragraph text-align-type-left MsoNormal 😁";
    String before = "# 查看素材分析报表\n\n### 接口信息\\n- **请求地址**：`https://cm.bilibili.com/takumi/api/open_api/report/v3/material`";

    public static String compressString(String data) throws IOException {
        if (data == null || data.isEmpty()) {
            return data;
        }

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
                GZIPOutputStream gzipOS = new GZIPOutputStream(bos)) {

            gzipOS.write(data.getBytes(StandardCharsets.UTF_8));
            gzipOS.finish();
            return Base64.getEncoder().encodeToString(bos.toByteArray());
        }
    }

    // 解压Base64字符串 → 原始字符串
    public static String decompressString(String compressedData) throws IOException {
        if (compressedData == null || compressedData.isEmpty()) {
            return compressedData;
        }

        byte[] decodedBytes = Base64.getDecoder().decode(compressedData);
        try (ByteArrayInputStream bis = new ByteArrayInputStream(decodedBytes);
                GZIPInputStream gzipIS = new GZIPInputStream(bis);
                ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipIS.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toString(StandardCharsets.UTF_8.displayName());
        }
    }

    public static String compressStringV2(String data) throws IOException {
        if (data == null || data.isEmpty()) {
            return data;
        }

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
                GZIPOutputStream gzipOS = new GZIPOutputStream(bos)) {

            gzipOS.write(data.getBytes(StandardCharsets.UTF_8));
            gzipOS.finish();
            return bos.toString("ISO-8859-1");
        }
    }

    // 解压Base64字符串 → 原始字符串
    public static String decompressStringV2(String compressedData) throws IOException {
        if (compressedData == null || compressedData.isEmpty()) {
            return compressedData;
        }

        byte[] decodedBytes = compressedData.getBytes("ISO-8859-1");
        try (ByteArrayInputStream bis = new ByteArrayInputStream(decodedBytes);
                GZIPInputStream gzipIS = new GZIPInputStream(bis);
                ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipIS.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toString(StandardCharsets.UTF_8.displayName());
        }
    }

    // 压缩字符串
    public static String compressStringV3(String data) throws IOException {
        if (data == null || data.isEmpty()) {
            return data;
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            gzipOutputStream.write(data.getBytes("UTF-8"));
        }
        return byteArrayOutputStream.toString("ISO-8859-1");
    }

    // 解压缩字符串
    public static String decompressStringV3(String compressedData) throws IOException {
        if (compressedData == null || compressedData.isEmpty()) {
            return compressedData;
        }
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(compressedData.getBytes("ISO-8859-1"));
        try (GZIPInputStream gzipInputStream = new GZIPInputStream(byteArrayInputStream);
                InputStreamReader inputStreamReader = new InputStreamReader(gzipInputStream, "UTF-8");
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {

            StringBuilder output = new StringBuilder();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                output.append(line);
            }
            return output.toString();
        }
    }

    @Test
    public void test() throws IOException {

        System.out.println(before);

        System.out.println("before size =" + before.length());

        String after = compressString(before);

        System.out.println("after size =" + after.length());

        String after2 = decompressString(after);

        System.out.println("decompress size =" + after2.length());
        System.out.println(after2);

    }

    @Test
    public void test2() throws IOException {

        System.out.println(before);

        System.out.println("before size =" + before.length());

        String after = compressStringV2(before);

        System.out.println("after size =" + after.length());
        System.out.println("compressed data = " + after);

        String after2 = decompressStringV2(after);

        System.out.println("decompress size =" + after2.length());
        System.out.println(after2);

    }

    @Test
    public void test3() throws IOException {

        String nbefore = "";

        for (int i = 0; i < 1000; i++) {
            nbefore += before;
        }

        System.out.println(before);

        System.out.println("before size =" + nbefore.length());

        String after = compressStringV2(nbefore);

        System.out.println("after size =" + after.length());
        System.out.println("compressed data = " + after);

        String after2 = decompressStringV2(after);

        System.out.println("decompress size =" + after2.length());
        System.out.println(after2);

    }
}