//package com.bilibili.mgk.platform.biz.repositories;
//
//import com.bilibili.adp.common.util.Page;
//import com.bilibili.adp.common.util.Utils;
//import com.bilibili.mgk.platform.api.model.dto.ESModelDto;
//import com.bilibili.mgk.platform.api.model.dto.ESModelResultDto;
//import com.bilibili.mgk.platform.api.model.dto.QueryModelParamDto;
//import com.bilibili.mgk.platform.biz.BaseMockitoTest;
//import com.bilibili.mgk.platform.biz.es.ESMgkModelPo;
//import com.bilibili.mgk.platform.biz.utils.ESUtil;
//import com.bilibili.mgk.platform.common.MgkConstants;
//import com.google.common.collect.Lists;
//import org.elasticsearch.index.query.BoolQueryBuilder;
//import org.elasticsearch.index.query.QueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.junit.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.stubbing.Answer;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.elasticsearch.core.aggregation.impl.AggregatedPageImpl;
//import org.springframework.util.CollectionUtils;
//
//import java.util.*;
//
//import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
//import static org.junit.Assert.*;
//import static org.mockito.Matchers.any;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
///**
// * <AUTHOR>
// * @date 2020/08/17
// **/
//public class ESMgkModelServiceImplTest extends BaseMockitoTest {
//    @InjectMocks
//    private ESMgkModelServiceImpl esMgkModelService;
//
//    @Mock
//    private MgkModelRepository modelRepository;
//
//    @Mock
//    private ESUtil esUtil;
//
//    public void setUp() throws Exception {
//    }
//
//    @Test
//    public void testInsert() {
//
//
//        ESModelDto esModelDto = ESModelDto.builder()
//                .pageId(1L)
//                .modelType(1)
//                .accountId(1)
//                .tradeIds(Lists.newArrayList(1L))
//                .modelStyle(1)
//                .modelStatus(1)
//                .modelId(1L)
//                .modelName("模型名称")
//                .build();
//
//        esMgkModelService.insert(esModelDto);
//    }
//
//    @Test
//    public void testGetModels() {
//
//        QueryModelParamDto paramDto = QueryModelParamDto.builder()
//                .modelTypes(Lists.newArrayList(1))
//                .modelStyles(Lists.newArrayList(1))
//                .nameLike("nameLike")
//                .statusList(Lists.newArrayList(1))
//                .tradeIds(Lists.newArrayList(1L))
//                .orderBy("mtime desc")
//                .pageIds(Lists.newArrayList(1L))
//                .modelIds(Lists.newArrayList(1L))
//                .isDeleted(1)
//                .accountIds(Lists.newArrayList(1))
//                .build();
//
//        BoolQueryBuilder boolQueryBuilder = mock(BoolQueryBuilder.class);
//        when(esUtil.batchTermsQuery(any(), any())).thenReturn(boolQueryBuilder);
//        when(esUtil.queryForPage(any(), any())).thenReturn(new AggregatedPageImpl<>(Lists.newArrayList(ESMgkModelPo.builder()
//                .tradeIds(Lists.newArrayList(1L))
//                .pageId(1L)
//                .modelType(1)
//                .modelStyle(1)
//                .modelStatus(1)
//                .modelName("name")
//                .modelId(1L)
//                .Id(1L)
//                .build())));
//        ESModelResultDto resultDto = esMgkModelService.getModels(paramDto, Page.valueOf(1, 15));
//
//        assertNotNull(resultDto);
//        assertTrue(Utils.isPositive(resultDto.getTotal()));
//        assertTrue(Utils.isPositive(resultDto.getModelIds().size()));
//    }
//
//    @Test
//    public void testTestGetModels() {
//        QueryModelParamDto paramDto = QueryModelParamDto.builder()
//                .modelTypes(Lists.newArrayList(1))
//                .modelStyles(Lists.newArrayList(1))
//                .nameLike("nameLike")
//                .statusList(Lists.newArrayList(1))
//                .tradeIds(Lists.newArrayList(1L))
//                .orderBy("mtime desc")
//                .pageIds(Lists.newArrayList(1L))
//                .modelIds(Lists.newArrayList(1L))
//                .isDeleted(1)
//                .accountIds(Lists.newArrayList(1))
//                .build();
//
//        BoolQueryBuilder boolQueryBuilder = mock(BoolQueryBuilder.class);
//        when(esUtil.batchTermsQuery(any(), any())).thenReturn(boolQueryBuilder);
//        Iterable<ESMgkModelPo> iterator = Arrays.asList(ESMgkModelPo.builder()
//                .Id(1L)
//                .modelId(1L)
//                .modelName("modelName")
//                .modelStatus(1)
//                .modelStyle(1)
//                .modelType(1)
//                .pageId(1L)
//                .tradeIds(Lists.newArrayList(1L))
//                .build());
//        when(modelRepository.search((QueryBuilder) any())).thenReturn(iterator);
//        List<Long> models = esMgkModelService.getModels(paramDto);
//        assertNotNull(models);
//
//    }
//
//    @Test
//    public void testGetModelById() {
//        when(modelRepository.findById(1L)).thenReturn(Optional.of(ESMgkModelPo.builder()
//                .tradeIds(Lists.newArrayList(1L))
//                .pageId(1L)
//                .modelType(1)
//                .modelStyle(1)
//                .modelStatus(1)
//                .modelName("modelName")
//                .modelId(1L)
//                .Id(1L)
//                .build()));
//        ESModelDto modelById = esMgkModelService.getModelById(1L);
//        assertNotNull(modelById);
//    }
//
//    @Test
//    public void testGetModelsByIds() {
//        BoolQueryBuilder boolQueryBuilder = mock(BoolQueryBuilder.class);
//        when(esUtil.batchTermsQuery(any(), any())).thenReturn(boolQueryBuilder);
//        when(esUtil.queryForPage(any(), any())).thenReturn(new AggregatedPageImpl<>(Lists.newArrayList(ESMgkModelPo.builder()
//                .tradeIds(Lists.newArrayList(1L))
//                .pageId(1L)
//                .modelType(1)
//                .modelStyle(1)
//                .modelStatus(1)
//                .modelName("name")
//                .modelId(1L)
//                .Id(1L)
//                .build())));
//        List<ESModelDto> modelsByIds = esMgkModelService.getModelsByIds(Lists.newArrayList(1L));
//        assertNotNull(modelsByIds);
//        assertFalse(CollectionUtils.isEmpty(modelsByIds));
//    }
//
//    @Test
//    public void testGetESModelsByIds() {
//        BoolQueryBuilder boolQueryBuilder = mock(BoolQueryBuilder.class);
//        when(esUtil.batchTermsQuery(any(), any())).thenReturn(boolQueryBuilder);
//        when(esUtil.queryForPage(any(), any())).thenReturn(new AggregatedPageImpl<>(Lists.newArrayList(ESMgkModelPo.builder()
//                .tradeIds(Lists.newArrayList(1L))
//                .pageId(1L)
//                .modelType(1)
//                .modelStyle(1)
//                .modelStatus(1)
//                .modelName("name")
//                .modelId(1L)
//                .Id(1L)
//                .build())));
//        List<ESMgkModelPo> esModelsByIds = esMgkModelService.getESModelsByIds(Lists.newArrayList(1L));
//        assertNotNull(esModelsByIds);
//        assertFalse(CollectionUtils.isEmpty(esModelsByIds));
//    }
//
//
//    @Test
//    public void testGetModelsByName() {
//        Iterable<ESMgkModelPo> iterator = Arrays.asList(ESMgkModelPo.builder()
//                .Id(1L)
//                .modelId(1L)
//                .modelName("modelName")
//                .modelStatus(1)
//                .modelStyle(1)
//                .modelType(1)
//                .pageId(1L)
//                .tradeIds(Lists.newArrayList(1L))
//                .build());
//        when(modelRepository.search((QueryBuilder) any())).thenReturn(iterator);
//        List<ESModelDto> esModelDtos = esMgkModelService.getModelsByName("name");
//        assertNotNull(esModelDtos);
//        assertFalse(CollectionUtils.isEmpty(esModelDtos));
//    }
//}