package com.bilibili.mgk.platform.biz.service.chat.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/21 16:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatStartResp {
    private SWTMsgData data;
    private String code;
    private String msg;
    private String error_code;
    private String error_msg;
}
