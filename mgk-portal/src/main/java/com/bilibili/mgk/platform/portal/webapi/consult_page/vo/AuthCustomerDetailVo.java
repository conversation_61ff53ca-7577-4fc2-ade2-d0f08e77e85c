package com.bilibili.mgk.platform.portal.webapi.consult_page.vo;

import io.swagger.annotations.ApiModelProperty;
import jnr.ffi.annotations.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/18 20:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthCustomerDetailVo {
    @ApiModelProperty(value = "第三方客服id")
    private Integer customer_service_id;

    @ApiModelProperty(value = "第三方客服code")
    private String customer_service_code;

    @ApiModelProperty(value = "第三方客服名称")
    private String customer_service_name;

    @ApiModelProperty(value = "第三方客服头像")
    private String customer_service_profile;

    @ApiModelProperty(value = "是否已授权")
    private Boolean is_auth;

}
