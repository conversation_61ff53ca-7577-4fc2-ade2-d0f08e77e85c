package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkLandingPageGamePo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageGamePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkLandingPageGameDao {
    long countByExample(MgkLandingPageGamePoExample example);

    int deleteByExample(MgkLandingPageGamePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkLandingPageGamePo record);

    int insertBatch(List<MgkLandingPageGamePo> records);

    int insertUpdateBatch(List<MgkLandingPageGamePo> records);

    int insert(MgkLandingPageGamePo record);

    int insertUpdateSelective(MgkLandingPageGamePo record);

    int insertSelective(MgkLandingPageGamePo record);

    List<MgkLandingPageGamePo> selectByExample(MgkLandingPageGamePoExample example);

    MgkLandingPageGamePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkLandingPageGamePo record, @Param("example") MgkLandingPageGamePoExample example);

    int updateByExample(@Param("record") MgkLandingPageGamePo record, @Param("example") MgkLandingPageGamePoExample example);

    int updateByPrimaryKeySelective(MgkLandingPageGamePo record);

    int updateByPrimaryKey(MgkLandingPageGamePo record);
}