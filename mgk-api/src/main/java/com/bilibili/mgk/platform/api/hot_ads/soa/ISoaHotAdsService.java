package com.bilibili.mgk.platform.api.hot_ads.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.hot_ads.dto.DaiHuoHotAdDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDtoV2;
import com.bilibili.mgk.platform.api.hot_ads.dto.QueryDaiHuoHotAdDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.QueryHotAdsDto;

import java.sql.SQLException;

/**
 * @file: ISoaHotAdsService
 * @author: gaoming
 * @date: 2021/01/13
 * @version: 1.0
 * @description:
 **/
public interface ISoaHotAdsService {

    PageResult<HotAdsDto> getHotAdsDtos(Operator operator, QueryHotAdsDto queryHotAdsDto);

    PageResult<DaiHuoHotAdDto> getDaiHuoHotAdsDtos(Operator operator, QueryDaiHuoHotAdDto query) throws ServiceException, SQLException;
}
