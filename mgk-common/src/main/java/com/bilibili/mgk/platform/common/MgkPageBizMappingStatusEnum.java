package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkPageBizMappingStatusEnum
 * @author: xuh<PERSON>yu
 * @date: 2021/04/26
 * @version: 1.0
 * @description
 */
@AllArgsConstructor
public enum MgkPageBizMappingStatusEnum {

    VALID(0, "有效"),
    DELETED(1, "删除");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static MgkPageBizMappingStatusEnum getByCode(int code) {
        for (MgkPageBizMappingStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkPageBizMappingStatusEnum " + code);
    }

}
