package com.bilibili.mgk.platform.biz.dao.ext;

import com.bilibili.mgk.platform.biz.po.ExtMgkFormDataPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExtMgkFormDataDao {

    /**
     * 根据表单 ids 获取表单数据的统计量(除去线索通的 salesType)
     *
     * @see com.bilibili.adp.common.enums.SalesType
     * @param formIds
     * @return
     */
    List<ExtMgkFormDataPo> getFormIdAndFormDataInfo(@Param("formIds") List<Long> formIds);

    /**
     * 根据表单 ids 获取表单数据的统计量不过滤反作弊
     *
     * @see com.bilibili.adp.common.enums.SalesType
     * @param formIds
     * @return
     */
    List<ExtMgkFormDataPo> getFormIdAndFormDataInfoWithoutCheat(@Param("formIds") List<Long> formIds);
}