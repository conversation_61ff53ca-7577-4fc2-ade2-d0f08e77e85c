package com.bilibili.mgk.platform.biz.service.personal_mgk;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.crm.platform.api.account.dto.accountlabel.AccountLabelBindingDto;
import com.bilibili.crm.platform.common.UserType;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.mgk.platform.api.personal_mgk.IPerMgkAutoCreateAccService;
import com.bilibili.mgk.platform.api.personal_mgk.dto.AutoCreatePersonalFlyDto;
import com.bilibili.mgk.platform.biz.ad.po.AccAccountMidPo;
import com.bilibili.mgk.platform.biz.dao.MgkAutoCreateAccInfoDao;
import com.bilibili.mgk.platform.biz.grpc.GrpcCrmComponent;
import com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo;
import com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPoExample;
import com.bilibili.mgk.platform.biz.service.MgkAccountService;
import com.bilibili.mgk.platform.biz.service.MgkBaseService;
import com.bilibili.mgk.platform.common.MgkAccountLabelEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.enums.databus.MsgStatusEnum;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bilibili.crm.platform.common.ModifyType.ACCOUNT_LABEL_OPEN_API;

@Slf4j
@Service
@RequiredArgsConstructor
public class PerMgkAutoCreateAccService implements IPerMgkAutoCreateAccService {

    private final MgkAutoCreateAccInfoDao autoCreateAccInfoDao;
    private final MgkBaseService baseService;
    private final MgkAccountService accountService;
    private final GrpcCrmComponent grpcCrmComponent;
    private final ISoaAccountLabelService soaAccountLabelService;

    @Value("${mgk.allow.login.personal.mgk.label.id:678}")
    private Integer allowLoginPersonalMgkLabelId;

    private static final long LIMIT = 1000L;
    private static final long DURATION = 1200000L;


    @Override
    public Map<Long, Long> getDataId2Uid(List<Long> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return new HashMap<>();
        }
        MgkAutoCreateAccInfoPoExample poExample = new MgkAutoCreateAccInfoPoExample();
        poExample.or().andFormDataIdIn(dataIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkAutoCreateAccInfoPo> accInfoPos = autoCreateAccInfoDao.selectByExample(poExample);
        if (CollectionUtils.isEmpty(accInfoPos)) {
            return new HashMap<>();
        }
        return accInfoPos.stream().collect(Collectors.toMap(MgkAutoCreateAccInfoPo::getFormDataId,
                MgkAutoCreateAccInfoPo::getMid));
    }

    @Override
    public void refreshPerMgkAutoCreateAccJob() {
        long cur = 1L;
        log.info("refreshLandingPageInRedis begin");
        Timestamp splitTime = new Timestamp(System.currentTimeMillis() - DURATION);
        MgkAutoCreateAccInfoPoExample poExample = new MgkAutoCreateAccInfoPoExample();
        poExample.setLimit(1);
        poExample.setOrderByClause("mtime desc");
        List<MgkAutoCreateAccInfoPo> accInfoPos = autoCreateAccInfoDao.selectByExample(poExample);
        if (CollectionUtils.isEmpty(accInfoPos)) {
            return;
        }

        long maxId = accInfoPos.get(0).getId();
        while (cur <= maxId) {
            try {
                poExample.clear();
                poExample.or().andIdGreaterThanOrEqualTo(cur).andIdLessThanOrEqualTo(cur + LIMIT)
                        .andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andMtimeLessThanOrEqualTo(splitTime)
                        .andStatusIn(MsgStatusEnum.UNDO_STATUS);
                List<MgkAutoCreateAccInfoPo> pos = autoCreateAccInfoDao.selectByExample(poExample);
                if (!CollectionUtils.isEmpty(pos)) {
                    pos.forEach(po -> {
                        try {
                            autoCreateFlyAcc(AutoCreatePersonalFlyDto.builder().mid(po.getMid())
                                    .formDataId(po.getFormDataId()).pageId(po.getPageId())
                                    .ts(po.getCtime().getTime()).build());
                        } catch (Exception e) {
                            log.error("refreshLandingPageInRedis single error [{}]", ExceptionUtils.getSubStringMsg(e));
                        }
                    });
                }

            } catch (Exception e) {
                log.error("refreshLandingPageInRedis error [{}]", ExceptionUtils.getSubStringMsg(e));
            }
            cur += LIMIT;
        }
    }

    @Override
    public void autoCreateFlyAcc(AutoCreatePersonalFlyDto msg) {
        long mid = msg.getMid();
        RLock lock = baseService.getLock(mid, MgkConstants.AUTO_CREATE_ACC_LOCK_SUFFIX);
        Integer accountId = 0;
        try {
            List<AccAccountMidPo> accountPos = accountService.getByMidNew(mid,
                    Lists.newArrayList(UserType.PERSONAL_FLY.getCode()));
            if (CollectionUtils.isEmpty(accountPos)) {
                accountId = createPersonalFlyAccount(mid);
            } else {
                accountId = accountPos.get(0).getAccountId();
            }
            addAllowLoginPersonalMgkLabel(accountId);
            updateMsgStatus(mid, accountId, MsgStatusEnum.FINISHED.getCode(), "");
        } catch (Exception e) {
            updateMsgStatus(mid, accountId, MsgStatusEnum.FAILED.getCode(), ExceptionUtils.getSubStringMsg(e));
            log.error("autoCreateFlyAcc msg [{}] error [{}]", msg, ExceptionUtils.getSubStringMsg(e));
        } finally {
            lock.unlock();
        }
    }

    private Integer createPersonalFlyAccount(Long mid) {
        return grpcCrmComponent.openPersonalFly(mid);
    }

    public void addAllowLoginPersonalMgkLabel(Integer accountId) {
        boolean allow = accountService.checkAccountLabelFromDB(accountId,
                MgkAccountLabelEnum.ALLOW_LOGIN_PER_MGK.getCode());
        if (!allow) {
            //查询账户原有的标签
            AccountLabelBindingDto bindingDto = soaAccountLabelService
                    .getBindingDtoByAccountId(accountId);
            List<Integer> manualLabelIds = bindingDto.getManualMappingLabelIds() == null ?
                    new ArrayList<>() : bindingDto.getManualMappingLabelIds();
            List<Integer> autoLabelIds = bindingDto.getAutoMappingLabelIds() == null ?
                    new ArrayList<>() : bindingDto.getAutoMappingLabelIds();
            if (manualLabelIds.contains(allowLoginPersonalMgkLabelId) || autoLabelIds.contains(allowLoginPersonalMgkLabelId)) {
                return;
            }
            //手动操作
            manualLabelIds.add(allowLoginPersonalMgkLabelId);
            soaAccountLabelService.bind(
                    Operator.builder().operatorName("per_mgk").operatorType(OperatorType.SYSTEM)
                            .operatorId(accountId).build(),
                    AccountLabelBindingDto.builder().accountId(accountId).manualMappingLabelIds(manualLabelIds)
                            .autoMappingLabelIds(autoLabelIds).build(),
                    ACCOUNT_LABEL_OPEN_API);
        }
    }

    public void addBusinessToolLabel(Integer accountId, Integer labelId) {
        Boolean existLabel = soaAccountLabelService.checkAccountWithLabel(accountId, labelId);;

        if (BooleanUtils.isNotTrue(existLabel)) {
            AccountLabelBindingDto bindingDto = soaAccountLabelService.getBindingDtoByAccountId(accountId);
            List<Integer> manualLabelIds = bindingDto.getManualMappingLabelIds() == null ?
                    new ArrayList<>() : bindingDto.getManualMappingLabelIds();
            List<Integer> autoLabelIds = bindingDto.getAutoMappingLabelIds() == null ?
                    new ArrayList<>() : bindingDto.getAutoMappingLabelIds();
            if (manualLabelIds.contains(labelId) || autoLabelIds.contains(labelId)) {
                return;
            }
            manualLabelIds.add(labelId);
            soaAccountLabelService.bind(
                    Operator.builder().operatorName("per_mgk").operatorType(OperatorType.SYSTEM)
                            .operatorId(accountId).build(),
                    AccountLabelBindingDto.builder().accountId(accountId).manualMappingLabelIds(manualLabelIds)
                            .autoMappingLabelIds(autoLabelIds).build(),
                    ACCOUNT_LABEL_OPEN_API);
        }
    }

    private void updateMsgStatus(long mid, int accountId, int status, String reason) {
        MgkAutoCreateAccInfoPoExample poExample = new MgkAutoCreateAccInfoPoExample();
        poExample.or().andMidEqualTo(mid);
        autoCreateAccInfoDao.updateByExampleSelective(MgkAutoCreateAccInfoPo.builder().status(status)
                .accountId(accountId).reason(reason).build(), poExample);
    }
}
