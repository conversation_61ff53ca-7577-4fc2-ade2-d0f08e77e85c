<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.material.center.repository.mysql.AIDerivationImageBlacklistMapper">

  <resultMap id="BaseResultMap" type="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageBlacklist">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="accountId" column="account_id" jdbcType="BIGINT"/>
    <result property="creativeId" column="creative_id" jdbcType="BIGINT"/>
    <result property="unitId" column="unit_id" jdbcType="BIGINT"/>
    <result property="campaignId" column="campaign_id" jdbcType="BIGINT"/>
    <result property="imgMd5" column="img_md5" jdbcType="VARCHAR"/>
    <result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
    <result property="version" column="version" jdbcType="BIGINT"/>
    <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
    <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    <result property="deleted" column="deleted" jdbcType="TINYINT"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,account_id,creative_id,
        unit_id,campaign_id,img_md5,
        img_url,version,ctime,
        mtime,deleted
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mgk_material_ai_derivation_blacklist
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from mgk_material_ai_derivation_blacklist
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageBlacklist"
    useGeneratedKeys="true">
    insert into mgk_material_ai_derivation_blacklist
    ( id, account_id, creative_id
    , unit_id, campaign_id, img_md5
    , img_url, version, ctime
    , mtime, deleted)
    values ( #{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{creativeId,jdbcType=BIGINT}
           , #{unitId,jdbcType=BIGINT}, #{campaignId,jdbcType=BIGINT}, #{imgMd5,jdbcType=VARCHAR}
           , #{imgUrl,jdbcType=VARCHAR}, #{version,jdbcType=BIGINT}, #{ctime,jdbcType=TIMESTAMP}
           , #{mtime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageBlacklist"
    useGeneratedKeys="true">
    insert into mgk_material_ai_derivation_blacklist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="accountId != null">account_id,</if>
      <if test="creativeId != null">creative_id,</if>
      <if test="unitId != null">unit_id,</if>
      <if test="campaignId != null">campaign_id,</if>
      <if test="imgMd5 != null">img_md5,</if>
      <if test="imgUrl != null">img_url,</if>
      <if test="version != null">version,</if>
      <if test="ctime != null">ctime,</if>
      <if test="mtime != null">mtime,</if>
      <if test="deleted != null">deleted,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=BIGINT},</if>
      <if test="accountId != null">#{accountId,jdbcType=BIGINT},</if>
      <if test="creativeId != null">#{creativeId,jdbcType=BIGINT},</if>
      <if test="unitId != null">#{unitId,jdbcType=BIGINT},</if>
      <if test="campaignId != null">#{campaignId,jdbcType=BIGINT},</if>
      <if test="imgMd5 != null">#{imgMd5,jdbcType=VARCHAR},</if>
      <if test="imgUrl != null">#{imgUrl,jdbcType=VARCHAR},</if>
      <if test="version != null">#{version,jdbcType=BIGINT},</if>
      <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
      <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
      <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageBlacklist">
    update mgk_material_ai_derivation_blacklist
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=BIGINT},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=BIGINT},
      </if>
      <if test="imgMd5 != null">
        img_md5 = #{imgMd5,jdbcType=VARCHAR},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageBlacklist">
    update mgk_material_ai_derivation_blacklist
    set account_id  = #{accountId,jdbcType=BIGINT},
        creative_id = #{creativeId,jdbcType=BIGINT},
        unit_id     = #{unitId,jdbcType=BIGINT},
        campaign_id = #{campaignId,jdbcType=BIGINT},
        img_md5     = #{imgMd5,jdbcType=VARCHAR},
        img_url     = #{imgUrl,jdbcType=VARCHAR},
        version     = #{version,jdbcType=BIGINT},
        ctime       = #{ctime,jdbcType=TIMESTAMP},
        mtime       = #{mtime,jdbcType=TIMESTAMP},
        deleted     = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
