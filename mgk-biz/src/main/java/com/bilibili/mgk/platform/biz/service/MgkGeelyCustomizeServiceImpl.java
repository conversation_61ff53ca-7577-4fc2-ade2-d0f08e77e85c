package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.api.data.dto.ReportDataDto;
import com.bilibili.mgk.platform.api.data.service.IMgkGeelyCustomizeService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName MgkGeelyCustomizeServiceImpl
 * <AUTHOR>
 * @Date 2022/5/4 10:20 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkGeelyCustomizeServiceImpl implements IMgkGeelyCustomizeService {

    @Autowired
    private MgkGeelyCustomizeServiceDelegate mgkGeelyCustomizeServiceDelegate;

    @Override
    public Integer reportGeelyData(Long formDataId, ReportDataDto reportDataDto) {
        return mgkGeelyCustomizeServiceDelegate.reportGeelyData(formDataId, reportDataDto);
    }

    @Override
    public void refreshGeelyReportRecord() {
        mgkGeelyCustomizeServiceDelegate.refreshGeelyReportRecord();
    }

    public Response test() {
        return null;
    }


}
