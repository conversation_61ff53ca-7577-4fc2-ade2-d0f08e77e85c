package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 */

@AllArgsConstructor
public enum WxCustomerAcqStatusEnum {

    NONE(0, "未设置"),

    VALID(1, "有效"),

    INVALID(2, "无效"),;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static WxCustomerAcqStatusEnum getByCode(int code) {
        for (WxCustomerAcqStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code WxCustomerAcqStatusEnum " + code);
    }

}
