package com.bilibili.mgk.platform.biz.service.wechat.impl;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryLandingPageParamDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.wechat.dto.*;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatPackageService;
import com.bilibili.mgk.platform.biz.service.MgkBaseService;
import com.bilibili.mgk.platform.biz.service.wechat.delegate.MgkWechatPackageServiceDelegate;
import com.bilibili.mgk.platform.biz.service.wechat.delegate.MgkWechatPackageValidateService;
import com.bilibili.mgk.platform.common.IsModelEnum;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @ClassName MgkWechatPackageServiceImpl
 * <AUTHOR>
 * @Date 2022/6/1 4:07 下午
 * @Version 1.0
 **/
@Service
@Slf4j
@Lazy
public class MgkWechatPackageServiceImpl implements IMgkWechatPackageService {

    @Autowired
    private MgkBaseService mgkBaseService;

    @Autowired
    private MgkWechatPackageValidateService mgkWechatPackageValidateService;

    @Autowired
    private MgkWechatPackageServiceDelegate mgkWechatPackageServiceDelegate;

    @Autowired
    private IMgkLandingPageService landingPageService;

    @Override
    public Integer create(WechatPackageCreateDto createDto) {
        mgkWechatPackageValidateService.validCreateDto(createDto);
        return mgkWechatPackageServiceDelegate.create(createDto);
    }

    @Override
    public Integer update(WechatPackageCreateDto updateDto, Operator operator) {
        mgkWechatPackageValidateService.validUpdateDto(updateDto, operator);
        RLock lock = mgkBaseService.getLock(updateDto.getId(), MgkConstants.WECHAT_PACKAGE_LOCK_SUFFIX);
        try {
            return mgkWechatPackageServiceDelegate.update(updateDto);
        } finally {
            log.info(System.currentTimeMillis() + "update wechat package unlock:{}", updateDto.getId());
            lock.unlock();
        }
    }

    @Override
    public PageResult<WechatPackageListDto> queryByPage(WechatPackageQueryDto queryDto) {
        mgkWechatPackageValidateService.validQueryByPageDto(queryDto);
        return mgkWechatPackageServiceDelegate.queryByPage(queryDto);
    }

    @Override
    public List<WechatPackageListDto> queryList(WechatPackageQueryDto queryDto) {
        return mgkWechatPackageServiceDelegate.queryList(queryDto);
    }

    @Override
    public WechatPackageDto queryValidDtoById(Integer id) {
        return mgkWechatPackageServiceDelegate.queryValidDtoById(id);
    }

    @Override
    public WechatPackageDto queryValidBaseDtoById(Integer id) {
        return mgkWechatPackageServiceDelegate.queryValidBaseDtoById(id);
    }

    @Override
    public Integer deleteById(Integer id, Operator operator) {
        mgkWechatPackageValidateService.validateDeleteInfo(id, operator);
        RLock lock = mgkBaseService.getLock(id, MgkConstants.WECHAT_PACKAGE_LOCK_SUFFIX);
        try {
            return mgkWechatPackageServiceDelegate.deleteById(id);
        } finally {
            log.info(System.currentTimeMillis() + "delete wechat package unlock:{}", id);
            lock.unlock();
        }
    }

    @Override
    public List<Long> getPageIdsByPackageIds(List<Integer> packageIds) {
        return mgkWechatPackageServiceDelegate.getPageIdsByIds(packageIds);
    }

    @Override
    public Map<Long, WechatPackageDto> getWechatPackageMapByPageIds(List<Long> pageIds) {
        return mgkWechatPackageServiceDelegate.getWechatPackageDtoMapByPageIds(pageIds);
    }

    @Override
    public WechatPackageInfoDto getWechatPackageInfoByPackageId(Integer id) {
        return mgkWechatPackageServiceDelegate.getWechatPackageInfoByPackageId(id);
    }

    @Override
    public void refreshWechatPackageCacheByIds(List<Integer> ids) {
        mgkWechatPackageServiceDelegate.refreshWechatPackageCacheByIds(ids);
    }

    @Override
    public void refreshWechatPackageCache() {
        mgkWechatPackageServiceDelegate.refreshWechatPackageCache();
    }

    public Map<Integer, WechatPackageDto> getPackageWechatPackageBaseMapFromDb(List<Integer> packageIds) {
        return mgkWechatPackageServiceDelegate.getPackageWechatPackageBaseMapFromDb(packageIds);
    }

    @Override
    public MgkLandingPageDto getPageDtoByWechatPackageIdIdForCommentConvert(Integer wechatPackageId) {
        if(Utils.isPositive(wechatPackageId)){
            List<Long> pageIds = mgkWechatPackageServiceDelegate
                    .getPageIdsByWechatPackageIds(Lists.newArrayList(wechatPackageId));
            if(!CollectionUtils.isEmpty(pageIds)) {
                List<MgkLandingPageDto> pageDtoS = landingPageService
                        .getLandingPageDtos(QueryLandingPageParamDto.builder()
                                .isModelList(Lists.newArrayList(IsModelEnum.COMMENT.getCode()))
                                .statusList(Lists.newArrayList(LandingPageStatusEnum.PUBLISHED.getCode()))
                                .pageIdList(pageIds).formIdLtZero(false)
                                .build());

                //评论区微信浮层落地页一个微信包只会生成一个落地页
                if(!CollectionUtils.isEmpty(pageDtoS)){
                    return pageDtoS.get(0);
                }
            }
        }

        return null;
    }
}
