package com.bilibili.mgk.platform.api.es.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @file: MgkHomeDto
 * @author: gaoming
 * @date: 2021/07/08
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MgkHomeDto implements Serializable {
    private static final long serialVersionUID = 1610868632533296121L;

    /**
     * 落地页id
     */
    private Long mgkPageId;

    /**
     * 广告主
     */
    private Long userId;

    /**
     * 点击数
     */
    private Long click;

    /**
     * ctr数量
     */
    private Long ctr;

    /**
     * 时间
     */
    private Timestamp groupDate;
}
