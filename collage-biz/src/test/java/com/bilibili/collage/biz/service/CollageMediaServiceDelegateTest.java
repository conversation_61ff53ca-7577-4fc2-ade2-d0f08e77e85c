package com.bilibili.collage.biz.service;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.dto.CollageMediaUpdateDto;
import com.bilibili.collage.api.dto.CollageSizeDto;
import com.bilibili.collage.api.dto.QueryCollageMediaDto;
import com.bilibili.collage.api.service.ICollageCoverService;
import com.bilibili.collage.api.service.ICollageLogService;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.bilibili.collage.biz.dao.MgkCollageMediaDao;
import com.bilibili.collage.biz.po.MgkCollageMediaPo;
import com.google.common.collect.Lists;
import java.util.Collections;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2020/09/15
 **/
public class CollageMediaServiceDelegateTest extends BaseMockitoTest {

    @InjectMocks
    private CollageMediaServiceDelegate collageMediaServiceDelegate;

    @Mock
    private MgkCollageMediaDao mgkCollageMediaDao;

    @Mock
    private ICollageLogService collageLogService;

    @Mock
    private SnowflakeIdWorker snowflakeIdWorker;

    @Mock
    private ICollageSizeService collageSizeService;

    @Mock
    private ICollageCoverService collageCoverService;


    private QueryCollageMediaDto queryCollageMediaDto;

    private CollageMediaUpdateDto collageMediaUpdateDto;

    private CollageMediaDto collageMediaDto;

    @Mock
    private CollageMediaPushSyncer collageMediaPushSyncer;

    @Before
    public void setUp() throws Exception {

        queryCollageMediaDto = QueryCollageMediaDto.builder()
                .mediaRadios(Lists.newArrayList(1))
                .pageInfo(Page.valueOf(1, 15))
                .accountIds(Lists.newArrayList(1))
                .ids(Lists.newArrayList(1))
                .worksIds(Lists.newArrayList(1))
                .patternIds(Lists.newArrayList(1))
                .mediaTypes(Lists.newArrayList(1))
                .mediaName("name")
                .width(1)
                .height(1)
                .filterNoMd5(true)
                .build();
        collageMediaUpdateDto = CollageMediaUpdateDto.builder()
                .mediaName("名称")
                .id(1)
                .build();

        collageMediaDto = CollageMediaDto.builder()
                .build();
    }

    @Test
    public void testGetMediaList() {
        when(mgkCollageMediaDao.countByExample(any())).thenReturn(1L);

        PageResult<CollageMediaDto> pageResult = collageMediaServiceDelegate.getMediaList(queryCollageMediaDto);
        assertFalse(CollectionUtils.isEmpty(pageResult.getRecords()));

        when(mgkCollageMediaDao.countByExample(any())).thenReturn(0L);
        pageResult = collageMediaServiceDelegate.getMediaList(queryCollageMediaDto);
        assertTrue(CollectionUtils.isEmpty(pageResult.getRecords()));

        when(mgkCollageMediaDao.countByExample(any())).thenReturn(1L);
        when(mgkCollageMediaDao.selectByExample(any())).thenReturn(Collections.emptyList());
        pageResult = collageMediaServiceDelegate.getMediaList(queryCollageMediaDto);
        assertTrue(CollectionUtils.isEmpty(pageResult.getRecords()));

        pageResult = collageMediaServiceDelegate.getMediaList(null);

    }

    @Test
    public void testUpdateMedia() {
        collageMediaServiceDelegate.updateMedia(operator, collageMediaUpdateDto);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBatchDeleted() {
        collageMediaServiceDelegate.batchDeleted(operator, Lists.newArrayList(1));
        collageMediaServiceDelegate.batchDeleted(operator, Lists.newArrayList(1, 2));
        collageMediaServiceDelegate.batchDeleted(operator, Collections.emptyList());
    }

    @Test
    public void testSingleDeleted() {
        collageMediaServiceDelegate.singleDeleted(operator, 1);
    }

    @Test
    public void testInsert() {
        when(mgkCollageMediaDao.insertSelective(any())).thenAnswer(answer -> {
            MgkCollageMediaPo po = answer.getArgumentAt(0, MgkCollageMediaPo.class);
            po.setId(2);
            return 1;
        });
        collageMediaServiceDelegate.insert(operator, collageMediaDto);
    }

    @Test
    public void testTestInsert() {
        collageMediaServiceDelegate.insert(operator, Collections.emptyList());
        collageMediaServiceDelegate.insert(operator, Lists.newArrayList(collageMediaDto));
    }

    @Test
    public void testGetMediaOther() {
        try {
            PageResult<CollageMediaDto> mediaOther = collageMediaServiceDelegate.getMediaOther(queryCollageMediaDto);
            assertFalse(CollectionUtils.isEmpty(mediaOther.getRecords()));
            when(collageSizeService.queryCollageSize(any())).thenReturn(Lists.newArrayList(CollageSizeDto.builder()
                    .width(1)
                    .height(10000)
                    .build()));
            mediaOther = collageMediaServiceDelegate.getMediaOther(QueryCollageMediaDto.builder()
                    .accountIds(Lists.newArrayList(10005))
                    .build());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }

//    @Test
//    public void testGetMediaOther() {
//        PageResult<CollageMediaDto> mediaOther = collageMediaServiceDelegate.getMediaOther(queryCollageMediaDto);
//        assertFalse(CollectionUtils.isEmpty(mediaOther.getRecords()));
//        when(collageSizeService.queryCollageSize(any())).thenReturn(Lists.newArrayList(CollageSizeDto.builder()
//                .width(1)
//                .height(10000)
//                .build()));
//        mediaOther = collageMediaServiceDelegate.getMediaOther(QueryCollageMediaDto.builder()
//                .accountIds(Lists.newArrayList(10005))
//                .build());
//    }

    @Test
    public void testUpdateMediaUrl() {
        collageMediaServiceDelegate.updateMediaUrl(operator, 1, "http");
    }


}