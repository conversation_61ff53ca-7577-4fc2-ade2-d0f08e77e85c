package com.bilibili.mgk.platform.portal.webapi.hot_ads;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.mgk.platform.api.daihuo_category.ICategoryService;
import com.bilibili.mgk.platform.api.hot_ads.dto.*;
import com.bilibili.mgk.platform.api.hot_ads.service.IDaiHuoHotAdsService;
import com.bilibili.mgk.platform.api.hot_ads.service.IHotAdsService;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoBlackDto;
import com.bilibili.mgk.platform.api.hot_video.dto.QueryHotVideoCollectDto;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoBlackService;
import com.bilibili.mgk.platform.common.*;
import com.bilibili.mgk.platform.common.utils.TimeUtil;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Pagination;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebHotAdsService;
import com.bilibili.mgk.platform.portal.webapi.hot_ads.vo.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.mysema.commons.lang.Pair;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/web_api/v1/daihuo/hot_ads")
@Slf4j
@Api(value = "/hot_ads", tags = "DaiHuoHotAdsController-带货热门广告相关")
@FreeLogin
public class DaiHuoHotAdsController extends BasicController {

    @Autowired
    private IDaiHuoHotAdsService daihuoHotAdsService;

    @Autowired
    private IHotAdsService hotAdsService;

    @Autowired
    private IHotVideoBlackService hotVideoBlackService;

    @Autowired
    private ICategoryService categoryService;


    @ApiOperation(value = "带货热门广告列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @FreeLogin
    @ResponseBody
    public Response<Pagination<List<DaiHuoHotAdsListVo>>> getList(@ApiIgnore Context context,
            @ApiParam(value = "商品标题") @RequestParam(value = "item_name", defaultValue = "") String itemName,
                                                                  @ApiParam(value = "商品来源") @RequestParam(value = "item_source", defaultValue = "") String itemSource,
                                                                  @ApiParam(value = "带货一级类目") @RequestParam(value = "first_category", defaultValue = "") String firstCategory,
                                                                  @ApiParam(value = "带货二级类目") @RequestParam(value = "second_category", defaultValue = "") String secondCategory,
                                                                  @ApiParam(value = "时间类型 0-7d 1-30d") @RequestParam(value = "day_type", defaultValue = "0") Integer dateType,
                                                                  @ApiParam(value = "排序 0-曝光 1-点击 2-转化 3-点击转化") @RequestParam(value = "order_by", defaultValue = "0") Integer orderBy,
                                                                  @ApiParam(value = "页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                  @ApiParam(value = "每页大小") @RequestParam(value = "size", defaultValue = "15") Integer size) throws SQLException, ServiceException {
        QueryDaiHuoHotAdDto queryHotAdsDto = QueryDaiHuoHotAdDto.builder()
                .itemName(Strings.isNullOrEmpty(itemName) ? "" : itemName)
                .itemSource(itemSource)
                .firstCategory(firstCategory)
                .secondCategory(secondCategory)
                .dayType(dateType == null ? MgkHotAdsDateTypeEnum.LAST_WEEK.getCode() : dateType)
                .orderType(orderBy == null ? MgkHotAdsOrderByEnum.CTR.getCode() : orderBy)
                .page(Page.valueOf(page, size))
                .blackStatus(MgkHotVideoIsBlackEnum.NORMAL.getCode())
                .build();

        List<String> blackList = hotVideoBlackService.getBlackList(MgkHotVideoBlackTypeEnum.DAI_HUO_HOT_ADS.getCode())
                .stream().map(HotVideoBlackDto::getAdTypeCreativeId).collect(Collectors.toList());
        queryHotAdsDto.setBlackList(blackList);

        Operator operator = context == null ? null : super.getOperator(context);
        PageResult<DaiHuoHotAdDto> pageResult = daihuoHotAdsService.getDaiHuoHotAdDtos(queryHotAdsDto, operator);
        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(),
                WebHotAdsService.convertDaiHUoListDtos2Vos(pageResult.getRecords())));

    }

    @ApiOperation(value = "带货广告详情")
    @RequestMapping(value = "/{creative_id}", method = RequestMethod.GET)
    @FreeLogin
    @ResponseBody
    public Response<DaiHuoHotAdsDetailVo> getHotVideoById(@ApiParam(value = "创意Id") @PathVariable(value = "creative_id") String creativeId,
                                                          @ApiParam(value = "时间类型") @RequestParam(value = "day_type", defaultValue = "0") Integer dayType,
                                                          @ApiParam(value = "查询日期") @RequestParam(value = "log_date") String logDate) throws ServiceException, SQLException {
        DaiHuoHotAdDetailDto adDetailDto = daihuoHotAdsService.getHotAdDetail(creativeId, dayType, logDate);
        return Response.SUCCESS(WebHotAdsService.convertDaiHuoHotAdDetail2Vo(adDetailDto));
    }

    @ApiModelProperty(value = "热门广告-商品来源查询")
    @RequestMapping(value = "/item_source", method = RequestMethod.GET)
    @FreeLogin
    @ResponseBody
    public Response<List<DaiHuoSourceItemVo>> getItemSource() {
        return Response.SUCCESS(Arrays.stream(DaihuoItemSourceEnum.values())
                        .map(item->DaiHuoSourceItemVo.builder().item_source(item.getCode())
                                .item_source_name(item.getDesc()).build())
                .collect(Collectors.toList()));
    }

    @ApiModelProperty(value = "热门广告-一级类目查询")
    @RequestMapping(value = "/first_category", method = RequestMethod.GET)
    @FreeLogin
    @ResponseBody
    public Response<List<String>> getFirstCategory(@ApiParam(value = "商品来源") @RequestParam(value = "item_source") Integer itemSource) {
        Assert.notNull(itemSource, "商品来源不能为空");
        List<String> firstList =  categoryService.findCategory(itemSource, null);
        return Response.SUCCESS(firstList);
    }

    @ApiModelProperty(value = "热门广告-二级类目查询")
    @RequestMapping(value = "/second_category", method = RequestMethod.GET)
    @FreeLogin
    @ResponseBody
    public Response<List<String>> getSecondCategory(@ApiParam(value = "商品来源") @RequestParam(value = "item_source") Integer itemSource,
                                                    @ApiParam(value = "一级类目") @RequestParam(value = "first_category") String firstCategory) {
        Assert.notNull(itemSource, "商品来源不能为空");
        Assert.isTrue(StringUtils.hasText(firstCategory), "一级类目不能为空");
        List<String> secondList =  categoryService.findCategory(itemSource, firstCategory);
        return Response.SUCCESS(secondList);
    }

    @ApiOperation(value = "新建收藏")
    @RequestMapping(value = "/collect/{creative_id}", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> doCollect(@ApiIgnore Context context,
                                      @ApiParam("收藏信息") @RequestBody DaiHuoHotCollectVo collectVo) {
        Assert.notNull(context, "操作人信息不能为空");
        hotAdsService.doCollect(this.getOperator(context), collectVo.getCreativeId(), collectVo.getTitle(),
                MgkHotVideoCollectTypeEnum.DAIHUO_HOT_ADS.getCode(), collectVo.getLogDate(), collectVo.getDayType());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "取消收藏")
    @RequestMapping(value = "/cancel_collect/{creative_id}", method = RequestMethod.PUT)
    @ResponseBody
    public Response<String> cancelCollect(@ApiIgnore Context context,
                                          @ApiParam("广告类型和创意Id") @PathVariable(value = "creative_id") String creativeId) {
        Assert.notNull(context, "操作人信息不能为空");
        hotAdsService.cancelCollect(this.getOperator(context), creativeId, MgkHotVideoCollectTypeEnum.DAIHUO_HOT_ADS.getCode());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "收藏列表")
    @RequestMapping(value = "/collect", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<DaiHuoHotAdsListVo>>> getCollectList(@ApiIgnore Context context,
                                                                   @ApiParam(value = "页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                   @ApiParam(value = "每页大小") @RequestParam(value = "size", defaultValue = "15") Integer size) throws ServiceException, SQLException {
        Assert.notNull(context, "操作人信息不能为空");
        List<String> blackList = hotVideoBlackService.getBlackList(MgkHotVideoBlackTypeEnum.DAI_HUO_HOT_ADS.getCode())
                .stream().map(HotVideoBlackDto::getAdTypeCreativeId).collect(Collectors.toList());
        QueryHotVideoCollectDto query = QueryHotVideoCollectDto.builder().page(Page.valueOf(page, size))
                .collectTypes(Lists.newArrayList(MgkHotVideoCollectTypeEnum.DAIHUO_HOT_ADS.getCode()))
                .blackList(blackList)
                //底表数据只留存360天
                .beginDate(TimeUtil.localDateToISOStr(LocalDate.now().minusDays(359L)))
                .accountIds(Lists.newArrayList(super.getOperator(context).getOperatorId()))
                .isDeleted(IsDeleted.VALID.getCode())
                .build();
        PageResult<Pair<String, String>> collectPage = hotAdsService.getCollectAdsByPage(query);
        int count = collectPage.getTotal();
        if(count == 0){
            return Response.SUCCESS(new Pagination<>(page, count, null));
        }
        List<DaiHuoHotAdDto> hotAdDtos = daihuoHotAdsService
                .getDaiHuoHotAdDtosByIds(collectPage.getRecords(), MgkHotAdsOrderByEnum.PV.getCode(),
                Page.valueOf(page, size));

        hotAdDtos.forEach(hotAdDto->hotAdDto.setIsCollect(MgkHotVideoIsCollectEnum.COLLECT_ENUM.getCode()));

        return Response.SUCCESS(new Pagination<>(page, count,
                WebHotAdsService.convertDaiHUoListDtos2Vos(hotAdDtos)));
    }
}
