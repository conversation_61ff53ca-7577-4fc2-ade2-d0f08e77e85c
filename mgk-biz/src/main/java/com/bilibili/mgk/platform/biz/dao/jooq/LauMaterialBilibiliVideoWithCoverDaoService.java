package com.bilibili.mgk.platform.biz.dao.jooq;

import com.bilibili.mgk.platform.biz.service.archive.delegate.LauArchiveInfoServiceDelegate;
import org.jooq.DSLContext;
import org.jooq.types.ULong;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bilibili.mgk.platform.biz.dao.jooq.generated.Tables.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER;

/**
 * @ClassName LauMaterialBilibiliVideoWithCoverDaoService
 * <AUTHOR>
 * @Date 2022/1/26 7:27 下午
 * @Version 1.0
 **/
@Service
public class LauMaterialBilibiliVideoWithCoverDaoService {

    public static final String JOOQ_DSL = "jooqDsl";

    private final DSLContext mgkDslContext;

    LauMaterialBilibiliVideoWithCoverDaoService(@Qualifier(JOOQ_DSL) DSLContext mgkDslContext) {
        this.mgkDslContext = mgkDslContext;
    }

    public List<LauArchiveInfoServiceDelegate.ArchiveRefreshInfoDto> getLauBilibiliVideoWithCoverRefreshInfoList(long startId) {
        return mgkDslContext
                .select(LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.ID,
                        LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.AVID)
                .from(LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER)
                .where(LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.ID.gt(ULong.valueOf(startId)))
                .orderBy(LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.ID.asc())
                .limit(100)
                .fetch().into(LauArchiveInfoServiceDelegate.ArchiveRefreshInfoDto.class);
    }
}
