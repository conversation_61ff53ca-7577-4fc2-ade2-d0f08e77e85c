package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class LbsSubDropDown {

    private Integer id;

    private String name;

    private String address;

    private Integer level;

    private List<LbsSubDropDown> children;

}
