package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.launch.api.soa.ISoaAdpCpcAccountService;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.collage.api.dto.QueryCollageEnterpriseDto;
import com.bilibili.collage.biz.BaseMockitoTest;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.mockito.Mockito.when;

/**
 * @file: CollageEnterpriseServiceDelegateTest
 * @author: gaoming
 * @date: 2020/12/11
 * @version: 1.0
 * @description:
 **/
public class CollageEnterpriseServiceDelegateTest extends BaseMockitoTest {

    @InjectMocks
    private CollageEnterpriseServiceDelegate collageEnterpriseServiceDelegate;

    @Mock
    private RedisTemplate<String, String> stringRedisTemplate;
    @Mock
    private ValueOperations valueOperations;
    @Mock
    private IPassportService passportService;
    @Mock
    private ISoaAdpCpcAccountService soaAdpCpcAccountService;

    public void setUp() throws Exception {
    }

    @Test
    public void testGetArchiveVideos() {
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        collageEnterpriseServiceDelegate.getArchiveVideos(QueryCollageEnterpriseDto.builder()
                .pageInfo(Page.valueOf(1, 15))
                .durationEnd(1)
                .durationBegin(1)
                .sizeType(1)
                .accountId(10005)
                .mid(27515256L)
                .build());
    }
}