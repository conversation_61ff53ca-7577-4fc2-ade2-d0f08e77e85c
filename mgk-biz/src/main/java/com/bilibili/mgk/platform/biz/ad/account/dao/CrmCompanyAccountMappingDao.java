package com.bilibili.mgk.platform.biz.ad.account.dao;

import com.bilibili.mgk.platform.biz.ad.po.CrmCompanyAccountMappingPo;
import com.bilibili.mgk.platform.biz.ad.po.CrmCompanyAccountMappingPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface CrmCompanyAccountMappingDao {
    long countByExample(CrmCompanyAccountMappingPoExample example);

    int deleteByExample(CrmCompanyAccountMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmCompanyAccountMappingPo record);

    int insertBatch(List<CrmCompanyAccountMappingPo> records);

    int insertUpdateBatch(List<CrmCompanyAccountMappingPo> records);

    int insert(CrmCompanyAccountMappingPo record);

    int insertUpdateSelective(CrmCompanyAccountMappingPo record);

    int insertSelective(CrmCompanyAccountMappingPo record);

    List<CrmCompanyAccountMappingPo> selectByExample(CrmCompanyAccountMappingPoExample example);

    CrmCompanyAccountMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmCompanyAccountMappingPo record, @Param("example") CrmCompanyAccountMappingPoExample example);

    int updateByExample(@Param("record") CrmCompanyAccountMappingPo record, @Param("example") CrmCompanyAccountMappingPoExample example);

    int updateByPrimaryKeySelective(CrmCompanyAccountMappingPo record);

    int updateByPrimaryKey(CrmCompanyAccountMappingPo record);
}