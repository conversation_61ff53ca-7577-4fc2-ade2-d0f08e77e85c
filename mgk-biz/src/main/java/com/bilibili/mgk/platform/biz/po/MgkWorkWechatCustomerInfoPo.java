package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkWorkWechatCustomerInfoPo implements Serializable {
    /**
     * 自增ID（主键）
     */
    private Long id;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 昵称
     */
    private String name;

    private static final long serialVersionUID = 1L;
}