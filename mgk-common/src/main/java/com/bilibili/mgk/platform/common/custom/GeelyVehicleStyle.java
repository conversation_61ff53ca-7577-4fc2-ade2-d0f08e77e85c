package com.bilibili.mgk.platform.common.custom;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName GeelyVehicleType
 * <AUTHOR>
 * @Date 2022/4/29 2:35 下午
 * @Version 1.0
 * @desc 吉利汽车车型
 **/
@Getter
@AllArgsConstructor
public enum GeelyVehicleStyle {
    LYNKCO_01(1, "Lynkco-01", "领克01"),
    LYNKCO_01_PHEV(2, "Lynkco-01PHEV", "领克01PHEV"),
    LYNKCO_02(3, "Lynkco-02", "领克02"),
    LYNKCO_02_PHEV(4, "Lynkco-02PHEV", "领克02PHEV"),
    LYNKCO_03(5, "Lynkco-03", "领克03"),
    LYNKCO_03_PLUS(6, "Lynkco-03+", "领克03+"),
    LYNKCO_03_PHEV(7, "Lynkco-03PHEV", "领克03PHEV"),
    LYNKCO_05(8, "Lynkco-05", "领克05"),
    LYNKCO_05_PLUS(9, "Lynkco-05+", "领克05+"),
    LYNKCO_05_PHEV(10, "Lynkco-05PHEV", "领克05PHEV"),
    LYNKCO_06(11, "Lynkco-06", "领克06"),
    LYNKCO_06_PHEV(12, "Lynkco-06PHEV", "领克06PHEV"),
    LYNKCO_09(13, "Lynkco-09", "领克09"),
    LYNKCO_09_PHEV(14, "Lynkco-09PHEV", "领克09PHEV"),
    ;

    private Integer id;
    private String code;
    private String desc;

    public static GeelyVehicleStyle getById(int id) {
        for (GeelyVehicleStyle bean : values()) {
            if (bean.getId() == id) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown id GeelyVehicleStyle " + id);
    }

    public static GeelyVehicleStyle getByCode(String code) {
        for (GeelyVehicleStyle bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code GeelyVehicleStyle " + code);
    }
}