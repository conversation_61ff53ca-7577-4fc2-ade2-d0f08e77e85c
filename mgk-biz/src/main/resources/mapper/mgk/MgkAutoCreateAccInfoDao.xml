<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkAutoCreateAccInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="mid" jdbcType="BIGINT" property="mid" />
    <result column="form_data_id" jdbcType="BIGINT" property="formDataId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, page_id, account_id, mid, form_data_id, status, reason, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_auto_create_acc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_auto_create_acc_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mgk_auto_create_acc_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPoExample">
    delete from mgk_auto_create_acc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_auto_create_acc_info (page_id, account_id, mid, 
      form_data_id, status, reason, 
      is_deleted, ctime, mtime
      )
    values (#{pageId,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{mid,jdbcType=BIGINT}, 
      #{formDataId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{reason,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_auto_create_acc_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="formDataId != null">
        form_data_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="formDataId != null">
        #{formDataId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPoExample" resultType="java.lang.Long">
    select count(*) from mgk_auto_create_acc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_auto_create_acc_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.pageId != null">
        page_id = #{record.pageId,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.formDataId != null">
        form_data_id = #{record.formDataId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_auto_create_acc_info
    set id = #{record.id,jdbcType=BIGINT},
      page_id = #{record.pageId,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      mid = #{record.mid,jdbcType=BIGINT},
      form_data_id = #{record.formDataId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      reason = #{record.reason,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo">
    update mgk_auto_create_acc_info
    <set>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="formDataId != null">
        form_data_id = #{formDataId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo">
    update mgk_auto_create_acc_info
    set page_id = #{pageId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=INTEGER},
      mid = #{mid,jdbcType=BIGINT},
      form_data_id = #{formDataId,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      reason = #{reason,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_auto_create_acc_info (page_id, account_id, mid, 
      form_data_id, status, reason, 
      is_deleted, ctime, mtime
      )
    values (#{pageId,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{mid,jdbcType=BIGINT}, 
      #{formDataId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{reason,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      page_id = values(page_id),
      account_id = values(account_id),
      mid = values(mid),
      form_data_id = values(form_data_id),
      status = values(status),
      reason = values(reason),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_auto_create_acc_info
      (page_id,account_id,mid,form_data_id,status,reason,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.pageId,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.mid,jdbcType=BIGINT},
        #{item.formDataId,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT},
        #{item.reason,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_auto_create_acc_info
      (page_id,account_id,mid,form_data_id,status,reason,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.pageId,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.mid,jdbcType=BIGINT},
        #{item.formDataId,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT},
        #{item.reason,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      page_id = values(page_id),
      account_id = values(account_id),
      mid = values(mid),
      form_data_id = values(form_data_id),
      status = values(status),
      reason = values(reason),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_auto_create_acc_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="formDataId != null">
        form_data_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="formDataId != null">
        #{formDataId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="pageId != null">
        page_id = values(page_id),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="mid != null">
        mid = values(mid),
      </if>
      <if test="formDataId != null">
        form_data_id = values(form_data_id),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="reason != null">
        reason = values(reason),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>