package com.bilibili.collage.biz.dao;

import com.bilibili.collage.biz.po.CollageOperationLogPo;
import com.bilibili.collage.biz.po.CollageOperationLogPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CollageOperationLogDao {
    long countByExample(CollageOperationLogPoExample example);

    int deleteByExample(CollageOperationLogPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CollageOperationLogPo record);

    int insertBatch(List<CollageOperationLogPo> records);

    int insertUpdateBatch(List<CollageOperationLogPo> records);

    int insert(CollageOperationLogPo record);

    int insertUpdateSelective(CollageOperationLogPo record);

    int insertSelective(CollageOperationLogPo record);

    List<CollageOperationLogPo> selectByExample(CollageOperationLogPoExample example);

    CollageOperationLogPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CollageOperationLogPo record, @Param("example") CollageOperationLogPoExample example);

    int updateByExample(@Param("record") CollageOperationLogPo record, @Param("example") CollageOperationLogPoExample example);

    int updateByPrimaryKeySelective(CollageOperationLogPo record);

    int updateByPrimaryKey(CollageOperationLogPo record);
}