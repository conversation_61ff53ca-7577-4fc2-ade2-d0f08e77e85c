package com.bilibili.mgk.platform.portal.backdoor;

import com.bilibili.mgk.material.center.service.creative.InspirationCaseService;
import com.bilibili.mgk.material.center.service.creative.model.MaterialInspirationCase;
import com.bilibili.mgk.material.center.service.mainsite.AuditAdviceService;
import com.bilibili.mgk.platform.portal.common.BaseController;
import com.bilibili.mgk.platform.portal.core.Response;
import io.swagger.annotations.Api;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/5
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/backdoor/material")
@Api(value = "/backdoor/material")
public class MaterialDebugController extends BaseController {

    private final InspirationCaseService inspirationCaseService;

    private final AuditAdviceService auditAdviceService;


    @GetMapping("/img/audit/advice")
    public Response<Object> save(
            @RequestParam("url") String url) {

        return Response.SUCCESS(auditAdviceService.audit(url));
    }


    @PostMapping("/inspiration_case/save")
    public Response<MaterialInspirationCase> save(@RequestBody MaterialInspirationCase inspirationCase) {

        inspirationCaseService.save(inspirationCase);
        return Response.SUCCESS(null);
    }


    @PostMapping("/inspiration_case/query")
    public Response<MaterialInspirationCase> query(
            @RequestParam("id") Long id
    ) {

        return Response.SUCCESS(inspirationCaseService.query(id));
    }


    @PostMapping("/inspiration_case/update")
    public Response<Void> update(@RequestBody MaterialInspirationCase update) {
        inspirationCaseService.edit(update);
        return Response.SUCCESS(null);
    }


    @PostMapping("/blacklist/import")
    public Response<Void> importBlacklist(
            @RequestParam("file") MultipartFile multipartFile
    ) {

        String filename = multipartFile.getOriginalFilename();

        String ext = Optional.ofNullable(FilenameUtils.getExtension(filename)).orElse("");

        return Response.SUCCESS(null);
    }
}
