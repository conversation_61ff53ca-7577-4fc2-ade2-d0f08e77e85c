package com.bilibili.mgk.platform.biz.service.wechat.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.schedule.dto.WeiXinGdInfoDTO;
import com.bilibili.brand.api.schedule.dto.WeiXinTextDTO;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.cpt.platform.util.OkHttpUtils;
import com.bilibili.mgk.platform.api.account.dto.AccountDto;
import com.bilibili.mgk.platform.api.audit.dto.MgkAuditPageActDto;
import com.bilibili.mgk.platform.api.audit.service.IMgkAuditPageService;
import com.bilibili.mgk.platform.api.data.dto.WorkWechatDataCountDto;
import com.bilibili.mgk.platform.api.landing_page.dto.BusinessToolStatusChangeDto;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.shadow.IMgkShadowLandingPageService;
import com.bilibili.mgk.platform.api.wechat.dto.*;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWorkWxCustomerService;
import com.bilibili.mgk.platform.biz.dao.MgkLandingPageCustomerAcquisitionDao;
import com.bilibili.mgk.platform.biz.dao.MgkWorkWechatCustomerAcquisitionDao;
import com.bilibili.mgk.platform.biz.dao.MgkWorkWechatCustomerAcquisitionMappingDao;
import com.bilibili.mgk.platform.biz.databus.pub.PubBusinessToolDisable;
import com.bilibili.mgk.platform.biz.po.*;
import com.bilibili.mgk.platform.biz.service.LandingPageServiceDelegate;
import com.bilibili.mgk.platform.biz.service.MgkAccountService;
import com.bilibili.mgk.platform.biz.service.MgkBaseService;
import com.bilibili.mgk.platform.biz.service.data.delegate.MgkWechatPackageDataServiceDelegate;
import com.bilibili.mgk.platform.biz.utils.WxHttpUtils;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.mgk.platform.common.WxApiTypeEnum;
import com.bilibili.mgk.platform.common.WxCustomerAcqStatusEnum;
import com.bilibili.mgk.platform.common.WxCustomerPageStatusEnum;
import com.bilibili.mgk.platform.common.constants.WorkWxConstants;
import com.bilibili.mgk.platform.common.enums.page.BusinessToolTypeEnum;
import com.bilibili.mgk.platform.common.enums.page.OptTypeEnum;
import com.bilibili.mgk.platform.common.utils.ExampleUtils;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import com.google.common.collect.Lists;
import com.mysema.commons.lang.URLEncoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.redisson.api.RLock;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.http.utils.OkHttpUtils.JSON;
import static com.bilibili.mgk.platform.common.MgkConstants.CUSTOMER_ACQ_SYN_LOCK_SUFFIX;
import static com.bilibili.mgk.platform.common.constants.WorkWxConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class MgkWorkWxCustomerServiceImpl implements IMgkWorkWxCustomerService {

    private final MgkWorkWechatAuthServiceImpl authService;
    private final NewMgkWorkWechatAuthServiceImpl newAuthService;
    private final WxHttpUtils wxHttpUtils;
    private final MgkAccountService accountService;
    private final MgkWorkWechatCustomerAcquisitionDao customerAcquisitionDao;
    private final MgkWorkWechatCustomerAcquisitionMappingDao acquisitionMappingDao;
    private final MgkBaseService mgkBaseService;
    private final MgkLandingPageCustomerAcquisitionDao pageCustomerAcquisitionDao;
    private final MgkWechatPackageDataServiceDelegate packageDataServiceDelegate;
    private final IMgkLandingPageService landingPageService;
    private final IMgkShadowLandingPageService mgkShadowLandingPageService;
    private final LandingPageServiceDelegate pageServiceDelegate;
    private final IMgkAuditPageService mgkAuditPageService;
    private final PubBusinessToolDisable pubBusinessToolDisable;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    //授权链接
    private static final String ACCREDIT_LINK = "https://open.work.weixin.qq.com/compApp/wx52d89326b55452da";

    @Override
    public List<String> getUserList(Operator operator) throws ServiceException {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");

        JSONObject response = wxHttpUtils.httpCall(WxApiTypeEnum.GET_FOLLOW_USER, null,
                authService.getEnterpriseAccessTokenByAccountId(operator.getOperatorId()));
        JSONArray userArray = response.getJSONArray("follow_user");
        return userArray.toJavaList(String.class);
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void createCustomerAcquisitionLink(Operator operator,
                                              WorkChatCustomerAcquisitionAddDto addDto) throws ServiceException {
        List<String> userList = addDto.getUserIdList();
        validAndReturnAccount(operator, addDto.getLinkName(), userList);

        Map<String, Object> params = new HashMap<>();
        params.put("link_name", addDto.getLinkName());
        params.put("skip_verify", true);
        params.put("range", JSONObject.toJSON(WorkWechatRangeDto.builder()
                .user_list(userList).build()));

        JSONObject response = wxHttpUtils.httpCall(WxApiTypeEnum.CREATE_CUSTOMER_ACQUISITION_LINK,
                params, authService.getEnterpriseAccessTokenByAccountId(operator.getOperatorId()));

        JSONObject link = response.getJSONObject("link");
        WorkWechatLinkResDto linkDto = link.toJavaObject(WorkWechatLinkResDto.class);
        try {
            customerAcquisitionDao.insertSelective(MgkWorkWechatCustomerAcquisitionPo.builder()
                    .accountId(operator.getOperatorId()).createTime(new Timestamp(linkDto.getCreate_time() * 1000))
                    .linkId(linkDto.getLink_id()).linkName(linkDto.getLink_name()).url(linkDto.getUrl())
                    .build());

            userList.forEach(user -> acquisitionMappingDao
                    .insertSelective(MgkWorkWechatCustomerAcquisitionMappingPo.builder()
                            .linkId(linkDto.getLink_id()).userId(user).build()));
        } catch (Exception e) {
            log.error("createCustomerAcquisitionLink exception [{}]", ExceptionUtils.getSubStringMsg(e));
            Map<String, Object> delParams = new HashMap<>();
            delParams.put("link_id", linkDto.getLink_id());
            wxHttpUtils.httpCall(WxApiTypeEnum.DEL_CUSTOMER_ACQUISITION_LINK,
                    delParams, authService.getEnterpriseAccessTokenByAccountId(operator.getOperatorId()));
            throw new ServiceException("createCustomerAcquisitionLink db error");
        }
    }

    private void validAndReturnAccount(Operator operator, String linkName, List<String> userList) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.isTrue(StringUtils.hasText(linkName), "链接名称不能为空");
        Assert.isTrue(!CollectionUtils.isEmpty(userList), "最少要选择一个客服");
        userList = userList.stream().distinct().collect(Collectors.toList());
        Assert.isTrue(userList.size() <= 100, "最多选择100个客服");
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void updateCustomerAcquisitionLink(Operator operator, WorkWechatCustomerAcqLinkDto linkDto) throws ServiceException {
        List<String> userList = linkDto.getUserIdList();
        validAndReturnAccount(operator, linkDto.getLinkName(), userList);
        Assert.isTrue(StringUtils.hasText(linkDto.getLinkId()), "linkId不能为空");

        MgkWorkWechatCustomerAcquisitionPo acquisitionPo = getCustomerAcquisitionPo(linkDto.getId(), null);
        Assert.isTrue(acquisitionPo.getAccountId().equals(operator.getOperatorId()), "不允许您操作不属于您的获客链接");

        Map<String, Object> params = new HashMap<>();
        params.put("link_id", linkDto.getLinkId());
        params.put("link_name", linkDto.getLinkName());
        params.put("skip_verify", true);
        params.put("range", JSONObject.toJSON(WorkWechatRangeDto.builder()
                .user_list(userList).build()));

        wxHttpUtils.httpCall(WxApiTypeEnum.UPDATE_CUSTOMER_ACQUISITION_LINK,
                params, authService.getEnterpriseAccessTokenByAccountId(operator.getOperatorId()));

        MgkWorkWechatCustomerAcquisitionPoExample poExample = new MgkWorkWechatCustomerAcquisitionPoExample();
        poExample.or().andAccountIdEqualTo(operator.getOperatorId()).andLinkIdEqualTo(linkDto.getLinkId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        customerAcquisitionDao.updateByExampleSelective(MgkWorkWechatCustomerAcquisitionPo.builder()
                .accountId(operator.getOperatorId()).createTime(new Timestamp(System.currentTimeMillis()))
                .linkName(linkDto.getLinkName()).build(), poExample);

        MgkWorkWechatCustomerAcquisitionMappingPoExample mappingPoExample
                = new MgkWorkWechatCustomerAcquisitionMappingPoExample();
        mappingPoExample.or().andLinkIdEqualTo(linkDto.getLinkId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        acquisitionMappingDao.updateByExampleSelective(MgkWorkWechatCustomerAcquisitionMappingPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode()).build(), mappingPoExample);
        userList.forEach(user -> acquisitionMappingDao
                .insertSelective(MgkWorkWechatCustomerAcquisitionMappingPo.builder()
                        .linkId(linkDto.getLinkId()).userId(user).build()));
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void delCustomerAcquisitionLink(Operator operator, Long id, String linkId, Boolean needDelInWorkWx) throws ServiceException {
        MgkWorkWechatCustomerAcquisitionPo acquisitionPo = getCustomerAcquisitionPo(id, linkId);
        customerAcquisitionDao.updateByPrimaryKeySelective(MgkWorkWechatCustomerAcquisitionPo.builder()
                .id(acquisitionPo.getId())
                .status(WxCustomerAcqStatusEnum.INVALID.getCode()).build());


        String realLinkId = acquisitionPo.getLinkId();
        MgkWorkWechatCustomerAcquisitionMappingPoExample mappingPoExample
                = new MgkWorkWechatCustomerAcquisitionMappingPoExample();
        mappingPoExample.or().andLinkIdEqualTo(realLinkId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        acquisitionMappingDao.updateByExampleSelective(MgkWorkWechatCustomerAcquisitionMappingPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode()).build(), mappingPoExample);

        //对应的落地页下线
        MgkLandingPageCustomerAcquisitionPoExample pageCustomerAcqExample = new MgkLandingPageCustomerAcquisitionPoExample();
        pageCustomerAcqExample.or().andLinkIdEqualTo(acquisitionPo.getLinkId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(WxCustomerPageStatusEnum.VALID.getCode());
        List<MgkLandingPageCustomerAcquisitionPo> pageCustomerAcquisitionPos = pageCustomerAcquisitionDao
                .selectByExample(pageCustomerAcqExample);

        if (!CollectionUtils.isEmpty(pageCustomerAcquisitionPos)) {
            List<Long> pageIds = pageCustomerAcquisitionPos.stream().map(MgkLandingPageCustomerAcquisitionPo::getPageId)
                    .collect(Collectors.toList());
            List<MgkLandingPagePo> pos = pageServiceDelegate.getInPageIds(pageIds);
            Assert.notEmpty(pos, "落地页不存在");

            List<Long> canDownlinePageIds = pos.stream().filter(po -> LandingPageStatusEnum.getByCode(po.getStatus())
                            .validateToStatus(LandingPageStatusEnum.DOWNLINE))
                    .map(MgkLandingPagePo::getPageId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(canDownlinePageIds)) {
                mgkAuditPageService.cancelAudit(pageIds, "获客链接下线导致落地页企业微信获客不可用");
                pageServiceDelegate.batchDownline(Operator.SYSTEM, pageIds, "获客链接下线导致落地页企业微信获客不可用");
                pageCustomerAcquisitionPos.forEach(customerAcquisitionPo -> {
                    customerAcquisitionPo.setStatus(WxCustomerPageStatusEnum.INVALID.getCode());
                    pageCustomerAcquisitionDao.updateByPrimaryKeySelective(customerAcquisitionPo);
                });
            }
        }

        if (needDelInWorkWx) {
            Map<String, Object> params = new HashMap<>();
            params.put("link_id", realLinkId);
            wxHttpUtils.httpCall(WxApiTypeEnum.DEL_CUSTOMER_ACQUISITION_LINK,
                    params, authService.getEnterpriseAccessTokenByAccountId(operator.getOperatorId()));
        }

    }

    private MgkWorkWechatCustomerAcquisitionPo getCustomerAcquisitionPo(Long id, String linkId) {
        MgkWorkWechatCustomerAcquisitionPoExample poExample = new MgkWorkWechatCustomerAcquisitionPoExample();
        MgkWorkWechatCustomerAcquisitionPoExample.Criteria criteria = poExample.or()
                .andStatusEqualTo(WxCustomerAcqStatusEnum.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        ExampleUtils.notNull(id, criteria::andIdEqualTo);
        ExampleUtils.notNull(linkId, criteria::andLinkIdEqualTo);
        List<MgkWorkWechatCustomerAcquisitionPo> acquisitionPos = customerAcquisitionDao.selectByExample(poExample);
        Assert.notEmpty(acquisitionPos, "获客链接链接无效或不存在");

        return acquisitionPos.get(0);
    }


    @Override
    public PageResult<WorkWechatCustomerAcqLinkDto> queryCustomerAcqLinkListByPage(QueryWorkWechatLinkDto query,
                                                                                   int page, int size) {

        MgkWorkWechatCustomerAcquisitionPoExample poExample = buildExample(query);
        long count = customerAcquisitionDao.countByExample(poExample);
        if (count == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        Page pageBean = Page.valueOf(page, size);
        poExample.setLimit(pageBean.getLimit());
        poExample.setOffset(pageBean.getOffset());
        List<MgkWorkWechatCustomerAcquisitionPo> acquisitionPos = customerAcquisitionDao.selectByExample(poExample);

        List<WorkWechatCustomerAcqLinkDto> acqLinkDtos = buildCustomerAcqLinkDtos(acquisitionPos);
        if (Objects.equals(query.getNeedData(), true)) {
            acqLinkDtos = buildCustomerAcqLinkDataDto(acqLinkDtos, query.getBeginTime(), query.getEndTime());
        }
        return PageResult.<WorkWechatCustomerAcqLinkDto>builder().total(Math.toIntExact(count))
                .records(acqLinkDtos).build();
    }

    @Override
    public List<WorkWechatCustomerAcqLinkDto> queryCustomerAcqLinkList(QueryWorkWechatLinkDto query) {
        MgkWorkWechatCustomerAcquisitionPoExample poExample = buildExample(query);

        List<MgkWorkWechatCustomerAcquisitionPo> acquisitionPos = customerAcquisitionDao.selectByExample(poExample);

        return buildCustomerAcqLinkDtos(acquisitionPos);
    }

    @Override
    public WorkWechatCustomerAcqLinkDto getCustomerAcqLinkDtoByIdWithoutCheckStatus(Long id) {
        Assert.isTrue(Utils.isPositive(id), "getCustomerAcqLinkDtoByIdWithoutCheckStatus linkdataID is null");

        MgkWorkWechatCustomerAcquisitionPo acquisitionPo = customerAcquisitionDao.selectByPrimaryKey(id);
        return buildCustomerAcqLinkDto(acquisitionPo);
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void savePageCustomerAcqLink(long pageId, List<String> linkIds) {
        disablePageCustomerAcqLink(pageId);
        if (CollectionUtils.isEmpty(linkIds)) {
            return;
        }
        linkIds = linkIds.stream().distinct().collect(Collectors.toList());
        linkIds.forEach(linkId -> pageCustomerAcquisitionDao
                .insertSelective(MgkLandingPageCustomerAcquisitionPo.builder()
                        .linkId(linkId).pageId(pageId)
                        .status(WxCustomerPageStatusEnum.VALID.getCode()).build()));
    }

    @Override
    public String getLinkAndReplaceTrackByLinkIds(String linkId, String shortTrackId) {
        Assert.isTrue(StringUtils.hasText(linkId), "获客链接id不能为空");
        Long id = Long.parseLong(linkId);
        //防止在投放的过程中获客助手突然下线，广告没有下线，暂时先不做无效链接的判断
        MgkWorkWechatCustomerAcquisitionPoExample poExample = new MgkWorkWechatCustomerAcquisitionPoExample();
        poExample.or().andIdEqualTo(id);
        List<MgkWorkWechatCustomerAcquisitionPo> acquisitionPos = customerAcquisitionDao.selectByExample(poExample);
        if (CollectionUtils.isEmpty(acquisitionPos)) {
            log.error("[{}] getLinkByIds find link is null", linkId);
            return "";
        }
        MgkWorkWechatCustomerAcquisitionPo acquisitionPo = acquisitionPos.get(0);

        return CUSTOMER_ACQUISITION_LINK_SCHEME + URLEncoder.encodeURL(UriComponentsBuilder
                .fromHttpUrl(acquisitionPo.getUrl())
                .queryParam("customer_channel", 0 + "_" + shortTrackId
                        + "_" + acquisitionPo.getId())
                .toUriString());
    }

    @Override
    public List<String> getLinkIdsByCorpId(String corpId) {
        MgkWorkWechatCustomerAcquisitionPoExample poExample = new MgkWorkWechatCustomerAcquisitionPoExample();
        poExample.or().andCorpIdEqualTo(corpId).andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(1);
        List<MgkWorkWechatCustomerAcquisitionPo> acquisitionPos = customerAcquisitionDao.selectByExample(poExample);
        if (CollectionUtils.isEmpty(acquisitionPos)) {
            return new ArrayList<>();
        }
        return acquisitionPos.stream().map(MgkWorkWechatCustomerAcquisitionPo::getLinkId)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<Integer> getAccountIdsByCorpId(String corpId) {
        MgkWorkWechatCustomerAcquisitionPoExample poExample = new MgkWorkWechatCustomerAcquisitionPoExample();
        poExample.or().andCorpIdEqualTo(corpId).andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(1);
        List<MgkWorkWechatCustomerAcquisitionPo> acquisitionPos = customerAcquisitionDao.selectByExample(poExample);
        if (CollectionUtils.isEmpty(acquisitionPos)) {
            return new ArrayList<>();
        }
        return acquisitionPos.stream().map(MgkWorkWechatCustomerAcquisitionPo::getAccountId)
                .distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void rejectPageAndCreative(List<String> linkIds) {
        if (CollectionUtils.isEmpty(linkIds)) {
            return;
        }
        log.info("rejectPageAndCreative linkIds is[{}]", linkIds);

        MgkWorkWechatCustomerAcquisitionPoExample poExample = new MgkWorkWechatCustomerAcquisitionPoExample();
        poExample.or().andLinkIdIn(linkIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        var acquisitionPos = customerAcquisitionDao.selectByExample(poExample);
        if (CollectionUtils.isEmpty(acquisitionPos)) {
            return;
        }
        //更新获客链接状态
        customerAcquisitionDao.updateByExampleSelective(MgkWorkWechatCustomerAcquisitionPo.builder()
                        .status(WxCustomerAcqStatusEnum.INVALID.getCode()).build(),
                poExample);

        //发布删除消息
        var acquisitionPoIds = acquisitionPos.stream().map(MgkWorkWechatCustomerAcquisitionPo::getId)
                .distinct()
                .collect(Collectors.toList());
        log.info("rejectPageAndCreative acquisitionPoIds is [{}]", acquisitionPoIds);
        acquisitionPoIds.forEach(id -> pubBusinessToolDisable.businessToolDisablePub(BusinessToolStatusChangeDto.builder()
                .id(id).businessToolType(BusinessToolTypeEnum.CUSTOMER_ACQ.getCode())
                .optType(OptTypeEnum.DOWNLINE.getCode()).build()));

        //更新获客链接对应的落地页状态
        MgkLandingPageCustomerAcquisitionPoExample pageAcquisitionPoExample = new MgkLandingPageCustomerAcquisitionPoExample();
        pageAcquisitionPoExample.or().andLinkIdIn(linkIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(WxCustomerPageStatusEnum.VALID.getCode());
        List<MgkLandingPageCustomerAcquisitionPo> pageAcquisitionPos = pageCustomerAcquisitionDao
                .selectByExample(pageAcquisitionPoExample);
        if (CollectionUtils.isEmpty(pageAcquisitionPos)) {
            return;
        }
        log.info("rejectPageAndCreative pageAcquisitionPos ids is [{}]", pageAcquisitionPos.stream()
                .map(MgkLandingPageCustomerAcquisitionPo::getId).collect(Collectors.toList()));
        pageCustomerAcquisitionDao.updateByExampleSelective(MgkLandingPageCustomerAcquisitionPo.builder()
                        .status(WxCustomerPageStatusEnum.INVALID.getCode()).build(),
                pageAcquisitionPoExample);
        var pageIds = pageAcquisitionPos.stream().map(MgkLandingPageCustomerAcquisitionPo::getPageId)
                .distinct()
                .collect(Collectors.toList());
        //拒审创意
        taskExecutor.execute(() -> pageIds.forEach(pageId -> {
            try {
                Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(pageId);
                MgkLandingPageDto shadowPageDto = landingPageService.getLandingPageDtoByPageId(shadowPageId);
                landingPageService.auditReject(MgkAuditPageActDto.builder().pageId(pageId)
                        .auditorName("mgk_work_wx").reason("落地页的企微获客链接不可用")
                        .shadowVersion(shadowPageDto.getVersion()).build());
            } catch (Exception e) {
                log.error("rejectPageAndCreative fail, pageId [{}] err [{}]", pageId,
                        ExceptionUtils.getSubStringMsg(e));
            }
        }));
    }

    @Override
    public boolean isPageMappingIsModified(long pageId, List<String> linkIds) {
        Map<Long, List<String>> pageId2LinkIds = getPageCustomerAcqLinkByPageIds(Lists.newArrayList(pageId));
        Set<String> originLinkIdSet = new HashSet<>(pageId2LinkIds.getOrDefault(pageId, new ArrayList<>()));
        linkIds = linkIds == null ? new ArrayList<>() : linkIds;
        Set<String> updateLinkIdSet = new HashSet<>(linkIds);
        return originLinkIdSet.size() != updateLinkIdSet.size() || !updateLinkIdSet.equals(originLinkIdSet);
    }


    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void disablePageCustomerAcqLink(long pageId) {
        MgkLandingPageCustomerAcquisitionPoExample poExample = new MgkLandingPageCustomerAcquisitionPoExample();
        poExample.or().andPageIdEqualTo(pageId).andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(WxCustomerPageStatusEnum.VALID.getCode());
        List<MgkLandingPageCustomerAcquisitionPo> acquisitionPos = pageCustomerAcquisitionDao
                .selectByExample(poExample);
        if (!CollectionUtils.isEmpty(acquisitionPos)) {
            acquisitionPos.forEach(acquisitionPo -> {
                acquisitionPo.setStatus(WxCustomerPageStatusEnum.INVALID.getCode());
                pageCustomerAcquisitionDao.updateByPrimaryKeySelective(acquisitionPo);
            });
        }
    }

    @Override
    public Map<Long, List<String>> getPageCustomerAcqLinkByPageIds(List<Long> pageIds) {
        MgkLandingPageCustomerAcquisitionPoExample poExample = new MgkLandingPageCustomerAcquisitionPoExample();
        poExample.or().andPageIdIn(pageIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(WxCustomerPageStatusEnum.VALID.getCode());

        List<MgkLandingPageCustomerAcquisitionPo> acquisitionPos = pageCustomerAcquisitionDao
                .selectByExample(poExample);
        if (CollectionUtils.isEmpty(acquisitionPos)) {
            return new HashMap<>();
        }
        return acquisitionPos.stream().collect(Collectors.groupingBy(MgkLandingPageCustomerAcquisitionPo::getPageId,
                Collectors.mapping(MgkLandingPageCustomerAcquisitionPo::getLinkId, Collectors.toList())));
    }

    private MgkWorkWechatCustomerAcquisitionPoExample buildExample(QueryWorkWechatLinkDto query) {
        MgkWorkWechatCustomerAcquisitionPoExample poExample = new MgkWorkWechatCustomerAcquisitionPoExample();
        MgkWorkWechatCustomerAcquisitionPoExample.Criteria criteria = poExample.or()
                .andAccountIdEqualTo(query.getAccountId());
        ExampleUtils.notNull(query.getBeginTime(), criteria::andMtimeGreaterThanOrEqualTo);
        ExampleUtils.notNull(query.getEndTime(), criteria::andMtimeLessThanOrEqualTo);
        ExampleUtils.notNull(query.getStatus(), criteria::andStatusEqualTo);
        ExampleUtils.notEmpty(query.getLinkIds(), criteria::andLinkIdIn);
        ExampleUtils.notEmpty(query.getIds(), criteria::andIdIn);
        if (StringUtils.hasText(query.getNameLike())) {
            criteria.andLinkNameLike("%" + query.getNameLike() + "%");
        }
        poExample.setOrderByClause("mtime desc");

        return poExample;
    }

    private List<WorkWechatCustomerAcqLinkDto> buildCustomerAcqLinkDtos(List<MgkWorkWechatCustomerAcquisitionPo> acquisitionPos) {
        return acquisitionPos.stream().map(this::buildCustomerAcqLinkDto).collect(Collectors.toList());
    }

    private WorkWechatCustomerAcqLinkDto buildCustomerAcqLinkDto(MgkWorkWechatCustomerAcquisitionPo acquisitionPo) {
        if (acquisitionPo == null) {
            return null;
        }
        WorkWechatCustomerAcqLinkDto acqLinkDto = new WorkWechatCustomerAcqLinkDto();
        BeanUtils.copyProperties(acquisitionPo, acqLinkDto);
        acqLinkDto.setUrl(buildLinkScheme(acquisitionPo.getUrl()));
        return acqLinkDto;
    }

    public List<WorkWechatCustomerAcqLinkDto> buildCustomerAcqLinkDataDto(List<WorkWechatCustomerAcqLinkDto> linkDtos,
                                                                          Timestamp startTime, Timestamp endTime) {
        if (CollectionUtils.isEmpty(linkDtos)) {
            return linkDtos;
        }
        List<Long> linkDataIds = linkDtos.stream().map(WorkWechatCustomerAcqLinkDto::getId)
                .collect(Collectors.toList());

        Map<Long, WorkWechatDataCountDto> countDtoMap = packageDataServiceDelegate
                .queryWorkWechatDataMap(linkDataIds, true, startTime, endTime);

        return linkDtos.stream().peek(linkDto -> {
            WorkWechatDataCountDto dataCountDto = countDtoMap.getOrDefault(linkDto.getId(),
                    WorkWechatDataCountDto.getDefaultDto(linkDto.getId()));
            linkDto.setAddFans(dataCountDto.getAddFansCount());
            linkDto.setWxChat(dataCountDto.getChatCount());
            linkDto.setRecentSubmitTime(dataCountDto.getRecentSubmitTime());
        }).collect(Collectors.toList());
    }

    private String buildLinkScheme(String customerAcqLinkUrl) {
        Assert.isTrue(StringUtils.hasText(customerAcqLinkUrl), "获客链接不能为空");
        return CUSTOMER_ACQUISITION_LINK_SCHEME + URLEncoder.encodeURL(UriComponentsBuilder
                .fromHttpUrl(customerAcqLinkUrl)
                .queryParam("customer_channel", CUSTOMER_ACQUISITION_STATE)
                .toUriString());
    }

    public void sendMsg(String changeType, String corpId) {
        Assert.notNull(changeType, "sendMsg changeType is null");
        MgkWorkWechatEnterpriseInfoPo enterpriseInfoPo = authService.getEnterpriseInfoByCorpId(corpId);
        String customerName = accountService
                .getCustomerNamePoById(enterpriseInfoPo.getCustomerId());
        String msg = "";
        if (WorkWxConstants.CHANGE_TYPE_BALANCE_LOW.equals(changeType)) {
            msg = "客户[" + customerName + "], id:[" + enterpriseInfoPo.getCustomerId() + "],获客助手额度即将用尽,请提醒客户尽快充值";
        } else if (WorkWxConstants.CHANGE_TYPE_BALANCE_EXHAUSTED.equals(changeType)) {
            msg = "客户[" + customerName + "], id:[" + enterpriseInfoPo.getCustomerId() + "],获客助手额度已用尽,请提醒客户尽快充值";
        }
        Request.Builder builder = new Request.Builder();
        builder.addHeader("Content-Type", "application/json");
        builder.url("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2ae703bb-3830-446d-8d22-f79e7319e966");
        builder.post(RequestBody.create(JSON, GsonUtils.toJson(WeiXinGdInfoDTO.builder()
                .msgtype("markdown").markdown(WeiXinTextDTO.builder()
                        .content(msg).build()).build())));
        Request request = builder.build();
        OkHttpUtils.callSuccess(request);
    }

    /*
     * 获取授权链接
     */
    @Override
    public String getAccreditLink(Integer customerId) {
        return UriComponentsBuilder.fromHttpUrl(ACCREDIT_LINK).queryParam("state",
                MGK_CALL_BACK_STATE_PRE + customerId).toUriString();
    }

    /*
     * 同步获客链接
     */
//    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void synCustomerAcqLink(Integer accountId, Boolean synDel) {
        AccountDto accountDto = accountService.getAccount(accountId);
        String corpId = newAuthService.getCorpIdByAccountId(accountId);

        if (mgkBaseService.isLockEd(accountDto.getCustomerId(), CUSTOMER_ACQ_SYN_LOCK_SUFFIX)) {
            return;
        }

        RLock lock = mgkBaseService.getLock(accountDto.getCustomerId(), CUSTOMER_ACQ_SYN_LOCK_SUFFIX);
        try {

            ((MgkWorkWxCustomerServiceImpl) AopContext.currentProxy()).doSynCustomerAcqLink(accountId, synDel, corpId);

        } catch (Exception e) {
            log.error("synCustomerAcqLink fail accountId [{}] msg [{}]", accountId, ExceptionUtils.getSubStringMsg(e));
        } finally {
            lock.unlock();
        }
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public boolean doSynCustomerAcqLink(Integer accountId, Boolean synDel, String corpId) throws ServiceException {
        List<WorkWechatCustomerAcqLinkDto> acqLinkDtos = queryCustomerAcqLinkList(QueryWorkWechatLinkDto.builder()
                .accountId(accountId).status(1).build());
        List<String> oldLinkIds = acqLinkDtos.stream().map(WorkWechatCustomerAcqLinkDto::getLinkId)
                .collect(Collectors.toList());

        Set<String> linkIdSet = new HashSet<>();
        getCustomerAcqLinkS(accountId, linkIdSet, null);
        if (CollectionUtils.isEmpty(linkIdSet) && CollectionUtils.isEmpty(oldLinkIds)) {
            return true;
        }
        List<String> incLinkIds = linkIdSet.stream().filter(linkId -> !oldLinkIds.contains(linkId))
                .collect(Collectors.toList());
        log.info("synCustomerAcqLink accountId [{}] incLinkIds [{}]", accountId, incLinkIds);
        List<String> delLinkIds = new ArrayList<>();
        if (synDel) {
            //某些链接被取消授权了,需要删除
            delLinkIds = oldLinkIds.stream().filter(linkId -> !linkIdSet.contains(linkId))
                    .collect(Collectors.toList());
            log.info("synCustomerAcqLink accountId [{}] delLinkIds [{}]", accountId, delLinkIds);
        }

        Timestamp now = new Timestamp(System.currentTimeMillis());
        incLinkIds.forEach(linkId -> {
            try {
                WorkWechatLinkResDto linkResDto = getCustomerAcqDetail(linkId, accountId);
                customerAcquisitionDao.insertUpdateSelective(MgkWorkWechatCustomerAcquisitionPo.builder()
                        .linkName(linkResDto.getLink_name()).linkId(linkId).url(linkResDto.getUrl())
                        .accountId(accountId).createTime(now).corpId(corpId)
                        .status(WxCustomerAcqStatusEnum.VALID.getCode()).build());
            } catch (Exception e) {
                log.error("synCustomerAcqLink syn link [{}] fail [{}]", linkId, ExceptionUtils.getSubStringMsg(e));
            }
        });

        rejectPageAndCreative(delLinkIds);
        return false;
    }

    private String getCustomerAcqLinkS(Integer accountId, Set<String> linkIds, String cursor) throws ServiceException {
        Map<String, Object> params = new HashMap<>();
        params.put("limit", 100);
        if (StringUtils.hasText(cursor)) {
            params.put("cursor", cursor);
        }
        String accessToken = newAuthService.getEnterpriseAccessTokenByAccountId(accountId);
        if (!StringUtils.hasText(accessToken)) {
            return null;
        }
        JSONObject response = wxHttpUtils.httpCall(WxApiTypeEnum.GET_CUSTOMER_ACQUISITION_LINK_LIST, params,
                accessToken);
        JSONArray linkArray = response.getJSONArray("link_id_list");
        if (linkArray != null) {
            linkIds.addAll(linkArray.toJavaList(String.class));
        }
        String nextCursor = response.getString("next_cursor");
        while (StringUtils.hasText(nextCursor)) {
            log.info("getCustomerAcqLinkS nextCursor is [{}]", nextCursor);
            nextCursor = getCustomerAcqLinkS(accountId, linkIds, nextCursor);
        }
        return nextCursor;
    }

    private WorkWechatLinkResDto getCustomerAcqDetail(String linkId, Integer accountId) throws ServiceException {
        Map<String, Object> params = new HashMap<>();
        params.put("link_id", linkId);

        JSONObject response = wxHttpUtils.httpCall(WxApiTypeEnum.GET_CUSTOMER_ACQUISITION_LINK_DETAIL, params,
                newAuthService.getEnterpriseAccessTokenByAccountId(accountId));
        WorkWechatLinkResDto linkResDto = response.getObject("link", WorkWechatLinkResDto.class);
        Assert.notNull(linkResDto, "query link" + linkId + "fail");

        return linkResDto;
    }


}
