package com.bilibili.mgk.platform.portal.openapi.form.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhoneInfoQueryReqVo {

    //    档次A—标签ID1005（尾号ABAB）
//    档次A—标签ID1006（尾号AAAB）
//    档次A—标签ID1018（尾号AA）
//    档次B—标签ID2001（内含AAA）
//    档次B—标签ID2002（内含AABB）
//    档次D—标签ID4003（内含AA）
//    档次E—标签ID5002（末四位不带4）

    //档次
    private String num_tag;

    //标签
    private String tag_id;

    //页面数量
    @NotNull
    private Integer page_size;

    //省编码
    @NotNull
    private String province_code;

    //市编码
    @NotNull
    private String city_code;
}
