package com.bilibili.mgk.platform.portal.openapi.wechat.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName WechatAccountInfoVo
 * <AUTHOR>
 * @Date 2022/6/17 5:12 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WechatPackageInfoVo {

    @ApiModelProperty(value = " 微信包id")
    private Integer wechat_package_id;

    @ApiModelProperty(value = "微信包类型 0-个人号 1-公众号 2-企业微信 3-其他")
    private Integer type;

    @ApiModelProperty(value = "微信号")
    private WechatAccountInfoVo wechat_account;
}
