package com.bilibili.mgk.platform.portal.httplog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName HttpStatusLogEntity
 * <AUTHOR>
 * @Date 2023/2/14 12:10 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class HttpStatusLogEntity {

    private String requestUri;

    private String method;

    private String referer;

    private String userAgent;

    private Integer httpStatus;

    private Integer code;

    private String message;

}
