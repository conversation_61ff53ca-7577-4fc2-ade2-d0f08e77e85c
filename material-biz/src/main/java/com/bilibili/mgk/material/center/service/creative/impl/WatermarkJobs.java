package com.bilibili.mgk.material.center.service.creative.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.mgk.material.center.repository.mysql.MaterialWatermarkMapper;
import com.bilibili.mgk.material.center.service.creative.impl.WatermarkServiceImpl.WatermarkImgInput;
import com.bilibili.mgk.material.center.service.creative.impl.WatermarkServiceImpl.WatermarkTaskResizeParam;
import com.bilibili.mgk.material.center.service.creative.model.MaterialWatermark;
import com.bilibili.mgk.platform.common.utils.WatermarkImageUtil;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.imageio.ImageIO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/16
 */
@Slf4j
public class WatermarkJobs {

    @Data
    public static final class WatermarkJob implements Callable<byte[]> {

        private final String imgUrl;

        private final boolean isFile;

        private final WatermarkTaskResizeParam resizeParam;

        private final String format;


        public WatermarkJob(String imgUrl, boolean isFile, WatermarkTaskResizeParam resizeParam) {
            this.imgUrl = imgUrl;
            this.isFile = isFile;
            this.resizeParam = resizeParam;
            this.format = FilenameUtils.getExtension(imgUrl);
        }

        public WatermarkJob(String imgUrl, boolean isFile, WatermarkTaskResizeParam resizeParam, String format) {
            this.imgUrl = imgUrl;
            this.isFile = isFile;
            this.resizeParam = resizeParam;
            this.format = format;
        }

        @Override
        public byte[] call() {

            BufferedImage downloadImage = Try.of(() -> downloadImage(imgUrl))
                    .getOrElseThrow(t -> {
                        return new RuntimeException("无法下载水印目标原图, 图片" + imgUrl, t);
                    });

            return Try.of(() -> {
                return WatermarkImageUtil.drawWatermarkImage(
                        downloadImage,
                        resizeParam.isResizeOriginImageAndFixWatermarkCntInHorizontal(),
                        resizeParam.getWatermarkFileResourcePath(),
                        resizeParam.getDrawWatermarkCntInHorizontal(),
                        resizeParam.getResizeWatermarkImage());
            }).map(img -> {
                return img2data(img, format);
            }).getOrElseThrow(t -> {
                return new RuntimeException("执行水印处理失败,图片" + imgUrl, t);
            });

        }


        private BufferedImage downloadImage(String url) throws IOException {


            // FIXME: https://bugs.openjdk.org/browse/JDK-8119048
            // 可以通过升级jdk解决,也可以通过twelvemonkeys插件解决
            if (isFile) {
                return ImageIO.read(new File(url));
            } else {
                return ImageIO.read(new URL(url));
            }
        }




        private byte[] img2data(BufferedImage targetImg, String format) {

            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

                ImageIO.write(targetImg, format, baos);
                baos.flush();
                return baos.toByteArray();
            } catch (IOException e) {
                log.error("图片转字节失败", e);
                e.printStackTrace();
                throw new IllegalArgumentException("图片转字节失败", e);
            }
        }


    }

    @Data
    @RequiredArgsConstructor
    public static final class UploadWatermarkImgJob implements Callable<String> {

        private final IBfsService service;

        private final byte[] imgData;
        private final String targetImgName;
        /**
         * PNG， JPEG
         */


        private final boolean retryEnabled;


        private final int attempts;

        private final int retrySleepMillis;


        @Override
        public String call() {

            String catePath = getCategoryPathByFileName(targetImgName);
            BfsUploadResult bfsUploadResult = Try.of(() -> {

                        if (!retryEnabled) {
                            return service.upload(catePath, targetImgName, imgData);
                        }

                        // 401等错误 增加重试能力
                        Retryer<BfsUploadResult> retryer = RetryerBuilder.<BfsUploadResult>newBuilder()
                                .retryIfException()
                                .withWaitStrategy(WaitStrategies.fixedWait(retrySleepMillis,
                                        TimeUnit.MILLISECONDS))
                                .withStopStrategy(StopStrategies.stopAfterAttempt(attempts))
                                .build();

                        return retryer.call(() -> service.upload(catePath, targetImgName, imgData));
                    })
                    .onFailure(t -> {
                        log.error("Fail to upload watermark to bfs,img={}", targetImgName, t);
                    })
                    .getOrElseThrow(t -> {
                        return new RuntimeException("上传水印图片到文件存储失败," + targetImgName, t);
                    });

            return bfsUploadResult.getUrl();
        }


        private String getCategoryPathByFileName(String fileName) {
            Assert.hasText(fileName, "文件名称不可为空");
            String cate = StringUtils.trimWhitespace(fileName.substring(fileName.lastIndexOf(".") + 1)).toLowerCase();
            return String.format("mgk/material/%s", cate);
        }


    }

    @Data
    @RequiredArgsConstructor
    public static class WatermarkPersistJob implements Callable<List<String>> {

        private final List<Tuple2<WatermarkImgInput, String>> imgDownloadUrls;


        private final MaterialWatermarkMapper watermarkMapper;

        @Override
        public List<String> call() {

            log.info("Start to persist watermark img, img size={}", imgDownloadUrls.size());

            // 主链路会跳过该逻辑
            List<String> overwriteUrls = Try.of(() -> {

                List<MaterialWatermark> overwrites = imgDownloadUrls
                        .stream()
                        .filter(tuple -> {
                            // 和线程运行参数保持一致
                            return tuple._1.isForceOverwrite();
                        }).map(tuple -> {
                            return new MaterialWatermark()
                                    .setImgMd5(tuple._1.getImgMd5())

                                    .setImgWmUrl(tuple._2)
                                    .setMtime(new Date());
                        }).collect(Collectors.toList());

                log.info("Start to try overwrite watermark imgs, overwrites={}", overwrites);

                overwrites.forEach(watermark -> {
                    Try.run(() -> {
                        watermarkMapper.updateByMd5Selective(watermark);
                    }).onFailure(t -> {
                        log.error("Fail to overrite watermark img={}", watermark, t);
                    });
                });

                return overwrites.stream().map(MaterialWatermark::getImgWmUrl).collect(Collectors.toList());
            }).getOrElse(new ArrayList<>());




            List<MaterialWatermark> total = imgDownloadUrls
                    .stream()
                    .filter(tuple->{
                        // 和线程运行参数保持一致
                        return !tuple._1.isForceOverwrite();
                    }).map(tuple -> {
                return new MaterialWatermark()
                        .setImgMd5(tuple._1.getImgMd5())
                        .setImgWmMd5("")
                        .setImgUrl(tuple._1.getImgUrl())
                        .setImgWmUrl(tuple._2)
                        .setImgSize(0L)
                        .setImgWmSize(0L)
                        .setExtra("")
                        .setCtime(new Date())
                        .setMtime(new Date())
                        .setDeleted(false)
                        .setWmType(0);
            }).collect(Collectors.toList());

            List<String> insertsUrls = ListUtils.partition(total, 100).stream().flatMap(batch -> {
                try {
                    watermarkMapper.insertBatch(batch);

                    return batch.stream().map(MaterialWatermark::getImgWmUrl);


                } catch (Throwable t) {
                    log.warn("Fail to insert batch, try insert step by step, data={}", imgDownloadUrls, t);
                    return batch.stream()
                            .map(single -> Try
                                    .of(() -> {
                                        watermarkMapper.insertBatch(Lists.newArrayList(single));
                                        return single;
                                    })
                                    .onFailure(t2 -> {
                                        log.error("Fail to insert watermark img={}", single, t2);
                                    })
                                    .getOrNull())
                            .filter(Objects::nonNull).map(MaterialWatermark::getImgWmUrl);
                }
            }).collect(Collectors.toList());

            return imgDownloadUrls.stream().map(tuple -> tuple._2).collect(Collectors.toList());


        }


    }

    @Data
    @RequiredArgsConstructor
    public static final class GifWatermarkJob implements Callable<byte[]> {

        private final String bgImgResourceFilePath;

        private final String imgUrl;

        private final Integer watermarkCntInHorizontal;

        /**
         * convert sample_input.gif -resize %s frame-%02d.gif
         * <p>
         * convert '%s' '%s'
         */
        private final String decodeWebpFrameCmdTpl;

        /**
         * convert -delay 10 -loop 0 frame-*.gif sample_output.gif
         */
        private final String encodeFrameToWebpCmdTpl;


        private final String tmpDirPath;

        @Override
        public byte[] call() {

            // 所有名称统一带有后缀".webp"

            // 下载
            String tmpInputWebpFileName = readWebpMngAndSaveTmpFile();

            // 拆帧
            Tuple2<String, List<String>> frameFileNames = decodeWebp2Frames(tmpInputWebpFileName);

            AtomicReference<BufferedImage> resizedFixedWatermarkImage = new AtomicReference<>();

            // 逐帧加水印
            List<String> watermarkFrameFileNames = frameFileNames._2.stream().map(framFilePath -> {

                return Try.of(() -> {

                    BufferedImage bgWatermarkImage = resizedFixedWatermarkImage.get();

                    if (bgWatermarkImage == null) {

                        BufferedImage watermarkImg =
                                ImageIO.read(WatermarkServiceImpl.class.getClassLoader()
                                        .getResourceAsStream(bgImgResourceFilePath));
                        BufferedImage originImg = ImageIO.read(new File(framFilePath));

                        int wmTargetWidth = Math.max(1, originImg.getWidth() / watermarkCntInHorizontal);

                        int wmTargetHeight = Math.max(1,
                                wmTargetWidth * watermarkImg.getHeight() / (watermarkImg.getWidth()));

                        bgWatermarkImage = WatermarkImageUtil.resizeImage(watermarkImg, wmTargetWidth, wmTargetHeight);
                        resizedFixedWatermarkImage.set(bgWatermarkImage);

                    }

                    byte[] watermarkImg = new WatermarkJob(framFilePath, true,
                            new WatermarkTaskResizeParam()
                                    .setResizeOriginImageAndFixWatermarkCntInHorizontal(true)
//                                    .setWatermarkFileResourcePath(bgImgResourceFilePath)
//                                    .setDrawWatermarkCntInHorizontal(watermarkCntInHorizontal)
                                    .setResizeOriginImageAndFixWatermarkCntInHorizontal(false)
                                    .setResizeWatermarkImage(bgWatermarkImage)
                    ).call();

                    String watermarkFrameFileName = framFilePath.replace(".gif", "_wm.gif");

                    FileUtil.writeBytes(watermarkImg, new File(watermarkFrameFileName));

                    log.info("write gif frame to file={}", watermarkFrameFileName);
                    return watermarkFrameFileName;
                }).getOrNull();
//                        .getOrNull(t -> {
//                    throw new RuntimeException("gif动图逐帧加水印失败:" + imgUrl, t);
//                });

            }).filter(Objects::nonNull).collect(Collectors.toList());

            // 合成动图
            try {
                return encodeFrames2Webp(
                        tmpInputWebpFileName.replace(".gif", "_output.gif"),
                        frameFileNames._1.replace("_frame_*.gif", "_frame_*_wm.gif")
                );
            } finally {
                Stream.concat(frameFileNames._2.stream(),
                        watermarkFrameFileNames.stream()
                ).forEach(file -> {
                    Try.run(() -> FileUtil.del(file));
                });

            }


        }


        private String readWebpMngAndSaveTmpFile() {

            InputStream fileStream = HttpUtil.createGet(imgUrl).execute().bodyStream();

            String extension = FilenameUtils.getExtension(imgUrl);

            String tmpInputWebpFileName = (tmpDirPath + FilenameUtils.getName(imgUrl))
                    .replace("." + extension, "_input.gif");

            FileUtil.writeFromStream(fileStream, new File(tmpInputWebpFileName));

            return tmpInputWebpFileName;
        }


        private byte[] encodeFrames2Webp(String outputFileName, String watermarkFileReg) {

            String encodeCmd = String.format(
                    encodeFrameToWebpCmdTpl, watermarkFileReg, outputFileName
            );

            try {

                Runtime.getRuntime().exec(encodeCmd).waitFor();
                log.info("execute encode gif cmd={} ", encodeCmd);

            } catch (InterruptedException | IOException e) {
                throw new RuntimeException("gif逐帧合并失败", e);
            }

            return Try.of(() -> {
                return FileUtils.readFileToByteArray(new File(outputFileName));
            }).getOrElseThrow(t -> {
                throw new RuntimeException("读取合并gif动图失败", t);
            });

        }


        /**
         * @param tmpInputWebpFileName
         * @return frame reg
         */
        private Tuple2<String, List<String>> decodeWebp2Frames(String tmpInputWebpFileName) {

            String frameOutputFileName = tmpInputWebpFileName.replace(".gif", "_frame_%02d.gif");

            String decodeCmd = String.format(
                    decodeWebpFrameCmdTpl,
                    tmpInputWebpFileName,
//                    targetWidth + "x" + targetWidth,
                    frameOutputFileName
            );

            log.info("execute decode gif cmd={}", decodeCmd);
            String rsp = tmpInputWebpFileName.replace(".gif", "_frame_*.gif");

            String reg = tmpInputWebpFileName.replace(".gif", "_frame_.*.gif");

            try {
                int code = Runtime.getRuntime().exec(decodeCmd).waitFor();

                File dir = FileUtil.mkParentDirs(tmpInputWebpFileName);

                Pattern pattern = Pattern.compile(reg);
                List<String> files = Arrays.stream(dir.listFiles(new FileFilter() {
                    @Override
                    public boolean accept(File pathname) {
                        return pattern.matcher(pathname.getAbsolutePath()).matches();
                    }
                })).map(file -> file.getAbsolutePath()).collect(Collectors.toList());

                return Tuple.of(rsp, files);

            } catch (Throwable e) {
                throw new RuntimeException("gif拆帧失败", e);
            }


        }

    }

    @Data
    @RequiredArgsConstructor
    public static final class WebpWatermarkJob implements Callable<byte[]> {

        private final String bgImgResourceFilePath;

        private final String imgUrl;

        private final Integer watermarkCntInHorizontal;

        /**
         * mac: webpmux -get frame $i $input -o $output linux: webpmux -get_frame $i $input -o $output
         */
        private final String decodeWebpFrameCmdTpl;

        /**
         * webpmux -frame output_frame_1.webp +40+0+0+0 -frame output_frame_2.webp +40+0+0+0 -frame output_frame_3.webp
         * +40+0+0+0 ... -frame output_frame_38.webp +40+0+0+0 -loop 0 -bgcolor 255,255,255,255 -o output_animate.webp
         * <p>
         * webpmux $frame_args -loop 0 -bgcolor 255,255,255,255 -o output_animate.webp
         */
        private final String encodeFrameToWebpCmdTpl;

        /**
         * -frame $framefile.webp +40+0+0+0
         */
        private final String frameArgTpl;

        private final String tmpDirPath;

        @Override
        public byte[] call() {

            // 所有名称统一带有后缀".webp"

            // 下载
            String tmpInputWebpFileName = readWebpMngAndSaveTmpFile();

            // 拆帧
            List<String> frameFileNames = decodeWebp2Frames(tmpInputWebpFileName);

            // 逐帧加水印
            List<String> watermarkFrameFileNames = frameFileNames.stream().map(frame -> {

                return Try.of(() -> {

                    byte[] watermarkImg = new WatermarkJob(frame, true,

                            new WatermarkTaskResizeParam(
                                    true, bgImgResourceFilePath, watermarkCntInHorizontal, null
                            )).call();

                    String watermarkFrameFileName = frame.replace(".webp", "_wm.webp");

                    FileUtil.writeBytes(watermarkImg, new File(watermarkFrameFileName));

                    log.info("write webp frame to file={}", watermarkFrameFileName);
                    return watermarkFrameFileName;
                }).getOrElseThrow(t -> {
                    throw new RuntimeException("webp动图逐帧加水印失败" + imgUrl, t);
                });

            }).collect(Collectors.toList());

            try {
                // 合成动图
                return encodeFrames2Webp(
                        tmpInputWebpFileName.replace(".webp", "_output.webp"),
                        watermarkFrameFileNames
                );
            } finally {
                Stream.concat(frameFileNames.stream(), watermarkFrameFileNames.stream()).forEach(file -> {
                    Try.run(() -> FileUtil.del(file));
                });

            }


        }


        private String readWebpMngAndSaveTmpFile() {

            InputStream fileStream = HttpUtil.createGet(imgUrl).execute().bodyStream();

            String extension = FilenameUtils.getExtension(imgUrl);

            String tmpInputWebpFileName = (tmpDirPath + FilenameUtils.getName(imgUrl))
                    .replace("." + extension, "_input.webp");

            FileUtil.writeFromStream(fileStream, new File(tmpInputWebpFileName));

            return tmpInputWebpFileName;
        }


        private byte[] encodeFrames2Webp(String outputFileName, List<String> watermarkFrameFileNames) {
            String frameArg = Joiner.on(" ").join(watermarkFrameFileNames.stream().map(watermarkFrameFileName -> {
                return String.format(frameArgTpl, watermarkFrameFileName);
            }).collect(Collectors.toList()));

            String encodeCmd = String.format(
                    encodeFrameToWebpCmdTpl, frameArg, outputFileName
            );

            try {

                Runtime.getRuntime().exec(encodeCmd).waitFor();
                log.info("execute encode webp cmd={} ", encodeCmd);

            } catch (InterruptedException | IOException e) {
                throw new RuntimeException("webp逐帧合并失败", e);
            }

            return Try.of(() -> {
                return FileUtils.readFileToByteArray(new File(outputFileName));
            }).getOrElseThrow(t -> {
                throw new RuntimeException("读取合并webp动图失败", t);
            });

        }


        private List<String> decodeWebp2Frames(String tmpInputWebpFileName) {
            boolean stop = false;

            int frameId = 1;

            List<String> frameFileNames = new ArrayList<>();
            Throwable throwable = null;
            // decode
            while (!stop) {

                try {

                    String frameOutputFileName = tmpInputWebpFileName.replace(".webp", "_" + frameId + ".webp");

                    String decodeCmd = String.format(
                            decodeWebpFrameCmdTpl, frameId++,
                            tmpInputWebpFileName,
                            frameOutputFileName
                    );

                    log.info("execute decode webp cmd={}", decodeCmd);

                    int code = Runtime.getRuntime().exec(decodeCmd).waitFor();

                    if (code == 0) {
                        frameFileNames.add(frameOutputFileName);
                    } else {
                        stop = true;
                    }


                } catch (Throwable t) {
                    throwable = t;
                    stop = true;
                }
            }

            if (CollectionUtils.isEmpty(frameFileNames)) {
                if (throwable == null) {
                    throw new RuntimeException("webp动图拆帧失败" + imgUrl);
                } else {
                    throw new RuntimeException("webp动图拆帧失败" + imgUrl, throwable);
                }
            }

            return frameFileNames;
        }
    }
}
