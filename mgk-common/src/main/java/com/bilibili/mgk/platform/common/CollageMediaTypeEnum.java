package com.bilibili.mgk.platform.common;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/09
 **/
@AllArgsConstructor
public enum CollageMediaTypeEnum {
    PIC_FILE(1, "图片类型"),
    GIF_FILE(2, "GIF类型");

    public static final List<Integer> COLLAGE_MEDIA_TYPE_ENUM_LIST = Lists.newArrayList(CollageMediaTypeEnum.PIC_FILE.getCode(), CollageMediaTypeEnum.GIF_FILE.getCode());

    @Getter
    private Integer code;
    private String desc;

    public static CollageMediaTypeEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("图层媒体文件格式不可为空");
        }
        for (CollageMediaTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }

        throw new IllegalArgumentException("未知的图层媒体文件格式");
    }
}
