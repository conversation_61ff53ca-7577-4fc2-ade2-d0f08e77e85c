package com.bilibili.mgk.platform.portal.webapi.collage;

import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.collage.api.service.ICollageMediaService;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.service.WebCollageService;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.QueryCollageMediaVo;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.UpdateMediaVo;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static org.mockito.Mockito.mock;

/**
 * <AUTHOR>
 * @date 2020/09/14
 **/
public class CollageMediaControllerTest extends BaseMockitoTest {
    @InjectMocks
    private CollageMediaController collageMediaController;

    @Mock
    private WebCollageService webCollageService;

    @Mock
    private ICollageMediaService collageMediaService;


    Context context;

    @Before
    public void setUp() throws Exception {
        context = new Context();
        context.setAccountId(10005);
        context.setType(OperatorType.SYSTEM.getCode());
        context.setProxyName("SYS_TEST");
        context.setSalesType(SalesType.CPM.getCode());
        context.setUsername("bilibili_user");
        context.setProxyId(1);
    }

    @Test
    public void testGetList() throws Exception {
        collageMediaController.getList(context, QueryCollageMediaVo.builder().build());
    }

    @Test
    public void testMediaUpload() throws IOException, ServiceException {
        MultipartFile multipartFile = mock(MultipartFile.class);
        collageMediaController.mediaUpload(context, multipartFile);
    }

    @Test
    public void testMediaLimitUpload() throws IOException, ServiceException {
        MultipartFile multipartFile = mock(MultipartFile.class);
        collageMediaController.mediaLimitUpload(context, multipartFile, 1000);
    }

    @Test
    public void testEditName() {
        collageMediaController.editName(context, UpdateMediaVo.builder().build());
    }

    @Test
    public void testBatchDeleted() {
        collageMediaController.batchDeleted(context, Lists.newArrayList(1));
    }

    @Test
    public void testSingleDeleted() {
        collageMediaController.singleDeleted(context, 1);
    }
}