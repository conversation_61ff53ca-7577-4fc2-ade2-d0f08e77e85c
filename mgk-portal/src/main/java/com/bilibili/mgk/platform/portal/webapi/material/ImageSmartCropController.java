package com.bilibili.mgk.platform.portal.webapi.material;

import com.bilibili.mgk.material.center.service.aigc.SmartCropService;
import com.bilibili.mgk.material.center.service.aigc.model.CropOriginImage;
import com.bilibili.mgk.material.center.service.aigc.model.CropResultImage;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.webapi.material.vo.SmartCropReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/14
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/aigc/image")
@Api(value = "/material/aigc", tags = "material-center")
public class ImageSmartCropController extends BasicController {


    @Resource
    private SmartCropService smartCropService;


    @SneakyThrows
    @ApiOperation("智能裁切，必须登录")
    @RequestMapping(value = "/crop", method = RequestMethod.POST)
    public Response<List<CropResultImage>> listCoverDerivation(

            @ApiIgnore Context context,
            @RequestBody SmartCropReq smartCropReq

    ) {

        return Response.SUCCESS(smartCropService.smartCrop(new CropOriginImage()
                .setOriginImgMd5(smartCropReq.getMd5())
                .setOriginImgUrl(smartCropReq.getUrl())
                .setOriginImgHeight(smartCropReq.getHeight())
                .setOriginImgWidth(smartCropReq.getWidth()
                )));
    }

}
