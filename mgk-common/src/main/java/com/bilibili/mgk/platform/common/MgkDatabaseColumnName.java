package com.bilibili.mgk.platform.common;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * @ClassName MgkPageOperateColumnName
 * <AUTHOR>
 * @Date 2022/12/19 2:14 上午
 * @Version 1.0
 **/
@Retention(RUNTIME)
@Target(FIELD)
public @interface MgkDatabaseColumnName {
    public String value();
    public boolean needShowForLog() default true;
    public boolean needFalseShow() default false;
}
