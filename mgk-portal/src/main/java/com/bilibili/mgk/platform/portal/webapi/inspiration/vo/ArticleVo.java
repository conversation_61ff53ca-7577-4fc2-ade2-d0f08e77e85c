package com.bilibili.mgk.platform.portal.webapi.inspiration.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @file: ArticleVo
 * @author: gaoming
 * @date: 2021/03/24
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class ArticleVo {
    @ApiModelProperty(value = "文章id")
    private String article_id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "封面地址")
    private String cover;

    @ApiModelProperty(value = "行业 1-电商 2-游戏 3-网服 4-教育 5-其他")
    private String industry;

    @ApiModelProperty(value = "阅读数")
    private String article_read;

    @ApiModelProperty(value = "点赞数")
    private String article_like;

    @ApiModelProperty(value = "状态 0-启用 1-禁用")
    private Integer article_status;

    @ApiModelProperty(value = "创建时间")
    private Timestamp ctime;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "是否点赞 0-没有点赞 1-点赞")
    private Integer is_like;

    @ApiModelProperty(value = "用户名称")
    private String creator;
}
