package com.bilibili.mgk.material.center.service.asset.model;

import com.bapis.dynamic.admin.feed.CommerceDyn;
import com.bilibili.mgk.material.center.service.asset.vo.RichTextDTO;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicBluelinkComponentAudit;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicBluelinkResolveContent;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicComponentAuditWithBluelink;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.bilibili.mgk.material.center.service.mainsite.model.DynamicFilterType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/8
 */
@Data
@Accessors(chain = true)
public class MaterialDynamicInfo implements SnakeCaseBody {

    @ApiModelProperty(value = "动态id")
    private String dynId;

    @ApiModelProperty(value = "动态类型")
    private String dynRid;

    @ApiModelProperty(value = "动态类型")
    private String dynType;


    // owner
    @ApiModelProperty(value = "用户id")
    private Long uid;

    @ApiModelProperty(value = "用户名称")
    private String upName;

    @ApiModelProperty(value = "用户头像")
    private String upFace;


    @ApiModelProperty(value = "动态标题")
    private String title;

    @ApiModelProperty(value = "动态图片")
    private List<DynamicDrawPic> pics;

    @ApiModelProperty(value = "预约id")
    private Long reserveId;

    // 预约id
    @ApiModelProperty(value = "调度任务id")
    private Long scheduleId;


    @ApiModelProperty(value = "发布时间,单位秒")
    private Long pubTime;

    @ApiModelProperty(value = "动态内容")
    private String content;

    @ApiModelProperty(value = "动态内容v2，支持富文本")
    private List<RichTextDTO> richContent;

    // 动态是否支持暗投

    @ApiModelProperty(value = "是否支持暗投")
    private Boolean sneakingVisible;

    // 联合投稿动态信息
    @ApiModelProperty(value = "联合投稿信息")
    private ShadowInfo shadowInfo;


    @ApiModelProperty(value = "定时发布调度时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime scheduleTime;


    @ApiModelProperty(value = "动态发布状态")
    private Integer publishStatus;


    @ApiModelProperty(value = "错误码，只有定时发布失败才存在")
    private Integer errCode;


    @ApiModelProperty(value = "错误信息，只有定时发布失败才存在")
    private String errMsg;


    @ApiModelProperty("商务审核状态, 0:未审核, 1:审核通过, 2:审核不通过")
    private Integer commerceAuditStatus;


    @ApiModelProperty("商业侧审核原因")
    private String commerceAuditReason;


    @ApiModelProperty("商务审核状态描述, 商业审核中, 商业审核驳回")
    private String commerceAuditStatusDesc;


    public static MaterialDynamicInfo fromGrpcResp(CommerceDyn commerceDyn) {
        MaterialDynamicInfo materialDynamicInfo = new MaterialDynamicInfo();

        // 动态id
        materialDynamicInfo.setDynId(String.valueOf(commerceDyn.getDynId()));
        materialDynamicInfo.setDynRid(String.valueOf(commerceDyn.getRid()));
        materialDynamicInfo.setDynType(String.valueOf(commerceDyn.getType()));

        // 用户信息
        materialDynamicInfo.setUid(commerceDyn.getOwner().getUid());
        materialDynamicInfo.setUpName(commerceDyn.getOwner().getName());
        materialDynamicInfo.setUpFace(commerceDyn.getOwner().getFace());

        // 文本信息
        materialDynamicInfo.setTitle(commerceDyn.getTitle());
        materialDynamicInfo.setPics(commerceDyn.getPicturesList()
                .stream()
                .map(DynamicDrawPic::fromGrpcResp)
                .collect(Collectors.toList()));
        materialDynamicInfo.setReserveId(commerceDyn.getReserveId());
        materialDynamicInfo.setPubTime(commerceDyn.getPubTime());
        materialDynamicInfo.setContent(commerceDyn.getContent());
        materialDynamicInfo.setSneakingVisible(commerceDyn.getSneakingVisiable());
        materialDynamicInfo.setShadowInfo(ShadowInfo.fromGrpcResp(commerceDyn.getShadowInfo()));
        materialDynamicInfo.setPublishStatus(DynamicFilterType
                .filterType2PublishStatus((int) commerceDyn.getFilterType()));

        return materialDynamicInfo;

    }

    /**
     * @param info 真心不想用DynamicBlueLinkDetailsBo这个封装
     * @return
     */
    public static MaterialDynamicInfo fromCommerceAuditInfo(DynamicComponentAuditWithBluelink auditInfo) {

        DynamicBluelinkComponentAudit info = auditInfo.getAudit();
        List<DynamicBluelinkResolveContent> bluelinks = auditInfo.getBluelinks();

        MaterialDynamicInfo materialDynamicInfo = new MaterialDynamicInfo();

        // 动态id
        materialDynamicInfo.setDynId(info.getDynamicId());
//        materialDynamicInfo.setDynRid(String.valueOf(commerceDyn.getRid()));
//        materialDynamicInfo.setDynType(String.valueOf(commerceDyn.getType()));

        // 用户信息
        materialDynamicInfo.setUid(info.getMid());
        materialDynamicInfo.setUpName(info.getUpNickname());
//        materialDynamicInfo.setUpFace(commerceDyn.getOwner().getFace());

        // 文本信息
        materialDynamicInfo.setTitle(info.getDynamicTitle());
        materialDynamicInfo.setPics(info.getDynamicPics());
        materialDynamicInfo.setReserveId(null);

        // 主站是秒！这里fellow主站，
        materialDynamicInfo.setPubTime(info.getCtime().toEpochSecond(ZoneOffset.of("+8")));

        materialDynamicInfo.setContent(auditInfo.getAudit().getDynamicContent());

        materialDynamicInfo.setRichContent(auditInfo.asRichTextDTO());

        materialDynamicInfo.setSneakingVisible(true);
        materialDynamicInfo.setShadowInfo(new ShadowInfo());
        materialDynamicInfo.setPublishStatus(
                DynamicPublishStatus.fromCommerceAuditStatus(info.getAuditStatus()).getCode());
        materialDynamicInfo.setCommerceAuditReason(auditInfo.getAudit().getReason());

        return materialDynamicInfo;

    }


    /**
     * 联合投稿信息
     */
    @Data
    public static class ShadowInfo implements SnakeCaseBody {

        @ApiModelProperty(value = "源动态信息")
        private ShadowItem originDynInfo;

        @ApiModelProperty(value = "影子投稿信息")
        private List<ShadowItem> shadowDynInfo;


        public static ShadowInfo fromGrpcResp(com.bapis.dynamic.admin.feed.ShadowInfo shadow) {
            ShadowInfo shadowInfo = new ShadowInfo();
            shadowInfo.setOriginDynInfo(ShadowItem.fromGrpcResp(shadow.getOriginDynInfo()));
            shadowInfo.setShadowDynInfo(shadow.getShadowDynInfoList()
                    .stream()
                    .map(item -> ShadowItem.fromGrpcResp(item))
                    .collect(Collectors.toList())
            );

            return shadowInfo;

        }


        @Data
        public static class ShadowItem implements SnakeCaseBody {

            @ApiModelProperty(value = "动态id")
            private Long dynId;


            @ApiModelProperty(value = "用户id")
            private Long uid;


            @ApiModelProperty(value = "用户id类型")
            private Long uidType;


            public static ShadowItem fromGrpcResp(com.bapis.dynamic.admin.feed.ShadowInfo.ShadowItem shadow) {
                ShadowItem shadowItem = new ShadowItem();
                shadowItem.setDynId(shadow.getDynId());
                shadowItem.setUid(shadow.getUid());
                shadowItem.setUidType(shadow.getUidType());
                return shadowItem;
            }

        }
    }


}
