/** 
* <AUTHOR> 
* @date  2018年5月18日
*/ 

package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Tag implements Serializable {

	private static final long serialVersionUID = -519716344481184459L;
	
	private String text;
    private BigDecimal position_x;
    private BigDecimal position_y;
    private String jump_url;
    private List<String> report_urls;
}
