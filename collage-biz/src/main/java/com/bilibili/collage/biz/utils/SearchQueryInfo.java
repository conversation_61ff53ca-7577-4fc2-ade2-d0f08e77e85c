package com.bilibili.collage.biz.utils;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;

import java.util.List;


public class SearchQueryInfo {

    /**
     * es filter过滤条件
     */
    private BoolQueryBuilder filterBuilder;
    /**
     * es match条件
     */
    private BoolQueryBuilder queryBuilder;
    /**
     * es 排序条件
     */
    private List<SortBuilder> sortBuilders;

    public BoolQueryBuilder getFilterBuilder() {
        return filterBuilder;
    }

    public void setFilterBuilder(BoolQueryBuilder filterBuilder) {
        this.filterBuilder = filterBuilder;
    }

    public BoolQueryBuilder getQueryBuilder() {
        return queryBuilder;
    }

    public void setQueryBuilder(BoolQueryBuilder queryBuilder) {
        this.queryBuilder = queryBuilder;
    }

    public List<SortBuilder> getSortBuilders() {
        return sortBuilders;
    }

    public void setSortBuilders(List<SortBuilder> sortBuilders) {
        this.sortBuilders = sortBuilders;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("SearchQueryInfo{");
        sb.append("filterBuilder=").append(filterBuilder);
        sb.append(", queryBuilder=").append(queryBuilder);
        sb.append(", sortBuilders=").append(sortBuilders);
        sb.append('}');
        return sb.toString();
    }
}
