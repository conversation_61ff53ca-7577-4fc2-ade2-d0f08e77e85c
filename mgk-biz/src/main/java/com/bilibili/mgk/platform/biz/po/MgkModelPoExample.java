package com.bilibili.mgk.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MgkModelPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MgkModelPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andModelIdIsNull() {
            addCriterion("model_id is null");
            return (Criteria) this;
        }

        public Criteria andModelIdIsNotNull() {
            addCriterion("model_id is not null");
            return (Criteria) this;
        }

        public Criteria andModelIdEqualTo(Long value) {
            addCriterion("model_id =", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdNotEqualTo(Long value) {
            addCriterion("model_id <>", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdGreaterThan(Long value) {
            addCriterion("model_id >", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("model_id >=", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdLessThan(Long value) {
            addCriterion("model_id <", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdLessThanOrEqualTo(Long value) {
            addCriterion("model_id <=", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdIn(List<Long> values) {
            addCriterion("model_id in", values, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdNotIn(List<Long> values) {
            addCriterion("model_id not in", values, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdBetween(Long value1, Long value2) {
            addCriterion("model_id between", value1, value2, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdNotBetween(Long value1, Long value2) {
            addCriterion("model_id not between", value1, value2, "modelId");
            return (Criteria) this;
        }

        public Criteria andPageIdIsNull() {
            addCriterion("page_id is null");
            return (Criteria) this;
        }

        public Criteria andPageIdIsNotNull() {
            addCriterion("page_id is not null");
            return (Criteria) this;
        }

        public Criteria andPageIdEqualTo(Long value) {
            addCriterion("page_id =", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotEqualTo(Long value) {
            addCriterion("page_id <>", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdGreaterThan(Long value) {
            addCriterion("page_id >", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdGreaterThanOrEqualTo(Long value) {
            addCriterion("page_id >=", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdLessThan(Long value) {
            addCriterion("page_id <", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdLessThanOrEqualTo(Long value) {
            addCriterion("page_id <=", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdIn(List<Long> values) {
            addCriterion("page_id in", values, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotIn(List<Long> values) {
            addCriterion("page_id not in", values, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdBetween(Long value1, Long value2) {
            addCriterion("page_id between", value1, value2, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotBetween(Long value1, Long value2) {
            addCriterion("page_id not between", value1, value2, "pageId");
            return (Criteria) this;
        }

        public Criteria andModelVersionIsNull() {
            addCriterion("model_version is null");
            return (Criteria) this;
        }

        public Criteria andModelVersionIsNotNull() {
            addCriterion("model_version is not null");
            return (Criteria) this;
        }

        public Criteria andModelVersionEqualTo(String value) {
            addCriterion("model_version =", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionNotEqualTo(String value) {
            addCriterion("model_version <>", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionGreaterThan(String value) {
            addCriterion("model_version >", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionGreaterThanOrEqualTo(String value) {
            addCriterion("model_version >=", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionLessThan(String value) {
            addCriterion("model_version <", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionLessThanOrEqualTo(String value) {
            addCriterion("model_version <=", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionLike(String value) {
            addCriterion("model_version like", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionNotLike(String value) {
            addCriterion("model_version not like", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionIn(List<String> values) {
            addCriterion("model_version in", values, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionNotIn(List<String> values) {
            addCriterion("model_version not in", values, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionBetween(String value1, String value2) {
            addCriterion("model_version between", value1, value2, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionNotBetween(String value1, String value2) {
            addCriterion("model_version not between", value1, value2, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelNameIsNull() {
            addCriterion("model_name is null");
            return (Criteria) this;
        }

        public Criteria andModelNameIsNotNull() {
            addCriterion("model_name is not null");
            return (Criteria) this;
        }

        public Criteria andModelNameEqualTo(String value) {
            addCriterion("model_name =", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotEqualTo(String value) {
            addCriterion("model_name <>", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameGreaterThan(String value) {
            addCriterion("model_name >", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("model_name >=", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLessThan(String value) {
            addCriterion("model_name <", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLessThanOrEqualTo(String value) {
            addCriterion("model_name <=", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLike(String value) {
            addCriterion("model_name like", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotLike(String value) {
            addCriterion("model_name not like", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameIn(List<String> values) {
            addCriterion("model_name in", values, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotIn(List<String> values) {
            addCriterion("model_name not in", values, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameBetween(String value1, String value2) {
            addCriterion("model_name between", value1, value2, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotBetween(String value1, String value2) {
            addCriterion("model_name not between", value1, value2, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelTypeIsNull() {
            addCriterion("model_type is null");
            return (Criteria) this;
        }

        public Criteria andModelTypeIsNotNull() {
            addCriterion("model_type is not null");
            return (Criteria) this;
        }

        public Criteria andModelTypeEqualTo(Integer value) {
            addCriterion("model_type =", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeNotEqualTo(Integer value) {
            addCriterion("model_type <>", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeGreaterThan(Integer value) {
            addCriterion("model_type >", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("model_type >=", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeLessThan(Integer value) {
            addCriterion("model_type <", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeLessThanOrEqualTo(Integer value) {
            addCriterion("model_type <=", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeIn(List<Integer> values) {
            addCriterion("model_type in", values, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeNotIn(List<Integer> values) {
            addCriterion("model_type not in", values, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeBetween(Integer value1, Integer value2) {
            addCriterion("model_type between", value1, value2, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("model_type not between", value1, value2, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelStyleIsNull() {
            addCriterion("model_style is null");
            return (Criteria) this;
        }

        public Criteria andModelStyleIsNotNull() {
            addCriterion("model_style is not null");
            return (Criteria) this;
        }

        public Criteria andModelStyleEqualTo(Integer value) {
            addCriterion("model_style =", value, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleNotEqualTo(Integer value) {
            addCriterion("model_style <>", value, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleGreaterThan(Integer value) {
            addCriterion("model_style >", value, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleGreaterThanOrEqualTo(Integer value) {
            addCriterion("model_style >=", value, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleLessThan(Integer value) {
            addCriterion("model_style <", value, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleLessThanOrEqualTo(Integer value) {
            addCriterion("model_style <=", value, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleIn(List<Integer> values) {
            addCriterion("model_style in", values, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleNotIn(List<Integer> values) {
            addCriterion("model_style not in", values, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleBetween(Integer value1, Integer value2) {
            addCriterion("model_style between", value1, value2, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStyleNotBetween(Integer value1, Integer value2) {
            addCriterion("model_style not between", value1, value2, "modelStyle");
            return (Criteria) this;
        }

        public Criteria andModelStatusIsNull() {
            addCriterion("model_status is null");
            return (Criteria) this;
        }

        public Criteria andModelStatusIsNotNull() {
            addCriterion("model_status is not null");
            return (Criteria) this;
        }

        public Criteria andModelStatusEqualTo(Integer value) {
            addCriterion("model_status =", value, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusNotEqualTo(Integer value) {
            addCriterion("model_status <>", value, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusGreaterThan(Integer value) {
            addCriterion("model_status >", value, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("model_status >=", value, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusLessThan(Integer value) {
            addCriterion("model_status <", value, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusLessThanOrEqualTo(Integer value) {
            addCriterion("model_status <=", value, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusIn(List<Integer> values) {
            addCriterion("model_status in", values, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusNotIn(List<Integer> values) {
            addCriterion("model_status not in", values, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusBetween(Integer value1, Integer value2) {
            addCriterion("model_status between", value1, value2, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andModelStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("model_status not between", value1, value2, "modelStatus");
            return (Criteria) this;
        }

        public Criteria andCoverUrlIsNull() {
            addCriterion("cover_url is null");
            return (Criteria) this;
        }

        public Criteria andCoverUrlIsNotNull() {
            addCriterion("cover_url is not null");
            return (Criteria) this;
        }

        public Criteria andCoverUrlEqualTo(String value) {
            addCriterion("cover_url =", value, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlNotEqualTo(String value) {
            addCriterion("cover_url <>", value, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlGreaterThan(String value) {
            addCriterion("cover_url >", value, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlGreaterThanOrEqualTo(String value) {
            addCriterion("cover_url >=", value, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlLessThan(String value) {
            addCriterion("cover_url <", value, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlLessThanOrEqualTo(String value) {
            addCriterion("cover_url <=", value, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlLike(String value) {
            addCriterion("cover_url like", value, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlNotLike(String value) {
            addCriterion("cover_url not like", value, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlIn(List<String> values) {
            addCriterion("cover_url in", values, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlNotIn(List<String> values) {
            addCriterion("cover_url not in", values, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlBetween(String value1, String value2) {
            addCriterion("cover_url between", value1, value2, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCoverUrlNotBetween(String value1, String value2) {
            addCriterion("cover_url not between", value1, value2, "coverUrl");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdIsNull() {
            addCriterion("module_content_id is null");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdIsNotNull() {
            addCriterion("module_content_id is not null");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdEqualTo(Integer value) {
            addCriterion("module_content_id =", value, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdNotEqualTo(Integer value) {
            addCriterion("module_content_id <>", value, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdGreaterThan(Integer value) {
            addCriterion("module_content_id >", value, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("module_content_id >=", value, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdLessThan(Integer value) {
            addCriterion("module_content_id <", value, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdLessThanOrEqualTo(Integer value) {
            addCriterion("module_content_id <=", value, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdIn(List<Integer> values) {
            addCriterion("module_content_id in", values, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdNotIn(List<Integer> values) {
            addCriterion("module_content_id not in", values, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdBetween(Integer value1, Integer value2) {
            addCriterion("module_content_id between", value1, value2, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleContentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("module_content_id not between", value1, value2, "moduleContentId");
            return (Criteria) this;
        }

        public Criteria andModuleWeightIsNull() {
            addCriterion("module_weight is null");
            return (Criteria) this;
        }

        public Criteria andModuleWeightIsNotNull() {
            addCriterion("module_weight is not null");
            return (Criteria) this;
        }

        public Criteria andModuleWeightEqualTo(Integer value) {
            addCriterion("module_weight =", value, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightNotEqualTo(Integer value) {
            addCriterion("module_weight <>", value, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightGreaterThan(Integer value) {
            addCriterion("module_weight >", value, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("module_weight >=", value, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightLessThan(Integer value) {
            addCriterion("module_weight <", value, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightLessThanOrEqualTo(Integer value) {
            addCriterion("module_weight <=", value, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightIn(List<Integer> values) {
            addCriterion("module_weight in", values, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightNotIn(List<Integer> values) {
            addCriterion("module_weight not in", values, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightBetween(Integer value1, Integer value2) {
            addCriterion("module_weight between", value1, value2, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("module_weight not between", value1, value2, "moduleWeight");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdIsNull() {
            addCriterion("module_style_id is null");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdIsNotNull() {
            addCriterion("module_style_id is not null");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdEqualTo(Integer value) {
            addCriterion("module_style_id =", value, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdNotEqualTo(Integer value) {
            addCriterion("module_style_id <>", value, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdGreaterThan(Integer value) {
            addCriterion("module_style_id >", value, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("module_style_id >=", value, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdLessThan(Integer value) {
            addCriterion("module_style_id <", value, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdLessThanOrEqualTo(Integer value) {
            addCriterion("module_style_id <=", value, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdIn(List<Integer> values) {
            addCriterion("module_style_id in", values, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdNotIn(List<Integer> values) {
            addCriterion("module_style_id not in", values, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdBetween(Integer value1, Integer value2) {
            addCriterion("module_style_id between", value1, value2, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleStyleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("module_style_id not between", value1, value2, "moduleStyleId");
            return (Criteria) this;
        }

        public Criteria andModuleHeightIsNull() {
            addCriterion("module_height is null");
            return (Criteria) this;
        }

        public Criteria andModuleHeightIsNotNull() {
            addCriterion("module_height is not null");
            return (Criteria) this;
        }

        public Criteria andModuleHeightEqualTo(Integer value) {
            addCriterion("module_height =", value, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightNotEqualTo(Integer value) {
            addCriterion("module_height <>", value, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightGreaterThan(Integer value) {
            addCriterion("module_height >", value, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("module_height >=", value, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightLessThan(Integer value) {
            addCriterion("module_height <", value, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightLessThanOrEqualTo(Integer value) {
            addCriterion("module_height <=", value, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightIn(List<Integer> values) {
            addCriterion("module_height in", values, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightNotIn(List<Integer> values) {
            addCriterion("module_height not in", values, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightBetween(Integer value1, Integer value2) {
            addCriterion("module_height between", value1, value2, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andModuleHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("module_height not between", value1, value2, "moduleHeight");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andIsAdminIsNull() {
            addCriterion("is_admin is null");
            return (Criteria) this;
        }

        public Criteria andIsAdminIsNotNull() {
            addCriterion("is_admin is not null");
            return (Criteria) this;
        }

        public Criteria andIsAdminEqualTo(Integer value) {
            addCriterion("is_admin =", value, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminNotEqualTo(Integer value) {
            addCriterion("is_admin <>", value, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminGreaterThan(Integer value) {
            addCriterion("is_admin >", value, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_admin >=", value, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminLessThan(Integer value) {
            addCriterion("is_admin <", value, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminLessThanOrEqualTo(Integer value) {
            addCriterion("is_admin <=", value, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminIn(List<Integer> values) {
            addCriterion("is_admin in", values, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminNotIn(List<Integer> values) {
            addCriterion("is_admin not in", values, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminBetween(Integer value1, Integer value2) {
            addCriterion("is_admin between", value1, value2, "isAdmin");
            return (Criteria) this;
        }

        public Criteria andIsAdminNotBetween(Integer value1, Integer value2) {
            addCriterion("is_admin not between", value1, value2, "isAdmin");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}