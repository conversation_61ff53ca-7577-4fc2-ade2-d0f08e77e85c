<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkWechatPackageAccountMappingDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wechat_account_id" jdbcType="INTEGER" property="wechatAccountId" />
    <result column="wechat_package_id" jdbcType="INTEGER" property="wechatPackageId" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPo">
    <id column="mgk_wechat_package_account_mapping_id" jdbcType="BIGINT" property="id" />
    <result column="mgk_wechat_package_account_mapping_wechat_account_id" jdbcType="INTEGER" property="wechatAccountId" />
    <result column="mgk_wechat_package_account_mapping_wechat_package_id" jdbcType="INTEGER" property="wechatPackageId" />
    <result column="mgk_wechat_package_account_mapping_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="mgk_wechat_package_account_mapping_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mgk_wechat_package_account_mapping_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as mgk_wechat_package_account_mapping_id, ${alias}.wechat_account_id as mgk_wechat_package_account_mapping_wechat_account_id, 
    ${alias}.wechat_package_id as mgk_wechat_package_account_mapping_wechat_package_id, 
    ${alias}.is_deleted as mgk_wechat_package_account_mapping_is_deleted, ${alias}.ctime as mgk_wechat_package_account_mapping_ctime, 
    ${alias}.mtime as mgk_wechat_package_account_mapping_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, wechat_account_id, wechat_package_id, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_wechat_package_account_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_wechat_package_account_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mgk_wechat_package_account_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPoExample">
    delete from mgk_wechat_package_account_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_wechat_package_account_mapping (wechat_account_id, wechat_package_id, 
      is_deleted, ctime, mtime
      )
    values (#{wechatAccountId,jdbcType=INTEGER}, #{wechatPackageId,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_wechat_package_account_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wechatAccountId != null">
        wechat_account_id,
      </if>
      <if test="wechatPackageId != null">
        wechat_package_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wechatAccountId != null">
        #{wechatAccountId,jdbcType=INTEGER},
      </if>
      <if test="wechatPackageId != null">
        #{wechatPackageId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPoExample" resultType="java.lang.Long">
    select count(*) from mgk_wechat_package_account_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_wechat_package_account_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.wechatAccountId != null">
        wechat_account_id = #{record.wechatAccountId,jdbcType=INTEGER},
      </if>
      <if test="record.wechatPackageId != null">
        wechat_package_id = #{record.wechatPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_wechat_package_account_mapping
    set id = #{record.id,jdbcType=BIGINT},
      wechat_account_id = #{record.wechatAccountId,jdbcType=INTEGER},
      wechat_package_id = #{record.wechatPackageId,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPo">
    update mgk_wechat_package_account_mapping
    <set>
      <if test="wechatAccountId != null">
        wechat_account_id = #{wechatAccountId,jdbcType=INTEGER},
      </if>
      <if test="wechatPackageId != null">
        wechat_package_id = #{wechatPackageId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPo">
    update mgk_wechat_package_account_mapping
    set wechat_account_id = #{wechatAccountId,jdbcType=INTEGER},
      wechat_package_id = #{wechatPackageId,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_wechat_package_account_mapping (wechat_account_id, wechat_package_id, 
      is_deleted, ctime, mtime
      )
    values (#{wechatAccountId,jdbcType=INTEGER}, #{wechatPackageId,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      wechat_account_id = values(wechat_account_id),
      wechat_package_id = values(wechat_package_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_wechat_package_account_mapping
      (wechat_account_id,wechat_package_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.wechatAccountId,jdbcType=INTEGER},
        #{item.wechatPackageId,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_wechat_package_account_mapping
      (wechat_account_id,wechat_package_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.wechatAccountId,jdbcType=INTEGER},
        #{item.wechatPackageId,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      wechat_account_id = values(wechat_account_id),
      wechat_package_id = values(wechat_package_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkWechatPackageAccountMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_wechat_package_account_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wechatAccountId != null">
        wechat_account_id,
      </if>
      <if test="wechatPackageId != null">
        wechat_package_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wechatAccountId != null">
        #{wechatAccountId,jdbcType=INTEGER},
      </if>
      <if test="wechatPackageId != null">
        #{wechatPackageId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="wechatAccountId != null">
        wechat_account_id = values(wechat_account_id),
      </if>
      <if test="wechatPackageId != null">
        wechat_package_id = values(wechat_package_id),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>