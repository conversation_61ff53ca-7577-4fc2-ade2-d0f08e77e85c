package com.bilibili.mgk.material.center.service.creative.impl;

import com.bapis.archive.service.SimpleArc;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.mgk.material.center.config.NoLoginAccessConfig;
import com.bilibili.mgk.material.center.facade.RisingFastVideoFacadeImpl;
import com.bilibili.mgk.material.center.service.creative.RisingFastVideoService;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideo;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideoCurve;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideoCurvePoint;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideoCurveType;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.PaginationExt;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoCurveQuery;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoIdQuery;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoPageExtra;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoQuery;
import com.bilibili.mgk.material.center.service.mainsite.BiliAccountService;
import com.bilibili.mgk.material.center.service.mainsite.BiliArchiveService;
import com.google.common.collect.Lists;
import io.vavr.Lazy;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/24
 */
@Slf4j
@Service
public class RisingFastVideoServiceImpl implements RisingFastVideoService {

    @Resource
    private RisingFastVideoFacadeImpl risingFastVideoFacade;

    @Resource
    private BiliArchiveService biliArchiveService;

    @Resource
    private BiliAccountService biliAccountService;

    @Resource
    private NoLoginAccessConfig noLoginAccessConfig;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");



    @Override
    public RisingFastVideoPageExtra candidateDaySelections(DateAggregation dayType) {

        LocalDate readyDate = risingFastVideoFacade.apiDataReadyDate();

        return new RisingFastVideoPageExtra()
                .setCandidateDays(dayType.fetchCandidateDaysByReadyLogdate(readyDate, formatter));
    }

    @Override
    public PaginationExt<List<RisingFastVideo>, RisingFastVideoPageExtra> page(RisingFastVideoQuery originQuery) {

        return interceptNoLoginLimitation(
                originQuery,
                this::doPage
        );

    }


    public PaginationExt<List<RisingFastVideo>, RisingFastVideoPageExtra> doPage(RisingFastVideoQuery query) {

        query.validate();

        Tuple2<Pagination<List<RisingFastVideo>>, LocalDate> page = risingFastVideoFacade.videoPage(query);
        PaginationExt<List<RisingFastVideo>, RisingFastVideoPageExtra> result = PaginationExt.extent(
                page._1.map(list ->
                        this.blockingInvalidVideos(list, Lazy.of(() -> this.biliArchiveService
                                .findSimpleArchiveStateByAvid(list.stream()
                                        .map(RisingFastVideo::getAvid)
                                        .collect(Collectors.toList()), null)
                                .entrySet()
                                .stream()
                                .collect(Collectors.toMap(entry -> entry.getKey(),
                                        entry -> entry.getValue().getState() >= 0, (a, b) -> b))))),
                new RisingFastVideoPageExtra()
                        .setCandidateDays(query
                                .getDayType()
                                .fetchCandidateDaysByReadyLogdate(page._2, formatter))
        ).map(list -> {
            return Try.of(() -> biliArchiveService.blockRisingVideos(
                    list, null
            )).getOrElse(list);
        });

        this.filling(result.getData());

        return result;

    }


    private PaginationExt<List<RisingFastVideo>, RisingFastVideoPageExtra> interceptNoLoginLimitation(
            RisingFastVideoQuery query,
            Function<RisingFastVideoQuery, PaginationExt<List<RisingFastVideo>, RisingFastVideoPageExtra>> handler
    ) {

        // 如果没有登录且对未登录限制开启；
        if (query.getAccountId() == null &&
                noLoginAccessConfig.isRisingFastVideoLimitNoLoginUserEnabled()) {

            // TODO 对于其他几个由于都是page接口的弹窗，目前是看没有必要限制，且产品未定义；
            if (noLoginAccessConfig.getRisingFastVideoLimitNoLoginUserContentLimitMax() <= 0) {
                throw new IllegalArgumentException("用户未登录，无法查看");
            }

            query = new RisingFastVideoQuery()
                    .setPs(noLoginAccessConfig.getRisingFastVideoLimitNoLoginUserContentLimitMax())
                    .setPn(1)
                    .setSortBy(MaterialSortBy.play)
                    .setDayType(DateAggregation.d1);

            PaginationExt<List<RisingFastVideo>, RisingFastVideoPageExtra> result = handler.apply(query);

            result.setTotal_count(Math.min(
                    noLoginAccessConfig.getRisingFastVideoLimitNoLoginUserContentLimitMax(),
                    result.getTotal_count()));

            return result;

        }

        else {
            return handler.apply(query);
        }

    }



    private List<RisingFastVideo> blockingInvalidVideos(List<RisingFastVideo> data,
            Lazy<Map<Long, Boolean>> validateStateSupplier) {

        Map<Long, Boolean> validateStates = validateStateSupplier.get();

        return data.stream().filter(video -> {

            return Optional.ofNullable(validateStates.get(video.getAvid()))
                    .orElse(true);

        }).collect(Collectors.toList());

    }


    @Override
    public RisingFastVideoCurve curve(RisingFastVideoCurveQuery query) {

        query.validate();

        Lazy<SimpleArc> archiveLazy = Lazy.of(() -> {
            return Optional.ofNullable(
                            biliArchiveService.findSimpleArchiveStateByAvid(Lists.newArrayList(query.getAvid())
                                    , null).get(query.getAvid()))
                    .filter(arc -> arc.getState() >= 0)
                    .orElseThrow(() -> new IllegalArgumentException("目标稿件不存在"));


        });

        query.setPubtime(archiveLazy.map(SimpleArc::getPubdate));

        this.checkArchiveStateSkipIfNotExisted(archiveLazy);


        return risingFastVideoFacade.videoCurve(query)
                .map(curve -> {
                    this.fixingRisingVideoCurveCauseByDailyDataMissing(curve, Lazy.of(() -> {
                        return Optional.ofNullable(
                                        biliArchiveService.findSimpleArchiveStateByAvid(Lists.newArrayList(query.getAvid())
                                                , null).get(query.getAvid()))
                                .filter(arc -> arc.getState() >= 0)
                                .orElseThrow(() -> new IllegalArgumentException("目标稿件不存在"));


                    }).map(SimpleArc::getPubdate).map(pubTimeSeconds -> {
                        return LocalDateTime.ofInstant(Instant.ofEpochSecond(pubTimeSeconds), ZoneOffset.of("+8"));
                    }), query.getDayType(), query.getCurveType());
                    return curve;
                })
                .orElseThrow(() -> new IllegalArgumentException("目标稿件的聚合数据不存在"));
    }


    /**
     * 修复因为数据准入而导致的sum(data_daily) 即data_incr不准确问题；
     * <p>
     * 修复方案： 当数据天不足dayType时，且incr不符合acc时，直接往前虚构一天，补足incr，
     *
     * @param video
     * @param dayType
     */
    private void fixingRisingVideoCurveCauseByDailyDataMissing(
            RisingFastVideoCurve curve,
            Lazy<LocalDateTime> videoPubTime,
            DateAggregation dayType, RisingFastVideoCurveType curveType) {

        // 只有当数据不足，且只缺少发布当天的数据时，才进行补充
        Try.run(() -> {

            if (dayType != DateAggregation.d7 && dayType != DateAggregation.d30 && dayType != DateAggregation.d90) {
                return;
            }

            if (curve.getPointsAcc().size() != curve.getPointsIncr().size()) {

                // 只尝试修复对齐，且只缺一个点的情况
                return;
            }

            if (curve.getPointsIncr().size() == dayType.getDateFromOffset()) {

                // 点数充足
                return;
            }

            // 只少了发布日的一个点
            if (!curve.getPointsIncr().get(0).getDate().minusDays(1).equals(videoPubTime.get().toLocalDate())) {

                return;

            }

            // build fake head point;

            LocalDate addDate = curve.getPointsIncr().get(0).getDate().minusDays(1);

            RisingFastVideoCurvePoint firstIncr = curve.getPointsIncr().get(0);
            RisingFastVideoCurvePoint firstAcc = curve.getPointsAcc().get(0);

            List<Long> indicatePreFirstValue = IntStream.range(0, firstAcc.getValueDecimal().size())

                    .mapToObj(index -> {
                        return firstAcc.getValueDecimal().get(index) -
                                firstIncr.getValueDecimal().get(index);
                    }).collect(Collectors.toList());

            RisingFastVideoCurvePoint preFirstPointIncr = new RisingFastVideoCurvePoint()
                    .setDate(addDate)
                    .setTimestamp(addDate
                            .atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.of("+8")).toEpochMilli())
                    .setValueOfVideoCurveByDecimal(
                            curveType, indicatePreFirstValue
                    );

            RisingFastVideoCurvePoint preFirstPointAcc = new RisingFastVideoCurvePoint()
                    .setDate(addDate)
                    .setTimestamp(addDate.atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.of("+8")).toEpochMilli())
                    .setValueOfVideoCurveByDecimal(
                            curveType, indicatePreFirstValue
                    );

            curve.getPointsIncr().add(0, preFirstPointIncr);
            curve.getPointsAcc().add(0, preFirstPointAcc);

            log.info("fixingRisingVideoCurveCauseByDailyDataMissing: id={} {}, add point={}", curve.getAvid(),
                    curveType, preFirstPointIncr);
        }).onFailure(t -> {
            log.error("Fail to fixingRisingVideoCurveCauseByDailyDataMissing ");

        });


    }


    @Override
    public RisingFastVideo detail(RisingFastVideoIdQuery id) {
        id.validate();

        Lazy<SimpleArc> archive = Lazy.of(() -> Optional.ofNullable(
                        biliArchiveService.findSimpleArchiveStateByAvid(Lists.newArrayList(id.getAvid())
                                , null).get(id.getAvid()))
                .filter(arc -> arc.getState() >= 0)
                .orElseThrow(() -> new IllegalArgumentException("目标稿件不存在")));

        id.setPubtime(archive.map(SimpleArc::getPubdate));

        this.checkArchiveStateSkipIfNotExisted(archive);

        return risingFastVideoFacade.videoGroupDetail(id)
                .map(detail -> {
                    filling(Lists.newArrayList(detail));
                    return detail;
                })
                .map(detail -> {
                    fixingRisingVideoDetailCauseByDailyDataMissing(detail, id.getDayType());
                    return detail;
                })
                .orElseThrow(() -> new IllegalArgumentException("目标稿件的聚合数据不存在"));


    }


    /**
     * 修复因为数据准入而导致的sum(data_daily) 即data_incr不准确问题； 修复方案： 当dayType 覆盖publish_time时，直接使用data_acc替换data_incr
     *
     * @param video
     */
    private void fixingRisingVideoDetailCauseByDailyDataMissing(RisingFastVideo video, DateAggregation dayType) {

        Try.run(() -> {
            switch (dayType) {
                case d7:
                case d30:
                case d90: {

                    // 今天2024-07-30   近7天= 24 25 26 27 28 29 30  =07-24 ~ 07-30

                    LocalDate logdate = video.getParsedLogDate();

                    // 07-24.00
                    LocalDateTime groupStartDateTime = logdate.minusDays(dayType.getDateFromOffset() - 1)
                            .atTime(LocalTime.MIDNIGHT);

                    if (groupStartDateTime.isBefore(video.getAvidPubtime())) {

                        video.replaceIncrDataByAccData();

                    }

                    break;
                }

                default: {
                    //skip
                }
            }

        }).onFailure(t -> {
            log.error("Fail to fixingRisingVideoDetailCauseByDailyDataMissing", t);
        });


    }


    private void filling(List<RisingFastVideo> data) {

        this.fillingBvId(data);

        this.fillingUpFace(data);

    }


    private void fillingBvId(List<RisingFastVideo> data) {

        data.forEach(video -> {
            Try.run(() -> {
                video.setBvid(BVIDUtils.avToBv(video.getAvid()));
            });
        });

    }

    private void fillingUpFace(List<RisingFastVideo> data){

        biliAccountService.fillingUpAvatarOfRisingVideo(data);
    }


    /**
     * 查不到无所谓，查到还是禁用的，那就不可以接受了
     * @param archiveLazy
     */
    private void checkArchiveStateSkipIfNotExisted( Lazy<SimpleArc> archiveLazy){

        SimpleArc archive = Try.of(archiveLazy::get).getOrNull();

        if (archive == null) {
            return;
        }

        if (archive.getState() < 0) {
            throw new IllegalArgumentException("目标稿件不存在{0}");
        }

    }


}
