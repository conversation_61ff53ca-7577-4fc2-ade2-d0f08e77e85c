package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkLandingPageShowUrlPo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageShowUrlPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MgkLandingPageShowUrlDao {
    long countByExample(MgkLandingPageShowUrlPoExample example);

    int deleteByExample(MgkLandingPageShowUrlPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(MgkLandingPageShowUrlPo record);

    int insertSelective(MgkLandingPageShowUrlPo record);

    List<MgkLandingPageShowUrlPo> selectByExample(MgkLandingPageShowUrlPoExample example);

    MgkLandingPageShowUrlPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkLandingPageShowUrlPo record, @Param("example") MgkLandingPageShowUrlPoExample example);

    int updateByExample(@Param("record") MgkLandingPageShowUrlPo record, @Param("example") MgkLandingPageShowUrlPoExample example);

    int updateByPrimaryKeySelective(MgkLandingPageShowUrlPo record);

    int updateByPrimaryKey(MgkLandingPageShowUrlPo record);
}