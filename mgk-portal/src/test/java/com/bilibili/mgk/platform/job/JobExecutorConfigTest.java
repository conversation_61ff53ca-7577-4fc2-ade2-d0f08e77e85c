package com.bilibili.mgk.platform.job;

import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年09月20日
 */
public class JobExecutorConfigTest extends MockitoDefaultTest {
    @InjectMocks
    JobExecutorConfig jobExecutorConfig;

    @Test
    public void xxlJobSpringExecutor() {
        ReflectionTestUtils.setField(jobExecutorConfig, "executorPort", 9080);
        ReflectionTestUtils.setField(jobExecutorConfig, "logretentiondays", 30);
        XxlJobSpringExecutor xxlJobSpringExecutor = jobExecutorConfig.xxlJobSpringExecutor();
        Assert.assertNotNull(xxlJobSpringExecutor);
    }
}