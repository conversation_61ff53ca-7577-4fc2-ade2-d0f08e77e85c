package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.mgk.platform.api.model.dto.*;
import com.bilibili.mgk.platform.api.model.service.IMgkModelService;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.MgkRightLabelEnum;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.support.Assert;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/02
 **/
@Service
@Slf4j
public class MgkModelServiceImpl implements IMgkModelService {


    @Autowired
    private ModelServiceDelegate modelServiceDelegate;
    @Autowired
    private MgkBaseService mgkBaseService;
    @Value("${mgk.model_search_hot_words:aaa}")
    private String hotWords;


    @Override
    public PageResult<MgkModelDto> getModelDtos(QueryModelParamDto modelParamDto, Page page) {
        return modelServiceDelegate.getModelDtos(modelParamDto, page);
    }

    @Override
    public List<MgkModelDto> getModelsByModelIds(List<Long> modelIds) {
        return modelServiceDelegate.getMgkModelDtosByIds(modelIds);
    }

    @Override
    public List<MgkModelDto> getModelDtosByName(String name, Long modelId) {
        return modelServiceDelegate.getModelDtosByName(name, modelId);
    }

    @Override
    public long create(Operator operator, NewModelDto convertNewModelVo2Dto) {
        return modelServiceDelegate.create(operator, convertNewModelVo2Dto);
    }

    @Override
    public List<MgkTradeDto> getTradeDtos(QueryTradeParamDto paramDto) {
        return modelServiceDelegate.getTradeDtos(paramDto);
    }

    @Override
    public List<MgkModelTradeMappingDto> getModelTradeMappingDtos(QueryModelTradeMappingParamDto param) {
        return modelServiceDelegate.getModelTradeMappingDtos(param);
    }

    @Override
    public long createTrade(Operator operator, NewTradeDto newTradeDto) {
        return modelServiceDelegate.createTrade(operator, newTradeDto);
    }

    @Override
    public void update(Operator operator, UpdateModelDto updateModelDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(updateModelDto, "模板配置不可为空");
        Assert.notNull(updateModelDto.getModelId(), "模板Id不可为空");

        RLock lock = mgkBaseService.getLock(updateModelDto.getModelId(), MgkConstants.MODEL_LOCK_SUFFIX);
        try {
            modelServiceDelegate.update(operator, updateModelDto);
        } finally {
            log.info(System.currentTimeMillis() + "---update model unLock---");
            lock.unlock();
        }
    }

    @Override
    public void publish(Operator operator, Long modelId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(modelId, "模板Id不可为空");
        RLock lock = mgkBaseService.getLock(modelId, MgkConstants.MODEL_LOCK_SUFFIX);
        try {
            modelServiceDelegate.publish(operator, modelId);
        } finally {
            log.info(System.currentTimeMillis() + "--- publish unlock ---");
            lock.unlock();
        }
    }

    @Override
    public void downline(Operator operator, Long modelId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(modelId, "模板Id不可为空");
        RLock lock = mgkBaseService.getLock(modelId, MgkConstants.MODEL_LOCK_SUFFIX);
        try {
            modelServiceDelegate.downline(operator, modelId);
        } finally {
            log.info(System.currentTimeMillis() + "--- downline unlock ---");
            lock.unlock();
        }
    }

    @Override
    public void batchDisable(Operator operator, List<Long> modelIds) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.isTrue(!CollectionUtils.isEmpty(modelIds), "模板id不可为空");
        Set<RLock> rLockSet = modelIds.stream().map(modelId -> mgkBaseService.getLock(modelId, MgkConstants.MODEL_LOCK_SUFFIX)).collect(Collectors.toSet());
        try {
            modelServiceDelegate.batchDisable(operator, modelIds);
        } finally {
            rLockSet.forEach(RLock::unlock);
        }
    }

    @Override
    public List<MgkRightLabelEnum> hasRights(Operator operator) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        return modelServiceDelegate.hasRights(operator.getOperatorId());
    }

    @Override
    public List<MgkModelUsedDto> getModelUsed(Operator operator) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        return modelServiceDelegate.getModelUsed(operator);
    }

    @Override
    public List<String> getHotWords() {
        return Splitter.on(",").splitToList(hotWords);
    }
}
