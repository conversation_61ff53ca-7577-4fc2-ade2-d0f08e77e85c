package com.bilibili.mgk.platform.portal.webapi.log.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2018/1/22
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MgkLogOperationVo {
    /**
     * 自增ID
     */
    private String id;

    /**
     * 对象ID
     */
    private String obj_id;

    /**
     * 操作对象类型: 1-站点
     */
    private Integer obj_flag;


    /**
     * 操作对象类型: 1-站点
     */
    private String obj_flag_desc;

    /**
     * 操作类型:1-新建 2-删除 3-修改 4-复制 5-发布 6-下线 7-管理员驳回
     */
    private Integer operate_type;

    private String operate_type_desc;

    /**
     * 操作人
     */
    private String operator_username;

    /**
     * 操作人类型: 0-广告主 1-运营 2-系统 4-代理商 5-代理商的系统管理员 6-代理商的投放管理员 7-二级代理运营人员 100-内部LDAP登陆
     */
    private Integer operator_type;

    private String operator_type_desc;

    /**
     * 更新时间
     */
    private String mtime;

    /**
     * 新值
     */
    private String oldValue;

    /**
     * 旧值
     */
    private String newValue;
}
