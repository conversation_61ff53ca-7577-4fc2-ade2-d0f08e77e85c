package com.bilibili.mgk.platform.portal.webapi.wechat.vo;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WorkChatCustomerAcquisitionUpdateVo {

    @ApiModelProperty(value = "客服id列表")
    private List<String> userIdList;

    @ApiModelProperty(value = "获客链接名称")
    private String linkName;

    @ApiModelProperty(value = "获客链接id")
    private String linkId;

}
