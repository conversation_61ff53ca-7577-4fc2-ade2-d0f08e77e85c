package com.bilibili.collage.biz.service;

import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.dto.CollageMediaUpdateDto;
import com.bilibili.collage.api.dto.QueryCollageMediaDto;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.redisson.RedissonLock;
import org.redisson.api.RedissonClient;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/09/16
 **/
public class CollageMediaServiceImplTest extends BaseMockitoTest {

    @InjectMocks
    private CollageMediaServiceImpl collageMediaService;

    @Mock
    private CollageMediaServiceDelegate collageMediaServiceDelegate;

    @Mock
    private RedissonClient redissonClient;
    @Mock
    private RedissonLock redissonLock;

    @Mock
    private CollageBaseService collageBaseService;

    @Mock
    private CollageValidService collageValidService;


    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testGetMediaList() {
        collageMediaService.getMediaList(QueryCollageMediaDto.builder().build());
    }

    @Test
    public void testUpdateMedia() {
        when(collageBaseService.getLock(any(), any())).thenReturn(redissonLock);
        collageMediaService.updateMedia(operator, CollageMediaUpdateDto.builder().build());
    }

    @Test
    public void testBatchDeleted() {
        when(collageBaseService.getLock(any(), any())).thenReturn(redissonLock);
        collageMediaService.batchDeleted(operator, Lists.newArrayList(1));
    }

    @Test
    public void testSingleDeleted() {
        when(collageBaseService.getLock(any(), any())).thenReturn(redissonLock);
        collageMediaService.singleDeleted(operator, 1);
    }

    @Test
    public void testInsert() {
        collageMediaService.insert(operator, CollageMediaDto.builder().build());
    }

    @Test
    public void testTestInsert() {
        collageMediaService.insert(operator, Lists.newArrayList(CollageMediaDto.builder().build()));
    }

    @Test
    public void testGetMediaOther() {
        collageMediaService.getMediaOther(QueryCollageMediaDto.builder().build());
    }

    @Test
    public void testUpdateMediaUrl() {
        when(collageBaseService.getLock(any(), any())).thenReturn(redissonLock);
        collageMediaService.updateMediaUrl(operator, 1, "http://bilibili.com");
    }
}