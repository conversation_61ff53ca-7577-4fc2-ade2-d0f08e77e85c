package com.bilibili.mgk.platform.biz.service.grpc;


import com.alibaba.fastjson.JSON;
import com.bapis.ad.mgk.business_tool.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.account.IMgkAccountService;
import com.bilibili.mgk.platform.api.account.dto.AccountDto;
import com.bilibili.mgk.platform.api.audit.service.IMgkAuditPageService;
import com.bilibili.mgk.platform.api.consult_component.ConsultComponentService;
import com.bilibili.mgk.platform.api.consult_component.dto.ConsultComponentDto;
import com.bilibili.mgk.platform.api.data.dto.*;
import com.bilibili.mgk.platform.api.data.service.IMgkDataService;
import com.bilibili.mgk.platform.api.data.service.IMgkWechatPackageDataService;
import com.bilibili.mgk.platform.api.form.dto.MgkFormDto;
import com.bilibili.mgk.platform.api.form.dto.MgkFormItemDto;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryLandingPageParamDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.wechat.dto.QueryWorkWechatLinkDto;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWorkWxCustomerService;
import com.bilibili.mgk.platform.biz.ad.dao.AccAccountAwakenAppMappingDao;
import com.bilibili.mgk.platform.biz.ad.account.dao.AccAccountDao;
import com.bilibili.mgk.platform.biz.ad.account.dao.CustomerDao;
import com.bilibili.mgk.platform.biz.ad.po.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.LauThirdPartyLandingPageComponentQueryDSLPo;
import com.bilibili.mgk.platform.biz.service.data.delegate.MgkWechatPackageDataServiceDelegate;
import com.bilibili.mgk.platform.biz.service.personal_mgk.PerMgkAutoCreateAccService;
import com.bilibili.mgk.platform.biz.service.third_party.page.delegate.LauThirdPartyLandingPageComponentDelegate;
import com.bilibili.mgk.platform.biz.service.third_party.page.delegate.MgkThirdPartyPageServiceDelegate;
import com.bilibili.mgk.platform.common.*;
import com.google.common.collect.Lists;
import com.google.protobuf.Empty;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.common.constants.WorkWxConstants.CUSTOMER_ACQUISITION_LINK_BACKUP_PAGE_LINK;

/**
 * <AUTHOR>
 * 号经营查询落地页接口
 * @date 2024/2/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessToolServiceGrpcImpl extends MgkBusinessToolServiceGrpc.MgkBusinessToolServiceImplBase {

    private final IMgkLandingPageService mgkLandingPageService;
    private final IMgkAuditPageService mgkAuditPageService;
    private final IMgkFormService mgkFormService;
    private final IMgkDataService mgkDataService;
    private final IMgkWorkWxCustomerService workWxCustomerService;
    private final IMgkAccountService accountService;
    private final IMgkWechatPackageDataService wechatPackageDataService;
    private final MgkWechatPackageDataServiceDelegate packageDataServiceDelegate;
    private final IMgkWorkWxCustomerService customerService;
    private final PerMgkAutoCreateAccService perMgkAutoCreateAccService;
    private final ConsultComponentService consultComponentService;
    private final MgkThirdPartyPageServiceDelegate mgkThirdPartyPageServiceDelegate;
    private final LauThirdPartyLandingPageComponentDelegate lauThirdPartyLandingPageComponentDelegate;
    private final AccAccountDao accAccountDao;
    private final CustomerDao customerDao;
    private final AccAccountAwakenAppMappingDao accAccountAwakenAppMappingDao;

    /**
     * <pre>
     * 查询建站落地页
     * </pre>
     */
    @Override
    public void getLaunchPageByPage(LandingPagesReq request,
                                    StreamObserver<LaunchPageByPageReply> responseObserver) {
        var pageByPageReply = LaunchPageByPageReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getPage()), "分页页数不能为空");
            Assert.isTrue(Utils.isPositive(request.getSize()), "分页参数不能为空");
            Assert.isTrue(!CollectionUtils.isEmpty(request.getAccountIdList()), "账户id不能为空");
            Assert.isTrue(request.getMgkPageIdList().size() <= 50, "落地页操作批次不能超过50条");

            var pageResult = mgkLandingPageService.getLandingPageDtos(QueryLandingPageParamDto.builder()
                    .pageIdList(request.getMgkPageIdList()).statusList(request.getStatusList())
                    .accountIdList(Lists.newArrayList(request.getAccountIdList())).orderBy("mtime desc")
                    //兼容发布过程
                    .templateStyleList(org.apache.commons.collections4.CollectionUtils.isEmpty(request.getTemplateStyleListList()) ? Lists.newArrayList(TemplateStyleEnum.BUSINESS_TOOL.getCode()) : request.getTemplateStyleListList())
                    .nameLike(request.getNameLike()).build(), Page.valueOf(request.getPage(), request.getSize()));

            List<LaunchPage> res = new ArrayList<>();
            if (pageResult.getTotal() != 0) {
                var records = pageResult.getRecords();
                List<Long> pageIds = records.stream().map(MgkLandingPageDto::getPageId).collect(Collectors.toList());
                List<Long> waitingAuditPageIds = mgkAuditPageService.queryWaitingAuditPageIds(pageIds);
                records.forEach(dto -> {
                    res.add(LaunchPage.newBuilder().setLaunchUrl(dto.getPcLaunchUrl() == null ? "" : dto.getPcLaunchUrl())
                            .setAccountId(dto.getAccountId())
                            .setCtime(dto.getCtime().getTime()).setMtime(dto.getMtime().getTime())
                            .setName(dto.getName())
                            .setPageCover(dto.getPageCover())
                            .setPageId(dto.getPageId())
                            .setPageCover(dto.getPageCover())
                            .setContainAuditingVersion(waitingAuditPageIds.contains(dto.getPageId()))
                            .setReason(dto.getReason()).setStatus(dto.getStatus())
                            .setStatusDesc(LandingPageStatusEnum.getByCode(dto.getStatus()).getDesc())
                            .setTitle(dto.getTitle()).build());
                });

            }
            pageByPageReply.setTotal(pageResult.getTotal())
                    .addAllPage(res)
                    .build();
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getLaunchPageByPage fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(pageByPageReply.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 删除落地页
     * </pre>
     */
    @Override
    public void disable(HandlePagesReq request,
                        StreamObserver<com.google.protobuf.Empty> responseObserver) {
        try {
            Assert.isTrue(!CollectionUtils.isEmpty(request.getMgkPageIdList()), "落地页id不能为空");
            Assert.isTrue(request.getMgkPageIdList().size() <= 10, "落地页删除批次不能超过10条");
            mgkLandingPageService.batchDisable(Operator.builder()
                            .operatorId(request.getAccountId()).operatorType(OperatorType.ADVERTISERS)
                            .operatorName(request.getOperatorName()).build(),
                    request.getMgkPageIdList());
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getLaunchPageByPage fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(Empty.newBuilder().build());
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 查询建站落地页汇总数据
     * </pre>
     */
    @Override
    public void getPageSumData(PageWithTimeReq request,
                               StreamObserver<PageFormSumDataReply> responseObserver) {
        var sumPageReply = PageFormSumDataReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getMgkPageId()), "落地页id不能为空");
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "账户id不能为空");
            var formId = getBusinessToolFormIdByPageId(request.getMgkPageId(), request.getAccountId());
            if (Utils.isPositive(formId)) {
                var dataMap = mgkDataService.getFormId2ExtFormDataMap(Lists.newArrayList(formId),
                        true);

                var dataDto = dataMap.getOrDefault(formId, ExtFormDataDto.builder()
                        .formDataCount(0).build());
                sumPageReply.setFormSubmitCount(dataDto.getFormDataCount());
            }
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getPageSumData fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(sumPageReply.build());
            responseObserver.onCompleted();
        }
    }

    private Long getBusinessToolFormIdByPageId(Long pageId, Integer accountId) {
        var pageDto = mgkLandingPageService.getLandingPageDtoByPageId(pageId);
        Assert.isTrue(pageDto.getAccountId().equals(accountId), "账号和操作的落地页不匹配");
        var formIds = mgkFormService.getFormIdsByPageId(pageId);
        return CollectionUtils.isEmpty(formIds) ? null : formIds.get(0);
    }

    /**
     * <pre>
     * 分页查询建站落地页回收数据
     * </pre>
     */
    @Override
    public void getPageDataByPage(PageWithTimePageReq request,
                                  StreamObserver<PageFormDataPageReply> responseObserver) {
        var pageDataReply = PageFormDataPageReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getMgkPageId()), "落地页id必传");
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "账户id必传");
            Assert.isTrue(Utils.isPositive(request.getPage()), "分页页数不能为空");
            Assert.isTrue(Utils.isPositive(request.getSize()), "分页参数不能为空");

            var formId = getBusinessToolFormIdByPageId(request.getMgkPageId(), request.getAccountId());
            if (Utils.isPositive(formId)) {
                var query = FormDataQueryDto.builder()
                        .pageIds(Lists.newArrayList(request.getMgkPageId()))
                        .formId(formId).accountId(request.getAccountId()).orderBy("mtime desc")
                        .page(Page.valueOf(request.getPage(), request.getSize())).build();
                if (Utils.isPositive(request.getStartTime())) {
                    query.setBeginCtime(new Timestamp(request.getStartTime()));
                }
                if (Utils.isPositive(request.getEndTime())) {
                    query.setEndCtime(new Timestamp(request.getEndTime()));
                }
                var pageResult = mgkDataService.getFormDataByPage(query);
                pageDataReply.setTotal(pageResult.getTotal());

                var records = pageResult.getRecords();

                var headers = getHeader(formId);
                if (headers != null) {
                    pageDataReply.addAllHeaders(headers);
                }
                List<PageFormItemDataReply> dataReplies = new ArrayList<>();
                if (!CollectionUtils.isEmpty(records)) {
                    records.forEach(dto -> {
                        var dataList = dto.getFormData().stream().map(FormItemDataDto::getValue)
                                .collect(Collectors.toList());
                        var item = PageFormItemDataReply.newBuilder()
                                .setPageId(dto.getPageId())
                                .setMtime(dto.getCtime().getTime());
                        if (!CollectionUtils.isEmpty(dataList)) {
                            item.addAllValues(dataList);
                        }
                        dataReplies.add(item.build());
                    });
                }
                pageDataReply.addAllDataReplies(dataReplies);
            }
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getPageDataByPage fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(pageDataReply.build());
            responseObserver.onCompleted();
        }

    }

    /**
     * <pre>
     * 查询建站落地页回收数据用于下载
     * </pre>
     */
    @Override
    public void getPageData(com.bapis.ad.mgk.business_tool.PageWithTimeReq request,
                            io.grpc.stub.StreamObserver<PageFormDataReply> responseObserver) {
        var pageDataReply = PageFormDataReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getMgkPageId()), "落地页id必传");
            Assert.isTrue(Utils.isPositive(request.getStartTime()), "开始时间必传");
            Assert.isTrue(Utils.isPositive(request.getEndTime()), "结束时间必传");
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "账户id必传");

            var formId = getBusinessToolFormIdByPageId(request.getMgkPageId(), request.getAccountId());
            if (Utils.isPositive(formId)) {
                var dataDtos = mgkDataService.getReportDatas(QueryReportDataParamDto.builder()
                        .formId(formId).accountId(request.getAccountId()).orderBy("mtime desc").download(true)
                        .beginCtime(new Timestamp(request.getStartTime())).endCtime(new Timestamp(request.getEndTime()))
                        .build());

                var headers = getHeader(formId);
                if (headers != null) {
                    pageDataReply.addAllHeaders(headers);
                }

                List<PageFormItemDataReply> dataReplies = new ArrayList<>();
                if (!CollectionUtils.isEmpty(dataDtos)) {
                    dataDtos.forEach(dto -> {
                        var dataList = dto.getFormData().stream().map(FormItemDataDto::getValue)
                                .collect(Collectors.toList());
                        var item = PageFormItemDataReply.newBuilder()
                                .setPageId(dto.getPageId())
                                .setMtime(dto.getCtime().getTime());
                        if (!CollectionUtils.isEmpty(dataList)) {
                            item.addAllValues(dataList);
                        }
                        dataReplies.add(item.build());
                    });
                }
                pageDataReply.addAllDataReplies(dataReplies);
            }
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getPageData fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(pageDataReply.build());
            responseObserver.onCompleted();
        }
    }

    private List<String> getHeader(Long formId) {
        MgkFormDto formDto = mgkFormService.getFormDtoByFormId(formId);
        return formDto.getItems().stream()
                .filter(item -> !FormItemTypeEnum.BUTTON.getId().equals(item.getType()))
                .map(MgkFormItemDto::getLabel).collect(Collectors.toList());
    }

    /**
     * <pre>
     * 查询获客链接授权链接
     * </pre>
     */
    @Override
    public void getCustomerAcqAuthLink(HandleCustomerAcqLinkAuthReq request,
                                       StreamObserver<CustomerAcqLinkAuthReply> responseObserver) {

        var acqLinkAuthReply = CustomerAcqLinkAuthReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "账户id不能为空");
            AccountDto accountDto = accountService.getAccount(request.getAccountId());
            var authUrl = workWxCustomerService.getAccreditLink(accountDto.getCustomerId());
            acqLinkAuthReply.setAuthUrl(authUrl);
            acqLinkAuthReply.setAccountName(accountDto.getUsername());
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getCustomerAcqAuthLink fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(acqLinkAuthReply.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 不分页查询获客链接信息
     * </pre>
     */
    @Override
    public void getCustomerAcqLink(CustomerAcqLinkReq request,
                                   StreamObserver<CustomerAcqLinkListReply> responseObserver) {
        var acqLinkReply = CustomerAcqLinkListReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getStatus()), "查询状态不能为空");
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "账户id不能为空");
            var linkDtos = workWxCustomerService.queryCustomerAcqLinkList(QueryWorkWechatLinkDto
                    .builder().status(request.getStatus()).accountId(request.getAccountId())
                    .ids(request.getIdsList()).linkIds(request.getLinkIdsList())
                    .nameLike(request.getNameLike()).build());
            List<CustomerAcqLinkReply> linkReplies = new ArrayList<>();
            if (!CollectionUtils.isEmpty(linkDtos)) {
                linkDtos.forEach(dto -> {
                    var linkReply = CustomerAcqLinkReply.newBuilder().setLinkId(dto.getLinkId())
                            .setCreateTime(dto.getCreateTime().getTime())
                            .setId(dto.getId()).setLinkId(dto.getLinkId())
                            .setLinkName(dto.getLinkName()).setLinkUrl(buildCustomerAcqBackLink(dto.getId())).build();
                    linkReplies.add(linkReply);
                });
            }
            acqLinkReply.addAllReplies(linkReplies);
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getCustomerAcqLink fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(acqLinkReply.build());
            responseObserver.onCompleted();
        }
    }

    private String buildCustomerAcqBackLink(Long id) {
        var launchUrl = CUSTOMER_ACQUISITION_LINK_BACKUP_PAGE_LINK + id;
        StringBuilder sbParam = new StringBuilder();
        Arrays.stream(MacroParamEnum.values()).forEach(t -> {
            sbParam.append("&").append(t.getParamName()).append("=").append(t.getMacro());
        });
        sbParam.append("&").append("assembly_track_id").append("=").append("__ASSEMBLYTRACKID__");
        return launchUrl + sbParam;
    }

    /**
     * <pre>
     * 分页查询获客链接信息
     * </pre>
     */
    @Override
    public void getCustomerAcqLinkByPage(CustomerAcqLinkPageReq request,
                                         StreamObserver<CustomerAcqLinkPageReply> responseObserver) {
        var acqLinkReply = CustomerAcqLinkPageReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getStatus()), "查询状态不能为空");
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "账户id不能为空");
            Assert.isTrue(Utils.isPositive(request.getPage()), "分页页数不能为空");
            Assert.isTrue(Utils.isPositive(request.getSize()), "分页参数不能为空");
            customerService.synCustomerAcqLink(request.getAccountId(), false);
            var query = QueryWorkWechatLinkDto
                    .builder().accountId(request.getAccountId())
                    .ids(request.getIdsList()).status(request.getStatus())
                    .nameLike(request.getNameLike()).build();

            var pageResult = workWxCustomerService
                    .queryCustomerAcqLinkListByPage(query, request.getPage(), request.getSize());
            List<CustomerAcqLinkReply> linkReplies = new ArrayList<>();
            var records = pageResult.getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                records.forEach(dto -> {
                    var linkReply = CustomerAcqLinkReply.newBuilder().setLinkId(dto.getLinkId())
                            .setCreateTime(dto.getCreateTime().getTime())
                            .setId(dto.getId()).setLinkId(dto.getLinkId())
                            .setLinkName(dto.getLinkName()).setStatus(dto.getStatus())
                            .setLinkUrl(buildCustomerAcqBackLink(dto.getId())).build();
                    linkReplies.add(linkReply);
                });
            }
            acqLinkReply.setTotal(pageResult.getTotal());
            acqLinkReply.addAllReplies(linkReplies);
        } catch (IllegalArgumentException illegalArgumentException) {
            log.error("BusinessToolServiceGrpcImpl getCustomerAcqLink fail IllegalArgumentException: {}", ExceptionUtils.getStackTrace(illegalArgumentException));
            responseObserver.onError(Status.INVALID_ARGUMENT.withDescription(ExceptionUtils.getRootCauseMessage(illegalArgumentException)).asRuntimeException());
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getCustomerAcqLink fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(acqLinkReply.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 查询获客链接汇总数据
     * </pre>
     */
    @Override
    public void getCustomerAcqLinkSumData(CustomerAcqLinkWithTimeReq request,
                                          StreamObserver<CustomerAcqLinkSumDataReply> responseObserver) {
        var sumDataReply = CustomerAcqLinkSumDataReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getId()), "获客链接id不能为空");
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "获客链接账户id不能为空");
            Map<Long, WorkWechatDataCountDto> countDtoMap = packageDataServiceDelegate
                    .queryWorkWechatDataMap(Lists.newArrayList(request.getId()),
                            true,
                            Utils.isPositive(request.getStartTime()) ? new Timestamp(request.getStartTime()) : null,
                            Utils.isPositive(request.getEndTime()) ? new Timestamp(request.getEndTime()) : null);
            if (!CollectionUtils.isEmpty(countDtoMap)) {
                var data = countDtoMap.getOrDefault(request.getId(),
                        WorkWechatDataCountDto.builder().chatCount(0).addFansCount(0).build());
                sumDataReply.setAddFans(data.getAddFansCount());
                sumDataReply.setWxChat(data.getChatCount());
            }
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getCustomerAcqLink fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(sumDataReply.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 分页查询获客链接回收数据
     * </pre>
     */
    @Override
    public void getCustomerAcqLinkDataByPage(CustomerAcqLinkWithTimePageReq request,
                                             StreamObserver<CustomerAcqLinkDataPageReply> responseObserver) {
        var dataPageReply = CustomerAcqLinkDataPageReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getId()), "获客链接id不能为空");
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "获客链接账户id不能为空");

            Assert.isTrue(Utils.isPositive(request.getPage()), "分页页数不能为空");
            Assert.isTrue(Utils.isPositive(request.getSize()), "分页参数不能为空");
            var paramDto = QueryWechatReportDataDto.builder()
                    .accountId(request.getAccountId())
                    .orderBy("mtime desc").page(Page.valueOf(request.getPage(), request.getSize()))
                    .dataTypeList(WechatDataType.WORK_WX_SHOW).download(false)
                    .linkDataIds(Lists.newArrayList(request.getId()))
                    .build();
            if (Utils.isPositive(request.getStartTime())) {
                paramDto.setBeginCtime(new Timestamp(request.getStartTime()));
            }
            if (Utils.isPositive(request.getEndTime())) {
                paramDto.setEndCtime(new Timestamp(request.getEndTime()));
            }
            var pageResult = wechatPackageDataService.queryWechatPackageData(paramDto);
            List<CustomerAcqLinkDataItemReply> linkReplies = new ArrayList<>();
            var records = pageResult.getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                records.forEach(dto -> {
                    var linkReply = CustomerAcqLinkDataItemReply.newBuilder()
                            .setDataTypeDesc(dto.getDataType())
                            .setMtime(dto.getCtime().getTime())
                            .build();
                    linkReplies.add(linkReply);
                });
            }
            dataPageReply.addAllHeaders(Lists.newArrayList("转化类型", "转化时间"));
            dataPageReply.setTotal(pageResult.getTotal());
            dataPageReply.addAllReplies(linkReplies);
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getCustomerAcqLinkDataByPage fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(dataPageReply.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 查询获客链接回收数据用于下载
     * </pre>
     */
    @Override
    public void getCustomerAcqLinkData(CustomerAcqLinkWithTimeReq request,
                                       StreamObserver<CustomerAcqLinkDataReply> responseObserver) {
        var dataPageReply = CustomerAcqLinkDataReply.newBuilder();
        try {
            Assert.isTrue(Utils.isPositive(request.getStartTime()), "开始时间必传");
            Assert.isTrue(Utils.isPositive(request.getEndTime()), "结束时间必传");
            Assert.isTrue(Utils.isPositive(request.getId()), "获客链接id不能为空");
            Assert.isTrue(Utils.isPositive(request.getAccountId()), "获客链接账户id不能为空");
            var paramDto = QueryWechatReportDataDto.builder()
                    .accountId(request.getAccountId())
                    .beginCtime(new Timestamp(request.getStartTime()))
                    .endCtime(new Timestamp(request.getEndTime()))
                    .orderBy("mtime desc")
                    .dataTypeList(WechatDataType.WORK_WX_SHOW)
                    .linkDataIds(Lists.newArrayList(request.getId()))
                    .download(true)
                    .build();

            var pageResult = wechatPackageDataService.queryWechatPackageData(paramDto);
            List<CustomerAcqLinkDataItemReply> linkReplies = new ArrayList<>();
            var records = pageResult.getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                records.forEach(dto -> {
                    var linkReply = CustomerAcqLinkDataItemReply.newBuilder()
                            .setDataTypeDesc(dto.getDataType())
                            .setMtime(dto.getCtime().getTime())
                            .build();
                    linkReplies.add(linkReply);
                });
            }
            dataPageReply.addAllHeaders(Lists.newArrayList("转化类型", "转化时间"));
            dataPageReply.addAllReplies(linkReplies);
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getCustomerAcqLinkData fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(dataPageReply.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void addAllowLoginBusinessToolMgkLabel(AccountReq request, StreamObserver<BusinessToolMgkLabelReply> responseObserver) {
        BusinessToolMgkLabelReply reply = BusinessToolMgkLabelReply.getDefaultInstance();
        try {
            perMgkAutoCreateAccService.addAllowLoginPersonalMgkLabel(request.getAccountId());
            reply.toBuilder().setMsg("success");
            responseObserver.onNext(reply);
        } catch (Exception e) {
            log.error("error ", e);
            responseObserver.onError(e);
        }
        responseObserver.onCompleted();
    }

    @Override
    public void addBusinessToolLabel(AddBusinessToolLabelReq request, StreamObserver<AddBusinessToolLabelReply> responseObserver) {
        Integer accountId = request.getAccountId();
        Integer labelId = request.getLabelId();
        try {

            perMgkAutoCreateAccService.addBusinessToolLabel(accountId, labelId);
            responseObserver.onNext(AddBusinessToolLabelReply.newBuilder().setMsg("success").build());
            responseObserver.onCompleted();
            log.info("addBusinessToolLabel success accountId = {}, labelId = {}", accountId, labelId);
        } catch (Exception e) {
            log.error("addBusinessToolLabel error :{}",  ExceptionUtils.getStackTrace(e));
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
        }
    }

    @Override
    public void createOnlineConsultComponent(CreateOnlineConsultComponentReq request,
                                             StreamObserver<CreateOnlineConsultComponentReply> responseObserver) {
        var dataPageReply = CreateOnlineConsultComponentReply.newBuilder();
        try {
            log.info("accountId = {}", request.getAccountId());
            ConsultComponentDto consultComponentDto = consultComponentService.createConsultComponent(request.getAccountId(), request.getContent());
            log.info("consultComponentDto = {}", JSON.toJSONString(consultComponentDto));
            dataPageReply
                    .setAccountId(consultComponentDto.getAccountId())
                    .setContent(consultComponentDto.getContent())
                    .setToolId(consultComponentDto.getToolId())
                    .setStatus(consultComponentDto.getStatus().intValue());
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl createOnlineConsultComponent fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(dataPageReply.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void queryOnlineConsultComponentList(QueryOnlineConsultComponentReq request, StreamObserver<QueryOnlineConsultComponentReply> responseObserver) {
        var dataPageReply = QueryOnlineConsultComponentReply.newBuilder();
        try {
            List<ConsultComponentDto> consultComponentDtoList = consultComponentService.queryConsultComponent(request.getAccountId());
            dataPageReply.addAllOnlineConsultComponentList(dto2Replay(consultComponentDtoList));
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl queryOnlineConsultComponentList fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
        } finally {
            responseObserver.onNext(dataPageReply.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getThirdPartyLandingPageByPageId(QueryThirdPartyLandingPageByPageIdsReq request, StreamObserver<QueryThirdPartyLandingPageByPageIdsReply> responseObserver) {
        try {
            List<Long> pageIdsList = request.getPageIdsList();
            if (CollectionUtils.isEmpty(pageIdsList)) {
                responseObserver.onError(Status.INVALID_ARGUMENT.withDescription("pageIdsList不能为空").asRuntimeException());
                responseObserver.onCompleted();
                return;
            }
            List<LauThirdPartyLandingPageComponentQueryDSLPo> lauThirdPartyLandingPageComponentQueryDSLPos = lauThirdPartyLandingPageComponentDelegate.queryLauThirdPartyLandingPage(pageIdsList.get(0));
            List<QueryThirdPartyLandingPageByPageIdInfo> queryThirdPartyLandingPageByPageIdInfoList = new ArrayList<>();

            for (LauThirdPartyLandingPageComponentQueryDSLPo lauThirdPartyLandingPageComponentQueryDSLPo : lauThirdPartyLandingPageComponentQueryDSLPos) {
                QueryThirdPartyLandingPageByPageIdInfo queryThirdPartyLandingPageByPageIdInfo = QueryThirdPartyLandingPageByPageIdInfo.newBuilder()
                        .setPageId(lauThirdPartyLandingPageComponentQueryDSLPo.getBizId())
                        .setPageName(lauThirdPartyLandingPageComponentQueryDSLPo.getName())
                        .setPageUrl(lauThirdPartyLandingPageComponentQueryDSLPo.getUrl())
                        .build();
                queryThirdPartyLandingPageByPageIdInfoList.add(queryThirdPartyLandingPageByPageIdInfo);
            }
            QueryThirdPartyLandingPageByPageIdsReply queryThirdPartyLandingPageByPageIdsReply = QueryThirdPartyLandingPageByPageIdsReply.newBuilder()
                    .addAllThirdPartyLandingPageInfos(queryThirdPartyLandingPageByPageIdInfoList)
                    .build();

            responseObserver.onNext(queryThirdPartyLandingPageByPageIdsReply);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("BusinessToolServiceGrpcImpl getThirdPartyLandingPageByPageId fail:{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(Status.INTERNAL.withDescription(ExceptionUtils.getRootCauseMessage(e)).asRuntimeException());
            responseObserver.onCompleted();
        }

    }

    @Override
    public void queryAccountCustomerName(QueryAccountCustomerNameReq request, StreamObserver<QueryAccountCustomerNameReply> responseObserver) {
        try {
            List<Integer> accountIdList = request.getAccountIdList();
            AccAccountPoExample accAccountPoExample = new AccAccountPoExample();
            accAccountPoExample.createCriteria().andAccountIdIn(accountIdList).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<AccAccountPo> accAccountPos = accAccountDao.selectByExample(accAccountPoExample);
            List<Integer> customerIdList = accAccountPos.stream().map(AccAccountPo::getCustomerId).collect(Collectors.toList());
            CustomerPoExample customerPoExample = new CustomerPoExample();
            customerPoExample.createCriteria().andIdIn(customerIdList).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<CustomerPo> customerPoList = customerDao.selectByExample(customerPoExample);
            Map<Integer, String> customerIdNameMap = customerPoList.stream().collect(Collectors.toMap(CustomerPo::getId, CustomerPo::getUsername));
            List<QueryAccountCustomerNameSingleInfo> queryAccountCustomerNameSingleInfoList = new ArrayList<>();
            for (AccAccountPo accAccountPo : accAccountPos) {
                QueryAccountCustomerNameSingleInfo queryAccountCustomerNameSingleInfo = QueryAccountCustomerNameSingleInfo.newBuilder()
                        .setAccountId(accAccountPo.getAccountId())
                        .setCustomerName(customerIdNameMap.get(accAccountPo.getCustomerId()))
                        .build();
                queryAccountCustomerNameSingleInfoList.add(queryAccountCustomerNameSingleInfo);
            }
            QueryAccountCustomerNameReply queryAccountCustomerNameReply = QueryAccountCustomerNameReply.newBuilder()
                    .addAllInfos(queryAccountCustomerNameSingleInfoList)
                    .build();
            responseObserver.onNext(queryAccountCustomerNameReply);
            responseObserver.onCompleted();
            log.info("queryAccountCustomerName success accountIdList = {}", accountIdList);

        } catch (Exception e) {
            log.error("queryAccountCustomerName error :{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
        }
    }

    @Override
    public void addAccountAwakenApp(AddAccountAwakenAppReq request, StreamObserver<AddAccountAwakenAppReply> responseObserver) {
        try {
            int accountId = request.getAccountId();
            int appId = request.getAppId();

            AccAccountAwakenAppMappingPoExample selectExample = new AccAccountAwakenAppMappingPoExample();
            selectExample.or().andAccountIdEqualTo(accountId).andAppIdEqualTo(appId);

            List<AccAccountAwakenAppMappingPo> accAccountAwakenAppMappingPos = accAccountAwakenAppMappingDao.selectByExample(selectExample);

            Integer executeCount = 0;

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(accAccountAwakenAppMappingPos)) {
                AccAccountAwakenAppMappingPo accAccountAwakenAppMappingPo = accAccountAwakenAppMappingPos.get(0);
                accAccountAwakenAppMappingPo.setIsDeleted(0);
                executeCount = accAccountAwakenAppMappingDao.updateByPrimaryKey(accAccountAwakenAppMappingPo);
            } else {
                AccAccountAwakenAppMappingPo accAccountAwakenAppMappingPo = new AccAccountAwakenAppMappingPo();
                accAccountAwakenAppMappingPo.setAccountId(accountId);
                accAccountAwakenAppMappingPo.setAppId(appId);
                accAccountAwakenAppMappingPo.setIsDeleted(0);
                executeCount = accAccountAwakenAppMappingDao.insert(accAccountAwakenAppMappingPo);
            }
            AddAccountAwakenAppReply addAccountAwakenAppReply = AddAccountAwakenAppReply.newBuilder()
                    .setCode(Objects.equals(executeCount, 1) ? 0 : -1)
                    .build();
            responseObserver.onNext(addAccountAwakenAppReply);
            responseObserver.onCompleted();
            log.info("addAccountAwakenApp success accountId = {}, appId = {}", accountId, appId);
        } catch (Exception e) {
            log.error("addAccountAwakenApp error :{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
        }


    }


    @Override
    public void deleteAccountAwakenApp(DeleteAccountAwakenAppReq request, StreamObserver<DeleteAccountAwakenAppReply> responseObserver) {
        try {
            int accountId = request.getAccountId();
            int appId = request.getAppId();

            AccAccountAwakenAppMappingPo accAccountAwakenAppMappingPo = new AccAccountAwakenAppMappingPo();
            accAccountAwakenAppMappingPo.setIsDeleted(1);
            AccAccountAwakenAppMappingPoExample example = new AccAccountAwakenAppMappingPoExample();
            example.or().andAccountIdEqualTo(accountId).andAppIdEqualTo(appId);
            int executeCount = accAccountAwakenAppMappingDao.updateByExampleSelective(accAccountAwakenAppMappingPo, example);

            DeleteAccountAwakenAppReply deleteAccountAwakenAppReply = DeleteAccountAwakenAppReply.newBuilder()
                    .setCode(Objects.equals(executeCount, 1) ? 0 : -1)
                    .build();
            responseObserver.onNext(deleteAccountAwakenAppReply);
            responseObserver.onCompleted();
            log.info("deleteAccountAwakenApp success accountId = {}, appId = {}", accountId, appId);
        } catch (Exception e) {
            log.error("deleteAccountAwakenApp error :{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
        }
    }

    @Override
    public void queryAccountAwakenApp(QueryAccountAwakenAppReq request, StreamObserver<QueryAccountAwakenAppReply> responseObserver) {

        try {
            int accountId = request.getAccountId();
            AccAccountAwakenAppMappingPoExample example = new AccAccountAwakenAppMappingPoExample();
            example.or().andAccountIdEqualTo(accountId).andIsDeletedEqualTo(0);
            List<AccAccountAwakenAppMappingPo> accAccountAwakenAppMappingPos = accAccountAwakenAppMappingDao.selectByExample(example);
            List<Integer> appIds = accAccountAwakenAppMappingPos.stream().map(AccAccountAwakenAppMappingPo::getAppId).collect(Collectors.toList());
            QueryAccountAwakenAppReply queryAccountAwakenAppReply = QueryAccountAwakenAppReply.newBuilder()
                    .addAllAppIds(appIds)
                    .build();
            responseObserver.onNext(queryAccountAwakenAppReply);
            responseObserver.onCompleted();
            log.info("queryAccountAwakenApp success accountId = {} appId = {}", accountId, JSON.toJSONString(appIds));
        } catch (Exception e) {
            log.error("queryAccountAwakenApp error :{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
        }
    }

    @Override
    public void queryAccountsAwakenApp(QueryAccountsAwakenAppReq request, StreamObserver<QueryAccountsAwakenAppReply> responseObserver) {
        try {
            List<Integer> accountIds = request.getAccountIdsList();
            AccAccountAwakenAppMappingPoExample example = new AccAccountAwakenAppMappingPoExample();
            example.or().andAccountIdIn(accountIds).andIsDeletedEqualTo(0);
            List<AccAccountAwakenAppMappingPo> accAccountAwakenAppMappingPos = accAccountAwakenAppMappingDao.selectByExample(example);
            Map<Integer, List<Integer>> accountIdAppIdMap = accAccountAwakenAppMappingPos.stream().collect(Collectors.groupingBy(AccAccountAwakenAppMappingPo::getAccountId, Collectors.mapping(AccAccountAwakenAppMappingPo::getAppId, Collectors.toList())));
            List<QueryAccountsAwakenAppSingleInfo> queryAccountsAwakenAppReplyInfoList = new ArrayList<>();
            for (Integer accountId : accountIds) {
                List<Integer> appIds = accountIdAppIdMap.getOrDefault(accountId, new ArrayList<>());
                QueryAccountsAwakenAppSingleInfo queryAccountsAwakenAppReplyInfo = QueryAccountsAwakenAppSingleInfo.newBuilder()
                        .setAccountId(accountId)
                        .addAllAppIds(appIds)
                        .build();
                queryAccountsAwakenAppReplyInfoList.add(queryAccountsAwakenAppReplyInfo);
            }
            QueryAccountsAwakenAppReply queryAccountsAwakenAppReply = QueryAccountsAwakenAppReply.newBuilder()
                    .addAllInfos(queryAccountsAwakenAppReplyInfoList)
                    .build();
            responseObserver.onNext(queryAccountsAwakenAppReply);
            responseObserver.onCompleted();
            log.info("queryAccountsAwakenApp success accountIds = {}", JSON.toJSONString(accountIds));
        } catch (Exception e) {
            log.error("queryAccountsAwakenApp error :{}", ExceptionUtils.getStackTrace(e));
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
        }
    }

    private List<CreateOnlineConsultComponentReply> dto2Replay(List<ConsultComponentDto> consultComponentDtoList) {
        return consultComponentDtoList.stream().map(dto -> CreateOnlineConsultComponentReply.newBuilder()
                .setAccountId(dto.getAccountId())
                .setContent(dto.getContent())
                .setStatus(dto.getStatus())
                .setToolId(dto.getToolId())
                .setCtime(dto.getCtime())
                .setMtime(dto.getMtime()).build()).collect(Collectors.toList());
    }
}
