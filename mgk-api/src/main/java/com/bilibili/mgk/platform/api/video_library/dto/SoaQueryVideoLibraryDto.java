package com.bilibili.mgk.platform.api.video_library.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/14
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SoaQueryVideoLibraryDto implements Serializable {

    private static final long serialVersionUID = 6454241365960264143L;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 账号id
     */
    private List<Integer> accountIds;

    private String name;

    private Integer definitionType;

    private Integer page;

    private Integer size;

    private Integer width;

    private Integer height;

    /**
     * 审核状态 0全部 1待审核 2审核通过 3审核驳回
     */
    private Integer auditStatus;

    /**
     * 视频修改时间起
     */
    private Long mtimeFrom;

    /**
     * 视频修改时间止
     */
    private Long mtimeTo;

    /**
     * 视频id
     */
    private Integer mgkVideoId;

    /**
     * 视频状态 不传全部 1正常 2已删除
     */
    private Integer mgkVideoStatus;

    /**
     * SizeTypeEnum ignore
     * false -> sizeType逻辑
     * true  -> 宽高查询逻辑
     */
    private boolean sizeTypeIgnore;
}
