package com.bilibili.mgk.platform.api.wechat.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.wechat.dto.QueryWorkWechatLinkDto;
import com.bilibili.mgk.platform.api.wechat.dto.WorkChatCustomerAcquisitionAddDto;
import com.bilibili.mgk.platform.api.wechat.dto.WorkWechatCustomerAcqLinkDto;

import java.util.List;
import java.util.Map;

public interface IMgkWorkWxCustomerService {

    List<String> getUserList(Operator operator) throws ServiceException;

    void createCustomerAcquisitionLink(Operator operator, WorkChatCustomerAcquisitionAddDto addDto) throws ServiceException;

    void updateCustomerAcquisitionLink(Operator operator, WorkWechatCustomerAcqLinkDto linkDto) throws ServiceException;

    void delCustomerAcquisitionLink(Operator operator, Long id, String linkId,
                                    Boolean needDelInWorkWx) throws ServiceException;

    PageResult<WorkWechatCustomerAcqLinkDto> queryCustomerAcqLinkListByPage(QueryWorkWechatLinkDto query,
                                                                            int page, int size) throws ServiceException;

    List<WorkWechatCustomerAcqLinkDto> queryCustomerAcqLinkList(QueryWorkWechatLinkDto query) throws ServiceException;

    WorkWechatCustomerAcqLinkDto getCustomerAcqLinkDtoByIdWithoutCheckStatus(Long id);

    void savePageCustomerAcqLink(long pageId, List<String> linkId);

    String getLinkAndReplaceTrackByLinkIds(String linkId, String shortTrackId);

    List<String> getLinkIdsByCorpId(String corpId);

    List<Integer> getAccountIdsByCorpId(String corpId);

    void rejectPageAndCreative(List<String> linkIds);

    boolean isPageMappingIsModified(long pageId, List<String> linkId);

    void disablePageCustomerAcqLink(long pageId);

    Map<Long, List<String>> getPageCustomerAcqLinkByPageIds(List<Long> pageIds);

    void sendMsg(String changeType, String corpId);


    String getAccreditLink(Integer customerId) throws ServiceException;

    void synCustomerAcqLink(Integer accountId, Boolean synDel);

}
