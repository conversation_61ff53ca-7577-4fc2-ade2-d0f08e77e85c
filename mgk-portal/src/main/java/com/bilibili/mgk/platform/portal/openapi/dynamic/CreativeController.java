package com.bilibili.mgk.platform.portal.openapi.dynamic;

import com.bilibili.mgk.platform.api.dynamic.dto.MgkDynamicCreativeBizReplyDto;
import com.bilibili.mgk.platform.api.dynamic.service.IMgkDynamicService;
import com.bilibili.mgk.platform.portal.common.OpenApiBasicController;
import com.bilibili.mgk.platform.portal.openapi.bean.OpenApiResponse;
import com.bilibili.mgk.platform.portal.openapi.dynamic.vo.MgkDynamicCreativeBizReplyVo;
import com.bilibili.mgk.platform.portal.service.WebDynamicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @file: CreativeController
 * @author: gaoming
 * @date: 2021/06/15
 * @version: 1.0
 * @description:
 **/

@Slf4j
@RestController
@RequestMapping("/open_api/v1/creative")
@Api(value = "/creative", description = "创意点赞相关")
public class CreativeController extends OpenApiBasicController {

    @Autowired
    private IMgkDynamicService dynamicService;

    @Autowired
    private WebDynamicService webDynamicService;

    @ApiModelProperty(value = "根据创意id获取bizId")
    @RequestMapping(value = "/{creative_id}", method = RequestMethod.GET)
    public
    @ResponseBody
    OpenApiResponse<MgkDynamicCreativeBizReplyVo> getDynamicBizByCreativeId(@ApiParam(value = "创意Id") @PathVariable(value = "creative_id") Integer creativeId) {
        MgkDynamicCreativeBizReplyDto replyDto = dynamicService.getDynamicBizByCreativeIdFromRedis(creativeId);
        if (replyDto == null) {
            return OpenApiResponse.SUCCESS(null);
        }
        return OpenApiResponse.SUCCESS(webDynamicService.convertDynamicBizDto2Vo(replyDto));
    }
}
