package com.bilibili.mgk.material.center.service.agreement.dto;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/3
 */
@Data
@Accessors(chain = true)
public class AgreementCheckReq implements SnakeCaseBody {

    @ApiModelProperty("账户id")
    private Long accountId;

    @ApiModelProperty("协议id")
    private Long agreementId;

    @ApiModelProperty("非必填，权限key，如果不传则根据协议id生成")
    private String permissionKey;

    @ApiModelProperty("代理商id")
    private Integer agentId;

    @ApiModelProperty("产品id")
    private Integer productId;

    public void validate() {

        Assert.notNull(accountId, "accountId不能为空");
        Assert.notNull(agreementId, "agreementId不能为空");


    }


}
