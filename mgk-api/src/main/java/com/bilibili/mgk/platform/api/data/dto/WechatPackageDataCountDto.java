package com.bilibili.mgk.platform.api.data.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @ClassName WechatPackageDataCountDto
 * <AUTHOR>
 * @Date 2022/6/18 3:01 下午
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatPackageDataCountDto implements Serializable {

    /**
     * 微信帐户id
     */
    private Integer wechatAccountId;

    /**
     * 微信包id
     */
    private Integer wechatPackageId;

    /**
     * 微信复制数量
     */
    private Integer copyCount;

    /**
     * 跳数
     */
    private Integer jumpCount;

    /**
     * 最近提交时间
     */
    private Timestamp recentSubmitTime;

    public static WechatPackageDataCountDto getDefaultPackageDto(Integer wechatPackageId) {
        return WechatPackageDataCountDto.builder()
                .wechatPackageId(wechatPackageId)
                .copyCount(0)
                .jumpCount(0)
                .build();
    }

    public static WechatPackageDataCountDto getDefaultAccountDto(Integer wechatAccountId) {
        return WechatPackageDataCountDto.builder()
                .wechatAccountId(wechatAccountId)
                .copyCount(0)
                .jumpCount(0)
                .build();
    }
}
