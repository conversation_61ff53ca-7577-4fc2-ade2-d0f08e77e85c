package com.bilibili.mgk.material.center.facade;

import com.bapis.datacenter.service.oneservice.AdvFilter;
import com.bapis.datacenter.service.oneservice.OsHeader;
import com.bapis.datacenter.service.oneservice.PageReq;
import com.bapis.datacenter.service.oneservice.PageVo;
import com.bapis.datacenter.service.oneservice.QueryReq;
import com.bapis.datacenter.service.oneservice.QueryReq.Builder;
import com.bapis.datacenter.service.oneservice.QueryResp;
import com.bilibili.mgk.material.center.facade.converter.UpRankItemOneserviceDTO;
import com.bilibili.mgk.material.center.facade.proxy.OneServiceFlowControlProxy;
import com.bilibili.mgk.material.center.service.creative.model.FansAgeRange;
import com.bilibili.mgk.material.center.service.creative.model.UpRankInfo;
import com.bilibili.mgk.material.center.service.creative.model.UpRewardType;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankQuery;
import com.google.common.collect.Lists;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * SELECT lower(up_name) as lower_up_name,
 * <p>
 * log_date, up_mid, up_name, sign, join_time, verify_type, up_gender, level, avs_tid_name, avs_sub_tid_name,
 * is_rewarded, is_huahuo, up_type, fans, male_fans_ratio, age_range, first_up, last_up, avs,
 * ad_first_industry_name_list, ad_industry_name_list, avg_play_recent10, avg_interact_recent10, play_total,
 * likes_total, reply_total, danmu_total, coin_total, fav_total, share_total, play_7d, likes_7d, reply_7d, danmu_7d,
 * coin_7d, fav_7d, share_7d, play_30d, likes_30d, reply_30d, danmu_30d, coin_30d, fav_30d, share_30d, play_90d,
 * likes_90d, reply_90d, danmu_90d, coin_90d, fav_90d, share_90d, play_180d, likes_180d, reply_180d, danmu_180d,
 * coin_180d, fav_180d, share_180d FROM bi_sycpb.ads_flow_avid_crea_center_up_rank_data_1d_d
 *
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */
@Slf4j
@Service
public class UpRankOneserviceFacade {

    @Resource
    private OneServiceFlowControlProxy serviceOpenApiManagerBlockingStub;

    @Resource
    private ApiOneServiceBase helper;

    private DateTimeFormatter lastestUpdateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private DateTimeFormatter logdateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");


    @Value("${material.oneservice.up-rank.male-major-percent:0.55}")
    private String maleMajorPercent;

    @Value("${material.oneservice.up-rank.female-major-percent:0.45}")
    private String femaleMajorPercent;

    @Value("${material.oneservice.up-rank.app-key:496b1dd505103413f42117ff1d5840ca}")
    private String appKey;

    @Value("${material.oneservice.up-rank.secret:tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=}")
    private String secret;

    @Value("${material.oneservice.up-rank.api-id:api_2537}")
    private String apiId;


    /**
     * 180累积的和dayType其实已经没有关系了
     *
     * @param query
     * @return
     */
    public Pagination<List<UpRankInfo>> page(UpRankQuery query) {
        Builder req = baseReq();

        List<AdvFilter.Builder> advFilters = new ArrayList<>();

        Tuple2<String, LocalDate> readyDate = helper.findLatestOpenApiLogDateAndDate(apiId);

        this.whereLogdateEq(req, advFilters, query, readyDate._2);

        this.whereKeywordLike(req, advFilters, query);

        this.whereArchiveTidNameIn(req, advFilters, query);

        this.whereFansNumBetween(req, advFilters, query);

        this.whereRewordTypeEq(req, advFilters, query);

        this.whereAdIndustryNameIn(req, advFilters, query);

        this.whereUpAuthTypeEq(req, advFilters, query);

        this.whereUpGenderEq(req, advFilters, query);

        this.whereUpLevelIn(req, advFilters, query);

        this.whereFansGenderRatioBetween(req, advFilters, query);

        this.whereFansAgeRangeBetween(req, advFilters, query);

        this.whereArchiveNumBetween(req, advFilters, query);

        this.whereRecent10ArchivePlayNumBetween(req, advFilters, query);

        this.whereRecent10ArchiveHudongRateBetween(req, advFilters, query);

        this.whereUpUidIn(req, advFilters, query);

        this.orderByAndLimitOffset(req, advFilters, query);

        req.setAdvFilter(AdvFilter
                .newBuilder()
                .setType("and")
                .addAllExpress(advFilters
                        .stream()
                        .map(AdvFilter.Builder::build)
                        .collect(Collectors.toList()))
                .build());

        QueryResp rsp = serviceOpenApiManagerBlockingStub.query(req.build());

        String dataReadyTime = lastestUpdateTimeFormatter.format(readyDate._2);

        PageVo page = rsp.getPageVo();

        List<UpRankInfo> results = rsp.getRowsList().stream()
                .map(map -> {
                    UpRankInfo data = UpRankItemOneserviceDTO
                            .fromMapValue(map.getValueMap())
                            .setDataReadyTime(dataReadyTime)
                            .toBO();
                    return data;
                })
                .collect(Collectors.toList());

        return new Pagination<>(page.getPage(), (int) page.getTotalSize(), results);

    }


    private void orderByAndLimitOffset(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query) {
        req.setPageReq(PageReq.newBuilder()
                .setPage(query.getPn())
                .setPageSize(query.getPs())
                .build());

        // sort
        req.addAllOrders(Lists.newArrayList(Try.of(() -> {
            switch (Optional.ofNullable(query.getSortBy()).orElse(MaterialSortBy.play)) {
                case play: {
                    return "avg_play_recent10 desc";
                }
                case archive_num: {
                    return "avs desc";
                }
                case fans: {
                    return "fans desc";
                }
                default: {
                    throw new UnsupportedOperationException("unsupported sort type");
                }
            }
        }).get()));

    }

    private void whereUpUidIn(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions, UpRankQuery query) {

        if (CollectionUtils.isEmpty(query.getUpMids())) {
            return;
        }

        helper.appendAdvParams("up_mid", "in", query.getUpMids(), rootAdvFilterExpressions);

    }

    private void whereRecent10ArchiveHudongRateBetween(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query) {

        if (!StringUtils.isEmpty(query.getRecent10HudongRateFrom())
                && isPositiveDouble(query.getRecent10HudongRateFrom())) {

            helper.appendAdvParams("avg_interact_recent10", ">", query.getRecent10HudongRateFrom(),
                    rootAdvFilterExpressions);
        }

        if (!StringUtils.isEmpty(query.getRecent10HudongRateTo())
                && isPositiveDouble(query.getRecent10HudongRateTo())) {

            helper.appendAdvParams("avg_interact_recent10", "<=", query.getRecent10HudongRateTo(),
                    rootAdvFilterExpressions);
        }

    }

    private boolean isPositiveDouble(String value) {

        return Try.of(() -> Double.parseDouble(value) > 0)
                .getOrElse(false);

    }

    private void whereRecent10ArchivePlayNumBetween(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query) {

        if (query.getRecent10PlayFrom() != null && query.getRecent10PlayFrom() > 0) {
            helper.appendAdvParams("avg_play_recent10", ">", query.getRecent10PlayFrom().toString(),
                    rootAdvFilterExpressions);
        }

        if (query.getRecent10PlayTo() != null && query.getRecent10PlayTo() > 0) {
            helper.appendAdvParams("avg_play_recent10", "<=", query.getRecent10PlayTo().toString(),
                    rootAdvFilterExpressions);
        }

    }

    private void whereArchiveNumBetween(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query) {

        if (query.getArchiveNumFrom() != null && query.getArchiveNumFrom() > 0) {

            helper.appendAdvParams("avs", ">", query.getArchiveNumFrom().toString(), rootAdvFilterExpressions);

        }

        if (query.getArchiveNumTo() != null && query.getArchiveNumTo() > 0) {
            helper.appendAdvParams("avs", "<=", query.getArchiveNumTo().toString(), rootAdvFilterExpressions);
        }


    }

    private void whereFansAgeRangeBetween(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query) {

        FansAgeRange fansAgeRange = FansAgeRange.getByCode(query.getFansAgeRange());

        if (fansAgeRange == null) {
            return;
        }

        helper.appendAdvParams("age_range", "=", String.valueOf(fansAgeRange.getCode()), rootAdvFilterExpressions);
    }

    private void whereFansGenderRatioBetween(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query) {

        if (query.getFansGender() == null) {
            return;
        }

        switch (query.getFansGender()) {

            case male_major: {

                helper.appendAdvParams("male_fans_ratio", ">=", maleMajorPercent, rootAdvFilterExpressions);

                break;
            }
            case female_major: {
                helper.appendAdvParams("male_fans_ratio", "<=", femaleMajorPercent, rootAdvFilterExpressions);

                break;
            }

            case balance: {
                helper.appendAdvParams("male_fans_ratio", ">", femaleMajorPercent, rootAdvFilterExpressions);
                helper.appendAdvParams("male_fans_ratio", "<", maleMajorPercent, rootAdvFilterExpressions);
                break;
            }
            case all:
            default: {
                break;
            }

        }


    }

    private void whereUpLevelIn(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions, UpRankQuery query) {

        if (CollectionUtils.isEmpty(query.getUpLevels())) {
            return;
        }

        helper.appendAdvParams("level", "in",
                query.getUpLevels().stream().map(String::valueOf).collect(Collectors.toList()),
                rootAdvFilterExpressions);

    }

    private void whereUpGenderEq(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions, UpRankQuery query) {

        if (query.getUpGender() == null) {
            return;
        }
        helper.appendAdvParams("up_gender", "=", String.valueOf(query.getUpGender().getCode()),
                rootAdvFilterExpressions);
    }

    private void whereUpAuthTypeEq(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions, UpRankQuery query) {

        if (query.getUpType() == null) {
            return;
        }

        helper.appendAdvParams("verify_type", "in",
                query.getUpType().getVerifyTypes(), rootAdvFilterExpressions);

    }


    private void whereAdIndustryNameIn(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query) {

        List<String> firstIndustryNames = Optional.ofNullable(query.getFirstIndustryNames())
                .orElse(new ArrayList<>())
                .stream()
                .filter(name -> !StringUtils.isEmpty(name))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(firstIndustryNames)) {

            return;
        }

        rootAdvFilterExpressions.add(AdvFilter.newBuilder()
                .setType("or")
                .addAllExpress(firstIndustryNames.stream().map(industryName -> {
                    return AdvFilter.newBuilder()
                            .setField("ad_first_industry_name_list")
                            .setOperator("like")
                            .addValues(industryName)
                            .build();

                }).collect(Collectors.toList())));


    }

    private void whereRewordTypeEq(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions, UpRankQuery query) {

        UpRewardType rewardType = UpRewardType.getByCode(query.getRewardType());
        switch (rewardType) {
            case huahuo: {

                helper.appendAdvParams("is_huahuo", "=", "1", rootAdvFilterExpressions);
                break;

            }
            case rewarded: {
                helper.appendAdvParams("is_rewarded", "=", "1", rootAdvFilterExpressions);
                break;
            }
            case all:
            default: {
                break;

            }
        }

    }

    private void whereFansNumBetween(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions, UpRankQuery query) {

        if (query.getFansFrom() != null && query.getFansFrom() > 0) {

            helper.appendAdvParams("fans", ">", query.getFansFrom().toString(), rootAdvFilterExpressions);
        }

        if (query.getFansTo() != null && query.getFansTo() > 0) {
            helper.appendAdvParams("fans", "<=", query.getFansTo().toString(), rootAdvFilterExpressions);
        }

    }

    private void whereArchiveTidNameIn(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query) {

        if (CollectionUtils.isEmpty(query.getTidNames())) {
            return;
        }

        helper.appendAdvParams("avs_tid_name", "in", query.getTidNames(), rootAdvFilterExpressions);


    }

    private void whereKeywordLike(Builder req, List<AdvFilter.Builder> rootAdvFilterExpressions, UpRankQuery query) {

        if (StringUtils.isEmpty(query.getKeyword()) || query.getKeywordType() == null) {
            return;
        }

        switch (query.getKeywordType()) {
            case up_mid: {
                helper.appendAdvParams("up_mid", "=",
                        Optional.ofNullable(Try.of(() -> Long.valueOf(query.getKeyword())).getOrNull())
                                .map(String::valueOf)
                                .orElse("-1"),
                        rootAdvFilterExpressions);
                break;
            }
            case up_name: {

                //
                helper.appendAdvParams("lower_up_name", "like", query.getKeyword().toLowerCase(Locale.ROOT),
                        rootAdvFilterExpressions);
                break;

            }

            case composite: {

                AdvFilter.Builder filter = AdvFilter.newBuilder()
                        .setType("or")

                        .addExpress(AdvFilter.newBuilder()
                                .setField("lower_up_name")
                                .setOperator("like")
                                .addValues(query.getKeyword().toLowerCase())
                        );

                if (Try.of(() -> Long.valueOf(query.getKeyword())).getOrNull() != null) {

                    // 能数字化
                    filter.addExpress(AdvFilter.newBuilder()
                            .setField("up_mid")
                            .setOperator("=")
                            .addValues(query.getKeyword())
                    );
                }

                rootAdvFilterExpressions.add(filter);
                break;
            }
            default: {

                throw new IllegalArgumentException("unknown keyword type: " + query.getKeywordType());

            }

        }

    }

    private void whereLogdateEq(Builder req,
            List<AdvFilter.Builder> rootAdvFilterExpressions,
            UpRankQuery query,
            LocalDate localDate) {

        helper.appendAdvParams("log_date", "=", logdateFormatter.format(localDate),
                rootAdvFilterExpressions);
    }


    /**
     * @return
     */
    private Builder baseReq() {

        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey(appKey)
                .setSecret(secret)
                .setApiId(apiId)
                .build();

        Builder openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader);

        return openApiReq;

    }


}
