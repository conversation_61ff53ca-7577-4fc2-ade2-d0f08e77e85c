package com.bilibili.mgk.material.center.service.aigc.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 审核拒绝的原素材
 *
 * <AUTHOR>
 * @desc
 * @date 2025/4/7
 */
@Data
@Accessors(chain = true)
public class RejectedMaterialAigcRecord implements SnakeCaseBody {


    /**
     * 自增id
     */
    private Long id;

    /**
     * 素材id， mgk_material
     */
    private String materialId;

    /**
     * 旧素材id lau_material
     */
    private Long lauMaterialId;


    /**
     * 素材类型 {@link com.bilibili.mgk.material.center.service.creative.model.MaterialIdType}
     */
    private String materialIdType;


    /**
     * 素材唯一键
     */
    private String materialUk;


    /**
     * 版本号，预留，目前不启用都为0，可能需要用于参与构建唯一键，仅最新版本的生效 参考聚合argMax
     */
    private Long version;

    /**
     * 拒审理由
     */
    private String rejectReason;

    /**
     * 拒审key， md5(拒审理由)
     */
    private String rejectKey;


    /**
     * 首次收到的拒审时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date rejectTime;

    /**
     * aigc素材生成提交时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date aigcSubmitTime;

    /**
     * aigc素材生成完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date aigcCompleteTime;


    private Integer aigcErrorCode;
    /**
     * aigc素材生成的错误消息
     */
    private String aigcErrorMsg;


    private RejectedMaterialRegenerationStatus aigcStatus;


    /**
     * aigc素材生成的请求id
     */
    private String aigcRequestId;


    private String extra;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ctime;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mtime;


    @Data
    @Accessors(chain = true)
    public static class RejectedMaterialExtra implements SnakeCaseBody {

        private String mainAuditCls;

        private String subAuditCls;

        private String auditInfo;

        private Integer creativeId;

    }


    public boolean canResubmitAigcTask(Integer algoCallbackTimeoutSeconds) {

        switch (this.getAigcStatus()) {
            case init:
            case failed: {
                return true;
            }
            case success: {
                // false 没必要重新生成
                return false;
            }
            case processing: {

                return new Date(new Date().getTime() - algoCallbackTimeoutSeconds * 1000)
                        .after(this.getAigcSubmitTime());
            }
            default: {
                return false;
            }
        }


    }


}
