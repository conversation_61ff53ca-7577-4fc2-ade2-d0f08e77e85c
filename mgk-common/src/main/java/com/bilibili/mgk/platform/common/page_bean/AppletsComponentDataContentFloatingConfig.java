package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataContentFloatingConfig
 * @author: gaoming
 * @date: 2021/12/03
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataContentFloatingConfig implements Serializable {
    private static final long serialVersionUID = 6726065394178663859L;

    private Integer floatType;

    private Integer showTimeStatus;
    private Integer start;
    private AppletsComponentDataContentFloatingFormConfig form;
    private Integer playStyleType;

    private AppletsComponentDataContentFloatingLinkConfig link;
    private AppletsComponentDataContentFloatingFloatSettingConfig floatSetting;
    private AppletsComponentDataContentFloatingButtonStyleConfig buttonStyle;


}
