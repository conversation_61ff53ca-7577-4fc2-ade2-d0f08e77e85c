CREATE TABLE `mgk_material_watermark`
(
    `id`                    bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `img_md5`            varchar(100)  default ''  not null comment '图片md5',
    `img_wm_md5`            varchar(100)  default ''  not null comment '图片md5',
    `img_url`            varchar(300)  default ''  not null comment '图片md5',
    `img_wm_url`            varchar(300)  default ''  not null comment '图片md5',
    `img_size`       bigint(20) NOT NULL  DEFAULT 0 COMMENT  '图片大小',
    `img_wm_size`       bigint(20) NOT NULL  DEFAULT 0 COMMENT  '图片大小',
    `extra`       varchar(1500)   NOT NULL DEFAULT '' COMMENT '额外参数',
    `ctime`                 datetime     NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`                 datetime     NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`               tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',
    `wm_type`               int(4)   NOT NULL DEFAULT 0 COMMENT '水印类型版本',
    PRIMARY KEY (`id`),
    KEY `idx_md5` (`img_md5`) USING BTREE,
    UNIQUE KEY `idx_md5_type` (`img_md5`, `wm_type`) USING BTREE ,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='图片素材水印';



