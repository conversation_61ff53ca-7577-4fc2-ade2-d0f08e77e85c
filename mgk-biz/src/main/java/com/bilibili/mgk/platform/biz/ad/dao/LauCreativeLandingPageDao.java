package com.bilibili.mgk.platform.biz.ad.dao;

import com.bilibili.mgk.platform.biz.ad.po.LauCreativeLandingPagePo;
import com.bilibili.mgk.platform.biz.ad.po.LauCreativeLandingPagePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauCreativeLandingPageDao {
    long countByExample(LauCreativeLandingPagePoExample example);

    int deleteByExample(LauCreativeLandingPagePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauCreativeLandingPagePo record);

    int insertBatch(List<LauCreativeLandingPagePo> records);

    int insertUpdateBatch(List<LauCreativeLandingPagePo> records);

    int insert(LauCreativeLandingPagePo record);

    int insertUpdateSelective(LauCreativeLandingPagePo record);

    int insertSelective(LauCreativeLandingPagePo record);

    List<LauCreativeLandingPagePo> selectByExample(LauCreativeLandingPagePoExample example);

    LauCreativeLandingPagePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauCreativeLandingPagePo record, @Param("example") LauCreativeLandingPagePoExample example);

    int updateByExample(@Param("record") LauCreativeLandingPagePo record, @Param("example") LauCreativeLandingPagePoExample example);

    int updateByPrimaryKeySelective(LauCreativeLandingPagePo record);

    int updateByPrimaryKey(LauCreativeLandingPagePo record);
}