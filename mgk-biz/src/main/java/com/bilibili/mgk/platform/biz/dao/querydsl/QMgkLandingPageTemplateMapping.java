package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkLandingPageTemplateMappingQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkLandingPageTemplateMapping is a Querydsl query type for MgkLandingPageTemplateMappingQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkLandingPageTemplateMapping extends com.querydsl.sql.RelationalPathBase<MgkLandingPageTemplateMappingQueryDSLPo> {

    private static final long serialVersionUID = **********;

    public static final QMgkLandingPageTemplateMapping mgkLandingPageTemplateMapping = new QMgkLandingPageTemplateMapping("mgk_landing_page_template_mapping");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final NumberPath<Integer> isModel = createNumber("isModel", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Long> pageId = createNumber("pageId", Long.class);

    public final NumberPath<Long> templatePageId = createNumber("templatePageId", Long.class);

    public final com.querydsl.sql.PrimaryKey<MgkLandingPageTemplateMappingQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkLandingPageTemplateMapping(String variable) {
        super(MgkLandingPageTemplateMappingQueryDSLPo.class, forVariable(variable), "null", "mgk_landing_page_template_mapping");
        addMetadata();
    }

    public QMgkLandingPageTemplateMapping(String variable, String schema, String table) {
        super(MgkLandingPageTemplateMappingQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkLandingPageTemplateMapping(String variable, String schema) {
        super(MgkLandingPageTemplateMappingQueryDSLPo.class, forVariable(variable), schema, "mgk_landing_page_template_mapping");
        addMetadata();
    }

    public QMgkLandingPageTemplateMapping(Path<? extends MgkLandingPageTemplateMappingQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_landing_page_template_mapping");
        addMetadata();
    }

    public QMgkLandingPageTemplateMapping(PathMetadata metadata) {
        super(MgkLandingPageTemplateMappingQueryDSLPo.class, metadata, "null", "mgk_landing_page_template_mapping");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(6).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(isModel, ColumnMetadata.named("is_model").withIndex(5).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(8).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(pageId, ColumnMetadata.named("page_id").withIndex(3).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(templatePageId, ColumnMetadata.named("template_page_id").withIndex(4).ofType(Types.BIGINT).withSize(20).notNull());
    }

}

