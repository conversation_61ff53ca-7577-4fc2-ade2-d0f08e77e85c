package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkLandingPageImageEnhancementPo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageImageEnhancementPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkLandingPageImageEnhancementDao {
    long countByExample(MgkLandingPageImageEnhancementPoExample example);

    int deleteByExample(MgkLandingPageImageEnhancementPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkLandingPageImageEnhancementPo record);

    int insertBatch(List<MgkLandingPageImageEnhancementPo> records);

    int insertUpdateBatch(List<MgkLandingPageImageEnhancementPo> records);

    int insert(MgkLandingPageImageEnhancementPo record);

    int insertUpdateSelective(MgkLandingPageImageEnhancementPo record);

    int insertSelective(MgkLandingPageImageEnhancementPo record);

    List<MgkLandingPageImageEnhancementPo> selectByExample(MgkLandingPageImageEnhancementPoExample example);

    MgkLandingPageImageEnhancementPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkLandingPageImageEnhancementPo record, @Param("example") MgkLandingPageImageEnhancementPoExample example);

    int updateByExample(@Param("record") MgkLandingPageImageEnhancementPo record, @Param("example") MgkLandingPageImageEnhancementPoExample example);

    int updateByPrimaryKeySelective(MgkLandingPageImageEnhancementPo record);

    int updateByPrimaryKey(MgkLandingPageImageEnhancementPo record);
}