package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018年8月26日
 * @desc 操作系统类型
 */
@AllArgsConstructor
public enum OsEnum {
    ANDROID(0, "Android"),
    IOS(1, "IOS"),
    WINDOWS_PHONE(2, "windowsPhone"),
    OTHER(3, "other");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static OsEnum getByCode(Integer code) {
        for (OsEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return null;
    }
}
