package com.bilibili.mgk.platform.portal.webapi.enterprise;

import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.collage.api.service.ICollageEnterpriseService;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.service.WebCollageService;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * @file: EnterpriseControllerTest
 * @author: gaoming
 * @date: 2020/12/11
 * @version: 1.0
 * @description:
 **/
public class EnterpriseControllerTest extends BaseMockitoTest {

    @InjectMocks
    private EnterpriseController enterpriseController;

    @Mock
    private ICollageEnterpriseService collageEnterpriseService;

    @Mock
    private WebCollageService webCollageService;

    private Context context;

    @Before
    public void setUp() throws Exception {
        context = new Context();
        context.setAccountId(10005);
        context.setType(OperatorType.SYSTEM.getCode());
        context.setProxyName("SYS_TEST");
        context.setSalesType(SalesType.CPM.getCode());
        context.setUsername("bilibili_user");
        context.setProxyId(1);
    }

    @Test
    public void testGetLandingPageList() throws ServiceException {
//        enterpriseController.getLandingPageList(context, 1L, "", 1, 1, 1, 1, 1, 1, 1,"0", "");
    }

    @Test
    public void testGetEnterprise() {
        enterpriseController.getEnterprise(context);
    }
}