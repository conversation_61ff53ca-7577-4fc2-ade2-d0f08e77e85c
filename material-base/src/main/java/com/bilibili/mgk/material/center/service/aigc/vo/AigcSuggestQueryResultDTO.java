package com.bilibili.mgk.material.center.service.aigc.vo;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/9
 */
@Data
@Accessors(chain = true)
public class AigcSuggestQueryResultDTO implements SnakeCaseBody {


    private String rejectedMaterialId;

    private String rejectedMaterialIdType;


    private String rejectedMaterialUk;

    private String rejectReason;


    private String aigcPageCursor;


    private List<AigcMaterialDTO> aigcMaterials;


    @Data
    @Accessors(chain = true)
    public static class AigcMaterialDTO implements SnakeCaseBody {


        private String aigcMaterialId;

        private String aigcMaterialIdType;

        private String aigcMaterialUk;


        private String aigcMaterialContent;

        private String aigcScore;

    }

}
