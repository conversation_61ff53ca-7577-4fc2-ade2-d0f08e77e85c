package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * MgkVideoStatusQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class MgkVideoStatusQueryDSLPo {

    private Long bizId;

    private java.sql.Timestamp ctime;

    private Long dislikedNum;

    private Integer id;

    private Long likedNum;

    private java.sql.Timestamp mtime;

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getDislikedNum() {
        return dislikedNum;
    }

    public void setDislikedNum(Long dislikedNum) {
        this.dislikedNum = dislikedNum;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getLikedNum() {
        return likedNum;
    }

    public void setLikedNum(Long likedNum) {
        this.likedNum = likedNum;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
         return "bizId = " + bizId + ", ctime = " + ctime + ", dislikedNum = " + dislikedNum + ", id = " + id + ", likedNum = " + likedNum + ", mtime = " + mtime;
    }

}

