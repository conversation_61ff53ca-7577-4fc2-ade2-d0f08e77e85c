package com.bilibili.mgk.platform.portal.webapi.archive.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: AidCidVo
 * @author: gaoming
 * @date: 2021/12/29
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AidCidVo {
    @ApiModelProperty(value = "稿件id")
    private Long avid;
    @ApiModelProperty(value = "cid")
    private Long cid;
}
