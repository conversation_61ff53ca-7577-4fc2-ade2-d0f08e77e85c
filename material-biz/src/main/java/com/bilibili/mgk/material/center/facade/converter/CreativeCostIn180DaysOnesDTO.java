package com.bilibili.mgk.material.center.facade.converter;

import com.bilibili.mgk.material.center.service.aigc.model.CreativeCostIn180Days;
import com.bilibili.mgk.material.center.util.JsonUtil;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/25
 */
@Data
@Accessors(chain = true)
public class CreativeCostIn180DaysOnesDTO {

//    private String logDate;

//    private Long accountId;

    private Long creativeId;


    /**
     * 累计出价 单位（毫分）
     */
    private Long sumChargedCostMillis;


    public static CreativeCostIn180DaysOnesDTO fromMapValue(Map<String, String> mapValue) {
        return JsonUtil.fromJson(mapValue, CreativeCostIn180DaysOnesDTO.class);
    }


    public CreativeCostIn180Days toBO() {

        return OneServiceDTOConverter.converter.toBO(this);
    }


}
