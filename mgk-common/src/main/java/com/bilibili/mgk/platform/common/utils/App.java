package com.bilibili.mgk.platform.common.utils;

import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * Hello world!
 *
 */
public class App 
{
    public static void main( String[] args ) {
        Long ts = new Timestamp(System.currentTimeMillis()).getTime();
        String nonce = "1563";
        String secretId = "99681026020333237118";
        String secretKey = "f636fb138c1b4085919779d4262603a5";

        OkHttpClientUtil util = new OkHttpClientUtil();
        String url="https://sandbox.open.10086.cn/38857911/gateway/api/common/personalization/region/getExpress";
        Map<String,Object> headers = new HashMap<>();
        headers.put("X-Hmac-Auth-Secret-Id",secretId);
        headers.put("X-Hmac-Auth-Timestamp",ts);
        headers.put("X-Hmac-Auth-Nonce",nonce);
        MessageEncrypt encrypt = new MessageEncrypt();
        encrypt.setUri("/gateway/api/common/personalization/region/getExpress");
        encrypt.setNonce(nonce);
        Map<String,String[]> maps = new HashMap<>();
      //  String payLoad="{\"getType\":\"1\",\"telType\":\"0\",\"cardType\":\"2\",\"province\":\"100\",\"city\":\"100\",\"maketingAction\":\"110\",\"numberAmount\":\"2\"}";
      //  String payLoad="{\"activityId\":\"1354968481069203456\"}";
       // String payLoad ="{\"authNonce\":\"7879\",\"busiCode\":\"001\",,\"serviceNoType\":\"01\",\"serviceNo\":\"18327046883\"}";
     // String payLoad ="{\"mobile\":\"18876315178\"}";

        String payLoad="";
        if(StringUtils.isNotEmpty(payLoad)){
            maps.put(payLoad,new String[]{null});
        }else{
            maps.put("{}",new String[]{null});
        }
        encrypt.setParamMap(maps);
        encrypt.setTimestamp(ts);
        String signature = encrypt.getSignature(secretKey);
        System.out.println(signature);
        headers.put("X-Hmac-Auth-Signature",signature);


        String result = util.postRequest(url,headers,payLoad);
        System.out.println(result);

    }

}
