package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataFormAdvanceSettingConfig
 * @author: gaoming
 * @date: 2021/12/03
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataSettingConfig implements Serializable {
    private static final long serialVersionUID = -6588900049361483631L;
    private AppletsComponentDataSettingCountConfig count;

    private AppletsComponentDataSettingLinkConfig link;

    private AppletsComponentDataSettingRecentSubmitConfig recentSubmit;

    private AppletsComponentDataSettingLocationButtonConfig locationButton;
}
