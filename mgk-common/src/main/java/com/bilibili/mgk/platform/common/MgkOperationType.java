package com.bilibili.mgk.platform.common;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @ClassName MgkOperationType
 * <AUTHOR>
 * @Date 2022/12/19 2:42 上午
 * @Version 1.0
 **/
public enum MgkOperationType {
    // 0-创建, 1-删除, 2-更新,4-更新状态
    CREATE(0, "创建"),
    DISABLE(1, "删除"),
    UPDATE(2, "更新"),
    UPDATE_STATUS(4, "更新状态"),
    PUBLISH(5, "发布"),
    DOWNLINE(6, "下线"),
    SEND_AUDIT(7, "推审"),
    AUDIT_PASS(8, "审核通过"),
    AUDIT_REJECT(9, "审核驳回"),
    ADMIN_REJECT(10, "管理员驳回")
    ;

    public static final List<Integer> QUERY_LOG_REPLACE_OPERATE_LIST = Lists.newArrayList(
            SEND_AUDIT.getCode(), PUBLISH.getCode(), DOWNLINE.getCode(),
            AUDIT_PASS.getCode(), AUDIT_REJECT.getCode());

    public static final List<Integer> UPDATE_STATUS_OPERATE_LIST = Lists.newArrayList(
            PUBLISH.getCode(), DOWNLINE.getCode(),
            AUDIT_PASS.getCode(), AUDIT_REJECT.getCode());

    public static final List<Integer> AUDIT_STATUS_OPERATE_LIST = Lists.newArrayList(
            AUDIT_PASS.getCode(),
            AUDIT_REJECT.getCode());

    private Integer code;

    private String desc;

    MgkOperationType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MgkOperationType getByCode(Integer code) {
        for(MgkOperationType ot: values()) {
            if(ot.getCode().equals(code)) {
                return ot;
            }
        }
        throw new IllegalArgumentException("unknown MgkOperationType Enum code:" + code);
    }
}
