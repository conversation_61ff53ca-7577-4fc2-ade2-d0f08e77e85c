package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ConsultComponentStatusEnum {
    /**
     * 未生效
     */
    INVALID((byte)0, "未生效"),

    /**
     * 已生效
     */
    VALID((byte)1, "已生效");

    @Getter
    private byte code;
    @Getter
    private String desc;


    public static ConsultComponentStatusEnum getByCode(byte code) {
        for (ConsultComponentStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code ConsultComponentEnum " + code);
    }
}
