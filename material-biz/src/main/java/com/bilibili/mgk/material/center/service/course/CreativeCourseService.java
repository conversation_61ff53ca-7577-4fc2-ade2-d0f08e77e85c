package com.bilibili.mgk.material.center.service.course;

import com.bilibili.mgk.material.center.service.course.model.CourseDirectory;
import com.bilibili.mgk.material.center.service.course.model.CreativeCourse;
import com.bilibili.mgk.material.center.service.course.vo.CourseFavoriteAddReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseFavoritePageReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseFavoriteRemoveReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseFavoriteResp;
import com.bilibili.mgk.material.center.service.course.vo.CourseIdReq;
import com.bilibili.mgk.material.center.service.course.vo.CoursePageReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseRelatedReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseSearchReq;
import com.bilibili.mgk.material.center.service.course.vo.CreativeCourseSearchResult;
import com.bilibili.mgk.material.center.service.course.vo.DirectoryListReq;
import com.biz.common.doc.tree.common.Pagination;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Optional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */
public interface CreativeCourseService {


    /**
     * 查询所有二级目录
     *
     * @param req
     * @return
     */
    List<CourseDirectory> listAllTwoLevelDirectory(DirectoryListReq req);


    /**
     * 根据课程id查询课程
     *
     * @param req
     * @return
     */
    List<CreativeCourse> listById(CourseIdReq req);


    List<CreativeCourse> relatedCourse(CourseRelatedReq req);

    /**
     * 分页查询课程
     *
     * @param coursePageReq
     * @return
     */
    Pagination<List<CreativeCourse>> pageCourse(CoursePageReq coursePageReq);

    /**
     * 搜索课程
     *
     * @param req
     * @return
     */
    Pagination<List<CreativeCourseSearchResult>> searchCourse(CourseSearchReq req);


    CourseFavoriteResp addFavorite(CourseFavoriteAddReq req);

    CourseFavoriteResp removeFavorite(CourseFavoriteRemoveReq req);


    Pagination<List<CreativeCourse>> pageFavoriteCourse(CourseFavoritePageReq coursePageReq);




    /**
     * 根据课程id查询课程
     *
     * @param courseId
     * @return
     */
    default Optional<CreativeCourse> getById(Long accountId, Long courseId, Boolean isShow) {
        List<CreativeCourse> list = listById(new CourseIdReq()
                .setAccountId(accountId)
                .setCourseIds(Lists.newArrayList(courseId))
                .setIsShow(true)
        );

        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        } else {
            return Optional.of(list.get(0));
        }
    }
}
