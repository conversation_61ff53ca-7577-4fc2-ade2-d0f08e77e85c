package com.bilibili.mgk.platform.portal.webapi.wechat.vo;


import com.bilibili.mgk.platform.portal.annotation.ExcelResources;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WorkChatCustomerAcquisitionDataVo {

    @ApiModelProperty(value = "获客链接名称")
    @ExcelResources(title = "获客链接名称")
    private String linkName;

    @ApiModelProperty(value = "获客链接id")
    @ExcelResources(title = "获客链接id")
    private String id;

    @ApiModelProperty(value = "转化时间")
    @ExcelResources(title = "转化时间")
    private String convTime;

    @ApiModelProperty(value = "转化类型")
    @ExcelResources(title = "转化类型")
    private String dataType;

    @ApiModelProperty(value = "来源页面ID")
    @ExcelResources(title = "来源页面ID")
    private String pageId;

    @ApiModelProperty(value = "来源页面名称")
    @ExcelResources(title = "来源页面名称")
    private String title;

    @ApiModelProperty(value = "创意ID")
    @ExcelResources(title = "创意ID")
    private String creativeId;
    @ApiModelProperty(value = "创意标题")
    @ExcelResources(title = "创意标题")
    private String creativeTitle;
    @ApiModelProperty(value = "单元ID")
    @ExcelResources(title = "单元ID")
    private String unitId;
    @ApiModelProperty(value = "单元名称")
    @ExcelResources(title = "单元名称")
    private String unitName;
    @ApiModelProperty(value = "计划ID")
    @ExcelResources(title = "计划ID")
    private String campaignId;
    @ApiModelProperty(value = "计划名称")
    @ExcelResources(title = "计划名称")
    private String campaignName;

    @ApiModelProperty(value = "售卖类型")
    @ExcelResources(title = "售卖类型")
    private String salesTypeDesc;
    @ApiModelProperty(value = "trackId")
    @ExcelResources(title = "trackId")
    private String trackId;



}
