package com.bilibili.mgk.platform.common.complain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 二级投诉缘由
 * @Date 2019/6/10 12:16
 * @Created by wangbin01
 */
@AllArgsConstructor
public enum SecondComplainTypeEnum {
    DEFAULT(null, "未知"),

    //其他
    OTHER_FREQUENCY_SHOW(5, "频繁展示"),
    OTHER_LOW_QUALITY(6, "广告/推广质量低"),
    OTHER_OTHER(7, "不在以上分类"),

    //侵权
    TORT(8, "侵权"),

    //不实消息
    FABRICATE_FAKE_ACTIVITY(9, "虚假活动"),
    FABRICATE_FAKE_GOODS(10, "涉及售卖假货"),
    FABRICATE_OTHER(11, "不在以上分类"),

    //涉黄信息
    COPY_WRITING_VULGAR(12, "文案低俗"),
    PICTURE_VULGAR(13, "图片低俗"),
    VIDEO_VULGAR(14, "视频低俗"),

    //恶心血腥
    COPY_WRITING_NAUSEA(15, "文案恶心"),
    PICTURE_NAUSEA(16, "图片恶心"),
    PICTURE_BLOODY_AWFUL(17, "图片血腥恐怖");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static SecondComplainTypeEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("二级投诉缘由不可为空");
        }
        for(SecondComplainTypeEnum lrt: values()) {
            if(code.equals(lrt.getCode())) {
                return lrt;
            }
        }
//        throw new IllegalArgumentException("unknow SecondComplainTypeEnum code:" + code);
        return SecondComplainTypeEnum.DEFAULT;
    }
}
