package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotVideoIsCollectEnum
 * @author: gaoming
 * @date: 2020/11/12
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
public enum MgkHotVideoIsCollectEnum {

    NOT_COLLECT_ENUM(0, "未收藏"),
    COLLECT_ENUM(1, "已收藏");

    @Getter
    private final Integer code;
    @Getter
    private final String desc;


    public static MgkHotVideoIsCollectEnum getByCode(int code) {
        for (MgkHotVideoIsCollectEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotVideoIsCollectEnum " + code);
    }

}
