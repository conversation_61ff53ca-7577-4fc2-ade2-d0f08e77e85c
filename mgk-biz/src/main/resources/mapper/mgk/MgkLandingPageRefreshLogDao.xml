<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkLandingPageRefreshLogDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="template_style" jdbcType="INTEGER" property="templateStyle" />
    <result column="page_type" jdbcType="INTEGER" property="pageType" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    <result column="page_config" jdbcType="LONGVARCHAR" property="pageConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, page_id, template_style, page_type, is_deleted, ctime, mtime
  </sql>
  <sql id="Blob_Column_List">
    page_config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mgk_landing_page_refresh_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_landing_page_refresh_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mgk_landing_page_refresh_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_landing_page_refresh_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPoExample">
    delete from mgk_landing_page_refresh_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_landing_page_refresh_log (page_id, template_style, page_type, 
      is_deleted, ctime, mtime, 
      page_config)
    values (#{pageId,jdbcType=BIGINT}, #{templateStyle,jdbcType=INTEGER}, #{pageType,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{pageConfig,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_landing_page_refresh_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="templateStyle != null">
        template_style,
      </if>
      <if test="pageType != null">
        page_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="pageConfig != null">
        page_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="templateStyle != null">
        #{templateStyle,jdbcType=INTEGER},
      </if>
      <if test="pageType != null">
        #{pageType,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="pageConfig != null">
        #{pageConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPoExample" resultType="java.lang.Long">
    select count(*) from mgk_landing_page_refresh_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_landing_page_refresh_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.pageId != null">
        page_id = #{record.pageId,jdbcType=BIGINT},
      </if>
      <if test="record.templateStyle != null">
        template_style = #{record.templateStyle,jdbcType=INTEGER},
      </if>
      <if test="record.pageType != null">
        page_type = #{record.pageType,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pageConfig != null">
        page_config = #{record.pageConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update mgk_landing_page_refresh_log
    set id = #{record.id,jdbcType=INTEGER},
      page_id = #{record.pageId,jdbcType=BIGINT},
      template_style = #{record.templateStyle,jdbcType=INTEGER},
      page_type = #{record.pageType,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      page_config = #{record.pageConfig,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_landing_page_refresh_log
    set id = #{record.id,jdbcType=INTEGER},
      page_id = #{record.pageId,jdbcType=BIGINT},
      template_style = #{record.templateStyle,jdbcType=INTEGER},
      page_type = #{record.pageType,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    update mgk_landing_page_refresh_log
    <set>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=BIGINT},
      </if>
      <if test="templateStyle != null">
        template_style = #{templateStyle,jdbcType=INTEGER},
      </if>
      <if test="pageType != null">
        page_type = #{pageType,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="pageConfig != null">
        page_config = #{pageConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    update mgk_landing_page_refresh_log
    set page_id = #{pageId,jdbcType=BIGINT},
      template_style = #{templateStyle,jdbcType=INTEGER},
      page_type = #{pageType,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      page_config = #{pageConfig,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    update mgk_landing_page_refresh_log
    set page_id = #{pageId,jdbcType=BIGINT},
      template_style = #{templateStyle,jdbcType=INTEGER},
      page_type = #{pageType,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_landing_page_refresh_log (page_id, template_style, page_type, 
      is_deleted, ctime, mtime, 
      page_config)
    values (#{pageId,jdbcType=BIGINT}, #{templateStyle,jdbcType=INTEGER}, #{pageType,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{pageConfig,jdbcType=LONGVARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      page_id = values(page_id),
      template_style = values(template_style),
      page_type = values(page_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      page_config = values(page_config),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_landing_page_refresh_log
      (page_id,template_style,page_type,is_deleted,ctime,mtime,page_config)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.pageId,jdbcType=BIGINT},
        #{item.templateStyle,jdbcType=INTEGER},
        #{item.pageType,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.pageConfig,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_landing_page_refresh_log
      (page_id,template_style,page_type,is_deleted,ctime,mtime,page_config)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.pageId,jdbcType=BIGINT},
        #{item.templateStyle,jdbcType=INTEGER},
        #{item.pageType,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.pageConfig,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      page_id = values(page_id),
      template_style = values(template_style),
      page_type = values(page_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      page_config = values(page_config),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_landing_page_refresh_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="templateStyle != null">
        template_style,
      </if>
      <if test="pageType != null">
        page_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="pageConfig != null">
        page_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="templateStyle != null">
        #{templateStyle,jdbcType=INTEGER},
      </if>
      <if test="pageType != null">
        #{pageType,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="pageConfig != null">
        #{pageConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="pageId != null">
        page_id = values(page_id),
      </if>
      <if test="templateStyle != null">
        template_style = values(template_style),
      </if>
      <if test="pageType != null">
        page_type = values(page_type),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="pageConfig != null">
        page_config = values(page_config),
      </if>
    </trim>
  </insert>
</mapper>