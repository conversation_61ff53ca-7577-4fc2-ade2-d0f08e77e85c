<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.ChinaAreaDao">
    <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.ChinaAreaPo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="level" jdbcType="BIT" property="level"/>
        <result column="parent_code" jdbcType="BIGINT" property="parentCode"/>
        <result column="area_code" jdbcType="BIGINT" property="areaCode"/>
        <result column="zip_code" jdbcType="INTEGER" property="zipCode"/>
        <result column="city_code" jdbcType="CHAR" property="cityCode"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="merger_name" jdbcType="VARCHAR" property="mergerName"/>
        <result column="pinyin" jdbcType="VARCHAR" property="pinyin"/>
        <result column="lng" jdbcType="DECIMAL" property="lng"/>
        <result column="lat" jdbcType="DECIMAL" property="lat"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, level, parent_code, area_code, zip_code, city_code, name, short_name, merger_name, 
    pinyin, lng, lat
  </sql>
    <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPoExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from china_area
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from china_area
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from china_area
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPoExample">
        delete from china_area
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into china_area (level, parent_code, area_code,
        zip_code, city_code, name,
        short_name, merger_name, pinyin,
        lng, lat)
        values (#{level,jdbcType=BIT}, #{parentCode,jdbcType=BIGINT}, #{areaCode,jdbcType=BIGINT},
        #{zipCode,jdbcType=INTEGER}, #{cityCode,jdbcType=CHAR}, #{name,jdbcType=VARCHAR},
        #{shortName,jdbcType=VARCHAR}, #{mergerName,jdbcType=VARCHAR}, #{pinyin,jdbcType=VARCHAR},
        #{lng,jdbcType=DECIMAL}, #{lat,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into china_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="level != null">
                level,
            </if>
            <if test="parentCode != null">
                parent_code,
            </if>
            <if test="areaCode != null">
                area_code,
            </if>
            <if test="zipCode != null">
                zip_code,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="shortName != null">
                short_name,
            </if>
            <if test="mergerName != null">
                merger_name,
            </if>
            <if test="pinyin != null">
                pinyin,
            </if>
            <if test="lng != null">
                lng,
            </if>
            <if test="lat != null">
                lat,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="level != null">
                #{level,jdbcType=BIT},
            </if>
            <if test="parentCode != null">
                #{parentCode,jdbcType=BIGINT},
            </if>
            <if test="areaCode != null">
                #{areaCode,jdbcType=BIGINT},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=INTEGER},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=CHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="mergerName != null">
                #{mergerName,jdbcType=VARCHAR},
            </if>
            <if test="pinyin != null">
                #{pinyin,jdbcType=VARCHAR},
            </if>
            <if test="lng != null">
                #{lng,jdbcType=DECIMAL},
            </if>
            <if test="lat != null">
                #{lat,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPoExample" resultType="java.lang.Long">
        select count(*) from china_area
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update china_area
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.level != null">
                level = #{record.level,jdbcType=BIT},
            </if>
            <if test="record.parentCode != null">
                parent_code = #{record.parentCode,jdbcType=BIGINT},
            </if>
            <if test="record.areaCode != null">
                area_code = #{record.areaCode,jdbcType=BIGINT},
            </if>
            <if test="record.zipCode != null">
                zip_code = #{record.zipCode,jdbcType=INTEGER},
            </if>
            <if test="record.cityCode != null">
                city_code = #{record.cityCode,jdbcType=CHAR},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.shortName != null">
                short_name = #{record.shortName,jdbcType=VARCHAR},
            </if>
            <if test="record.mergerName != null">
                merger_name = #{record.mergerName,jdbcType=VARCHAR},
            </if>
            <if test="record.pinyin != null">
                pinyin = #{record.pinyin,jdbcType=VARCHAR},
            </if>
            <if test="record.lng != null">
                lng = #{record.lng,jdbcType=DECIMAL},
            </if>
            <if test="record.lat != null">
                lat = #{record.lat,jdbcType=DECIMAL},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update china_area
        set id = #{record.id,jdbcType=INTEGER},
        level = #{record.level,jdbcType=BIT},
        parent_code = #{record.parentCode,jdbcType=BIGINT},
        area_code = #{record.areaCode,jdbcType=BIGINT},
        zip_code = #{record.zipCode,jdbcType=INTEGER},
        city_code = #{record.cityCode,jdbcType=CHAR},
        name = #{record.name,jdbcType=VARCHAR},
        short_name = #{record.shortName,jdbcType=VARCHAR},
        merger_name = #{record.mergerName,jdbcType=VARCHAR},
        pinyin = #{record.pinyin,jdbcType=VARCHAR},
        lng = #{record.lng,jdbcType=DECIMAL},
        lat = #{record.lat,jdbcType=DECIMAL}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPo">
        update china_area
        <set>
            <if test="level != null">
                level = #{level,jdbcType=BIT},
            </if>
            <if test="parentCode != null">
                parent_code = #{parentCode,jdbcType=BIGINT},
            </if>
            <if test="areaCode != null">
                area_code = #{areaCode,jdbcType=BIGINT},
            </if>
            <if test="zipCode != null">
                zip_code = #{zipCode,jdbcType=INTEGER},
            </if>
            <if test="cityCode != null">
                city_code = #{cityCode,jdbcType=CHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="mergerName != null">
                merger_name = #{mergerName,jdbcType=VARCHAR},
            </if>
            <if test="pinyin != null">
                pinyin = #{pinyin,jdbcType=VARCHAR},
            </if>
            <if test="lng != null">
                lng = #{lng,jdbcType=DECIMAL},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPo">
    update china_area
    set level = #{level,jdbcType=BIT},
      parent_code = #{parentCode,jdbcType=BIGINT},
      area_code = #{areaCode,jdbcType=BIGINT},
      zip_code = #{zipCode,jdbcType=INTEGER},
      city_code = #{cityCode,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      short_name = #{shortName,jdbcType=VARCHAR},
      merger_name = #{mergerName,jdbcType=VARCHAR},
      pinyin = #{pinyin,jdbcType=VARCHAR},
      lng = #{lng,jdbcType=DECIMAL},
      lat = #{lat,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into china_area (level, parent_code, area_code,
        zip_code, city_code, name,
        short_name, merger_name, pinyin,
        lng, lat)
        values (#{level,jdbcType=BIT}, #{parentCode,jdbcType=BIGINT}, #{areaCode,jdbcType=BIGINT},
        #{zipCode,jdbcType=INTEGER}, #{cityCode,jdbcType=CHAR}, #{name,jdbcType=VARCHAR},
        #{shortName,jdbcType=VARCHAR}, #{mergerName,jdbcType=VARCHAR}, #{pinyin,jdbcType=VARCHAR},
        #{lng,jdbcType=DECIMAL}, #{lat,jdbcType=DECIMAL})
        <trim prefix="on duplicate key update" suffixOverrides=",">
            level = values(level),
            parent_code = values(parent_code),
            area_code = values(area_code),
            zip_code = values(zip_code),
            city_code = values(city_code),
            name = values(name),
            short_name = values(short_name),
            merger_name = values(merger_name),
            pinyin = values(pinyin),
            lng = values(lng),
            lat = values(lat),
        </trim>
    </insert>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into
        china_area
        (level,parent_code,area_code,zip_code,city_code,name,short_name,merger_name,pinyin,lng,lat)
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.level,jdbcType=BIT},
                #{item.parentCode,jdbcType=BIGINT},
                #{item.areaCode,jdbcType=BIGINT},
                #{item.zipCode,jdbcType=INTEGER},
                #{item.cityCode,jdbcType=CHAR},
                #{item.name,jdbcType=VARCHAR},
                #{item.shortName,jdbcType=VARCHAR},
                #{item.mergerName,jdbcType=VARCHAR},
                #{item.pinyin,jdbcType=VARCHAR},
                #{item.lng,jdbcType=DECIMAL},
                #{item.lat,jdbcType=DECIMAL},
            </trim>
        </foreach>
    </insert>
    <insert id="insertUpdateBatch" parameterType="java.util.List">
        insert into
        china_area
        (level,parent_code,area_code,zip_code,city_code,name,short_name,merger_name,pinyin,lng,lat)
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.level,jdbcType=BIT},
                #{item.parentCode,jdbcType=BIGINT},
                #{item.areaCode,jdbcType=BIGINT},
                #{item.zipCode,jdbcType=INTEGER},
                #{item.cityCode,jdbcType=CHAR},
                #{item.name,jdbcType=VARCHAR},
                #{item.shortName,jdbcType=VARCHAR},
                #{item.mergerName,jdbcType=VARCHAR},
                #{item.pinyin,jdbcType=VARCHAR},
                #{item.lng,jdbcType=DECIMAL},
                #{item.lat,jdbcType=DECIMAL},
            </trim>
        </foreach>
        <trim prefix="on duplicate key update" suffixOverrides=",">
            level = values(level),
            parent_code = values(parent_code),
            area_code = values(area_code),
            zip_code = values(zip_code),
            city_code = values(city_code),
            name = values(name),
            short_name = values(short_name),
            merger_name = values(merger_name),
            pinyin = values(pinyin),
            lng = values(lng),
            lat = values(lat),
        </trim>
    </insert>
    <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.ChinaAreaPo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into china_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="level != null">
                level,
            </if>
            <if test="parentCode != null">
                parent_code,
            </if>
            <if test="areaCode != null">
                area_code,
            </if>
            <if test="zipCode != null">
                zip_code,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="shortName != null">
                short_name,
            </if>
            <if test="mergerName != null">
                merger_name,
            </if>
            <if test="pinyin != null">
                pinyin,
            </if>
            <if test="lng != null">
                lng,
            </if>
            <if test="lat != null">
                lat,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="level != null">
                #{level,jdbcType=BIT},
            </if>
            <if test="parentCode != null">
                #{parentCode,jdbcType=BIGINT},
            </if>
            <if test="areaCode != null">
                #{areaCode,jdbcType=BIGINT},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=INTEGER},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=CHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="mergerName != null">
                #{mergerName,jdbcType=VARCHAR},
            </if>
            <if test="pinyin != null">
                #{pinyin,jdbcType=VARCHAR},
            </if>
            <if test="lng != null">
                #{lng,jdbcType=DECIMAL},
            </if>
            <if test="lat != null">
                #{lat,jdbcType=DECIMAL},
            </if>
        </trim>
        <trim prefix="on duplicate key update" suffixOverrides=",">
            <if test="level != null">
                level = values(level),
            </if>
            <if test="parentCode != null">
                parent_code = values(parent_code),
            </if>
            <if test="areaCode != null">
                area_code = values(area_code),
            </if>
            <if test="zipCode != null">
                zip_code = values(zip_code),
            </if>
            <if test="cityCode != null">
                city_code = values(city_code),
            </if>
            <if test="name != null">
                name = values(name),
            </if>
            <if test="shortName != null">
                short_name = values(short_name),
            </if>
            <if test="mergerName != null">
                merger_name = values(merger_name),
            </if>
            <if test="pinyin != null">
                pinyin = values(pinyin),
            </if>
            <if test="lng != null">
                lng = values(lng),
            </if>
            <if test="lat != null">
                lat = values(lat),
            </if>
        </trim>
    </insert>
</mapper>