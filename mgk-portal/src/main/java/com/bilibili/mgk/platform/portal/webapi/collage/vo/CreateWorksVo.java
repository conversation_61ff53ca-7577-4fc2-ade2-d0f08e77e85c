package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/11
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateWorksVo {

    @ApiModelProperty(value = "作品名称")
    private String name;

    @ApiModelProperty(value = "尺寸Id")
    private Integer collage_size_id;

    @ApiModelProperty(value = "模板Id")
    private Integer pattern_id;

    @ApiModelProperty(value = "图层")
    private List<LayerVo> layers;

    @ApiModelProperty(value = "渲染图片的url")
    private String works_url;

    @ApiModelProperty(value = "作品md5")
    private String works_md5;

    @ApiModelProperty(value = "作品大小")
    private Long works_size;
}
