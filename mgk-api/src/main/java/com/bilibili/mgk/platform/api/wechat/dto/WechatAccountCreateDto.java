package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName WechatAccountCreateDto
 * <AUTHOR>
 * @Date 2022/5/31 12:13 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WechatAccountCreateDto implements Serializable {
    private static final long serialVersionUID = -45638534539834753L;
    /**
     * 微信号id
     */
    private Integer id;

    /**
     * 微信号
     */
    private String wechatAccount;

    /**
     * 微信号类型 0-个人号 1-公众号 2-企业微信 3-其他
     */
    private Integer type;

    /**
     * 微信名称
     */
    private String wechatName;

    /**
     * 帐户id
     */
    private Integer accountId;

    /**
     * 备注信息
     */
    private String info;
}
