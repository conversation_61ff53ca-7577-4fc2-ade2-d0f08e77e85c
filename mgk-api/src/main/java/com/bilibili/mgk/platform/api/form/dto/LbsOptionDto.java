package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LbsOptionDto implements Serializable{


    private static final long serialVersionUID = 7929315136329596785L;

    /**
     * 省市码
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 选项类型
     * @See LBSOptionTypeEnum;
     */
    private String type;

    /**
     * 地址
     */
    private String address;


    /**
     * 子级
     */
    private List<LbsOptionDto> children;


}
