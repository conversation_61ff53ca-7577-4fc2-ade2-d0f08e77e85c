package com.bilibili.mgk.platform.biz.es;

import com.bilibili.adp.common.util.Utils;
import lombok.Getter;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Getter
public class SearchParams {
    private int count;
    private Map<String, List<Object>> terms;
    private Map<String, String> matches;
    private Map<String, Range> ranges;
    private Map<String, List<Range>> numberTerms;
    private int page = 0;
    private int size = 10;
    private boolean randomResult;

    private String sortName;
    private SortOrder sortOrder;

    private SearchParams() {}

    public static SearchParams newInstance() {
        final SearchParams searchParams = new SearchParams();
        searchParams.terms = new HashMap<>();
        searchParams.matches = new HashMap<>();
        searchParams.ranges = new HashMap<>();
        searchParams.numberTerms = new HashMap<>();
        searchParams.count = 0;
        return searchParams;
    }

    public SearchParams addTerm(String key, List<?> values) {
        if (StringUtils.hasText(key) && !CollectionUtils.isEmpty(values)) {
            terms.put(key, values.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
            count++;
        }
        return this;
    }

    public SearchParams setSize(Integer size) {
        if (Utils.isPositive(size)) {
            this.size = size;
        }
        return this;
    }

    public SearchParams setPage(Integer page) {
        if (Utils.isPositive(page)) {
            this.page = page;
        }
        return this;
    }

    public SearchParams addMatch(String key, String value) {
        if (StringUtils.hasText(key) && StringUtils.hasText(value)) {
            matches.put(key, value);
            count++;
        }
        return this;
    }

    public SearchParams addRange(String key, Object min, boolean minInclusive,
                                 Object max, boolean maxInclusive) {
        if (StringUtils.hasText(key) && Objects.nonNull(min) && Objects.nonNull(max)) {
            ranges.put(key, new Range(min, minInclusive, max, maxInclusive));
            count++;
        }
        return this;
    }

//    public SearchParams addNumberTerm(String key, List<? extends Number> numbers) {
//        final List<? extends Number> sortedNumbers = numbers.stream()
//                .filter(x -> x.longValue() >= 0)
//                .sorted()
//                .distinct()
//                .collect(Collectors.toList());
//        if (!CollectionUtils.isEmpty(sortedNumbers)) {
//            final List<Range> ranges = new ArrayList<>();
//            long lastV = -1;
//            long minV = -1;
//            for (Number number : sortedNumbers) {
//                final long curV = number.longValue();
//                if (lastV < 0) {
//                    minV = curV;
//                } else if (curV - lastV > 1) {
//                    ranges.add(new Range(minV, lastV+1));
//                    minV = curV;
//                }
//                lastV = curV;
//            }
//            ranges.add(new Range(minV, lastV+1));
//            numberTerms.put(key, ranges);
//            count++;
//        }
//        return this;
//    }

    public SearchParams setRandomResult(Boolean randomResult) {
        this.randomResult = Optional.ofNullable(randomResult).orElse(false);
        return this;
    }

    public SearchParams setSort(String sortName, SortOrder sortOrder) {
        Assert.isTrue(StringUtils.hasText(sortName), "sortName is must");
        this.sortName = sortName;
        this.sortOrder = sortOrder == null ? SortOrder.ASC : sortOrder;
        return this;
    }

    public boolean isValid() {
        return count > 0;
    }
}
