package com.bilibili.mgk.material.center.facade.converter;

import com.bapis.datacenter.service.oneservice.QueryReq.Builder;
import com.bilibili.mgk.material.center.facade.impl.CreativeMaterialFacadeAdvFilterImpl;
import com.bilibili.mgk.material.center.service.creative.model.AdType;
import com.bilibili.mgk.material.center.service.creative.model.CreativeMaterial;
import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.bilibili.mgk.material.center.service.creative.model.StyleAbility;
import com.bilibili.mgk.material.center.service.creative.model.VerifyType;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery;
import com.bilibili.mgk.material.center.service.creative.vo.ImgTypeQuery;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/15
 */

@Data
public class CreativeMaterialOnesDTO {


    @JsonProperty("creative_id")
    private String top1CreativeId;

    @JsonProperty("creative_title")
    private String top1CreativeTitle;


    /**
     * {@link StyleAbility}
     */
    private Integer styleAbility;

    /**
     * {@link AdType}
     */
    private String adType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creativeCtime;

    /**
     * TODO 目前数据组好像还未有这个字段， 可以先存在， 后续会在模型上加上这个字段，最好是要从源头投放端加上这个字段
     */
    private String materialId;

    @JsonProperty("material_type")
    private Integer materialType;

    // TODO  不确定是否要增加该子类型， 需要数据团队一起确定
    private ImgTypeQuery imageType;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime materialCtime;


    private String imageUrl;

    private String videoUrl;

    private String imageMd5;

    private Long avid;

    private String bvid;


    private String upType;

    /**
     * {@link VerifyType}
     */
    private Integer verifyType;

    private Integer isVerticalScreen;

    private Long videoDuration;


    /**
     * 资源位位置，也就是story ，非story
     */
    private Integer srcType;


    /**
     * 目前系统里好像都是int
     */
    private Long accountId;

    @JsonProperty("customer_id")
    private Long customerId;

    private Long productId;


    @JsonProperty("agent_id")
    private Long dependencyAgentId;

    private Long departmentId;

    /**
     * 创意中心分类的一级行业
     */
    private String categoryFirstName;

    /**
     * todo 这个字段数据模型可能缺少
     */
    private String gameCategory;

    private Integer commerceCategoryFirstId;

    private Integer commerceCategorySecondId;


    private String commerceCategoryFirstName;

    private String commerceCategorySecondName;

    private String promotionPurposeType;


    private String ocpcTarget;


    /**
     * 带货商品，注意批量
     */
    private String itemId;

    /**
     * 带货商品，注意批量
     */
    private String itemName;

    /**
     * 带货商品，注意批量
     */
    private String mainImageUrl;

    /**
     * 带货商品，注意批量, 注意由于是批量的为了方便模糊搜索，改成了中文
     */
    private String itemSource;

    /**
     * 带货商品，注意批量
     */
    private String daihuoSecondCategory;
    /**
     * 带货商品，注意批量
     */
    private String daihuoFirstCategory;


    /**
     * 带货一级类目
     */
    private String firstCategory;

    /**
     * 带货二级类目
     */
    private String secondCategory;


    private Long pv;

    private Long click;

    private Long hot;

    @JsonProperty("start_play")
    private Long play;


    @JsonProperty("play_10s_cnt")
    private Long play10sCnt;

    @JsonProperty("play_3s_cnt")
    private Long play3sCnt;

    private Long hudong;

    private String hotRank;


    private String pvRank;

    private String ctrRank;

    private String cvrRank;

    private String ctcvrRank;

    private String hudongRank;

    /**
     * 有效播放率等级 10s_play/play
     */
    @JsonProperty("play_10s_rate_rank")
    private String validPlay10sRateRank;


    /**
     * 有效播放率等级 3s_play/play
     */
    @JsonProperty("play_3s_rate_rank")
    private String validPlay3sRateRank;


    //*******返回参数中不存在字段******
    private String campaignId;

    private String unitName;

    private Long convNum;


    private String logDate;


    private Integer cpaTargetType;

//    private Integer startPlay;

    private String campaignName;

    private String searchDaihuoKeyword;

    private String accountName;


    private Long unitId;


    private String dayType;

    private String searchKeyword;

    private Double cost;

    private Integer cpaLevel;


    private String ctr;

    private String cvr;

    private String ctcvr;

    private String play3sRate;

    private String play10sRate;


    /**
     * item_id,item_name,daihuo_first_category,daihuo_second_category,main_image_url,item_source的顺序按照'_'拼接。多个商品的话会用,分隔
     */
    private String itemInfo;

    private String agentName;

    private String productName;

    private String videoTag;

    @JsonProperty("tags")
    private String creativeTag;


    private MaterialType materialTypeEnum;

    public static CreativeMaterialOnesDTO fromMapValue(Map<String, String> mapValue, boolean migrate2HiveAdRankV2) {
        // FIXME 目前直接走的{@link MaterialType.ordinal()}的序列化，只是正好反序列化是对的
        CreativeMaterialOnesDTO v = JsonUtil.fromJson(mapValue, CreativeMaterialOnesDTO.class);

        v.setMaterialTypeEnum(v.fetchMaterialTypeOfMaterialType(migrate2HiveAdRankV2));

        return v;
    }


    private MaterialType fetchMaterialTypeOfMaterialType(Boolean migrate2HiveAdRankV2) {
        if (migrate2HiveAdRankV2) {

            if (Objects.equals(materialType, MaterialType.img.getMaterialTypeIdV2())) {
                return MaterialType.img;
            } else if (Objects.equals(materialType, MaterialType.gif.getMaterialTypeIdV2())) {
                return MaterialType.gif;
            } else if (Objects.equals(materialType, MaterialType.cover_img.getMaterialTypeIdV2())) {
                return MaterialType.cover_img;
            } else if (Objects.equals(materialType, MaterialType.video.getMaterialTypeIdV2())) {
                return MaterialType.video;
            } else {
                return MaterialType.reserved;
            }

        } else {
            /**
             * {@link CreativeMaterialFacadeAdvFilterImpl#whereMaterialTypeIn(Builder, CreativeMaterialQuery, List)
             */
            if (Objects.equals(materialType, MaterialType.img.getMaterialTypeId())
                    || Objects.equals(materialType, MaterialType.gif.getMaterialTypeId())) {

                if (avid == null || avid == 0) {

                    if (Objects.equals(materialType, MaterialType.img.getMaterialTypeId())) {
                        return MaterialType.img;
                    } else {
                        return MaterialType.gif;
                    }
                } else {
                    return MaterialType.cover_img;
                }
            } else if (Objects.equals(materialType, MaterialType.video.getMaterialTypeId())) {
                return MaterialType.video;
            } else {
                return MaterialType.reserved;
            }
        }

    }



    public CreativeMaterial toBO() {
        return OneServiceDTOConverter.converter.toMaterialBO(this);
    }


}
