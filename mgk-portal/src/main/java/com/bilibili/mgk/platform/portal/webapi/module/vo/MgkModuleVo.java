package com.bilibili.mgk.platform.portal.webapi.module.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MgkModuleVo {
    @ApiModelProperty(value = "模块id")
    private String id;
    @ApiModelProperty(value = "模块名称")
    private String name;
    @ApiModelProperty(value = "所属行业")
    private List<Integer> trade_ids;
    @ApiModelProperty(value = "内容类型")
    private List<Integer> content_ids;
    @ApiModelProperty(value = "样式类型")
    private List<Integer> style_ids;
    @ApiModelProperty(value = "模块权重")
    private String module_weight;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "状态描述")
    private String status_desc;
}
