package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.collage.api.dto.CollageFontLibraryDto;
import com.bilibili.collage.api.dto.QueryCollageFontLibraryDto;
import com.bilibili.collage.api.service.ICollageService;
import com.bilibili.collage.api.service.IFontLibraryService;
import com.bilibili.collage.biz.dao.MgkCollageFontLibraryDao;
import com.bilibili.collage.biz.po.MgkCollageFontLibraryPo;
import com.bilibili.collage.biz.po.MgkCollageFontLibraryPoExample;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/19
 * 字体
 **/
@Service
@Slf4j
public class FontLibraryServiceImpl implements IFontLibraryService {

    @Autowired
    private MgkCollageFontLibraryDao mgkCollageFontLibraryDao;

    @Autowired
    private CollageValidService collageValidService;

    @Autowired
    private ICollageService collageService;

    @Override
    public PageResult<CollageFontLibraryDto> queryFontLibraryByPage(QueryCollageFontLibraryDto queryParam) {
        MgkCollageFontLibraryPoExample example = getMgkCollageFontLibraryPoExample(queryParam);
        Long total = mgkCollageFontLibraryDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        List<CollageFontLibraryDto> list = queryFontLibrary(queryParam);
        if (CollectionUtils.isEmpty(list)) {
            return PageResult.emptyPageResult();
        }
        return PageResult.<CollageFontLibraryDto>builder()
                .total(total.intValue())
                .records(list)
                .build();
    }

    @Override
    public List<CollageFontLibraryDto> queryFontLibrary(QueryCollageFontLibraryDto queryParam) {

        MgkCollageFontLibraryPoExample example = getMgkCollageFontLibraryPoExample(queryParam);
        List<MgkCollageFontLibraryPo> pos = mgkCollageFontLibraryDao.selectByExample(example);
        return pos.stream().map(this::convertFontLibraryPo).collect(Collectors.toList());
    }

    @Override
    public List<CollageFontLibraryDto> getFontLibraryByName(String name) {
        Assert.notNull(name, "字体库名称不能为空");
        MgkCollageFontLibraryPoExample example = getMgkCollageFontLibraryPoExample(QueryCollageFontLibraryDto.builder()
                .name(name)
                .build());
        List<MgkCollageFontLibraryPo> pos = mgkCollageFontLibraryDao.selectByExample(example);
        return pos.stream().map(this::convertFontLibraryPo).collect(Collectors.toList());
    }

    @Override
    public CollageFontLibraryDto getFontLibraryById(Integer fontId) {
        Assert.notNull(fontId, "字体库id不能为空");
        MgkCollageFontLibraryPo po = mgkCollageFontLibraryDao.selectByPrimaryKey(fontId);
        return this.convertFontLibraryPo(po);
    }

    @Override
    public List<CollageFontLibraryDto> getFontLibraryByIds(List<Integer> fontIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(fontIds), "字体库id不能为空");
        MgkCollageFontLibraryPoExample example = getMgkCollageFontLibraryPoExample(QueryCollageFontLibraryDto.builder()
                .ids(fontIds)
                .build());
        List<MgkCollageFontLibraryPo> pos = mgkCollageFontLibraryDao.selectByExample(example);
        return pos.stream().map(this::convertFontLibraryPo).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, CollageFontLibraryDto> getFontLibraryMapByIds(List<Integer> fontIds) {
        List<CollageFontLibraryDto> list = getFontLibraryByIds(fontIds);
        return list.stream().collect(Collectors.toMap(CollageFontLibraryDto::getId, Function.identity()));
    }

    @Override
    public void createFontLibrary(CollageFontLibraryDto fontLibraryDto) {
        collageValidService.validFontLibrary(fontLibraryDto);
        MgkCollageFontLibraryPo fontPo = new MgkCollageFontLibraryPo();
        fontPo.setName(fontLibraryDto.getName());
        fontPo.setUrl(collageService.replaceHttpsProtocol(fontLibraryDto.getUrl()));
        fontPo.setEdition(fontLibraryDto.getEdition());
        mgkCollageFontLibraryDao.insertSelective(fontPo);
    }

    @Override
    public void updateFontLibrary(CollageFontLibraryDto fontLibraryDto) {

    }

    @Override
    public void deleteFontLibrary(Integer fontId) {

    }

    @Override
    public void updateStatus(Operator operator, Integer fontId, Integer status) {
        getFontLibraryById(fontId);
        MgkCollageFontLibraryPo po = new MgkCollageFontLibraryPo();
        po.setId(fontId);
        po.setStatus(status);
        mgkCollageFontLibraryDao.updateByPrimaryKeySelective(po);
    }

    private MgkCollageFontLibraryPoExample getMgkCollageFontLibraryPoExample(QueryCollageFontLibraryDto fontLibraryDto) {
        MgkCollageFontLibraryPoExample example = new MgkCollageFontLibraryPoExample();
        MgkCollageFontLibraryPoExample.Criteria criteria = example.or();
        if (!Strings.isNullOrEmpty(fontLibraryDto.getName())) {
            criteria.andNameLike("%" + fontLibraryDto.getName() + "%");
        }
        ObjectUtils.setList(fontLibraryDto::getIds, criteria::andIdIn);
        ObjectUtils.setObject(fontLibraryDto::getStatus, criteria::andStatusEqualTo);
        ObjectUtils.setObject(fontLibraryDto::getEdition, criteria::andEditionEqualTo);
        example.setOrderByClause("id asc");
        Page page = fontLibraryDto.getPageInfo();
        if (page != null) {
            example.setLimit(page.getLimit());
            example.setOffset(page.getOffset());
        }
        return example;
    }

    private CollageFontLibraryDto convertFontLibraryPo(MgkCollageFontLibraryPo po) {
        return CollageFontLibraryDto.builder()
                .id(po.getId())
                .name(po.getName())
                .url(po.getUrl())
                .status(po.getStatus())
                .build();
    }
}
