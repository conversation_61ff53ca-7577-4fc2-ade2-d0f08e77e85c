package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/25
 */
@Deprecated
@Data
public class RisingFastVideoDetail implements SnakeCaseBody {


    // 视频基本信息 包括 视频标题、视频简介、UP名称（头像、名称）、视频标签、发布时间
    @ApiModelProperty("视频标题")
    private String title;


    @ApiModelProperty("avid")
    private Long avid;


    @ApiModelProperty("bvid")
    private String bvid;


    @ApiModelProperty("")
    private String introduce;


    @ApiModelProperty("upName")
    private String upName;


    @ApiModelProperty("upAvatar")
    private String upAvatar;

    @ApiModelProperty("upId")
    private String upId;


    @ApiModelProperty("publishTime")
    private String publishTime;


    @ApiModelProperty("videoTags")

    private List<String> videoTags;

    // 视频聚合统计星系包括 ：
    // 播放量、点赞量、评论数、弹幕数、投币数、收藏数、分享数、互动率=（点赞量+评论数+弹幕数+投币数+分享数）/ 播放量、播粉比=播放量/粉丝数
    // 每个指标显示【时间窗内的增量】、【累计】，数据1w以内显示具体数值，1w以上显示X.XXw，保留2位小数

    @ApiModelProperty("playIncr")
    private Long playIncr;


    @ApiModelProperty("play")
    private Long play;


    @ApiModelProperty("favoritesIncr")
    private Long likesIncr;


    @ApiModelProperty("favorites")
    private Long likes;


    @ApiModelProperty("commentIncr")
    private Long replyIncr;


    @ApiModelProperty("comment")
    private Long reply;


    @ApiModelProperty("coinIncr")
    private Long danmuIncr;


    @ApiModelProperty("coin")
    private Long danmu;

    // 投币数、收藏数、分享数、互动率=（点赞量+评论数+弹幕数+投币数+分享数）/ 播放量、播粉比=播放量/粉丝数
    private Long coinIncr;

    private Long coin;

    private Long favIncr;
    private Long fav;


    private Long shareIncr;

    private Long share;


    private Long interactIncr;


    private Long interact;


    private Long interactIncrRate;


    private Long interactRate;


    private Long playFollowerIncrRate;

    private Long playFollowerRate;


    // 数据聚合信息
    private String dataReadyTime;

    private String logdateGroupingFrom;

    private String logdateGroupFrom;


}
