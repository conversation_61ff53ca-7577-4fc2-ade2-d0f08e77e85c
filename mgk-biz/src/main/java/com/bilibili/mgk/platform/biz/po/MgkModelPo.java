package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkModelPo implements Serializable {
    /**
     * 自增ID（主键）
     */
    private Integer id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 模板ID
     */
    private Long modelId;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 模板版本
     */
    private String modelVersion;

    /**
     * 模板名称
     */
    private String modelName;

    /**
     * 模板类型 1-H5 2-原生 3-原生自定义
     */
    private Integer modelType;

    /**
     * 模板类型：0-自定义 10-H5 1001-原生
     */
    private Integer modelStyle;

    /**
     * 状态: 1-未发布 2-已发布 3-已下线 4-管理员驳回 5-已删除
     */
    private Integer modelStatus;

    /**
     * 模板封面地址
     */
    private String coverUrl;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 软删除：0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 模块内容id: 1-营销口号 2-服务展示 3-特色优势
     */
    private Integer moduleContentId;

    /**
     * 模块打分权重
     */
    private Integer moduleWeight;

    /**
     * 模块样式id：1-图文叠加 2-图文合集 3-内容对比
     */
    private Integer moduleStyleId;

    /**
     * 模块高度
     */
    private Integer moduleHeight;

    /**
     * 类型 0-模板 1-模块
     */
    private Integer type;

    /**
     * 是否是管理账号操作
     */
    private Integer isAdmin;

    private static final long serialVersionUID = 1L;
}