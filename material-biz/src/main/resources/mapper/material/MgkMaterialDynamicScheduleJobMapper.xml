<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.material.center.repository.mysql.DynamicScheduleJobRepository">

    <resultMap id="BaseResultMap" type="com.bilibili.mgk.material.center.service.asset.model.DynamicScheduleJob">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="uid" column="uid" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="picsRawText" column="pics_raw_text" jdbcType="VARCHAR"/>
            <result property="errCode" column="err_code" jdbcType="INTEGER"/>
            <result property="errMsg" column="err_msg" jdbcType="VARCHAR"/>
            <result property="dynId" column="dyn_id" jdbcType="BIGINT"/>
            <result property="dynRid" column="dyn_rid" jdbcType="BIGINT"/>
            <result property="dynType" column="dyn_type" jdbcType="BIGINT"/>
            <result property="scheduleTime" column="schedule_time" jdbcType="TIMESTAMP"/>
            <result property="scheduleStatus" column="schedule_status" jdbcType="VARCHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,uid,title,
        content,pics_raw_text,err_code,
        err_msg,dyn_id,dyn_rid,
        dyn_type,schedule_time,schedule_status,
        ctime,mtime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mgk_material_dynamic_schedule_job
        where  id = #{id,jdbcType=BIGINT} 
    </select>
  <select id="selectByUidAndScheduleStatus"
          resultType="java.util.ArrayList" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from mgk_material_dynamic_schedule_job
    where uid =#{uid,jdbcType=BIGINT}
    and schedule_status = #{scheduleStatus,jdbcType=VARCHAR}

    <if test="titleLike != null">
      and title like CONCAT('%',#{titleLike},'%')
    </if>

  </select>
  <select id="selectTopSchedulingTaskAndScheduleTimeLteOrderByMtimeAsc"
    resultType="java.util.ArrayList" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from mgk_material_dynamic_schedule_job
    where
        schedule_status = #{scheduleStatus}
        and schedule_time &lt;= #{scheduleTime}

   order by mtime asc limit #{limit}

  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from mgk_material_dynamic_schedule_job
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bilibili.mgk.material.center.service.asset.model.DynamicScheduleJob" useGeneratedKeys="true">
        insert into mgk_material_dynamic_schedule_job
        ( id,uid,title
        ,content,pics_raw_text,err_code
        ,err_msg,dyn_id,dyn_rid
        ,dyn_type,schedule_time,schedule_status
        ,ctime,mtime)
        values (#{id,jdbcType=BIGINT},#{uid,jdbcType=BIGINT},#{title,jdbcType=VARCHAR}
        ,#{content,jdbcType=VARCHAR},#{picsRawText,jdbcType=VARCHAR},#{errCode,jdbcType=INTEGER}
        ,#{errMsg,jdbcType=VARCHAR},#{dynId,jdbcType=BIGINT},#{dynRid,jdbcType=BIGINT}
        ,#{dynType,jdbcType=BIGINT},#{scheduleTime,jdbcType=TIMESTAMP},#{scheduleStatus,jdbcType=VARCHAR}
        ,#{ctime,jdbcType=TIMESTAMP},#{mtime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.bilibili.mgk.material.center.service.asset.model.DynamicScheduleJob" useGeneratedKeys="true">
        insert into mgk_material_dynamic_schedule_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="uid != null">uid,</if>
                <if test="title != null">title,</if>
                <if test="content != null">content,</if>
                <if test="picsRawText != null">pics_raw_text,</if>
                <if test="errCode != null">err_code,</if>
                <if test="errMsg != null">err_msg,</if>
                <if test="dynId != null">dyn_id,</if>
                <if test="dynRid != null">dyn_rid,</if>
                <if test="dynType != null">dyn_type,</if>
                <if test="scheduleTime != null">schedule_time,</if>
                <if test="scheduleStatus != null">schedule_status,</if>
                <if test="ctime != null">ctime,</if>
                <if test="mtime != null">mtime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="uid != null">#{uid,jdbcType=BIGINT},</if>
                <if test="title != null">#{title,jdbcType=VARCHAR},</if>
                <if test="content != null">#{content,jdbcType=VARCHAR},</if>
                <if test="picsRawText != null">#{picsRawText,jdbcType=VARCHAR},</if>
                <if test="errCode != null">#{errCode,jdbcType=INTEGER},</if>
                <if test="errMsg != null">#{errMsg,jdbcType=VARCHAR},</if>
                <if test="dynId != null">#{dynId,jdbcType=BIGINT},</if>
                <if test="dynRid != null">#{dynRid,jdbcType=BIGINT},</if>
                <if test="dynType != null">#{dynType,jdbcType=BIGINT},</if>
                <if test="scheduleTime != null">#{scheduleTime,jdbcType=TIMESTAMP},</if>
                <if test="scheduleStatus != null">#{scheduleStatus,jdbcType=VARCHAR},</if>
                <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
                <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.material.center.service.asset.model.DynamicScheduleJob">
        update mgk_material_dynamic_schedule_job
        <set>
                <if test="uid != null">
                    uid = #{uid,jdbcType=BIGINT},
                </if>
                <if test="title != null">
                    title = #{title,jdbcType=VARCHAR},
                </if>
                <if test="content != null">
                    content = #{content,jdbcType=VARCHAR},
                </if>
                <if test="picsRawText != null">
                    pics_raw_text = #{picsRawText,jdbcType=VARCHAR},
                </if>
                <if test="errCode != null">
                    err_code = #{errCode,jdbcType=INTEGER},
                </if>
                <if test="errMsg != null">
                    err_msg = #{errMsg,jdbcType=VARCHAR},
                </if>
                <if test="dynId != null">
                    dyn_id = #{dynId,jdbcType=BIGINT},
                </if>
                <if test="dynRid != null">
                    dyn_rid = #{dynRid,jdbcType=BIGINT},
                </if>
                <if test="dynType != null">
                    dyn_type = #{dynType,jdbcType=BIGINT},
                </if>
                <if test="scheduleTime != null">
                    schedule_time = #{scheduleTime,jdbcType=TIMESTAMP},
                </if>
                <if test="scheduleStatus != null">
                    schedule_status = #{scheduleStatus,jdbcType=VARCHAR},
                </if>
                <if test="ctime != null">
                    ctime = #{ctime,jdbcType=TIMESTAMP},
                </if>
                <if test="mtime != null">
                    mtime = #{mtime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.material.center.service.asset.model.DynamicScheduleJob">
        update mgk_material_dynamic_schedule_job
        set 
            uid =  #{uid,jdbcType=BIGINT},
            title =  #{title,jdbcType=VARCHAR},
            content =  #{content,jdbcType=VARCHAR},
            pics_raw_text =  #{picsRawText,jdbcType=VARCHAR},
            err_code =  #{errCode,jdbcType=INTEGER},
            err_msg =  #{errMsg,jdbcType=VARCHAR},
            dyn_id =  #{dynId,jdbcType=BIGINT},
            dyn_rid =  #{dynRid,jdbcType=BIGINT},
            dyn_type =  #{dynType,jdbcType=BIGINT},
            schedule_time =  #{scheduleTime,jdbcType=TIMESTAMP},
            schedule_status =  #{scheduleStatus,jdbcType=VARCHAR},
            ctime =  #{ctime,jdbcType=TIMESTAMP},
            mtime =  #{mtime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
