package com.bilibili.mgk.platform.job;

import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.mgk.platform.api.auto.IAutoLandingPageService;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkVideoLibraryQueryDSLPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkVideoLibrary.mgkVideoLibrary;

@Slf4j
@Service
public class UpdateCoverMd5 {
    private static final String job = "刷新视频库md5";

    private final BaseQueryFactory bqf;
    private final IAutoLandingPageService autoLandingPageService;

    public UpdateCoverMd5(BaseQueryFactory bqf,
                          IAutoLandingPageService autoLandingPageService) {
        this.bqf = bqf;
        this.autoLandingPageService = autoLandingPageService;
    }

    @Transactional("mgkPlatformTransactionManager")
    public void exec(Integer concurrency) {
        final Instant start = Instant.now();
        log.info("{}: 开始", job);
        try {
            final ExecutorService executor = Executors.newFixedThreadPool(concurrency);
            final List<MgkVideoLibraryQueryDSLPo> pos = bqf.from(mgkVideoLibrary)
                    .where(mgkVideoLibrary.coverMd5.eq(""))
                    .where(mgkVideoLibrary.cover.ne(""))
                    .select(mgkVideoLibrary.cover, mgkVideoLibrary.id)
                    .fetch(MgkVideoLibraryQueryDSLPo.class);
            if (CollectionUtils.isEmpty(pos)) return;

            final CountDownLatch latch = new CountDownLatch(pos.size());
            pos.forEach(x -> {
                executor.execute(() -> {
                    try {
                        final String md5 = autoLandingPageService.downloadAndGetMd5(x.getCover());
                        bqf.update(mgkVideoLibrary)
                                .where(mgkVideoLibrary.id.eq(x.getId()))
                                .set(mgkVideoLibrary.coverMd5, md5)
                                .execute();
                    } catch (Exception e) {
                        log.info("{}: 下载失败 -> {}", job, x.getCover());
                    } finally {
                        latch.countDown();
                    }
                });
            });
            latch.await();
        } catch (Throwable t) {
            log.error(String.format("%s: 中止", job), t);
        } finally {
            log.info("{}: 结束, 耗时 -> {}", job, Duration.between(start, Instant.now()));
        }
    }
}
