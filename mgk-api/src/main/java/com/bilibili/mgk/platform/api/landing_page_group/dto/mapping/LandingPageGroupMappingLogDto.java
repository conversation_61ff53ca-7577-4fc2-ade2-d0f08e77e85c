package com.bilibili.mgk.platform.api.landing_page_group.dto.mapping;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName PageGroupMappingLogDto
 * <AUTHOR>
 * @Date 2023/5/31 4:36 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupMappingLogDto {

    @JSONField(name = "落地页id")
    private Long pageId;

    @JSONField(name = "是否启用")
    private Integer isEnable;

    @JSONField(name = "状态")
    private String statusDesc;

    @JSONField(name = "拒审原因")
    private String reason;
}
