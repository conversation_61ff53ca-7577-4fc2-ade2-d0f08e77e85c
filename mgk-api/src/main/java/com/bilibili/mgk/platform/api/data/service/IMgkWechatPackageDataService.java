package com.bilibili.mgk.platform.api.data.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.data.dto.QueryWechatReportDataDto;
import com.bilibili.mgk.platform.api.data.dto.WechatPackageReportDataDto;
import com.bilibili.mgk.platform.api.data.dto.WechatPackageDataCountDto;
import com.bilibili.mgk.platform.api.data.dto.WechatPackageReportDataInfoDto;

import java.util.List;
import java.util.Map;

/**
 * @ClassName IMgkWechatPackageDataService
 * <AUTHOR>
 * @Date 2022/6/18 2:26 下午
 * @Version 1.0
 **/
public interface IMgkWechatPackageDataService {

    Long insertWechatPackageData(WechatPackageReportDataDto dataDto);

    int refreshUnReportedData(int beforeDay);

    Map<Integer, WechatPackageDataCountDto> queryWechatPackageDataMap(List<Integer> wechatPackageIds);

    Map<Integer, WechatPackageDataCountDto> queryWechatAccountDataMap(List<Integer> wechatAccountIds, Boolean removeCheat);

    PageResult<WechatPackageReportDataInfoDto> queryWechatPackageData(QueryWechatReportDataDto queryDataDto);
}