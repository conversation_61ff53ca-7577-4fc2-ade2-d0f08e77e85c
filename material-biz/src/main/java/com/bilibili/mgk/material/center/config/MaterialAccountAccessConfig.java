package com.bilibili.mgk.material.center.config;

import com.google.common.base.Splitter;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/22
 */
@Data
@Configuration
public class MaterialAccountAccessConfig {

    @Value("${material.access.whitelist.accountIds:}")
    private String whitelistAccountIds;


    @Value("${material.access.whitelist.agentNames:}")
    private String whitelistAgentNames;


    // 如果同时为空则开放
    public boolean canAccess(String accountId, String agentName) {

        if (StringUtils.isEmpty(whitelistAccountIds) && StringUtils.isEmpty(whitelistAgentNames)) {
            return true;
        }

        if (StringUtils.isNotEmpty(whitelistAccountIds) && StringUtils.isNotEmpty(accountId)) {

            if (Splitter.on(",").splitToStream(whitelistAccountIds)
                    .collect(Collectors.toSet())
                    .contains(accountId)) {

                return true;
            }

        }

        if (StringUtils.isNotEmpty(whitelistAgentNames) && StringUtils.isNotEmpty(agentName)) {
            if (Splitter.on(",").splitToStream(whitelistAgentNames)
                    .collect(Collectors.toSet())
                    .contains(agentName)) {
                return true;
            }
        }

        return false;


    }


}
