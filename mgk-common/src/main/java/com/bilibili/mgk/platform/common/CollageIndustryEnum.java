package com.bilibili.mgk.platform.common;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018/11/20
 **/
public enum CollageIndustryEnum {

    E_BUSINESS(1, "电商"),
    GAME(2, "游戏"),
    NETWORK_SERVICE(3, "网服"),
    EDUCATION(4, "教育"),
    OTHER(5, "其他");

    @Getter
    private Integer code;

    @Getter
    private String name;

    CollageIndustryEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CollageIndustryEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("行业编号不可为空");
        }

        for(CollageIndustryEnum lrt: values()) {
            if(lrt.getCode().equals(code)) {
                return lrt;
            }
        }

        throw new IllegalArgumentException("未知的行业编号");
    }
}
