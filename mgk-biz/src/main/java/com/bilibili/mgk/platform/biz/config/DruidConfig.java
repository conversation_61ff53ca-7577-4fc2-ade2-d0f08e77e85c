package com.bilibili.mgk.platform.biz.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * @file: DtuidConfig
 * @author: gaoming
 * @date: 2021/06/18
 * @version: 1.0
 * @description:
 **/

@Configuration
public class DruidConfig {

    @Value("${clickhouse.url:***********************************}")
    private String CLICKHOUSE_URL;
    @Value("${clickhouse.username:default}")
    private String USERNAME;
    @Value("${clickhouse.passport:test}")
    private String PASSWORD;
    @Value("${clickhouse.database:sycpb}")
    private String DATABASE;
    @Value("${clickhouse.driver:ru.yandex.clickhouse.ClickHouseDriver}")
    private String DRIVER;

    @Bean(name = "clickHouseDataSource")
    @Primary
    public DataSource clickHouseDataSource(){
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl(CLICKHOUSE_URL + '/' + DATABASE);
        dataSource.setDriverClassName(DRIVER);
        dataSource.setInitialSize(10);
        dataSource.setMinIdle(10);
        dataSource.setMaxActive(15);
        dataSource.setMaxWait(6000);
        dataSource.setUsername(USERNAME);
        dataSource.setPassword(PASSWORD);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

}
