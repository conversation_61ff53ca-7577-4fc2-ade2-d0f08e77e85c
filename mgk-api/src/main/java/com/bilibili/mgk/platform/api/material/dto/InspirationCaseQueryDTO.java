package com.bilibili.mgk.platform.api.material.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/6
 */
@Data
@Accessors(chain = true)
public class InspirationCaseQueryDTO implements Serializable {

    private String id;

    private List<Integer> auditStatus;

    private String title;

    private String keyword;

    private String keywordType;

    private Map<String, IndustrySubConditions> industryFilters = new HashMap<>();

    private Integer isVerticalScreen;

    /**
     * 或者adCategory
     */
    private List<String> archTypes;


    private String sortBy;


    /**
     * 投放时间区间 开始
     */
    private Date deliveryTimeFrom;

    /**
     * 投放时间区间，结束
     */
    private Date deliveryTimeTo;

    private Integer pn;

    private Integer ps;

    public void validate() {

    }

    public List<IndustrySubConditions> getAllIndustrySubConditions() {

        return industryFilters.entrySet().stream()
                .map(entry -> entry.getValue().setIndustryName(entry.getKey()))
                .collect(Collectors.toList());
    }

    @Data
    @Accessors(chain = true)
    public static class IndustrySubConditions implements Serializable {


        private String industryName;


        private String commerceFirstCategoryId;

        private String commerceSecondCategoryId;


        // TODO: 2024/4/16 具体的字段待产品进一步评审
        private String itemSource;

        private String daihuoFirstCategory;

        private String daihuoSecondCategory;


        private String gameCategory;
    }


}
