package com.bilibili.mgk.platform.api.download.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 埃及页面下载组件高度dto
 *
 * @ClassName MgkPageComponentSizeDto
 * <AUTHOR>
 * @Date 2022/8/15 4:32 下午
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MgkPageDownloadComponentHeightDto implements Serializable {
    private static final long serialVersionUID = 667832484L;

    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 总模块高度
     */
    private Integer totalBlockSize;

    /**
     * 总下载组件高度
     */
    private Integer totalDownloadComponentSize;

    /**
     * 第一个屏幕下载组件的大小
     */
    private Integer totalFirstScreenDownloadComponentSize;

    /**
     * 最大的下载组件高度
     */
    private Integer maxDownloadComponentSize;
}
