package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.mgk.platform.api.hot_ads.service.IHotAdsService;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoCollectDto;
import com.bilibili.mgk.platform.api.hot_video.dto.QueryHotVideoCollectDto;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoService;
import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import com.bilibili.mgk.platform.biz.dao.MgkHotVideoCollectDao;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * @file: HotVideoCollectServiceDelegateTest
 * @author: gaoming
 * @date: 2020/11/16
 * @version: 1.0
 * @description:
 **/
public class HotVideoCollectServiceDelegateTest extends BaseMockitoTest {

    @InjectMocks
    private HotVideoCollectServiceDelegate hotVideoCollectServiceDelegate;

    @Mock
    private MgkHotVideoCollectDao hotVideoCollectDao;

    @Mock
    private IHotVideoService hotVideoService;

    @Mock
    private IHotAdsService hotAdsService;

    @Mock
    private SnowflakeIdWorker snowflakeIdWorker;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testGetCollects() {
        hotVideoCollectServiceDelegate.getCollects(QueryHotVideoCollectDto.builder()
                .isDeleted(1)
                .page(Page.valueOf(1, 15))
                .accountIds(Lists.newArrayList(1))
                .bvids(Lists.newArrayList("1"))
                .collectIds(Lists.newArrayList(1L))
                .build());
    }

    @Test
    public void testUpdateIsDeleted() {
        hotVideoCollectServiceDelegate.updateIsDeleted(operator, "1", 1, IsDeleted.VALID);
    }

    @Test
    public void testInsert() {
        when(hotVideoCollectDao.insertSelective(any())).thenReturn(1);
        hotVideoCollectServiceDelegate.insert(HotVideoCollectDto.builder()
                .accountId(1)
                .bvid("1")
                .collectId(1L)
                .isDeleted(1)
                .build());
    }

    @Test
    public void testGetCollectList() {
        hotVideoCollectServiceDelegate.getCollectList(QueryHotVideoCollectDto.builder()
                .collectIds(Lists.newArrayList(1L))
                .bvids(Lists.newArrayList("1"))
                .accountIds(Lists.newArrayList(100))
                .page(Page.valueOf(1, 15))
                .isDeleted(IsDeleted.VALID.getCode())
                .build());
    }

    @Test
    public void testGetCollectAdsList() {
        hotVideoCollectServiceDelegate.getCollectAdsList(QueryHotVideoCollectDto.builder()
                .blackList(Lists.newArrayList("1"))
                .accountIds(Lists.newArrayList(1))
                .collectTypes(Lists.newArrayList(1))
                .isDeleted(1)
                .adTypeCreativeIds(Lists.newArrayList("1"))
                .bvids(Lists.newArrayList("bvid"))
                .titles(Lists.newArrayList("1"))
                .page(Page.valueOf(1, 15))
                .collectIds(Lists.newArrayList(1L))
                .adTypeCreativeIdBlacks(Lists.newArrayList("1"))
                .build());
    }

    @Test
    public void testUpdateCreativeStatus() {
        hotVideoCollectServiceDelegate.updateCreativeStatus(operator, 1, 1, null, null);
    }
}