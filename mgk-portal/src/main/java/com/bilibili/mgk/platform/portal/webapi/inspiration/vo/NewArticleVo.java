package com.bilibili.mgk.platform.portal.webapi.inspiration.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: NewArticleVo
 * @author: gaoming
 * @date: 2021/03/23
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class NewArticleVo {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "封面地址")
    private String cover;

    @ApiModelProperty(value = "行业 1-电商 2-游戏 3-网服 4-教育 5-其他")
    private String industry;

    @ApiModelProperty(value = "内容")
    private String content;
}
