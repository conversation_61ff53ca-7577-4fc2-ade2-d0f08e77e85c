<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="APP" value="mgk"/>
    <property name="APP_ID" value="mgk-portal"/>
    <property name="LOG_HOME" value="/data/logs/${APP_ID}"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger.%line - %msg%n
            </Pattern>
        </layout>
    </appender>

    <appender name="INFO_ONLINE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_ID}.log</file>

        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger.%line - %msg%n
            </Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_HOME}/${APP_ID}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>3</maxHistory>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <appender name="AGENT" class="com.bilibili.bjcom.log.agent.logback.logstash.LogAgentAppender">
        <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
            <maxDepthPerThrowable>10</maxDepthPerThrowable>
            <maxLength>4096</maxLength>
            <shortenedClassNameLength>20</shortenedClassNameLength>
            <rootCauseFirst>true</rootCauseFirst>
        </throwableConverter>
        <serviceName>${APP_ID}</serviceName>
        <sockfile>/var/run/lancer/collector.sock</sockfile>
        <taskId>000161</taskId>
    </appender>

    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="INFO_ONLINE"/>
        <neverBlock>true</neverBlock>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>2048</queueSize>
    </appender>

    <appender name="CAT" class="com.dianping.cat.logback.CatLogbackAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <root level="INFO">
        <appender-ref ref="AGENT"/>
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="CAT"/>
    </root>

</configuration>