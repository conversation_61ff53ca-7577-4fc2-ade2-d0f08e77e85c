package com.bilibili.mgk.platform.biz.ad.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauBizCreativeMappingPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 视频biz_id
     */
    private Integer bizId;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 是否删除 -正常 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}