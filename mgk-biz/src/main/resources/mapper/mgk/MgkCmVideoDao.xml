<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkCmVideoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkCmVideoPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="raw_name" jdbcType="VARCHAR" property="rawName" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="size_in_kb" jdbcType="INTEGER" property="sizeInKb" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, raw_name, url, size_in_kb, md5, biz_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_cm_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_cm_video
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_cm_video
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPoExample">
    delete from mgk_cm_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_cm_video (ctime, mtime, raw_name, 
      url, size_in_kb, md5, 
      biz_id)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{rawName,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{sizeInKb,jdbcType=INTEGER}, #{md5,jdbcType=VARCHAR}, 
      #{bizId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_cm_video
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="rawName != null">
        raw_name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="sizeInKb != null">
        size_in_kb,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawName != null">
        #{rawName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="sizeInKb != null">
        #{sizeInKb,jdbcType=INTEGER},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPoExample" resultType="java.lang.Long">
    select count(*) from mgk_cm_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_cm_video
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rawName != null">
        raw_name = #{record.rawName,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.sizeInKb != null">
        size_in_kb = #{record.sizeInKb,jdbcType=INTEGER},
      </if>
      <if test="record.md5 != null">
        md5 = #{record.md5,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_cm_video
    set id = #{record.id,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      raw_name = #{record.rawName,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      size_in_kb = #{record.sizeInKb,jdbcType=INTEGER},
      md5 = #{record.md5,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPo">
    update mgk_cm_video
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawName != null">
        raw_name = #{rawName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="sizeInKb != null">
        size_in_kb = #{sizeInKb,jdbcType=INTEGER},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPo">
    update mgk_cm_video
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      raw_name = #{rawName,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      size_in_kb = #{sizeInKb,jdbcType=INTEGER},
      md5 = #{md5,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_cm_video (ctime, mtime, raw_name, 
      url, size_in_kb, md5, 
      biz_id)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{rawName,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{sizeInKb,jdbcType=INTEGER}, #{md5,jdbcType=VARCHAR}, 
      #{bizId,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      raw_name = values(raw_name),
      url = values(url),
      size_in_kb = values(size_in_kb),
      md5 = values(md5),
      biz_id = values(biz_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_cm_video
      (ctime,mtime,raw_name,url,size_in_kb,md5,biz_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.rawName,jdbcType=VARCHAR},
        #{item.url,jdbcType=VARCHAR},
        #{item.sizeInKb,jdbcType=INTEGER},
        #{item.md5,jdbcType=VARCHAR},
        #{item.bizId,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_cm_video
      (ctime,mtime,raw_name,url,size_in_kb,md5,biz_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.rawName,jdbcType=VARCHAR},
        #{item.url,jdbcType=VARCHAR},
        #{item.sizeInKb,jdbcType=INTEGER},
        #{item.md5,jdbcType=VARCHAR},
        #{item.bizId,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      raw_name = values(raw_name),
      url = values(url),
      size_in_kb = values(size_in_kb),
      md5 = values(md5),
      biz_id = values(biz_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkCmVideoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_cm_video
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="rawName != null">
        raw_name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="sizeInKb != null">
        size_in_kb,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawName != null">
        #{rawName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="sizeInKb != null">
        #{sizeInKb,jdbcType=INTEGER},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="rawName != null">
        raw_name = values(raw_name),
      </if>
      <if test="url != null">
        url = values(url),
      </if>
      <if test="sizeInKb != null">
        size_in_kb = values(size_in_kb),
      </if>
      <if test="md5 != null">
        md5 = values(md5),
      </if>
      <if test="bizId != null">
        biz_id = values(biz_id),
      </if>
    </trim>
  </insert>
</mapper>