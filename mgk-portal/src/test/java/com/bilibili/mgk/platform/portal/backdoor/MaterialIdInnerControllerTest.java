package com.bilibili.mgk.platform.portal.backdoor;

import com.bilibili.mgk.material.center.service.creative.vo.MaterialIdRegisterReq;
import com.bilibili.mgk.material.center.util.HttpApiUtil;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.BeforeClass;
import org.junit.Test;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/1/14
 */
public class MaterialIdInnerControllerTest {

    private static MaterialIdRegisterApi materialIdRegisterApi;

    private ExecutorService executor = Executors.newFixedThreadPool(10);

    @BeforeClass
    public static void init() {
        materialIdRegisterApi = HttpApiUtil.buildApi(
                MaterialIdRegisterApi.class,
                "http://localhost:8080",
                false,
                null, null
        );

    }

    @Test
    public void testRegister() {

        List<Future<Map<String, Object>>> futures = IntStream.range(0, 10)
                .mapToObj(index -> {
                    return executor.submit(() -> {
                        return HttpApiUtil.call(materialIdRegisterApi.register(
                                new MaterialIdRegisterReq()
                                        .setAccountId("10")
                                        .setContent("")
                                        .setMaterialUk("uk" + System.currentTimeMillis())
                                        .setName("n")
                                        .setSource("1")
                                        .setMaterialIdType("video")
                                        .setReferenceUk("ruk")
                                        .setForceUpdate(true)
                        ));
                    });


                })
                .collect(Collectors.toList());

        futures.forEach(f -> {
            try {
                f.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        });


    }


    public interface MaterialIdRegisterApi {


        @POST("/internal/v1/material/id/register")
        Call<Map<String, Object>> register(@Body MaterialIdRegisterReq req);

    }
}