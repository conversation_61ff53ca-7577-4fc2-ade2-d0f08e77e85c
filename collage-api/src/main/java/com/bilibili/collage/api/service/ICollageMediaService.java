package com.bilibili.collage.api.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.dto.CollageMediaUpdateDto;
import com.bilibili.collage.api.dto.MediaPushDto;
import com.bilibili.collage.api.dto.MediaPushResultV2Dto;
import com.bilibili.collage.api.dto.QueryCollageMediaDto;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/09
 **/
public interface ICollageMediaService {
    PageResult<CollageMediaDto> getMediaList(QueryCollageMediaDto dto);

    void updateMedia(Operator operator, CollageMediaUpdateDto dto);

    List<Integer> batchDeleted(Operator operator, List<Integer> ids);

    void singleDeleted(Operator operator, Integer id);

    void insert(Operator operator, List<CollageMediaDto> collageMediaDto);

    Integer insert(Operator operator, CollageMediaDto collageMediaDto);

    PageResult<CollageMediaDto> getMediaOther(QueryCollageMediaDto dto);

    void updateMediaUrl(Operator operator, Integer mediaId, String rendered);

    Long insertAndGetMediaId(Operator operator, CollageMediaDto collageMediaDto);

    /**
     * 批量推送媒体材料
     *
     * @param operator
     * @param mediaPushDto
     * @return
     */
    MediaPushResultV2Dto batchPush(Operator operator, MediaPushDto mediaPushDto);

}
