package com.bilibili.mgk.platform.portal.openapi.dynamic.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: MgkDynamicLikeReplyVo
 * @author: gaoming
 * @date: 2021/05/17
 * @version: 1.0
 * @description:
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MgkDynamicLikeReplyVo {
    /**
     * 视频id
     */
    private Long biz_id;

    /**
     * 点赞数
     */
    private Long like_number;

    /**
     * 点踩数
     */
    private Long dislike_number;
}
