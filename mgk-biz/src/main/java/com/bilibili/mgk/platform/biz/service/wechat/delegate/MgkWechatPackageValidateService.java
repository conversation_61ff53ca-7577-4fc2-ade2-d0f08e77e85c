package com.bilibili.mgk.platform.biz.service.wechat.delegate;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.data.dto.WechatPackageDataCountDto;
import com.bilibili.mgk.platform.api.data.service.IMgkWechatPackageDataService;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.wechat.dto.*;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatAccountService;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatPackageService;
import com.bilibili.mgk.platform.common.IsModelEnum;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.mgk.platform.common.WechatTypeEnum;
import com.google.common.collect.Lists;
import org.jooq.tools.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.common.MgkConstants.MGK_WECHAT_PACKAGE_MAX_SIZE;

/**
 * @ClassName MgkWechatPackageValidateService
 * <AUTHOR>
 * @Date 2022/6/1 4:41 下午
 * @Version 1.0
 **/
@Service
public class MgkWechatPackageValidateService {

    private static final Integer PAGE_SIZE_LIMIT = 100;

    @Autowired
    private IMgkWechatPackageService mgkWechatPackageService;

    @Autowired
    private IMgkWechatAccountService mgkWechatAccountService;

    @Autowired
    private IMgkWechatPackageDataService mgkWechatPackageDataService;

    @Autowired
    private IMgkLandingPageService mgkLandingPageService;

    public void validCreateDto(WechatPackageCreateDto createDto) {
        Assert.notNull(createDto, "微信包创建对象不可为空");
        Assert.isTrue(!StringUtils.isEmpty(createDto.getName()), "微信包名称不可为空");
        Assert.isTrue(Utils.isPositive(createDto.getAccountId()), "账户id不可为空");
        Assert.notNull(createDto.getType(), "微信包类型不可为空");
        WechatTypeEnum.getByCode(createDto.getType());
        Assert.notEmpty(createDto.getWechatAccountIds(), "微信包关联微信号不可为空");
        Set<Integer> wechatAccountIdSet = new HashSet<>(createDto.getWechatAccountIds());
        Assert.isTrue(wechatAccountIdSet.size() == createDto.getWechatAccountIds().size(), "微信号重复");
        Assert.isTrue(createDto.getWechatAccountIds().size() <= MGK_WECHAT_PACKAGE_MAX_SIZE,
                "微信包关联微信号数量不能大于" + MGK_WECHAT_PACKAGE_MAX_SIZE + "个");
        WechatAccountQueryDto queryDto = WechatAccountQueryDto.builder()
                .ids(createDto.getWechatAccountIds())
                .type(createDto.getType())
                .needsCount(false)
                .build();
        List<WechatAccountListDto> accountListDtos = mgkWechatAccountService.queryList(queryDto);
        Assert.isTrue(accountListDtos.size() == createDto.getWechatAccountIds().size(), "微信号不存在或类型不匹配");
    }

    public void validUpdateDto(WechatPackageCreateDto updateDto, Operator operator) {
        Assert.notNull(updateDto, "微信包更新对象不可为空");
        Assert.isTrue(Utils.isPositive(updateDto.getId()), "微信包更新id不可为空");
        WechatTypeEnum.getByCode(updateDto.getType());
        Assert.notEmpty(updateDto.getWechatAccountIds(), "微信包关联微信号不可为空");
        Set<Integer> wechatAccountIdSet = new HashSet<>(updateDto.getWechatAccountIds());
        Assert.isTrue(wechatAccountIdSet.size() == updateDto.getWechatAccountIds().size(), "微信号重复");
        Assert.isTrue(updateDto.getWechatAccountIds().size() <= MGK_WECHAT_PACKAGE_MAX_SIZE,
                "微信包关联微信号数量不能大于" + MGK_WECHAT_PACKAGE_MAX_SIZE + "个");
        WechatAccountQueryDto queryDto = WechatAccountQueryDto.builder()
                .ids(updateDto.getWechatAccountIds())
                .type(updateDto.getType())
                .needsCount(false)
                .build();
        List<WechatAccountListDto> accountListDtos = mgkWechatAccountService.queryList(queryDto);
        Assert.isTrue(accountListDtos.size() == updateDto.getWechatAccountIds().size(), "微信号不存在或类型不匹配");
        List<Long> pageIds = mgkWechatPackageService.getPageIdsByPackageIds(Lists.newArrayList(updateDto.getId()));
        List<MgkLandingPageDto> pagePos = mgkLandingPageService.getLandingPageDtoByPageIds(pageIds);
        List<Long> publishedPageIds = pagePos.stream()
                .filter(pagePo -> LandingPageStatusEnum.PUBLISHED.getCode().equals(pagePo.getStatus()))
                .filter(pagePo -> IsModelEnum.NOT_LAUNCH_TEMPLATE_LIST.contains(pagePo.getIsModel()))
                .map(MgkLandingPageDto::getPageId)
                .collect(Collectors.toList());
        publishedPageIds = publishedPageIds.stream().limit(3).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(publishedPageIds), "该微信包已关联已发布落地页" + publishedPageIds + ",更新失败");
        List<Long> waitAuditPageIds = pagePos.stream()
                .filter(pagePo -> LandingPageStatusEnum.WAIT_AUDIT.getCode().equals(pagePo.getStatus()))
                .map(MgkLandingPageDto::getPageId)
                .collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(waitAuditPageIds), "该微信包已有关联落地页推审,更新失败");
        validPermission(updateDto.getId(), operator);
        Map<Integer, WechatPackageDataCountDto> existDataMap = mgkWechatPackageDataService.queryWechatPackageDataMap(Lists.newArrayList(updateDto.getId()));
        Assert.isTrue(CollectionUtils.isEmpty(existDataMap), "该微信包已有提交数据,更新失败");
    }

    public void validateDeleteInfo(Integer id, Operator operator) {
        validPermission(id, operator);
        List<Long> pageIds = mgkWechatPackageService.getPageIdsByPackageIds(Lists.newArrayList(id));
        List<MgkLandingPageDto> pagePos = mgkLandingPageService.getLandingPageDtoByPageIds(pageIds);
        List<Long> publishedPageIds = pagePos.stream()
                .filter(pagePo -> LandingPageStatusEnum.PUBLISHED.getCode().equals(pagePo.getStatus()))
                .filter(pagePo -> IsModelEnum.NOT_LAUNCH_TEMPLATE_LIST.contains(pagePo.getIsModel()))
                .map(MgkLandingPageDto::getPageId)
                .collect(Collectors.toList());
        publishedPageIds = publishedPageIds.stream().limit(3).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(publishedPageIds), "该微信包已关联已发布落地页" + publishedPageIds + ",删除失败");
        List<Long> waitAuditPageIds = pagePos.stream()
                .filter(pagePo -> LandingPageStatusEnum.WAIT_AUDIT.getCode().equals(pagePo.getStatus()))
                .map(MgkLandingPageDto::getPageId)
                .collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(waitAuditPageIds), "该微信包已有关联落地页推审,删除失败");
        Map<Integer, WechatPackageDataCountDto> existDataMap = mgkWechatPackageDataService.queryWechatPackageDataMap(Lists.newArrayList(id));
        Assert.isTrue(CollectionUtils.isEmpty(existDataMap), "该微信包已有提交数据,删除失败");

        //针对于评论区暗投落地页
        MgkLandingPageDto commentPageDto = mgkWechatPackageService.getPageDtoByWechatPackageIdIdForCommentConvert(id);
        Assert.isTrue(commentPageDto == null, "该微信包已创建评论组件,不能删除");
    }

    private void validPermission(Integer id, Operator operator) {
        WechatPackageDto dto = getValidDtoById(id);
        Assert.isTrue(operator.getOperatorId().equals(dto.getAccountId()), "您不能操作不属于您的微信包");
    }

    private WechatPackageDto getValidDtoById(Integer id) {
        Assert.isTrue(Utils.isPositive(id), "微信包id不可为空");
        WechatPackageDto dto = mgkWechatPackageService.queryValidBaseDtoById(id);
        Assert.notNull(dto, "没有符合条件的微信包");
        return dto;
    }

    public void validQueryByPageDto(WechatPackageQueryDto queryDto) {
        Assert.notNull(queryDto.getPage(), "分页参数不可为空");
        Assert.isTrue(Utils.isPositive(queryDto.getPage().getPage()), "分页信息错误");
        Assert.isTrue(queryDto.getPage().getPageSize() <= PAGE_SIZE_LIMIT, "分页过大");
    }
}
