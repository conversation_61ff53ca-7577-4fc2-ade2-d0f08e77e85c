<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.collage.biz.dao.MgkCollageSizeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.collage.biz.po.MgkCollageSizePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="width" jdbcType="INTEGER" property="width" />
    <result column="height" jdbcType="INTEGER" property="height" />
    <result column="sketch" jdbcType="VARCHAR" property="sketch" />
    <result column="des" jdbcType="VARCHAR" property="des" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="edition" jdbcType="TINYINT" property="edition" />
    <result column="size_type" jdbcType="INTEGER" property="sizeType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, width, height, sketch, des, status, is_deleted, ctime, mtime, edition, size_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_collage_size
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_collage_size
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_collage_size
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePoExample">
    delete from mgk_collage_size
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_size (width, height, sketch, 
      des, status, is_deleted, 
      ctime, mtime, edition, 
      size_type)
    values (#{width,jdbcType=INTEGER}, #{height,jdbcType=INTEGER}, #{sketch,jdbcType=VARCHAR}, 
      #{des,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{edition,jdbcType=TINYINT}, 
      #{sizeType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_size
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="sketch != null">
        sketch,
      </if>
      <if test="des != null">
        des,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="edition != null">
        edition,
      </if>
      <if test="sizeType != null">
        size_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="sketch != null">
        #{sketch,jdbcType=VARCHAR},
      </if>
      <if test="des != null">
        #{des,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="edition != null">
        #{edition,jdbcType=TINYINT},
      </if>
      <if test="sizeType != null">
        #{sizeType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePoExample" resultType="java.lang.Long">
    select count(*) from mgk_collage_size
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_collage_size
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.width != null">
        width = #{record.width,jdbcType=INTEGER},
      </if>
      <if test="record.height != null">
        height = #{record.height,jdbcType=INTEGER},
      </if>
      <if test="record.sketch != null">
        sketch = #{record.sketch,jdbcType=VARCHAR},
      </if>
      <if test="record.des != null">
        des = #{record.des,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.edition != null">
        edition = #{record.edition,jdbcType=TINYINT},
      </if>
      <if test="record.sizeType != null">
        size_type = #{record.sizeType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_collage_size
    set id = #{record.id,jdbcType=INTEGER},
      width = #{record.width,jdbcType=INTEGER},
      height = #{record.height,jdbcType=INTEGER},
      sketch = #{record.sketch,jdbcType=VARCHAR},
      des = #{record.des,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      edition = #{record.edition,jdbcType=TINYINT},
      size_type = #{record.sizeType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePo">
    update mgk_collage_size
    <set>
      <if test="width != null">
        width = #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=INTEGER},
      </if>
      <if test="sketch != null">
        sketch = #{sketch,jdbcType=VARCHAR},
      </if>
      <if test="des != null">
        des = #{des,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="edition != null">
        edition = #{edition,jdbcType=TINYINT},
      </if>
      <if test="sizeType != null">
        size_type = #{sizeType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePo">
    update mgk_collage_size
    set width = #{width,jdbcType=INTEGER},
      height = #{height,jdbcType=INTEGER},
      sketch = #{sketch,jdbcType=VARCHAR},
      des = #{des,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      edition = #{edition,jdbcType=TINYINT},
      size_type = #{sizeType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_size (width, height, sketch, 
      des, status, is_deleted, 
      ctime, mtime, edition, 
      size_type)
    values (#{width,jdbcType=INTEGER}, #{height,jdbcType=INTEGER}, #{sketch,jdbcType=VARCHAR}, 
      #{des,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{edition,jdbcType=TINYINT}, 
      #{sizeType,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      width = values(width),
      height = values(height),
      sketch = values(sketch),
      des = values(des),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      edition = values(edition),
      size_type = values(size_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_collage_size
      (width,height,sketch,des,status,is_deleted,ctime,mtime,edition,size_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.width,jdbcType=INTEGER},
        #{item.height,jdbcType=INTEGER},
        #{item.sketch,jdbcType=VARCHAR},
        #{item.des,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.edition,jdbcType=TINYINT},
        #{item.sizeType,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_collage_size
      (width,height,sketch,des,status,is_deleted,ctime,mtime,edition,size_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.width,jdbcType=INTEGER},
        #{item.height,jdbcType=INTEGER},
        #{item.sketch,jdbcType=VARCHAR},
        #{item.des,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.edition,jdbcType=TINYINT},
        #{item.sizeType,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      width = values(width),
      height = values(height),
      sketch = values(sketch),
      des = values(des),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      edition = values(edition),
      size_type = values(size_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.collage.biz.po.MgkCollageSizePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_size
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="sketch != null">
        sketch,
      </if>
      <if test="des != null">
        des,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="edition != null">
        edition,
      </if>
      <if test="sizeType != null">
        size_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="sketch != null">
        #{sketch,jdbcType=VARCHAR},
      </if>
      <if test="des != null">
        #{des,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="edition != null">
        #{edition,jdbcType=TINYINT},
      </if>
      <if test="sizeType != null">
        #{sizeType,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="width != null">
        width = values(width),
      </if>
      <if test="height != null">
        height = values(height),
      </if>
      <if test="sketch != null">
        sketch = values(sketch),
      </if>
      <if test="des != null">
        des = values(des),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="edition != null">
        edition = values(edition),
      </if>
      <if test="sizeType != null">
        size_type = values(size_type),
      </if>
    </trim>
  </insert>
</mapper>