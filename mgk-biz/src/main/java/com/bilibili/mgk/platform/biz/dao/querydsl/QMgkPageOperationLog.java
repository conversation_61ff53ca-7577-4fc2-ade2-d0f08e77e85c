package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkPageOperationLogQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkPageOperationLog is a Querydsl query type for MgkPageOperationLogQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkPageOperationLog extends com.querydsl.sql.RelationalPathBase<MgkPageOperationLogQueryDSLPo> {

    private static final long serialVersionUID = 1460334170;

    public static final QMgkPageOperationLog mgkPageOperationLog = new QMgkPageOperationLog("mgk_page_operation_log");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> operateType = createNumber("operateType", Integer.class);

    public final StringPath operateValue = createString("operateValue");

    public final NumberPath<Integer> operatorId = createNumber("operatorId", Integer.class);

    public final StringPath operatorName = createString("operatorName");

    public final NumberPath<Integer> operatorType = createNumber("operatorType", Integer.class);

    public final NumberPath<Long> pageId = createNumber("pageId", Long.class);

    public final com.querydsl.sql.PrimaryKey<MgkPageOperationLogQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkPageOperationLog(String variable) {
        super(MgkPageOperationLogQueryDSLPo.class, forVariable(variable), "null", "mgk_page_operation_log");
        addMetadata();
    }

    public QMgkPageOperationLog(String variable, String schema, String table) {
        super(MgkPageOperationLogQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkPageOperationLog(String variable, String schema) {
        super(MgkPageOperationLogQueryDSLPo.class, forVariable(variable), schema, "mgk_page_operation_log");
        addMetadata();
    }

    public QMgkPageOperationLog(Path<? extends MgkPageOperationLogQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_page_operation_log");
        addMetadata();
    }

    public QMgkPageOperationLog(PathMetadata metadata) {
        super(MgkPageOperationLogQueryDSLPo.class, metadata, "null", "mgk_page_operation_log");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(6).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(8).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(operateType, ColumnMetadata.named("operate_type").withIndex(3).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(operateValue, ColumnMetadata.named("operate_value").withIndex(4).ofType(Types.LONGVARCHAR).withSize(65535).notNull());
        addMetadata(operatorId, ColumnMetadata.named("operator_id").withIndex(9).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(operatorName, ColumnMetadata.named("operator_name").withIndex(5).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(operatorType, ColumnMetadata.named("operator_type").withIndex(10).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(pageId, ColumnMetadata.named("page_id").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
    }

}

