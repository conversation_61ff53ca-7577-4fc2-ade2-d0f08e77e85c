package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkPageWechatPackageMappingPo;
import com.bilibili.mgk.platform.biz.po.MgkPageWechatPackageMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkPageWechatPackageMappingDao {
    long countByExample(MgkPageWechatPackageMappingPoExample example);

    int deleteByExample(MgkPageWechatPackageMappingPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkPageWechatPackageMappingPo record);

    int insertBatch(List<MgkPageWechatPackageMappingPo> records);

    int insertUpdateBatch(List<MgkPageWechatPackageMappingPo> records);

    int insert(MgkPageWechatPackageMappingPo record);

    int insertUpdateSelective(MgkPageWechatPackageMappingPo record);

    int insertSelective(MgkPageWechatPackageMappingPo record);

    List<MgkPageWechatPackageMappingPo> selectByExample(MgkPageWechatPackageMappingPoExample example);

    MgkPageWechatPackageMappingPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkPageWechatPackageMappingPo record, @Param("example") MgkPageWechatPackageMappingPoExample example);

    int updateByExample(@Param("record") MgkPageWechatPackageMappingPo record, @Param("example") MgkPageWechatPackageMappingPoExample example);

    int updateByPrimaryKeySelective(MgkPageWechatPackageMappingPo record);

    int updateByPrimaryKey(MgkPageWechatPackageMappingPo record);
}