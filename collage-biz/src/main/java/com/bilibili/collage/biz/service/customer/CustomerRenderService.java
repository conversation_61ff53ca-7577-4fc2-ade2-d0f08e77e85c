package com.bilibili.collage.biz.service.customer;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.api.service.customer.IMaterialService;
import com.bilibili.collage.biz.render.PatternRender;
import com.bilibili.mgk.platform.common.CollageLayerEnum;
import com.bilibili.mgk.platform.common.collage.DrawingRenderStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/12/27
 * 用户端渲染服务类
 **/
@Slf4j
@Service
public class CustomerRenderService {

    @Autowired
    private PatternRender patternRender;

    @Autowired
    private DrawingCacheService drawingCacheService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IMaterialService materialService;

    public void doRenderTaskAsync (String requestId, List<PatternDto> matching) {

        // 异步线程
        matching.forEach(pattern -> taskExecutor.execute(() -> {

            Timestamp begin = Utils.getNow();
            String patternId = pattern.getId().toString();
            DrawingRenderResultDto renderResult = drawingCacheService.getRenderResult(requestId, patternId);
            Assert.notNull(renderResult, "cache not found by requestId: " + requestId + ", patternId: " + patternId);
            try {
                // 渲染
                String url = this.render(requestId, pattern);
                setRenderStatus(renderResult, url, pattern.getRenderImage(), DrawingRenderStatus.SUCCESS.getCode());
                // 记录渲染时间
                Timestamp end = Utils.getNow();
                long expend = (end.getTime() - begin.getTime()) / 1000;
                log.info("requestId: {}, patternId: {} 绘图耗时: {}秒", requestId, patternId, expend);
            } catch (Exception e) {
                log.error("render fail: requestId: {}, patternId: {}", requestId, patternId, e);
                setRenderStatus(renderResult, "", pattern.getRenderImage(), DrawingRenderStatus.FAIL.getCode());
            }
            // 更新图片cache
            drawingCacheService.updateRenderResult(requestId, renderResult);
        }));
    }

    private void setRenderStatus (DrawingRenderResultDto renderResult, String url, String exampleUrl, Integer status) {

        renderResult.setUrl(url);
        renderResult.setExampleUrl(exampleUrl);
        renderResult.setStatus(status);
    }

    public String render (String requestId, PatternDto pattern) throws Exception {

        mixinPattern(requestId, pattern);
        return patternRender.renderAndUploadToK3s(pattern);
    }

    /**
     * 混合用户参数(素材图，背景图，LOGO图，主标，副标，其他文案)
     * @param requestId
     * @param pattern
     */
    public void mixinPattern (String requestId, PatternDto pattern) {

        DrawingRenderInfoDto renderInfo = drawingCacheService.getRenderInfo(requestId);
        DrawingReqDto request = renderInfo.getRequest();

        mixinMainMate(request, pattern);

        mixinLogo(request, pattern);

        mixinMainTitle(request, pattern);

        mixinSubTitle(request, pattern);

        mixinOtherText(request, pattern);
    }

    // 混合主素材
    private void mixinMainMate (DrawingReqDto request, PatternDto pattern) {

        // 为空则排除此图层
        if (CollectionUtils.isEmpty(request.getMainMateId())) {
            List<LayerDto> layers = getLayersByPredicate(pattern, layer -> !layer.getCategory().equals(CollageLayerEnum.MATE_IMG));
            pattern.setLayerDtos(layers);
            return;
        }

        List<CollageMaterialDto> mateList = materialService.queryMaterialByIds(request.getMainMateId());
        Assert.isTrue(!CollectionUtils.isEmpty(mateList), "素材图已失效，渲染失败");
        List<String> mateUrls = mateList.stream().map(CollageMaterialDto::getUrl).collect(Collectors.toList());

        // 替换
        List<LayerDto> layers = getLayersByPredicate(pattern, layer -> layer.getCategory().equals(CollageLayerEnum.MATE_IMG));
        if (!CollectionUtils.isEmpty(layers)) {
            for (int i = 0; i < layers.size(); i ++) {
                layers.get(i).setImageUrl(mateUrls.get(i));
            }
        }
    }

    // 混合logo
    private void mixinLogo (DrawingReqDto request, PatternDto pattern) {

        String logoUrl = request.getLogoUrl();
        // 为空则排除此图层
        if (StringUtils.isEmpty(logoUrl)) {
            List<LayerDto> layers = getLayersByPredicate(pattern, layer -> !layer.getCategory().equals(CollageLayerEnum.LOGO_IMG));
            pattern.setLayerDtos(layers);
            return;
        }

        List<LayerDto> layers = getLayersByPredicate(pattern, layer -> layer.getCategory().equals(CollageLayerEnum.LOGO_IMG));
        if (!CollectionUtils.isEmpty(layers)) {
            layers.forEach(layer -> layer.setImageUrl(logoUrl));
        }
    }

    // 混合mainTitle
    private void mixinMainTitle (DrawingReqDto request, PatternDto pattern) {

        String mainTitle = request.getMainTitle();
        // 为空则排除此图层
        if (StringUtils.isEmpty(mainTitle)) {
            List<LayerDto> layers = getLayersByPredicate(pattern, layer -> !layer.getCategory().equals(CollageLayerEnum.MAIN_TITLE));
            pattern.setLayerDtos(layers);
            return;
        }

        List<LayerDto> layers = getLayersByPredicate(pattern, layer -> layer.getCategory().equals(CollageLayerEnum.MAIN_TITLE));
        if (!CollectionUtils.isEmpty(layers)) {
            layers.forEach(layer -> layer.setText(mainTitle));
        }
    }

    // 混合subTitle
    private void mixinSubTitle (DrawingReqDto request, PatternDto pattern) {

        String subTitle = request.getSubTitle();
        // 为空则排除此图层
        if (StringUtils.isEmpty(subTitle)) {
            List<LayerDto> layers = getLayersByPredicate(pattern, layer -> !layer.getCategory().equals(CollageLayerEnum.SUB_TITLE));
            pattern.setLayerDtos(layers);
            return;
        }

        List<LayerDto> layers = getLayersByPredicate(pattern, layer -> layer.getCategory().equals(CollageLayerEnum.SUB_TITLE));
        if (!CollectionUtils.isEmpty(layers)) {
            layers.forEach(layer -> layer.setText(subTitle));
        }
    }

    // 混合otherText
    private void mixinOtherText (DrawingReqDto request, PatternDto pattern) {

        String otherWrods = request.getOtherWords();
        // 为空则排除此图层
        if (StringUtils.isEmpty(otherWrods)) {
            List<LayerDto> layers = getLayersByPredicate(pattern, layer -> !layer.getCategory().equals(CollageLayerEnum.OTHER_TEXT));
            pattern.setLayerDtos(layers);
            return;
        }

        List<LayerDto> layers = getLayersByPredicate(pattern, layer -> layer.getCategory().equals(CollageLayerEnum.OTHER_TEXT));
        if (!CollectionUtils.isEmpty(layers)) {
            layers.forEach(layer -> layer.setText(otherWrods));
        }
    }

    private List<LayerDto> getLayersByPredicate (PatternDto pattern, Predicate<LayerDto> predicate) {

        return pattern.getLayerDtos().stream().filter(predicate).collect(Collectors.toList());
    }
}
