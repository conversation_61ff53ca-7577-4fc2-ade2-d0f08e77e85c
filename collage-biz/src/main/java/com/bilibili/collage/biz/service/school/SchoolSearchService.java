package com.bilibili.collage.biz.service.school;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.SchoolArticleDto;
import com.bilibili.collage.api.dto.SearchBriefDto;
import com.bilibili.collage.api.dto.SearchResultDto;
import com.bilibili.collage.api.service.customer.ISchoolSearchService;
import com.bilibili.collage.biz.entity.ArticleListQuery;
import com.bilibili.collage.biz.po.MyFavouritePo;
import com.bilibili.collage.biz.repo.MyFavouriteRepo;
import com.bilibili.collage.biz.service.EsSearchService;
import com.bilibili.collage.biz.utils.Es5Utils;
import com.bilibili.collage.biz.utils.EsV2Utils;
import com.bilibili.collage.biz.utils.SearchQueryInfo;
import com.bilibili.mgk.platform.common.enums.school.CategoryTypeEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@Deprecated
public class SchoolSearchService implements ISchoolSearchService {

    @Value("${mgk.es.token:sDRF2mzwJ6iyQ3wnl7viGInO5ZkmDegWlNN3ALQkISM}")
    private String esSearchToken;
    @Autowired
    private EsSearchService esSearchService;
    @Autowired
    private MyFavouriteRepo myFavouriteRepo;

    @Override
    public List<SearchBriefDto> getSearchBriefList(String searchString) {
        SearchQueryInfo searchQueryInfo = null;
        ArticleListQuery articleListQuery = new ArticleListQuery();
        articleListQuery.setIsDeleted((byte) IsDeleted.VALID.getCode());
        articleListQuery.setIsShow((byte)1);
        articleListQuery.setSortRules(Lists.newArrayList("orderNum asc","ctime desc"));
        searchQueryInfo = EsV2Utils.build(articleListQuery, ArticleListQuery.class, esSearchToken);
        SearchSourceBuilder searchSourceBuilder = Es5Utils.buildSearchSourceBuilderWithPage(searchQueryInfo,0,100);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("title",searchString));
        searchQueryInfo.getQueryBuilder().must(boolQueryBuilder);
        PageInfo<SchoolArticleDto> schoolArticleDtoPageInfo = null;
        schoolArticleDtoPageInfo = esSearchService.getEsList(searchSourceBuilder,"mgk-portal", SchoolArticleDto.class);
        List<SearchBriefDto> searchBriefDtoList = new ArrayList<>();
        for(SchoolArticleDto schoolArticleDto : schoolArticleDtoPageInfo.getList()){
            SearchBriefDto searchBriefDto = new SearchBriefDto();
            searchBriefDto.setResult(schoolArticleDto.getTitle());
            searchBriefDto.setArticleId(schoolArticleDto.getArticleId());
            searchBriefDto.setType(schoolArticleDto.getType());
            searchBriefDtoList.add(searchBriefDto);
        }
        return searchBriefDtoList;
    }

    @Override
    public PageResult<SearchResultDto> getSearchList(String searchString, Byte type, Integer page, Integer size) {
        SearchQueryInfo searchQueryInfo = null;
        ArticleListQuery articleListQuery = new ArticleListQuery();
        if(type != null && Utils.isPositive(Integer.valueOf(type))) {
            articleListQuery.setType(type);
        }
        articleListQuery.setIsDeleted((byte)IsDeleted.VALID.getCode());
        articleListQuery.setIsShow((byte)1);
        articleListQuery.setSortRules(Lists.newArrayList("orderNum asc","ctime desc"));
        searchQueryInfo = EsV2Utils.build(articleListQuery, ArticleListQuery.class, esSearchToken);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("title",searchString));
        boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("content",searchString));
        boolQueryBuilder.should(QueryBuilders.matchPhraseQuery("brief",searchString));
        boolQueryBuilder.minimumShouldMatch(1);
        searchQueryInfo.getQueryBuilder().must(boolQueryBuilder);
        SearchSourceBuilder searchSourceBuilder = Es5Utils.buildSearchSourceBuilderWithPage(searchQueryInfo,(page-1)*size,size);
        PageInfo<SchoolArticleDto> schoolArticleDtoPageInfo = null;
        schoolArticleDtoPageInfo = esSearchService.getEsList(searchSourceBuilder,"mgk-portal", SchoolArticleDto.class);
        List<SearchResultDto> searchResultDtos = new ArrayList<>();
        for(SchoolArticleDto schoolArticleDto : schoolArticleDtoPageInfo.getList()) {
            SearchResultDto searchResultDto = new SearchResultDto();
            searchResultDto.setArticleId(schoolArticleDto.getArticleId());
            searchResultDto.setTitle(schoolArticleDto.getTitle());
            searchResultDto.setType(schoolArticleDto.getType());
            if(CategoryTypeEnum.PRODUCT_MANUAL.getCode().equals(schoolArticleDto.getType())){
                searchResultDto.setContent(schoolArticleDto.getContent());
            }else{
                searchResultDto.setContent(schoolArticleDto.getBrief());
            }
            searchResultDtos.add(searchResultDto);
        }
            return  PageResult.<SearchResultDto>builder()
                .records(searchResultDtos)
                .total((int) schoolArticleDtoPageInfo.getTotal())
                .build();
    }

    @Override
    public PageResult<SearchBriefDto> getFavoriteList(String searchString, Integer page, Integer size, Operator operator) {
        Long total = myFavouriteRepo.count(searchString,Long.valueOf(operator.getOperatorId()));
        List<MyFavouritePo> myFavouritePoList= myFavouriteRepo.searchByTitle(searchString,page,size,Long.valueOf(operator.getOperatorId()));
        List<SearchBriefDto> searchBriefDtoList = new ArrayList<>();
        for(MyFavouritePo favouritePo : myFavouritePoList){
            SearchBriefDto searchBriefDto = new SearchBriefDto();
            searchBriefDto.setResult(favouritePo.getTitle());
            searchBriefDto.setType(favouritePo.getType());
            searchBriefDto.setArticleId(favouritePo.getRelateId());
        }
        return  PageResult.<SearchBriefDto>builder()
                .total(total.intValue())
                .records(searchBriefDtoList)
                .build();
    }
}
