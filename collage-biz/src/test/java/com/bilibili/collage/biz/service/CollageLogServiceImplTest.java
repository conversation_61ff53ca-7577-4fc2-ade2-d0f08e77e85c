package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.Page;
import com.bilibili.collage.api.dto.NewCollageOperationLogDto;
import com.bilibili.collage.api.dto.QueryCollageOperationLogDto;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.bilibili.collage.biz.dao.CollageOperationLogDao;
import com.bilibili.mgk.platform.common.CollageLogObjFlagEnum;
import com.bilibili.mgk.platform.common.CollageOperateTypeEnum;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.sql.Timestamp;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/09/15
 **/
public class CollageLogServiceImplTest extends BaseMockitoTest {

    @InjectMocks
    private CollageLogServiceImpl collageLogService;

    @Mock
    private CollageOperationLogDao collageOperationLogDao;

    private NewCollageOperationLogDto newCollageOperationLogDto;

    private QueryCollageOperationLogDto queryCollageOperationLogDto;

    @Before
    public void setUp() throws Exception {

        newCollageOperationLogDto = NewCollageOperationLogDto.builder()
                .operatorUsername("SYS_TEST")
                .operatorType(OperatorType.SYSTEM.getCode())
                .oldValue("")
                .newValue("")
                .objId(1L)
                .operateType(1)
                .objFlag(1)
                .accountId(10005)
                .build();

        queryCollageOperationLogDto = QueryCollageOperationLogDto.builder()
                .accountId(10005)
                .endTime(new Timestamp(System.currentTimeMillis()))
                .objFlag(CollageLogObjFlagEnum.COLLAGE_WORKS.getCode())
                .objId(1L)
                .operateType(CollageOperateTypeEnum.COLLAGE_PATTERN_ADD.getCode())
                .orderBy("mtime desc")
                .operatorType(OperatorType.SYSTEM.getCode())
                .page(Page.valueOf(1, 15))
                .startTime(new Timestamp(System.currentTimeMillis()))
                .build();
    }

    @Test
    public void testInsertLog() {
        collageLogService.insertLog(newCollageOperationLogDto);
        NullPointerException nullPointerException = mock(NullPointerException.class);
        when(collageOperationLogDao.insertSelective(any())).thenThrow(nullPointerException);
        collageLogService.insertLog(newCollageOperationLogDto);
    }

    @Test
    public void testQueryOperationLogs() {
        collageLogService.queryOperationLogs(queryCollageOperationLogDto);
        when(collageOperationLogDao.countByExample(any())).thenReturn(0L);
        collageLogService.queryOperationLogs(queryCollageOperationLogDto);
    }

    @Test
    public void testBatchInsert() {
        collageLogService.batchInsert(Lists.newArrayList(newCollageOperationLogDto));
    }
}