package com.bilibili.mgk.platform.biz.service.grpc;


import com.alibaba.fastjson.JSON;
import com.bapis.ad.mgk.chat.*;
import com.bilibili.mgk.platform.api.landing_page.dto.*;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.biz.service.MgkLandingPageServiceImpl;
import com.bilibili.mgk.platform.biz.service.chat.AbstractMgkConsultChatServiceImpl;
import com.bilibili.mgk.platform.biz.service.chat.ConsultChatFactory;
import com.bilibili.mgk.platform.common.ConsultPageCustomerServiceEnum;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/20 11:37
 */

@Service
@Slf4j
public class ConsultPageServiceGrpcImpl extends ConsultChatServiceGrpc.ConsultChatServiceImplBase {


    @Autowired
    private ConsultChatFactory consultChartFactory; // 注入工厂类

    @Autowired
    private IMgkLandingPageService landingPageService;
    @Autowired
    private MgkLandingPageServiceImpl mgkLandingPageService;

    @Override
    public void startConsultChat(StartConsultChatReq request, StreamObserver<StartConsultChatReply> responseObserver) {
        try {
            log.info("startConsultChat :request.getChatContextMap():{}", JSON.toJSONString(request.getChatContextMap()));
            Assert.notNull(request, "查询参数不可为空");
            Assert.isTrue(!request.getPageId().isEmpty(), "PageId查询参数不可为空");
            List<ConsultLandingPageDto> consultLandingPageDtos = landingPageService.getConsultPageInfoByPageId(Collections.singletonList(Long.valueOf(request.getPageId())));
            if (consultLandingPageDtos.isEmpty()){
                throw new IllegalArgumentException("未找到对应的咨询页设置");
            }
            ConsultLandingPageDto dto = consultLandingPageDtos.get(consultLandingPageDtos.size()-1);
            List<MgkLandingPageDto> pageDtos = mgkLandingPageService.getLandingPageDtosInPageIds(Collections.singletonList(Long.valueOf(request.getPageId())));
            if (pageDtos.isEmpty()){
                throw new IllegalArgumentException("未找到对应的落地页设置");
            }
            dto.setAccountId(pageDtos.get(0).getAccountId());
            AbstractMgkConsultChatServiceImpl service = consultChartFactory.getService(ConsultPageCustomerServiceEnum.getByCode(dto.getCustomerServiceType()).getThirdPartCode());
            ChatStartRespDto respDto = service.startConsultChat(dto, service.convertStartChatReq2Dto(request));
            responseObserver.onNext(StartConsultChatReply.newBuilder().setThirdChatId(respDto.getThirdChatId()).build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("[ConsultPageServiceGrpcImpl]startConsultChat 失败",  t);
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("[ConsultPageServiceGrpcImpl]startConsultChat 失败",  e);
        }
    }

    @Override
    public void endConsultChat(EndConsultChatReq request, StreamObserver<EndConsultChatReply> responseObserver) {
        try {
            Assert.notNull(request, "查询参数不可为空");
            Assert.isTrue(!request.getPageId().isEmpty(), "PageId查询参数不可为空");
            List<ConsultLandingPageDto> consultLandingPageDtos = landingPageService.getConsultPageInfoByPageId(Collections.singletonList(Long.valueOf(request.getPageId())));
            if (consultLandingPageDtos.isEmpty()){
                throw new IllegalArgumentException("未找到对应的咨询页设置");
            }
            ConsultLandingPageDto dto = consultLandingPageDtos.get(consultLandingPageDtos.size()-1);
            List<MgkLandingPageDto> pageDtos = mgkLandingPageService.getLandingPageDtosInPageIds(Collections.singletonList(Long.valueOf(request.getPageId())));
            if (pageDtos.isEmpty()){
                throw new IllegalArgumentException("未找到对应的落地页设置");
            }
            dto.setAccountId(pageDtos.get(0).getAccountId());
            AbstractMgkConsultChatServiceImpl service = consultChartFactory.getService(ConsultPageCustomerServiceEnum.getByCode(dto.getCustomerServiceType()).getThirdPartCode());
            ChatEndRespDto respDto = service.endConsultChat(dto, service.convertEndConsultChatReq2Dto(request));
            responseObserver.onNext(EndConsultChatReply.newBuilder().build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("[ConsultPageServiceGrpcImpl]endConsultChat 失败",  t);
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("[ConsultPageServiceGrpcImpl]endConsultChat 失败",  e);
        }
    }

    @Override
    public void chatMessagePush(ChatMessagePushReq request, StreamObserver<ChatMessagePushReply> responseObserver) {
        try {
            Assert.notNull(request, "查询参数不可为空");
            Assert.isTrue(!request.getPageId().isEmpty(), "PageId查询参数不可为空");
            List<ConsultLandingPageDto> consultLandingPageDtos = landingPageService.getConsultPageInfoByPageId(Collections.singletonList(Long.valueOf(request.getPageId())));
            if (consultLandingPageDtos.isEmpty()){
                throw new IllegalArgumentException("未找到对应的咨询页设置");
            }
            ConsultLandingPageDto dto = consultLandingPageDtos.get(consultLandingPageDtos.size()-1);
            List<MgkLandingPageDto> pageDtos = mgkLandingPageService.getLandingPageDtosInPageIds(Collections.singletonList(Long.valueOf(request.getPageId())));
            if (pageDtos.isEmpty()){
                throw new IllegalArgumentException("未找到对应的落地页设置");
            }
            dto.setAccountId(pageDtos.get(0).getAccountId());
            AbstractMgkConsultChatServiceImpl service = consultChartFactory.getService(ConsultPageCustomerServiceEnum.getByCode(dto.getCustomerServiceType()).getThirdPartCode());
            MessagePushRespDto respDto = service.pushMessageInConsultChat(dto, service.convertChatMessagePushReq2Dto(request));
            responseObserver.onNext(ChatMessagePushReply.newBuilder().build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("[ConsultPageServiceGrpcImpl]chatMessagePush 失败",  t);
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("[ConsultPageServiceGrpcImpl]chatMessagePush 失败",  e);
        }
    }

}
