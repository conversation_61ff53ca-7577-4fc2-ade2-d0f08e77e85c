package com.bilibili.mgk.platform.portal.openapi.address.vo;

import com.bilibili.mgk.platform.api.ipdb.dto.RegionAnalyzeDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CustomizeLbsAnalyzeVo {

    @ApiModelProperty(value = "中国行政区划代码")
    private String china_admin_code;

    @ApiModelProperty(value = "城市名称")
    private String city_name;

    @ApiModelProperty(value = "省份码")
    private String province_code;

    @ApiModelProperty(value = "省份名称")
    private String province_name;

    @ApiModelProperty(value = "门店")
    private String shop;

    public static CustomizeLbsAnalyzeVo convertDto2Vo(RegionAnalyzeDto dto) {
        return CustomizeLbsAnalyzeVo.builder()
                .city_name(dto.getCityName())
                .china_admin_code(dto.getChinaAdminCode())
                .build();
    }
}
