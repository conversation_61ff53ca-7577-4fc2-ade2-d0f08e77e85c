package com.bilibili.mgk.platform.portal.common;


/**
 * <AUTHOR>
 * @Description
 * @date 2020/12/16
 **/
public enum RequestTypeEnum {

    /**
     * 0 是投放端
     */
    OPEN_API(1, "开放平台");

    private Integer id;

    private String typeName;


    RequestTypeEnum(int id, String typeName) {
        this.id = id;
        this.typeName = typeName;
    }

    public Integer getId() {
        return id;
    }

    public String getTypeName() {
        return typeName;
    }
}
