package com.bilibili.mgk.material.center.service.creative;

import com.bilibili.mgk.material.center.service.creative.impl.WatermarkServiceImpl.WatermarkImgInput;
import com.bilibili.mgk.material.center.service.creative.model.CreativeMaterial;
import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideo;
import com.bilibili.mgk.material.center.service.creative.model.HotLandingPage;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

/**
 * 水印服务， 1. 提供打水印的能力 2. 提供
 *
 * <AUTHOR>
 * @desc
 * @date 2024/3/27
 */
public interface WatermarkService {

    Logger logger = LoggerFactory.getLogger(WatermarkService.class);

    /**
     * 获取水印任务完成的最新日期， 格式yyyyMMdd
     * <p>
     * 如果今天的水印任务没有完成，那么使用昨天的日期进行查询；
     *
     * @return
     */

    /**
     * 触发热门广告素材图片的打水印任务，
     * <p>
     * <p>
     * 这里如果已经打过了那么进行替换；
     */


    /**
     * 触发热门视频封面图片的打水印任务, 强制替换水印图片
     * @param originMd5s
     * @return
     */
    Map<String, String> forceOverwriteWatermarkImg(List<String> originMd5s);

    /**
     * @param originImg5
     * @param generateIfNotExisted 是否读扩散， true 表示查询过程中会触发水印任务，但是相应的会发生阻塞等待生成结果， 目前来看水印任务的消耗时间不会过大
     * @return
     */
    Map<String, String> queryWaterMarkedImgByOriginImgMd5(List<WatermarkImgInput> originImg5, boolean generateIfNotExisted);


    /**
     * 是否要因为水印任务未完成而阻塞本次的返回，目前看是不需要的 （1） 水印任务本身处理很快，且大部分是已经处理过的 （2） 处理任务在后台执行，且是在用户的闲时时间，用户基本无感
     * <p>
     * 使用水印图片替换原有的图片
     */
    default void maskImgByWatermarkImg(List<CreativeMaterial> materials) {

        doMaskVideoCoverByWatermarkImg(
                materials,
                CreativeMaterial::getImageUrl,
                CreativeMaterial::getImageMd5,
                CreativeMaterial::setImageUrl);


    }


    /**
     * 对于b站热门视频 没有MD5，直接使用url，可能存在重复生成水印的问题，不过不重要了
     */
    default void maskVideoCoverByWatermarkImg(List<HotBiliVideo> biliVideos) {

        doMaskVideoCoverByWatermarkImg(biliVideos, HotBiliVideo::getCover, HotBiliVideo::getCover,
                HotBiliVideo::setCover);
    }

    default void maskLandingPageCoverByWatermark(List<HotLandingPage> landingPages) {
        doMaskVideoCoverByWatermarkImg(landingPages, HotLandingPage::getPageCover, HotLandingPage::getPageCover,
                HotLandingPage::setPageCover);
    }


    default <T> void doMaskVideoCoverByWatermarkImg(
            List<T> biliVideos,
            Function<T, String> urlGetter, Function<T, String> md5Getter,
            BiConsumer<T, String> urlSetter) {

        long startTs = System.currentTimeMillis();

        Map<String, String> imgMd5ToWatermarkUrls = queryWaterMarkedImgByOriginImgMd5(
                biliVideos.stream()
                        .map(material -> {
                            return new WatermarkImgInput(urlGetter.apply(material), md5Getter.apply(material), 0);
                        }).collect(Collectors.toList()), true
        );

        List<String> imgUrlsWithNoWatermark = new ArrayList<>();

        biliVideos.forEach(material -> {
            String imgMd5 = md5Getter.apply(material);
            String watermarkUrl = imgMd5ToWatermarkUrls.get(imgMd5);
            if (watermarkUrl != null) {
                urlSetter.accept(material, watermarkUrl);
            } else {
                imgUrlsWithNoWatermark.add(urlGetter.apply(material));

            }

        });

        logger.info("Success to mask watermark img, cost={} millis for img.size={}, absent.size={}",
                (System.currentTimeMillis() - startTs), biliVideos.size(), imgUrlsWithNoWatermark.size());

        if (!CollectionUtils.isEmpty(imgUrlsWithNoWatermark)) {
            logger.error("No watermark img found for urls={}", imgUrlsWithNoWatermark);
        }
    }

}
