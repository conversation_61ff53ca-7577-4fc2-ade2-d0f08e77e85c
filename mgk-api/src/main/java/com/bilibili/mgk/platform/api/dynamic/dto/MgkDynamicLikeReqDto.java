package com.bilibili.mgk.platform.api.dynamic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * @file: MgkDynamicLikeReqDto
 * @author: gaoming
 * @date: 2021/05/17
 * @version: 1.0
 * @description:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MgkDynamicLikeReqDto implements Serializable {
    private static final long serialVersionUID = -3625015118584813656L;
    /**
     * mid
     */
    private Long mid;

    /**
     * buvid
     */
    private String buvid;

    /**
     * 对象id（视频点赞 biz_id）
     */
    private Integer bizId;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 类型 0-默认 1 点赞 2 取消点赞 3 点彩 4 取消点踩
     */
    private Integer type;

    /**
     * ip
     */
    private String ip;

    /**
     * 移动App
     */
    private String mobiApp;

    /**
     * platform
     */
    private String platform;

    /**
     * device
     */
    private String device;
}
