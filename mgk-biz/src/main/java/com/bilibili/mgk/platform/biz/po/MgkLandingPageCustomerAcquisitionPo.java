package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkLandingPageCustomerAcquisitionPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 获客链接id
     */
    private String linkId;

    /**
     * 状态: 0-有效 1-无效 2-删除
     */
    private Integer status;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}