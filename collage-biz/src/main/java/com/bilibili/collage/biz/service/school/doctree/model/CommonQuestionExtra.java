package com.bilibili.collage.biz.service.school.doctree.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.biz.common.doc.tree.model.DocFlattenNode;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/26
 */
@Data
@Accessors(chain = true)
@Deprecated
public class CommonQuestionExtra implements SnakeCaseBody {


    /**
     * common-question文档树，有一组对应的影子数据，其中三连的主要保存sanlian order 和isShow数据
     */


    private Long articleId;

    private String articleTitle;

    private Integer articleType;

    private Long articleParentId;

    private String articleParentName;

    private Boolean isShowInSanlian;

    private Integer sanlianOrderNum;



    public static CommonQuestionExtra fromBizExtra(String bizExtra) {
        return JsonUtil.readValue(bizExtra, CommonQuestionExtra.class);

    }

    public CommonQuestionExtra setRelatedArticle(DocFlattenNode article, Integer articleType,

            Boolean isShowInSanlian, Integer orderNumInSanlian
    ) {

        this.setArticleId(article.getNodeId())
                .setArticleTitle(article.getNodeName())
                .setArticleParentId(article.getParentId())
                .setArticleParentName(article.getParentName())
                .setArticleType(articleType)
                .setIsShowInSanlian(isShowInSanlian)
                .setSanlianOrderNum(orderNumInSanlian)
        ;
        return this;
    }

    public String toBizExtra() {

        return JsonUtil.writeValueAsString(this);
    }
}
