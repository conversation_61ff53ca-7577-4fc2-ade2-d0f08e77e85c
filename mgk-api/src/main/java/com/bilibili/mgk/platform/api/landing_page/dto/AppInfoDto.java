package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/08/20
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 开发商
     */
    private String devName;

    /**
     * 权限列表
     */
    private List<String> authList;

    /**
     * 版本号
     */
    private String version;

    /**
     * 更新时间
     */
    private Timestamp apkUpdateTime;

    /**
     * 隐私政策
     */
    private String privacyPolicy;

    /**
     * 备案号.
     */
    private String recordNumber;

}
