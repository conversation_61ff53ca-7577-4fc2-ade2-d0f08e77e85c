package com.bilibili.mgk.material.center.http;

import com.bilibili.mgk.material.center.http.dto.Img2ImgResultDTO;
import com.bilibili.mgk.material.center.http.dto.Img2ImgSubmitRespDTO;
import com.bilibili.mgk.material.center.http.dto.Img2TxtRespDTO;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;

// TODO 需要集成openTelemetry
/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/19
 */
//@RESTClient(name = "img2img", host = "discovery://sycpb.sycpb-model.aigc-img2img")
public interface Img2ImgHttpApi {


    @POST("/img2txt")
    Call<Img2TxtRespDTO> img2txt(
            @Query("url") String imgUrl
    );


    /**
     * @param imgUrl    url有效性，大小不限，格式（仅限png ,jpg, jpeg）
     * @param model     最长不要超过256字符
     * @param relevance 只能是0-1的浮点数
     * @param quantity  最大6，不支持负数
     * @param textkeep  默认开启 0是不保持，1是保持，加在原来接口上就行
     * @param shape     输出图像尺寸 1-1, 4-3, 16-9, 16-10
     * @return
     */
    @POST("/generate")
    Call<Img2ImgSubmitRespDTO> img2img(
            @Query("url") String imgUrl,
            @Query("prompt") String model,
            @Query("relevance") Double relevance,
            @Query("quantity") Integer quantity,
            @Query("textkeep") Integer textkeep,
            @Query("shape") String shape
    );

    @GET("/api/img2img/{taskId}")
    Call<Img2ImgResultDTO> img2img(
            @Path("taskId") String taskId
    );


}
