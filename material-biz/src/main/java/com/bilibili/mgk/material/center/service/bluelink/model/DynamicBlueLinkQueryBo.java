package com.bilibili.mgk.material.center.service.bluelink.model;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 原生稿件审核任务列表的查询对象
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DynamicBlueLinkQueryBo {

    @ApiModelProperty("账户id")
    private List<Integer> accountIds;

    @ApiModelProperty("动态挂链审核状态")
    private List<Integer> auditStatus;

    @ApiModelProperty("up主MID")
    private List<Long> mids;

    @ApiModelProperty("页号")
    private Integer page;
    @ApiModelProperty("页大小")
    private Integer size;

    @ApiModelProperty("图文动态ID")
    private List<String> dynamicIds;


    // 导出条件
    @ApiModelProperty("任务创建时间起")
    private Long ctimeFrom;
    @ApiModelProperty("任务创建时间止")
    private Long ctimeTo;

    @ApiModelProperty("任务处理时间起")
    private Long mtimeFrom;
    @ApiModelProperty("任务处理时间止")
    private Long mtimeTo;

    @ApiModelProperty("任务处理人")
    private List<Long> operatorIds;

    @ApiModelProperty("任务处理人name")
    private List<String> operatorNames;


    /**
     * 与其他条件的逻辑是 other & (titleLike | dynamicIdEq)
     */
    private String searchTitleLike;

    private String searchDynamicIdEq;


}