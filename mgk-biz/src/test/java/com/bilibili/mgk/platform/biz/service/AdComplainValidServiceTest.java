package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.api.ad_complain.dto.CreateAdComplainDto;
import com.bilibili.mgk.platform.common.complain.ComplainTypeEnum;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

public class AdComplainValidServiceTest {

    private AdComplainValidService adComplainValidService;

    @Before
    public void before(){
        adComplainValidService = new AdComplainValidService();
    }

    @Test
    public void validCreateAdComplain() {
        CreateAdComplainDto dto = CreateAdComplainDto.builder()
                .creativeId(12L)
                .email("<EMAIL>")
                .complainType(ComplainTypeEnum.OTHER.getCode())
                .complainText("abc\uD83E\uDD28")
                .build();
        adComplainValidService.validCreateAdComplain(dto);
        assertEquals("abc", dto.getComplainText());

        CreateAdComplainDto dto1 = CreateAdComplainDto.builder()
                .creativeId(12L)
                .email("<EMAIL>")
                .complainType(ComplainTypeEnum.OTHER.getCode())
                .complainText("abc\uD83C\uDDE8\uD83C\uDDF3\uD83C\uDDED\uD83C\uDDF0\uD83C\uDDF2\uD83C\uDDF4")
                .build();
        adComplainValidService.validCreateAdComplain(dto1);
        assertEquals("abc", dto1.getComplainText());

        CreateAdComplainDto dto2 = CreateAdComplainDto.builder()
                .creativeId(12L)
                .email("<EMAIL>")
                .complainType(ComplainTypeEnum.OTHER.getCode())
                .complainText("abc㊎㊍㊌㊏❤�\uD83D\uDC9B✝\uD83D\uDE39\uD83D\uDE39\uD83D\uDE39\uD83D\uDE07\uD83D\uDE00\uD83D\uDE03٩\uD83D\uDE10۶充满阳光(☀๓´╰╯`๓)")
                .build();
        adComplainValidService.validCreateAdComplain(dto2);
        assertEquals("abc㊎㊍㊌㊏:heart:�:latin_cross:٩۶充满阳光(:sunny:๓´╰╯`๓)", dto2.getComplainText());
    }
}