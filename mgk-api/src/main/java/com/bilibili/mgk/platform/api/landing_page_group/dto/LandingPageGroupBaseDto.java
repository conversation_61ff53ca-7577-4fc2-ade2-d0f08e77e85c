package com.bilibili.mgk.platform.api.landing_page_group.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName LandingPageGroupDto
 * <AUTHOR>
 * @Date 2023/5/17 2:14 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupBaseDto {

    private Long groupId;

    private Integer accountId;

    private String name;

    private Integer auditCreativeId;

    private Integer groupSource;

    private Integer groupStatus;

}
