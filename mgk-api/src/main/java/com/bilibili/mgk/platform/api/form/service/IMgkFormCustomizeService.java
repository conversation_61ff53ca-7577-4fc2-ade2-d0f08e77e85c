package com.bilibili.mgk.platform.api.form.service;

import java.util.List;

/**
 * 表单定制化权限管理 目前只通过后门进行调用 控制定制化表单查看权限
 * 可能需要配合配置修改
 *
 * @ClassName IMgkFormCustomizeService
 * <AUTHOR>
 * @Date 2022/5/8 11:40 下午
 * @Version 1.0
 **/
public interface IMgkFormCustomizeService {

    void addCustomizeFormAuthority(Integer accountId, Long formId);

    void updateCustomizeFormAuthority(Integer accountId, Long oldFormId, Long newFormId);

    void deleteCustomizeFormAuthority(Integer accountId, Long formId);

    List<Long> checkFormDataDownloadPermission(Integer accountId);

    void checkFormDataDownloadPermission(Integer accountId, List<Long> formIds);

}
