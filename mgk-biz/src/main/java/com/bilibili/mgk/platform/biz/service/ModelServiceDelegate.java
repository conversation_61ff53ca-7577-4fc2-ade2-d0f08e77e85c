package com.bilibili.mgk.platform.biz.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.mgk.platform.api.data.dto.ReportDataDto;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.NewLandingPageDto;
import com.bilibili.mgk.platform.api.log.dto.NewLogOperationDto;
import com.bilibili.mgk.platform.api.log.service.IMgkLogService;
import com.bilibili.mgk.platform.api.model.dto.*;
import com.bilibili.mgk.platform.biz.dao.MgkModelDao;
import com.bilibili.mgk.platform.biz.dao.MgkModelTradeMappingDao;
import com.bilibili.mgk.platform.biz.dao.MgkTradeDao;
import com.bilibili.mgk.platform.biz.po.*;
import com.bilibili.mgk.platform.biz.validator.MgkModelValidator;
import com.bilibili.mgk.platform.common.*;
import com.bilibili.mgk.platform.common.utils.ExampleUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/08
 **/
@Service
public class ModelServiceDelegate {

    private static final Logger LOGGER = LoggerFactory.getLogger(LandingPageServiceDelegate.class);

    @Value("#{'${mgk.model.show.full.screen.open.double.jump.video.accs:29,3,10005}'.split(',')}")
    private List<Integer> SHOW_FULL_SCREEN_OPEN_DOUBLE_JUMP_VIDEO_ACCS;

    //因为能操作模板的账号很固定,为了减轻crm的压力直接查账号,不查标签了
    @Value("${mgk.model.account.id}")
    private Integer modelAccountId;

    @Autowired
    private MgkModelDao mgkModelDao;
    @Autowired
    private MgkModelValidator mgkModelValidator;
    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;
    @Autowired
    private LandingPageServiceDelegate landingPageServiceDelegate;
    @Autowired
    private MgkModelTradeMappingDao mgkModelTradeMappingDao;
    @Autowired
    private MgkTradeDao mgkTradeDao;
    @Autowired
    private IMgkLogService mgkLogService;

    public List<MgkModelTradeMappingDto> getModelTradeMappingDtos(QueryModelTradeMappingParamDto param) {
        Assert.notNull(param, "模板行业查询参数不可为空");
        MgkModelTradeMappingPoExample example = this.getModelTradeMappingExample(param);
        List<MgkModelTradeMappingDto> modelTradeMappingDtos = this.getModelTradeMappingDtosByExample(example);
        return CollectionUtils.isEmpty(modelTradeMappingDtos) ? Collections.emptyList() : modelTradeMappingDtos;
    }

    private List<MgkModelTradeMappingDto> getModelTradeMappingDtosByExample(MgkModelTradeMappingPoExample example) {
        List<MgkModelTradeMappingPo> modelTradeMappingPos = mgkModelTradeMappingDao.selectByExample(example);
        return this.convertModelTradeMappingPos2Dtos(modelTradeMappingPos);
    }

    private List<MgkModelTradeMappingDto> convertModelTradeMappingPos2Dtos(List<MgkModelTradeMappingPo> modelTradeMappingPos) {
        return modelTradeMappingPos.stream().map(this::convertModelTradeMappingPo2Dto).collect(Collectors.toList());
    }

    private MgkModelTradeMappingDto convertModelTradeMappingPo2Dto(MgkModelTradeMappingPo modelTradeMappingPo) {
        MgkModelTradeMappingDto modelTradeMappingDto = MgkModelTradeMappingDto.builder().build();
        BeanUtils.copyProperties(modelTradeMappingPo, modelTradeMappingDto);
        return modelTradeMappingDto;
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public long create(Operator operator, NewModelDto newModelDto) {
        LOGGER.info("create operator: [{}], newModelDto: [{}]", operator, newModelDto);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        mgkModelValidator.validateCreateModel(newModelDto);

        AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(newModelDto.getAccountId());
        Assert.notNull(accountBaseDto, "该用户不存在");

        long modelId = snowflakeIdWorker.nextId();
        long pageId = this.insertModelPage(operator, modelId, newModelDto);
        this.insertModel(operator, modelId, pageId, newModelDto);
        this.insertModelTradeMapping(modelId, newModelDto);
        this.insertLog(operator, modelId, LogObjFlagEnum.MODEL.getCode(), LogOperateTypeEnum.MODEL_ADD.getCode());
        return pageId;
    }

    public List<MgkModelDto> getMgkModelDtosByIds(List<Long> modelIds) {
        // get mgkModel form DB
        List<MgkModelDto> modelDtoList = this.convertModelPosToDtos(getModelsByIds(modelIds));

        buildTrade(modelDtoList);

        buildTemplateStyle(modelDtoList);

        return modelDtoList;
    }

    public void buildTrade(List<MgkModelDto> modelDtoList){
        if(CollectionUtils.isEmpty(modelDtoList)){
            return;
        }
        List<Long> modelIds = modelDtoList.stream().map(MgkModelDto::getModelId).collect(Collectors.toList());
        List<MgkModelTradeMappingPo> mappingPos = getTradeMappingPosByModelIds((modelIds));

        // 从DB中获取行业信息
        Set<Long> tradeIds = mappingPos.stream().map(MgkModelTradeMappingPo::getTradeId).collect(Collectors.toSet());
        Map<Long, List<MgkModelTradeMappingPo>> modelId2TradeIdsMap = mappingPos.stream().collect(Collectors
                .groupingBy(MgkModelTradeMappingPo::getModelId));
        Map<Long, MgkTradePo> tradeId2TradePoMap = this.getTradePosByTradeIds(new ArrayList<>(tradeIds)).stream().collect(Collectors.toMap(MgkTradePo::getTradeId, x -> x, (x, y) -> x));
        List<Long> parentTradeIds = tradeId2TradePoMap.entrySet().stream().map(entry->entry.getValue().getParentTradeId()).collect(Collectors.toList());
        Map<Long, MgkTradePo> parentTradeId2TradePoMap = this.getTradePosByTradeIds(new ArrayList<>(parentTradeIds)).stream().collect(Collectors.toMap(MgkTradePo::getTradeId, x -> x, (x, y) -> x));

        modelDtoList.forEach(modelDto -> {
            Long key = modelDto.getModelId();
            List<Long> mgkTradeIds = modelId2TradeIdsMap.getOrDefault(key, Collections.emptyList()).stream().map(MgkModelTradeMappingPo::getTradeId).collect(Collectors.toList());
            List<String> tradeNames = mgkTradeIds.stream().filter(id->tradeId2TradePoMap.containsKey(id)).map(id->tradeId2TradePoMap.get(id).getTradeName()).collect(Collectors.toList());
            List<Long> thisParentTradeIds = mgkTradeIds.stream().filter(id->tradeId2TradePoMap.containsKey(id)).map(id->tradeId2TradePoMap.get(id).getParentTradeId()).filter(id->!id.equals(0L)).collect(Collectors.toList());
            List<String>  parentTradeNames = thisParentTradeIds.stream().filter(id->parentTradeId2TradePoMap.containsKey(id)).map(id->parentTradeId2TradePoMap.get(id).getTradeName()).collect(Collectors.toList());
            modelDto.setTradeIds(mgkTradeIds);
            modelDto.setTradeNames(tradeNames);
            modelDto.setParentTradeIds(thisParentTradeIds);
            modelDto.setParentTradeNames(parentTradeNames);
        });
    }

    public List<MgkModelDto> buildTrade(List<MgkModelDto> modelDtoList , QueryModelParamDto queryModelParamDto){
        List<MgkModelDto> filteredList = new ArrayList<>();
        if(CollectionUtils.isEmpty(modelDtoList)){
            return modelDtoList;
        }

        List<Long> modelIds = modelDtoList.stream().map(MgkModelDto::getModelId).collect(Collectors.toList());
        List<MgkModelTradeMappingPo> mappingPos = getTradeMappingPosByModelIds((modelIds));


        // 从DB中获取行业信息
        Set<Long> tradeIds = mappingPos.stream().map(MgkModelTradeMappingPo::getTradeId).collect(Collectors.toSet());
        Map<Long, List<MgkModelTradeMappingPo>> modelId2TradeIdsMap = mappingPos.stream().collect(Collectors
                .groupingBy(MgkModelTradeMappingPo::getModelId));
        Map<Long, MgkTradePo> tradeId2TradePoMap = this.getTradePosByTradeIds(new ArrayList<>(tradeIds)).stream().collect(Collectors.toMap(MgkTradePo::getTradeId, x -> x, (x, y) -> x));
        List<Long> parentTradeIds = tradeId2TradePoMap.entrySet().stream().map(entry->entry.getValue().getParentTradeId()).collect(Collectors.toList());
        Map<Long, MgkTradePo> parentTradeId2TradePoMap = this.getTradePosByTradeIds(new ArrayList<>(parentTradeIds)).stream().collect(Collectors.toMap(MgkTradePo::getTradeId, x -> x, (x, y) -> x));
        //为了其他,无子id的父id放入子id中
        List<Long> toAddChildIds = new ArrayList<>();
        for(Long parentId :queryModelParamDto.getParentTradeIds()){
            if(tradeId2TradePoMap.containsKey(parentId)){
                toAddChildIds.add(parentId);
            }
        }
        LOGGER.info("toAddChildIds={}",toAddChildIds);
        List<Long> queryParentIds = Lists.newArrayList(queryModelParamDto.getParentTradeIds());
        List<Long> queryTradeIds = Lists.newArrayList(queryModelParamDto.getTradeIds());
        queryTradeIds.addAll(toAddChildIds);
        queryParentIds.removeAll(toAddChildIds);

        modelDtoList.forEach(modelDto -> {
            Long key = modelDto.getModelId();
            List<MgkModelTradeMappingPo> mgkModelTradeMappingList = modelId2TradeIdsMap.getOrDefault(key, Collections.emptyList());
            //tradeid包含传入tradeid
            Boolean containsTradeId = queryTradeIds.stream().anyMatch(tradeId->mgkModelTradeMappingList.stream().map(MgkModelTradeMappingPo::getTradeId).collect(Collectors.toList()).contains(tradeId));
            //tradeid包含传入parenttradeid（其他没有父id）
//            Boolean containsNoParentTradeId = queryModelParamDto.getParentTradeIds().stream().anyMatch(tradeId->mgkModelTradeMappingList.stream().map(MgkModelTradeMappingPo::getTradeId).collect(Collectors.toList()).contains(tradeId));
            //parenttradeid包含传入parenttradeid
            Boolean containsParentTradeId = queryParentIds.stream().anyMatch(parentTradeId->tradeId2TradePoMap.entrySet().stream().map(po->po.getValue().getParentTradeId()).collect(Collectors.toList()).contains(parentTradeId));

            if(CollectionUtils.isEmpty(queryTradeIds) && CollectionUtils.isEmpty(queryParentIds)
                    ||(!CollectionUtils.isEmpty(queryTradeIds) && containsTradeId || (!CollectionUtils.isEmpty(queryParentIds) && (containsParentTradeId )))){
                List<Long> mgkTradeIds = modelId2TradeIdsMap.getOrDefault(key, Collections.emptyList()).stream().map(MgkModelTradeMappingPo::getTradeId).collect(Collectors.toList());
                LOGGER.info("mgkTradeIds = {}, modelId2TradeIdsMap = {}, tradeId2TradePoMap = {}",mgkTradeIds, JSON.toJSONString(modelId2TradeIdsMap),JSON.toJSONString(tradeId2TradePoMap));
                List<String> tradeNames = mgkTradeIds.stream().filter(id->tradeId2TradePoMap.containsKey(id)).map(id->tradeId2TradePoMap.get(id).getTradeName()).collect(Collectors.toList());
                List<Long> thisParentTradeIds = mgkTradeIds.stream().filter(id->tradeId2TradePoMap.containsKey(id)).map(id->tradeId2TradePoMap.get(id).getParentTradeId()).filter(id->!id.equals(0L)).collect(Collectors.toList());
                List<String>  parentTradeNames = thisParentTradeIds.stream().filter(id->parentTradeId2TradePoMap.containsKey(id)).map(id->parentTradeId2TradePoMap.get(id).getTradeName()).collect(Collectors.toList());
                modelDto.setTradeIds(mgkTradeIds);
                modelDto.setTradeNames(tradeNames);
                modelDto.setParentTradeIds(thisParentTradeIds);
                modelDto.setParentTradeNames(parentTradeNames);
                filteredList.add(modelDto);
            }
        });
        return filteredList;
    }

    public void buildTemplateStyle(List<MgkModelDto> modelDtoList){
        // get templateStyle form landing page
        List<Long> pageIds = modelDtoList.stream().map(MgkModelDto::getPageId).collect(Collectors.toList());
        Map<Long, Integer> landingPageMapInPageIds = landingPageServiceDelegate.getTemplateStyleMapByPageId(pageIds);
        if (!CollectionUtils.isEmpty(landingPageMapInPageIds)) {
            modelDtoList.forEach(dto -> {
                Integer templateStyle = landingPageMapInPageIds.get(dto.getPageId());
                dto.setTemplateStyle(templateStyle);
                if(templateStyle != null) {
                    dto.setTemplateStyleName(TemplateStyleEnum.getByCode(templateStyle).getDesc());
                }
            });
        }
    }

    private List<MgkModelTradeMappingPo> getTradeMappingPosByModelIds(List<Long> modelIds) {
        if (CollectionUtils.isEmpty(modelIds)) {
            return Collections.emptyList();
        }
        MgkModelTradeMappingPoExample example = new MgkModelTradeMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andModelIdIn(modelIds);
        return mgkModelTradeMappingDao.selectByExample(example);
    }

    private List<MgkModelTradeMappingPo> getTradeMappingPosByTradeIds(List<Long> tradeIds) {
        if (CollectionUtils.isEmpty(tradeIds)) {
            return Collections.emptyList();
        }
        MgkModelTradeMappingPoExample example = new MgkModelTradeMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andTradeIdIn(tradeIds);
        return mgkModelTradeMappingDao.selectByExample(example);
    }

    private Map<Long, String> getTradeNameMap(List<Long> tradeIds) {
        if (CollectionUtils.isEmpty(tradeIds)) {
            return Collections.emptyMap();
        }
        List<MgkTradePo> pos = this.getTradePosByTradeIds(tradeIds);
        return pos.stream().collect(Collectors.toMap(MgkTradePo::getTradeId, MgkTradePo::getTradeName));
    }

    private List<MgkTradePo> getTradePosByTradeIds(List<Long> tradeIds) {
        if (CollectionUtils.isEmpty(tradeIds)) {
            return Collections.emptyList();
        }
        MgkTradePoExample example = new MgkTradePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andTradeIdIn(tradeIds);
        return mgkTradeDao.selectByExample(example);
    }

    /**
     * 数据量小,不走es,直接走数据库
     *
     * @param queryModelParamDto 查询参数
     * @param page 分页信息
     * @return 分页信息
     */
    public PageResult<MgkModelDto> getModelDtos(QueryModelParamDto queryModelParamDto, Page page) {
        Assert.notNull(queryModelParamDto, "模板查询参数不可为空");
        Assert.notNull(page, "分页参数不可为空");
        Assert.notEmpty(queryModelParamDto.getAccountIds(), "账户信息必传");
        if(!queryModelParamDto.getAccountIds().contains(modelAccountId)){
            queryModelParamDto.setStatusList(Lists.newArrayList(ModelStatusEnum.PUBLISHED.getCode()));
        }

        if (!SHOW_FULL_SCREEN_OPEN_DOUBLE_JUMP_VIDEO_ACCS.contains(queryModelParamDto.getAccountIds().get(0))) {
            queryModelParamDto.setExcludeModelStyles(Lists.newArrayList(MgkModelStyleEnum.FULL_SCREEN_OPEN_DOUBLE_JUMP_VIDEO.getCode()));
        }

        MgkModelPoExample poExample = getModelExample(queryModelParamDto, null);
        long total = mgkModelDao.countByExample(poExample);
        if(total == 0){
            return PageResult.EMPTY_PAGE_RESULT;
        }

        List<MgkModelDto> modelDtos = getModelDtosByExample(poExample);

        //TODO 热度排序
        if("hot desc".equals(queryModelParamDto.getOrderBy())){

        }
        buildCover(modelDtos);

        modelDtos = buildTrade(modelDtos, queryModelParamDto);

        buildTemplateStyle(modelDtos);

        List<MgkModelDto> res = new ArrayList<>();
        if(!CollectionUtils.isEmpty(modelDtos)) {
            res = Lists.partition(modelDtos, page.getPageSize()).get(page.getPage() - 1);
        }

        return PageResult.<MgkModelDto>builder().total(Math.toIntExact(modelDtos.size())).records(res).build();
    }

    private void buildCover(List<MgkModelDto> modelDtos) {
        List<Long> pageIds = modelDtos.stream().map(MgkModelDto::getPageId).collect(Collectors.toList());
        List<MgkLandingPageDto> mgkLandingPageDtoList = landingPageServiceDelegate.getLandingPageDtoByPageIds(pageIds);
        Map<Long, List<MgkLandingPageDto>> pageIdMap = mgkLandingPageDtoList.stream().collect(Collectors.groupingBy(MgkLandingPageDto::getPageId));
        for(MgkModelDto modelDto : modelDtos){
            modelDto.setCoverUrl(pageIdMap.containsKey(modelDto.getPageId()) && StringUtils.isNotEmpty(pageIdMap.get(modelDto.getPageId()).get(0).getPageCover())?pageIdMap.get(modelDto.getPageId()).get(0).getPageCover(): modelDto.getCoverUrl());
        }
    }


    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public long createTrade(Operator operator, NewTradeDto newTradeDto) {
        LOGGER.info("create operator: [{}], newTradeDto: [{}]", operator, newTradeDto);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        mgkModelValidator.validateCreateTrade(newTradeDto);

        long tradeId = snowflakeIdWorker.nextId();
        this.insertTrade(tradeId, newTradeDto);
        return tradeId;
    }

    private void insertTrade(long tradeId, NewTradeDto newTradeDto) {
        MgkTradePo po = MgkTradePo.builder().build();
        BeanUtils.copyProperties(newTradeDto, po);
        po.setTradeId(tradeId);
        int res = mgkTradeDao.insertSelective(po);
        Assert.isTrue(Utils.isPositive(res), "创建行业失败");
    }

    public List<MgkModelDto> getModelDtosByName(String name, Long excludeModelId) {
        MgkModelPoExample exm = new MgkModelPoExample();
        MgkModelPoExample.Criteria criteria = exm.or().andModelNameEqualTo(name);
        ExampleUtils.notNull(excludeModelId, criteria::andModelIdNotEqualTo);
        return getModelDtosByExample(exm);
    }


    private List<MgkModelDto> getModelDtosByExample(MgkModelPoExample example) {
        List<MgkModelPo> mgkModelPos = mgkModelDao.selectByExample(example);
        return this.convertModelPosToDtos(mgkModelPos);
    }

    private List<MgkModelDto> convertModelPosToDtos(List<MgkModelPo> mgkModelPos) {
        if (CollectionUtils.isEmpty(mgkModelPos)) {
            return Collections.emptyList();
        }
        return mgkModelPos.stream().map(this::convertModelPoToDto).collect(Collectors.toList());
    }

    private MgkModelDto convertModelPoToDto(MgkModelPo po) {
        MgkModelDto dto = MgkModelDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        dto.setType(Integer.parseInt(po.getType().toString()));
        return dto;
    }

    private MgkModelPoExample getModelExample(QueryModelParamDto param, Page page) {
        if(!CollectionUtils.isEmpty(param.getTradeIds())){
            List<MgkModelTradeMappingPo> mgkModelTradeMappingPoList  = getTradeMappingPosByTradeIds(param.getTradeIds());
            List<Long> modelIdsByTradeIds = mgkModelTradeMappingPoList.stream().map(MgkModelTradeMappingPo::getModelId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(param.getModelIds())){
                param.setModelIds(modelIdsByTradeIds);
            }else{
                List<Long> modelIds= param.getModelIds();
                modelIds.retainAll(modelIdsByTradeIds);
                param.setModelIds(modelIds);
            }
        }
        MgkModelPoExample example = this.getModelExample(param);
        if(page != null) {
            example.setLimit(page.getLimit());
            example.setOffset(page.getOffset());
        }
        if(!StringUtils.isEmpty(param.getOrderBy()) && ("ctime desc".equals(param.getOrderBy()))){
            example.setOrderByClause("ctime desc");
        }
        return example;
    }

    private MgkModelPoExample getModelExample(QueryModelParamDto param) {
        MgkModelPoExample example = new MgkModelPoExample();
        MgkModelPoExample.Criteria c = example.or();
        ObjectUtils.setList(param::getModelIds, c::andModelIdIn);
        ObjectUtils.setList(param::getModelStyles, c::andModelStyleIn);
        ObjectUtils.setList(param::getStatusList, c::andModelStatusIn);
        ObjectUtils.setList(param::getModelTypes, c::andModelTypeIn);
        ObjectUtils.setList(param::getModuleContentIds, c::andModuleContentIdIn);
        ObjectUtils.setList(param::getModuleStyleIds, c::andModuleStyleIdIn);
        ObjectUtils.setList(param::getExcludeModelStyles, c::andModelStyleNotIn);

        c.andTypeEqualTo(param.getType());
        if(MgkTemplateType.MODULE.getCode().equals(param.getType())){
            ExampleUtils.notNull(param.getIsAdmin(), c::andIsAdminEqualTo);
            if(WhetherEnum.YES.getCode().equals(param.getIsAdmin())){
                c.andAccountIdEqualTo(modelAccountId);
            }else {
                c.andAccountIdIn(param.getAccountIds());
            }
        }

        if (!Strings.isNullOrEmpty(param.getNameLike())) {
            c.andModelNameLike("%" + param.getNameLike() + "%");
        }
        return example;
    }

    private MgkModelTradeMappingPoExample getModelTradeMappingExample(QueryModelTradeMappingParamDto param) {
        MgkModelTradeMappingPoExample example = new MgkModelTradeMappingPoExample();
        MgkModelTradeMappingPoExample.Criteria c = example.or();

        ObjectUtils.setList(param::getModelIds, c::andModelIdIn);
        ObjectUtils.setList(param::getTradeIds, c::andTradeIdIn);

        c.andIsDeletedEqualTo(param.getIsDeleted());
        return example;
    }

    private void insertModel(Operator operator, long modelId, long pageId, NewModelDto newModelDto) {
        // New Model in DB
        MgkModelPo modelPo = MgkModelPo.builder()
                .modelId(modelId)
                .modelVersion(newModelDto.getModelVersion())
                .creator(operator.getOperatorName())
                .modelStatus(ModelStatusEnum.UNPUBLISHED.getCode())
                .pageId(pageId)
                .type(newModelDto.getType())
                .build();
        BeanUtils.copyProperties(newModelDto, modelPo);
        //自己收藏的模版不需要审核
        if(MgkTemplateType.MODULE.getCode().equals(newModelDto.getType())
                && WhetherEnum.NO.getCode().equals(newModelDto.getIsAdmin())){
            modelPo.setModelStatus(ModelStatusEnum.PUBLISHED.getCode());
        }
        int res = mgkModelDao.insertSelective(modelPo);
        Assert.isTrue(res == 1, "保存失败，请稍后重试");
    }


    private void insertModelTradeMapping(long modelId, NewModelDto newModelDto) {
        this.batchInsertModelTradeMapping(this.buildModelTradeMappingDtos(modelId, newModelDto.getTradeIds()));
    }

    private long insertModelPage(Operator operator, long modelId, NewModelDto newModelDto) {
        NewLandingPageDto newLandingPageDto = newModelDto.getNewLandingPageDto();
        newLandingPageDto.setModelId(modelId);
        newLandingPageDto.setIsModel(WhetherEnum.YES.getCode());
        return landingPageServiceDelegate.create(operator, newLandingPageDto);
    }

    private List<MgkModelTradeMappingDto> buildModelTradeMappingDtos(long modelId, List<Long> tradeIds) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        return tradeIds.stream().map(tradeId -> MgkModelTradeMappingDto.builder()
                .modelId(modelId)
                .tradeId(tradeId)
                .isDeleted(IsDeleted.VALID.getCode())
                .ctime(timestamp)
                .mtime(timestamp)
                .build()).collect(Collectors.toList());
    }

    private void batchInsertModelTradeMapping(List<MgkModelTradeMappingDto> dtos) {
        if(CollectionUtils.isEmpty(dtos)){
            return;
        }
        List<MgkModelTradeMappingPo> records = this.convertModelTradeDtos2Pos(dtos);
        int res = mgkModelTradeMappingDao.insertBatch(records);
        Assert.isTrue(Utils.isPositive(res), "插入模板行业映射失败");
    }

    private List<MgkModelTradeMappingPo> convertModelTradeDtos2Pos(List<MgkModelTradeMappingDto> dtos) {
        return dtos.stream().map(this::convertModelTradeDto2Po).collect(Collectors.toList());
    }

    private MgkModelTradeMappingPo convertModelTradeDto2Po(MgkModelTradeMappingDto dto) {
        MgkModelTradeMappingPo po = MgkModelTradeMappingPo.builder().build();
        BeanUtils.copyProperties(dto, po);
        return po;
    }


    public List<MgkTradeDto> getTradeDtos(QueryTradeParamDto paramDto) {
        MgkTradePoExample example = this.getTradeExample(paramDto);
        List<MgkTradePo> tradePos = this.getTradePosByExample(example);
        return this.convertTradePos2Dtos(tradePos);
    }

    private List<MgkTradeDto> convertTradePos2Dtos(List<MgkTradePo> tradePos) {
        if (CollectionUtils.isEmpty(tradePos)) return Collections.emptyList();
        return tradePos.stream().map(this::convertTradePo2Dto).collect(Collectors.toList());
    }

    private MgkTradeDto convertTradePo2Dto(MgkTradePo tradePo) {
        MgkTradeDto tradeDto = MgkTradeDto.builder().build();
        BeanUtils.copyProperties(tradePo, tradeDto);
        return tradeDto;
    }

    private List<MgkTradePo> getTradePosByExample(MgkTradePoExample example) {
        return mgkTradeDao.selectByExample(example);
    }

    private MgkTradePoExample getTradeExample(QueryTradeParamDto paramDto) {
        MgkTradePoExample example = new MgkTradePoExample();
        MgkTradePoExample.Criteria or = example.or();

        or.andIsDeletedEqualTo(paramDto.getIsDeleted());
        ObjectUtils.setList(paramDto::getParentTradeIds, or::andParentTradeIdIn);
        ObjectUtils.setList(paramDto::getTradeIds, or::andTradeIdIn);
        ObjectUtils.setList(paramDto::getLevel, or::andTradeLevelIn);

        if (!Strings.isNullOrEmpty(paramDto.getNameLike())) or.andTradeNameLike("%" + paramDto.getNameLike() + "%");
        ObjectUtils.setObject(paramDto::getOrderBy, example::setOrderByClause);
        return example;
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void update(Operator operator, UpdateModelDto updateModelDto) {
        this.updateModel(updateModelDto);
        this.updateModelTradeMapping(updateModelDto.getModelId(), updateModelDto.getTradeIds());
        this.insertLog(operator, updateModelDto.getModelId(), LogObjFlagEnum.MODEL.getCode(), LogOperateTypeEnum.MODEL_MODIFY.getCode());
    }


    private void insertLog(Operator operator, Long objId, Integer objFlag, Integer operateType) {
        mgkLogService.insertLog(NewLogOperationDto.builder()
                .accountId(operator.getOperatorId())
                .objId(objId)
                .objFlag(objFlag)
                .operateType(operateType)
                .operatorUsername(operator.getOperatorName())
                .operatorType(operator.getOperatorType().getCode())
                .oldValue("")
                .newValue("")
                .build());
    }

    private void updateModelTradeMapping(Long modelId, List<Long> updateTradeIds) {
        // Update ModelTradeMapping in DB
        MgkModelTradeMappingPoExample modelTradeMappingPoExample = new MgkModelTradeMappingPoExample();
        modelTradeMappingPoExample.or().andModelIdEqualTo(modelId);
        List<MgkModelTradeMappingPo> modelTradeMappingPos = mgkModelTradeMappingDao.selectByExample(modelTradeMappingPoExample);
        List<Long> oldTradeIds = modelTradeMappingPos.stream().map(MgkModelTradeMappingPo::getTradeId).collect(Collectors.toList());

        List<Long> intersection = oldTradeIds.stream().filter(updateTradeIds::contains).collect(Collectors.toList());
        List<Long> deletedIds = oldTradeIds.stream().filter(tradeId -> !intersection.contains(tradeId)).collect(Collectors.toList());
        List<Long> appendIds = updateTradeIds.stream().filter(tradeId -> !intersection.contains(tradeId)).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(intersection)) {
            // status 更新为 0
            MgkModelTradeMappingPoExample example1 = new MgkModelTradeMappingPoExample();
            example1.or().andModelIdEqualTo(modelId).andTradeIdIn(intersection);
            MgkModelTradeMappingPo mgkModelTradeMappingPo = MgkModelTradeMappingPo.builder().isDeleted(IsDeleted.VALID.getCode()).build();
            mgkModelTradeMappingDao.updateByExampleSelective(mgkModelTradeMappingPo, example1);
        }

        if (!CollectionUtils.isEmpty(deletedIds)) {
            // status 更新为 1
            MgkModelTradeMappingPoExample example1 = new MgkModelTradeMappingPoExample();
            example1.or().andModelIdEqualTo(modelId).andTradeIdIn(deletedIds);
            MgkModelTradeMappingPo mgkModelTradeMappingPo = MgkModelTradeMappingPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();
            mgkModelTradeMappingDao.updateByExampleSelective(mgkModelTradeMappingPo, example1);
        }

        if (!CollectionUtils.isEmpty(appendIds)) {
            List<MgkModelTradeMappingPo> mgkModelTradeMappingPos = appendIds.stream().map(appendId -> {
                Timestamp currentTime = new Timestamp(System.currentTimeMillis());
                return MgkModelTradeMappingPo.builder()
                        .modelId(modelId)
                        .tradeId(appendId)
                        .isDeleted(IsDeleted.VALID.getCode())
                        .mtime(currentTime)
                        .ctime(currentTime)
                        .build();
            }).collect(Collectors.toList());
            mgkModelTradeMappingDao.insertBatch(mgkModelTradeMappingPos);
        }
    }

    private void updateModel(UpdateModelDto updateModelDto) {
        MgkModelPoExample example = new MgkModelPoExample();
        example.or().andModelIdEqualTo(updateModelDto.getModelId());
        List<MgkModelPo> mgkModelPos = mgkModelDao.selectByExample(example);
        Assert.isTrue(!CollectionUtils.isEmpty(mgkModelPos), "模板不存在");
        // 模板名称不能重复 包括已删除的 由于mysql约束
        Assert.isTrue(CollectionUtils.isEmpty(getModelDtosByName(updateModelDto.getModelName(),
                updateModelDto.getModelId())), "模板名称已被占用");
        // Update Model in DB
        MgkModelPo modelPo = MgkModelPo.builder().build();
        BeanUtils.copyProperties(updateModelDto, modelPo);
        modelPo.setId(mgkModelPos.get(0).getId());
        modelPo.setMtime(new Timestamp(System.currentTimeMillis()));
        int res = mgkModelDao.updateByPrimaryKeySelective(modelPo);
        Assert.isTrue(Utils.isPositive(res), "更新模板失败");
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void publish(Operator operator, Long modelId) {
        List<MgkModelPo> modelPos = this.getModelsByIds(Collections.singletonList(modelId));
        Assert.isTrue(!CollectionUtils.isEmpty(modelPos), "更新失败，模板不存在");
        MgkModelPo modelPo = modelPos.get(0);

        this.updateStatus(modelPo, ModelStatusEnum.PUBLISHED);
        landingPageServiceDelegate.manualPublish(operator, modelPo.getPageId());

        this.insertLog(operator, modelId, LogObjFlagEnum.MODEL.getCode(), LogOperateTypeEnum.MODEL_PUBLISHED.getCode());
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void downline(Operator operator, Long modelId) {
        List<MgkModelPo> modelPos = this.getModelsByIds(Collections.singletonList(modelId));
        Assert.isTrue(!CollectionUtils.isEmpty(modelPos), "更新失败，模板不存在");
        MgkModelPo modelPo = modelPos.get(0);

        this.updateStatus(modelPo, ModelStatusEnum.DOWNLINE);
        landingPageServiceDelegate.downline(operator, modelPo.getPageId());

        this.insertLog(operator, modelId, LogObjFlagEnum.MODEL.getCode(), LogOperateTypeEnum.MODEL_DOWNLINE.getCode());
    }


    private void updateStatus(MgkModelPo modelPo, ModelStatusEnum operatedStatus) {
        this.validateToStatus(ModelStatusEnum.getByCode(modelPo.getModelStatus()), operatedStatus);
        int res = this.updateModelStatus(modelPo.getModelId(), operatedStatus);
        Assert.isTrue(Utils.isPositive(res), "更新状态失败");
    }

    private int updateModelStatus(Long modelId, ModelStatusEnum operatedStatus) {
        MgkModelPo po = MgkModelPo.builder().modelStatus(operatedStatus.getCode()).build();
        MgkModelPoExample example = new MgkModelPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andModelIdEqualTo(modelId);
        return mgkModelDao.updateByExampleSelective(po, example);
    }


    private void validateToStatus(ModelStatusEnum actualStatus, ModelStatusEnum operatedStatus) {
        Assert.isTrue(!actualStatus.equals(operatedStatus), "该模板状态未作任何更改，请刷新后重试！");
        Assert.isTrue(actualStatus.validateToStatus(operatedStatus), String.format("状态为%s的模板不可转换为%s", actualStatus.getDesc(), operatedStatus.getDesc()));
    }

    public List<MgkModelPo> getModelsByIds(List<Long> modelIds) {
        if (CollectionUtils.isEmpty(modelIds)) {
            return Collections.emptyList();
        }
        MgkModelPoExample example = new MgkModelPoExample();
        MgkModelPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (modelIds.size() == 1) {
            criteria.andModelIdEqualTo(modelIds.get(0));
        } else {
            criteria.andModelIdIn(modelIds);
        }
        example.setOrderByClause(MgkConstants.ORDER_BY_MTIME_DESC);
        return mgkModelDao.selectByExample(example);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void batchDisable(Operator operator, List<Long> modelIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(modelIds), "模板id不可为空");
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");

        List<MgkModelPo> mgkModelPos = this.getModelsByIds(modelIds);
        Assert.isTrue(!CollectionUtils.isEmpty(mgkModelPos), "模板不存在");
        mgkModelPos.forEach(mgkModelPo -> this.validateToStatus(ModelStatusEnum.getByCode(mgkModelPo.getModelStatus()), ModelStatusEnum.DELETED));
        this.batchUpdateStatus(operator, modelIds, ModelStatusEnum.DELETED);
        landingPageServiceDelegate.batchDisable(operator, mgkModelPos.stream().map(MgkModelPo::getPageId).collect(Collectors.toList()));

        this.batchInsertLog(operator, modelIds, LogObjFlagEnum.MODEL.getCode(), LogOperateTypeEnum.MODEL_DOWNLINE.getCode());
    }

    private void batchInsertLog(Operator operator, List<Long> modelIds, Integer objFlag, Integer operateType) {
        modelIds.forEach(modelId -> this.insertLog(operator, modelId, objFlag, operateType));
    }

    private void batchUpdateStatus(Operator operator, List<Long> modelIds, ModelStatusEnum operatedStatus) {
        Assert.isTrue(!CollectionUtils.isEmpty(modelIds), "模板id不可为空");
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");

        MgkModelPo modelPo = MgkModelPo.builder().modelStatus(operatedStatus.getCode()).build();

        MgkModelPoExample example = new MgkModelPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andModelIdIn(modelIds);
        int res = mgkModelDao.updateByExampleSelective(modelPo, example);
        Assert.isTrue(Utils.isPositive(res), "删除模板失败");
    }

    public List<MgkRightLabelEnum> hasRights(Integer operatorId) {
        List<MgkRightLabelEnum> rights = new ArrayList<>();
        if (modelAccountId.equals(operatorId)) {
            rights.add(MgkRightLabelEnum.MODEL_MANAGE_RIGHT);
        }
        return rights;
    }

    public List<MgkModelUsedDto> getModelUsed(Operator operator) {

        List<MgkModelUsedDto> dtos = new ArrayList<>();

        List<Long> modelIds = landingPageServiceDelegate.getModelUsedDtos(operator);
        if (CollectionUtils.isEmpty(modelIds)) return Collections.emptyList();

        if (modelIds.contains(0L)) {
            dtos.add(MgkModelUsedDto.builder()
                    .modelName("自定义")
                    .modelId(0L)
                    .build());
        }

        List<Long> modelIdsWithOutZero = modelIds.stream().filter(Utils::isPositive).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(modelIdsWithOutZero)) return dtos;

        List<MgkModelDto> modelDtos = getMgkModelDtosByIds(modelIdsWithOutZero);
        if (CollectionUtils.isEmpty(modelDtos)) return dtos;

        modelDtos.forEach(dto ->
                dtos.add(MgkModelUsedDto.builder()
                        .modelId(dto.getModelId())
                        .modelName(dto.getModelName())
                        .build()));

        return dtos;
    }
}
