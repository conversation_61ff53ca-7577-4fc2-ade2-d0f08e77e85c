package com.bilibili.collage.biz.dao;

import com.bilibili.collage.biz.po.MgkCollageLayerStrokePo;
import com.bilibili.collage.biz.po.MgkCollageLayerStrokePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkCollageLayerStrokeDao {
    long countByExample(MgkCollageLayerStrokePoExample example);

    int deleteByExample(MgkCollageLayerStrokePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkCollageLayerStrokePo record);

    int insertBatch(List<MgkCollageLayerStrokePo> records);

    int insertUpdateBatch(List<MgkCollageLayerStrokePo> records);

    int insert(MgkCollageLayerStrokePo record);

    int insertUpdateSelective(MgkCollageLayerStrokePo record);

    int insertSelective(MgkCollageLayerStrokePo record);

    List<MgkCollageLayerStrokePo> selectByExample(MgkCollageLayerStrokePoExample example);

    MgkCollageLayerStrokePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkCollageLayerStrokePo record, @Param("example") MgkCollageLayerStrokePoExample example);

    int updateByExample(@Param("record") MgkCollageLayerStrokePo record, @Param("example") MgkCollageLayerStrokePoExample example);

    int updateByPrimaryKeySelective(MgkCollageLayerStrokePo record);

    int updateByPrimaryKey(MgkCollageLayerStrokePo record);
}