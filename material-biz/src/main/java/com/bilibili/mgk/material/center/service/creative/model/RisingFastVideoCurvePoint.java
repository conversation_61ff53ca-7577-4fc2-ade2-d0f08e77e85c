package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.facade.converter.RisingVideoAdItem180DayAccumulationOnesDTO;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import io.vavr.control.Try;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/27
 */
@Data
@Accessors(chain = true)
public class RisingFastVideoCurvePoint implements SnakeCaseBody {


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "日期", example = "2019-06-27")
    private LocalDate date;

    @ApiModelProperty(value = "时间戳", example = "1561724800")
    private Long timestamp;


    /**
     * 增量值， 大部分为long， 大于互动率和博粉笔为小数
     */
    @ApiModelProperty(value = "数值", example = "110.5")
    private String value;


    /**
     * 临时变量用于对缺失的第一个点的补偿
     */
    @JsonIgnore
    private List<Long> valueDecimal;

    public static RisingFastVideoCurvePoint asVideoCurvePoint(
            RisingFastVideoCurveType curveType,
            RisingFastVideo video,

            boolean increasing
    ) {
        RisingFastVideoCurvePoint result = new RisingFastVideoCurvePoint();

        result.date = video.getParsedLogDate();
        result.timestamp = Optional.ofNullable(video.getParsedLogDate())
                .map(date -> {
                    return date.atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                }).orElse(null);

        result.valueDecimal = Try.of(() -> {

            List<Long> valueDecimals = new ArrayList<>();

            Long value = null;

            Long extraValue = null;

            switch (curveType) {
                case play: {

                    value = increasing ? video.getPlayIncr() : video.getPlay();
                    break;
                }
                case fav: {

                    value = increasing ? video.getFavIncr() : video.getFav();
                    break;
                }
                case share: {

                    value = increasing ? video.getShareIncr() : video.getShare();
                    break;

                }
                case coin: {

                    value = increasing ? video.getCoinIncr() : video.getCoin();
                    break;

                }
                case danmu: {

                    value = increasing ? video.getDanmuIncr() : video.getDanmu();
                    break;

                }
                case likes: {
                    value = increasing ? video.getLikesIncr() : video.getLikes();

                    break;

                }
                case reply: {
                    value = increasing ? video.getReplyIncr() : video.getReply();

                    break;
                }

                case hudong_rate: {

                    value = increasing ? video.getHudongIncr() : video.getHudong();

                    extraValue = increasing ? video.getPlayIncr() : video.getPlay();

                    break;

                }
                case play_fans_rate: {

                    // 无论哪个都是除以fans 范式计算前一天的fans需要fansIncr， 对于第一天而言 fans=fansIncr反而不需要考虑除数的问题了

                    value = increasing ? video.getPlayIncr() : video.getPlay();

                    extraValue = increasing ? video.getFansIncr() : video.getFans();

                    break;
                }
                default: {
                    break;
                }

            }

            if (value != null) {
                valueDecimals.add(value);
            }
            if (extraValue != null) {
                valueDecimals.add(extraValue);
            }
            return valueDecimals;

        }).getOrNull();

        result.value = Try.of(() -> {
            String value = null;
            switch (curveType) {
                case play: {
                    if (increasing) {
                        value = Try.of(() -> video.getPlayIncr().toString()).getOrNull();
                    } else {
                        value = Try.of(() -> video.getPlay().toString()).getOrNull();
                    }
                    break;
                }
                case fav: {
                    if (increasing) {
                        value = Try.of(() -> video.getFavIncr().toString()).getOrNull();
                    } else {
                        value = Try.of(() -> video.getFav().toString()).getOrNull();
                    }
                    break;
                }
                case share: {
                    if (increasing) {
                        value = Try.of(() -> video.getShareIncr().toString()).getOrNull();
                    } else {
                        value = Try.of(() -> video.getShare().toString()).getOrNull();
                    }
                    break;

                }
                case coin: {
                    if (increasing) {
                        value = Try.of(() -> video.getCoinIncr().toString()).getOrNull();
                    } else {
                        value = Try.of(() -> video.getCoin().toString()).getOrNull();
                    }
                    break;

                }
                case danmu: {
                    if (increasing) {
                        value = Try.of(() -> video.getDanmuIncr().toString()).getOrNull();
                    } else {
                        value = Try.of(() -> video.getDanmu().toString()).getOrNull();
                    }
                    break;

                }
                case likes: {
                    value = increasing ? Try.of(() -> video.getLikesIncr().toString()).getOrNull() :
                            Try.of(() -> video.getLikes().toString()).getOrNull();

                    break;

                }
                case reply: {
                    value = increasing ? Try.of(() -> video.getReplyIncr().toString()).getOrNull()
                            : Try.of(() -> video.getReply().toString()).getOrNull();
                    break;
                }

                case hudong_rate: {
                    value = increasing ?
                            Try.of(() -> String.format("%.4f", video.getHudongIncr() * 1.0 / video.getPlayIncr())).getOrNull()
                            : Try.of(() -> String.format("%.4f", video.getHudong() * 1.0 / video.getPlay())).getOrNull();

                    break;

                }
                case play_fans_rate: {
                    value = increasing ?
                            Try.of(() -> String.format("%.4f", video.getPlayIncr() * 1.0 / video.getFans())).getOrNull()
                            :Try.of(() -> String.format("%.4f", video.getPlay() * 1.0 / video.getFans())).getOrNull();
                    break;
                }
                default: {
                    // nothing
                }

            }
            return value;
        }).getOrNull();

        return result;
    }

    public RisingFastVideoCurvePoint setValueOfVideoCurveByDecimal(
            RisingFastVideoCurveType curveType,
            List<Long> valueDecimals) {

        this.value = Try.of(() -> {
            String value = null;
            switch (curveType) {
                case play:
                case fav:
                case share:
                case coin:
                case danmu:
                case likes:
                case reply: {
                    value = valueDecimals.get(0).toString();
                    break;
                }
                case hudong_rate: {
                    // hudongIncr - playIncr
                    value = Try.of(() -> String.format("%.4f", valueDecimals.get(0) * 1.0 / valueDecimal.get(1)))
                            .getOrNull();
                    break;

                }
                case play_fans_rate: {
                    // layIncr / play
                    // 注意此处 取得是fans而不是fansIncr只是因为fans=fansIncr

                    value = Try.of(() -> String.format("%.4f", valueDecimals.get(0) * 1.0 / valueDecimal.get(1)))
                            .getOrNull();

                    break;
                }
                default: {
                    // nothing
                }

            }
            return value;
        }).getOrNull();

        return this;

    }


    public static RisingFastVideoCurvePoint asItemCurveIncrPoint(
            RisingFastVideoCurveType curveType,
            RisingVideoAdItem increasing
    ) {

        RisingFastVideoCurvePoint result = new RisingFastVideoCurvePoint();
        result.date = increasing.getParsedLogDate();
        result.timestamp = Optional.ofNullable(increasing.getParsedLogDate())
                .map(date -> {
                    return date.atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                }).orElse(null);
        switch (curveType) {
            case up_count: {
                result.value = Try.of(() -> increasing.getUpCountIncr().toString()).getOrNull();
                break;
            }
            case video_count: {
                result.value = Try.of(() -> increasing.getVideoCountIncr().toString()).getOrNull();
                break;
            }
            case play: {
                result.value = Try.of(() -> increasing.getPlayIncr().toString()).getOrNull();
                break;
            }

            case share: {
                result.value = Try.of(() -> increasing.getShareIncr().toString()).getOrNull();
                break;
            }
            case likes: {
                result.value = Try.of(() -> increasing.getLikesIncr().toString()).getOrNull();
                break;
            }
            case danmu: {
                result.value = Try.of(() -> increasing.getDanmuIncr().toString()).getOrNull();
                break;
            }
            case reply: {
                result.value = Try.of(() -> increasing.getReplyIncr().toString()).getOrNull();
                break;
            }
            case coin: {
                result.value = Try.of(() -> increasing.getCoinIncr().toString()).getOrNull();
                break;
            }
            case fav: {
                result.value = Try.of(() -> increasing.getFavIncr().toString()).getOrNull();
                break;
            }
            case hudong_rate:
            case play_fans_rate:
            default: {
                break;
            }

        }

        return result;

    }

    public static RisingFastVideoCurvePoint asItemCurveAccPoint(
            RisingFastVideoCurveType curveType,
            RisingVideoAdItem180DayAccumulationOnesDTO accumulate
    ) {

        RisingFastVideoCurvePoint result = new RisingFastVideoCurvePoint();
        result.date = accumulate.getParsedLogDate();
        result.timestamp = Optional.ofNullable(accumulate.getParsedLogDate())
                .map(date -> {
                    return date.atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                }).orElse(null);
        switch (curveType) {
            case up_count: {
                result.value = Try.of(() -> accumulate.getUpCount().toString()).getOrNull();
                break;
            }
            case video_count: {
                result.value = Try.of(() -> accumulate.getVideoCount().toString()).getOrNull();
                break;
            }
            case fav: {
                result.value = Try.of(() -> accumulate.getFav().toString()).getOrNull();
                break;
            }
            case danmu: {
                result.value = Try.of(() -> accumulate.getDanmu().toString()).getOrNull();
                break;
            }
            case share: {
                result.value = Try.of(() -> accumulate.getShare().toString()).getOrNull();
                break;
            }
            case play: {
                result.value = Try.of(() -> accumulate.getPlay().toString()).getOrNull();
                break;
            }
            case coin: {
                result.value = Try.of(() -> accumulate.getCoin().toString()).getOrNull();
                break;
            }
            case likes: {
                result.value = Try.of(() -> accumulate.getLikes().toString()).getOrNull();
                break;
            }
            case reply: {
                result.value = Try.of(() -> accumulate.getReply().toString()).getOrNull();
                break;
            }
            case play_fans_rate:
            case hudong_rate:
            default: {
                break;
            }
        }

        return result;
    }

}
