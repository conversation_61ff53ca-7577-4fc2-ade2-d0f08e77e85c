package com.bilibili.mgk.platform.api.wechat.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.common.utils.aes.AesException;

import java.io.UnsupportedEncodingException;

public interface IMgkWorkWechatCallBackService {

    String callbackCommand(String msgSignature, String timestamp,
                           String nonce, String postData) throws ServiceException, AesException;

    String checkCallback(String msgSignature, String timestamp,
                         String nonce, String echostr) throws ServiceException, AesException;

    String callbackData(String msgSignature, String timestamp,
                           String nonce, String postData) throws ServiceException, AesException;

    String savaTrackId(String trackId, String fromTrackId) throws UnsupportedEncodingException;
}
