package com.biz.common.doc.tree.http.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/11
 */
@Setter
@Getter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class EsSearchReq {

    @JsonProperty("_source")
    private boolean source;

    @JsonProperty("highlight")
    private Map<String, Object> highlight;

    @JsonProperty("from")
    private int from;

    @JsonProperty("size")
    private int size;

    @JsonProperty("query")
    private Query query;

    @JsonProperty("sort")
    private List<Map<String, SortOrder>> sort;

    @JsonProperty("track_scores")
    private boolean trackScores;

    @JsonProperty("track_total_hits")
    private boolean trackTotalHits;

    // Get<PERSON> and Setters
    @Setter
    @Getter
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Query {

        @JsonProperty("bool")
        private Bool bool;

        // Getters and Setters
        @Setter
        @Getter
        @NoArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Bool {

            @JsonProperty("filter")
            private List<Filter> filter;

            // Getters and Setters
            @Setter
            @Getter
            @NoArgsConstructor
            @JsonIgnoreProperties(ignoreUnknown = true)
            public static class Filter {

                @JsonProperty("term")
                private Map<String, Object> term;

                @JsonProperty("range")
                private Range range;

                // Getters and Setters
                @Setter
                @Getter
                @NoArgsConstructor
                @JsonIgnoreProperties(ignoreUnknown = true)
                public static class Range {

                    @JsonProperty("ctime")
                    private Ctime ctime;

                    // Getters and Setters
                    @Setter
                    @Getter
                    @NoArgsConstructor
                    @JsonIgnoreProperties(ignoreUnknown = true)
                    public static class Ctime {

                        @JsonProperty("from")
                        private String from;

                        @JsonProperty("include_lower")
                        private boolean includeLower;

                        @JsonProperty("include_upper")
                        private boolean includeUpper;

                        @JsonProperty("to")
                        private String to;

                        // Getters and Setters
                    }
                }
            }
        }
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SortOrder {

        @JsonProperty("mode")
        private String mode;

        @JsonProperty("order")
        private String order;

        // Getters and Setters
    }
}