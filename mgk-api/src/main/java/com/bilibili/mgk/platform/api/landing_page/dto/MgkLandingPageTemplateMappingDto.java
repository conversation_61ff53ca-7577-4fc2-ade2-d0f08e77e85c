package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName MgkLandingPageTemplateMappingDto
 * <AUTHOR>
 * @Date 2022/2/24 5:01 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MgkLandingPageTemplateMappingDto implements Serializable {
    private static final long serialVersionUID = 4966667787L;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 模板page_id
     */
    private Long templatePageId;

    /**
     * 视频模板类型 0-普通落地页 1-落地页模板 2-创意视频联投模板 3-去除视频副本
     */
    private Integer isModel;
}
