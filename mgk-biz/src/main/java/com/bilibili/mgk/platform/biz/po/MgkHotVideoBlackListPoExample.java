package com.bilibili.mgk.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MgkHotVideoBlackListPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MgkHotVideoBlackListPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBlackIdIsNull() {
            addCriterion("black_id is null");
            return (Criteria) this;
        }

        public Criteria andBlackIdIsNotNull() {
            addCriterion("black_id is not null");
            return (Criteria) this;
        }

        public Criteria andBlackIdEqualTo(Long value) {
            addCriterion("black_id =", value, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdNotEqualTo(Long value) {
            addCriterion("black_id <>", value, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdGreaterThan(Long value) {
            addCriterion("black_id >", value, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdGreaterThanOrEqualTo(Long value) {
            addCriterion("black_id >=", value, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdLessThan(Long value) {
            addCriterion("black_id <", value, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdLessThanOrEqualTo(Long value) {
            addCriterion("black_id <=", value, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdIn(List<Long> values) {
            addCriterion("black_id in", values, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdNotIn(List<Long> values) {
            addCriterion("black_id not in", values, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdBetween(Long value1, Long value2) {
            addCriterion("black_id between", value1, value2, "blackId");
            return (Criteria) this;
        }

        public Criteria andBlackIdNotBetween(Long value1, Long value2) {
            addCriterion("black_id not between", value1, value2, "blackId");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andBvidIsNull() {
            addCriterion("bvid is null");
            return (Criteria) this;
        }

        public Criteria andBvidIsNotNull() {
            addCriterion("bvid is not null");
            return (Criteria) this;
        }

        public Criteria andBvidEqualTo(String value) {
            addCriterion("bvid =", value, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidNotEqualTo(String value) {
            addCriterion("bvid <>", value, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidGreaterThan(String value) {
            addCriterion("bvid >", value, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidGreaterThanOrEqualTo(String value) {
            addCriterion("bvid >=", value, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidLessThan(String value) {
            addCriterion("bvid <", value, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidLessThanOrEqualTo(String value) {
            addCriterion("bvid <=", value, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidLike(String value) {
            addCriterion("bvid like", value, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidNotLike(String value) {
            addCriterion("bvid not like", value, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidIn(List<String> values) {
            addCriterion("bvid in", values, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidNotIn(List<String> values) {
            addCriterion("bvid not in", values, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidBetween(String value1, String value2) {
            addCriterion("bvid between", value1, value2, "bvid");
            return (Criteria) this;
        }

        public Criteria andBvidNotBetween(String value1, String value2) {
            addCriterion("bvid not between", value1, value2, "bvid");
            return (Criteria) this;
        }

        public Criteria andBlackTypeIsNull() {
            addCriterion("black_type is null");
            return (Criteria) this;
        }

        public Criteria andBlackTypeIsNotNull() {
            addCriterion("black_type is not null");
            return (Criteria) this;
        }

        public Criteria andBlackTypeEqualTo(Integer value) {
            addCriterion("black_type =", value, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeNotEqualTo(Integer value) {
            addCriterion("black_type <>", value, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeGreaterThan(Integer value) {
            addCriterion("black_type >", value, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("black_type >=", value, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeLessThan(Integer value) {
            addCriterion("black_type <", value, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeLessThanOrEqualTo(Integer value) {
            addCriterion("black_type <=", value, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeIn(List<Integer> values) {
            addCriterion("black_type in", values, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeNotIn(List<Integer> values) {
            addCriterion("black_type not in", values, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeBetween(Integer value1, Integer value2) {
            addCriterion("black_type between", value1, value2, "blackType");
            return (Criteria) this;
        }

        public Criteria andBlackTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("black_type not between", value1, value2, "blackType");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdIsNull() {
            addCriterion("ad_type_creative_id is null");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdIsNotNull() {
            addCriterion("ad_type_creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdEqualTo(String value) {
            addCriterion("ad_type_creative_id =", value, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdNotEqualTo(String value) {
            addCriterion("ad_type_creative_id <>", value, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdGreaterThan(String value) {
            addCriterion("ad_type_creative_id >", value, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdGreaterThanOrEqualTo(String value) {
            addCriterion("ad_type_creative_id >=", value, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdLessThan(String value) {
            addCriterion("ad_type_creative_id <", value, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdLessThanOrEqualTo(String value) {
            addCriterion("ad_type_creative_id <=", value, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdLike(String value) {
            addCriterion("ad_type_creative_id like", value, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdNotLike(String value) {
            addCriterion("ad_type_creative_id not like", value, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdIn(List<String> values) {
            addCriterion("ad_type_creative_id in", values, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdNotIn(List<String> values) {
            addCriterion("ad_type_creative_id not in", values, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdBetween(String value1, String value2) {
            addCriterion("ad_type_creative_id between", value1, value2, "adTypeCreativeId");
            return (Criteria) this;
        }

        public Criteria andAdTypeCreativeIdNotBetween(String value1, String value2) {
            addCriterion("ad_type_creative_id not between", value1, value2, "adTypeCreativeId");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}