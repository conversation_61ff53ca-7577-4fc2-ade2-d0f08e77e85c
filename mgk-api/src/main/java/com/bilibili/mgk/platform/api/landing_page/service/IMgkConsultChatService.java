package com.bilibili.mgk.platform.api.landing_page.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.landing_page.dto.*;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/20 12:07
 */
public interface IMgkConsultChatService {

    // 开启会话
    ChatStartRespDto startConsultChat(ConsultLandingPageDto config , StartChatDto startChatDto)throws ServiceException;

    // 结束会话
    ChatEndRespDto endConsultChat(ConsultLandingPageDto config , EndChatDto endChatDto) throws ServiceException;

    // 推送消息
    MessagePushRespDto pushMessageInConsultChat(ConsultLandingPageDto config , MessagePushDto messagePushDto) throws ServiceException;

}
