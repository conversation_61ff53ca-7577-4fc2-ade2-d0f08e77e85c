package com.bilibili.mgk.platform.portal.service;

import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDto;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import com.google.common.collect.Lists;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * @file: WebHotAdsServiceTest
 * @author: gaoming
 * @date: 2021/01/15
 * @version: 1.0
 * @description:
 **/
public class WebHotAdsServiceTest extends BaseMockitoTest {


    @InjectMocks
    private WebHotAdsService webHotAdsService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testConvertListDtos2Vos() {
        HotAdsDto hotAdsDto = BeanTestUtils.initSimpleFields(HotAdsDto.builder().build());
        webHotAdsService.convertListDtos2Vos(Lists.newArrayList(hotAdsDto));
    }

    @Test
    public void testConvertListDto2Vo() {
        HotAdsDto hotAdsDto = BeanTestUtils.initSimpleFields(HotAdsDto.builder().build());
        webHotAdsService.convertListDto2Vo(hotAdsDto);
    }
}