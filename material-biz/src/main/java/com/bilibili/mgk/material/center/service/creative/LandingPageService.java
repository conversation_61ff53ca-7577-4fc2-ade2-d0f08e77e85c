package com.bilibili.mgk.material.center.service.creative;

import com.bilibili.mgk.material.center.config.MaterialQueryProfileConfig;
import com.bilibili.mgk.material.center.config.MaterialRankDescriptionConfig;
import com.bilibili.mgk.material.center.facade.LandingPageFacade;
import com.bilibili.mgk.material.center.service.creative.model.HotLandingPage;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdMandatory;
import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.HotLandingPageQuery;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.favorite.MaterialFavoriteKeyUtils;
import com.bilibili.mgk.material.center.service.favorite.MaterialFavoriteService;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.util.List;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/8
 */
@Slf4j
@Service
public class LandingPageService {

    @Resource
    private LandingPageFacade landingPageFacade;

    @Resource
    private MaterialFavoriteService materialFavoriteService;

    @Resource
    private MaterialRankDescriptionConfig rankDescriptionConfig;
    @Resource
    private MaterialQueryProfileConfig queryProfileConfig;

    @Resource
    private WatermarkService watermarkService;


    public HotLandingPage detail(
            @Nullable Long visitorAccountId,
            String pageId, String commerceCategorySecondId, String promotionPurposeType,
            DateAggregation dayType) {
        return landingPageFacade
                .detail(pageId, commerceCategorySecondId, promotionPurposeType, dayType)
                .map(page -> {
                    this.filling(visitorAccountId, Lists.newArrayList(page));
                    return page;
                })
                .orElseThrow(() -> new RuntimeException("没有对应的热门落地页"));
    }

    public Pagination<List<HotLandingPage>> page(HotLandingPageQuery query) {
        query.validate();

        Pagination<List<HotLandingPage>> page = landingPageFacade.landingPage(query);

        this.filling(query.getVisitorAccountId(), page.getData());

        return page;
    }


    private void filling(Long accountId, List<HotLandingPage> hotLandingPages) {

        Try.run(() -> this.fillingFavoriteStatus(accountId, hotLandingPages))
                .onFailure(t -> {
                    log.error("fillingFavoriteStatus error", t);
                });

        Try.run(() -> this.rankDescriptionConfig.fillingRankDescOfLandingPage(hotLandingPages))
                .onFailure(t -> {
                    log.error("fillingRankDescOfLandingPage error", t);
                });

        Try.run(() -> this.queryProfileConfig.mappingPromotionPurposeOfLandingPage(hotLandingPages))
                .onFailure(t -> {
                    log.error("mappingPromotionPurposeOfLandingPage error", t);
                });

        Try.run(() -> {
            this.watermarkService.maskLandingPageCoverByWatermark(hotLandingPages);
        }).onFailure(t -> {
            log.error("maskLandingPageCoverByWatermark error", t);
        });

    }


    private void fillingFavoriteStatus(Long accountId, List<HotLandingPage> hotLandingPages) {

        hotLandingPages.forEach(page -> {

            page.setFavoriteKey(MaterialFavoriteKeyUtils.generateId(
                    new MaterialIdMandatory()
                            .setMaterialType(MaterialType.landing_page)
                            .setMaterialUk(Joiner.on("_").join(
                                    page.getPageId(),
                                    page.getPromotionPurposeType(),
                                    page.getCommerceCategorySecondId())
                            )
            ));
        });

        materialFavoriteService.fillingFavoriteStatus(
                accountId,
                hotLandingPages,
                HotLandingPage::getFavoriteKey,
                HotLandingPage::setFavorite
        );

    }


}
