package com.bilibili.mgk.platform.portal.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.api.service.ICollageService;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.mgk.platform.common.CollageLayerEnum;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import com.bilibili.mgk.platform.portal.util.ImageUtils;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.*;
import com.bilibili.mgk.platform.portal.webapi.landing_page.vo.ImageResolution;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/09/15
 **/
public class WebCollageServiceTest extends BaseMockitoTest {

    @InjectMocks
    private WebCollageService webCollageService;

    @Mock
    private ICollageSizeService collageSizeService;

    @Mock
    private ICollageService collageService;

    private List<LayerDto> layerDtos;

    private PatternDto patternDto;

    private CollageSizeDto collageSizeDto;

    private CollageFontLibraryDto collageFontLibraryDto;

    private CollageMediaDto collageMediaDto;

    private CollageMaterialDto collageMaterialDto;

    private CollageCoverDto cover;

    @Before
    public void setUp() throws Exception {

        cover = CollageCoverDto.builder()
                .coverMd5("md5")
                .coverSize(1)
                .coverUrl("http://www.bilibili.com")
                .ratio(1)
                .height(1)
                .width(1)
                .id(1)
                .objType(1)
                .objId(1)
                .coverId(1L)
                .build();

        layerDtos = Lists.newArrayList(LayerDto.builder()
                .worksId(1)
                .textColor(RgbaDto.builder()
                        .alpha(1)
                        .blue(1)
                        .green(1)
                        .opacity(0.1f)
                        .red(1)
                        .build())
                .status(1)
                .creator("SYS_TEST")
                .bgColor(RgbaDto.builder()
                        .alpha(1)
                        .blue(1)
                        .green(1)
                        .opacity(0.1f)
                        .red(1)
                        .build())
                .y(1)
                .x(1)
                .width(1)
                .underline(1)
                .textAlign("")
                .text("")
                .seq(1)
                .rotate(1)
                .patternId(1)
                .name("")
                .linethrough(1)
                .imageUrl("")
                .imageLock(1)
                .id(1)
                .height(1)
                .fontWeight("")
                .fontStyle("")
                .fontSize(1)
                .fontFamilyUrl("")
                .fontFamilyName("")
                .fontFamilyId(1)
                .opacity(0.6f)
                .category(CollageLayerEnum.MAIN_TITLE)
                .build());

        patternDto = PatternDto.builder()
                .patternId(1L)
                .id(1)
                .edition(1)
                .layerDtos(layerDtos)
                .name("")
                .collageSizeId(1)
                .industryIds(new Integer[]{1})
                .tagId(1)
                .creator("")
                .isDeleted(1)
                .mtime(new Timestamp(System.currentTimeMillis()))
                .renderImage("")
                .status(1)
                .tagCode("")
                .height(1)
                .sizeDesc("")
                .width(1)
                .build();

        collageSizeDto = CollageSizeDto.builder()
                .edition(1)
                .desc("")
                .height(1)
                .id(1)
                .sketch("")
                .width(1)
                .isDeleted(IsDeleted.VALID.getCode())
                .status(1)
                .build();

        collageFontLibraryDto = CollageFontLibraryDto.builder()
                .name("")
                .url("")
                .id(1)
                .status(1)
                .build();

        collageMediaDto = CollageMediaDto.builder()
                .mtime(new Timestamp(System.currentTimeMillis()))
                .ctime(new Timestamp(System.currentTimeMillis()))
                .mediaRatio(1)
                .width(1)
                .height(1)
                .mediaType(1)
                .mediaUrl("")
                .patternId(1)
                .worksId(1)
                .mediaName("")
                .accountId(10005)
                .id(1)
                .isDeleted(IsDeleted.VALID.getCode())
                .mediaId(1L)
                .mediaOrigin(1)
                .mediaMd5("md5")
                .mediaSize(1L)
                .totalDuration(1)
                .durationPerFrame(1)
                .frames(1)
                .roundsNumber(1)
                .cover(cover)
                .build();

        collageMaterialDto = CollageMaterialDto.builder()
                .mid(1)
                .url("")
                .id(1)
                .build();
    }


    @Test
    public void testMultipartToFile() throws IOException {
        MultipartFile multipartFile = mock(MultipartFile.class);
        webCollageService.multipartToFile(multipartFile);
    }

    @Test
    public void testVo2dto() {
        webCollageService.vo2dto(QuerySizeVo.builder()
                .build());
    }

    @Test
    public void testTestVo2dto() {
        webCollageService.vo2dto(QueryPatternVo.builder()
                .collage_size_ids("1,2,3")
                .industry_ids("1,2,3")
                .build());
    }

    @Test
    public void testTestVo2dto1() {
        webCollageService.vo2dto(CreatePatternVo.builder()
                .layers(Lists.newArrayList(LayerVo.builder()
                        .category(1)
                        .build()))
                .industry_ids("1,2,3")
                .build());
    }

    @Test
    public void testTestVo2dto2() {
        webCollageService.vo2dto(1, CollagePatternBaseVo.builder()
                .pattern_cover("123")
                .name("name")
                .industry_ids("1,2,3")
                .build());
    }

    @Test
    public void testPatternDto2vo() {
        webCollageService.patternDto2vo(Lists.newArrayList(patternDto));
    }

    @Test
    public void testTestPatternDto2vo() {
        webCollageService.patternDto2vo(patternDto);
    }

    @Test
    public void testLayerDto2vo() {
        webCollageService.layerDto2vo(layerDtos);
    }

    @Test
    public void testSizeDto2vo() {
        webCollageService.sizeDto2vo(Lists.newArrayList(collageSizeDto));
    }

    @Test
    public void testDto2vo() {
        webCollageService.dto2vo(collageSizeDto);
    }

    @Test
    public void testFontLibraryDto2vo() {
        webCollageService.fontLibraryDto2vo(Lists.newArrayList(collageFontLibraryDto));
    }

    @Test
    public void testTestDto2vo() {
        webCollageService.dto2vo(collageFontLibraryDto);
    }

    @Test
    public void testGetFontLibraryDto() {
        webCollageService.getFontLibraryDto("", "", 1);
    }

    @Test
    public void testTestGetFontLibraryDto() {
        webCollageService.getFontLibraryDto("");
    }

    @Test
    public void testTestVo2dto3() {
        webCollageService.vo2dto(QueryFontLibraryVo.builder().build());
    }

    @Test
    public void testIndustryEnum2DropBox() {
        webCollageService.industryEnum2DropBox();
    }

    @Test
    public void testCollageLayerCategory2vo() {
        webCollageService.collageLayerCategory2vo();
    }

    @Test
    public void testTestPatternDto2vo1() {
        Map<Integer, CollageSizeDto> sizeMap = new HashMap<>();
        sizeMap.put(1, collageSizeDto);
        webCollageService.patternDto2vo(patternDto, sizeMap);
    }

    @Test
    public void testIsImageType() {
        MultipartFile multipartFile = mock(MultipartFile.class);
        webCollageService.isImageType(multipartFile);
    }

    @Test
    public void testUpload() throws IOException, ServiceException {
        MultipartFile multipartFile = mock(MultipartFile.class);
        webCollageService.upload(multipartFile);
    }

    @Test
    public void testConvertDto2Vo() {
        webCollageService.convertDto2Vo(collageMaterialDto);
    }

    @Test
    public void testTestVo2dto4() {
        webCollageService.vo2dto(CreateSizeVo.builder().build());
    }

    @Test
    public void testTestVo2dto5() {
        webCollageService.vo2dto(1, QueryCollageMediaVo.builder().build());
    }

    @Test
    public void testTestDto2vo1() {
        webCollageService.dto2vo(Lists.newArrayList(collageMediaDto));
    }

    @Test
    public void testTestVo2dto6() {
        webCollageService.vo2dto(collageMediaDto);
    }

    @Test
    public void testWorksVo2Dto() {
        webCollageService.worksVo2Dto(CreateWorksVo.builder()
                .collage_size_id(1)
                .layers(Lists.newArrayList(LayerVo.builder()
                        .id(1)
                        .category(1)
                        .build()))
                .build());
    }

    @Test
    public void testQueryVo2Dto() {
        webCollageService.queryVo2Dto(operator, QueryWorksVo.builder().build());
    }

    @Test
    public void testConvertWorksDtos2Vos() {
        webCollageService.convertWorksDtos2Vos(Lists.newArrayList(CollageWorksDto.builder()
                .layerDtos(layerDtos)
                .build()));
    }

    @Test
    public void testConvertWorksDto2Vo() {
        webCollageService.convertWorksDto2Vo(CollageWorksDto.builder()
                .layerDtos(layerDtos)
                .build());
    }

    @Test
    public void testWorksEditVo2Dto() {
        webCollageService.worksEditVo2Dto(1, CollageEditLayerVo.builder()
                .layers(Lists.newArrayList(LayerVo.builder()
                        .category(1)
                        .build()))
                .build());
    }

    @Test
    public void testConvertMediaVo2Dto() {
        webCollageService.convertMediaVo2Dto(CollageMediaVo.builder()
                .build());
    }

    @Test
    public void testConvertMediaVos2Dtos() {
        webCollageService.convertMediaVo2Dto(CollageMediaVo.builder().build());
    }

    @Test
    public void testWorksNoLayersVo2Dto() {
        webCollageService.worksNoLayersVo2Dto(CreateWorksNoLayersVo.builder()
                .collage_size_id(1)
                .cover(CollageCoverVo.builder()
                        .id(1)
                        .cover_url("http://www.bilibili.com")
                        .cover_size(1)
                        .cover_md5("md5")
                        .height(1)
                        .ratio(1)
                        .width(1)
                        .build())
                .duration_perFrame(1)
                .frames(1)
                .name("name")
                .rounds_number(1)
                .total_duration(1)
                .works_md5("md5")
                .works_size(1L)
                .works_url("http://www.bilibili.com")
                .build());
    }

    @Test
    public void testEditNoLayersVo2Dto() {
        webCollageService.editNoLayersVo2Dto(EditWorksNoLayersVo.builder()
                .cover(CollageCoverVo.builder()
                        .width(1)
                        .ratio(1)
                        .height(1)
                        .cover_md5("md5")
                        .cover_size(1)
                        .cover_url("http://www.bilibili.com")
                        .id(1)
                        .build())
                .duration_perFrame(1)
                .frames(1)
                .id(1)
                .rounds_number(1)
                .total_duration(1)
                .works_md5("md5")
                .works_size(1L)
                .works_url("http://www.bilibili.com")
                .build());
    }

    @Test
    public void testConvertWorksNoLayersDto2Vo() {
        webCollageService.convertWorksNoLayersDto2Vo(CollageWorksDto.builder()
                .worksSize(1L)
                .worksMd5("md5")
                .worksRadio(1)
                .worksOrigin(1)
                .roundsNumber(1)
                .frames(1)
                .durationPerFrame(1)
                .totalDuration(1)
                .patternId(1)
                .isSynchro(1)
                .accountId(1)
                .renderImage("http://www.bilibili.com")
                .collageSizeId(1)
                .name("名称")
                .id(1)
                .cover(CollageCoverDto.builder()
                        .coverId(1L)
                        .objId(1)
                        .objType(1)
                        .id(1)
                        .width(1)
                        .height(1)
                        .ratio(1)
                        .coverUrl("http://www.bilibili.com")
                        .coverSize(1)
                        .coverMd5("md5")
                        .build())
                .height(1)
                .sizeDesc("desc")
                .width(1)
                .build());
    }


    public void testConvertVideoDtos2Vos() {
        webCollageService.convertVideoDtos2Vos(Lists.newArrayList(CollageEnterpriseVideoDto.builder()
                .duration(1L)
                .width(1L)
                .title("")
                .tid(1)
                .tags("")
                .state(1)
                .rotate(1L)
                .pubTime(1L)
                .height(1L)
                .ctime(1L)
                .cover("")
                .copyright(1)
                .cid(1L)
                .aid(1L)
                .build()));
    }

    @Test
    public void testConvertEnterpriseDtos2Vos() {
        webCollageService.convertEnterpriseDtos2Vos(Lists.newArrayList(CollageEnterpriseDto.builder()
                .name("")
                .mid(1L)
                .build()));
    }

    @Test
    public void testConvertFile2Vo() throws IOException {
//        ImageUtils imageUtils = mock(ImageUtils.class);
//        byte[] content = new byte[2];
//        content[0] = 1;
//        content[1] = 2;
//        MockMultipartFile mockMultipartFile = new MockMultipartFile("java", "java", "image/", content);
//        when(imageUtils.getImageResolution(new FileInputStream("1"))).thenReturn(ImageResolution.builder().width(100).height(100).build());

        String rootPath = this.getClass().getResource("/file.png").getFile().toString();
        File file = new File(rootPath);
        InputStream inputStream = new FileInputStream(file);
//        MultipartFile multipartFile = new MockMultipartFile(file.getName(), inputStream);
        webCollageService.convertFile2Vo(file, "", 4096000);
    }

    @Test
    public void testUploadAndGenalWaterMark() {
        try {
            byte[] data = new byte[100];
            webCollageService.uploadAndGenalWaterMark("", data);
        } catch (Exception e) {
        }

    }

}