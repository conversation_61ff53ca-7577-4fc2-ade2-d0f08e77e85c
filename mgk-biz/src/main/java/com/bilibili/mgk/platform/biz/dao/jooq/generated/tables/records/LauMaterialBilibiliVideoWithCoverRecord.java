/*
 * This file is generated by jOOQ.
 */
package com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.records;


import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.TLauMaterialBilibiliVideoWithCover;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;
import org.jooq.types.ULong;


/**
 * 物料稿件表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauMaterialBilibiliVideoWithCoverRecord extends UpdatableRecordImpl<LauMaterialBilibiliVideoWithCoverRecord> implements Record9<ULong, LocalDateTime, LocalDateTime, String, ULong, String, String, String, ULong> {

    private static final long serialVersionUID = -333954328;

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.id</code>. 自增id
     */
    public void setId(ULong value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.id</code>. 自增id
     */
    public ULong getId() {
        return (ULong) get(0);
    }

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.ctime</code>. 添加时间
     */
    public void setCtime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.ctime</code>. 添加时间
     */
    public LocalDateTime getCtime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.mtime</code>. 修改时间
     */
    public void setMtime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.mtime</code>. 修改时间
     */
    public LocalDateTime getMtime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.material_md5</code>. 物料md5
     */
    public void setMaterialMd5(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.material_md5</code>. 物料md5
     */
    public String getMaterialMd5() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.avid</code>. 视频ID
     */
    public void setAvid(ULong value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.avid</code>. 视频ID
     */
    public ULong getAvid() {
        return (ULong) get(4);
    }

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.cover_url</code>. 封面URL
     */
    public void setCoverUrl(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.cover_url</code>. 封面URL
     */
    public String getCoverUrl() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.cover_md5</code>. 封面MD5
     */
    public void setCoverMd5(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.cover_md5</code>. 封面MD5
     */
    public String getCoverMd5() {
        return (String) get(6);
    }

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.video_md5</code>. avid生成的md5
     */
    public void setVideoMd5(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.video_md5</code>. avid生成的md5
     */
    public String getVideoMd5() {
        return (String) get(7);
    }

    /**
     * Setter for <code>lau_material_bilibili_video_with_cover.cid</code>. 视频CID
     */
    public void setCid(ULong value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_material_bilibili_video_with_cover.cid</code>. 视频CID
     */
    public ULong getCid() {
        return (ULong) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<ULong> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<ULong, LocalDateTime, LocalDateTime, String, ULong, String, String, String, ULong> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<ULong, LocalDateTime, LocalDateTime, String, ULong, String, String, String, ULong> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<ULong> field1() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.CTIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.MTIME;
    }

    @Override
    public Field<String> field4() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.MATERIAL_MD5;
    }

    @Override
    public Field<ULong> field5() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.AVID;
    }

    @Override
    public Field<String> field6() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.COVER_URL;
    }

    @Override
    public Field<String> field7() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.COVER_MD5;
    }

    @Override
    public Field<String> field8() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.VIDEO_MD5;
    }

    @Override
    public Field<ULong> field9() {
        return TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.CID;
    }

    @Override
    public ULong component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCtime();
    }

    @Override
    public LocalDateTime component3() {
        return getMtime();
    }

    @Override
    public String component4() {
        return getMaterialMd5();
    }

    @Override
    public ULong component5() {
        return getAvid();
    }

    @Override
    public String component6() {
        return getCoverUrl();
    }

    @Override
    public String component7() {
        return getCoverMd5();
    }

    @Override
    public String component8() {
        return getVideoMd5();
    }

    @Override
    public ULong component9() {
        return getCid();
    }

    @Override
    public ULong value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCtime();
    }

    @Override
    public LocalDateTime value3() {
        return getMtime();
    }

    @Override
    public String value4() {
        return getMaterialMd5();
    }

    @Override
    public ULong value5() {
        return getAvid();
    }

    @Override
    public String value6() {
        return getCoverUrl();
    }

    @Override
    public String value7() {
        return getCoverMd5();
    }

    @Override
    public String value8() {
        return getVideoMd5();
    }

    @Override
    public ULong value9() {
        return getCid();
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value1(ULong value) {
        setId(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value2(LocalDateTime value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value3(LocalDateTime value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value4(String value) {
        setMaterialMd5(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value5(ULong value) {
        setAvid(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value6(String value) {
        setCoverUrl(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value7(String value) {
        setCoverMd5(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value8(String value) {
        setVideoMd5(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord value9(ULong value) {
        setCid(value);
        return this;
    }

    @Override
    public LauMaterialBilibiliVideoWithCoverRecord values(ULong value1, LocalDateTime value2, LocalDateTime value3, String value4, ULong value5, String value6, String value7, String value8, ULong value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauMaterialBilibiliVideoWithCoverRecord
     */
    public LauMaterialBilibiliVideoWithCoverRecord() {
        super(TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER);
    }

    /**
     * Create a detached, initialised LauMaterialBilibiliVideoWithCoverRecord
     */
    public LauMaterialBilibiliVideoWithCoverRecord(ULong id, LocalDateTime ctime, LocalDateTime mtime, String materialMd5, ULong avid, String coverUrl, String coverMd5, String videoMd5, ULong cid) {
        super(TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER);

        set(0, id);
        set(1, ctime);
        set(2, mtime);
        set(3, materialMd5);
        set(4, avid);
        set(5, coverUrl);
        set(6, coverMd5);
        set(7, videoMd5);
        set(8, cid);
    }
}
