package com.bilibili.collage.biz.service.school.doctree.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/6
 */
@Data
@Accessors(chain = true)
public class SanlianNodeDeleteReq implements SnakeCaseBody {


    @ApiModelProperty("必选，待删除节点id")
    private Long nodeId;

    @ApiModelProperty("可选，是否强制删除，默认否，如果是强制删除子孙节点")
    private Boolean force;


    public void validate() {
    }

}
