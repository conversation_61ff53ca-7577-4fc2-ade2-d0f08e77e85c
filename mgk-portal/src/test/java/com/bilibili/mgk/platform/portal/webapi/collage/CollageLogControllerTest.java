package com.bilibili.mgk.platform.portal.webapi.collage;

import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.collage.api.service.ICollageLogService;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @date 2020/09/15
 **/
public class CollageLogControllerTest extends BaseMockitoTest {

    @InjectMocks
    private CollageLogController collageLogController;

    @Mock
    private ICollageLogService collageLogService;

    Context context;

    @Before
    public void setUp() throws Exception {
        context = new Context();
        context.setAccountId(10005);
        context.setType(OperatorType.SYSTEM.getCode());
        context.setProxyName("SYS_TEST");
        context.setSalesType(SalesType.CPM.getCode());
        context.setUsername("bilibili_user");
        context.setProxyId(1);
    }

    @Test
    public void testGetLogs() throws ServiceException {
        collageLogController.getLogs(context, 1L, 1, 1, 1, System.currentTimeMillis(), System.currentTimeMillis(), 1, 15);
    }
}