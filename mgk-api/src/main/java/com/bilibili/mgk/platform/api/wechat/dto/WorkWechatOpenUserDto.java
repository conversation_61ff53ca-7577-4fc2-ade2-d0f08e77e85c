package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WorkWechatOpenUserDto implements Serializable {


    private static final long serialVersionUID = -9002688969136949255L;
    /**
     * userid
     */
    private String userid;

    /**
     * oper_userid
     */
    private String oper_userid;

    /**
     * 企业信息
     */
    private String state;

    private Long createtime;

    //16-通过获客链接添加
    private Integer add_way;

}
