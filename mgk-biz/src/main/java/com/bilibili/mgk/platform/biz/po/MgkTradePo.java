package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkTradePo implements Serializable {
    /**
     * 自增ID（主键）
     */
    private Integer id;

    /**
     * 父级行业ID
     */
    private Long parentTradeId;

    /**
     * 行业ID
     */
    private Long tradeId;

    /**
     * 行业级别
     */
    private Integer tradeLevel;

    /**
     * 行业名称
     */
    private String tradeName;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}