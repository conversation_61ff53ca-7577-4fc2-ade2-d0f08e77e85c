package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 是否已经发送成功给智齿， 0-未发送 1-已发送成功 2-已发送失败
 * @author: wangbin01
 * @create: 2018-12-04
 **/
@AllArgsConstructor
public enum IsSentEnum {

    NON_SEND(0, "未发送"),

    SEND_SUCCESS(1, "已发送成功"),

    SEND_FAILED(2, "已发送失败");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static IsSentEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("未知发送状态");
        }
        for(IsSentEnum lrt: values()) {
            if(lrt.getCode().equals(code)) {
                return lrt;
            }
        }
        throw new IllegalArgumentException("未知发送状态");
    }
}
