<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.ad.dao.LauLiveGameCardSubInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_id" jdbcType="BIGINT" property="cardId" />
    <result column="card_type" jdbcType="TINYINT" property="cardType" />
    <result column="launch_platform" jdbcType="TINYINT" property="launchPlatform" />
    <result column="download_type" jdbcType="TINYINT" property="downloadType" />
    <result column="apk_id" jdbcType="INTEGER" property="apkId" />
    <result column="apk_button_text" jdbcType="VARCHAR" property="apkButtonText" />
    <result column="game_id" jdbcType="INTEGER" property="gameId" />
    <result column="sub_pkg" jdbcType="TINYINT" property="subPkg" />
    <result column="h5_bg_image" jdbcType="VARCHAR" property="h5BgImage" />
    <result column="h5_apk_description" jdbcType="VARCHAR" property="h5ApkDescription" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, card_id, card_type, launch_platform, download_type, apk_id, apk_button_text, 
    game_id, sub_pkg, h5_bg_image, h5_apk_description, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_live_game_card_sub_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_live_game_card_sub_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_live_game_card_sub_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPoExample">
    delete from lau_live_game_card_sub_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_game_card_sub_info (card_id, card_type, launch_platform, 
      download_type, apk_id, apk_button_text, 
      game_id, sub_pkg, h5_bg_image, 
      h5_apk_description, ctime, mtime, 
      is_deleted)
    values (#{cardId,jdbcType=BIGINT}, #{cardType,jdbcType=TINYINT}, #{launchPlatform,jdbcType=TINYINT}, 
      #{downloadType,jdbcType=TINYINT}, #{apkId,jdbcType=INTEGER}, #{apkButtonText,jdbcType=VARCHAR}, 
      #{gameId,jdbcType=INTEGER}, #{subPkg,jdbcType=TINYINT}, #{h5BgImage,jdbcType=VARCHAR}, 
      #{h5ApkDescription,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_game_card_sub_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cardId != null">
        card_id,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="launchPlatform != null">
        launch_platform,
      </if>
      <if test="downloadType != null">
        download_type,
      </if>
      <if test="apkId != null">
        apk_id,
      </if>
      <if test="apkButtonText != null">
        apk_button_text,
      </if>
      <if test="gameId != null">
        game_id,
      </if>
      <if test="subPkg != null">
        sub_pkg,
      </if>
      <if test="h5BgImage != null">
        h5_bg_image,
      </if>
      <if test="h5ApkDescription != null">
        h5_apk_description,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cardId != null">
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=TINYINT},
      </if>
      <if test="launchPlatform != null">
        #{launchPlatform,jdbcType=TINYINT},
      </if>
      <if test="downloadType != null">
        #{downloadType,jdbcType=TINYINT},
      </if>
      <if test="apkId != null">
        #{apkId,jdbcType=INTEGER},
      </if>
      <if test="apkButtonText != null">
        #{apkButtonText,jdbcType=VARCHAR},
      </if>
      <if test="gameId != null">
        #{gameId,jdbcType=INTEGER},
      </if>
      <if test="subPkg != null">
        #{subPkg,jdbcType=TINYINT},
      </if>
      <if test="h5BgImage != null">
        #{h5BgImage,jdbcType=VARCHAR},
      </if>
      <if test="h5ApkDescription != null">
        #{h5ApkDescription,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPoExample" resultType="java.lang.Long">
    select count(*) from lau_live_game_card_sub_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_live_game_card_sub_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardId != null">
        card_id = #{record.cardId,jdbcType=BIGINT},
      </if>
      <if test="record.cardType != null">
        card_type = #{record.cardType,jdbcType=TINYINT},
      </if>
      <if test="record.launchPlatform != null">
        launch_platform = #{record.launchPlatform,jdbcType=TINYINT},
      </if>
      <if test="record.downloadType != null">
        download_type = #{record.downloadType,jdbcType=TINYINT},
      </if>
      <if test="record.apkId != null">
        apk_id = #{record.apkId,jdbcType=INTEGER},
      </if>
      <if test="record.apkButtonText != null">
        apk_button_text = #{record.apkButtonText,jdbcType=VARCHAR},
      </if>
      <if test="record.gameId != null">
        game_id = #{record.gameId,jdbcType=INTEGER},
      </if>
      <if test="record.subPkg != null">
        sub_pkg = #{record.subPkg,jdbcType=TINYINT},
      </if>
      <if test="record.h5BgImage != null">
        h5_bg_image = #{record.h5BgImage,jdbcType=VARCHAR},
      </if>
      <if test="record.h5ApkDescription != null">
        h5_apk_description = #{record.h5ApkDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_live_game_card_sub_info
    set id = #{record.id,jdbcType=BIGINT},
      card_id = #{record.cardId,jdbcType=BIGINT},
      card_type = #{record.cardType,jdbcType=TINYINT},
      launch_platform = #{record.launchPlatform,jdbcType=TINYINT},
      download_type = #{record.downloadType,jdbcType=TINYINT},
      apk_id = #{record.apkId,jdbcType=INTEGER},
      apk_button_text = #{record.apkButtonText,jdbcType=VARCHAR},
      game_id = #{record.gameId,jdbcType=INTEGER},
      sub_pkg = #{record.subPkg,jdbcType=TINYINT},
      h5_bg_image = #{record.h5BgImage,jdbcType=VARCHAR},
      h5_apk_description = #{record.h5ApkDescription,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPo">
    update lau_live_game_card_sub_info
    <set>
      <if test="cardId != null">
        card_id = #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=TINYINT},
      </if>
      <if test="launchPlatform != null">
        launch_platform = #{launchPlatform,jdbcType=TINYINT},
      </if>
      <if test="downloadType != null">
        download_type = #{downloadType,jdbcType=TINYINT},
      </if>
      <if test="apkId != null">
        apk_id = #{apkId,jdbcType=INTEGER},
      </if>
      <if test="apkButtonText != null">
        apk_button_text = #{apkButtonText,jdbcType=VARCHAR},
      </if>
      <if test="gameId != null">
        game_id = #{gameId,jdbcType=INTEGER},
      </if>
      <if test="subPkg != null">
        sub_pkg = #{subPkg,jdbcType=TINYINT},
      </if>
      <if test="h5BgImage != null">
        h5_bg_image = #{h5BgImage,jdbcType=VARCHAR},
      </if>
      <if test="h5ApkDescription != null">
        h5_apk_description = #{h5ApkDescription,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPo">
    update lau_live_game_card_sub_info
    set card_id = #{cardId,jdbcType=BIGINT},
      card_type = #{cardType,jdbcType=TINYINT},
      launch_platform = #{launchPlatform,jdbcType=TINYINT},
      download_type = #{downloadType,jdbcType=TINYINT},
      apk_id = #{apkId,jdbcType=INTEGER},
      apk_button_text = #{apkButtonText,jdbcType=VARCHAR},
      game_id = #{gameId,jdbcType=INTEGER},
      sub_pkg = #{subPkg,jdbcType=TINYINT},
      h5_bg_image = #{h5BgImage,jdbcType=VARCHAR},
      h5_apk_description = #{h5ApkDescription,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_game_card_sub_info (card_id, card_type, launch_platform, 
      download_type, apk_id, apk_button_text, 
      game_id, sub_pkg, h5_bg_image, 
      h5_apk_description, ctime, mtime, 
      is_deleted)
    values (#{cardId,jdbcType=BIGINT}, #{cardType,jdbcType=TINYINT}, #{launchPlatform,jdbcType=TINYINT}, 
      #{downloadType,jdbcType=TINYINT}, #{apkId,jdbcType=INTEGER}, #{apkButtonText,jdbcType=VARCHAR}, 
      #{gameId,jdbcType=INTEGER}, #{subPkg,jdbcType=TINYINT}, #{h5BgImage,jdbcType=VARCHAR}, 
      #{h5ApkDescription,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      card_id = values(card_id),
      card_type = values(card_type),
      launch_platform = values(launch_platform),
      download_type = values(download_type),
      apk_id = values(apk_id),
      apk_button_text = values(apk_button_text),
      game_id = values(game_id),
      sub_pkg = values(sub_pkg),
      h5_bg_image = values(h5_bg_image),
      h5_apk_description = values(h5_apk_description),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_live_game_card_sub_info
      (card_id,card_type,launch_platform,download_type,apk_id,apk_button_text,game_id,sub_pkg,h5_bg_image,h5_apk_description,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.cardId,jdbcType=BIGINT},
        #{item.cardType,jdbcType=TINYINT},
        #{item.launchPlatform,jdbcType=TINYINT},
        #{item.downloadType,jdbcType=TINYINT},
        #{item.apkId,jdbcType=INTEGER},
        #{item.apkButtonText,jdbcType=VARCHAR},
        #{item.gameId,jdbcType=INTEGER},
        #{item.subPkg,jdbcType=TINYINT},
        #{item.h5BgImage,jdbcType=VARCHAR},
        #{item.h5ApkDescription,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_live_game_card_sub_info
      (card_id,card_type,launch_platform,download_type,apk_id,apk_button_text,game_id,sub_pkg,h5_bg_image,h5_apk_description,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.cardId,jdbcType=BIGINT},
        #{item.cardType,jdbcType=TINYINT},
        #{item.launchPlatform,jdbcType=TINYINT},
        #{item.downloadType,jdbcType=TINYINT},
        #{item.apkId,jdbcType=INTEGER},
        #{item.apkButtonText,jdbcType=VARCHAR},
        #{item.gameId,jdbcType=INTEGER},
        #{item.subPkg,jdbcType=TINYINT},
        #{item.h5BgImage,jdbcType=VARCHAR},
        #{item.h5ApkDescription,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      card_id = values(card_id),
      card_type = values(card_type),
      launch_platform = values(launch_platform),
      download_type = values(download_type),
      apk_id = values(apk_id),
      apk_button_text = values(apk_button_text),
      game_id = values(game_id),
      sub_pkg = values(sub_pkg),
      h5_bg_image = values(h5_bg_image),
      h5_apk_description = values(h5_apk_description),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_game_card_sub_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cardId != null">
        card_id,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="launchPlatform != null">
        launch_platform,
      </if>
      <if test="downloadType != null">
        download_type,
      </if>
      <if test="apkId != null">
        apk_id,
      </if>
      <if test="apkButtonText != null">
        apk_button_text,
      </if>
      <if test="gameId != null">
        game_id,
      </if>
      <if test="subPkg != null">
        sub_pkg,
      </if>
      <if test="h5BgImage != null">
        h5_bg_image,
      </if>
      <if test="h5ApkDescription != null">
        h5_apk_description,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cardId != null">
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=TINYINT},
      </if>
      <if test="launchPlatform != null">
        #{launchPlatform,jdbcType=TINYINT},
      </if>
      <if test="downloadType != null">
        #{downloadType,jdbcType=TINYINT},
      </if>
      <if test="apkId != null">
        #{apkId,jdbcType=INTEGER},
      </if>
      <if test="apkButtonText != null">
        #{apkButtonText,jdbcType=VARCHAR},
      </if>
      <if test="gameId != null">
        #{gameId,jdbcType=INTEGER},
      </if>
      <if test="subPkg != null">
        #{subPkg,jdbcType=TINYINT},
      </if>
      <if test="h5BgImage != null">
        #{h5BgImage,jdbcType=VARCHAR},
      </if>
      <if test="h5ApkDescription != null">
        #{h5ApkDescription,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="cardId != null">
        card_id = values(card_id),
      </if>
      <if test="cardType != null">
        card_type = values(card_type),
      </if>
      <if test="launchPlatform != null">
        launch_platform = values(launch_platform),
      </if>
      <if test="downloadType != null">
        download_type = values(download_type),
      </if>
      <if test="apkId != null">
        apk_id = values(apk_id),
      </if>
      <if test="apkButtonText != null">
        apk_button_text = values(apk_button_text),
      </if>
      <if test="gameId != null">
        game_id = values(game_id),
      </if>
      <if test="subPkg != null">
        sub_pkg = values(sub_pkg),
      </if>
      <if test="h5BgImage != null">
        h5_bg_image = values(h5_bg_image),
      </if>
      <if test="h5ApkDescription != null">
        h5_apk_description = values(h5_apk_description),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>