package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.mgk.platform.api.form.service.IMgkFormCustomizeService;
import com.bilibili.mgk.platform.biz.dao.MgkFormCustomizeAuthorityDao;
import com.bilibili.mgk.platform.biz.dao.MgkFormDao;
import com.bilibili.mgk.platform.biz.po.MgkFormCustomizeAuthorityPo;
import com.bilibili.mgk.platform.biz.po.MgkFormCustomizeAuthorityPoExample;
import com.bilibili.mgk.platform.biz.po.MgkFormPo;
import com.bilibili.mgk.platform.biz.po.MgkFormPoExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName MgkCustomizeFormServiceImpl
 * <AUTHOR>
 * @Date 2022/5/8 11:46 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkCustomizeFormServiceImpl implements IMgkFormCustomizeService {

    @Autowired
    private MgkFormCustomizeAuthorityDao mgkFormCustomizeAuthorityDao;
    @Autowired
    private MgkFormDao mgkFormDao;

    @Override
    public void addCustomizeFormAuthority(Integer accountId, Long formId) {
        MgkFormCustomizeAuthorityPo po = MgkFormCustomizeAuthorityPo.builder()
                .accountId(accountId)
                .formId(formId)
                .isDeleted(IsDeleted.VALID.getCode())
                .build();

        // 查询对应关系是否已经存在
        MgkFormCustomizeAuthorityPoExample exm = new MgkFormCustomizeAuthorityPoExample();
        exm.or().andAccountIdEqualTo(accountId)
                .andFormIdEqualTo(formId);
        List<MgkFormCustomizeAuthorityPo> existPos = mgkFormCustomizeAuthorityDao.selectByExample(exm);
        if (!CollectionUtils.isEmpty(existPos)) {
            MgkFormCustomizeAuthorityPo existPo = existPos.get(0);
            if (IsDeleted.DELETED.getCode() == existPo.getIsDeleted()) {
                po.setId(existPo.getId());
                mgkFormCustomizeAuthorityDao.updateByPrimaryKeySelective(po);
            } else {
                throw new IllegalArgumentException("该定制化表单对应关系已存在, 新增失败");
            }
        }
        mgkFormCustomizeAuthorityDao.insertSelective(po);
    }

    @Override
    public void updateCustomizeFormAuthority(Integer accountId, Long oldFormId, Long newFormId) {
        // 移除旧关系,增加新关系
        deleteCustomizeFormAuthority(accountId, oldFormId);
        addCustomizeFormAuthority(accountId, newFormId);
    }

    @Override
    public void deleteCustomizeFormAuthority(Integer accountId, Long formId) {
        MgkFormCustomizeAuthorityPoExample exm = new MgkFormCustomizeAuthorityPoExample();
        exm.or().andAccountIdEqualTo(accountId)
                .andFormIdEqualTo(formId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkFormCustomizeAuthorityPo> existPos = mgkFormCustomizeAuthorityDao.selectByExample(exm);
        if (!CollectionUtils.isEmpty(existPos)) {
            MgkFormCustomizeAuthorityPo existPo = existPos.get(0);
            existPo.setIsDeleted(IsDeleted.DELETED.getCode());
            existPo.setMtime(null);
            mgkFormCustomizeAuthorityDao.updateByPrimaryKeySelective(existPo);
        }
    }

    @Override
    public List<Long> checkFormDataDownloadPermission(Integer accountId) {
        MgkFormCustomizeAuthorityPoExample exm = new MgkFormCustomizeAuthorityPoExample();
        exm.or().andAccountIdEqualTo(accountId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkFormCustomizeAuthorityPo> pos = mgkFormCustomizeAuthorityDao.selectByExample(exm);
        if (!CollectionUtils.isEmpty(pos)) {
            return pos.stream()
                    .map(MgkFormCustomizeAuthorityPo::getFormId)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public void checkFormDataDownloadPermission(Integer accountId, List<Long> formIds) {
        MgkFormPoExample formPoExample = new MgkFormPoExample();
        formPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId).andFormIdIn(formIds);
        List<MgkFormPo> formPos = mgkFormDao.selectByExample(formPoExample);
        if (!CollectionUtils.isEmpty(formPos) && formPos.size() == formIds.size()) {
            return;
        }

        MgkFormCustomizeAuthorityPoExample exm = new MgkFormCustomizeAuthorityPoExample();
        exm.or().andAccountIdEqualTo(accountId).andFormIdIn(formIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkFormCustomizeAuthorityPo> pos = mgkFormCustomizeAuthorityDao.selectByExample(exm);
        if (!CollectionUtils.isEmpty(pos) && pos.size() == formIds.size()) {
            return;
        }
        throw new IllegalArgumentException("您不能操作不属于您的表单");
    }
}
