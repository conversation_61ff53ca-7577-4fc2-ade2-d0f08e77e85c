package com.bilibili.collage.biz.service.school.doctree;


import com.bilibili.collage.biz.config.SchoolDocTreeConfig;
import com.bilibili.collage.biz.service.school.SchoolPojoConvertor;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocNode;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeFavoritePageReq;
import com.biz.common.doc.tree.common.Pagination;
import com.biz.common.doc.tree.model.DocFlattenNode;
import com.biz.common.doc.tree.model.DocNodeFavorite;
import com.biz.common.doc.tree.service.DocNodeFavoriteService;
import com.biz.common.doc.tree.service.DocNodeQueryService;
import com.biz.common.doc.tree.service.vo.FavoriteNodePageReq;
import com.biz.common.doc.tree.service.vo.NodeFavoriteAddReq;
import com.biz.common.doc.tree.service.vo.NodeFavoriteRemoveReq;
import io.vavr.control.Try;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/9
 */
@Slf4j
@Service
public class SanlianSchoolDocTreeFavoriteService {

    @Resource
    private DocNodeQueryService docNodeQueryService;

    @Resource
    private DocNodeFavoriteService docNodeFavoriteService;

    @Resource
    private SchoolDocTreeConfig docTreeConfig;


    public Pagination<List<SanlianDocNode>> listFavorite(SanlianNodeFavoritePageReq req) {

        Pagination<List<DocFlattenNode>> page = docNodeFavoriteService.pageFavoriteNodes(
                new FavoriteNodePageReq()
                        .setAccountId(req.getAccountId())
                        .setPn(req.getPn())
                        .setPs(req.getPs())
                        .setRetrieveDoc(false)
                        .setIsDeleted(false)
                        .setIsShow(true)
                        .setRootId(Try.of(() -> {
                            return req.getTreeType()
                                    .stream()
                                    .map(tr -> docTreeConfig.findRootIdBySchoolTreeType(tr))
                                    .collect(Collectors.toList());
                        }).getOrElse(docTreeConfig.fetchDocRootIds()))

        );

        return page.map(list -> {

            return list.stream()
                    .map(node -> {
                        return SchoolPojoConvertor.convertor.docNode2SchoolDocNode(node)
                                .setTreeType(docTreeConfig.findTreeTypeByRootId(node.findRootIdIfZeroUseNodeId()));
                    })
                    .collect(Collectors.toList());
        });
    }


    public DocNodeFavorite addFavorite(String accountId, Long nodeId) {

        DocNodeFavorite r = docNodeFavoriteService.add(
                new NodeFavoriteAddReq()
                        .setAccountId(accountId)
                        .setNodeId(nodeId)
        );

        return r;
    }

    public DocNodeFavorite removeFavorite(String accountId, Long nodeId) {

        DocNodeFavorite r = docNodeFavoriteService.remove(
                (NodeFavoriteRemoveReq) new NodeFavoriteRemoveReq()
                        .setAccountId(accountId)
                        .setNodeId(nodeId)
        );

        return r;
    }

}
