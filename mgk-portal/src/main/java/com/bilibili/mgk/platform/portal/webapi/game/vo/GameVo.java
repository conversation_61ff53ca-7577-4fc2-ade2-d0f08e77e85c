package com.bilibili.mgk.platform.portal.webapi.game.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/21 21:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GameVo implements Serializable {
    private static final long serialVersionUID = -3989255105387908218L;
    @ApiModelProperty(notes = "游戏唯一标识ID")
    private Integer gameBaseId;
    @ApiModelProperty(notes = "游戏名")
    private String gameName;
    @ApiModelProperty(notes = "游戏icon")
    private String gameIcon;
    @ApiModelProperty(notes = "开放平台游戏状态")
    private Integer gameState;
    @ApiModelProperty(notes = "开放平台游戏状态描述")
    private String gameStateDesc;
    @ApiModelProperty(notes = "游戏中心游戏状态")
    private Integer gameStatus;
    @ApiModelProperty(notes = "游戏中心游戏状态描述")
    private String gameStatusDesc;
    @ApiModelProperty(notes = "是否上下架")
    private Integer onLine;
    @ApiModelProperty(notes = "是否上下架描述")
    private String onLineDesc;
    @ApiModelProperty("分包属性。1：广告包，0：普通包")
    private Integer channelId;
}
