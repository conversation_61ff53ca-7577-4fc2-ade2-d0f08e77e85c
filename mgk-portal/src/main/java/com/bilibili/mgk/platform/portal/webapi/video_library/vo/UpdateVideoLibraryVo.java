package com.bilibili.mgk.platform.portal.webapi.video_library.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/3/12
 * 视频库
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UpdateVideoLibraryVo {

    @ApiModelProperty(value = "视频id")
    private Integer id;

    @ApiModelProperty(value = "视频名称")
    @NotBlank(message = "视频名称不可为空")
    private String name;

    @ApiModelProperty(value = "视频bizId")
    @NotNull(message = "视频业务ID不可为空")
    private Integer biz_id;

    @ApiModelProperty("自定义视频封面")
    private VideoCoverVo videoCover;

    @ApiModelProperty("视频标签")
    private MgkVideoTagVo videoTag;
}
