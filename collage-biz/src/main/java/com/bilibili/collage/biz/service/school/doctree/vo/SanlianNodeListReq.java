package com.bilibili.collage.biz.service.school.doctree.vo;

import com.bilibili.collage.biz.service.school.doctree.model.SanlianSchoolNodeType;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianSchoolTreeType;
import com.biz.common.doc.tree.common.SnakeCaseBody;
import com.biz.common.doc.tree.service.vo.NodeSortType;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/9
 */
@Data
@Accessors(chain = true)
public class SanlianNodeListReq implements SnakeCaseBody {

    @ApiModelProperty(value = "必选，树类型，product_manual:产品说明中心,ad_guidance:行业投放指南,common_question:常见问题,"
            + "commercial_info:商业信息,latest_info:最新动态")
    private SanlianSchoolTreeType treeType;

    @ApiModelProperty(value = "可选，父节点id")
    private Long nodeId;

    @ApiModelProperty(value = "忽略，登录态账号id")
    private String accountId;

    @ApiModelProperty(value = "可选，深度策略，depth_eq:深度等于； depth_lte:深度小于等于；默认为depth_eq")
    private String depthStrategy;

    @ApiModelProperty(value = "可选，指定节点深度，当策略为depth_eq时，默认为1， 当策略为depth_lte时，默认为空，表示可以穷举所有子节点")
    private Integer depth;

    /**
     * 深入不限，但是先
     */
    @ApiModelProperty(value = "可选，过滤目标节点类型，不传则表示全部；"
            + "product_manual:产品手册目录,manual_article:产品手册文章,"
            + "ad_guide_dir:广告指导目录,ad_guide_article:广告指导文章,"
            + "common_question_link:常见问题目录,"
            + "commercial_info_link:商业资讯,"
            + "latest_info_link:平台动态，最近更新")
    private List<SanlianSchoolNodeType> nodeTypes;

    @ApiModelProperty(value = "可选，是否过滤删除属性， 默认空，表示全部")
    private Boolean isDeleted;

    @ApiModelProperty(value = "可选，是否过滤显示属性， 默认空，表示全部")
    private Boolean isShow;

    @ApiModelProperty(value = "可选，排序方式，包括"
            + "ctime_asc:创建时间升序，ctime_desc:创建时间降序，"
            + "mtime_asc:修改时间升序，mtime_desc:修改时间降序，"
            + "priority_asc:优先级升序，priority_desc:优先级降序， 默认排序为priority_asc")
    private NodeSortType sort;

    public void validate() {

        Assert.notNull(treeType, "treeType can not be null");


    }


    public List<String> fetchNodeTypesAsStringList() {

        return Optional.ofNullable(nodeTypes)
                .map(list -> list.stream().map(SanlianSchoolNodeType::name).collect(Collectors.toList()))
                .orElse(null);
    }

}
