package com.bilibili.mgk.material.center.repository.mysql;


import com.bilibili.mgk.material.center.service.agreement.model.AgreementPermission;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【mgk_agreement_permission(协议授权表)】的数据库操作Mapper
 * @createDate 2024-09-03 20:00:05
 * @Entity .domain.MgkAgreementPermission
 */
public interface MgkAgreementPermissionMapper {

    int insertSelective(AgreementPermission record);

    int updateByPrimaryKeySelective(AgreementPermission record);

    AgreementPermission selectByAgreementIdAndPermissionKey(
            @Param("agreementId") Long agreementId,
            @Param("permissionKey") String permissionKey
    );


}
