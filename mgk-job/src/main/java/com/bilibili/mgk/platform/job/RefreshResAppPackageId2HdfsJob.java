package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.app_package.service.IResAppPackageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName RefreshAppPackageResId2HdfsJob
 * <AUTHOR>
 * @Date 2021/12/27 8:32 下午
 * @Version 1.0
 **/
@Slf4j
@Component
@JobHandler("RefreshAppPackageResId2HdfsJob")
public class RefreshResAppPackageId2HdfsJob extends IJobHandler {

    @Autowired
    private IResAppPackageService resAppPackageService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("start execute refresh res app package id job");
        try {
            resAppPackageService.uploadResAppPackageId2Hdfs();
        } catch (Exception ex) {
            log.error("execute refresh res app package id job failed, exception :{}", ex.getMessage());
        }
        log.info("end execute refresh res app package id job");
        return SUCCESS;
    }
}
