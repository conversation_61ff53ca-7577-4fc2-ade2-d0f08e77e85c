package com.bilibili.mgk.platform.biz.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.cpt.platform.common.LogFlag;
import com.bilibili.cpt.platform.common.LogProperty;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.mgk.platform.api.video_library.dto.MgkVideoLogOperationDto;
import com.bilibili.mgk.platform.api.video_library.service.IMgkVideoLogService;
import com.bilibili.mgk.platform.biz.component.ClasspathPackageScanner;
import com.bilibili.mgk.platform.biz.dao.MgkVideoLogOperationDao;
import com.bilibili.mgk.platform.biz.po.MgkVideoLogOperationPo;
import com.bilibili.mgk.platform.biz.po.MgkVideoLogOperationPoExample;
import com.bilibili.mgk.platform.common.video_library.MgkVideoLogFlag;
import com.bilibili.mgk.platform.common.video_library.MgkVideoLogOperateType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/06/02
 */
@Slf4j
@Service
public class MgkVideoLogServiceImpl implements IMgkVideoLogService {

    private final static String LOG_BEAN_PATH = "com.bilibili.mgk.platform.biz.log";
    private Map<String, Class> logFlag2ClassMap = Maps.newHashMap();

    @Autowired
    private MgkVideoLogOperationDao mgkVideoLogOperationDao;

    @PostConstruct
    public void init() {
        ClasspathPackageScanner scan = new ClasspathPackageScanner(LOG_BEAN_PATH);
        try {
            List<String> nameList = scan.getFullyQualifiedClassNameList();
            for (String name : nameList) {
                if (name.endsWith("LogBeanBuilder")) {
                    continue;
                }
                Class clazz = Class.forName(name);
                if (clazz.isAnnotationPresent(LogFlag.class)) {
                    LogFlag logFlag = (LogFlag) clazz.getAnnotation(LogFlag.class);
                    logFlag2ClassMap.put(logFlag.value(), clazz);
                }
            }
        } catch (ClassNotFoundException | IOException e) {
            log.error("获取LogBean失败", e);
        }
    }

    @Override
    public void insertLog(Integer objId, MgkVideoLogFlag logFlag, MgkVideoLogOperateType operationType, Operator operator, Object value) {
        Assert.notNull(objId, "操作对象ID不可为空");
        Assert.notNull(logFlag, "CPT日志Flag不可为空");
        Assert.notNull(operationType, "操作类型不可为空");
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(value, "操作内容不可为空");
        try {
            MgkVideoLogOperationPo po = new MgkVideoLogOperationPo();
            po.setObjId(Long.valueOf(objId));
            po.setObjFlag(logFlag.getCode());
            po.setOperateType(operationType.getCode());
            po.setOperatorUsername(StringUtils.isEmpty(operator.getBilibiliUserName()) ? operator.getOperatorName() : operator.getBilibiliUserName());
            po.setIp(operator.getIp());
            po.setValue(JSON.toJSONString(value));

            mgkVideoLogOperationDao.insertSelective(po);
        } catch (Exception e) {
            log.error("insertLog.error", e);
        }

    }

    @Override
    public PageResult<MgkVideoLogOperationDto> getLogsByObjId(String objId, MgkVideoLogFlag logFlag, Integer page, Integer size){
        Assert.notNull(page,"页号不能为空");
        Assert.notNull(size,"页大小不能为空");
        Assert.notNull(objId,"操作对象id不能为空");
        Assert.notNull(logFlag,"日志类型不能为空");
        Page pageBean = Page.valueOf(page, size);

        MgkVideoLogOperationPoExample example = new MgkVideoLogOperationPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andObjIdEqualTo(Long.valueOf(objId))
                .andObjFlagEqualTo(logFlag.getCode());
        example.setOrderByClause("ctime desc");

        long total = mgkVideoLogOperationDao.countByExample(example);

        if (total == 0) {
            return PageResult.<MgkVideoLogOperationDto>builder().records(Collections.emptyList()).total(0).build();
        }

        example.setOffset(pageBean.getOffset());
        example.setLimit(pageBean.getLimit());
        List<MgkVideoLogOperationPo> pos = mgkVideoLogOperationDao.selectByExampleWithBLOBs(example);

        return PageResult.<MgkVideoLogOperationDto>builder().records(this.logOperationPos2Dtos(objId, pos)).total((int) total).build();
    }

    private List<MgkVideoLogOperationDto> logOperationPos2Dtos(String objId, List<MgkVideoLogOperationPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        } else {
            List<MgkVideoLogOperationDto> dtos = Lists.newArrayListWithCapacity(pos.size());
            try {
                for (MgkVideoLogOperationPo po : pos) {
                    dtos.add(MgkVideoLogOperationDto.builder()
                            .id(po.getId())
                            .objId(objId)
                            .objFlag(MgkVideoLogFlag.getByCode(po.getObjFlag()).getDesc())
                            .operateType(MgkVideoLogOperateType.getByCode(po.getOperateType()).getDesc())
                            .operatorUsername(po.getOperatorUsername())
                            .ctime(TimeUtils.getTimestamp2String(po.getCtime()))
                            .value(getLogPropertyValue(MgkVideoLogFlag.getByCode(po.getObjFlag()), po.getValue()))
                            .build());
                }
            } catch (Exception e) {
                log.error("日志解析失败", e);
            }
            return dtos;
        }
    }

    private String getLogPropertyValue(MgkVideoLogFlag logFlag, String value) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException, InstantiationException {
        Class clazz = logFlag2ClassMap.get(logFlag.name());
        return getObjectValue(JSON.parseObject(value, clazz));
    }

    private String getObjectValue(Object obj) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        if (obj == null) {
            return "";
        } else {
            Class clazz = obj.getClass();
            Field[] fields = clazz.getDeclaredFields();
            StringBuilder sb = new StringBuilder();
            for (Field field : fields) {
                if (field.isAnnotationPresent(LogProperty.class)) {
                    LogProperty logProperty = field.getAnnotation(LogProperty.class);
                    Method m = clazz.getMethod("get" + getMethodName(field.getName()));
                    Object vo = m.invoke(obj);
                    if (vo != null) {
                        sb.append(logProperty.value() + ": " + vo + "<br/>");
                    }
                }
            }
            return sb.toString();
        }
    }

    private String getMethodName(String fildeName) {
        byte[] items = fildeName.getBytes();
        items[0] = (byte) ((char) items[0] - 'a' + 'A');
        return new String(items);
    }
}
