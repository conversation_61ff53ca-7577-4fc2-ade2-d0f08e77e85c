CREATE TABLE `lau_dynamic_bluelink_resolve_content`
(

#     发布和投放信息
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `deleted`             tinyint(4)    NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',
    `ctime`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `mtime`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `account_id`  int(10) unsigned    NOT NULL DEFAULT '0' COMMENT '首次注册的账号id',
    `customer_id` int(10) unsigned    NOT NULL DEFAULT '0' COMMENT '首次注册的客户id',
    `agent_id`    int(10) unsigned    NOT NULL DEFAULT '0' COMMENT '首次注册的代理商id',
    `campaign_id` int(10)             NOT NULL DEFAULT '0' COMMENT '计划id',
    `unit_id`     int(10)             NOT NULL DEFAULT '0' COMMENT '单元id',
    `creative_id` int(10)             NOT NULL DEFAULT '0' COMMENT '创意id',
    `up_nickname` varchar(256)        NOT NULL DEFAULT '' COMMENT 'up主昵称',
    `up_face`         varchar(500) NOT NULL DEFAULT '' COMMENT 'up主头像',
    `mid`         bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'up主id',
    `bluelink_seq_id`     int(10)       NOT NULL DEFAULT '0' COMMENT '蓝链在正文中的序号id，仅蓝链排序，同一个dynaId下唯一',
    `raw_text`            varchar(200)  NOT NULL DEFAULT '0' COMMENT '蓝链在正文中',
    `text_segment_id`     int(10)       NOT NULL DEFAULT '0' COMMENT '蓝链在正文中片段id， 普通文本和蓝链共同排序',



# 审计信息，进复制grouping表用于同步显示状态
    `status`      tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '0-可见，1-不可见',
    `audit_status`           tinyint(4)          NOT NULL DEFAULT '0' COMMENT '0-审核中 1-审核通过 2-审核拒绝',
    `reason`                 varchar(255)        NOT NULL DEFAULT '' COMMENT '原因（审核拒绝时填写）',


#  移除字段，不再关联稿件，
#     `aid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '稿件id',
#     `archive_status` int(11) NOT NULL DEFAULT '0' COMMENT '稿件状态 0-正常  1-异常',




# 展示的主体不再是评论，移除评论字段
#     `comment_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '评论id',
#     `comment_mid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '评论mid',
#     `comment_text` varchar(1024) NOT NULL DEFAULT '' COMMENT '评论文本',
#     `general_comment_text_ext` varchar(1024) NOT NULL DEFAULT '' COMMENT '普通评论文案扩展(转化文案居中时使用)',
#     `general_comment_text` varchar(1024) NOT NULL DEFAULT '' COMMENT '普通评论文案',
#     `text_location` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0-转化链在前 1-文案在前',


# 改为动态
    `dynamic_id`          varchar(100)  NOT NULL DEFAULT '0' COMMENT '动态id',
    `dynamic_biz_id`  varchar(100) NOT NULL DEFAULT '0' COMMENT '动态蓝链使用的业务id',
    `dynamic_status`       int(11)    NOT NULL DEFAULT '0' COMMENT '动态状态 0-正常 1-异常',
    `dynamic_content`      text COMMENT '动态正文',
    `audit_record_id` bigint(20)   NOT NULL DEFAULT '0' COMMENT '关联的审核记录id',


# 蓝链的转化明细
    `component_type`         tinyint(4)          NOT NULL DEFAULT '0' COMMENT '转化组件类型1-线索2-游戏3-应用4-商品',
    `conversion_url_type`    tinyint(4)          NOT NULL DEFAULT '0' COMMENT '转化链链接(兜底链接)类型，1-三方落地页 5-高能建站落地页',
    `conversion_url_page_id` bigint(20)          NOT NULL DEFAULT '0' COMMENT '转化链链接(兜底链接)高能建站落地页page_id, 针对type = 5 回显用',
    `conversion_url`         varchar(1024)       NOT NULL DEFAULT '' COMMENT '转化链连接(兜底链接)',
    `conversion_url_text`    varchar(1024)       NOT NULL DEFAULT '' COMMENT '转化链文案',
    `conversion_short_url`   varchar(1024)       NOT NULL DEFAULT '' COMMENT '转化链连接短链',
    `game_base_id`           int(10)             NOT NULL DEFAULT '0' COMMENT '游戏id',
    `game_platform_type`     tinyint(4)          NOT NULL DEFAULT '0' COMMENT '游戏平台类型1-安卓 2-IOS',
    `ios_url_type`           tinyint(4)          NOT NULL DEFAULT '0' COMMENT 'ios链接类型，1-三方落地页 5-高能建站落地页',
    `ios_url_page_id`        bigint(20)          NOT NULL DEFAULT '0' COMMENT 'ios链接类型高能建站落地页page_id, 针对type = 5 回显用',
    `ios_url`                varchar(1024)       NOT NULL DEFAULT '' COMMENT 'ios链接(引擎用)',
    `android_url_type`       tinyint(4)          NOT NULL DEFAULT '0' COMMENT 'android链接类型，1-三方落地页 5-高能建站落地页',
    `android_url_page_id`    bigint(20)          NOT NULL DEFAULT '0' COMMENT 'android链接高能建站落地页page_id, 针对type = 5 回显用',
    `android_url`            varchar(1024)       NOT NULL DEFAULT '' COMMENT 'android链接(引擎用)',
    `ios_schema_url`         varchar(1024)       NOT NULL DEFAULT '' COMMENT 'ios唤起schema链接(引擎用)',
    `android_schema_url`     varchar(1024)       NOT NULL DEFAULT '' COMMENT 'android唤起schema链接(引擎用)',
    `trigger_time`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '触发时间',

    `ios_app_package_id`     int(10)             NOT NULL DEFAULT '0' COMMENT 'ios包id',
    `android_app_package_id` int(10)             NOT NULL DEFAULT '0' COMMENT 'android包id',
    `qualification_ids`      varchar(255)        NOT NULL DEFAULT '' COMMENT '资质id',
    `sub_pkg`                tinyint(4)          NOT NULL DEFAULT '0' COMMENT '游戏包: 0-联运包, 1-广告包',
    `clue_type`              tinyint(4)          NOT NULL DEFAULT '0' COMMENT '线索类型 0-落地页 1-评论浮层表单 2-微信浮层',
    `clue_data`              varchar(128)        NOT NULL DEFAULT '' COMMENT '线索',
    `customized_imp_url`     varchar(1024)       NOT NULL DEFAULT '' COMMENT '展示监控链接',
    `customized_click_url`   varchar(1024)       NOT NULL DEFAULT '' COMMENT '点击监控链接',
    `auto_fill_text`         varchar(128)        NOT NULL DEFAULT '' COMMENT '隐私协议名称',
    `auto_fill_link`         varchar(1024)       NOT NULL DEFAULT '' COMMENT '隐私协议链接',
    `product_id`             bigint(20)          NOT NULL DEFAULT '0' COMMENT '商品id',
    `product_short_url`   varchar(1024) NOT NULL DEFAULT '' COMMENT '商品短链',
    `biz_code`               tinyint(4)          NOT NULL DEFAULT '0' COMMENT '业务方代码:0-三连 1-营销助手(个人建站)',
    `contact_type`           tinyint(4)          NOT NULL DEFAULT '0' COMMENT '联系组件类型,用于经营助手：0-其他 1-微信 2-QQ 3-手机号',
    `app_sub_type`           tinyint(4)          NOT NULL DEFAULT '1' COMMENT '应用子类型, 0-应用下载, 1-应用唤起',
    `is_android_app_direct`  tinyint(4)          NOT NULL DEFAULT '0' COMMENT '是否安卓应用商店直投: 0-不直投, 1-直投',

# 影子表信息，待定， 考虑移除
    `content_in_edit`      text COMMENT '影子组件',
    `audit_status_in_edit` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0-审核中 1-审核通过 2-审核拒绝',


    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_dynamic_order` (`dynamic_id`, `bluelink_seq_id`) USING BTREE,
    KEY `idx_mtime` (`mtime`) USING BTREE,
    KEY `idx_account_id` (`account_id`) USING BTREE,
    KEY `idx_audit_record_id` (`audit_record_id`) USING BTREE,
    KEY `idx_clue_type_data` (`clue_type`, `clue_data`) USING BTREE,
    KEY `idx_clue_type_page_id` (`clue_type`, `conversion_url_page_id`) USING BTREE,
    KEY `ix_creative_id` (`creative_id`),
    KEY `idx_dynamic_id` (`dynamic_id`) COMMENT '动态id的索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='动态蓝链解析表组件表';



ALTER TABLE `lau_dynamic_bluelink_resolve_content`
    ADD COLUMN `link_icon`    varchar(1024) default '' not null comment '链接icon',
    ADD COLUMN `capsule_icon` varchar(1024) default '' not null comment '胶囊icon',
    ADD COLUMN `capsule_text` varchar(200)  default '' not null comment '胶囊文案';



