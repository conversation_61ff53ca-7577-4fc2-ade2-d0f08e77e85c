package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/10/28
 **/
@AllArgsConstructor
public enum MgkFormTypeEnum {
    COMMON_FORM(0, "常规表单"),
    FLOAT_FORM(1, "视频浮层表单"),
    STANDARDIZATION_FLOAT_FORM(2, "简版浮层表单模板"),
    COMMENT_FLOAT_FORM(3, "评论浮层表单"),
    VISUAL_FORM(4, "虚拟表单"),

    ;
    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static MgkFormTypeEnum getByCode(int code) {
        for (MgkFormTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkFormTypeEnum " + code);
    }
}
