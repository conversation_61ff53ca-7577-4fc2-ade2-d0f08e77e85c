package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkLandingPageConfigPo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageConfigPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MgkLandingPageConfigDao {
    long countByExample(MgkLandingPageConfigPoExample example);

    int deleteByExample(MgkLandingPageConfigPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(MgkLandingPageConfigPo record);

    int insertSelective(MgkLandingPageConfigPo record);

    List<MgkLandingPageConfigPo> selectByExampleWithBLOBs(MgkLandingPageConfigPoExample example);

    List<MgkLandingPageConfigPo> selectByExample(MgkLandingPageConfigPoExample example);

    MgkLandingPageConfigPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkLandingPageConfigPo record, @Param("example") MgkLandingPageConfigPoExample example);

    int updateByExampleWithBLOBs(@Param("record") MgkLandingPageConfigPo record, @Param("example") MgkLandingPageConfigPoExample example);

    int updateByExample(@Param("record") MgkLandingPageConfigPo record, @Param("example") MgkLandingPageConfigPoExample example);

    int updateByPrimaryKeySelective(MgkLandingPageConfigPo record);

    int updateByPrimaryKeyWithBLOBs(MgkLandingPageConfigPo record);

    int updateByPrimaryKey(MgkLandingPageConfigPo record);
}