package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkLandingPageAvidPo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageAvidPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkLandingPageAvidDao {
    long countByExample(MgkLandingPageAvidPoExample example);

    int deleteByExample(MgkLandingPageAvidPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkLandingPageAvidPo record);

    int insertBatch(List<MgkLandingPageAvidPo> records);

    int insertUpdateBatch(List<MgkLandingPageAvidPo> records);

    int insert(MgkLandingPageAvidPo record);

    int insertUpdateSelective(MgkLandingPageAvidPo record);

    int insertSelective(MgkLandingPageAvidPo record);

    List<MgkLandingPageAvidPo> selectByExample(MgkLandingPageAvidPoExample example);

    MgkLandingPageAvidPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkLandingPageAvidPo record, @Param("example") MgkLandingPageAvidPoExample example);

    int updateByExample(@Param("record") MgkLandingPageAvidPo record, @Param("example") MgkLandingPageAvidPoExample example);

    int updateByPrimaryKeySelective(MgkLandingPageAvidPo record);

    int updateByPrimaryKey(MgkLandingPageAvidPo record);
}