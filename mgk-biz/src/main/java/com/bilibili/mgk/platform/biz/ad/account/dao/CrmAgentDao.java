package com.bilibili.mgk.platform.biz.ad.account.dao;

import com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo;
import com.bilibili.mgk.platform.biz.ad.po.CrmAgentPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface CrmAgentDao {
    long countByExample(CrmAgentPoExample example);

    int deleteByExample(CrmAgentPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmAgentPo record);

    int insertBatch(List<CrmAgentPo> records);

    int insertUpdateBatch(List<CrmAgentPo> records);

    int insert(CrmAgentPo record);

    int insertUpdateSelective(CrmAgentPo record);

    int insertSelective(CrmAgentPo record);

    List<CrmAgentPo> selectByExample(CrmAgentPoExample example);

    CrmAgentPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmAgentPo record, @Param("example") CrmAgentPoExample example);

    int updateByExample(@Param("record") CrmAgentPo record, @Param("example") CrmAgentPoExample example);

    int updateByPrimaryKeySelective(CrmAgentPo record);

    int updateByPrimaryKey(CrmAgentPo record);
}