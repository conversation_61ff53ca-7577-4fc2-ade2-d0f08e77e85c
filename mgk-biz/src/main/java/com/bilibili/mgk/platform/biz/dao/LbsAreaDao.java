package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.LbsAreaPo;
import com.bilibili.mgk.platform.biz.po.LbsAreaPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LbsAreaDao {
    long countByExample(LbsAreaPoExample example);

    int deleteByExample(LbsAreaPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LbsAreaPo record);

    int insertBatch(List<LbsAreaPo> records);

    int insertUpdateBatch(List<LbsAreaPo> records);

    int insert(LbsAreaPo record);

    int insertUpdateSelective(LbsAreaPo record);

    int insertSelective(LbsAreaPo record);

    List<LbsAreaPo> selectByExample(LbsAreaPoExample example);

    LbsAreaPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LbsAreaPo record, @Param("example") LbsAreaPoExample example);

    int updateByExample(@Param("record") LbsAreaPo record, @Param("example") LbsAreaPoExample example);

    int updateByPrimaryKeySelective(LbsAreaPo record);

    int updateByPrimaryKey(LbsAreaPo record);
}