package com.bilibili.mgk.material.center.util;

import io.vavr.control.Try;
import java.io.IOException;
import okhttp3.Interceptor;
import okhttp3.Response;
import pleiades.component.metrics.BiliSimpleCollector;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/14
 */
public class HttpMetricInterceptor implements Interceptor {


    @Override
    public Response intercept(Chain chain) throws IOException {
        long start = System.nanoTime();

        Response r = null;
        try {

            r = chain.proceed(chain.request());

            return r;
        } finally {

            Response finalR = r;
            Try.run(() -> {

                String code = Try.of(() -> {

                    if (finalR == null) {
                        return "500";
                    }

                    return String.valueOf(finalR.code());

                }).getOrElse("500");

                String host = chain.request().url().host();

                String originUrl = chain.request().url().encodedPath();

                double elapsed = BiliSimpleCollector.escapeMillisFromNanos(start, System.nanoTime());

                HttpClientMetrics.HTTP_CLIENT_CODE.inc(originUrl, code, host);
                HttpClientMetrics.HTTP_CLIENT_DURATION.observe(elapsed, originUrl);
                HttpClientMetrics.HTTP_CLIENT_TOTAL.inc(originUrl);

            });


        }
    }
}
