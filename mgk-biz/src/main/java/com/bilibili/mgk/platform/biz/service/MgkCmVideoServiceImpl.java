package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.api.archive.dto.MgkCmVideoDto;
import com.bilibili.mgk.platform.api.archive.service.IMgkCmVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @file: MgkCmVideoServiceImpl
 * @author: gaoming
 * @date: 2021/12/13
 * @version: 1.0
 * @description:
 **/
@Service
public class MgkCmVideoServiceImpl implements IMgkCmVideoService {

    @Autowired
    private MgkCmVideoServiceDelegate cmVideoServiceDelegate;

    @Override
    public List<MgkCmVideoDto> getMgkCmVideoDtosByMd5s(List<String> md5s) {
        return cmVideoServiceDelegate.getMgkCmVideoDtosByMd5s(md5s);
    }
}
