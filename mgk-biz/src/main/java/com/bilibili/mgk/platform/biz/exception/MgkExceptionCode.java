package com.bilibili.mgk.platform.biz.exception;

import com.bilibili.adp.common.exception.IExceptionCode;
import lombok.Getter;

/**
 * @ClassName MgkExceptionCode
 * <AUTHOR>
 * @Date 2022/3/1 9:28 下午
 * @Version 1.0
 **/
public enum MgkExceptionCode implements IExceptionCode {
    ISP_PHONE_INFO_FAIL(-3001001, "运营商手机号信息获取失败"),
    ;

    private String message;
    private Integer code;

    MgkExceptionCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
