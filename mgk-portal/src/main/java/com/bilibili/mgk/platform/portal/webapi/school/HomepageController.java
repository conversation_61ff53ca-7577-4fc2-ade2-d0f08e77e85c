package com.bilibili.mgk.platform.portal.webapi.school;


import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.collage.api.dto.CategoryDto;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocTreeNode;
import com.bilibili.collage.biz.service.school.migrate.SanlianDetailSchoolServiceDocTreeAdaptImpl;
import com.bilibili.mgk.platform.common.enums.school.CategoryTypeEnum;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CategoryVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CommercialInfoVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.SchoolArticleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

@Controller
@RequestMapping("/web_api/v1/school")
@Api(value = "/school", description = "学堂首页相关")
@Slf4j
@Deprecated
@FreeLogin
public class HomepageController extends BasicController {

    @Autowired
    private SanlianDetailSchoolServiceDocTreeAdaptImpl schoolServiceDocTreeAdapt;

    @Autowired
    private SystemType systemType;


    @ApiOperation(value = "产品说明中心列表")
    @RequestMapping(value = "/homepage/product_manual/list", method = RequestMethod.GET)
    @ResponseBody
    @Deprecated
    @FreeLogin
    public Response<List<CategoryVo>> getProductManualList(@ApiIgnore Context context){
        List<CategoryDto> categoryDtoList = schoolServiceDocTreeAdapt.getProductManualIndex(
                CategoryTypeEnum.PRODUCT_MANUAL.getCode(), getOperator(context));
        return Response.SUCCESS(categoryDtoList.stream().map(dto->convertCategoryDto2CategoryVo(dto)).collect(Collectors.toList()));
    }

    @ApiOperation(value = "产品说明中心列表")
    @RequestMapping(value = "/homepage/product_manual/list/v2", method = RequestMethod.GET)
    @ResponseBody
    @FreeLogin
    public Response<List<SanlianDocTreeNode>> getProductManualListV2(@ApiIgnore Context context) {
        List<SanlianDocTreeNode> categoryDtoList = schoolServiceDocTreeAdapt.getArticleIndexV2(
                CategoryTypeEnum.PRODUCT_MANUAL.getCode(), getOperator(context));
        return Response.SUCCESS(categoryDtoList);
    }

    @ApiOperation(value = "行业投放指南列表")
    @RequestMapping(value = "/homepage/advertise_guidance/list", method = RequestMethod.GET)
    @ResponseBody
    @Deprecated
    @FreeLogin
    public Response<List<CategoryVo>> getAdvertiseGuidanceList(@ApiIgnore Context context){
        List<CategoryDto> categoryDtoList = schoolServiceDocTreeAdapt.getAdvertiseGuidance(
                CategoryTypeEnum.ADVERTISE_GUIDANCE.getCode(), getOperator(context));
        return Response.SUCCESS(categoryDtoList.stream().map(dto->convertCategoryDto2CategoryVo(dto)).collect(Collectors.toList()));
    }

    @ApiOperation(value = "行业投放指南列表")
    @RequestMapping(value = "/homepage/advertise_guidance/list/v2", method = RequestMethod.GET)
    @ResponseBody
    @FreeLogin
    public Response<List<SanlianDocTreeNode>> getAdvertiseGuidanceListV2(@ApiIgnore Context context) {
        List<SanlianDocTreeNode> categoryDtoList = schoolServiceDocTreeAdapt.getArticleIndexV2(
                CategoryTypeEnum.ADVERTISE_GUIDANCE.getCode(), getOperator(context));
        return Response.SUCCESS(categoryDtoList);
    }


    @ApiOperation(value = "商业资讯列表")
    @RequestMapping(value = "/homepage/commercial_info/list", method = RequestMethod.GET)
    @ResponseBody
    @FreeLogin
    public Response<List<CommercialInfoVo>> getCommercialInfoList(@ApiIgnore Context context){
        return Response.SUCCESS(JSON.parseArray(JSON.toJSONString(schoolServiceDocTreeAdapt.getCommercialInfoDto()),
                CommercialInfoVo.class));
    }

    @ApiOperation(value = "常见问题列表")
    @RequestMapping(value = "/homepage/common_question/list", method = RequestMethod.GET)
    @ResponseBody
    @FreeLogin
    public Response<List<SchoolArticleVo>> getCommonQuestionList(@ApiIgnore Context context){
        return Response.SUCCESS(JSON.parseArray(JSON.toJSONString(schoolServiceDocTreeAdapt.getCommonQuestionList()),
                SchoolArticleVo.class));
    }

    private CategoryVo convertCategoryDto2CategoryVo(CategoryDto dto) {
        CategoryVo categoryVo = new CategoryVo();
        categoryVo.setId(dto.getId());
        categoryVo.setImage(dto.getImage());
        categoryVo.setIndustryName(dto.getIndustryName());
        categoryVo.setParentId(dto.getParentId());
        categoryVo.setParentTitle(dto.getParentTitle());
        categoryVo.setTitle(dto.getTitle());
        if(CategoryTypeEnum.ADVERTISE_GUIDANCE.getCode().equals(dto.getType())){
            categoryVo.setTitle(dto.getIndustryName());
        }
        categoryVo.setType(dto.getType());
        categoryVo.setCtime(dto.getCtime());
        categoryVo.setMtime(dto.getMtime());
        categoryVo.setSchoolArticleVoList(JSON.parseArray(JSON.toJSONString(dto.getSchoolArticleDtoList()),SchoolArticleVo.class));
        categoryVo.setChildCategory(CollectionUtils.isNotEmpty(dto.getChildCategory())?dto.getChildCategory().stream().map(child->convertCategoryDto2CategoryVo(child)).collect(Collectors.toList()):null);
        return categoryVo;
    }

    public Operator getOperator(Context context) {
        return Optional.ofNullable(context)
                .map(Context::getAccountId)
                .map(accountId -> Operator.builder().operatorId(accountId)
                        .operatorName(context.getUsername())
                        .operatorType(OperatorType.getByCode(context.getType()))
                        .systemType(systemType)
                        .bilibiliUserName(
                                context.getProxyId() > 0 ? context.getProxyName() + "(" + context.getProxyId() + ")"
                                        : context.getProxyName())
                        .build())
                .orElse(null);
    }

}
