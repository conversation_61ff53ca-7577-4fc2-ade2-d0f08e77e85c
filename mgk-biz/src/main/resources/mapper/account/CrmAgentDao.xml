<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.ad.account.dao.CrmAgentDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="old_id" jdbcType="INTEGER" property="oldId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="sys_agent_id" jdbcType="INTEGER" property="sysAgentId" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="agree_buy_material_protocol" jdbcType="TINYINT" property="agreeBuyMaterialProtocol" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo">
    <id column="crm_agent_id" jdbcType="INTEGER" property="id" />
    <result column="crm_agent_old_id" jdbcType="INTEGER" property="oldId" />
    <result column="crm_agent_name" jdbcType="VARCHAR" property="name" />
    <result column="crm_agent_status" jdbcType="TINYINT" property="status" />
    <result column="crm_agent_type" jdbcType="TINYINT" property="type" />
    <result column="crm_agent_sys_agent_id" jdbcType="INTEGER" property="sysAgentId" />
    <result column="crm_agent_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="crm_agent_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="crm_agent_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="crm_agent_agree_buy_material_protocol" jdbcType="TINYINT" property="agreeBuyMaterialProtocol" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as crm_agent_id, ${alias}.old_id as crm_agent_old_id, ${alias}.name as crm_agent_name, 
    ${alias}.status as crm_agent_status, ${alias}.type as crm_agent_type, ${alias}.sys_agent_id as crm_agent_sys_agent_id, 
    ${alias}.is_deleted as crm_agent_is_deleted, ${alias}.ctime as crm_agent_ctime, ${alias}.mtime as crm_agent_mtime, 
    ${alias}.agree_buy_material_protocol as crm_agent_agree_buy_material_protocol
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, old_id, name, status, type, sys_agent_id, is_deleted, ctime, mtime, agree_buy_material_protocol
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_agent
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_agent
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_agent
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPoExample">
    delete from crm_agent
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent (old_id, name, status, 
      type, sys_agent_id, is_deleted, 
      ctime, mtime, agree_buy_material_protocol
      )
    values (#{oldId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{type,jdbcType=TINYINT}, #{sysAgentId,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{agreeBuyMaterialProtocol,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="oldId != null">
        old_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="sysAgentId != null">
        sys_agent_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="agreeBuyMaterialProtocol != null">
        agree_buy_material_protocol,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="oldId != null">
        #{oldId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="sysAgentId != null">
        #{sysAgentId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="agreeBuyMaterialProtocol != null">
        #{agreeBuyMaterialProtocol,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPoExample" resultType="java.lang.Long">
    select count(*) from crm_agent
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_agent
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.oldId != null">
        old_id = #{record.oldId,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.sysAgentId != null">
        sys_agent_id = #{record.sysAgentId,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.agreeBuyMaterialProtocol != null">
        agree_buy_material_protocol = #{record.agreeBuyMaterialProtocol,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_agent
    set id = #{record.id,jdbcType=INTEGER},
      old_id = #{record.oldId,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      type = #{record.type,jdbcType=TINYINT},
      sys_agent_id = #{record.sysAgentId,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      agree_buy_material_protocol = #{record.agreeBuyMaterialProtocol,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo">
    update crm_agent
    <set>
      <if test="oldId != null">
        old_id = #{oldId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="sysAgentId != null">
        sys_agent_id = #{sysAgentId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="agreeBuyMaterialProtocol != null">
        agree_buy_material_protocol = #{agreeBuyMaterialProtocol,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo">
    update crm_agent
    set old_id = #{oldId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      sys_agent_id = #{sysAgentId,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      agree_buy_material_protocol = #{agreeBuyMaterialProtocol,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent (old_id, name, status, 
      type, sys_agent_id, is_deleted, 
      ctime, mtime, agree_buy_material_protocol
      )
    values (#{oldId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{type,jdbcType=TINYINT}, #{sysAgentId,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{agreeBuyMaterialProtocol,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      old_id = values(old_id),
      name = values(name),
      status = values(status),
      type = values(type),
      sys_agent_id = values(sys_agent_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      agree_buy_material_protocol = values(agree_buy_material_protocol),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_agent
      (old_id,name,status,type,sys_agent_id,is_deleted,ctime,mtime,agree_buy_material_protocol)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.oldId,jdbcType=INTEGER},
        #{item.name,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.type,jdbcType=TINYINT},
        #{item.sysAgentId,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.agreeBuyMaterialProtocol,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_agent
      (old_id,name,status,type,sys_agent_id,is_deleted,ctime,mtime,agree_buy_material_protocol)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.oldId,jdbcType=INTEGER},
        #{item.name,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.type,jdbcType=TINYINT},
        #{item.sysAgentId,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.agreeBuyMaterialProtocol,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      old_id = values(old_id),
      name = values(name),
      status = values(status),
      type = values(type),
      sys_agent_id = values(sys_agent_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      agree_buy_material_protocol = values(agree_buy_material_protocol),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmAgentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="oldId != null">
        old_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="sysAgentId != null">
        sys_agent_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="agreeBuyMaterialProtocol != null">
        agree_buy_material_protocol,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="oldId != null">
        #{oldId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="sysAgentId != null">
        #{sysAgentId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="agreeBuyMaterialProtocol != null">
        #{agreeBuyMaterialProtocol,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="oldId != null">
        old_id = values(old_id),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="sysAgentId != null">
        sys_agent_id = values(sys_agent_id),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="agreeBuyMaterialProtocol != null">
        agree_buy_material_protocol = values(agree_buy_material_protocol),
      </if>
    </trim>
  </insert>
</mapper>