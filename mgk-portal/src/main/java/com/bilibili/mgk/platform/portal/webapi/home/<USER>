package com.bilibili.mgk.platform.portal.webapi.home;

import com.bilibili.mgk.platform.api.es.dto.MgkHomeDataDto;
import com.bilibili.mgk.platform.api.es.service.IMgkOnlineHomeEsService;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebHomeService;
import com.bilibili.mgk.platform.portal.webapi.home.vo.MgkHomeDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @file: MgkHomeController
 * @author: gaoming
 * @date: 2021/07/06
 * @version: 1.0
 * @description:
 **/
@Controller
@RequestMapping("/web_api/v1/home")
@Api(value = "/home", description = "首页相关")
@Slf4j
public class MgkHomeController extends BasicController {


    @Autowired
    private IMgkOnlineHomeEsService mgkOnlineHomeService;

    @Autowired
    private WebHomeService webHomeService;

    @ApiOperation(value = "查询落地页信息")
    @RequestMapping(value = "/data", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<MgkHomeDataVo> getHomeData(@ApiIgnore Context context) {
        MgkHomeDataDto mgkHomeDataDto = mgkOnlineHomeService.getHomeData(getOperator(context));
//        MgkHomeDataDto mgkHomeDataDto = mgkHomeService.getHomeData(getOperator(context));
        return Response.SUCCESS(webHomeService.convertHomeDataDto2Vo(mgkHomeDataDto));
    }
}
