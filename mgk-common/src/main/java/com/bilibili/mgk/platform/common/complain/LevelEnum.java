package com.bilibili.mgk.platform.common.complain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域等级
 */
@AllArgsConstructor
public enum LevelEnum {

    PROVINCE(1, "省"),

    CITY(2, "市"),

    DISTRICT(3, "区");

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static LevelEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("区域等级不可为空");
        }
        for(LevelEnum lrt: values()) {
            if(lrt.getCode().equals(code)) {
                return lrt;
            }
        }
        throw new IllegalArgumentException("未知的区域等级");
    }
}
