package com.bilibili.mgk.platform.api.material.soa;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.material.dto.InspirationCaseAuditReqDTO;
import com.bilibili.mgk.platform.api.material.dto.InspirationCaseQueryDTO;
import com.bilibili.mgk.platform.api.material.dto.MaterialInspirationCaseDTO;
import com.bilibili.mgk.platform.api.material.dto.MaterialQueryProfilesDTO;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/17
 */
public interface ISoaMaterialInspirationCaseService {


    MaterialQueryProfilesDTO queryProfiles();


    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    MaterialInspirationCaseDTO query(Long id);


    /***
     * 分页展示
     * @param query
     * @return
     */
    PageResult<MaterialInspirationCaseDTO> list(InspirationCaseQueryDTO query);


    /**
     * 新增
     *
     * @param save
     */
    int save(MaterialInspirationCaseDTO save);

    /**
     * TODO 编辑是否要重新进入审核流程？理论上是需要的
     *
     * @param selective
     */
    void edit(MaterialInspirationCaseDTO selective);


    /**
     * @param id
     */
    void delete(Long id);

    /**
     * 更新审核状态
     *
     * @param req
     */
    void updateAuditStatus(InspirationCaseAuditReqDTO req);

}
