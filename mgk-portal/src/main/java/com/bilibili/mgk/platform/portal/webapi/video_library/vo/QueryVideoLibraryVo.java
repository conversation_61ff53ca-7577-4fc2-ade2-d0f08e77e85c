package com.bilibili.mgk.platform.portal.webapi.video_library.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/3/12
 * 视频库查询
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryVideoLibraryVo {

    @ApiModelProperty(value = "视频名称")
    private String name;

    @ApiModelProperty(value = "比例 0-其他 1-16:9 2-9:16 3-3:4 4-4:3")
    private Integer size_type;

    @ApiModelProperty(value = "时长(单位秒)")
    private Integer duration_begin;

    @ApiModelProperty(value = "时长(单位秒)")
    private Integer duration_end;

    private Integer page;

    private Integer size;

    @ApiModelProperty("搜索词， 可对视频名称和素材id进行搜索")
    private String search_word;

    @ApiModelProperty(value = "是否倒序，0 倒序，1 正序, 默认0 倒序 ")
    private Integer order = 0;

}
