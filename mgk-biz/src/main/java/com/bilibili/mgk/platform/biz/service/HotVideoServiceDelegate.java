package com.bilibili.mgk.platform.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.passport.api.dto.TypesDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.mgk.platform.api.hot_video.dto.*;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoCollectService;
import com.bilibili.mgk.platform.biz.config.ClickHouseJDBCClient;
import com.bilibili.mgk.platform.biz.dmp.dao.BusinessCategoryDao;
import com.bilibili.mgk.platform.biz.dmp.po.BusinessCategoryPo;
import com.bilibili.mgk.platform.biz.dmp.po.BusinessCategoryPoExample;
import com.bilibili.mgk.platform.biz.utils.DateUtils;
import com.bilibili.mgk.platform.common.*;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import reactor.rx.action.filter.DistinctAction;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.mgk.platform.common.MgkConstants.HOT_VIDEO_EXPIRE_TIME_IN_REDIS;
import static org.jooq.impl.DSL.*;

/**
 * @file: HotVideoServiceDelegate
 * @author: gaoming
 * @date: 2020/11/04
 * @version: 1.0
 * @description:
 **/
@Service
@Slf4j
public class HotVideoServiceDelegate {

    @Autowired
    private IPassportService passportService;

    @Autowired
    private IHotVideoCollectService hotVideoCollectService;

    @Autowired
    private BusinessCategoryDao businessCategoryDao;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    // clickHouse表名
    @Value("${hot_video_table_name:sycpb_2.app_hot_type_archive_local}")
    private String HOT_VIDEO_TABLE_NAME;
    // avid
    private final String COL_AVID = "avid";
    // bvid
    private final String COL_BVID = "bvid";
    // 标题
    private final String COL_TITLE = "title";
    private final String COL_TITLE_COPY = "title_copy";
    // Up主mid
    private final String COL_UP_MID = "up_mid";
    // Up主名称
    private final String COL_UP_NAME = "up_name";
    // 一级分区
    private final String COL_TNAME = "tname";
    private final String COL_TNAME_COPY = "tname_copy";
    // 二级分区
    private final String COL_SUB_TNAME = "sub_tname";
    // 封面
    private final String COL_COVER = "cover";
    // 商业兴趣
    private final String COL_BUSS_INTEREST = "buss_interest";
    private final String COL_BUSS_INTEREST_COPY = "buss_interest_copy";
    // 总播放数量
    private final String COL_PLAY = "play";
    // 总评论数
    private final String COL_REPLY = "reply";
    // 总收藏数
    private final String COL_FAV = "fav";
    // 总投币数
    private final String COL_COIN = "coin";
    // 总弹幕数
    private final String COL_DANMU = "danmu";
    // 总分享数
    private final String COL_SHARE = "share";
    // 总点赞数
    private final String COL_LIKES = "likes";
    // 新增播放数
    private final String COL_PLAY_DAILY = "play_daily";
    // 新增评论数
    private final String COL_REPLY_DAILY = "reply_daily";
    // 新增收藏数
    private final String COL_FAV_DAILY = "fav_daily";
    // 新增投币数量
    private final String COL_COIN_DAILY = "coin_daily";
    // 新增弹幕数
    private final String COL_DANMU_DAILY = "danmu_daily";
    // 新增分享数
    private final String COL_SHARE_DAILY = "share_daily";
    // 新增点赞数
    private final String COL_LIKES_DAILY = "likes_daily";
    // 时间
    private final String COL_LOG_DATE = "log_date";

    // 数量
    private final String COL_BVID_COUNT = "bvid_count";

    // 是否是竖屏，1:是 0:否
    private final String COL_IS_VERTICAL_SCREEN = "is_vertical_screen";

    @Autowired
    private ClickHouseJDBCClient clickHouseJDBCClient;

    public PageResult<HotVideoDto> getHotVideoDtos(Operator operator, QueryHotVideoDto queryHotVideoDto) {

        SelectConditionStep<Record1<Integer>> countSqlSelect = getCountSqlSelect(queryHotVideoDto);
        SelectConditionStep<?> selectConditionStep = setQueryConditions(countSqlSelect, queryHotVideoDto);
        String sql = selectConditionStep.getSQL();
        log.info("getHotVideoDtos: " + sql);
        List<Map<String, Object>> maps = clickHouseJDBCClient.submitQuery(sql);
        if(CollectionUtils.isEmpty(maps)){
            return PageResult.EMPTY_PAGE_RESULT;
        }
        Long count = maps.stream().map(m -> Long.parseLong(m.getOrDefault(COL_BVID_COUNT, 0).toString()))
                .collect(Collectors.toList()).get(0);
        if (count == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }
        SelectHavingStep<?> sqlSelect = getBaseSqlSelect(queryHotVideoDto);
        SelectForUpdateStep<?> withPageAndOrderSqlSelect = getWithPageAndOrderSqlSelect(sqlSelect, queryHotVideoDto);
        log.info("getHotVideoDtos: " + withPageAndOrderSqlSelect.getSQL());
        List<Map<String, Object>> dbData = clickHouseJDBCClient.submitQuery(withPageAndOrderSqlSelect.getSQL());
        List<HotVideoDto> hotVideoDtos = dbData2Dto(dbData);
//        hotVideoDtos.forEach(h -> log.info(h.toString()));
        // 设置视频稿件的收藏状态
        if (!Objects.isNull(operator)) {
            setIsCollects(operator, hotVideoDtos);
        }

        return PageResult.<HotVideoDto>builder().total(count.intValue()).records(hotVideoDtos).build();
    }

    private void setIsCollects(Operator operator, List<HotVideoDto> hotVideoDtos) {
        List<HotVideoCollectDto> collectDtos = hotVideoCollectService.getCollects(QueryHotVideoCollectDto.builder()
                .isDeleted(IsDeleted.VALID.getCode())
                .collectTypes(Lists.newArrayList(MgkHotVideoCollectTypeEnum.HOT_VIDEO.getCode()))
                .accountIds(Lists.newArrayList(operator.getOperatorId()))
                .build());
        List<String> collects = collectDtos.stream().map(HotVideoCollectDto::getBvid).collect(Collectors.toList());
        // 查询列表的视频标识为是否收藏
        hotVideoDtos.forEach(hotVideoDto -> {
            if (collects.contains(hotVideoDto.getBvid())) {
                hotVideoDto.setIsCollect(MgkHotVideoIsCollectEnum.COLLECT_ENUM.getCode());
            }
        });
    }


    public List<HotVideoDto> getHotVideoDtoByBvids(List<String> bvids, Integer dateType) {
        SelectConditionStep<?> baseSqlSelect = this.getBaseSqlSelect(dateType);
        SelectConditionStep<?> selectConditionStep = baseSqlSelect.and(field(COL_BVID).in(bvids.stream().map(DSL::inline).collect(Collectors.toList())));
        SelectHavingStep<?> selectHavingStep = selectConditionStep.groupBy(field(COL_BVID), field(COL_IS_VERTICAL_SCREEN));
        String sql = selectHavingStep.getSQL();
        log.info(sql);
        List<Map<String, Object>> dbData = clickHouseJDBCClient.submitQuery(sql);
        List<HotVideoDto> hotVideoDtos = dbData2Dto(dbData);
        // 设置基本信息
        List<HotVideoDto> res = setBaseInfo(bvids, hotVideoDtos);
        return res;
    }

    private List<HotVideoDto> setBaseInfo(List<String> bvids, List<HotVideoDto> hotVideoDtos) {

        ImmutableMap<String, HotVideoDto> hotVideoDtoImmutableMap = Maps.uniqueIndex(hotVideoDtos, HotVideoDto::getBvid);
        // 获取视频的基本信息
        List<HotVideoBaseDto> hotVideoDtoBaseInfoByBvids = getHotVideoDtoBaseInfoByBvids(bvids);
        Map<String, List<HotVideoBaseDto>> stringHotVideoBaseDtoImmutableMap = hotVideoDtoBaseInfoByBvids.stream().collect(Collectors.groupingBy(HotVideoBaseDto::getBvid));

        return bvids.stream().map(bvid -> {
            HotVideoBaseDto hotVideoBaseDto = stringHotVideoBaseDtoImmutableMap.getOrDefault(bvid, Lists.newArrayList(HotVideoBaseDto.builder()
                    .bvid(bvid)
                    .build())).get(0);
            return hotVideoDtoImmutableMap.getOrDefault(bvid, HotVideoDto.builder()
                    .bvid(bvid)
                    .avid(hotVideoBaseDto.getAvid())
                    .bussInterest(hotVideoBaseDto.getBussInterest())
                    .cover(hotVideoBaseDto.getCover())
                    .subName(hotVideoBaseDto.getSubName())
                    .tname(hotVideoBaseDto.getTname())
                    .upName(hotVideoBaseDto.getUpName())
                    .title(hotVideoBaseDto.getTitle())
                    .upMid(hotVideoBaseDto.getUpMid())
                    .build());
        }).collect(Collectors.toList());
    }

    private SelectForUpdateStep<?> getWithPageAndOrderSqlSelect(SelectHavingStep<?> sqlSelect, QueryHotVideoDto queryHotVideoDto) {
        SelectSeekStep1<?, Object> selectSeekStep1 = setOrderBy(sqlSelect, queryHotVideoDto);
        return selectSeekStep1.limit(inline(queryHotVideoDto.getPage().getLimit()))
                .offset(inline(queryHotVideoDto.getPage().getOffset()));
    }

    private List<HotVideoDto> dbData2Dto(List<Map<String, Object>> dbData) {
        return dbData.stream().filter((m) -> !Objects.isNull(m.get(COL_BVID))).map((m) -> {
            HotVideoDto hotVideoDto = HotVideoDto.builder()
                    .avid(Long.parseLong(m.getOrDefault(COL_AVID, "0").toString()))
                    .bvid(m.getOrDefault(COL_BVID, "0").toString())
                    .title(m.getOrDefault(COL_TITLE_COPY, "").toString())
                    .upMid(Long.parseLong(m.getOrDefault(COL_UP_MID, "0").toString()))
                    .upName(m.getOrDefault(COL_UP_NAME, "").toString())
                    .tname(m.getOrDefault(COL_TNAME_COPY, "").toString())
                    .subName(m.getOrDefault(COL_SUB_TNAME, "").toString())
                    .cover(m.getOrDefault(COL_COVER, "").toString())
                    .bussInterest(m.getOrDefault(COL_BUSS_INTEREST_COPY, "").toString())
                    .play(Long.parseLong(m.getOrDefault(COL_PLAY, "0").toString()))
                    .replay(Long.parseLong(m.getOrDefault(COL_REPLY, "0").toString()))
                    .fav(Long.parseLong(m.getOrDefault(COL_FAV, "0").toString()))
                    .coin(Long.parseLong(m.getOrDefault(COL_COIN, "0").toString()))
                    .danmu(Long.parseLong(m.getOrDefault(COL_DANMU, "0").toString()))
                    .share(Long.parseLong(m.getOrDefault(COL_SHARE, "0").toString()))
                    .likes(Long.parseLong(m.getOrDefault(COL_LIKES, "0").toString()))
                    .playDaily(Long.parseLong(m.getOrDefault(COL_PLAY_DAILY, "0").toString()))
                    .replyDaily(Long.parseLong(m.getOrDefault(COL_REPLY_DAILY, "0").toString()))
                    .favDaily(Long.parseLong(m.getOrDefault(COL_FAV_DAILY, "0").toString()))
                    .coinDaily(Long.parseLong(m.getOrDefault(COL_COIN_DAILY, "0").toString()))
                    .danmuDaily(Long.parseLong(m.getOrDefault(COL_DANMU_DAILY, "0").toString()))
                    .shareDaily(Long.parseLong(m.getOrDefault(COL_SHARE_DAILY, "0").toString()))
                    .likesDaily(Long.parseLong(m.getOrDefault(COL_LIKES_DAILY, "0").toString()))
                    .isCollect(MgkHotVideoIsCollectEnum.NOT_COLLECT_ENUM.getCode())
                    .isVerticalScreen(Integer.parseInt(m.getOrDefault(COL_IS_VERTICAL_SCREEN, "0").toString()))
                    .build();
            hotVideoDto.setCover(!Strings.isNullOrEmpty(hotVideoDto.getCover()) &&
                    !hotVideoDto.getCover().startsWith("http") ? MgkConstants.BFS_DOMAIN_PREFIX + hotVideoDto.getCover() : hotVideoDto.getCover());
            return hotVideoDto;

        }).collect(Collectors.toList());
    }

    private SelectConditionStep<?> getBaseSqlSelect(Integer date) {
        DSLContext create = DSL.using(SQLDialect.MYSQL);
        String nowTime = DateUtils.getTimeNow("yyyy-MM-dd");

        MgkHotVideoDataTypeEnum dateType = MgkHotVideoDataTypeEnum.getByCode(date);
        int diffDays = dateType == MgkHotVideoDataTypeEnum.YESTERDAY ? -1 : (dateType == MgkHotVideoDataTypeEnum.LAST_WEEK ? -7 : -30);
        String timeBefore = null;
        try {
            timeBefore = DateUtils.getTimeBefore(nowTime, diffDays, "yyyy-MM-dd");
        } catch (ParseException e) {
            Assert.isTrue(true, e.getMessage());
        }

        List fileds = Lists.newArrayList(
                max(field(COL_AVID).coerce(Long.class)).as(COL_AVID),
                field(COL_BVID).coerce(String.class),
                max(field(COL_TITLE).coerce(String.class)).as(COL_TITLE_COPY),
                max(field(COL_UP_MID).coerce(String.class)).as(COL_UP_MID),
                max(field(COL_UP_NAME).coerce(String.class)).as(COL_UP_NAME),
                max(field(COL_TNAME).coerce(String.class)).as(COL_TNAME_COPY),
                max(field(COL_SUB_TNAME).coerce(String.class)).as(COL_SUB_TNAME),
                max(field(COL_COVER).coerce(String.class)).as(COL_COVER),
                max(field(COL_BUSS_INTEREST).coerce(String.class)).as(COL_BUSS_INTEREST_COPY),

                max(field(COL_PLAY).coerce(Long.class)).as(COL_PLAY),
                max(field(COL_REPLY).coerce(Long.class)).as(COL_REPLY),
                max(field(COL_FAV).coerce(Long.class)).as(COL_FAV),
                max(field(COL_COIN).coerce(Long.class)).as(COL_COIN),
                max(field(COL_DANMU).coerce(Long.class)).as(COL_DANMU),
                max(field(COL_SHARE).coerce(Long.class)).as(COL_SHARE),
                max(field(COL_LIKES).coerce(Long.class)).as(COL_LIKES),
                field(COL_IS_VERTICAL_SCREEN).coerce(Integer.class),

                sum(field(COL_PLAY_DAILY).coerce(Long.class)).as(COL_PLAY_DAILY),
                sum(field(COL_REPLY_DAILY).coerce(Long.class)).as(COL_REPLY_DAILY),
                sum(field(COL_FAV_DAILY).coerce(Long.class)).as(COL_FAV_DAILY),
                sum(field(COL_COIN_DAILY).coerce(Long.class)).as(COL_COIN_DAILY),
                sum(field(COL_DANMU_DAILY).coerce(Long.class)).as(COL_DANMU_DAILY),
                sum(field(COL_SHARE_DAILY).coerce(Long.class)).as(COL_SHARE_DAILY),
                sum(field(COL_LIKES_DAILY).coerce(Long.class)).as(COL_LIKES_DAILY)
        );

        return (SelectConditionStep<?>) create.select(fileds).from(HOT_VIDEO_TABLE_NAME).where(field(COL_LOG_DATE).greaterOrEqual(inline(timeBefore)));
    }

    private SelectConditionStep<Record1<Integer>> getCountSqlSelect(QueryHotVideoDto queryHotVideoDto) {
        DSLContext create = DSL.using(SQLDialect.MYSQL);
        String nowTime = DateUtils.getTimeNow("yyyy-MM-dd");

        MgkHotVideoDataTypeEnum dateType = MgkHotVideoDataTypeEnum.getByCode(queryHotVideoDto.getDateType());

        int diffDays = dateType == MgkHotVideoDataTypeEnum.YESTERDAY ? -1 : (dateType == MgkHotVideoDataTypeEnum.LAST_WEEK ? -7 : -30);
        String timeBefore = null;
        try {
            timeBefore = DateUtils.getTimeBefore(nowTime, diffDays, "yyyy-MM-dd");
        } catch (ParseException e) {
            Assert.isTrue(true, e.getMessage());
        }
        return create.select(countDistinct(field(COL_BVID)).as(COL_BVID_COUNT)).from(HOT_VIDEO_TABLE_NAME).where(field(COL_LOG_DATE).greaterOrEqual(inline(timeBefore)));
    }

    private SelectHavingStep<?> getBaseSqlSelect(QueryHotVideoDto queryHotVideoDto) {
        SelectConditionStep<?> sqlSelect = this.getBaseSqlSelect(queryHotVideoDto.getDateType());
        sqlSelect = setQueryConditions(sqlSelect, queryHotVideoDto);
        SelectHavingStep<?> selectHavingStep = setGroupBy(sqlSelect);
        return selectHavingStep;
    }

    private SelectHavingStep<?> setGroupBy(SelectConditionStep<?> sqlSelect) {
        return sqlSelect.groupBy(field(COL_BVID), field(COL_IS_VERTICAL_SCREEN));
    }

    private SelectSeekStep1<?, Object> setOrderBy(SelectHavingStep<?> sqlSelect, QueryHotVideoDto queryHotVideoDto) {
        MgkHotVideoOrderByEnum mgkHotVideoOrderByEnum = MgkHotVideoOrderByEnum.getByCode(queryHotVideoDto.getOrderType());
        return sqlSelect.orderBy(field(mgkHotVideoOrderByEnum.getOrderBy()).desc());
    }

    private SelectConditionStep<?> setQueryConditions(SelectConditionStep<?> sqlSelect, QueryHotVideoDto queryHotVideoDto) {

        Condition titleLike = DSL.falseCondition();
        if (!CollectionUtils.isEmpty(queryHotVideoDto.getTitles())) {
            for (String title : queryHotVideoDto.getTitles()) {
                titleLike = titleLike.or(field(COL_TITLE).like(inline("%" + title + "%")));
            }
            sqlSelect = sqlSelect.and(titleLike);
        }

        // 接口层面已限制只能传List<Integer> 防止sql注入
        if (!CollectionUtils.isEmpty(queryHotVideoDto.getBussInterest())) {
            String bussInterest = queryHotVideoDto.getBussInterest().stream()
                    .map(s -> "'" + s + "'").collect(Collectors.joining(","));
            sqlSelect = sqlSelect.and("hasAny(splitByChar(','," + COL_BUSS_INTEREST + "),[" + bussInterest + "])");
        }

        if (!CollectionUtils.isEmpty(queryHotVideoDto.getTnames())) {
            sqlSelect = sqlSelect.and(field(COL_TNAME).in(queryHotVideoDto.getTnames().stream().map(DSL::inline).collect(Collectors.toList())));
        }

        if (!Strings.isNullOrEmpty(queryHotVideoDto.getBvid())) {
            sqlSelect = sqlSelect.and(field(COL_BVID).eq(inline(queryHotVideoDto.getBvid())));
        }

        if (queryHotVideoDto.getBlackStatus() != null) {
            if (MgkHotVideoIsBlackEnum.BLACK.getCode().equals(queryHotVideoDto.getBlackStatus())) {
                sqlSelect.and(field(COL_BVID).in(queryHotVideoDto.getBlackList().stream().map(DSL::inline).collect(Collectors.toList())));
            }
            if (MgkHotVideoIsBlackEnum.NORMAL.getCode().equals(queryHotVideoDto.getBlackStatus())) {
                sqlSelect.and(field(COL_BVID).notIn(queryHotVideoDto.getBlackList().stream().map(DSL::inline).collect(Collectors.toList())));
            }
        }

        if (!Objects.isNull(queryHotVideoDto.getIsVerticalScreen())) {
            sqlSelect = sqlSelect.and(field(COL_IS_VERTICAL_SCREEN).eq(inline(queryHotVideoDto.getIsVerticalScreen())));
        }

        return sqlSelect;
    }

    public List<HotVideoBussInterestDto> getBussInterest() {

        String bussInterestRedisKey = MgkConstants.HOT_VIDEO_BUSSINTEREST_REDIS_KEY;
        String bussInterestString = stringRedisTemplate.opsForValue().get(bussInterestRedisKey);
        if (!Strings.isNullOrEmpty(bussInterestString)) {
            return JSONObject.parseArray(bussInterestString, HotVideoBussInterestDto.class);
        }

        BusinessCategoryPoExample example = new BusinessCategoryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andLevelEqualTo(1);
        List<BusinessCategoryPo> pos = businessCategoryDao.selectByExample(example);
        List<HotVideoBussInterestDto> bussInterest = pos.stream().map(po -> {
            return HotVideoBussInterestDto.builder()
                    .id(po.getId())
                    .name(po.getName())
                    .build();
        }).collect(Collectors.toList());
        stringRedisTemplate.opsForValue().set(bussInterestRedisKey, JSON.toJSONString(bussInterest), HOT_VIDEO_EXPIRE_TIME_IN_REDIS, TimeUnit.DAYS);
        return bussInterest;
    }

    public List<String> getTname() {

        String archiveTypesTNameRedisKey = MgkConstants.HOT_VIDEO_TNAME_REDIS_KEY;
        String archiveTypesString = stringRedisTemplate.opsForValue().get(archiveTypesTNameRedisKey);

        if (!Strings.isNullOrEmpty(archiveTypesString)) {
            return JSONObject.parseObject(archiveTypesString, List.class);
        }

        List<TypesDto> archiveTypes = passportService.getArchiveTypes();

        List<String> archiveTypesResult = archiveTypes.stream().filter(t -> t.getPid() == 0).filter(t -> !"付费".equals(t.getName())).map(TypesDto::getName).collect(Collectors.toList());

        stringRedisTemplate.opsForValue().set(archiveTypesTNameRedisKey, JSON.toJSONString(archiveTypesResult), HOT_VIDEO_EXPIRE_TIME_IN_REDIS, TimeUnit.DAYS);

        return archiveTypesResult;
    }

    public void doCollect(Operator operator, String bvid, Integer collectType) {
        // 查询这个人已经收藏过的所有稿件
        List<HotVideoCollectDto> hasCollectDtos = hotVideoCollectService.getCollects(QueryHotVideoCollectDto.builder()
                .accountIds(Lists.newArrayList(operator.getOperatorId()))
                .bvids(Lists.newArrayList(bvid))
                .collectTypes(Lists.newArrayList(collectType))
                .build());
        // 如果 bvid 在里面 更新，如果不在新增记录
        if (!CollectionUtils.isEmpty(hasCollectDtos)) {
            // 更新
            hotVideoCollectService.updateIsDeleted(operator, bvid, collectType, IsDeleted.VALID);
        } else {
            // 新增记录
            List<HotVideoBaseDto> hotVideoDtoBaseInfoByBvids = getHotVideoDtoBaseInfoByBvids(Lists.newArrayList(bvid));
            Assert.isTrue(!CollectionUtils.isEmpty(hotVideoDtoBaseInfoByBvids), "该视频不存在");
            hotVideoCollectService.insert(HotVideoCollectDto.builder()
                    .accountId(operator.getOperatorId())
                    .bvid(bvid)
                    .isDeleted(IsDeleted.VALID.getCode())
                    .title(hotVideoDtoBaseInfoByBvids.get(0).getTitle())
                    .collectType(collectType)
                    .build());
        }
    }

    public void cancelCollect(Operator operator, String bvid, Integer collectType) {
        List<HotVideoCollectDto> hasCollectDtos = hotVideoCollectService.getCollects(QueryHotVideoCollectDto.builder()
                .accountIds(Lists.newArrayList(operator.getOperatorId()))
                .bvids(Lists.newArrayList(bvid))
                .isDeleted(IsDeleted.VALID.getCode())
                .collectTypes(Lists.newArrayList(collectType))
                .build());
        Assert.isTrue(!CollectionUtils.isEmpty(hasCollectDtos), "未收藏");
        hotVideoCollectService.updateIsDeleted(operator, bvid, collectType, IsDeleted.DELETED);
    }

    public List<HotVideoBaseDto> getHotVideoDtoBaseInfoByBvids(List<String> bvids) {

        if (CollectionUtils.isEmpty(bvids)) {
            return Collections.emptyList();
        }

        SelectConditionStep<?> baseSqlSelect = getBaseSqlSelect(bvids);
        String sql = baseSqlSelect.getSQL();
        log.info(sql);
        List<Map<String, Object>> dbData = clickHouseJDBCClient.submitQuery(sql);
        return dbBaseData2Dto(dbData);
    }

    private List<HotVideoBaseDto> dbBaseData2Dto(List<Map<String, Object>> dbData) {
        return dbData.stream().filter((m) -> !Objects.isNull(m.get(COL_BVID))).map((m) -> {
            return HotVideoBaseDto.builder()
                    .avid(Long.parseLong(m.getOrDefault(COL_AVID, "0").toString()))
                    .bvid(m.getOrDefault(COL_BVID, "0").toString())
                    .title(m.getOrDefault(COL_TITLE, "").toString())
                    .upMid(Long.parseLong(m.getOrDefault(COL_UP_MID, "0").toString()))
                    .upName(m.getOrDefault(COL_UP_NAME, "").toString())
                    .tname(m.getOrDefault(COL_TNAME, "").toString())
                    .subName(m.getOrDefault(COL_SUB_TNAME, "").toString())
                    .cover(MgkConstants.BFS_DOMAIN_PREFIX + m.getOrDefault(COL_COVER, "").toString())
                    .bussInterest(m.getOrDefault(COL_BUSS_INTEREST, "").toString())
                    .build();
        }).collect(Collectors.toList());
    }

    private SelectConditionStep<?> getBaseSqlSelect(List<String> bvids) {
        DSLContext create = DSL.using(SQLDialect.MYSQL);

        List fileds = Lists.newArrayList(
                field(COL_AVID),
                field(COL_BVID),
                field(COL_TITLE),
                field(COL_UP_MID),
                field(COL_UP_NAME),
                field(COL_TNAME),
                field(COL_SUB_TNAME),
                field(COL_COVER),
                field(COL_BUSS_INTEREST)
        );
        return create.select(fileds).from(HOT_VIDEO_TABLE_NAME).where(field(COL_BVID).in(bvids.stream().map(DSL::inline).collect(Collectors.toList())));
    }
}
