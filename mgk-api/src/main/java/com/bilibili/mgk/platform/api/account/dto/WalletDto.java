package com.bilibili.mgk.platform.api.account.dto;

import com.bilibili.adp.common.util.AdMathUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName WalletDto
 * <AUTHOR>
 * @Date 2022/6/21 2:03 上午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WalletDto {
    private Integer accountId;
    private BigDecimal cash;
    private BigDecimal redPacket;
    private BigDecimal totalCashRecharge;
    private BigDecimal totalCashConsume;
    private BigDecimal totalRedPacketRecharge;
    private BigDecimal totalRedPacketConsume;

    /**
     * 起飞-现金-托管-余额（单位元）
     */
    private BigDecimal trustCash;

    /**
     * 起飞-激励金-托管-余额（单位元）
     */
    private BigDecimal trustIncentive;

    /**
     * 起飞-起飞币-托管-余额（单位元）
     */
    private BigDecimal trustFlyCoin;

    public BigDecimal getTrustTotalBalance(){
        BigDecimal temp = AdMathUtils.add(trustCash, trustIncentive);
        return AdMathUtils.add(temp, trustFlyCoin);
    }
}
