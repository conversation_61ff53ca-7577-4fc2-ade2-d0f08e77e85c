package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkLandingPageArchiveRefreshLogPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 页面id
     */
    private Long pageId;

    /**
     * 是否删除 0-正常 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 页面配置
     */
    private String config;

    private static final long serialVersionUID = 1L;
}