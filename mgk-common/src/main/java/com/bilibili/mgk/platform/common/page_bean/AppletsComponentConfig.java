package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsPageComponentConfig
 * @author: gaoming
 * @date: 2021/12/03
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentConfig implements Serializable {

    private static final long serialVersionUID = -7922972716079743785L;
    private String id;

    private String name;

    private Boolean active;

    private AppletsComponentDataConfig data;

    private String fixed;

    private Boolean position;

    private Boolean hasError;
}
