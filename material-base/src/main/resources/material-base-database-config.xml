<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:tx="http://www.springframework.org/schema/tx"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
  		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
  		http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">


  <bean id="materialBaseCatExecutorMybatisPlugin"
    class="com.bilibili.mgk.material.center.util.CatExecutorMybatisExceptionThrowPlugin"/>

    <bean id="materialBaseDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${mgk.jdbc.driver}"></property>
        <property name="jdbcUrl" value="${mgk.jdbc.url}"></property>
        <property name="user" value="${mgk.jdbc.username}"></property>
        <property name="password" value="${mgk.jdbc.password}"></property>
        <!--<property name="minPoolSize" value="10"></property>-->
        <property name="maxPoolSize" value="10"></property>
        <property name="maxIdleTime" value="7200"></property>
        <property name="testConnectionOnCheckin" value="true"></property>
        <property name="idleConnectionTestPeriod" value="5"></property>
        <property name="preferredTestQuery" value="SELECT 1"></property>
        <property name="checkoutTimeout" value="1800000"></property>
    </bean>


    <!-- Spring 和 MyBatis -->
    <bean id="materialBaseSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="materialBaseDataSource"/>
        <property name="mapperLocations" value="classpath:mapper/material_base/*.xml"/>
        <property name="configLocation" value="classpath:material-base-mybatis-config.xml"/>
        <property name="plugins">
            <array>
                <ref bean="materialBaseCatExecutorMybatisPlugin"/>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            params=value1
                        </value>
                    </property>
                </bean>
            </array>

        </property>
    </bean>

    <bean id="materialBaseMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.bilibili.mgk.material.center.repository.base"/>
        <property name="sqlSessionFactoryBeanName" value="materialBaseSqlSessionFactory"/>
    </bean>

    <tx:annotation-driven transaction-manager="materialBaseTransactionManager"/>
    <!-- 配置事务管理器 -->
    <bean id="materialBaseTransactionManager"
      class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="materialBaseDataSource"/>
    </bean>


  <!--   base ad datasource -->


  <bean id="baseAdCatExecutorMybatisPlugin" class="com.bilibili.bjcom.cat.mybatis.CatExecutorMybatisPlugin"/>

  <bean id="materialBaseAdDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <property name="driverClass" value="${mgk.ad.jdbc.driver}"></property>
    <property name="jdbcUrl" value="${mgk.ad.jdbc.url}"></property>
    <property name="user" value="${mgk.ad.jdbc.username}"></property>
    <property name="password" value="${mgk.ad.jdbc.password}"></property>
    <!--<property name="minPoolSize" value="10"></property>-->
    <property name="maxPoolSize" value="10"></property>
    <property name="maxIdleTime" value="7200"></property>
    <property name="testConnectionOnCheckin" value="true"></property>
    <property name="idleConnectionTestPeriod" value="5"></property>
    <property name="preferredTestQuery" value="SELECT 1"></property>
    <property name="checkoutTimeout" value="1800000"></property>
  </bean>


  <!-- Spring 和 MyBatis -->
  <bean id="materialBaseAdSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="dataSource" ref="materialBaseAdDataSource"/>
    <property name="mapperLocations" value="classpath:mapper/base_ad/*.xml"/>
    <property name="configLocation" value="classpath:material-base-mybatis-config.xml"/>
    <property name="plugins">
      <array>
        <ref bean="baseAdCatExecutorMybatisPlugin"/>
        <bean class="com.github.pagehelper.PageInterceptor">
          <property name="properties">
            <!--使用下面的方式配置参数，一行配置一个 -->
            <value>
              params=value1
            </value>
          </property>
        </bean>
      </array>

    </property>
  </bean>

  <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
    <property name="basePackage" value="com.bilibili.mgk.material.center.repository.base_ad"/>
    <property name="sqlSessionFactoryBeanName" value="materialBaseAdSqlSessionFactory"/>
  </bean>

  <tx:annotation-driven transaction-manager="materialBaseAdTransactionManager"/>
  <!-- 配置事务管理器 -->
  <!-- 配置事务管理器 -->
  <bean id="materialBaseAdTransactionManager"
    class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="materialBaseDataSource"/>
  </bean>


</beans>
