package com.bilibili.mgk.platform.portal.webapi.consult_page.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/28 17:17
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ThirdPartMessagePushVo {
    @ApiModelProperty(value = "三方服务Code")
    private String source_code;

    @ApiModelProperty(value = "聊天id")
    private String chat_id;

    @ApiModelProperty(value = "三方聊天id")
    private String third_chat_id;

    @ApiModelProperty(value = "消息内容")
    private String msg_content;

    @ApiModelProperty(value = "消息id")
    private String msg_id;

    @ApiModelProperty(value = "消息样式")
    private int msg_style;

    @ApiModelProperty(value = "消息类型")
    private int msg_type;

    @ApiModelProperty(value = "消息时间")
    private long time_stamp;

    @ApiModelProperty(value = "签名")
    private String signature;
}
