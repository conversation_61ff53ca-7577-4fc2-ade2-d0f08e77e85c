package com.bilibili.mgk.material.center.service.mainsite;

import com.bapis.dynamic.admin.feed.CommerceDyn;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicId;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicDrawPublishReq;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicPageQuery;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import edu.emory.mathcs.backport.java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import javax.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

/**
 * 图文动态服务
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/8
 */
@Slf4j
//@Service
//@Primary
public class BiliDynamicServiceMockImpl extends BiliDynamicService {


    public Pagination<List<CommerceDyn>> page(MaterialDynamicPageQuery query) {

        return Pagination.emptyPagination();


    }


    /**
     * @param dynamicIds
     * @param uid        如果提供，那么需要对uid进行过滤，如果uid为空，则不进行过滤
     * @return
     */
    public Map<Long, CommerceDyn> fetchById(List<Long> dynamicIds, @Nullable Long uid) {

        if (CollectionUtils.isEmpty(dynamicIds)) {
            return Collections.emptyMap();
        }

        return new HashMap<>();
//        return dynamicIds.stream().collect(Collectors.toMap(id -> id, id -> new MaterialDynamicInfo()
//                .setContent("test-" + id)
//                .setDynId(id.toString())
//                .setDynRid(id.toString())
//                .setDynType("test")
//
//        ));


    }


    public MaterialDynamicId createDraw(MaterialDynamicDrawPublishReq req) {
        Random random = new Random();

        return new MaterialDynamicId()
                .setDynId("" + Math.abs(random.nextLong()))
                .setDynRid("" + Math.abs(random.nextLong()))
                .setDynType("test");

    }


}
