package com.bilibili.mgk.platform.biz.service.chat;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.mgk.platform.api.landing_page.dto.*;
import com.bilibili.mgk.platform.biz.service.chat.dto.*;
import com.bilibili.mgk.platform.biz.utils.SecrctUtils;
import com.bilibili.mgk.platform.common.ConsultPageCustomerServiceEnum;
import com.bilibili.mgk.platform.common.MgkWebExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

@Service("KUAI_SHANG_TONG")
@Slf4j
public class KUAISHANGTONGMgkConsultChatServiceImpl extends AbstractMgkConsultChatServiceImpl {


    @Value("${mgk.consult.chat.kuaishangtong.start:https://otherzone.kristonhc.cn/bilibili/consult/session/start?}")
    private String startChatUrl;

    @Value("${mgk.consult.chat.kuaishangtong.end:https://otherzone.kristonhc.cn/bilibili/consult/session/end?}")
    private String endChatUrl;

    @Value("${mgk.consult.chat.kuaishangtong.message:https://otherzone.kristonhc.cn/bilibili/consult/session/push?}")
    private String messageChatUrl;

    @Value("${mgk.consult.chat.kuaishangtong.appid:1015}")
    private String appId;

    @Value("${mgk.consult.chat.kuaishangtong.appScr:VyZBmdQnDENDZWaLvHnUfXgrncVadfjx}")
    private String appScr;


    @Override
    public ChatStartRespDto startConsult(ConsultLandingPageDto config, StartChatDto startChatDto) throws ServiceException {
        ChatStartRespDto chatStartRespDto = new ChatStartRespDto();
        ChatStartReq req = ChatStartReq.builder()
                .app_id(appId)
                .advertiser_id(config.getAccountId().toString())
                .chat_id(startChatDto.getChatId())
                .chat_start_type(startChatDto.getChatStartType())
                .customer_info(startChatDto.getCustomerInfo())
                .chat_his("[]")
//                .chat_his(JSON.toJSONString(startChatDto.getChatHis().stream()
//                        .map(this::detail2Dto) // 使用方法引用将每个MessageDetail转换为MessageTransDto
//                        .collect(Collectors.toList())))
                .build();
        String reqUrl = genUrlWithToken(startChatUrl, JSON.toJSONString(req), startChatDto.getChatId());
        ChatStartResp resp;
        try {
            resp = OkHttpUtils.bodyPost(reqUrl)
                    .bean(req)
                    .callForObject(ChatStartResp.class);
        }catch (Exception e) {
            log.error("startConsultChart error , req:{}", req , e);
            throw new ServiceException(MgkWebExceptionCode.THIRD_CHAT_CURL_FAILED);
        }
        log.info("startConsultChart reqUrl:{} request:{}, response:{}, chat_id:{}", reqUrl, req, resp, startChatDto.getChatId());
        if(!RESP_SUCCESS.equals(resp.getCode()) || resp.getData() == null || resp.getData().getThird_chat_id() == null){
            throw new ServiceException(MgkWebExceptionCode.THIRD_CHAT_START_FAILED);
        }
        RBucket<String> bucket = redissonClient.getBucket(CHAT_ID_PREFIX + startChatDto.getChatId());
        bucket.set(startChatDto.getChatId(), CHAT_EXPIRE_SPAN, TimeUnit.MINUTES);

        chatStartRespDto.setThirdChatId(resp.getData().getThird_chat_id());
        return chatStartRespDto;
    }

    @Override
    public ChatEndRespDto endConsult(ConsultLandingPageDto config, EndChatDto endChatDto) throws ServiceException {
        ChatEndRespDto chatEndRespDto = new ChatEndRespDto();
        ChatEnqReq req = ChatEnqReq.builder()
                .app_id(appId)
                .advertiser_id(config.getAccountId().toString())
                .chat_id(endChatDto.getChatId())
                .third_chat_id(endChatDto.getThirdChatId())
                .build();
        String reqUrl = genUrlWithToken(endChatUrl, JSON.toJSONString(req), endChatDto.getChatId());
        ChatEndResp resp ;
        try{
            resp = OkHttpUtils.bodyPost(reqUrl)
                    .bean(req)
                    .callForObject(ChatEndResp.class);
        }catch (Exception e) {
            log.error("endConsultChart error , req:{}", req , e);
            throw new ServiceException(MgkWebExceptionCode.THIRD_CHAT_CURL_FAILED);
        }
        log.info("endConsultChart request:{}, response:{}, chat_id:{}", req, resp, endChatDto.getChatId());
        if(!RESP_SUCCESS.equals(resp.getCode())){
            throw new ServiceException(MgkWebExceptionCode.THIRD_CHAT_END_FAILED);
        }
        RBucket<String> bucket = redissonClient.getBucket(CHAT_ID_PREFIX + endChatDto.getChatId());
        bucket.delete();
        return chatEndRespDto;
    }

    @Override
    public MessagePushRespDto pushMessage(ConsultLandingPageDto config, MessagePushDto messagePushDto) throws ServiceException {
        MessagePushRespDto messagePushRespDto = new MessagePushRespDto();
        // 如果消息发送时间已超过5分钟，不再发送
        if (System.currentTimeMillis() - messagePushDto.getChatDetail().getTimeStamp() > MESSAGE_REFUSE_TIME_SPAN * 60 * 1000) {
            log.error("消息已过期，不再发送，TimeStamp:{}",  messagePushDto.getChatDetail().getTimeStamp());
//            throw new ServiceException(MgkWebExceptionCode.MESSAGE_EXPIRE);
        }
        if(redisService.getCache(genMsgKey(messagePushDto.getChatDetail().getMsgId())) != null){
            throw new ServiceException(MgkWebExceptionCode.MESSAGE_DEPULICATE);
        }

        MessagePushReq req = MessagePushReq.builder()
                .app_id(appId)
                .chat_id(messagePushDto.getChatId())
                .advertiser_id(config.getAccountId().toString())
                .third_chat_id(messagePushDto.getThirdChatId())
                .msg_style(messagePushDto.getChatDetail().getMsgStyle().getId())
                .msg_type(messagePushDto.getChatDetail().getMsgType().getThirdPartCode())
                .time_stamp(messagePushDto.getChatDetail().getTimeStamp())
                .msg_content(messagePushDto.getChatDetail().getMsgContent())
                .build();
        String reqUrl = genUrlWithToken(messageChatUrl, JSON.toJSONString(req), messagePushDto.getChatDetail().getMsgId());
        MessagePushResp resp;
        try {
            resp = OkHttpUtils.bodyPost(reqUrl)
                    .bean(req)
                    .callForObject(MessagePushResp.class);
        }catch (Exception e) {
            log.error("pushMessageInConsultChart error , req:{}", req , e);
            throw new ServiceException(MgkWebExceptionCode.THIRD_CHAT_CURL_FAILED);
        }
        log.info("pushMessageInConsultChart request:{}, response:{}, chat_id:{}", req, resp, messagePushDto.getChatId());
        if(!RESP_SUCCESS.equals(resp.getCode())){
            throw new ServiceException(MgkWebExceptionCode.THIRD_CHAT_MESSAGE_FAILED);
        }
        // lancer备份
        // lancer备份,本地日志文件打印，性能很好，不需要异步
        StringBuilder msgBuilder = new StringBuilder();
        msgBuilder.append(messagePushDto.getChatId()).append(SEPARATOR)
                .append(messagePushDto.getThirdChatId()).append(SEPARATOR)
                .append(messagePushDto.getChatDetail().getMsgId()).append(SEPARATOR)
                .append(messagePushDto.getChatDetail().getMsgContent()).append(SEPARATOR)
                .append(messagePushDto.getChatDetail().getMsgStyle().getId()).append(SEPARATOR)
                .append(messagePushDto.getChatDetail().getMsgType().getCode()).append(SEPARATOR)
                .append(messagePushDto.getChatDetail().getTimeStamp()).append(SEPARATOR)
                .append(ConsultPageCustomerServiceEnum.KUAI_SHANG_TONG.getThirdPartCode());
        chatApiLancerClient.put(logId, msgBuilder.toString());
        try {
            redisService.addCache(genMsgKey(messagePushDto.getChatDetail().getMsgId()), messagePushDto.getChatDetail().getMsgId(), MESSAGE_REFUSE_TIME_SPAN * 60);
            RBucket<String> bucket = redissonClient.getBucket(CHAT_ID_PREFIX + messagePushDto.getChatId());
            if (bucket.isExists()) {
                bucket.expire(CHAT_EXPIRE_SPAN, TimeUnit.MINUTES); // 更新过期时间为20分钟
            }
        }catch (Exception e){
            // 弱依赖
            log.error("[pushMessage] addCache error", e);
        }

        return messagePushRespDto;
    }


    private String genUrlWithToken(String url , String reqJsonStr , String uniqueId) throws ServiceException {
        try {
            String token = genMsgToken(reqJsonStr, uniqueId);
            String tokenParam = "&token=" + URLEncoder.encode(token, "UTF-8");
            return url + tokenParam;
        }catch (UnsupportedEncodingException unsupportedEncodingException){
            throw new ServiceException(MgkWebExceptionCode.TOKEN_SCR_FAIL);
        }
    }

    private String genMsgToken(String reqJsonStr , String uniqueId){
        String timestamp = String.valueOf(Instant.now().toEpochMilli());
        String signStr = reqJsonStr+appScr+timestamp+uniqueId;
        String sign = SecrctUtils.sha256Hash(signStr);
        String tokenStr = String.join(TOKEN_SEPARATOR, appId, timestamp, uniqueId, sign);
        log.info("genMsgToken tokenStr:{}, sign:{}, signStr:{}", tokenStr, sign, signStr);
        return SecrctUtils.base64Encode(tokenStr);
    }

    private String genMsgKey(String msgId){
        return String.format("%s_%s" , THIRD_MESSAGE_PREFIX , msgId);
    }

}
