package com.bilibili.mgk.platform.api.personal_mgk.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class AutoCreatePersonalFlyDto implements Serializable {


    private static final long serialVersionUID = -5550582270014299047L;
    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 用户id
     */
    private Long mid;

    /**
     * 转化时间
     */
    private Long ts;

    /**
     * 数据id
     */
    private Long formDataId;


}
