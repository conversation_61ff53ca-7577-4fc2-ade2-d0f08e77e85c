package com.bilibili.mgk.platform.common.utils;

import com.google.common.io.BaseEncoding;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.common.MgkConstants.OPEN_PAGE_VALIDATE_KEY_SPLIT;

/**
 * 数据操作工具类。
 *
 * <AUTHOR>
 * @since 2018年07月31日
 */
public class DataUtils {

    public final static String ALGORITHM_CBC = "AES/CBC/PKCS5Padding";

    private DataUtils() {
    }

    private static Cipher cbcCipher = null;

    public static String encrypt(String sSrc, String sKey, String ivParameter) {
        try {
            if(cbcCipher == null) {
                cbcCipher = Cipher.getInstance(ALGORITHM_CBC);
            }
            byte[] raw = sKey.getBytes();
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());//使用CBC模式，需要一个向量iv，可增加加密算法的强度
            cbcCipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            byte[] encrypted;
            encrypted = cbcCipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));
            return BaseEncoding.base64Url().encode(encrypted);
        } catch (Exception e) {
            return "";
        }
    }

//    public static void main(String[] args) throws Exception {
//        Long pageId = 705926727952793600L;
//        String str = pageId + OPEN_PAGE_VALIDATE_KEY_SPLIT +
//                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
//        String key = "h3yf6egdja5yq6dg";
//        String ivParam = "kijw7f6sdd5kko08";
//        String encrypt = encrypt(str, key, ivParam);
//        System.out.println(encrypt);
//        System.out.println(decrypt(encrypt, key ,ivParam));
//    }

    // 解密
    public static String decrypt(String sSrc, String sKey, String ivParameter) {
        try {
            byte[] raw = sKey.getBytes(StandardCharsets.US_ASCII);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            if(cbcCipher == null) {
                cbcCipher = Cipher.getInstance(ALGORITHM_CBC);
            }
            IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());
            cbcCipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] encrypted1 = com.google.common.io.BaseEncoding.base64Url().decode(sSrc);//先用base64解密
            byte[] original = cbcCipher.doFinal(encrypted1);
            String originalString = new String(original, StandardCharsets.UTF_8);
            return originalString;
        } catch (Exception ex) {
            return "";
        }
    }

    public static SecretKey generateStrongAESKey(final int keysize) {
        final KeyGenerator kgen;
        try {
            kgen = KeyGenerator.getInstance("AES");
        } catch (final NoSuchAlgorithmException e) {
            throw new RuntimeException("AES key generator should always be available in a Java runtime", e);
        }
        final SecureRandom rng;
        try {
            rng = SecureRandom.getInstanceStrong();
        } catch (final NoSuchAlgorithmException e) {
            throw new RuntimeException("No strong secure random available to generate strong AES key", e);
        }
        // already throws IllegalParameterException for wrong key sizes
        kgen.init(keysize, rng);

        return kgen.generateKey();
    }

    public static String toHex(final byte[] data) {
        final StringBuilder sb = new StringBuilder(data.length * 2);
        for (byte b : data) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }


    public static void main(String[] args) throws NoSuchAlgorithmException {
        SecretKey strongAESKey = generateStrongAESKey(128);
        System.out.println("generate key  " + toHex(strongAESKey.getEncoded()));
        System.out.println("generate iv  " +toHex(strongAESKey.getEncoded()));
    }




    /**
     * 清除掉所有特殊字符
     * @param str
     * @return
     * @throws PatternSyntaxException
     */
    public static String StringFilter(String str) throws PatternSyntaxException {
        String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    /**
     * 判断两个集合是否拥有相同的元素。
     */
    public static boolean isEquals(Collection dataA, Collection dataB) {
        if (dataA == null || dataB == null) {
            return false;
        }
        return dataA.containsAll(dataB) && dataA.size() == dataB.size();
    }

    /**
     * 取两个集合的交集。
     */
    public static <E, C extends Collection<E>> C intersect(C dataA, C dataB) {
        if (dataA == null || dataB == null) {
            return null;
        }
        if (dataA.isEmpty()) {
            return dataA;
        }
        if (dataB.isEmpty()) {
            return dataB;
        }
        dataA.retainAll(dataB);
        return dataA;
    }

    /**
     * 查找集合中的元素。
     */
    public static <E> List<E> find(Collection<E> data, Predicate<E> tester) {
        if (data == null || data.isEmpty() || tester == null) {
            return Collections.emptyList();
        }
        List<E> results = new ArrayList<>();
        for (E e : data) {
            if (e != null && tester.test(e)) {
                results.add(e);
            }
        }
        return results;
    }

    /**
     * 统计集合中的元素。
     */
    public static <E> long count(Collection<E> data, Predicate<E> tester) {
        if (data == null || data.isEmpty() || tester == null) {
            return 0L;
        }
        long count = 0L;
        for (E e : data) {
            if (e != null && tester.test(e)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 查找第一个匹配的元素。
     */
    public static <E> E findFirst(Collection<E> data, Predicate<E> tester) {
        if (data == null || data.isEmpty() || tester == null) {
            return null;
        }
        for (E e : data) {
            if (e != null && tester.test(e)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 查找唯一匹配的元素。
     */
    public static <E> E findUnique(Collection<E> data, Predicate<E> tester) {
        if (data == null || data.isEmpty() || tester == null) {
            return null;
        }
        E found = null;
        for (E e : data) {
            if (e != null && tester.test(e)) {
                if (found != null) {
                    throw new IllegalStateException("Found too much!");
                }
                found = e;
            }
        }
        return found;
    }

    /**
     * 查找并且合并成一个元素。
     */
    public static <E> E findReduce(Collection<E> data, Predicate<E> tester, BinaryOperator<E> merger) {
        if (data == null || data.isEmpty() || tester == null) {
            return null;
        }
        E found = null;
        for (E e : data) {
            if (e != null && tester.test(e)) {
                if (found == null) {
                    found = e;
                } else {
                    found = merger.apply(found, e);
                }
            }
        }
        return found;
    }

    /**
     * 把源数据合并成多个分组。
     */
    public static <E> Collection<E> groupBy(Collection<E> data, Function<E, ?> identifier, BinaryOperator<E> merger) {
        if (data == null || data.isEmpty()) {
            return Collections.emptyList();
        }
        return data.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(identifier, Function.identity(), merger))
                .values();
    }

    public static String[] getNullPropertyNames (Object source) {
        final BeanWrapper wrapper = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = wrapper.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for(java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = wrapper.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

}
