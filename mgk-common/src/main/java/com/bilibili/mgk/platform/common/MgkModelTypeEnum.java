package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/08/03
 **/
@Getter
@AllArgsConstructor
public enum MgkModelTypeEnum {

    /**
     * H5
     */
    H5(1, "H5"),

    /**
     * 原生
     */
    NATIVE(2, "原生"),

    /**
     * 原生自定义
     */
    CUSTOM(3, "原生自定义"),

    /**
     * 小程序
     */
    APPLETS(4, "小程序");
    private Integer code;
    private String desc;

    public static List<Integer> MODEL_TYPE_CODE = Arrays.stream(values()).map(MgkModelTypeEnum::getCode).collect(Collectors.toList());

    public static MgkModelTypeEnum getByCode(int code) {
        for (MgkModelTypeEnum bean : values()) {
            if (bean.getCode() == code) return bean;
        }
        throw new IllegalArgumentException("unknown code MgkModelTypeEnum " + code);
    }
}
