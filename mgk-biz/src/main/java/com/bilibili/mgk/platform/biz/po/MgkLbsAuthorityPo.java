package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkLbsAuthorityPo implements Serializable {
    /**
     * 自增ID（主键）
     */
    private Long id;

    /**
     * 主站mid
     */
    private Long mid;

    /**
     * B站用户唯一识别号
     */
    private String buvid;

    /**
     * 用户终端IMEI
     */
    private String imei;

    /**
     * 设备ID: os=0存AndroidID os=1 IDFA os=2 DUID
     */
    private String deviceId;

    /**
     * 是否允许获取位置权限 0-不允许 1-允许
     */
    private Integer isLbsAuthorized;

    /**
     * 是否被删除 0-正常 1-被删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}