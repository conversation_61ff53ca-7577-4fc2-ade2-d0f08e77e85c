package com.biz.common.doc.tree.repository.redis;

import com.biz.common.doc.tree.config.EsPushDatabusConfiguration.DocTreeConfig;
import com.google.common.collect.ImmutableMap;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RBucketAsync;
import org.redisson.api.RFuture;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 因为es存在同步延迟，所以在写入后使用该缓存作为即时的返回； 该缓存特点：
 * 1. 支持压缩
 * 2. 一定时间过期
 * 3. 支持大文档分片后存储，避免大key TODO  该feature暂时没有必要。压缩率非常高
 *
 * <AUTHOR>
 * @desc
 * @date 2025/5/19
 */
@Slf4j
@Service
public class DocContentRedisCache {

    @Resource
    private DocTreeConfig docTreeConfig;

    @Resource
    private RedissonClient redissonClient;

    // 压缩字符串
    private static String compressString(String data) throws IOException {
        if (data == null || data.isEmpty()) {
            return data;
        }

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
                GZIPOutputStream gzipOS = new GZIPOutputStream(bos)) {

            gzipOS.write(data.getBytes(StandardCharsets.UTF_8));
            gzipOS.finish();
            return bos.toString("ISO-8859-1");
        }
    }

    // 解压缩字符串
    private static String decompressString(String compressedData) throws IOException {
        if (compressedData == null || compressedData.isEmpty()) {
            return compressedData;
        }

        byte[] decodedBytes = compressedData.getBytes("ISO-8859-1");
        try (ByteArrayInputStream bis = new ByteArrayInputStream(decodedBytes);
                GZIPInputStream gzipIS = new GZIPInputStream(bis);
                ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipIS.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toString(StandardCharsets.UTF_8.displayName());
        }
    }

    public void setContent(Long nodeId, String content) {

        if (nodeId == null || StringUtils.isEmpty(content)) {
            return;
        }

        Try.run(() -> {

            String compressedContent = compressString(content);
            redissonClient.getBucket(String.format(docTreeConfig.getDocContentRedisCacheKey(), nodeId))
                    .set(compressedContent, docTreeConfig.getDocContextRedisCacheSeconds(), TimeUnit.SECONDS);

        }).onFailure(throwable -> {
            log.error("doc content redis cache set error, nodeId:{}", nodeId, throwable);
        });
    }

    public Optional<String> getContent(Long nodeId) {

        if (nodeId == null) {
            return Optional.empty();
        }

        return Try.of(() -> {

            String compressedContent = (String) redissonClient.getBucket(
                    String.format(docTreeConfig.getDocContentRedisCacheKey(), nodeId)).get();

            return decompress(compressedContent);
        }).onFailure(throwable -> {
            log.error("doc content redis cache get error, nodeId:{}", nodeId, throwable);
        }).getOrElse(Optional.empty());
    }

    public Map<Long, Optional<String>> getContent(List<Long> nodeIds) {

        if (nodeIds == null || nodeIds.isEmpty()) {
            return new HashMap<>();
        }

        return Try.of(() -> {

            if (nodeIds.size() == 1) {
                return ImmutableMap.of(nodeIds.get(0), getContent(nodeIds.get(0)));
            }

            RBatch batch = redissonClient.createBatch();

            // 批量获取

            List<Tuple2<Long, RFuture<String>>> futures = nodeIds.stream().map((nodeId) -> {
                RBucketAsync<String> bucket = batch.getBucket(
                        String.format(docTreeConfig.getDocContentRedisCacheKey(), nodeId));
                return Tuple.of(nodeId, bucket.getAsync());
            }).collect(Collectors.toList());

            return futures.stream().map(f -> {

                return Tuple.of(f._1, Try.of(() -> {
                    return decompress(f._2.getNow());
                }).onFailure(t -> {
                    log.error("Fail to decompress redis content of nodeId={}", f._1, t);
                }).getOrElse(Optional.empty()));
            }).collect(Collectors.toMap(tuple -> tuple._1, tuple -> tuple._2, (v1, v2) -> v1));

        }).onFailure(throwable -> {
            log.error("doc content redis cache get error, nodeIds:{}", nodeIds, throwable);
        }).getOrElse(
                nodeIds.stream().distinct().collect(Collectors.toMap(nodeId -> nodeId, nodeId -> Optional.empty())));

    }



    private Optional<String> decompress(String compressedContent) throws IOException {
        if (StringUtils.isEmpty(compressedContent)) {
            return Optional.<String>empty();
        }
        String decompressString = decompressString(compressedContent);

        if (StringUtils.isEmpty(decompressString)) {
            return Optional.<String>empty();
        }
        return Optional.of(decompressString);
    }




}
