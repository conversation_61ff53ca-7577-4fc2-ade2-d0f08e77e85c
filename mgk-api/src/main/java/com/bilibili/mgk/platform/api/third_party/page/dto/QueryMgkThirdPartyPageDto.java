package com.bilibili.mgk.platform.api.third_party.page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName QueryMgkThirdPartyPageDto
 * <AUTHOR>
 * @Date 2023/5/19 3:40 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryMgkThirdPartyPageDto {

    private List<Long> pageIdList;

    private Integer accountId;

    private Boolean needIsDeleted;

}
