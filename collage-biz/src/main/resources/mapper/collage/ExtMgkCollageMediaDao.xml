<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bilibili.collage.biz.dao.ext.ExtMgkCollageMediaDao">
  <resultMap id="ExtResultMap" type="com.bilibili.collage.biz.po.ExtMgkCollageMediaCntPo">
    <result column="account_id" jdbcType="INTEGER" property="accountId"/>
    <result column="media_count" jdbcType="INTEGER" property="mediaCount"/>
  </resultMap>

  <select id="countMediaNumGroupByAccountId" resultMap="ExtResultMap">
    SELECT account_id, count(1) AS media_count
    FROM mgk_collage_media
    WHERE is_deleted = 0
    AND account_id in
    <foreach item="accountId" index="index" collection="accountIds" open="(" separator="," close=")">
      #{accountId}
    </foreach>
    GROUP BY account_id;
  </select>

</mapper>