package com.bilibili.mgk.platform.portal.test;

import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.mgk.platform.api.data.service.IMgkDataService;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.bilibili.mgk.platform.portal.service.WebFormService;
import com.bilibili.mgk.platform.portal.webapi.form.MgkFormController;
import com.bilibili.mgk.platform.portal.webapi.form.vo.FormDataVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/9/26
 **/
@Slf4j
public class MgkFormControllerTest extends BaseMockitoTest {

    @Mock
    private IMgkFormService mgkFormService;
    @Mock
    private WebFormService webFormService;
    @Mock
    private IMgkDataService mgkDataService;

    @InjectMocks
    private MgkFormController mgkFormController;

    @Test
    public void getExcelDatas() throws Exception {
        List<List<String>> data = ReflectionTestUtils.invokeMethod(mgkFormController,
                "getExcelDatas",
                Collections.singletonList(BeanTestUtils.initSimpleFields(FormDataVo.builder().build())),
                Collections.singletonList(1L));

        Assert.assertNotNull(data);
        Assert.assertFalse(data.isEmpty());
    }

}
