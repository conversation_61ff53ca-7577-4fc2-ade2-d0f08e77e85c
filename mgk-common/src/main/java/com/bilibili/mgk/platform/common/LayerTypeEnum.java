package com.bilibili.mgk.platform.common;

import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/11/13
 * 拼贴艺术-图层类型
 **/
@NoArgsConstructor
public enum LayerTypeEnum {

    IMAGE(1, "图片"),
    TEXT(2, "文本");

    @Getter
    private Integer code;

    @Getter
    private String name;

    LayerTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
