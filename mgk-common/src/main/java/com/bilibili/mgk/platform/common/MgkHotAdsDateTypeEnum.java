package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotAdsDateTypeEnum
 * @author: gaoming
 * @date: 2021/01/07
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
public enum MgkHotAdsDateTypeEnum {

    LAST_WEEK(0, "7d", "上星期"),
    LAST_MOUTH(1, "30d", "上个月");
    @Getter
    private final Integer code;
    @Getter
    private final String value;

    private final String desc;

    public static MgkHotAdsDateTypeEnum getByCode(Integer code) {
        for (MgkHotAdsDateTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotAdsDateTypeEnum " + code);
    }
}
