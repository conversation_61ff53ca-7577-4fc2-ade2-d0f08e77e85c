package com.bilibili.mgk.platform.api.landing_page.service;

import com.bilibili.mgk.platform.api.landing_page.dto.MongoLandingPageDto;

import java.util.List;

/**
 * @file: IMgkMongoLandingPageService
 * @author: gaoming
 * @date: 2021/03/02
 * @version: 1.0
 * @description:
 **/
public interface IMgkMongoLandingPageService {

    /**
     * 插入落地页配置
     *
     * @param dto
     */
    void saveLandingPage(MongoLandingPageDto dto);

    /**
     * 根据用户id和页面id获取数据中包含web_url的pageIds
     * @param accountId
     * @param pageIds
     * @param pageId
     * @return
     */
    List<MongoLandingPageDto> findContainsWebUrlByPageIds(Integer accountId, List<Long> pageIds, Long pageId);
}
