package com.bilibili.mgk.platform.biz.service.page_group.delegate;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.*;
import com.bilibili.mgk.platform.api.third_party.page.service.IMgkThirdPartyPageService;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkLandingPageGroupMappingQueryDSLPo;
import com.bilibili.mgk.platform.biz.service.page.LandingPageUrlProc;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.bilibili.mgk.platform.common.page_group.PageGroupMappingStatusEnum;
import com.bilibili.mgk.platform.common.page_group.PageGroupSourceEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkLandingPageGroupMapping.mgkLandingPageGroupMapping;

/**
 * @ClassName LandingPageGroupMappingServiceDelegate
 * <AUTHOR>
 * @Date 2023/5/18 2:26 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class LandingPageGroupMappingServiceDelegate {

    private final IMgkLandingPageService mgkLandingPageService;
    private final IMgkThirdPartyPageService mgkThirdPartyPageService;
    private final LandingPageUrlProc landingPageUrlProc;
    private final BaseQueryFactory bqf;


    LandingPageGroupMappingServiceDelegate(IMgkLandingPageService mgkLandingPageService,
                                           IMgkThirdPartyPageService mgkThirdPartyPageService,
                                           LandingPageUrlProc landingPageUrlProc,
                                           BaseQueryFactory bqf) {
        this.mgkLandingPageService = mgkLandingPageService;
        this.mgkThirdPartyPageService = mgkThirdPartyPageService;
        this.landingPageUrlProc = landingPageUrlProc;
        this.bqf = bqf;
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public boolean savePageGroupMapping(LandingPageGroupMappingSaveDto saveDto, Operator operator) {
        PageGroupSourceEnum groupSourceEnum = PageGroupSourceEnum.getByCode(saveDto.getGroupSource());
        boolean isMgkSource = groupSourceEnum.equals(PageGroupSourceEnum.MGK_SOURCE);
        List<LandingPageGroupMappingListDto> mappingList = saveDto.getMappingList();
        // 保存三方链接
        if (!isMgkSource) {
            mappingList = mgkThirdPartyPageService.saveThirdPartyPage(mappingList, operator);
        } else {
            fillMappingMgkPageUrl(mappingList);
        }
        List<Long> savePageIds = mappingList.stream()
                .map(LandingPageGroupMappingListDto::getPageId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());

        // 删除多余的mapping
        List<LandingPageGroupMappingDto> existMappingDtoList = queryPageGroupMappingList(saveDto.getGroupId());
        List<Long> needDeleteIds = existMappingDtoList.stream()
                .filter(existMappingDto -> !savePageIds.contains(existMappingDto.getPageId()))
                .map(LandingPageGroupMappingDto::getId)
                .collect(Collectors.toList());
        deleteMappingByIds(needDeleteIds);

        // 插入新增mapping
        List<Long> existMappingPageIds = existMappingDtoList.stream()
                .map(LandingPageGroupMappingDto::getPageId)
                .collect(Collectors.toList());
        List<LandingPageGroupMappingListDto> insertList = mappingList.stream()
                .filter(insertDto -> !existMappingPageIds.contains(insertDto.getPageId()))
                .collect(Collectors.toList());
        boolean insertNeedAudit = !CollectionUtils.isEmpty(insertList)
                && Boolean.TRUE.equals(saveDto.getHasAuditCreative());
        if (!CollectionUtils.isEmpty(insertList)) {
            List<MgkLandingPageGroupMappingQueryDSLPo> insertPoList = insertList.stream()
                    .map(insertDto -> {
                        MgkLandingPageGroupMappingQueryDSLPo insertPo = new MgkLandingPageGroupMappingQueryDSLPo();
                        insertPo.setPageId(insertDto.getPageId());
                        insertPo.setGroupId(saveDto.getGroupId());
                        insertPo.setAccountId(operator.getOperatorId());
                        insertPo.setGroupSource(saveDto.getGroupSource());
                        insertPo.setIsEnable(insertDto.getIsEnable());
                        insertPo.setMappingStatus(Boolean.TRUE.equals(saveDto.getHasAuditCreative()) ?
                                PageGroupMappingStatusEnum.WAIT_AUDIT.getCode() :
                                PageGroupMappingStatusEnum.NO_CREATIVE.getCode());
                        insertPo.setPageUrl(insertDto.getPageUrl());
                        insertPo.setContainerPageUrl(insertDto.getContainerPageUrl());
                        insertPo.setContainerPageId(insertDto.getContainerPageId());
                        return insertPo;
                    }).collect(Collectors.toList());
            bqf.insert(mgkLandingPageGroupMapping).insertBeans(insertPoList);
        }

        // 更新mapping 主要是为了开关打开/关闭 以及更新状态
        List<LandingPageGroupMappingListDto> updateList = mappingList.stream()
                .filter(mappingDto -> existMappingPageIds.contains(mappingDto.getPageId()))
                .collect(Collectors.toList());
        boolean updateNeedAudit = false;
        if (!CollectionUtils.isEmpty(updateList)) {
            Map<Long, LandingPageGroupMappingDto> existMappingDtoMap = existMappingDtoList.stream()
                    .collect(Collectors.toMap(LandingPageGroupMappingDto::getPageId,
                            Function.identity()));
            List<Boolean> pageNeedAuditList = updateList.stream().map(updateDto -> {
                Assert.isTrue(existMappingDtoMap.containsKey(updateDto.getPageId()), "存在无效数据,请刷新页面");
                LandingPageGroupMappingDto existMappingDto = existMappingDtoMap.get(updateDto.getPageId());
                boolean needAudit = WhetherEnum.YES.getCode().equals(updateDto.getIsEnable())
                        && PageGroupMappingStatusEnum.TO_AUDIT_STATUS_LIST.contains(existMappingDto.getMappingStatus())
                        && Boolean.TRUE.equals(saveDto.getHasAuditCreative());
                Integer updateStatus = null;
                if (needAudit) {
                    updateStatus = PageGroupMappingStatusEnum.WAIT_AUDIT.getCode();
                }
                bqf.update(mgkLandingPageGroupMapping)
                        .set(mgkLandingPageGroupMapping.isEnable, updateDto.getIsEnable())
                        .setIfNotNull(mgkLandingPageGroupMapping.mappingStatus, updateStatus)
                        .set(mgkLandingPageGroupMapping.pageUrl, updateDto.getPageUrl())
                        .setIfNotNull(mgkLandingPageGroupMapping.containerPageUrl, updateDto.getContainerPageUrl())
                        .setIfNotNull(mgkLandingPageGroupMapping.containerPageId, updateDto.getContainerPageId())
                        .where(mgkLandingPageGroupMapping.groupId.eq(saveDto.getGroupId()))
                        .where(mgkLandingPageGroupMapping.pageId.eq(updateDto.getPageId()))
                        .execute();
                return needAudit;
            }).collect(Collectors.toList());
            updateNeedAudit = pageNeedAuditList.stream()
                    .filter(Boolean.TRUE::equals)
                    .findFirst()
                    .orElse(false);
        }

        // 当前无关联创意 不推审
        if (!Boolean.TRUE.equals(saveDto.getHasAuditCreative())) {
            return false;
        }
        // 落地页本身是否可用 前面已经校验过了 走到这里不需要再校验了 但是落地页依然可能是待审核状态 落地页本身审核的时候有相关逻辑
        // 有新增落地页 或者更新的落地页关联关系中有开关为启用且审核拒绝/启用且无关联创意
        return insertNeedAudit || updateNeedAudit;
    }

    private void fillMappingMgkPageUrl(List<LandingPageGroupMappingListDto> mappingList) {
        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }
        List<Long> pageIdList = mappingList.stream()
                .map(LandingPageGroupMappingListDto::getPageId)
                .collect(Collectors.toList());
        List<MgkLandingPageDto> pageDtos = mgkLandingPageService.getLandingPageDtosInPageIds(pageIdList);
        Map<Long, Integer> pageHeadMap = pageDtos.stream()
                .collect(Collectors.toMap(MgkLandingPageDto::getPageId, MgkLandingPageDto::getHeader));

        Map<Long, MgkLandingPageDto> templatePageMap = mgkLandingPageService.getMgkAppletTemplatePageMapByPageIds(pageIdList);

        mappingList.forEach(mappingDto -> {
            Integer header = pageHeadMap.getOrDefault(mappingDto.getPageId(), 0);
            mappingDto.setPageUrl(landingPageUrlProc.getPcLaunchUrlWithMacroParamsForPageGroup(mappingDto.getPageId(), header));
            MgkLandingPageDto templatePageDto = templatePageMap.get(mappingDto.getPageId());
            Long templatePageId = Objects.isNull(templatePageDto) ? 0L : templatePageDto.getPageId();
            Integer templatePageHeader = Objects.isNull(templatePageDto) ? null : templatePageDto.getHeader();
            String templatePageUrl = Objects.isNull(templatePageDto) ? "" :
                    landingPageUrlProc.getPcTemplateLaunchUrlWithMacroParamsForPageGroup(templatePageDto.getPageId(), templatePageHeader);
            mappingDto.setContainerPageUrl(templatePageUrl);
            mappingDto.setContainerPageId(templatePageId);
        });
    }

    private void deleteMappingByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        bqf.delete(mgkLandingPageGroupMapping)
                .where(mgkLandingPageGroupMapping.id.in(ids))
                .execute();
    }

    public void deletePageGroupMapping(Long pageGroupId) {
        bqf.delete(mgkLandingPageGroupMapping)
                .where(mgkLandingPageGroupMapping.groupId.eq(pageGroupId))
                .execute();
    }

    public void updateTemplatePageUrl(List<Long> idList, Long pageId) {
        if (!Utils.isPositive(pageId) || CollectionUtils.isEmpty(idList)) {
            return;
        }

        Map<Long, MgkLandingPageDto> publishedTemplatePageMap =
                mgkLandingPageService.getMgkAppletTemplatePageMapByPageIds(Lists.newArrayList(pageId));
        MgkLandingPageDto templatePageDto = publishedTemplatePageMap.get(pageId);
        Long containerPageId = Objects.isNull(templatePageDto) ? 0L : templatePageDto.getPageId();
        String containerPageUrl = Objects.isNull(templatePageDto) ? "" :
                landingPageUrlProc.getPcTemplateLaunchUrlWithMacroParamsForPageGroup(containerPageId, templatePageDto.getHeader());

        bqf.update(mgkLandingPageGroupMapping)
                .set(mgkLandingPageGroupMapping.containerPageUrl, containerPageUrl)
                .set(mgkLandingPageGroupMapping.containerPageId, containerPageId)
                .where(mgkLandingPageGroupMapping.id.in(idList))
                .execute();

        log.info("removeTemplatePageUrl pageId:{}, updateIdList:{}", pageId, idList);
    }

    public List<LandingPageGroupMappingDto> queryPageGroupMappingDto(QueryLandingPageGroupMappingDto queryDto) {
        if (CollectionUtils.isEmpty(queryDto.getStatusList())) {
            queryDto.setStatusList(PageGroupMappingStatusEnum.NOT_DELETED_STATUS_LIST);
        }
        List<LandingPageGroupMappingDto> mappingDtos = bqf.selectFrom(mgkLandingPageGroupMapping)
                .whereIfNotEmpty(queryDto.getPageGroupIdList(), mgkLandingPageGroupMapping.groupId::in)
                .whereIfNotEmpty(queryDto.getPageIdList(), mgkLandingPageGroupMapping.pageId::in)
                .whereIfNotNull(queryDto.getGroupSource(), mgkLandingPageGroupMapping.groupSource::eq)
                .whereIfNotNull(queryDto.getAccountId(), mgkLandingPageGroupMapping.accountId::eq)
                .whereIfNotEmpty(queryDto.getStatusList(), mgkLandingPageGroupMapping.mappingStatus::in)
                .where(mgkLandingPageGroupMapping.isDeleted.eq(IsDeleted.VALID.getCode()))
                .orderBy(mgkLandingPageGroupMapping.id.asc())
                .fetch(LandingPageGroupMappingDto.class);

        if(CollectionUtils.isEmpty(mappingDtos)){
            return new ArrayList<>();
        }
        List<Long> pageIds = mappingDtos.stream().map(LandingPageGroupMappingDto::getPageId)
                .collect(Collectors.toList());
        Map<Long, List<Integer>> appPackageIdMap =
                mgkLandingPageService.getAppPackageIdMapInPageIds(pageIds);
        mappingDtos = mappingDtos.stream().peek(mappingDto->{
            List<Integer> appPackageIdList = appPackageIdMap.getOrDefault(mappingDto.getPageId(), new ArrayList<>());
            mappingDto.setAppPackageIdList(appPackageIdList);
        }).collect(Collectors.toList());
        return mappingDtos;
    }

    public List<LandingPageGroupMappingDto> queryPageGroupMappingList(Long groupId) {
        QueryLandingPageGroupMappingDto queryDto = QueryLandingPageGroupMappingDto.builder()
                .pageGroupIdList(Lists.newArrayList(groupId))
                .build();
        return queryPageGroupMappingDto(queryDto);
    }


    public void auditPageGroupMappingList(Long pageGroupId, List<LandingPageGroupMappingAuditDto> mappingAuditDtoList) {
        if (CollectionUtils.isEmpty(mappingAuditDtoList)) {
            return;
        }

        mappingAuditDtoList.forEach(auditDto -> {
            Assert.isTrue(PageGroupMappingStatusEnum.AUDIT_RESULT_STATUS_LIST.contains(auditDto.getStatus()),
                    "审核结果必须是审核通过/审核驳回");
        });

        mappingAuditDtoList.forEach(auditDto -> {
            bqf.update(mgkLandingPageGroupMapping)
                    .set(mgkLandingPageGroupMapping.mappingStatus, auditDto.getStatus())
                    .set(mgkLandingPageGroupMapping.reason, auditDto.getReason())
                    .where(mgkLandingPageGroupMapping.groupId.eq(pageGroupId))
                    .where(mgkLandingPageGroupMapping.pageId.eq(auditDto.getPageId()))
                    .where(mgkLandingPageGroupMapping.mappingStatus.in(PageGroupMappingStatusEnum.NOT_DELETED_STATUS_LIST))
                    .where(mgkLandingPageGroupMapping.isDeleted.eq(IsDeleted.VALID.getCode()))
                    .execute();
        });
    }

    public void batchRejectPageGroupMappingStatus(List<Long> pageIdList, List<Long> pageGroupIdList, String reason) {
        if (CollectionUtils.isEmpty(pageIdList)) {
            return;
        }
        bqf.update(mgkLandingPageGroupMapping)
                .set(mgkLandingPageGroupMapping.mappingStatus, PageGroupMappingStatusEnum.AUDIT_REJECT.getCode())
                .set(mgkLandingPageGroupMapping.reason, reason)
                .where(mgkLandingPageGroupMapping.pageId.in(pageIdList))
                .where(mgkLandingPageGroupMapping.groupId.in(pageGroupIdList))
                .where(mgkLandingPageGroupMapping.mappingStatus.in(PageGroupMappingStatusEnum.NOT_DELETED_STATUS_LIST))
                .where(mgkLandingPageGroupMapping.isDeleted.eq(IsDeleted.VALID.getCode()))
                .execute();
    }

    public void batchUpdatePageGroupMappingUsing(List<Long> pageGroupIdList) {
        if (CollectionUtils.isEmpty(pageGroupIdList)) {
            return;
        }
        bqf.update(mgkLandingPageGroupMapping)
                .set(mgkLandingPageGroupMapping.mappingStatus, PageGroupMappingStatusEnum.WAIT_AUDIT.getCode())
                .where(mgkLandingPageGroupMapping.groupId.in(pageGroupIdList))
                .where(mgkLandingPageGroupMapping.mappingStatus.in(PageGroupMappingStatusEnum.NOT_DELETED_STATUS_LIST))
                .where(mgkLandingPageGroupMapping.isDeleted.eq(IsDeleted.VALID.getCode()))
                .execute();
    }


    /**
        SELECT * from mgk_landing_page_group_mapping
        where group_source = 1
        and account_id !=3
        看的都是https的，这个审核拒绝应该不用管了
        把3的http改成https就行
    */
    public List<Integer> notSupportHttpsAccountIds = Arrays.asList(3);
    public void refreshPageUrlHttp2https(boolean write){
        // mgk_landing_page_group_mapping表，100+数据,一次查完
        List<MgkLandingPageGroupMappingQueryDSLPo> pos = bqf.selectFrom(mgkLandingPageGroupMapping)
                .where(mgkLandingPageGroupMapping.isDeleted.eq(0))
                .where(mgkLandingPageGroupMapping.groupSource.eq(1))
                .orderBy(mgkLandingPageGroupMapping.id.asc())
                .fetch(MgkLandingPageGroupMappingQueryDSLPo.class);
        // 把group_source为1-三方落地页来源的数据
        for(MgkLandingPageGroupMappingQueryDSLPo po : pos){
            if(StringUtils.startsWith(po.getPageUrl(), "http:")) {
                log.info("http2https2 mgk_landing_page_group_mapping id:" + po.getId() + "," + po.getPageUrl());
                if(notSupportHttpsAccountIds.contains(po.getAccountId())){
                    // todo 在名单内，审核拒绝
                    if(write) {

                    }
                } else {
                    if(write) {
                        // 不在，把http头改成https头
                        String newPageUrl = po.getPageUrl().replaceFirst("http:", "https:");
                        // 执行更新
                        bqf.update(mgkLandingPageGroupMapping)
                                .set(mgkLandingPageGroupMapping.pageUrl, newPageUrl)
                                .where(mgkLandingPageGroupMapping.id.eq(po.getId()))
                                .execute();
                    }
                }
            }
        }
    }

}
