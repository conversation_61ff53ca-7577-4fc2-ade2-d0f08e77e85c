package com.bilibili.mgk.platform.common;

public enum WxApiTypeEnum {

    GET_ACCESS_TOKEN("/cgi-bin/gettoken", "GET"),

    //获取第三方应用凭证
    //https://developer.work.weixin.qq.com/document/path/90600
    GET_SUITE_ACCESS_TOKEN("/cgi-bin/service/get_suite_token", "POST"),

    //用于获取预授权码。预授权码用于企业授权时的第三方服务商安全验证。
    SET_PRE_AUTH_CODE("/cgi-bin/service/set_session_info?suite_access_token=SUITE_ACCESS_TOKEN", "POST"),

    //用于获取预授权码。预授权码用于企业授权时的第三方服务商安全验证。
    GET_PRE_AUTH_CODE("/cgi-bin/service/get_pre_auth_code?suite_access_token=SUITE_ACCESS_TOKEN", "GET"),

    //该API用于使用临时授权码换取授权方的永久授权码，并换取授权信息、企业access_token，临时授权码一次有效
    //https://developer.work.weixin.qq.com/document/path/90603#14942
    GET_PERMANENT_CODE("/cgi-bin/service/get_permanent_code?suite_access_token=SUITE_ACCESS_TOKEN", "POST"),

    //获取企业凭证
    GET_ENTERPRISE_ACCESS_TOKEN("/cgi-bin/service/get_corp_token?suite_access_token=SUITE_ACCESS_TOKEN", "POST"),

    //用于获取用户信息
    GET_FOLLOW_USER("/cgi-bin/externalcontact/get_follow_user_list?access_token=ACCESS_TOKEN", "GET"),

    //获取访问用户身份
    GET_USER_INFO("/cgi-bin/service/auth/getuserinfo3rd", "GET"),

    //企业可通过此接口，根据外部联系人的userid，拉取客户详情
    GET_OPEN_USER_INFO("/cgi-bin/externalcontact/get", "GET"),

    //创建获客链接助手
    CREATE_CUSTOMER_ACQUISITION_LINK("/cgi-bin/externalcontact/customer_acquisition/create_link?access_token=ACCESS_TOKEN", "POST"),

    //编辑获客链接助手
    UPDATE_CUSTOMER_ACQUISITION_LINK("/cgi-bin/externalcontact/customer_acquisition/update_link?access_token=ACCESS_TOKEN", "POST"),

    GET_CUSTOMER_ACQUISITION_LINK_LIST("/cgi-bin/externalcontact/customer_acquisition/list_link?access_token=ACCESS_TOKEN", "POST"),

    GET_CUSTOMER_ACQUISITION_LINK_DETAIL("/cgi-bin/externalcontact/customer_acquisition/get?access_token=ACCESS_TOKEN", "POST"),

    GET_CUSTOMER_ACQUISITION_CUSTOMER_INFOS("/cgi-bin/externalcontact/customer_acquisition/customer?access_token=ACCESS_TOKEN", "POST"),

    DEL_CUSTOMER_ACQUISITION_LINK("/cgi-bin/externalcontact/customer_acquisition/delete_link?access_token=ACCESS_TOKEN", "POST"),
    ;

    private final String url;
    private final String method;

    WxApiTypeEnum(String url, String method) {
        this.url = url;
        this.method = method;
    }

    public String getUrl() {
        return this.url;
    }

    public String getMethod() {
        return this.method;
    }
}
