package com.bilibili.mgk.platform.api.es.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: ESMgkHotLandingPageDto
 * @author: gaoming
 * @date: 2021/11/10
 * @version: 1.0
 * @description:
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ESMgkHotLandingPageDto implements Serializable {


    private static final long serialVersionUID = 5268470596970534032L;

    /**
     * 数据id
     */
    private String data_id;

    /**
     * 转换数
     */
    private Long conv_cnt;

    /**
     * 点击数量
     */
    private Long click;

    /**
     * 一级行业
     */
    private String commerce_category_first_name;

    /**
     * 一级行业排行
     */
    private String commerce_category_first_name_cvr_rank;

    /**
     * cvr排行
     */
    private String cvr_rank;

    /**
     * 落地页id
     */
    private String page_id;

    /**
     * 平台名称
     */
    private String platform_name;

    /**
     * 平台名称cvr排行
     */
    private String platform_name_cvr_rank;

    /**
     * 日期
     */
    private String log_date;

    /**
     * 时间分区 1d 7d 30d
     */
    private String data_period;
}
