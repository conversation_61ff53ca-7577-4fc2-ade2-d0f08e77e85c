package com.bilibili.collage.biz.service;

import com.bilibili.collage.api.dto.CollageCoverDto;
import com.bilibili.collage.api.dto.QueryCollageCoverDto;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.bilibili.collage.biz.dao.MgkCollageCoverDao;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * @file: CollageCoverServiceDelegateTest
 * @author: gaoming
 * @date: 2020/12/02
 * @version: 1.0
 * @description:
 **/
public class CollageCoverServiceDelegateTest extends BaseMockitoTest {
    @InjectMocks
    private CollageCoverServiceDelegate collageCoverServiceDelegate;

    @Mock
    private MgkCollageCoverDao mgkCollageCoverDao;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testInsert() {
        collageCoverServiceDelegate.insert(operator, CollageCoverDto.builder()
                .coverMd5("md5")
                .coverSize(1)
                .coverUrl("http://bilibili.com")
                .ratio(1)
                .height(1)
                .width(1)
                .id(1)
                .objType(1)
                .objId(1)
                .coverId(1L)
                .build());
    }

    @Test
    public void testGetCoverDtos() {
        collageCoverServiceDelegate.getCoverDtos(QueryCollageCoverDto.builder()
                .objTypes(Lists.newArrayList(1))
                .objIds(Lists.newArrayList(1))
                .build());
    }

    @Test
    public void testUpdate() {
        collageCoverServiceDelegate.update(operator, CollageCoverDto.builder()
                .coverId(1L)
                .objId(1)
                .objType(1)
                .id(1)
                .width(1)
                .height(1)
                .ratio(1)
                .coverUrl("http://www.bilibili.com")
                .coverSize(1)
                .coverMd5("md5")
                .build());
    }
}