package com.bilibili.mgk.platform.common.enums.page;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum OptTypeEnum {

    DEL(0, "删除"),

    REJECT(1, "拒审"),

    DOWNLINE(2, "下线")

    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static OptTypeEnum getByCode(int code) {
        for (OptTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code OptTypeEnum " + code);
    }


}
