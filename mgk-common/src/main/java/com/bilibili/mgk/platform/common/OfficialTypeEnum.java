package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/06/24
 **/
@AllArgsConstructor
public enum OfficialTypeEnum {
    UP(1, "", "", "", "UP 主认证"),
    Identity(2, "", "", "", "身份认证"),
    Enterprise(3, "", "", "", "企业认证"),
    Government(4, "", "", "", "政府认证"),
    Media(5, "", "", "", "媒体认证"),
    Other(6, "", "", "", "其他认证"),
    Vertical(7, "", "", "", "垂直认证");
    @Getter
    private Integer role;
    @Getter
    private String title;
    @Getter
    private String desc;
    @Getter
    private String type;
    @Getter
    private String descEnum;
}
