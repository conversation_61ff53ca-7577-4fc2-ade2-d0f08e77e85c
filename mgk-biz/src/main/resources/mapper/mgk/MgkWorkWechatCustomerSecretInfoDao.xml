<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkWorkWechatCustomerSecretInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="secret" jdbcType="VARCHAR" property="secret" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, customer_id, secret, is_deleted, expire_time, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_work_wechat_customer_secret_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_work_wechat_customer_secret_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mgk_work_wechat_customer_secret_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPoExample">
    delete from mgk_work_wechat_customer_secret_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_work_wechat_customer_secret_info (customer_id, secret, is_deleted, 
      expire_time, ctime, mtime
      )
    values (#{customerId,jdbcType=INTEGER}, #{secret,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{expireTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_work_wechat_customer_secret_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="secret != null">
        secret,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="secret != null">
        #{secret,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPoExample" resultType="java.lang.Long">
    select count(*) from mgk_work_wechat_customer_secret_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_work_wechat_customer_secret_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.secret != null">
        secret = #{record.secret,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.expireTime != null">
        expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_work_wechat_customer_secret_info
    set id = #{record.id,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      secret = #{record.secret,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPo">
    update mgk_work_wechat_customer_secret_info
    <set>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="secret != null">
        secret = #{secret,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPo">
    update mgk_work_wechat_customer_secret_info
    set customer_id = #{customerId,jdbcType=INTEGER},
      secret = #{secret,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_work_wechat_customer_secret_info (customer_id, secret, is_deleted, 
      expire_time, ctime, mtime
      )
    values (#{customerId,jdbcType=INTEGER}, #{secret,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{expireTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      customer_id = values(customer_id),
      secret = values(secret),
      is_deleted = values(is_deleted),
      expire_time = values(expire_time),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_work_wechat_customer_secret_info
      (customer_id,secret,is_deleted,expire_time,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.customerId,jdbcType=INTEGER},
        #{item.secret,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.expireTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_work_wechat_customer_secret_info
      (customer_id,secret,is_deleted,expire_time,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.customerId,jdbcType=INTEGER},
        #{item.secret,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.expireTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      customer_id = values(customer_id),
      secret = values(secret),
      is_deleted = values(is_deleted),
      expire_time = values(expire_time),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkWorkWechatCustomerSecretInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_work_wechat_customer_secret_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="secret != null">
        secret,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="secret != null">
        #{secret,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="customerId != null">
        customer_id = values(customer_id),
      </if>
      <if test="secret != null">
        secret = values(secret),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="expireTime != null">
        expire_time = values(expire_time),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>