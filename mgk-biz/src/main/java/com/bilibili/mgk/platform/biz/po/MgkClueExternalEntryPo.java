package com.bilibili.mgk.platform.biz.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkClueExternalEntryPo {
    private Long id;
    private Integer accountId;
    private Integer creativeId;
    private String trackId;
    private String label;
    private String value;
    private String type;
    private Integer isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
}
