<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.material.center.repository.mysql.MgkAgreementPermissionMapper">

  <resultMap id="BaseResultMap" type="com.bilibili.mgk.material.center.service.agreement.model.AgreementPermission">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="agreementId" column="agreement_id" jdbcType="BIGINT"/>
    <result property="permissionKey" column="permission_key" jdbcType="VARCHAR"/>
    <result property="agreementName" column="agreement_name" jdbcType="VARCHAR"/>
    <result property="accountId" column="account_id" jdbcType="BIGINT"/>
    <result property="agreed" column="agreed" jdbcType="TINYINT"/>
    <result property="agreeTime" column="agree_time" jdbcType="TIMESTAMP"/>
    <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
    <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    <result property="deleted" column="deleted" jdbcType="TINYINT"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,agreement_id,permission_key,
        agreement_name,account_id,agreed,
        agree_time,ctime,mtime,
        deleted
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mgk_agreement_permission
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByAgreementIdAndPermissionKey"
    resultType="com.bilibili.mgk.material.center.service.agreement.model.AgreementPermission" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List"/>
    from mgk_agreement_permission
    where
    agreement_id = #{agreementId,jdbcType=BIGINT}
    and permission_key = #{permissionKey,jdbcType=VARCHAR}

  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from mgk_agreement_permission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.agreement.model.AgreementPermission"
    useGeneratedKeys="true">
    insert into mgk_agreement_permission
    ( id, agreement_id, permission_key
    , agreement_name, account_id, agreed
    , agree_time, ctime, mtime
    , deleted)
    values ( #{id,jdbcType=BIGINT}, #{agreementId,jdbcType=BIGINT}, #{permissionKey,jdbcType=VARCHAR}
           , #{agreementName,jdbcType=VARCHAR}, #{accountId,jdbcType=BIGINT}, #{agreed,jdbcType=TINYINT}
           , #{agreeTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
           , #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.agreement.model.AgreementPermission"
    useGeneratedKeys="true">
    insert into mgk_agreement_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="agreementId != null">agreement_id,</if>
      <if test="permissionKey != null">permission_key,</if>
      <if test="agreementName != null">agreement_name,</if>
      <if test="accountId != null">account_id,</if>
      <if test="agreed != null">agreed,</if>
      <if test="agreeTime != null">agree_time,</if>
      <if test="ctime != null">ctime,</if>
      <if test="mtime != null">mtime,</if>
      <if test="deleted != null">deleted,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=BIGINT},</if>
      <if test="agreementId != null">#{agreementId,jdbcType=BIGINT},</if>
      <if test="permissionKey != null">#{permissionKey,jdbcType=VARCHAR},</if>
      <if test="agreementName != null">#{agreementName,jdbcType=VARCHAR},</if>
      <if test="accountId != null">#{accountId,jdbcType=BIGINT},</if>
      <if test="agreed != null">#{agreed,jdbcType=TINYINT},</if>
      <if test="agreeTime != null">#{agreeTime,jdbcType=TIMESTAMP},</if>
      <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
      <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
      <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="com.bilibili.mgk.material.center.service.agreement.model.AgreementPermission">
    update mgk_agreement_permission
    <set>
      <if test="agreementId != null">
        agreement_id = #{agreementId,jdbcType=BIGINT},
      </if>
      <if test="permissionKey != null">
        permission_key = #{permissionKey,jdbcType=VARCHAR},
      </if>
      <if test="agreementName != null">
        agreement_name = #{agreementName,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="agreed != null">
        agreed = #{agreed,jdbcType=TINYINT},
      </if>
      <if test="agreeTime != null">
        agree_time = #{agreeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="com.bilibili.mgk.material.center.service.agreement.model.AgreementPermission">
    update mgk_agreement_permission
    set agreement_id   = #{agreementId,jdbcType=BIGINT},
        permission_key = #{permissionKey,jdbcType=VARCHAR},
        agreement_name = #{agreementName,jdbcType=VARCHAR},
        account_id     = #{accountId,jdbcType=BIGINT},
        agreed         = #{agreed,jdbcType=TINYINT},
        agree_time     = #{agreeTime,jdbcType=TIMESTAMP},
        ctime          = #{ctime,jdbcType=TIMESTAMP},
        mtime          = #{mtime,jdbcType=TIMESTAMP},
        deleted        = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
