package com.bilibili.mgk.material.center.service.aigc.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 审核拒绝的原素材
 *
 * <AUTHOR>
 * @desc
 * @date 2025/4/7
 */
@Data
@Accessors(chain = true)
public class RejectedRecordWithAigcMaterial implements SnakeCaseBody {


    private String cursor;

    private RejectedMaterialAigcRecord rejectedMaterial;


    private List<AigcSuggestMaterial> aigcResults;


}
