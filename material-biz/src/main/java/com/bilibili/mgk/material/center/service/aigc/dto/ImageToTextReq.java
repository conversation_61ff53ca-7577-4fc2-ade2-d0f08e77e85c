package com.bilibili.mgk.material.center.service.aigc.dto;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/18
 */
@Data
@Accessors(chain = true)
public class ImageToTextReq implements SnakeCaseBody {


    @ApiModelProperty("图片地址")
    private String imgUrl;


    public void validate() {

        Assert.notNull(imgUrl, "图片地址不能为空");

    }
}
