package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.passport.api.dto.*;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeCountDto;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeStatsReplyDto;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeStatsReqDto;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeStatusDto;
import com.bilibili.mgk.platform.api.archive.service.IMgkCmArchiveService;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageAvidService;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @file: ArchiveLikeServiceDelegate
 * @author: gaoming
 * @date: 2021/12/14
 * @version: 1.0
 * @description:
 **/
@Service
public class ArchiveLikeServiceDelegate {

    @Autowired
    private IPassportService passportService;

    @Autowired
    private IMgkCmArchiveService cmArchiveService;

    @Autowired
    private IMgkLandingPageAvidService mgkLandingPageAvidService;

    public MgkArchiveLikeCountDto getArchiveLikeCount(Long avid) throws ServiceException {
        ThumbUpLikeStatsReplyDto thumbUpLikeStats = passportService.getThumbUpLikeStats(ThumbUpLikeStatsReqDto.builder()
                .business(MgkConstants.MGK_ARCHIVE_BUSINESS)
                .originId(0L)
                .messageIds(Lists.newArrayList(avid))
                .build());

        return MgkArchiveLikeCountDto.builder()
                .avid(avid)
                .likeNumber(thumbUpLikeStats == null ? 0 : thumbUpLikeStats.getLikeNumber())
                .dislikeNumber(thumbUpLikeStats == null ? 0 : thumbUpLikeStats.getDislikeNumber())
                .build();
    }

    public List<MgkArchiveLikeCountDto> getArchivesLikeCount(List<Long> avids) throws ServiceException {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyList();
        }
        Map<Long, ThumbUpLikeStatsReplyDto> thumbUpLikeStatsMap = passportService.getThumbUpLikeStatsMap(ThumbUpLikeStatsReqDto.builder()
                .business(MgkConstants.MGK_ARCHIVE_BUSINESS)
                .originId(0L)
                .messageIds(avids)
                .build());
        if (CollectionUtils.isEmpty(thumbUpLikeStatsMap)) {
            return Collections.emptyList();
        }
        return thumbUpLikeStatsMap.keySet().stream().map(avid -> {
            return MgkArchiveLikeCountDto.builder()
                    .avid(avid)
                    .likeNumber(thumbUpLikeStatsMap.get(avid).getLikeNumber())
                    .dislikeNumber(thumbUpLikeStatsMap.get(avid).getDislikeNumber())
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 获取用户点赞点踩信息
     *
     * @param reqDto
     * @return MgkArchiveLikeStatsReplyDto (not null)
     */
    public MgkArchiveLikeStatsReplyDto archiveLikeStats(MgkArchiveLikeStatsReqDto reqDto) throws ServiceException {
        // 获取稿件的点赞点踩数
        MgkArchiveLikeCountDto likeCount = getArchiveLikeCount(reqDto.getAvid());
        // 获取稿件的点赞点踩状态
        MgkArchiveLikeStatusDto statusDto = Utils.isPositive(reqDto.getMid()) ? getArchiveLikeStatusWithMid(reqDto) : getArchiveLikeStatusWithBuvid(reqDto);

        // 稿件的信息
        MgkArchiveLikeStatsReplyDto replyDto = MgkArchiveLikeStatsReplyDto.builder()
                .avid(reqDto.getAvid())
                .likeNumber(likeCount.getLikeNumber())
                .dislikeNumber(likeCount.getDislikeNumber())
                .hasLike(statusDto.getHasLike())
                .hasDislike(statusDto.getHasDislike())
                .build();

        // 获取稿件对应的视频
        List<Integer> bizIds = cmArchiveService.getBizIdsByAvid(replyDto.getAvid());
        if (!CollectionUtils.isEmpty(bizIds)) {
            // 获取视频的点赞点踩数
            Map<Long, ThumbUpLikeStatsReplyDto> thumbUpLikeStatsMap = passportService.getThumbUpLikeStatsMap(ThumbUpLikeStatsReqDto.builder()
                    .business(MgkConstants.MGK_DYNAMIC_BUSINESS)
                    .originId(0L)
                    .messageIds(bizIds.stream().map(Long::valueOf).collect(Collectors.toList()))
                    .build());

            if (!CollectionUtils.isEmpty(thumbUpLikeStatsMap)) {
                long likeNumber = thumbUpLikeStatsMap.values().stream().mapToLong(ThumbUpLikeStatsReplyDto::getLikeNumber).sum();
                long disLikeNumber = thumbUpLikeStatsMap.values().stream().mapToLong(ThumbUpLikeStatsReplyDto::getDislikeNumber).sum();
                replyDto.setLikeNumber(replyDto.getLikeNumber() + likeNumber);
                replyDto.setDislikeNumber(replyDto.getDislikeNumber() + disLikeNumber);
            }
        }

        return replyDto;
    }

    private MgkArchiveLikeStatusDto getArchiveLikeStatusWithBuvid(MgkArchiveLikeStatsReqDto reqDto) throws ServiceException {
        ThumbUpHasLikeNoLoginReplyDto hasLike = passportService.getThumbUpHasLikeNoLogin(ThumbUpHasLikeNoLoginReqDto.builder()
                .business(MgkConstants.MGK_ARCHIVE_BUSINESS)
                .messageIds(Lists.newArrayList(reqDto.getAvid()))
                .buvid(reqDto.getBuvid())
                .build());
        return MgkArchiveLikeStatusDto.builder()
                .avid(reqDto.getAvid())
                .hasLike(hasLike == null ? 0 : hasLike.getHasLike())
                .hasDislike(hasLike == null ? 0 : hasLike.getHasDislike())
                .build();
    }

    private MgkArchiveLikeStatusDto getArchiveLikeStatusWithMid(MgkArchiveLikeStatsReqDto reqDto) throws ServiceException {
        ThumbUpHasLikeReplyDto hasLike = passportService.getThumbUpHasLike(ThumbUpHasLikeReqDto.builder()
                .business(MgkConstants.MGK_ARCHIVE_BUSINESS)
                .messageIds(Lists.newArrayList(reqDto.getAvid()))
                .mid(reqDto.getMid())
                .build());
        return MgkArchiveLikeStatusDto.builder()
                .avid(reqDto.getAvid())
                .hasLike(hasLike == null ? 0 : hasLike.getHasLike())
                .hasDislike(hasLike == null ? 0 : hasLike.getHasDislike())
                .build();
    }

    public MgkArchiveLikeStatsReplyDto getArchiveLikeStats(Long pageId, Long mid, String buvid) throws ServiceException {
        MgkArchiveLikeStatsReplyDto initDto = MgkArchiveLikeStatsReplyDto.builder().avid(0L).likeNumber(0L).dislikeNumber(0L).hasLike(0).hasDislike(0).build();
        if (!Utils.isPositive(pageId)) {
            return initDto;
        }
        if (!Utils.isPositive(mid) && Strings.isNullOrEmpty(buvid)) {
            return initDto;
        }
        Long avid = mgkLandingPageAvidService.getPageIdToAvidMappingInRedis(pageId);
        if (!Utils.isPositive(avid)) {
            return initDto;
        }
        return archiveLikeStats(MgkArchiveLikeStatsReqDto.builder().avid(avid).buvid(buvid).mid(mid).build());
    }
}
