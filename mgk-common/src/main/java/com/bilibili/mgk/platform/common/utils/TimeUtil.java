package com.bilibili.mgk.platform.common.utils;

import com.bilibili.adp.common.util.Utils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Created by xiongyan  on 2022/12/12.
 */
public class TimeUtil {

    //"yyyymmdd"
    public static LocalDate baseDateStrToLocalDate(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        int year = Integer.parseInt(str.substring(0, 4));
        int month = Integer.parseInt(str.substring(4, 6));
        int dayOfMonth = Integer.parseInt(str.substring(6, 8));
        return LocalDate.of(year, month, dayOfMonth);
    }

    //yyyy-mm-dd
    public static LocalDate isoDateStrToLocalDate(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        int year = Integer.parseInt(str.substring(0, 4));
        int month = Integer.parseInt(str.substring(5, 7));
        int dayOfMonth = Integer.parseInt(str.substring(8, 10));
        return LocalDate.of(year, month, dayOfMonth);
    }

    public static LocalDate dateToLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalDate();
    }

    public static String dateToDateStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    public static List<LocalDate> baseDateStrToLocalDate(List<String> dates) {
        List<LocalDate> localDates = Lists.newArrayList();
        if (dates == null || dates.isEmpty()) {
            return localDates;
        }
        for (String date : dates) {
            LocalDate localDate = baseDateStrToLocalDate(date);
            if (localDate != null) {
                localDates.add(localDate);
            }
        }
        return localDates;
    }

    public static String localDateToBasicStr(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.BASIC_ISO_DATE;
        return date.format(formatter);
    }

    public static String localDateToISOStr(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;
        return date.format(formatter);
    }

    public static Timestamp localDateToTimestamp(LocalDate date){
        if(date == null){
            return null;
        }
        return new Timestamp(date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    public static Timestamp localDateTimeToTimestamp(LocalDateTime time){
        if(time == null){
            return null;
        }
        return new Timestamp(time.toInstant(ZoneOffset.of("+8")).toEpochMilli());
    }

    //yyyy-MM-dd
    public static Timestamp isoStrToTimestamp(String str){
        if(StringUtils.isEmpty(str)){
            return null;
        }
        LocalDate date = isoDateStrToLocalDate(str);
        if(date == null){
            return null;
        }
        return new Timestamp(date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    //yyyy-MM-dd HH:mm:ss
    public static Timestamp isoTimeStr2Timestamp(String str){
        if(StringUtils.isEmpty(str)){
            return null;
        }
        return Timestamp.valueOf(str);
    }

    public static String timestampToIsoDateStr(Timestamp date) {
        if(date == null){
            return null;
        }
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(date);
    }

    public static String timestampToHourStr(Timestamp date) {
        if(date == null){
            return null;
        }
        LocalDateTime time = TimeUtil.timestampToLocalDateTime(date);
        return String.format("%02d", time.getHour()) + ":" + String.format("%02d", time.getMinute());
    }

    public static void main(String[] args) {
        System.out.println(TimeUtil.timestampToHourStr(Utils.getBeginOfDay(new Timestamp(System.currentTimeMillis()))));
        System.out.println(TimeUtil.timestampToHourStr(new Timestamp(System.currentTimeMillis())));
    }

    public static String timestampToBasicDateStr(Timestamp date) {
        if(date == null){
            return null;
        }
        DateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        return formatter.format(date);
    }

    public static String timestampToBasicDateStr(LocalDate date) {
        if(date == null){
            return null;
        }
        return date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public static String timestampToIsoTimeStr(Timestamp date) {
        if(date == null){
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(date);
    }

    public static boolean isWeekendDay(Timestamp queryDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(queryDate.getTime()));
        return cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY
                || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY;
    }

    //比较date1(yyyy-MM-dd)和date2的大小，如果date1大则返回1，等于则返回0，否则返回-1
    public static int compareTime(LocalDate date1, LocalDate date2) {
        Assert.notNull(date1, "请求时间不能为空!");
        Assert.notNull(date2, "请求时间不能为空!");
        if(date1.equals(date2)){
            return 0;
        }else if(date1.isAfter(date2)){
            return 1;
        }else {
            return -1;
        }
    }

    //比较开始和结束时间
    public static void checkBeginAndEndTime(Timestamp begin, Timestamp end) {
        Assert.notNull(begin, "开始时间不能为空!");
        Assert.notNull(end, "结束时间不能为空!");
        Assert.isTrue(!begin.after(end) , "开始时间不能大于结束时间");
    }

    /**
     * 时间戳 获取天
     */
    public static int getDay(Long time){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date date=new Date();
        String timeString = sdf.format(time);
        try {
            date = sdf.parse(timeString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    public static int getMonth(Long time){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date date=new Date();
        String timeString = sdf.format(time);
        try {
            date = sdf.parse(timeString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    //返回的时分秒有问题，只限于使用日期
    public static Date getLastDayOfMonth(int month){
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.MONTH , month-1);
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
        return ca.getTime();
    }

    public static Timestamp parseFromRFC3339(String str) {
        Assert.isTrue(!StringUtils.isBlank(str), "param is null");
        DateTime dateTime = new DateTime(str);
        long mils = dateTime.toCalendar(Locale.getDefault()).getTimeInMillis();
        return new Timestamp(mils);
    }

    public static String getRFC3339(long time) {
        DateTime dateTime = new DateTime(time);
        return dateTime.toString();
    }

    public static Timestamp getBeginOfDay(Timestamp timestamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(timestamp);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Timestamp(calendar.getTimeInMillis());
    }

    public static LocalDate timestampToLocalDate(Timestamp time){
        return time.toLocalDateTime().toLocalDate();
    }

    public static LocalDateTime timestampToLocalDateTime(Timestamp time){
        return time.toLocalDateTime();
    }

    public static Date timestampToDate(Timestamp time){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String timeString = sdf.format(time);
        try {
            date = sdf.parse(timeString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static boolean isAllDayHour(Timestamp beginTime, Timestamp endTime){
        Assert.notNull(beginTime, "method isAllDayHour beginTime is null");
        Assert.notNull(endTime, "method isAllDayHour endTime is null");
        List<Integer> hours = new ArrayList<>();
        for(int i = beginTime.toLocalDateTime().getHour();
            i < endTime.toLocalDateTime().getHour() + 1; i++){
            hours.add(i);
        }
        return hours.size() == 24;
    }

    public static Timestamp longToTimestamp(Long time){
        if(Utils.isPositive(time)){
            return new Timestamp(time);
        }
        return null;
    }

    public static Long timestampToLong(Timestamp time){
        if(time != null){
            return time.getTime();
        }
        return 0L;
    }

}
