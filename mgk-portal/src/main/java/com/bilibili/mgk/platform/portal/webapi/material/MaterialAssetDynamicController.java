package com.bilibili.mgk.platform.portal.webapi.material;

import com.bapis.ad.component.GamePlatformType;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.adp.passport.biz.service.GameCenterServiceImpl;
import com.bilibili.collage.api.dto.CollageEnterpriseDto;
import com.bilibili.collage.api.service.ICollageEnterpriseService;
import com.bilibili.mgk.material.center.service.asset.DynamicAssetService;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicId;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicInfo;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicDrawDeleteReq;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicDrawPublishReq;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicPageQuery;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicWaterfallPageQuery;
import com.bilibili.mgk.material.center.service.bluelink.dto.RichTextType;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.WaterfallPage;
import com.bilibili.mgk.platform.api.app_package.dto.QueryResAppPackageDto;
import com.bilibili.mgk.platform.api.app_package.dto.ResAppPackageDto;
import com.bilibili.mgk.platform.biz.service.ResAppPackageServiceImpl;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebCollageService;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.CollageMediaVo;
import com.bilibili.mgk.platform.portal.webapi.material.convertor.VOConvertor;
import com.bilibili.mgk.platform.portal.webapi.material.vo.DynamicBluelinkResolveContentDetailDTO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.MaterialDynamicDetailInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.vavr.control.Try;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 我的资产-动态图文
 *
 * <AUTHOR>
 * @desc
 * @date 2024/6/18
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/asset/dynamic")
@Api(value = "/material/landing_page", tags = "material-center")
public class MaterialAssetDynamicController extends BasicController {


    @Resource
    private DynamicAssetService dynamicAssetService;

    @Resource
    private WebCollageService webCollageService;

    @Resource
    private ICollageEnterpriseService collageEnterpriseService;

    @Resource
    private ResAppPackageServiceImpl resAppPackageService;

    @Resource
    private GameCenterServiceImpl gameCenterService;

//    @Resource
//    private LauQualificationService lauQualificationService;


    @Value("${material.asset.dynamic.account-permission-check.enabled:true}")
    private Boolean permissionCheckEnabled;

    /**
     * https://cm-mng.bilibili.co/mgk/api/web_api/v1/collage/media
     * <p>
     * 此处仅开放新的接口，但是不做新的实现
     *
     * @return
     */
    @ApiOperation(value = "本地上传媒体")
    @RequestMapping(value = "/upload_img", method = RequestMethod.POST)
    public Response<CollageMediaVo> mediaUpload(@ApiIgnore Context context,
            @ApiParam("图片文件 表单传送") @RequestParam("file") MultipartFile multipartFile) throws IOException {

        File file = webCollageService.multipartToFile(multipartFile);
        String url = webCollageService.uploadWithName(file);
        CollageMediaVo collageMediaVo = webCollageService.convertFile2Vo(file, url, 0);
        return Response.SUCCESS(collageMediaVo);
    }




    @ApiOperation(value = "代理发布图文动态")
    @RequestMapping(value = "/publish", method = RequestMethod.POST)
    public Response<MaterialDynamicId> publish(
            @ApiIgnore Context context,
            @RequestBody MaterialDynamicDrawPublishReq req
    ) {

        checkAccountMidPermission(context, req.getUid());

        return Response.SUCCESS(
                dynamicAssetService.publish(req, getOperator(context))
        );
    }


    @ApiOperation(value = "获取 mid 下动态列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response<Pagination<List<MaterialDynamicDetailInfo>>> midDynamics(
            @ApiIgnore Context context,
            @RequestParam(value = "uid") @ApiParam("uid") Long uid,
            @RequestParam(value = "keyword", required = false) @ApiParam("keyword") String keyword,
            @RequestParam(value = "pn", defaultValue = "1") Integer pn,
            @RequestParam(value = "ps", defaultValue = "10") Integer ps,
            @RequestParam(value = "publish_status") @ApiParam("发布状态， 1、审核中 2审核通过 3.审核不通过，4 定时发布中，5定时发布失败") Integer publishStatus) {

        checkAccountMidPermission(context, uid);

        return Response.SUCCESS(dynamicAssetService.list(
                new MaterialDynamicPageQuery()
                        .setKeyword(keyword)
                        .setPn(pn)
                        .setPs(ps)
                        .setUid(uid)
                        .setPublishStatus(publishStatus)
        ).map(this::convertToDetailInfo));
    }


    @ApiOperation(value = "获取 mid 下动态列表，瀑布流下拉，注意该接口下page_size不支持前端设置")
    @RequestMapping(value = "/list/v2", method = RequestMethod.POST)
    public Response<WaterfallPage<MaterialDynamicDetailInfo>> midDynamics(
            @ApiIgnore Context context,
            @RequestBody MaterialDynamicWaterfallPageQuery query
    ) {

        checkAccountMidPermission(context, query.getUid());

        return Response.SUCCESS(dynamicAssetService
                .list(query)
                .map(this::convertToDetailInfo)
        );
    }



    @ApiOperation(value = "删除动态，当前仅支持取消定时发布任务")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Response<MaterialDynamicId> delete(
            @ApiIgnore Context context,
            @RequestBody MaterialDynamicDrawDeleteReq req
    ) {

        checkAccountMidPermission(context, req.getUid());

        return Response.SUCCESS(dynamicAssetService.delete(req));

    }


    private void checkAccountMidPermission(Context context, Long mid) {

        if (!permissionCheckEnabled) {
            return;
        }

        List<CollageEnterpriseDto> dtos = collageEnterpriseService.getEnterpriseByAccount(context.getAccountId());

        boolean permitted = dtos.stream().anyMatch(dto -> Objects.equals(dto.getMid(), mid));

        if (!permitted) {
            throw new IllegalArgumentException("操作不允许，账号无授权");
        }

    }


    private List<MaterialDynamicDetailInfo> convertToDetailInfo(List<MaterialDynamicInfo> infos) {

        List<MaterialDynamicDetailInfo> details = infos.stream().map(info -> VOConvertor.convertor.toDetail(info))
                .collect(Collectors.toList());

        this.fillingAppPackageInfos(details);

        this.fillingGameInfos(details);

//        this.fillingQualifications(details);

        return details;
    }

    private void fillingAppPackageInfos(
            List<MaterialDynamicDetailInfo> dynamics
    ) {

        Try.run(() -> {

            List<DynamicBluelinkResolveContentDetailDTO> links = this.flatmapBluelinks(dynamics);

            List<Integer> appIds = links.stream()
                    .flatMap(link -> {

                        List<Integer> ids = new ArrayList<>();

                        if (link.getAndroidAppPackageId() != null) {
                            ids.add(link.getAndroidAppPackageId());
                        }
                        if (link.getIosAppPackageId() != null) {
                            ids.add(link.getIosAppPackageId());
                        }
                        return ids.stream();
                    })
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(appIds)) {
                return;
            }

            Map<Integer, ResAppPackageDto> r = resAppPackageService.queryList(
                            QueryResAppPackageDto.builder().ids(appIds).build())
                    .stream()
                    .collect(Collectors.toMap(item -> item.getId(), Function.identity(), (a, b) -> b));

            links.stream().forEach(link -> {

                if (link.getAndroidAppPackageId() != null) {
                    Optional.ofNullable(r.get(link.getAndroidAppPackageId()))
                            .ifPresent(app -> link.setAndroidAppPackage(VOConvertor.convertor.toDTO(app)));
                }

                if (link.getIosAppPackageId() != null) {
                    Optional.ofNullable(r.get(link.getIosAppPackageId()))
                            .ifPresent(app -> link.setIosAppPackage(VOConvertor.convertor.toDTO(app)));
                }
            });
        }).onFailure(t -> {
            log.error("Fail to fillingAppPackageInfos", t);
        });


    }


    private List<DynamicBluelinkResolveContentDetailDTO> flatmapBluelinks(List<MaterialDynamicDetailInfo> dynamics) {

        List<DynamicBluelinkResolveContentDetailDTO> links = dynamics.stream()
                .flatMap(d -> d.getRichContent().stream())
                .filter(rich -> rich.getRichTextType() == RichTextType.BLUE_LINK)
                .map(link -> link.getBluelink())
                .collect(Collectors.toList());

        return links;

    }


    private void fillingGameInfos(
            List<MaterialDynamicDetailInfo> dynamics
    ) {

        Try.run(() -> {

            List<DynamicBluelinkResolveContentDetailDTO> bluelinks = this.flatmapBluelinks(dynamics);

            List<Integer> gameBaseIds = bluelinks.stream()
                    .map(bluelink -> bluelink.getGameBaseId())
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(gameBaseIds)) {
                return;
            }

            Map<Integer, GameDto> gameMap = gameCenterService.muiltGetGameInfoByIdPlatform(gameBaseIds,
                            GamePlatformType.ANDROID_VALUE)
                    .stream()
                    .collect(Collectors.toMap(GameDto::getGameBaseId, Function.identity()));

            bluelinks.stream().forEach(bluelink -> {

                if (bluelink.getGameBaseId() != null) {
                    Optional.ofNullable(gameMap.get(bluelink.getGameBaseId()))
                            .ifPresent(game -> bluelink.setGame(VOConvertor.convertor.toDTO(game)));
                }

            });


        }).onFailure(t -> {
            log.error("Fail to fillingGameInfos", t);
        });


    }

//
//
//    private void fillingQualifications( List<MaterialDynamicDetailInfo> dynamics){
//
//        Try.run(() -> {
//
//            List<DynamicBluelinkResolveContentDetailDTO> bluelinks = this.flatmapBluelinks(dynamics);
//
//            List<Integer> qualificationIds = bluelinks.stream()
//                    .flatMap(bluelink -> Optional.ofNullable(bluelink.getQualificationIds())
//                            .orElse(new ArrayList<>()).stream())
//                    .filter(Objects::nonNull)
//                    .distinct()
//                    .collect(Collectors.toList());
//
//            if (CollectionUtils.isEmpty(qualificationIds)) {
//                return;
//            }
//
//            Map<Integer, LauQualificationDto> qulifications = Optional.ofNullable(
//                            lauQualificationService.getList(qualificationIds))
//                    .orElse(new ArrayList<>())
//                    .stream()
//                    .collect(Collectors.toMap(q -> q.getId(), Function.identity()));
//
//
//            bluelinks.stream().forEach(bluelink -> {
//
//                List<CreativeQualificationVo> qualificationVos = Optional.ofNullable(bluelink.getQualificationIds())
//                        .orElse(new ArrayList<>())
//                        .stream()
//                        .map(id -> qulifications.get(id))
//                        .filter(Objects::nonNull)
//                        .map(qua-> VOConvertor.convertor.toDTO(qua))
//                        .collect(Collectors.toList());
//
//                bluelink.setQualifications(qualificationVos);
//            });
//        }).onFailure(t->{
//            log.error("Fail to fillingQualifications", t);
//        });
//
//    }

}
