package com.bilibili.mgk.platform.biz.service.log;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.mgk.platform.api.log.dto.MgkLogOperationDto;
import com.bilibili.mgk.platform.api.log.dto.NewLogOperationDto;
import com.bilibili.mgk.platform.api.log.dto.QueryLogParamDto;
import com.bilibili.mgk.platform.api.log.service.IMgkLogService;
import com.bilibili.mgk.platform.biz.dao.MgkOperationLogDao;
import com.bilibili.mgk.platform.biz.po.MgkOperationLogPo;
import com.bilibili.mgk.platform.biz.po.MgkOperationLogPoExample;
import com.bilibili.mgk.platform.biz.po.MgkOperationLogPoWithBLOBs;
import com.bilibili.mgk.platform.common.LogObjFlagEnum;
import com.bilibili.mgk.platform.common.LogOperateTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/1/19
 **/
@Service
public class MgkLogServiceImpl implements IMgkLogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MgkLogServiceImpl.class);


    @Autowired
    private MgkOperationLogDao mgkOperationLogDao;

    @Override
    public void insertLog(NewLogOperationDto newLogOperationDto) {
        this.validateNewLogOperation(newLogOperationDto);

        MgkOperationLogPoWithBLOBs record = new MgkOperationLogPoWithBLOBs();
        BeanUtils.copyProperties(newLogOperationDto, record);
        record.setOldValue(JSON.toJSONString(newLogOperationDto.getOldValue()));
        record.setNewValue(JSON.toJSONString(newLogOperationDto.getNewValue()));

        try{
            mgkOperationLogDao.insertSelective(record);
        }catch (Exception e){
            LOGGER.error("insertLog.error", e);
        }
    }

    @Override
    public void batchInsertLog(List<NewLogOperationDto> newLogOperationDtos) {
//        newLogOperationDtos.stream().forEach(newLogOperationDto -> {
//            this.validateNewLogOperation(newLogOperationDto);
//        });

        List<MgkOperationLogPoWithBLOBs> operationLogPoWithBLOBs = newLogOperationDtos.stream().map(newLogOperationDto -> {
            MgkOperationLogPoWithBLOBs record = new MgkOperationLogPoWithBLOBs();
            BeanUtils.copyProperties(newLogOperationDto, record);
            record.setOldValue(JSON.toJSONString(newLogOperationDto.getOldValue()));
            record.setNewValue(JSON.toJSONString(newLogOperationDto.getNewValue()));
            record.setIsDeleted(IsDeleted.VALID.getCode());
            return record;
        }).collect(Collectors.toList());

        try{
            mgkOperationLogDao.insertBatch(operationLogPoWithBLOBs);
        }catch (Exception e){
            LOGGER.error("insertLog.error", e);
        }
    }

    @Override
    public PageResult<MgkLogOperationDto> queryOperationLogs(QueryLogParamDto queryLogParamDto) {
        Assert.notNull(queryLogParamDto, "查询日志参数不可为空");
        MgkOperationLogPoExample example = this.getLogExample(queryLogParamDto);
        Long total = mgkOperationLogDao.countByExample(example);
        if (total == 0){
            return PageResult.EMPTY_PAGE_RESULT;
        }
        List<MgkOperationLogPo> pos = mgkOperationLogDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)){
            return PageResult.EMPTY_PAGE_RESULT;
        }
        List<MgkLogOperationDto> dtos = pos.stream().map(this::convertLogPo2Dto).collect(Collectors.toList());
        return PageResult.<MgkLogOperationDto>builder().total(total.intValue()).records(dtos).build();

    }

    private MgkLogOperationDto convertLogPo2Dto(MgkOperationLogPo po){
        MgkLogOperationDto dto = MgkLogOperationDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private MgkOperationLogPoExample getLogExample(QueryLogParamDto param) {
        MgkOperationLogPoExample example = new MgkOperationLogPoExample();

        MgkOperationLogPoExample.Criteria c = example.or();

        ObjectUtils.setObject(param::getAccountId, c::andAccountIdEqualTo);
        ObjectUtils.setObject(param::getObjId, c::andObjIdEqualTo);
        ObjectUtils.setObject(param::getObjFlag, c::andObjFlagEqualTo);
        ObjectUtils.setObject(param::getOperateType, c::andOperateTypeEqualTo);
        ObjectUtils.setObject(param::getOperatorType, c::andOperatorTypeEqualTo);

        ObjectUtils.setObject(param::getStartTime, c::andCtimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(param::getEndTime, c::andCtimeLessThanOrEqualTo);

        ObjectUtils.setObject(param::getOrderBy, example::setOrderByClause);

        ObjectUtils.setPage(param::getPage, example::setLimit, example::setOffset);

        return example;
    }

    private void validateNewLogOperation(NewLogOperationDto newLogOperationDto) {
        Assert.notNull(newLogOperationDto, "操作信息不可为空");
        Assert.notNull(newLogOperationDto.getAccountId(), "账号ID不可为空");
        Assert.notNull(newLogOperationDto.getObjId(), "对象ID不可为空");
        Assert.notNull(newLogOperationDto.getObjFlag(), "对象类型不可为空");
        LogObjFlagEnum.getByCode(newLogOperationDto.getObjFlag());
        Assert.notNull(newLogOperationDto.getOperateType(), "操作类型不可为空");
        LogOperateTypeEnum.getByCode(newLogOperationDto.getOperateType());
        Assert.notNull(newLogOperationDto.getOperatorUsername(), "操作人不可为空");
        Assert.notNull(newLogOperationDto.getOperatorType(), "操作人类型不可为空");
        OperatorType.getByCode(newLogOperationDto.getOperatorType());
    }
}
