package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoBlackDto;
import com.bilibili.mgk.platform.api.hot_video.dto.NewHotVideoBlackDto;
import com.bilibili.mgk.platform.biz.dao.MgkHotVideoBlackListDao;
import com.bilibili.mgk.platform.biz.po.MgkHotVideoBlackListPo;
import com.bilibili.mgk.platform.biz.po.MgkHotVideoBlackListPoExample;
import com.bilibili.mgk.platform.common.MgkHotVideoBlackTypeEnum;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @file: HotVideoBlackServiceDelegate
 * @author: gaoming
 * @date: 2020/12/22
 * @version: 1.0
 * @description:
 **/

@Service
public class HotVideoBlackServiceDelegate {

    @Autowired
    private MgkHotVideoBlackListDao hotVideoBlackListDao;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    public List<HotVideoBlackDto> getBlackList(Integer blackType) {
        MgkHotVideoBlackListPoExample example = new MgkHotVideoBlackListPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andBlackTypeEqualTo(blackType);
        List<MgkHotVideoBlackListPo> pos = hotVideoBlackListDao.selectByExample(example);
        return covertPos2Dtos(pos);
    }

    private List<HotVideoBlackDto> covertPos2Dtos(List<MgkHotVideoBlackListPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertPo2Dto).collect(Collectors.toList());
    }

    private HotVideoBlackDto convertPo2Dto(MgkHotVideoBlackListPo po) {
        HotVideoBlackDto dto = HotVideoBlackDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public Integer insert(Operator operator, NewHotVideoBlackDto dto) {
        MgkHotVideoBlackListPo po = MgkHotVideoBlackListPo.builder()
                .blackId(dto.getBlackId())
                .bvid(dto.getBvid())
                .adTypeCreativeId(dto.getAdTypeCreativeId())
                .blackType(dto.getBlackType())
                .creator(operator.getOperatorName())
                .build();

        int res = hotVideoBlackListDao.insertSelective(po);
        Assert.isTrue(Utils.isPositive(res), "黑名单插入失败");
        return po.getId();
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void disable(Operator operator, String keyId, Integer blackType) {

        if (MgkHotVideoBlackTypeEnum.HOT_VIDEO.getCode().equals(blackType)) {
            disableHotVideo(operator, keyId, blackType);
            return;
        }
        disableHotAds(operator, keyId, blackType);
    }

    private void disableHotAds(Operator operator, String keyId, Integer blackType) {
        List<MgkHotVideoBlackListPo> pos = getBlackListPoByKeyIds(Lists.newArrayList(keyId), blackType);
        if (!CollectionUtils.isEmpty(pos)) {
            updateStatusById(operator, pos.get(0).getId(), IsDeleted.VALID.getCode());
            return;
        }
        // 插入
        insert(operator, NewHotVideoBlackDto.builder()
                .blackId(snowflakeIdWorker.nextId())
                .blackType(blackType)
                .adTypeCreativeId(keyId)
                .build());
    }

    private void disableHotVideo(Operator operator, String keyId, Integer blackType) {
        List<MgkHotVideoBlackListPo> pos = getBlackListPoByKeyIds(Lists.newArrayList(keyId), blackType);
        if (!CollectionUtils.isEmpty(pos)) {
            updateStatusById(operator, pos.get(0).getId(), IsDeleted.VALID.getCode());
            return;
        }
        // 插入
        insert(operator, NewHotVideoBlackDto.builder()
                .blackId(snowflakeIdWorker.nextId())
                .blackType(blackType)
                .bvid(keyId)
                .build());
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void enable(Operator operator, String keyId, Integer blackType) {

        if (MgkHotVideoBlackTypeEnum.HOT_VIDEO.getCode().equals(blackType)) {
            enableHotVideo(operator, keyId, blackType);
            return;
        }
        enableHotAds(operator, keyId, blackType);
    }

    private void enableHotAds(Operator operator, String keyId, Integer blackType) {
        List<MgkHotVideoBlackListPo> pos = getBlackListPoByKeyIds(Lists.newArrayList(keyId), blackType);
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "广告未禁用");
        // 更新
        updateStatusById(operator, pos.get(0).getId(), IsDeleted.DELETED.getCode());
    }

    private void enableHotVideo(Operator operator, String keyId, Integer blackType) {
        List<MgkHotVideoBlackListPo> pos = getBlackListPoByKeyIds(Lists.newArrayList(keyId), blackType);
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "视频未禁用");
        // 更新
        updateStatusById(operator, pos.get(0).getId(), IsDeleted.DELETED.getCode());
    }


    private List<MgkHotVideoBlackListPo> getBlackListPoByKeyIds(List<String> keyIds, Integer blackType) {
        if (CollectionUtils.isEmpty(keyIds)) {
            return Collections.emptyList();
        }
        MgkHotVideoBlackListPoExample example = new MgkHotVideoBlackListPoExample();
        MgkHotVideoBlackListPoExample.Criteria criteria = example.or();
        criteria.andBlackTypeEqualTo(blackType);

        if (MgkHotVideoBlackTypeEnum.HOT_VIDEO.getCode().equals(blackType)) {
            criteria.andBvidIn(keyIds);
        }else {
            criteria.andAdTypeCreativeIdIn(keyIds);
        }

        return hotVideoBlackListDao.selectByExample(example);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void updateStatusById(Operator operator, Integer id, Integer isDeleted) {
        MgkHotVideoBlackListPo po = MgkHotVideoBlackListPo.builder()
                .id(id)
                .isDeleted(isDeleted)
                .build();
        int res = hotVideoBlackListDao.updateByPrimaryKeySelective(po);
        Assert.isTrue(Utils.isPositive(res), "操作失败");
    }
}
