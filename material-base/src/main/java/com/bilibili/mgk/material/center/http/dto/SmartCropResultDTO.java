package com.bilibili.mgk.material.center.http.dto;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/9
 */
@Data
@Accessors(chain = true)
public class SmartCropResultDTO {

    private Integer code;

    private String msg;

    private List<CropImg> results;


    @Data
    @Accessors(chain = true)
    public static class CropImg {


        private String hwRatio;

        private String url;

        private String md5;

        private Integer width;

        private Integer height;

        private Integer leftTopX;


        private Integer leftTopY;


        private Integer rightBottomX;

        private Integer rightBottomY;


        /**
         * 0:原图 1:居中裁切 2:智能裁切
         */
        private Integer isCenterCrop;



    }

}
