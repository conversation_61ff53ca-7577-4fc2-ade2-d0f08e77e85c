package com.bilibili.mgk.platform.api.landing_page_group.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @ClassName LandingPageGroupDto
 * <AUTHOR>
 * @Date 2023/5/17 2:25 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupDto {

    private Long id;

    private Integer accountId;

    private String groupName;

    private Integer auditCreativeId;

    private Long groupId;

    private Integer groupSource;

    private Integer groupStatus;

    private Long modifyVersion;

    private Timestamp ctime;

    private Timestamp mtime;

}
