package com.bilibili.mgk.material.center.service.creative.vo;

import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoPageExtra.CandidateDay;
import com.bilibili.mgk.material.center.util.JsonUtil;
import io.vavr.Tuple2;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/25
 */
public class RisingFastVideoQueryTest {


    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    private LocalDate readyDate = LocalDate.parse("2024-02-28", formatter);
    private LocalDate readyDate3 = LocalDate.parse("2024-03-01", formatter);
    private LocalDate readyDate2 = LocalDate.parse("2023-02-28", formatter);
    private LocalDate readyDate4 = LocalDate.parse("2024-01-03", formatter);


    @Test
    public void testDateRange7() {

        RisingFastVideoQuery query = new RisingFastVideoQuery();

        query.setDayType(DateAggregation.d7);

        Tuple2<LocalDate, LocalDate> defaultLogdateRange = query.fetchDayTimeRange(readyDate);

        System.out.println(defaultLogdateRange);

        List<CandidateDay> candidates = query.fetchCandidateDaysByReadyLogdate(readyDate);

        System.out.println(candidates);
        System.out.println(JsonUtil.writeValueAsString(candidates));

    }

    @Test
    public void testDateRange7_4() {

        RisingFastVideoQuery query = new RisingFastVideoQuery();

        query.setDayType(DateAggregation.d7);

        Tuple2<LocalDate, LocalDate> defaultLogdateRange = query.fetchDayTimeRange(readyDate4);

        System.out.println(defaultLogdateRange);

        List<CandidateDay> candidates = query.fetchCandidateDaysByReadyLogdate(readyDate4);

        System.out.println(candidates);
        System.out.println(JsonUtil.writeValueAsString(candidates));

    }

    @Test
    public void testDateRange7_3() {

        RisingFastVideoQuery query = new RisingFastVideoQuery();

        query.setDayType(DateAggregation.d7);

        Tuple2<LocalDate, LocalDate> defaultLogdateRange = query.fetchDayTimeRange(readyDate3);

        System.out.println(defaultLogdateRange);

        List<CandidateDay> candidates = query.fetchCandidateDaysByReadyLogdate(readyDate3);

        System.out.println(candidates);

        System.out.println(JsonUtil.writeValueAsString(candidates));


    }


    @Test
    public void testDateRange1() {

        RisingFastVideoQuery query = new RisingFastVideoQuery();

        query.setDayType(DateAggregation.d1);

        Tuple2<LocalDate, LocalDate> defaultLogdateRange = query.fetchDayTimeRange(readyDate);

        System.out.println(defaultLogdateRange);

        List<CandidateDay> candidates = query.fetchCandidateDaysByReadyLogdate(readyDate);

        System.out.println(candidates);

        System.out.println(JsonUtil.writeValueAsString(candidates));

    }

    @Test
    public void testDateRange1_2() {

        RisingFastVideoQuery query = new RisingFastVideoQuery();

        query.setDayType(DateAggregation.d1);

        Tuple2<LocalDate, LocalDate> defaultLogdateRange = query.fetchDayTimeRange(readyDate2);

        System.out.println(defaultLogdateRange);

        List<CandidateDay> candidates = query.fetchCandidateDaysByReadyLogdate(readyDate2);

        System.out.println(candidates);

        System.out.println(JsonUtil.writeValueAsString(candidates));

    }


    @Test
    public void testDateRange30() {

        RisingFastVideoQuery query = new RisingFastVideoQuery();

        query.setDayType(DateAggregation.d30);

        Tuple2<LocalDate, LocalDate> defaultLogdateRange = query.fetchDayTimeRange(readyDate);

        System.out.println(defaultLogdateRange);

        List<CandidateDay> candidates = query.fetchCandidateDaysByReadyLogdate(readyDate);

        System.out.println(candidates);

        System.out.println(JsonUtil.writeValueAsString(candidates));


    }


    @Test
    public void testDateRange30_2() {

        RisingFastVideoQuery query = new RisingFastVideoQuery();

        query.setDayType(DateAggregation.d30);

        Tuple2<LocalDate, LocalDate> defaultLogdateRange = query.fetchDayTimeRange(readyDate2);

        System.out.println(defaultLogdateRange);

        List<CandidateDay> candidates = query.fetchCandidateDaysByReadyLogdate(readyDate2);

        System.out.println(candidates);

        System.out.println(JsonUtil.writeValueAsString(candidates));


    }

    @Test
    public void testDateRange30_3() {

        RisingFastVideoQuery query = new RisingFastVideoQuery();

        query.setDayType(DateAggregation.d30);

        Tuple2<LocalDate, LocalDate> defaultLogdateRange = query.fetchDayTimeRange(readyDate3);

        System.out.println(defaultLogdateRange);

        List<CandidateDay> candidates = query.fetchCandidateDaysByReadyLogdate(readyDate3);

        System.out.println(candidates);

        System.out.println(JsonUtil.writeValueAsString(candidates));

    }


}