#!/bin/sh

script=$0
if [ ${script:0:1} == "/" ]; then
    workspace=`dirname $script`/
else
    workspace=`pwd`/`dirname $script`/
fi

cd ${workspace}

echo "usage: ./" $script "current_branch target_branch}"

current_branch=${1:}
target_branch=${2}


git fetch origin
git checkout ${target_branch}
git merge ${current_branch} --no-edit
git push origin ${target_branch}
git checkout ${current_branch}





