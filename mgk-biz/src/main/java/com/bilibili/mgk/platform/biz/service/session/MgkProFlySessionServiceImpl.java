package com.bilibili.mgk.platform.biz.service.session;

import com.bilibili.adp.account.dto.LoginInfoDto;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.session.IMgkProFlySessionService;
import com.bilibili.mgk.platform.api.session.dto.MgkLoginInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName ProFlySessionServiceImpl
 * <AUTHOR>
 * @Date 2022/6/21 12:51 上午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkProFlySessionServiceImpl implements IMgkProFlySessionService {

    @Autowired
    private MgkProFlySessionServiceDelegate mgkProFlySessionServiceDelegate;

    @Override
    public MgkLoginInfoDto agentLogin(String cookieValue, Long userId, Integer accountId) throws Exception {
        return mgkProFlySessionServiceDelegate.agentLogin(cookieValue, userId, accountId);
    }

    @Override
    public MgkLoginInfoDto login(String cookieValue, Long userId) throws ServiceException {
        return mgkProFlySessionServiceDelegate.login(cookieValue, userId);
    }
}
