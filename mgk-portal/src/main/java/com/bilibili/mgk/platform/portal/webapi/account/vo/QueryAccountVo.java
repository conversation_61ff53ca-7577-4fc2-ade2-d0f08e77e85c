package com.bilibili.mgk.platform.portal.webapi.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName QueryAccountVo
 * <AUTHOR>
 * @Date 2022/6/19 8:26 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class QueryAccountVo {

    @ApiModelProperty("帐号ID")
    private Integer account_id;

    @ApiModelProperty("帐号名称")
    private String account_name;

    @ApiModelProperty("是否支持cpc广告")
    private Integer support_ad;

    @ApiModelProperty("是否支持gd广告")
    private Integer support_gd;

    @ApiModelProperty("是否支持本地广告")
    private Integer support_urban;

    @ApiModelProperty("当前页")
    private Integer page;
}
