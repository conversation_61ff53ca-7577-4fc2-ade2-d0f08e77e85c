<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.collage.biz.dao.CollageOperationLogDao">
  <resultMap id="BaseResultMap" type="com.bilibili.collage.biz.po.CollageOperationLogPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="obj_id" jdbcType="BIGINT" property="objId" />
    <result column="obj_flag" jdbcType="TINYINT" property="objFlag" />
    <result column="operate_type" jdbcType="TINYINT" property="operateType" />
    <result column="operator_username" jdbcType="VARCHAR" property="operatorUsername" />
    <result column="operator_type" jdbcType="TINYINT" property="operatorType" />
    <result column="old_value" jdbcType="VARCHAR" property="oldValue" />
    <result column="new_value" jdbcType="VARCHAR" property="newValue" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, obj_id, obj_flag, operate_type, operator_username, operator_type, 
    old_value, new_value, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from collage_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from collage_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from collage_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPoExample">
    delete from collage_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into collage_operation_log (account_id, obj_id, obj_flag, 
      operate_type, operator_username, operator_type, 
      old_value, new_value, is_deleted, 
      ctime, mtime)
    values (#{accountId,jdbcType=INTEGER}, #{objId,jdbcType=BIGINT}, #{objFlag,jdbcType=TINYINT}, 
      #{operateType,jdbcType=TINYINT}, #{operatorUsername,jdbcType=VARCHAR}, #{operatorType,jdbcType=TINYINT}, 
      #{oldValue,jdbcType=VARCHAR}, #{newValue,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into collage_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="objId != null">
        obj_id,
      </if>
      <if test="objFlag != null">
        obj_flag,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operatorUsername != null">
        operator_username,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="oldValue != null">
        old_value,
      </if>
      <if test="newValue != null">
        new_value,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="objFlag != null">
        #{objFlag,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="oldValue != null">
        #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPoExample" resultType="java.lang.Long">
    select count(*) from collage_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update collage_operation_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.objId != null">
        obj_id = #{record.objId,jdbcType=BIGINT},
      </if>
      <if test="record.objFlag != null">
        obj_flag = #{record.objFlag,jdbcType=TINYINT},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=TINYINT},
      </if>
      <if test="record.operatorUsername != null">
        operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorType != null">
        operator_type = #{record.operatorType,jdbcType=TINYINT},
      </if>
      <if test="record.oldValue != null">
        old_value = #{record.oldValue,jdbcType=VARCHAR},
      </if>
      <if test="record.newValue != null">
        new_value = #{record.newValue,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update collage_operation_log
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      obj_id = #{record.objId,jdbcType=BIGINT},
      obj_flag = #{record.objFlag,jdbcType=TINYINT},
      operate_type = #{record.operateType,jdbcType=TINYINT},
      operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      operator_type = #{record.operatorType,jdbcType=TINYINT},
      old_value = #{record.oldValue,jdbcType=VARCHAR},
      new_value = #{record.newValue,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPo">
    update collage_operation_log
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="objId != null">
        obj_id = #{objId,jdbcType=BIGINT},
      </if>
      <if test="objFlag != null">
        obj_flag = #{objFlag,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        operator_username = #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="oldValue != null">
        old_value = #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        new_value = #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPo">
    update collage_operation_log
    set account_id = #{accountId,jdbcType=INTEGER},
      obj_id = #{objId,jdbcType=BIGINT},
      obj_flag = #{objFlag,jdbcType=TINYINT},
      operate_type = #{operateType,jdbcType=TINYINT},
      operator_username = #{operatorUsername,jdbcType=VARCHAR},
      operator_type = #{operatorType,jdbcType=TINYINT},
      old_value = #{oldValue,jdbcType=VARCHAR},
      new_value = #{newValue,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into collage_operation_log (account_id, obj_id, obj_flag, 
      operate_type, operator_username, operator_type, 
      old_value, new_value, is_deleted, 
      ctime, mtime)
    values (#{accountId,jdbcType=INTEGER}, #{objId,jdbcType=BIGINT}, #{objFlag,jdbcType=TINYINT}, 
      #{operateType,jdbcType=TINYINT}, #{operatorUsername,jdbcType=VARCHAR}, #{operatorType,jdbcType=TINYINT}, 
      #{oldValue,jdbcType=VARCHAR}, #{newValue,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      obj_id = values(obj_id),
      obj_flag = values(obj_flag),
      operate_type = values(operate_type),
      operator_username = values(operator_username),
      operator_type = values(operator_type),
      old_value = values(old_value),
      new_value = values(new_value),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      collage_operation_log
      (account_id,obj_id,obj_flag,operate_type,operator_username,operator_type,old_value,new_value,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.objId,jdbcType=BIGINT},
        #{item.objFlag,jdbcType=TINYINT},
        #{item.operateType,jdbcType=TINYINT},
        #{item.operatorUsername,jdbcType=VARCHAR},
        #{item.operatorType,jdbcType=TINYINT},
        #{item.oldValue,jdbcType=VARCHAR},
        #{item.newValue,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      collage_operation_log
      (account_id,obj_id,obj_flag,operate_type,operator_username,operator_type,old_value,new_value,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.objId,jdbcType=BIGINT},
        #{item.objFlag,jdbcType=TINYINT},
        #{item.operateType,jdbcType=TINYINT},
        #{item.operatorUsername,jdbcType=VARCHAR},
        #{item.operatorType,jdbcType=TINYINT},
        #{item.oldValue,jdbcType=VARCHAR},
        #{item.newValue,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      obj_id = values(obj_id),
      obj_flag = values(obj_flag),
      operate_type = values(operate_type),
      operator_username = values(operator_username),
      operator_type = values(operator_type),
      old_value = values(old_value),
      new_value = values(new_value),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.collage.biz.po.CollageOperationLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into collage_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="objId != null">
        obj_id,
      </if>
      <if test="objFlag != null">
        obj_flag,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operatorUsername != null">
        operator_username,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="oldValue != null">
        old_value,
      </if>
      <if test="newValue != null">
        new_value,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="objFlag != null">
        #{objFlag,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="oldValue != null">
        #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="objId != null">
        obj_id = values(obj_id),
      </if>
      <if test="objFlag != null">
        obj_flag = values(obj_flag),
      </if>
      <if test="operateType != null">
        operate_type = values(operate_type),
      </if>
      <if test="operatorUsername != null">
        operator_username = values(operator_username),
      </if>
      <if test="operatorType != null">
        operator_type = values(operator_type),
      </if>
      <if test="oldValue != null">
        old_value = values(old_value),
      </if>
      <if test="newValue != null">
        new_value = values(new_value),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>