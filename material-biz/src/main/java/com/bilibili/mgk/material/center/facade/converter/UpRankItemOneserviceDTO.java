package com.bilibili.mgk.material.center.facade.converter;

import com.bilibili.mgk.material.center.service.creative.model.UpRankInfo;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */

@Data
@Accessors(chain = true)
public class UpRankItemOneserviceDTO {

    /**
     * UP主MID 机密数据(C3)
     */
    private Long upMid;

    /**
     * UP主昵称 机密数据(C3)
     */
    private String upName;

    /**
     * UP主简介 机密数据(C3)
     */
    private String sign;

    /**
     * 注册时间 机密数据(C3)
     */
    private String joinTime;

    /**
     * 认证类型 机密数据(C3)
     */
    private String verifyType;

    /**
     * 性别 机密数据(C3)
     */
    private String upGender;

    /**
     * UP主等级 机密数据(C3)
     */
    private String level;

    /**
     * 主投一级分区 机密数据(C3)
     */
    private String avsTidName;

    /**
     * 主投二级分区 机密数据(C3)
     */
    private String avsSubTidName;

    /**
     * 是否开通悬赏 机密数据(C3)
     */
    private Long isRewarded;

    /**
     * 是否开通花火 机密数据(C3)
     */
    private Long isHuahuo;

    /**
     * UP主类型 1:稿件UP主 2:主播 3:主播+稿件UP主 0其他 机密数据(C3)
     */
    private Long upType;

    /**
     * 粉丝数 机密数据(C3)
     */
    private Long fans;

    /**
     * 男粉比例 机密数据(C3)
     */
    private String maleFansRatio;

    /**
     * 粉丝年龄分布 机密数据(C3)
     */
    private String ageRange;

    /**
     * 首次投稿时间 机密数据(C3)
     */
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String firstUp;

    /**
     * 最近投稿时间 机密数据(C3)
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUp;

    /**
     * 稿件数量 机密数据(C3)
     */
    private Long avs;

    /**
     * 合作广告行业一级行业 机密数据(C3)
     */
    private String adFirstIndustryNameList;

    /**
     * 合作广告行业一级行业 - 二级行业 机密数据(C3)
     */
    private String adIndustryNameList;

    /**
     * 近10个稿件平均播放量 机密数据(C3)
     */
    private String avgPlayRecent10;

    /**
     * 近10个稿件平均互动率 机密数据(C3)
     */
    private String avgInteractRecent10;

    /**
     * 累计播放 机密数据(C3)
     */
    private Long playTotal;

    /**
     * 累计点赞 机密数据(C3)
     */
    private Long likesTotal;

    /**
     * 累计评论 机密数据(C3)
     */
    private Long replyTotal;

    /**
     * 累计弹幕 机密数据(C3)
     */
    private Long danmuTotal;

    /**
     * 累计投币 机密数据(C3)
     */
    private Long coinTotal;

    /**
     * 累计收藏 机密数据(C3)
     */
    private Long favTotal;

    /**
     * 累计分享 机密数据(C3)
     */
    private Long shareTotal;

    /**
     * 7天播放 机密数据(C3)
     */
    private Long play_7d;

    /**
     * 7天点赞 机密数据(C3)
     */
    private Long likes_7d;

    /**
     * 7天评论 机密数据(C3)
     */
    private Long reply_7d;

    /**
     * 7天弹幕 机密数据(C3)
     */
    private Long danmu_7d;

    /**
     * 7天投币 机密数据(C3)
     */
    private Long coin_7d;

    /**
     * 7天收藏 机密数据(C3)
     */
    private Long fav_7d;

    /**
     * 7天分享 机密数据(C3)
     */
    private Long share_7d;

    /**
     * 30天播放 机密数据(C3)
     */
    private Long play_30d;

    /**
     * 30天点赞 机密数据(C3)
     */
    private Long likes_30d;

    /**
     * 30天评论 机密数据(C3)
     */
    private Long reply_30d;

    /**
     * 30天弹幕 机密数据(C3)
     */
    private Long danmu_30d;

    /**
     * 30天投币 机密数据(C3)
     */
    private Long coin_30d;

    /**
     * 30天收藏 机密数据(C3)
     */
    private Long fav_30d;

    /**
     * 30天分享 机密数据(C3)
     */
    private Long share_30d;

    /**
     * 90天播放 机密数据(C3)
     */
    private Long play_90d;

    /**
     * 90天点赞 机密数据(C3)
     */
    private Long likes_90d;

    /**
     * 90天评论 机密数据(C3)
     */
    private Long reply_90d;

    /**
     * 90天弹幕 机密数据(C3)
     */
    private Long danmu_90d;

    /**
     * 90天投币 机密数据(C3)
     */
    private Long coin_90d;

    /**
     * 90天收藏 机密数据(C3)
     */
    private Long fav_90d;

    /**
     * 90天分享 机密数据(C3)
     */
    private Long share_90d;

    /**
     * 180天播放 机密数据(C3)
     */
    private Long play_180d;

    /**
     * 180天点赞 机密数据(C3)
     */
    private Long likes_180d;

    /**
     * 180天评论 机密数据(C3)
     */
    private Long reply_180d;

    /**
     * 180天弹幕 机密数据(C3)
     */
    private Long danmu_180d;

    /**
     * 180天投币 机密数据(C3)
     */
    private Long coin_180d;

    /**
     * 180天收藏 机密数据(C3)
     */
    private Long fav_180d;

    /**
     * 180天分享 机密数据(C3)
     */
    private Long share_180d;

    private String dataReadyTime;


    public static UpRankItemOneserviceDTO fromMapValue(Map<String, String> mapValue) {

        return JsonUtil.fromJson(mapValue, UpRankItemOneserviceDTO.class);

    }


    public UpRankInfo toBO() {

        return OneServiceDTOConverter.converter.toBO(this);
    }
}
