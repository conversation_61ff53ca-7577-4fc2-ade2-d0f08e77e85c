package com.bilibili.collage.api.service.customer;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.CollageSizeDto;
import com.bilibili.collage.api.dto.MatchingPatternDto;
import com.bilibili.collage.api.dto.PatternDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/11
 **/
public interface ICustomerPatternService {

    Map<Integer, List<PatternDto>> getPatternListInMateCount();

    List<Integer> getPatternCountList();

    List<CollageSizeDto> getSizeListByMateCount(Integer mateCount);

    PageResult<PatternDto> matchingPattern (MatchingPatternDto query);
}
