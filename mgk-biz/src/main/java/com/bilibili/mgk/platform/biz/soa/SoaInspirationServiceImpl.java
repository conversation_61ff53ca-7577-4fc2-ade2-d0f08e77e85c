package com.bilibili.mgk.platform.biz.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.inspiration.dto.ArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.NewArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.QueryArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.UpdateArticleDto;
import com.bilibili.mgk.platform.api.inspiration.service.IInspirationService;
import com.bilibili.mgk.platform.api.inspiration.soa.ISoaInspirationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * @file: SoaInspirationServiceImpl
 * @author: gaoming
 * @date: 2021/03/26
 * @version: 1.0
 * @description:
 **/
@Service
public class SoaInspirationServiceImpl implements ISoaInspirationService {

    @Autowired
    private IInspirationService inspirationService;

    @Override
    public List<String> uploadAndParsePDF(File file) throws IOException {
        return inspirationService.uploadAndParsePDF(file);
    }

    @Override
    public Long createArticle(Operator operator, NewArticleDto newArticleDto) {
        return inspirationService.createArticle(operator, newArticleDto);
    }

    @Override
    public void updateArticle(Operator operator, UpdateArticleDto updateArticleDto) {
        inspirationService.updateArticle(operator, updateArticleDto);
    }

    @Override
    public PageResult<ArticleDto> queryArticle(QueryArticleDto queryArticleDto) {
        return inspirationService.queryArticle(queryArticleDto);
    }

    @Override
    public ArticleDto getArticleByArticleId(Operator operator, Long articleId) {
        return inspirationService.getArticleByArticleId(operator, articleId);
    }

    @Override
    public void updateArticleStatus(Long articleId, Integer code) {
        inspirationService.updateArticleStatus(articleId, code);
    }
}
