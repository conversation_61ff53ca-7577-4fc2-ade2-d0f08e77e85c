package com.bilibili.collage.biz.po;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
public class ProductManualPo implements Serializable {
    /**
     * 自增ID（主键）
     */
    private Long id;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 分类类型
     */
    private Integer level;

    /**
     * 父级分类id
     */
    private Long parentId;

    /**
     * 父级分类名称
     */
    private String parentTitle;

    /**
     * 分类名称
     */
    private String title;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Byte isDeleted;

    /**
     * 添加时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentTitle() {
        return parentTitle;
    }

    public void setParentTitle(String parentTitle) {
        this.parentTitle = parentTitle;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Byte getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }
}