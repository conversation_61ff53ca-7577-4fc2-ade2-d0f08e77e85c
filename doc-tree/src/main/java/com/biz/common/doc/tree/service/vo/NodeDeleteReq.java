package com.biz.common.doc.tree.service.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */
@Data
@Accessors(chain = true)
public class NodeDeleteReq implements SnakeCaseBody {

    @ApiModelProperty("节点id")
    private Long nodeId;

    /**
     * 强制删除，及时还有子节点存在， 默认false
     */
    @ApiModelProperty("强制删除，即使还有子节点存在也强制删除， 默认false")
    private Boolean force;


    public void validate() {

    }
}


