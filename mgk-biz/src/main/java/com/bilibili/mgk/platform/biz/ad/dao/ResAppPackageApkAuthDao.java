package com.bilibili.mgk.platform.biz.ad.dao;

import com.bilibili.mgk.platform.biz.ad.po.ResAppPackageApkAuthPo;
import com.bilibili.mgk.platform.biz.ad.po.ResAppPackageApkAuthPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface ResAppPackageApkAuthDao {
    long countByExample(ResAppPackageApkAuthPoExample example);

    int deleteByExample(ResAppPackageApkAuthPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(ResAppPackageApkAuthPo record);

    int insertBatch(List<ResAppPackageApkAuthPo> records);

    int insertUpdateBatch(List<ResAppPackageApkAuthPo> records);

    int insert(ResAppPackageApkAuthPo record);

    int insertUpdateSelective(ResAppPackageApkAuthPo record);

    int insertSelective(ResAppPackageApkAuthPo record);

    List<ResAppPackageApkAuthPo> selectByExample(ResAppPackageApkAuthPoExample example);

    ResAppPackageApkAuthPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ResAppPackageApkAuthPo record, @Param("example") ResAppPackageApkAuthPoExample example);

    int updateByExample(@Param("record") ResAppPackageApkAuthPo record, @Param("example") ResAppPackageApkAuthPoExample example);

    int updateByPrimaryKeySelective(ResAppPackageApkAuthPo record);

    int updateByPrimaryKey(ResAppPackageApkAuthPo record);
}