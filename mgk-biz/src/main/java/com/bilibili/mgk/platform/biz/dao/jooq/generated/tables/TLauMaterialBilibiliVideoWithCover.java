/*
 * This file is generated by jOOQ.
 */
package com.bilibili.mgk.platform.biz.dao.jooq.generated.tables;


import com.bilibili.mgk.platform.biz.dao.jooq.generated.DefaultSchema;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.Indexes;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.Keys;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.records.LauMaterialBilibiliVideoWithCoverRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.jooq.types.ULong;


/**
 * 物料稿件表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauMaterialBilibiliVideoWithCover extends TableImpl<LauMaterialBilibiliVideoWithCoverRecord> {

    private static final long serialVersionUID = -840711559;

    /**
     * The reference instance of <code>lau_material_bilibili_video_with_cover</code>
     */
    public static final TLauMaterialBilibiliVideoWithCover LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER = new TLauMaterialBilibiliVideoWithCover();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauMaterialBilibiliVideoWithCoverRecord> getRecordType() {
        return LauMaterialBilibiliVideoWithCoverRecord.class;
    }

    /**
     * The column <code>lau_material_bilibili_video_with_cover.id</code>. 自增id
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, ULong> ID = createField(DSL.name("id"), org.jooq.impl.SQLDataType.BIGINTUNSIGNED.nullable(false).identity(true), this, "自增id");

    /**
     * The column <code>lau_material_bilibili_video_with_cover.ctime</code>. 添加时间
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, LocalDateTime> CTIME = createField(DSL.name("ctime"), org.jooq.impl.SQLDataType.LOCALDATETIME.nullable(false).defaultValue(org.jooq.impl.DSL.field("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.LOCALDATETIME)), this, "添加时间");

    /**
     * The column <code>lau_material_bilibili_video_with_cover.mtime</code>. 修改时间
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, LocalDateTime> MTIME = createField(DSL.name("mtime"), org.jooq.impl.SQLDataType.LOCALDATETIME.nullable(false).defaultValue(org.jooq.impl.DSL.field("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.LOCALDATETIME)), this, "修改时间");

    /**
     * The column <code>lau_material_bilibili_video_with_cover.material_md5</code>. 物料md5
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, String> MATERIAL_MD5 = createField(DSL.name("material_md5"), org.jooq.impl.SQLDataType.VARCHAR(32).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "物料md5");

    /**
     * The column <code>lau_material_bilibili_video_with_cover.avid</code>. 视频ID
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, ULong> AVID = createField(DSL.name("avid"), org.jooq.impl.SQLDataType.BIGINTUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.BIGINTUNSIGNED)), this, "视频ID");

    /**
     * The column <code>lau_material_bilibili_video_with_cover.cover_url</code>. 封面URL
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, String> COVER_URL = createField(DSL.name("cover_url"), org.jooq.impl.SQLDataType.VARCHAR(256).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "封面URL");

    /**
     * The column <code>lau_material_bilibili_video_with_cover.cover_md5</code>. 封面MD5
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, String> COVER_MD5 = createField(DSL.name("cover_md5"), org.jooq.impl.SQLDataType.VARCHAR(32).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "封面MD5");

    /**
     * The column <code>lau_material_bilibili_video_with_cover.video_md5</code>. avid生成的md5
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, String> VIDEO_MD5 = createField(DSL.name("video_md5"), org.jooq.impl.SQLDataType.VARCHAR(32).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "avid生成的md5");

    /**
     * The column <code>lau_material_bilibili_video_with_cover.cid</code>. 视频CID
     */
    public final TableField<LauMaterialBilibiliVideoWithCoverRecord, ULong> CID = createField(DSL.name("cid"), org.jooq.impl.SQLDataType.BIGINTUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.BIGINTUNSIGNED)), this, "视频CID");

    /**
     * Create a <code>lau_material_bilibili_video_with_cover</code> table reference
     */
    public TLauMaterialBilibiliVideoWithCover() {
        this(DSL.name("lau_material_bilibili_video_with_cover"), null);
    }

    /**
     * Create an aliased <code>lau_material_bilibili_video_with_cover</code> table reference
     */
    public TLauMaterialBilibiliVideoWithCover(String alias) {
        this(DSL.name(alias), LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER);
    }

    /**
     * Create an aliased <code>lau_material_bilibili_video_with_cover</code> table reference
     */
    public TLauMaterialBilibiliVideoWithCover(Name alias) {
        this(alias, LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER);
    }

    private TLauMaterialBilibiliVideoWithCover(Name alias, Table<LauMaterialBilibiliVideoWithCoverRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauMaterialBilibiliVideoWithCover(Name alias, Table<LauMaterialBilibiliVideoWithCoverRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("物料稿件表"), TableOptions.table());
    }

    public <O extends Record> TLauMaterialBilibiliVideoWithCover(Table<O> child, ForeignKey<O, LauMaterialBilibiliVideoWithCoverRecord> key) {
        super(child, key, LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER_IX_MTIME);
    }

    @Override
    public Identity<LauMaterialBilibiliVideoWithCoverRecord, ULong> getIdentity() {
        return Keys.IDENTITY_LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER;
    }

    @Override
    public UniqueKey<LauMaterialBilibiliVideoWithCoverRecord> getPrimaryKey() {
        return Keys.KEY_LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER_PRIMARY;
    }

    @Override
    public List<UniqueKey<LauMaterialBilibiliVideoWithCoverRecord>> getKeys() {
        return Arrays.<UniqueKey<LauMaterialBilibiliVideoWithCoverRecord>>asList(Keys.KEY_LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER_PRIMARY, Keys.KEY_LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER_UK_MATERIAL_MD5);
    }

    @Override
    public TLauMaterialBilibiliVideoWithCover as(String alias) {
        return new TLauMaterialBilibiliVideoWithCover(DSL.name(alias), this);
    }

    @Override
    public TLauMaterialBilibiliVideoWithCover as(Name alias) {
        return new TLauMaterialBilibiliVideoWithCover(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauMaterialBilibiliVideoWithCover rename(String name) {
        return new TLauMaterialBilibiliVideoWithCover(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauMaterialBilibiliVideoWithCover rename(Name name) {
        return new TLauMaterialBilibiliVideoWithCover(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<ULong, LocalDateTime, LocalDateTime, String, ULong, String, String, String, ULong> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
