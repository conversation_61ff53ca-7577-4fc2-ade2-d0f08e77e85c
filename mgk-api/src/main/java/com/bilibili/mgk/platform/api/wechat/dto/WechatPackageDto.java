package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName WechatPackageDto
 * <AUTHOR>
 * @Date 2022/6/1 11:57 上午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WechatPackageDto implements Serializable {
    private static final long serialVersionUID = -456847650354L;

    /**
     * 微信包id
     */
    private Integer id;

    /**
     * 微信包名称
     */
    private String name;

    /**
     * 微信包类型 -1 不存在 0-个人号 1-公众号 2-企业微信 3-其他
     */
    private Integer type;

    /**
     * 微信包类型描述
     */
    private String typeDesc;

    /**
     * 帐户id
     */
    private Integer accountId;

    /**
     * 微信账号数量
     */
    private Integer wechatAccountCount;

    /**
     * 微信号列表
     */
    private List<WechatAccountListDto> wechatAccountListDtos;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;


}
