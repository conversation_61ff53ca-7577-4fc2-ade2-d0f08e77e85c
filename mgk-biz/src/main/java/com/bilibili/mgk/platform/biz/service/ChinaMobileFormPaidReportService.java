package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.enums.ConvReturnMsgCode;
import com.bilibili.adp.passport.api.dto.AdConvDto;
import com.bilibili.adp.passport.api.service.IConvReturnService;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.mgk.platform.api.form.dto.MgkCmActivationBo;
import com.bilibili.mgk.platform.biz.po.MgkFormDataPo;
import com.bilibili.mgk.platform.common.MgkReportStatusEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.WhetherEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkCmActivation.mgkCmActivation;
import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkFormData.mgkFormData;

@Service
@Slf4j
public class ChinaMobileFormPaidReportService {
    @Autowired
    private IConvReturnService cvtReturnService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private BaseQueryFactory bqf;

    public void reportJob() {
        final LocalDateTime now = LocalDateTime.now();
        final Timestamp startTs = Timestamp.valueOf(now.minusMinutes(50));
        final Timestamp endTs = Timestamp.valueOf(now.minusMinutes(5));
        final List<MgkCmActivationBo> activations = bqf.selectFrom(mgkCmActivation)
                .where(mgkCmActivation.reportStatus.eq(MgkReportStatusEnum.UNREPORT.getCode()))
                .where(mgkCmActivation.mtime.gt(startTs))
                .where(mgkCmActivation.mtime.lt(endTs))
                .fetch(MgkCmActivationBo.class);
        if (CollectionUtils.isEmpty(activations)) return;

        log.info("移动表单付费上报任务: [{} - {}]; 总计上报数量: {}", startTs, endTs, activations.size());
        final List<Long> orderIds = activations.stream()
                .map(MgkCmActivationBo::getOrderNo)
                .collect(Collectors.toList());
        final Map<Long, String> map = bqf.from(mgkFormData)
                .where(mgkFormData.phoneOrderId.in(orderIds))
                .where(mgkFormData.isCheat.eq(WhetherEnum.NO.getCode()))
                .select(mgkFormData.phoneOrderId, mgkFormData.trackId)
                .fetch(MgkFormDataPo.class)
                .stream()
                .collect(Collectors.toMap(MgkFormDataPo::getPhoneOrderId, MgkFormDataPo::getTrackId));
        final List<Long> rejectedOrderIds = new LinkedList<>();
        activations.forEach(x -> {
            final String trackId = map.get(x.getOrderNo());
            if (!org.springframework.util.StringUtils.hasText(trackId)) {
                rejectedOrderIds.add(x.getOrderNo());
                return;
            }
            reportAsync(x.getOrderNo(), trackId, MgkConstants.FORM_PAID, Long.valueOf(x.getConvTime().getTime()).toString());
        });
        if (!CollectionUtils.isEmpty(rejectedOrderIds)) {
            bqf.update(mgkCmActivation)
                    .where(mgkCmActivation.orderNo.in(rejectedOrderIds))
                    .set(mgkCmActivation.reportStatus, MgkReportStatusEnum.INVALID_TRACKID.getCode())
                    .execute();
        }
    }

    public void report(Long orderId, String trackId, String cvtType, String cvtTime) {
        final AdConvDto adCvtDto = AdConvDto.builder()
                .track_id(trackId)
                .conv_type(cvtType)
                .conv_time(cvtTime)
                .client_ip("0.0.0.0")
                .scid("mgk")
                .build();
        final int resCode = cvtReturnService.adTrackReturn(adCvtDto);
        if (ConvReturnMsgCode.SUCCESS.getCode().equals(resCode)) {
            log.info(String.format("china mobile reported trackId [%s]: ok", adCvtDto.getTrack_id()));
            updateReportStatus(orderId, MgkReportStatusEnum.REPORTED.getCode());
        } else if (ConvReturnMsgCode.INVALID_TRACKID.getCode().equals(resCode)) {
            log.error(String.format("china mobile reported trackId [%s]: invalid", adCvtDto.getTrack_id()));
            updateReportStatus(orderId, MgkReportStatusEnum.INVALID_TRACKID.getCode());
        } else {
            log.error(String.format("china mobile reported trackId [%s]: failed", adCvtDto.getTrack_id()));
        }
    }

    public void updateReportStatus(Long orderId, Integer status) {
        bqf.update(mgkCmActivation)
                .where(mgkCmActivation.orderNo.eq(orderId))
                .set(mgkCmActivation.reportStatus, status)
                .execute();
    }

    public void reportAsync(Long orderId, String trackId, String cvtType, String cvtTime) {
        taskExecutor.execute(() -> report(orderId, trackId, cvtType, cvtTime));
    }
}
