package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkWechatAccountPo;
import com.bilibili.mgk.platform.biz.po.MgkWechatAccountPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkWechatAccountDao {
    long countByExample(MgkWechatAccountPoExample example);

    int deleteByExample(MgkWechatAccountPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkWechatAccountPo record);

    int insertBatch(List<MgkWechatAccountPo> records);

    int insertUpdateBatch(List<MgkWechatAccountPo> records);

    int insert(MgkWechatAccountPo record);

    int insertUpdateSelective(MgkWechatAccountPo record);

    int insertSelective(MgkWechatAccountPo record);

    List<MgkWechatAccountPo> selectByExample(MgkWechatAccountPoExample example);

    MgkWechatAccountPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkWechatAccountPo record, @Param("example") MgkWechatAccountPoExample example);

    int updateByExample(@Param("record") MgkWechatAccountPo record, @Param("example") MgkWechatAccountPoExample example);

    int updateByPrimaryKeySelective(MgkWechatAccountPo record);

    int updateByPrimaryKey(MgkWechatAccountPo record);
}