package com.bilibili.mgk.material.center.service.mainsite;


import com.bapis.account.service.Info;
import com.bapis.account.service.v2.AccountGrpc;
import com.bapis.account.service.v2.ControlInfo;
import com.bapis.account.service.v2.GetUserInfosReq;
import com.bapis.account.service.v2.UserInfo;
import com.bapis.account.service.v2.UserViewEnum;
import com.bapis.account.service.v2.UserViewItem;
import com.bapis.account.service.v2.UsersInfoReply;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import io.vavr.control.Try;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 *
 * @see https://git.bilibili.co/bapis/bapis/-/blob/master/account/service/v2/api.proto
 * <AUTHOR>
 * @desc
 * @date 2024/7/2
 */
@Slf4j
@Service
public class BiliAccountServiceImpl implements BiliAccountService {


    @Resource
    private AccountGrpc.AccountBlockingStub accountBlockingStub;

    @Value("${material.bili-account.filter-if-silence-not-present:false}")
    private Boolean filterIfSilenceNotPresent;


    /**
     * @param mids
     * @param needCtrlInfo 是否需要查询封禁状态
     * @return
     */
    @Override
    public Map<Long, UserInfo> findBasicUserInfo(List<Long> mids, boolean needCtrlInfo) {
        if (CollectionUtils.isEmpty(mids)) {
            return Collections.emptyMap();
        }

        return Try.of(() -> {
            UsersInfoReply rsp = accountBlockingStub.getUserInfos(
                    GetUserInfosReq.newBuilder()
                            .addAllMids(mids.stream().filter(Objects::nonNull).collect(Collectors.toList()))
                            .addAllViews(Try.of(() -> {

                                if (needCtrlInfo) {
                                    return Lists.newArrayList(UserViewItem.newBuilder()
                                                    .setView(UserViewEnum.BASE_INFO_VIEW)
                                                    .build(),
                                            UserViewItem.newBuilder()
                                                    .setView(UserViewEnum.CONTROL_INFO_VIEW)
                                                    .build());
                                } else {

                                    return Lists.newArrayList(UserViewItem.newBuilder()
                                            .setView(UserViewEnum.BASE_INFO_VIEW)
                                            .build());
                                }

                            }).get())
                            .build());

            return rsp.getInfosMap();
        }).onFailure(t -> {
            log.error("Fail to findBasicUserInfo, mids={}", mids, t);
        }).getOrElse(new HashMap<>());

    }

    @Override
    public <T> List<T> fillingUpAvatarAndFilterOutInvalidMids(List<T> videos, Function<T, Long> midGetter,
            BiConsumer<T, String> avatarSetter) {
        return Try.of(() -> {
            List<Long> mids = videos.stream().map(midGetter)

                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());

            Map<Long, UserInfo> userInfos = findBasicUserInfo(mids, true);

            return videos.stream().filter(video -> {

                Long mid = midGetter.apply(video);

                Optional.ofNullable(userInfos.get(mid))
                        .map(UserInfo::getBaseInfo)
                        .map(Info::getFace)
                        .ifPresent(face -> {
                            avatarSetter.accept(video, face);
                        });

                Long fetchMid = Optional.ofNullable(userInfos.get(mid))
                        .map(UserInfo::getBaseInfo)
                        .map(Info::getMid)
                        .orElse(0L);

                if (fetchMid <= 0) {
                    return false;
                }

                Boolean isDeleted = Optional.ofNullable(userInfos.get(mid))
                        .map(UserInfo::getBaseInfo)
                        .map(Info::getIsDeleted)
                        .map(delete -> delete == 1)
                        .orElse(true);

                if (isDeleted) {
                    return false;
                }

                return Optional.ofNullable(userInfos.get(mid))
                        .map(UserInfo::getControlInfo)
                        .map(ControlInfo::getSilence)
                        .map(silence -> silence == 0)
                        // false 表示必要能查到，不然直接过滤掉
                        .orElse(filterIfSilenceNotPresent);
            }).collect(Collectors.toList());
        }).onFailure(t -> {
            log.error("Fail to fillingUpAvatar error, data={}", videos, t);
        }).getOrElse(videos);
    }
}
