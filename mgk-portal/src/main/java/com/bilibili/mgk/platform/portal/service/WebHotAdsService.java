package com.bilibili.mgk.platform.portal.service;

import com.bilibili.mgk.platform.api.hot_ads.dto.DaiHuoHotAdDetailDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.DaiHuoHotAdDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDto;
import com.bilibili.mgk.platform.portal.webapi.hot_ads.vo.DaiHuoHotAdsDetailVo;
import com.bilibili.mgk.platform.portal.webapi.hot_ads.vo.DaiHuoHotAdsListVo;
import com.bilibili.mgk.platform.portal.webapi.hot_ads.vo.DaiHuoHotItemVo;
import com.bilibili.mgk.platform.portal.webapi.hot_ads.vo.HotAdsListVo;
import com.google.common.base.Strings;
import edu.emory.mathcs.backport.java.util.Collections;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @file: WebHotAdsService
 * @author: gaoming
 * @date: 2021/01/07
 * @version: 1.0
 * @description:
 **/
public class WebHotAdsService {

    private static final String VIDEO_URL_PRE = "https://www.bilibili.com/video/av";

    public static List<HotAdsListVo> convertListDtos2Vos(List<HotAdsDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return records.stream().map(WebHotAdsService::convertListDto2Vo).collect(Collectors.toList());
    }

    public static HotAdsListVo convertListDto2Vo(HotAdsDto hotAdsDto) {
        return HotAdsListVo.builder()
                .unit_name(hotAdsDto.getUnitName())
                .ad_type_creative_id(hotAdsDto.getAdTypeCreativeId())
                .ad_type(hotAdsDto.getAdType())
                .bvid(hotAdsDto.getBvid())
                .click(hotAdsDto.getClick())
                .conv_num(hotAdsDto.getConvNum())
                .creative_id(hotAdsDto.getCreativeId())
                .title(Strings.isNullOrEmpty(hotAdsDto.getCreativeTitle()) ? "--" : hotAdsDto.getCreativeTitle())
                .ctr(hotAdsDto.getCtr())
                .ctr_rank(Strings.isNullOrEmpty(hotAdsDto.getCtrRank()) ? "--" : hotAdsDto.getCtrRank())
                .cvr(hotAdsDto.getCvr())
                .cvr_rank(Strings.isNullOrEmpty(hotAdsDto.getCvrRank()) ? "--" : hotAdsDto.getCvrRank())
                .day_type(hotAdsDto.getDayType())
                .image_url(hotAdsDto.getImageUrl())
                .video_url(hotAdsDto.getVideoUrl())
                .account_id(hotAdsDto.getAccountId())
                .first_industry(Strings.isNullOrEmpty(hotAdsDto.getFirstIndustry()) ? "--" : hotAdsDto.getFirstIndustry())
                .log_date(hotAdsDto.getLogDate())
                .pv(hotAdsDto.getPv())
                .pv_rank(Strings.isNullOrEmpty(hotAdsDto.getPvRank()) ? "--" : hotAdsDto.getPvRank())
                .style_ability(hotAdsDto.getStyleAbility())
                .creative_create_time(hotAdsDto.getCreativeCreateTime())
                .is_collect(hotAdsDto.getIsCollect())
                .is_vertical_screen(hotAdsDto.getIsVerticalScreen())
                .build();
    }

    public static List<DaiHuoHotAdsListVo> convertDaiHUoListDtos2Vos(List<DaiHuoHotAdDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return records.stream().map(WebHotAdsService::convertDaiHuoHotAdDto2Vo).collect(Collectors.toList());
    }

    public static DaiHuoHotAdsListVo convertDaiHuoHotAdDto2Vo(DaiHuoHotAdDto hotAdsDto) {
        DaiHuoHotAdsListVo vo = new DaiHuoHotAdsListVo();
        BeanUtils.copyProperties(hotAdsDto, vo);
        vo.setVideoUrl(VIDEO_URL_PRE+hotAdsDto.getAvid());
        return vo;
    }

    public static DaiHuoHotAdsDetailVo convertDaiHuoHotAdDetail2Vo(DaiHuoHotAdDetailDto hotAdsDto) {
        DaiHuoHotAdsDetailVo detailVo = new DaiHuoHotAdsDetailVo();
        BeanUtils.copyProperties(hotAdsDto, detailVo);
        detailVo.setVideoUrl(VIDEO_URL_PRE+hotAdsDto.getAvid());

        List<DaiHuoHotItemVo> itemVos  = CollectionUtils.isEmpty(hotAdsDto.getItemDtoList()) ? new ArrayList<>()
                : hotAdsDto.getItemDtoList().stream().map(daiHuoHotItemDto -> {
            DaiHuoHotItemVo itemVo = new DaiHuoHotItemVo();
            BeanUtils.copyProperties(daiHuoHotItemDto, itemVo);
            return itemVo;
        }).collect(Collectors.toList());
        detailVo.setItemVos(itemVos);
        return detailVo;
    }

}
