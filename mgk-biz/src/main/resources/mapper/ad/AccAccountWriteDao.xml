<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.ad.dao.AccAccountWriteDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.AccAccountPo">
    <id column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="password_strength" jdbcType="TINYINT" property="passwordStrength" />
    <result column="salt" jdbcType="VARCHAR" property="salt" />
    <result column="salt_password" jdbcType="VARCHAR" property="saltPassword" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="crm_customer_id" jdbcType="INTEGER" property="crmCustomerId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="mid" jdbcType="BIGINT" property="mid" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="active_time" jdbcType="TIMESTAMP" property="activeTime" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="icp_record_number" jdbcType="VARCHAR" property="icpRecordNumber" />
    <result column="icp_info_image" jdbcType="VARCHAR" property="icpInfoImage" />
    <result column="brand_domain" jdbcType="VARCHAR" property="brandDomain" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="ad_status" jdbcType="TINYINT" property="adStatus" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="category_first_id" jdbcType="INTEGER" property="categoryFirstId" />
    <result column="category_second_id" jdbcType="INTEGER" property="categorySecondId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_agent" jdbcType="TINYINT" property="isAgent" />
    <result column="agent_type" jdbcType="TINYINT" property="agentType" />
    <result column="business_role_id" jdbcType="INTEGER" property="businessRoleId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="area_id" jdbcType="INTEGER" property="areaId" />
    <result column="dependency_agent_id" jdbcType="INTEGER" property="dependencyAgentId" />
    <result column="website_name" jdbcType="VARCHAR" property="websiteName" />
    <result column="weibo" jdbcType="VARCHAR" property="weibo" />
    <result column="internal_linkman" jdbcType="VARCHAR" property="internalLinkman" />
    <result column="linkman_address" jdbcType="VARCHAR" property="linkmanAddress" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="qualification_id" jdbcType="INTEGER" property="qualificationId" />
    <result column="business_licence_code" jdbcType="VARCHAR" property="businessLicenceCode" />
    <result column="business_licence_expire_date" jdbcType="DATE" property="businessLicenceExpireDate" />
    <result column="is_business_licence_indefinite" jdbcType="TINYINT" property="isBusinessLicenceIndefinite" />
    <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName" />
    <result column="legal_person_idcard_expire_date" jdbcType="DATE" property="legalPersonIdcardExpireDate" />
    <result column="is_legal_person_idcard_indefinite" jdbcType="TINYINT" property="isLegalPersonIdcardIndefinite" />
    <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
    <result column="account_status" jdbcType="TINYINT" property="accountStatus" />
    <result column="linkman_email" jdbcType="VARCHAR" property="linkmanEmail" />
    <result column="gd_status" jdbcType="TINYINT" property="gdStatus" />
    <result column="agent_auth_expire_date" jdbcType="DATE" property="agentAuthExpireDate" />
    <result column="is_inner" jdbcType="TINYINT" property="isInner" />
    <result column="department_id" jdbcType="INTEGER" property="departmentId" />
    <result column="is_support_seller" jdbcType="TINYINT" property="isSupportSeller" />
    <result column="is_support_game" jdbcType="TINYINT" property="isSupportGame" />
    <result column="is_support_dpa" jdbcType="TINYINT" property="isSupportDpa" />
    <result column="is_support_content" jdbcType="TINYINT" property="isSupportContent" />
    <result column="product_line" jdbcType="VARCHAR" property="productLine" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="idcard_type" jdbcType="TINYINT" property="idcardType" />
    <result column="idcard_number" jdbcType="VARCHAR" property="idcardNumber" />
    <result column="idcard_expire_date" jdbcType="TIMESTAMP" property="idcardExpireDate" />
    <result column="personal_address" jdbcType="VARCHAR" property="personalAddress" />
    <result column="is_idcard_indefinite" jdbcType="TINYINT" property="isIdcardIndefinite" />
    <result column="personal_name" jdbcType="VARCHAR" property="personalName" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="product_line_id" jdbcType="INTEGER" property="productLineId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="is_support_pickup" jdbcType="TINYINT" property="isSupportPickup" />
    <result column="is_support_mas" jdbcType="TINYINT" property="isSupportMas" />
    <result column="auto_update_label" jdbcType="TINYINT" property="autoUpdateLabel" />
    <result column="is_support_fly" jdbcType="TINYINT" property="isSupportFly" />
    <result column="allow_cash_pay" jdbcType="TINYINT" property="allowCashPay" />
    <result column="allow_incentive_bonus_pay" jdbcType="TINYINT" property="allowIncentiveBonusPay" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="payment_period" jdbcType="INTEGER" property="paymentPeriod" />
    <result column="is_support_local_ad" jdbcType="TINYINT" property="isSupportLocalAd" />
    <result column="first_industry_tag_id" jdbcType="INTEGER" property="firstIndustryTagId" />
    <result column="second_industry_tag_id" jdbcType="INTEGER" property="secondIndustryTagId" />
    <result column="third_industry_tag_id" jdbcType="INTEGER" property="thirdIndustryTagId" />
    <result column="commerce_category_first_id" jdbcType="INTEGER" property="commerceCategoryFirstId" />
    <result column="commerce_category_second_id" jdbcType="INTEGER" property="commerceCategorySecondId" />
    <result column="allow_signing_bonus_pay" jdbcType="TINYINT" property="allowSigningBonusPay" />
    <result column="allow_fly_coin_pay" jdbcType="TINYINT" property="allowFlyCoinPay" />
    <result column="allow_flow_ticket_pay" jdbcType="TINYINT" property="allowFlowTicketPay" />
    <result column="finance_type" jdbcType="TINYINT" property="financeType" />
    <result column="has_mgk_form" jdbcType="INTEGER" property="hasMgkForm" />
    <result column="mgk_form_privacy_policy" jdbcType="INTEGER" property="mgkFormPrivacyPolicy" />
    <result column="show_to_customer" jdbcType="TINYINT" property="showToCustomer" />
    <result column="is_support_clue_pass" jdbcType="TINYINT" property="isSupportCluePass" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.AccAccountPo">
    <id column="acc_account_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="acc_account_username" jdbcType="VARCHAR" property="username" />
    <result column="acc_account_mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="acc_account_password_strength" jdbcType="TINYINT" property="passwordStrength" />
    <result column="acc_account_salt" jdbcType="VARCHAR" property="salt" />
    <result column="acc_account_salt_password" jdbcType="VARCHAR" property="saltPassword" />
    <result column="acc_account_status" jdbcType="TINYINT" property="status" />
    <result column="acc_account_crm_customer_id" jdbcType="INTEGER" property="crmCustomerId" />
    <result column="acc_account_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="acc_account_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="acc_account_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="acc_account_order_type" jdbcType="TINYINT" property="orderType" />
    <result column="acc_account_mid" jdbcType="BIGINT" property="mid" />
    <result column="acc_account_account_type" jdbcType="TINYINT" property="accountType" />
    <result column="acc_account_active_time" jdbcType="TIMESTAMP" property="activeTime" />
    <result column="acc_account_name" jdbcType="VARCHAR" property="name" />
    <result column="acc_account_icp_record_number" jdbcType="VARCHAR" property="icpRecordNumber" />
    <result column="acc_account_icp_info_image" jdbcType="VARCHAR" property="icpInfoImage" />
    <result column="acc_account_brand_domain" jdbcType="VARCHAR" property="brandDomain" />
    <result column="acc_account_user_type" jdbcType="TINYINT" property="userType" />
    <result column="acc_account_ad_status" jdbcType="TINYINT" property="adStatus" />
    <result column="acc_account_version" jdbcType="INTEGER" property="version" />
    <result column="acc_account_category_first_id" jdbcType="INTEGER" property="categoryFirstId" />
    <result column="acc_account_category_second_id" jdbcType="INTEGER" property="categorySecondId" />
    <result column="acc_account_remark" jdbcType="VARCHAR" property="remark" />
    <result column="acc_account_is_agent" jdbcType="TINYINT" property="isAgent" />
    <result column="acc_account_agent_type" jdbcType="TINYINT" property="agentType" />
    <result column="acc_account_business_role_id" jdbcType="INTEGER" property="businessRoleId" />
    <result column="acc_account_company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="acc_account_area_id" jdbcType="INTEGER" property="areaId" />
    <result column="acc_account_dependency_agent_id" jdbcType="INTEGER" property="dependencyAgentId" />
    <result column="acc_account_website_name" jdbcType="VARCHAR" property="websiteName" />
    <result column="acc_account_weibo" jdbcType="VARCHAR" property="weibo" />
    <result column="acc_account_internal_linkman" jdbcType="VARCHAR" property="internalLinkman" />
    <result column="acc_account_linkman_address" jdbcType="VARCHAR" property="linkmanAddress" />
    <result column="acc_account_bank" jdbcType="VARCHAR" property="bank" />
    <result column="acc_account_qualification_id" jdbcType="INTEGER" property="qualificationId" />
    <result column="acc_account_business_licence_code" jdbcType="VARCHAR" property="businessLicenceCode" />
    <result column="acc_account_business_licence_expire_date" jdbcType="DATE" property="businessLicenceExpireDate" />
    <result column="acc_account_is_business_licence_indefinite" jdbcType="TINYINT" property="isBusinessLicenceIndefinite" />
    <result column="acc_account_legal_person_name" jdbcType="VARCHAR" property="legalPersonName" />
    <result column="acc_account_legal_person_idcard_expire_date" jdbcType="DATE" property="legalPersonIdcardExpireDate" />
    <result column="acc_account_is_legal_person_idcard_indefinite" jdbcType="TINYINT" property="isLegalPersonIdcardIndefinite" />
    <result column="acc_account_audit_remark" jdbcType="VARCHAR" property="auditRemark" />
    <result column="acc_account_account_status" jdbcType="TINYINT" property="accountStatus" />
    <result column="acc_account_linkman_email" jdbcType="VARCHAR" property="linkmanEmail" />
    <result column="acc_account_gd_status" jdbcType="TINYINT" property="gdStatus" />
    <result column="acc_account_agent_auth_expire_date" jdbcType="DATE" property="agentAuthExpireDate" />
    <result column="acc_account_is_inner" jdbcType="TINYINT" property="isInner" />
    <result column="acc_account_department_id" jdbcType="INTEGER" property="departmentId" />
    <result column="acc_account_is_support_seller" jdbcType="TINYINT" property="isSupportSeller" />
    <result column="acc_account_is_support_game" jdbcType="TINYINT" property="isSupportGame" />
    <result column="acc_account_is_support_dpa" jdbcType="TINYINT" property="isSupportDpa" />
    <result column="acc_account_is_support_content" jdbcType="TINYINT" property="isSupportContent" />
    <result column="acc_account_product_line" jdbcType="VARCHAR" property="productLine" />
    <result column="acc_account_phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="acc_account_idcard_type" jdbcType="TINYINT" property="idcardType" />
    <result column="acc_account_idcard_number" jdbcType="VARCHAR" property="idcardNumber" />
    <result column="acc_account_idcard_expire_date" jdbcType="TIMESTAMP" property="idcardExpireDate" />
    <result column="acc_account_personal_address" jdbcType="VARCHAR" property="personalAddress" />
    <result column="acc_account_is_idcard_indefinite" jdbcType="TINYINT" property="isIdcardIndefinite" />
    <result column="acc_account_personal_name" jdbcType="VARCHAR" property="personalName" />
    <result column="acc_account_group_id" jdbcType="INTEGER" property="groupId" />
    <result column="acc_account_product_line_id" jdbcType="INTEGER" property="productLineId" />
    <result column="acc_account_product_id" jdbcType="INTEGER" property="productId" />
    <result column="acc_account_is_support_pickup" jdbcType="TINYINT" property="isSupportPickup" />
    <result column="acc_account_is_support_mas" jdbcType="TINYINT" property="isSupportMas" />
    <result column="acc_account_auto_update_label" jdbcType="TINYINT" property="autoUpdateLabel" />
    <result column="acc_account_is_support_fly" jdbcType="TINYINT" property="isSupportFly" />
    <result column="acc_account_allow_cash_pay" jdbcType="TINYINT" property="allowCashPay" />
    <result column="acc_account_allow_incentive_bonus_pay" jdbcType="TINYINT" property="allowIncentiveBonusPay" />
    <result column="acc_account_customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="acc_account_creator" jdbcType="VARCHAR" property="creator" />
    <result column="acc_account_payment_period" jdbcType="INTEGER" property="paymentPeriod" />
    <result column="acc_account_is_support_local_ad" jdbcType="TINYINT" property="isSupportLocalAd" />
    <result column="acc_account_first_industry_tag_id" jdbcType="INTEGER" property="firstIndustryTagId" />
    <result column="acc_account_second_industry_tag_id" jdbcType="INTEGER" property="secondIndustryTagId" />
    <result column="acc_account_third_industry_tag_id" jdbcType="INTEGER" property="thirdIndustryTagId" />
    <result column="acc_account_commerce_category_first_id" jdbcType="INTEGER" property="commerceCategoryFirstId" />
    <result column="acc_account_commerce_category_second_id" jdbcType="INTEGER" property="commerceCategorySecondId" />
    <result column="acc_account_allow_signing_bonus_pay" jdbcType="TINYINT" property="allowSigningBonusPay" />
    <result column="acc_account_allow_fly_coin_pay" jdbcType="TINYINT" property="allowFlyCoinPay" />
    <result column="acc_account_allow_flow_ticket_pay" jdbcType="TINYINT" property="allowFlowTicketPay" />
    <result column="acc_account_finance_type" jdbcType="TINYINT" property="financeType" />
    <result column="acc_account_has_mgk_form" jdbcType="INTEGER" property="hasMgkForm" />
    <result column="acc_account_mgk_form_privacy_policy" jdbcType="INTEGER" property="mgkFormPrivacyPolicy" />
    <result column="acc_account_show_to_customer" jdbcType="TINYINT" property="showToCustomer" />
    <result column="acc_account_is_support_clue_pass" jdbcType="TINYINT" property="isSupportCluePass" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.account_id as acc_account_account_id, ${alias}.username as acc_account_username, 
    ${alias}.mobile as acc_account_mobile, ${alias}.password_strength as acc_account_password_strength, 
    ${alias}.salt as acc_account_salt, ${alias}.salt_password as acc_account_salt_password, 
    ${alias}.status as acc_account_status, ${alias}.crm_customer_id as acc_account_crm_customer_id, 
    ${alias}.ctime as acc_account_ctime, ${alias}.mtime as acc_account_mtime, ${alias}.is_deleted as acc_account_is_deleted, 
    ${alias}.order_type as acc_account_order_type, ${alias}.mid as acc_account_mid, ${alias}.account_type as acc_account_account_type, 
    ${alias}.active_time as acc_account_active_time, ${alias}.name as acc_account_name, 
    ${alias}.icp_record_number as acc_account_icp_record_number, ${alias}.icp_info_image as acc_account_icp_info_image, 
    ${alias}.brand_domain as acc_account_brand_domain, ${alias}.user_type as acc_account_user_type, 
    ${alias}.ad_status as acc_account_ad_status, ${alias}.version as acc_account_version, 
    ${alias}.category_first_id as acc_account_category_first_id, ${alias}.category_second_id as acc_account_category_second_id, 
    ${alias}.remark as acc_account_remark, ${alias}.is_agent as acc_account_is_agent, 
    ${alias}.agent_type as acc_account_agent_type, ${alias}.business_role_id as acc_account_business_role_id, 
    ${alias}.company_name as acc_account_company_name, ${alias}.area_id as acc_account_area_id, 
    ${alias}.dependency_agent_id as acc_account_dependency_agent_id, ${alias}.website_name as acc_account_website_name, 
    ${alias}.weibo as acc_account_weibo, ${alias}.internal_linkman as acc_account_internal_linkman, 
    ${alias}.linkman_address as acc_account_linkman_address, ${alias}.bank as acc_account_bank, 
    ${alias}.qualification_id as acc_account_qualification_id, ${alias}.business_licence_code as acc_account_business_licence_code, 
    ${alias}.business_licence_expire_date as acc_account_business_licence_expire_date, 
    ${alias}.is_business_licence_indefinite as acc_account_is_business_licence_indefinite, 
    ${alias}.legal_person_name as acc_account_legal_person_name, ${alias}.legal_person_idcard_expire_date as acc_account_legal_person_idcard_expire_date, 
    ${alias}.is_legal_person_idcard_indefinite as acc_account_is_legal_person_idcard_indefinite, 
    ${alias}.audit_remark as acc_account_audit_remark, ${alias}.account_status as acc_account_account_status, 
    ${alias}.linkman_email as acc_account_linkman_email, ${alias}.gd_status as acc_account_gd_status, 
    ${alias}.agent_auth_expire_date as acc_account_agent_auth_expire_date, ${alias}.is_inner as acc_account_is_inner, 
    ${alias}.department_id as acc_account_department_id, ${alias}.is_support_seller as acc_account_is_support_seller, 
    ${alias}.is_support_game as acc_account_is_support_game, ${alias}.is_support_dpa as acc_account_is_support_dpa, 
    ${alias}.is_support_content as acc_account_is_support_content, ${alias}.product_line as acc_account_product_line, 
    ${alias}.phone_number as acc_account_phone_number, ${alias}.idcard_type as acc_account_idcard_type, 
    ${alias}.idcard_number as acc_account_idcard_number, ${alias}.idcard_expire_date as acc_account_idcard_expire_date, 
    ${alias}.personal_address as acc_account_personal_address, ${alias}.is_idcard_indefinite as acc_account_is_idcard_indefinite, 
    ${alias}.personal_name as acc_account_personal_name, ${alias}.group_id as acc_account_group_id, 
    ${alias}.product_line_id as acc_account_product_line_id, ${alias}.product_id as acc_account_product_id, 
    ${alias}.is_support_pickup as acc_account_is_support_pickup, ${alias}.is_support_mas as acc_account_is_support_mas, 
    ${alias}.auto_update_label as acc_account_auto_update_label, ${alias}.is_support_fly as acc_account_is_support_fly, 
    ${alias}.allow_cash_pay as acc_account_allow_cash_pay, ${alias}.allow_incentive_bonus_pay as acc_account_allow_incentive_bonus_pay, 
    ${alias}.customer_id as acc_account_customer_id, ${alias}.creator as acc_account_creator, 
    ${alias}.payment_period as acc_account_payment_period, ${alias}.is_support_local_ad as acc_account_is_support_local_ad, 
    ${alias}.first_industry_tag_id as acc_account_first_industry_tag_id, ${alias}.second_industry_tag_id as acc_account_second_industry_tag_id, 
    ${alias}.third_industry_tag_id as acc_account_third_industry_tag_id, ${alias}.commerce_category_first_id as acc_account_commerce_category_first_id, 
    ${alias}.commerce_category_second_id as acc_account_commerce_category_second_id, 
    ${alias}.allow_signing_bonus_pay as acc_account_allow_signing_bonus_pay, ${alias}.allow_fly_coin_pay as acc_account_allow_fly_coin_pay, 
    ${alias}.allow_flow_ticket_pay as acc_account_allow_flow_ticket_pay, ${alias}.finance_type as acc_account_finance_type, 
    ${alias}.has_mgk_form as acc_account_has_mgk_form, ${alias}.mgk_form_privacy_policy as acc_account_mgk_form_privacy_policy, 
    ${alias}.show_to_customer as acc_account_show_to_customer, ${alias}.is_support_clue_pass as acc_account_is_support_clue_pass
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    account_id, username, mobile, password_strength, salt, salt_password, status, crm_customer_id, 
    ctime, mtime, is_deleted, order_type, mid, account_type, active_time, name, icp_record_number, 
    icp_info_image, brand_domain, user_type, ad_status, version, category_first_id, category_second_id, 
    remark, is_agent, agent_type, business_role_id, company_name, area_id, dependency_agent_id, 
    website_name, weibo, internal_linkman, linkman_address, bank, qualification_id, business_licence_code, 
    business_licence_expire_date, is_business_licence_indefinite, legal_person_name, 
    legal_person_idcard_expire_date, is_legal_person_idcard_indefinite, audit_remark, 
    account_status, linkman_email, gd_status, agent_auth_expire_date, is_inner, department_id, 
    is_support_seller, is_support_game, is_support_dpa, is_support_content, product_line, 
    phone_number, idcard_type, idcard_number, idcard_expire_date, personal_address, is_idcard_indefinite, 
    personal_name, group_id, product_line_id, product_id, is_support_pickup, is_support_mas, 
    auto_update_label, is_support_fly, allow_cash_pay, allow_incentive_bonus_pay, customer_id, 
    creator, payment_period, is_support_local_ad, first_industry_tag_id, second_industry_tag_id, 
    third_industry_tag_id, commerce_category_first_id, commerce_category_second_id, allow_signing_bonus_pay, 
    allow_fly_coin_pay, allow_flow_ticket_pay, finance_type, has_mgk_form, mgk_form_privacy_policy, 
    show_to_customer, is_support_clue_pass
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from acc_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from acc_account
    where account_id = #{accountId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from acc_account
    where account_id = #{accountId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPoExample">
    delete from acc_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPo">
    <selectKey keyProperty="accountId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_account (username, mobile, password_strength, 
      salt, salt_password, status, 
      crm_customer_id, ctime, mtime, 
      is_deleted, order_type, mid, 
      account_type, active_time, name, 
      icp_record_number, icp_info_image, brand_domain, 
      user_type, ad_status, version, 
      category_first_id, category_second_id, remark, 
      is_agent, agent_type, business_role_id, 
      company_name, area_id, dependency_agent_id, 
      website_name, weibo, internal_linkman, 
      linkman_address, bank, qualification_id, 
      business_licence_code, business_licence_expire_date, 
      is_business_licence_indefinite, legal_person_name, 
      legal_person_idcard_expire_date, is_legal_person_idcard_indefinite, 
      audit_remark, account_status, linkman_email, 
      gd_status, agent_auth_expire_date, is_inner, 
      department_id, is_support_seller, is_support_game, 
      is_support_dpa, is_support_content, product_line, 
      phone_number, idcard_type, idcard_number, 
      idcard_expire_date, personal_address, is_idcard_indefinite, 
      personal_name, group_id, product_line_id, 
      product_id, is_support_pickup, is_support_mas, 
      auto_update_label, is_support_fly, allow_cash_pay, 
      allow_incentive_bonus_pay, customer_id, creator, 
      payment_period, is_support_local_ad, first_industry_tag_id, 
      second_industry_tag_id, third_industry_tag_id, 
      commerce_category_first_id, commerce_category_second_id, 
      allow_signing_bonus_pay, allow_fly_coin_pay, 
      allow_flow_ticket_pay, finance_type, has_mgk_form, 
      mgk_form_privacy_policy, show_to_customer, is_support_clue_pass
      )
    values (#{username,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{passwordStrength,jdbcType=TINYINT}, 
      #{salt,jdbcType=VARCHAR}, #{saltPassword,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{crmCustomerId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{orderType,jdbcType=TINYINT}, #{mid,jdbcType=BIGINT}, 
      #{accountType,jdbcType=TINYINT}, #{activeTime,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR}, 
      #{icpRecordNumber,jdbcType=VARCHAR}, #{icpInfoImage,jdbcType=VARCHAR}, #{brandDomain,jdbcType=VARCHAR}, 
      #{userType,jdbcType=TINYINT}, #{adStatus,jdbcType=TINYINT}, #{version,jdbcType=INTEGER}, 
      #{categoryFirstId,jdbcType=INTEGER}, #{categorySecondId,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{isAgent,jdbcType=TINYINT}, #{agentType,jdbcType=TINYINT}, #{businessRoleId,jdbcType=INTEGER}, 
      #{companyName,jdbcType=VARCHAR}, #{areaId,jdbcType=INTEGER}, #{dependencyAgentId,jdbcType=INTEGER}, 
      #{websiteName,jdbcType=VARCHAR}, #{weibo,jdbcType=VARCHAR}, #{internalLinkman,jdbcType=VARCHAR}, 
      #{linkmanAddress,jdbcType=VARCHAR}, #{bank,jdbcType=VARCHAR}, #{qualificationId,jdbcType=INTEGER}, 
      #{businessLicenceCode,jdbcType=VARCHAR}, #{businessLicenceExpireDate,jdbcType=DATE}, 
      #{isBusinessLicenceIndefinite,jdbcType=TINYINT}, #{legalPersonName,jdbcType=VARCHAR}, 
      #{legalPersonIdcardExpireDate,jdbcType=DATE}, #{isLegalPersonIdcardIndefinite,jdbcType=TINYINT}, 
      #{auditRemark,jdbcType=VARCHAR}, #{accountStatus,jdbcType=TINYINT}, #{linkmanEmail,jdbcType=VARCHAR}, 
      #{gdStatus,jdbcType=TINYINT}, #{agentAuthExpireDate,jdbcType=DATE}, #{isInner,jdbcType=TINYINT}, 
      #{departmentId,jdbcType=INTEGER}, #{isSupportSeller,jdbcType=TINYINT}, #{isSupportGame,jdbcType=TINYINT}, 
      #{isSupportDpa,jdbcType=TINYINT}, #{isSupportContent,jdbcType=TINYINT}, #{productLine,jdbcType=VARCHAR}, 
      #{phoneNumber,jdbcType=VARCHAR}, #{idcardType,jdbcType=TINYINT}, #{idcardNumber,jdbcType=VARCHAR}, 
      #{idcardExpireDate,jdbcType=TIMESTAMP}, #{personalAddress,jdbcType=VARCHAR}, #{isIdcardIndefinite,jdbcType=TINYINT}, 
      #{personalName,jdbcType=VARCHAR}, #{groupId,jdbcType=INTEGER}, #{productLineId,jdbcType=INTEGER}, 
      #{productId,jdbcType=INTEGER}, #{isSupportPickup,jdbcType=TINYINT}, #{isSupportMas,jdbcType=TINYINT}, 
      #{autoUpdateLabel,jdbcType=TINYINT}, #{isSupportFly,jdbcType=TINYINT}, #{allowCashPay,jdbcType=TINYINT}, 
      #{allowIncentiveBonusPay,jdbcType=TINYINT}, #{customerId,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{paymentPeriod,jdbcType=INTEGER}, #{isSupportLocalAd,jdbcType=TINYINT}, #{firstIndustryTagId,jdbcType=INTEGER}, 
      #{secondIndustryTagId,jdbcType=INTEGER}, #{thirdIndustryTagId,jdbcType=INTEGER}, 
      #{commerceCategoryFirstId,jdbcType=INTEGER}, #{commerceCategorySecondId,jdbcType=INTEGER}, 
      #{allowSigningBonusPay,jdbcType=TINYINT}, #{allowFlyCoinPay,jdbcType=TINYINT}, 
      #{allowFlowTicketPay,jdbcType=TINYINT}, #{financeType,jdbcType=TINYINT}, #{hasMgkForm,jdbcType=INTEGER}, 
      #{mgkFormPrivacyPolicy,jdbcType=INTEGER}, #{showToCustomer,jdbcType=TINYINT}, #{isSupportCluePass,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPo">
    <selectKey keyProperty="accountId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="passwordStrength != null">
        password_strength,
      </if>
      <if test="salt != null">
        salt,
      </if>
      <if test="saltPassword != null">
        salt_password,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="crmCustomerId != null">
        crm_customer_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="activeTime != null">
        active_time,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="icpRecordNumber != null">
        icp_record_number,
      </if>
      <if test="icpInfoImage != null">
        icp_info_image,
      </if>
      <if test="brandDomain != null">
        brand_domain,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="adStatus != null">
        ad_status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="categoryFirstId != null">
        category_first_id,
      </if>
      <if test="categorySecondId != null">
        category_second_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isAgent != null">
        is_agent,
      </if>
      <if test="agentType != null">
        agent_type,
      </if>
      <if test="businessRoleId != null">
        business_role_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="dependencyAgentId != null">
        dependency_agent_id,
      </if>
      <if test="websiteName != null">
        website_name,
      </if>
      <if test="weibo != null">
        weibo,
      </if>
      <if test="internalLinkman != null">
        internal_linkman,
      </if>
      <if test="linkmanAddress != null">
        linkman_address,
      </if>
      <if test="bank != null">
        bank,
      </if>
      <if test="qualificationId != null">
        qualification_id,
      </if>
      <if test="businessLicenceCode != null">
        business_licence_code,
      </if>
      <if test="businessLicenceExpireDate != null">
        business_licence_expire_date,
      </if>
      <if test="isBusinessLicenceIndefinite != null">
        is_business_licence_indefinite,
      </if>
      <if test="legalPersonName != null">
        legal_person_name,
      </if>
      <if test="legalPersonIdcardExpireDate != null">
        legal_person_idcard_expire_date,
      </if>
      <if test="isLegalPersonIdcardIndefinite != null">
        is_legal_person_idcard_indefinite,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="accountStatus != null">
        account_status,
      </if>
      <if test="linkmanEmail != null">
        linkman_email,
      </if>
      <if test="gdStatus != null">
        gd_status,
      </if>
      <if test="agentAuthExpireDate != null">
        agent_auth_expire_date,
      </if>
      <if test="isInner != null">
        is_inner,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="isSupportSeller != null">
        is_support_seller,
      </if>
      <if test="isSupportGame != null">
        is_support_game,
      </if>
      <if test="isSupportDpa != null">
        is_support_dpa,
      </if>
      <if test="isSupportContent != null">
        is_support_content,
      </if>
      <if test="productLine != null">
        product_line,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="idcardType != null">
        idcard_type,
      </if>
      <if test="idcardNumber != null">
        idcard_number,
      </if>
      <if test="idcardExpireDate != null">
        idcard_expire_date,
      </if>
      <if test="personalAddress != null">
        personal_address,
      </if>
      <if test="isIdcardIndefinite != null">
        is_idcard_indefinite,
      </if>
      <if test="personalName != null">
        personal_name,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="productLineId != null">
        product_line_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="isSupportPickup != null">
        is_support_pickup,
      </if>
      <if test="isSupportMas != null">
        is_support_mas,
      </if>
      <if test="autoUpdateLabel != null">
        auto_update_label,
      </if>
      <if test="isSupportFly != null">
        is_support_fly,
      </if>
      <if test="allowCashPay != null">
        allow_cash_pay,
      </if>
      <if test="allowIncentiveBonusPay != null">
        allow_incentive_bonus_pay,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="paymentPeriod != null">
        payment_period,
      </if>
      <if test="isSupportLocalAd != null">
        is_support_local_ad,
      </if>
      <if test="firstIndustryTagId != null">
        first_industry_tag_id,
      </if>
      <if test="secondIndustryTagId != null">
        second_industry_tag_id,
      </if>
      <if test="thirdIndustryTagId != null">
        third_industry_tag_id,
      </if>
      <if test="commerceCategoryFirstId != null">
        commerce_category_first_id,
      </if>
      <if test="commerceCategorySecondId != null">
        commerce_category_second_id,
      </if>
      <if test="allowSigningBonusPay != null">
        allow_signing_bonus_pay,
      </if>
      <if test="allowFlyCoinPay != null">
        allow_fly_coin_pay,
      </if>
      <if test="allowFlowTicketPay != null">
        allow_flow_ticket_pay,
      </if>
      <if test="financeType != null">
        finance_type,
      </if>
      <if test="hasMgkForm != null">
        has_mgk_form,
      </if>
      <if test="mgkFormPrivacyPolicy != null">
        mgk_form_privacy_policy,
      </if>
      <if test="showToCustomer != null">
        show_to_customer,
      </if>
      <if test="isSupportCluePass != null">
        is_support_clue_pass,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="passwordStrength != null">
        #{passwordStrength,jdbcType=TINYINT},
      </if>
      <if test="salt != null">
        #{salt,jdbcType=VARCHAR},
      </if>
      <if test="saltPassword != null">
        #{saltPassword,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="crmCustomerId != null">
        #{crmCustomerId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="activeTime != null">
        #{activeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="icpRecordNumber != null">
        #{icpRecordNumber,jdbcType=VARCHAR},
      </if>
      <if test="icpInfoImage != null">
        #{icpInfoImage,jdbcType=VARCHAR},
      </if>
      <if test="brandDomain != null">
        #{brandDomain,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=TINYINT},
      </if>
      <if test="adStatus != null">
        #{adStatus,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="categoryFirstId != null">
        #{categoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="categorySecondId != null">
        #{categorySecondId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isAgent != null">
        #{isAgent,jdbcType=TINYINT},
      </if>
      <if test="agentType != null">
        #{agentType,jdbcType=TINYINT},
      </if>
      <if test="businessRoleId != null">
        #{businessRoleId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="dependencyAgentId != null">
        #{dependencyAgentId,jdbcType=INTEGER},
      </if>
      <if test="websiteName != null">
        #{websiteName,jdbcType=VARCHAR},
      </if>
      <if test="weibo != null">
        #{weibo,jdbcType=VARCHAR},
      </if>
      <if test="internalLinkman != null">
        #{internalLinkman,jdbcType=VARCHAR},
      </if>
      <if test="linkmanAddress != null">
        #{linkmanAddress,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="qualificationId != null">
        #{qualificationId,jdbcType=INTEGER},
      </if>
      <if test="businessLicenceCode != null">
        #{businessLicenceCode,jdbcType=VARCHAR},
      </if>
      <if test="businessLicenceExpireDate != null">
        #{businessLicenceExpireDate,jdbcType=DATE},
      </if>
      <if test="isBusinessLicenceIndefinite != null">
        #{isBusinessLicenceIndefinite,jdbcType=TINYINT},
      </if>
      <if test="legalPersonName != null">
        #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonIdcardExpireDate != null">
        #{legalPersonIdcardExpireDate,jdbcType=DATE},
      </if>
      <if test="isLegalPersonIdcardIndefinite != null">
        #{isLegalPersonIdcardIndefinite,jdbcType=TINYINT},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="accountStatus != null">
        #{accountStatus,jdbcType=TINYINT},
      </if>
      <if test="linkmanEmail != null">
        #{linkmanEmail,jdbcType=VARCHAR},
      </if>
      <if test="gdStatus != null">
        #{gdStatus,jdbcType=TINYINT},
      </if>
      <if test="agentAuthExpireDate != null">
        #{agentAuthExpireDate,jdbcType=DATE},
      </if>
      <if test="isInner != null">
        #{isInner,jdbcType=TINYINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="isSupportSeller != null">
        #{isSupportSeller,jdbcType=TINYINT},
      </if>
      <if test="isSupportGame != null">
        #{isSupportGame,jdbcType=TINYINT},
      </if>
      <if test="isSupportDpa != null">
        #{isSupportDpa,jdbcType=TINYINT},
      </if>
      <if test="isSupportContent != null">
        #{isSupportContent,jdbcType=TINYINT},
      </if>
      <if test="productLine != null">
        #{productLine,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="idcardType != null">
        #{idcardType,jdbcType=TINYINT},
      </if>
      <if test="idcardNumber != null">
        #{idcardNumber,jdbcType=VARCHAR},
      </if>
      <if test="idcardExpireDate != null">
        #{idcardExpireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="personalAddress != null">
        #{personalAddress,jdbcType=VARCHAR},
      </if>
      <if test="isIdcardIndefinite != null">
        #{isIdcardIndefinite,jdbcType=TINYINT},
      </if>
      <if test="personalName != null">
        #{personalName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="productLineId != null">
        #{productLineId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="isSupportPickup != null">
        #{isSupportPickup,jdbcType=TINYINT},
      </if>
      <if test="isSupportMas != null">
        #{isSupportMas,jdbcType=TINYINT},
      </if>
      <if test="autoUpdateLabel != null">
        #{autoUpdateLabel,jdbcType=TINYINT},
      </if>
      <if test="isSupportFly != null">
        #{isSupportFly,jdbcType=TINYINT},
      </if>
      <if test="allowCashPay != null">
        #{allowCashPay,jdbcType=TINYINT},
      </if>
      <if test="allowIncentiveBonusPay != null">
        #{allowIncentiveBonusPay,jdbcType=TINYINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="paymentPeriod != null">
        #{paymentPeriod,jdbcType=INTEGER},
      </if>
      <if test="isSupportLocalAd != null">
        #{isSupportLocalAd,jdbcType=TINYINT},
      </if>
      <if test="firstIndustryTagId != null">
        #{firstIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="secondIndustryTagId != null">
        #{secondIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="thirdIndustryTagId != null">
        #{thirdIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategoryFirstId != null">
        #{commerceCategoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategorySecondId != null">
        #{commerceCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="allowSigningBonusPay != null">
        #{allowSigningBonusPay,jdbcType=TINYINT},
      </if>
      <if test="allowFlyCoinPay != null">
        #{allowFlyCoinPay,jdbcType=TINYINT},
      </if>
      <if test="allowFlowTicketPay != null">
        #{allowFlowTicketPay,jdbcType=TINYINT},
      </if>
      <if test="financeType != null">
        #{financeType,jdbcType=TINYINT},
      </if>
      <if test="hasMgkForm != null">
        #{hasMgkForm,jdbcType=INTEGER},
      </if>
      <if test="mgkFormPrivacyPolicy != null">
        #{mgkFormPrivacyPolicy,jdbcType=INTEGER},
      </if>
      <if test="showToCustomer != null">
        #{showToCustomer,jdbcType=TINYINT},
      </if>
      <if test="isSupportCluePass != null">
        #{isSupportCluePass,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPoExample" resultType="java.lang.Long">
    select count(*) from acc_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update acc_account
    <set>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.passwordStrength != null">
        password_strength = #{record.passwordStrength,jdbcType=TINYINT},
      </if>
      <if test="record.salt != null">
        salt = #{record.salt,jdbcType=VARCHAR},
      </if>
      <if test="record.saltPassword != null">
        salt_password = #{record.saltPassword,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.crmCustomerId != null">
        crm_customer_id = #{record.crmCustomerId,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=TINYINT},
      </if>
      <if test="record.activeTime != null">
        active_time = #{record.activeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.icpRecordNumber != null">
        icp_record_number = #{record.icpRecordNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.icpInfoImage != null">
        icp_info_image = #{record.icpInfoImage,jdbcType=VARCHAR},
      </if>
      <if test="record.brandDomain != null">
        brand_domain = #{record.brandDomain,jdbcType=VARCHAR},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=TINYINT},
      </if>
      <if test="record.adStatus != null">
        ad_status = #{record.adStatus,jdbcType=TINYINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.categoryFirstId != null">
        category_first_id = #{record.categoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="record.categorySecondId != null">
        category_second_id = #{record.categorySecondId,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isAgent != null">
        is_agent = #{record.isAgent,jdbcType=TINYINT},
      </if>
      <if test="record.agentType != null">
        agent_type = #{record.agentType,jdbcType=TINYINT},
      </if>
      <if test="record.businessRoleId != null">
        business_role_id = #{record.businessRoleId,jdbcType=INTEGER},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.areaId != null">
        area_id = #{record.areaId,jdbcType=INTEGER},
      </if>
      <if test="record.dependencyAgentId != null">
        dependency_agent_id = #{record.dependencyAgentId,jdbcType=INTEGER},
      </if>
      <if test="record.websiteName != null">
        website_name = #{record.websiteName,jdbcType=VARCHAR},
      </if>
      <if test="record.weibo != null">
        weibo = #{record.weibo,jdbcType=VARCHAR},
      </if>
      <if test="record.internalLinkman != null">
        internal_linkman = #{record.internalLinkman,jdbcType=VARCHAR},
      </if>
      <if test="record.linkmanAddress != null">
        linkman_address = #{record.linkmanAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.bank != null">
        bank = #{record.bank,jdbcType=VARCHAR},
      </if>
      <if test="record.qualificationId != null">
        qualification_id = #{record.qualificationId,jdbcType=INTEGER},
      </if>
      <if test="record.businessLicenceCode != null">
        business_licence_code = #{record.businessLicenceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.businessLicenceExpireDate != null">
        business_licence_expire_date = #{record.businessLicenceExpireDate,jdbcType=DATE},
      </if>
      <if test="record.isBusinessLicenceIndefinite != null">
        is_business_licence_indefinite = #{record.isBusinessLicenceIndefinite,jdbcType=TINYINT},
      </if>
      <if test="record.legalPersonName != null">
        legal_person_name = #{record.legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="record.legalPersonIdcardExpireDate != null">
        legal_person_idcard_expire_date = #{record.legalPersonIdcardExpireDate,jdbcType=DATE},
      </if>
      <if test="record.isLegalPersonIdcardIndefinite != null">
        is_legal_person_idcard_indefinite = #{record.isLegalPersonIdcardIndefinite,jdbcType=TINYINT},
      </if>
      <if test="record.auditRemark != null">
        audit_remark = #{record.auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.accountStatus != null">
        account_status = #{record.accountStatus,jdbcType=TINYINT},
      </if>
      <if test="record.linkmanEmail != null">
        linkman_email = #{record.linkmanEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.gdStatus != null">
        gd_status = #{record.gdStatus,jdbcType=TINYINT},
      </if>
      <if test="record.agentAuthExpireDate != null">
        agent_auth_expire_date = #{record.agentAuthExpireDate,jdbcType=DATE},
      </if>
      <if test="record.isInner != null">
        is_inner = #{record.isInner,jdbcType=TINYINT},
      </if>
      <if test="record.departmentId != null">
        department_id = #{record.departmentId,jdbcType=INTEGER},
      </if>
      <if test="record.isSupportSeller != null">
        is_support_seller = #{record.isSupportSeller,jdbcType=TINYINT},
      </if>
      <if test="record.isSupportGame != null">
        is_support_game = #{record.isSupportGame,jdbcType=TINYINT},
      </if>
      <if test="record.isSupportDpa != null">
        is_support_dpa = #{record.isSupportDpa,jdbcType=TINYINT},
      </if>
      <if test="record.isSupportContent != null">
        is_support_content = #{record.isSupportContent,jdbcType=TINYINT},
      </if>
      <if test="record.productLine != null">
        product_line = #{record.productLine,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNumber != null">
        phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.idcardType != null">
        idcard_type = #{record.idcardType,jdbcType=TINYINT},
      </if>
      <if test="record.idcardNumber != null">
        idcard_number = #{record.idcardNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.idcardExpireDate != null">
        idcard_expire_date = #{record.idcardExpireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.personalAddress != null">
        personal_address = #{record.personalAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.isIdcardIndefinite != null">
        is_idcard_indefinite = #{record.isIdcardIndefinite,jdbcType=TINYINT},
      </if>
      <if test="record.personalName != null">
        personal_name = #{record.personalName,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=INTEGER},
      </if>
      <if test="record.productLineId != null">
        product_line_id = #{record.productLineId,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=INTEGER},
      </if>
      <if test="record.isSupportPickup != null">
        is_support_pickup = #{record.isSupportPickup,jdbcType=TINYINT},
      </if>
      <if test="record.isSupportMas != null">
        is_support_mas = #{record.isSupportMas,jdbcType=TINYINT},
      </if>
      <if test="record.autoUpdateLabel != null">
        auto_update_label = #{record.autoUpdateLabel,jdbcType=TINYINT},
      </if>
      <if test="record.isSupportFly != null">
        is_support_fly = #{record.isSupportFly,jdbcType=TINYINT},
      </if>
      <if test="record.allowCashPay != null">
        allow_cash_pay = #{record.allowCashPay,jdbcType=TINYINT},
      </if>
      <if test="record.allowIncentiveBonusPay != null">
        allow_incentive_bonus_pay = #{record.allowIncentiveBonusPay,jdbcType=TINYINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentPeriod != null">
        payment_period = #{record.paymentPeriod,jdbcType=INTEGER},
      </if>
      <if test="record.isSupportLocalAd != null">
        is_support_local_ad = #{record.isSupportLocalAd,jdbcType=TINYINT},
      </if>
      <if test="record.firstIndustryTagId != null">
        first_industry_tag_id = #{record.firstIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="record.secondIndustryTagId != null">
        second_industry_tag_id = #{record.secondIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="record.thirdIndustryTagId != null">
        third_industry_tag_id = #{record.thirdIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="record.commerceCategoryFirstId != null">
        commerce_category_first_id = #{record.commerceCategoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="record.commerceCategorySecondId != null">
        commerce_category_second_id = #{record.commerceCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="record.allowSigningBonusPay != null">
        allow_signing_bonus_pay = #{record.allowSigningBonusPay,jdbcType=TINYINT},
      </if>
      <if test="record.allowFlyCoinPay != null">
        allow_fly_coin_pay = #{record.allowFlyCoinPay,jdbcType=TINYINT},
      </if>
      <if test="record.allowFlowTicketPay != null">
        allow_flow_ticket_pay = #{record.allowFlowTicketPay,jdbcType=TINYINT},
      </if>
      <if test="record.financeType != null">
        finance_type = #{record.financeType,jdbcType=TINYINT},
      </if>
      <if test="record.hasMgkForm != null">
        has_mgk_form = #{record.hasMgkForm,jdbcType=INTEGER},
      </if>
      <if test="record.mgkFormPrivacyPolicy != null">
        mgk_form_privacy_policy = #{record.mgkFormPrivacyPolicy,jdbcType=INTEGER},
      </if>
      <if test="record.showToCustomer != null">
        show_to_customer = #{record.showToCustomer,jdbcType=TINYINT},
      </if>
      <if test="record.isSupportCluePass != null">
        is_support_clue_pass = #{record.isSupportCluePass,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update acc_account
    set account_id = #{record.accountId,jdbcType=INTEGER},
      username = #{record.username,jdbcType=VARCHAR},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      password_strength = #{record.passwordStrength,jdbcType=TINYINT},
      salt = #{record.salt,jdbcType=VARCHAR},
      salt_password = #{record.saltPassword,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      crm_customer_id = #{record.crmCustomerId,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      order_type = #{record.orderType,jdbcType=TINYINT},
      mid = #{record.mid,jdbcType=BIGINT},
      account_type = #{record.accountType,jdbcType=TINYINT},
      active_time = #{record.activeTime,jdbcType=TIMESTAMP},
      name = #{record.name,jdbcType=VARCHAR},
      icp_record_number = #{record.icpRecordNumber,jdbcType=VARCHAR},
      icp_info_image = #{record.icpInfoImage,jdbcType=VARCHAR},
      brand_domain = #{record.brandDomain,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=TINYINT},
      ad_status = #{record.adStatus,jdbcType=TINYINT},
      version = #{record.version,jdbcType=INTEGER},
      category_first_id = #{record.categoryFirstId,jdbcType=INTEGER},
      category_second_id = #{record.categorySecondId,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_agent = #{record.isAgent,jdbcType=TINYINT},
      agent_type = #{record.agentType,jdbcType=TINYINT},
      business_role_id = #{record.businessRoleId,jdbcType=INTEGER},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      area_id = #{record.areaId,jdbcType=INTEGER},
      dependency_agent_id = #{record.dependencyAgentId,jdbcType=INTEGER},
      website_name = #{record.websiteName,jdbcType=VARCHAR},
      weibo = #{record.weibo,jdbcType=VARCHAR},
      internal_linkman = #{record.internalLinkman,jdbcType=VARCHAR},
      linkman_address = #{record.linkmanAddress,jdbcType=VARCHAR},
      bank = #{record.bank,jdbcType=VARCHAR},
      qualification_id = #{record.qualificationId,jdbcType=INTEGER},
      business_licence_code = #{record.businessLicenceCode,jdbcType=VARCHAR},
      business_licence_expire_date = #{record.businessLicenceExpireDate,jdbcType=DATE},
      is_business_licence_indefinite = #{record.isBusinessLicenceIndefinite,jdbcType=TINYINT},
      legal_person_name = #{record.legalPersonName,jdbcType=VARCHAR},
      legal_person_idcard_expire_date = #{record.legalPersonIdcardExpireDate,jdbcType=DATE},
      is_legal_person_idcard_indefinite = #{record.isLegalPersonIdcardIndefinite,jdbcType=TINYINT},
      audit_remark = #{record.auditRemark,jdbcType=VARCHAR},
      account_status = #{record.accountStatus,jdbcType=TINYINT},
      linkman_email = #{record.linkmanEmail,jdbcType=VARCHAR},
      gd_status = #{record.gdStatus,jdbcType=TINYINT},
      agent_auth_expire_date = #{record.agentAuthExpireDate,jdbcType=DATE},
      is_inner = #{record.isInner,jdbcType=TINYINT},
      department_id = #{record.departmentId,jdbcType=INTEGER},
      is_support_seller = #{record.isSupportSeller,jdbcType=TINYINT},
      is_support_game = #{record.isSupportGame,jdbcType=TINYINT},
      is_support_dpa = #{record.isSupportDpa,jdbcType=TINYINT},
      is_support_content = #{record.isSupportContent,jdbcType=TINYINT},
      product_line = #{record.productLine,jdbcType=VARCHAR},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      idcard_type = #{record.idcardType,jdbcType=TINYINT},
      idcard_number = #{record.idcardNumber,jdbcType=VARCHAR},
      idcard_expire_date = #{record.idcardExpireDate,jdbcType=TIMESTAMP},
      personal_address = #{record.personalAddress,jdbcType=VARCHAR},
      is_idcard_indefinite = #{record.isIdcardIndefinite,jdbcType=TINYINT},
      personal_name = #{record.personalName,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=INTEGER},
      product_line_id = #{record.productLineId,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=INTEGER},
      is_support_pickup = #{record.isSupportPickup,jdbcType=TINYINT},
      is_support_mas = #{record.isSupportMas,jdbcType=TINYINT},
      auto_update_label = #{record.autoUpdateLabel,jdbcType=TINYINT},
      is_support_fly = #{record.isSupportFly,jdbcType=TINYINT},
      allow_cash_pay = #{record.allowCashPay,jdbcType=TINYINT},
      allow_incentive_bonus_pay = #{record.allowIncentiveBonusPay,jdbcType=TINYINT},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      creator = #{record.creator,jdbcType=VARCHAR},
      payment_period = #{record.paymentPeriod,jdbcType=INTEGER},
      is_support_local_ad = #{record.isSupportLocalAd,jdbcType=TINYINT},
      first_industry_tag_id = #{record.firstIndustryTagId,jdbcType=INTEGER},
      second_industry_tag_id = #{record.secondIndustryTagId,jdbcType=INTEGER},
      third_industry_tag_id = #{record.thirdIndustryTagId,jdbcType=INTEGER},
      commerce_category_first_id = #{record.commerceCategoryFirstId,jdbcType=INTEGER},
      commerce_category_second_id = #{record.commerceCategorySecondId,jdbcType=INTEGER},
      allow_signing_bonus_pay = #{record.allowSigningBonusPay,jdbcType=TINYINT},
      allow_fly_coin_pay = #{record.allowFlyCoinPay,jdbcType=TINYINT},
      allow_flow_ticket_pay = #{record.allowFlowTicketPay,jdbcType=TINYINT},
      finance_type = #{record.financeType,jdbcType=TINYINT},
      has_mgk_form = #{record.hasMgkForm,jdbcType=INTEGER},
      mgk_form_privacy_policy = #{record.mgkFormPrivacyPolicy,jdbcType=INTEGER},
      show_to_customer = #{record.showToCustomer,jdbcType=TINYINT},
      is_support_clue_pass = #{record.isSupportCluePass,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPo">
    update acc_account
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="passwordStrength != null">
        password_strength = #{passwordStrength,jdbcType=TINYINT},
      </if>
      <if test="salt != null">
        salt = #{salt,jdbcType=VARCHAR},
      </if>
      <if test="saltPassword != null">
        salt_password = #{saltPassword,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="crmCustomerId != null">
        crm_customer_id = #{crmCustomerId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="activeTime != null">
        active_time = #{activeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="icpRecordNumber != null">
        icp_record_number = #{icpRecordNumber,jdbcType=VARCHAR},
      </if>
      <if test="icpInfoImage != null">
        icp_info_image = #{icpInfoImage,jdbcType=VARCHAR},
      </if>
      <if test="brandDomain != null">
        brand_domain = #{brandDomain,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=TINYINT},
      </if>
      <if test="adStatus != null">
        ad_status = #{adStatus,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="categoryFirstId != null">
        category_first_id = #{categoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="categorySecondId != null">
        category_second_id = #{categorySecondId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isAgent != null">
        is_agent = #{isAgent,jdbcType=TINYINT},
      </if>
      <if test="agentType != null">
        agent_type = #{agentType,jdbcType=TINYINT},
      </if>
      <if test="businessRoleId != null">
        business_role_id = #{businessRoleId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="dependencyAgentId != null">
        dependency_agent_id = #{dependencyAgentId,jdbcType=INTEGER},
      </if>
      <if test="websiteName != null">
        website_name = #{websiteName,jdbcType=VARCHAR},
      </if>
      <if test="weibo != null">
        weibo = #{weibo,jdbcType=VARCHAR},
      </if>
      <if test="internalLinkman != null">
        internal_linkman = #{internalLinkman,jdbcType=VARCHAR},
      </if>
      <if test="linkmanAddress != null">
        linkman_address = #{linkmanAddress,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        bank = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="qualificationId != null">
        qualification_id = #{qualificationId,jdbcType=INTEGER},
      </if>
      <if test="businessLicenceCode != null">
        business_licence_code = #{businessLicenceCode,jdbcType=VARCHAR},
      </if>
      <if test="businessLicenceExpireDate != null">
        business_licence_expire_date = #{businessLicenceExpireDate,jdbcType=DATE},
      </if>
      <if test="isBusinessLicenceIndefinite != null">
        is_business_licence_indefinite = #{isBusinessLicenceIndefinite,jdbcType=TINYINT},
      </if>
      <if test="legalPersonName != null">
        legal_person_name = #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonIdcardExpireDate != null">
        legal_person_idcard_expire_date = #{legalPersonIdcardExpireDate,jdbcType=DATE},
      </if>
      <if test="isLegalPersonIdcardIndefinite != null">
        is_legal_person_idcard_indefinite = #{isLegalPersonIdcardIndefinite,jdbcType=TINYINT},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="accountStatus != null">
        account_status = #{accountStatus,jdbcType=TINYINT},
      </if>
      <if test="linkmanEmail != null">
        linkman_email = #{linkmanEmail,jdbcType=VARCHAR},
      </if>
      <if test="gdStatus != null">
        gd_status = #{gdStatus,jdbcType=TINYINT},
      </if>
      <if test="agentAuthExpireDate != null">
        agent_auth_expire_date = #{agentAuthExpireDate,jdbcType=DATE},
      </if>
      <if test="isInner != null">
        is_inner = #{isInner,jdbcType=TINYINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="isSupportSeller != null">
        is_support_seller = #{isSupportSeller,jdbcType=TINYINT},
      </if>
      <if test="isSupportGame != null">
        is_support_game = #{isSupportGame,jdbcType=TINYINT},
      </if>
      <if test="isSupportDpa != null">
        is_support_dpa = #{isSupportDpa,jdbcType=TINYINT},
      </if>
      <if test="isSupportContent != null">
        is_support_content = #{isSupportContent,jdbcType=TINYINT},
      </if>
      <if test="productLine != null">
        product_line = #{productLine,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="idcardType != null">
        idcard_type = #{idcardType,jdbcType=TINYINT},
      </if>
      <if test="idcardNumber != null">
        idcard_number = #{idcardNumber,jdbcType=VARCHAR},
      </if>
      <if test="idcardExpireDate != null">
        idcard_expire_date = #{idcardExpireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="personalAddress != null">
        personal_address = #{personalAddress,jdbcType=VARCHAR},
      </if>
      <if test="isIdcardIndefinite != null">
        is_idcard_indefinite = #{isIdcardIndefinite,jdbcType=TINYINT},
      </if>
      <if test="personalName != null">
        personal_name = #{personalName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="productLineId != null">
        product_line_id = #{productLineId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="isSupportPickup != null">
        is_support_pickup = #{isSupportPickup,jdbcType=TINYINT},
      </if>
      <if test="isSupportMas != null">
        is_support_mas = #{isSupportMas,jdbcType=TINYINT},
      </if>
      <if test="autoUpdateLabel != null">
        auto_update_label = #{autoUpdateLabel,jdbcType=TINYINT},
      </if>
      <if test="isSupportFly != null">
        is_support_fly = #{isSupportFly,jdbcType=TINYINT},
      </if>
      <if test="allowCashPay != null">
        allow_cash_pay = #{allowCashPay,jdbcType=TINYINT},
      </if>
      <if test="allowIncentiveBonusPay != null">
        allow_incentive_bonus_pay = #{allowIncentiveBonusPay,jdbcType=TINYINT},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="paymentPeriod != null">
        payment_period = #{paymentPeriod,jdbcType=INTEGER},
      </if>
      <if test="isSupportLocalAd != null">
        is_support_local_ad = #{isSupportLocalAd,jdbcType=TINYINT},
      </if>
      <if test="firstIndustryTagId != null">
        first_industry_tag_id = #{firstIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="secondIndustryTagId != null">
        second_industry_tag_id = #{secondIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="thirdIndustryTagId != null">
        third_industry_tag_id = #{thirdIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategoryFirstId != null">
        commerce_category_first_id = #{commerceCategoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategorySecondId != null">
        commerce_category_second_id = #{commerceCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="allowSigningBonusPay != null">
        allow_signing_bonus_pay = #{allowSigningBonusPay,jdbcType=TINYINT},
      </if>
      <if test="allowFlyCoinPay != null">
        allow_fly_coin_pay = #{allowFlyCoinPay,jdbcType=TINYINT},
      </if>
      <if test="allowFlowTicketPay != null">
        allow_flow_ticket_pay = #{allowFlowTicketPay,jdbcType=TINYINT},
      </if>
      <if test="financeType != null">
        finance_type = #{financeType,jdbcType=TINYINT},
      </if>
      <if test="hasMgkForm != null">
        has_mgk_form = #{hasMgkForm,jdbcType=INTEGER},
      </if>
      <if test="mgkFormPrivacyPolicy != null">
        mgk_form_privacy_policy = #{mgkFormPrivacyPolicy,jdbcType=INTEGER},
      </if>
      <if test="showToCustomer != null">
        show_to_customer = #{showToCustomer,jdbcType=TINYINT},
      </if>
      <if test="isSupportCluePass != null">
        is_support_clue_pass = #{isSupportCluePass,jdbcType=TINYINT},
      </if>
    </set>
    where account_id = #{accountId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPo">
    update acc_account
    set username = #{username,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      password_strength = #{passwordStrength,jdbcType=TINYINT},
      salt = #{salt,jdbcType=VARCHAR},
      salt_password = #{saltPassword,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      crm_customer_id = #{crmCustomerId,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      order_type = #{orderType,jdbcType=TINYINT},
      mid = #{mid,jdbcType=BIGINT},
      account_type = #{accountType,jdbcType=TINYINT},
      active_time = #{activeTime,jdbcType=TIMESTAMP},
      name = #{name,jdbcType=VARCHAR},
      icp_record_number = #{icpRecordNumber,jdbcType=VARCHAR},
      icp_info_image = #{icpInfoImage,jdbcType=VARCHAR},
      brand_domain = #{brandDomain,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=TINYINT},
      ad_status = #{adStatus,jdbcType=TINYINT},
      version = #{version,jdbcType=INTEGER},
      category_first_id = #{categoryFirstId,jdbcType=INTEGER},
      category_second_id = #{categorySecondId,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      is_agent = #{isAgent,jdbcType=TINYINT},
      agent_type = #{agentType,jdbcType=TINYINT},
      business_role_id = #{businessRoleId,jdbcType=INTEGER},
      company_name = #{companyName,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=INTEGER},
      dependency_agent_id = #{dependencyAgentId,jdbcType=INTEGER},
      website_name = #{websiteName,jdbcType=VARCHAR},
      weibo = #{weibo,jdbcType=VARCHAR},
      internal_linkman = #{internalLinkman,jdbcType=VARCHAR},
      linkman_address = #{linkmanAddress,jdbcType=VARCHAR},
      bank = #{bank,jdbcType=VARCHAR},
      qualification_id = #{qualificationId,jdbcType=INTEGER},
      business_licence_code = #{businessLicenceCode,jdbcType=VARCHAR},
      business_licence_expire_date = #{businessLicenceExpireDate,jdbcType=DATE},
      is_business_licence_indefinite = #{isBusinessLicenceIndefinite,jdbcType=TINYINT},
      legal_person_name = #{legalPersonName,jdbcType=VARCHAR},
      legal_person_idcard_expire_date = #{legalPersonIdcardExpireDate,jdbcType=DATE},
      is_legal_person_idcard_indefinite = #{isLegalPersonIdcardIndefinite,jdbcType=TINYINT},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      account_status = #{accountStatus,jdbcType=TINYINT},
      linkman_email = #{linkmanEmail,jdbcType=VARCHAR},
      gd_status = #{gdStatus,jdbcType=TINYINT},
      agent_auth_expire_date = #{agentAuthExpireDate,jdbcType=DATE},
      is_inner = #{isInner,jdbcType=TINYINT},
      department_id = #{departmentId,jdbcType=INTEGER},
      is_support_seller = #{isSupportSeller,jdbcType=TINYINT},
      is_support_game = #{isSupportGame,jdbcType=TINYINT},
      is_support_dpa = #{isSupportDpa,jdbcType=TINYINT},
      is_support_content = #{isSupportContent,jdbcType=TINYINT},
      product_line = #{productLine,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      idcard_type = #{idcardType,jdbcType=TINYINT},
      idcard_number = #{idcardNumber,jdbcType=VARCHAR},
      idcard_expire_date = #{idcardExpireDate,jdbcType=TIMESTAMP},
      personal_address = #{personalAddress,jdbcType=VARCHAR},
      is_idcard_indefinite = #{isIdcardIndefinite,jdbcType=TINYINT},
      personal_name = #{personalName,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=INTEGER},
      product_line_id = #{productLineId,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=INTEGER},
      is_support_pickup = #{isSupportPickup,jdbcType=TINYINT},
      is_support_mas = #{isSupportMas,jdbcType=TINYINT},
      auto_update_label = #{autoUpdateLabel,jdbcType=TINYINT},
      is_support_fly = #{isSupportFly,jdbcType=TINYINT},
      allow_cash_pay = #{allowCashPay,jdbcType=TINYINT},
      allow_incentive_bonus_pay = #{allowIncentiveBonusPay,jdbcType=TINYINT},
      customer_id = #{customerId,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      payment_period = #{paymentPeriod,jdbcType=INTEGER},
      is_support_local_ad = #{isSupportLocalAd,jdbcType=TINYINT},
      first_industry_tag_id = #{firstIndustryTagId,jdbcType=INTEGER},
      second_industry_tag_id = #{secondIndustryTagId,jdbcType=INTEGER},
      third_industry_tag_id = #{thirdIndustryTagId,jdbcType=INTEGER},
      commerce_category_first_id = #{commerceCategoryFirstId,jdbcType=INTEGER},
      commerce_category_second_id = #{commerceCategorySecondId,jdbcType=INTEGER},
      allow_signing_bonus_pay = #{allowSigningBonusPay,jdbcType=TINYINT},
      allow_fly_coin_pay = #{allowFlyCoinPay,jdbcType=TINYINT},
      allow_flow_ticket_pay = #{allowFlowTicketPay,jdbcType=TINYINT},
      finance_type = #{financeType,jdbcType=TINYINT},
      has_mgk_form = #{hasMgkForm,jdbcType=INTEGER},
      mgk_form_privacy_policy = #{mgkFormPrivacyPolicy,jdbcType=INTEGER},
      show_to_customer = #{showToCustomer,jdbcType=TINYINT},
      is_support_clue_pass = #{isSupportCluePass,jdbcType=TINYINT}
    where account_id = #{accountId,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPo">
    <selectKey keyProperty="accountId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_account (username, mobile, password_strength, 
      salt, salt_password, status, 
      crm_customer_id, ctime, mtime, 
      is_deleted, order_type, mid, 
      account_type, active_time, name, 
      icp_record_number, icp_info_image, brand_domain, 
      user_type, ad_status, version, 
      category_first_id, category_second_id, remark, 
      is_agent, agent_type, business_role_id, 
      company_name, area_id, dependency_agent_id, 
      website_name, weibo, internal_linkman, 
      linkman_address, bank, qualification_id, 
      business_licence_code, business_licence_expire_date, 
      is_business_licence_indefinite, legal_person_name, 
      legal_person_idcard_expire_date, is_legal_person_idcard_indefinite, 
      audit_remark, account_status, linkman_email, 
      gd_status, agent_auth_expire_date, is_inner, 
      department_id, is_support_seller, is_support_game, 
      is_support_dpa, is_support_content, product_line, 
      phone_number, idcard_type, idcard_number, 
      idcard_expire_date, personal_address, is_idcard_indefinite, 
      personal_name, group_id, product_line_id, 
      product_id, is_support_pickup, is_support_mas, 
      auto_update_label, is_support_fly, allow_cash_pay, 
      allow_incentive_bonus_pay, customer_id, creator, 
      payment_period, is_support_local_ad, first_industry_tag_id, 
      second_industry_tag_id, third_industry_tag_id, 
      commerce_category_first_id, commerce_category_second_id, 
      allow_signing_bonus_pay, allow_fly_coin_pay, 
      allow_flow_ticket_pay, finance_type, has_mgk_form, 
      mgk_form_privacy_policy, show_to_customer, is_support_clue_pass
      )
    values (#{username,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{passwordStrength,jdbcType=TINYINT}, 
      #{salt,jdbcType=VARCHAR}, #{saltPassword,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{crmCustomerId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{orderType,jdbcType=TINYINT}, #{mid,jdbcType=BIGINT}, 
      #{accountType,jdbcType=TINYINT}, #{activeTime,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR}, 
      #{icpRecordNumber,jdbcType=VARCHAR}, #{icpInfoImage,jdbcType=VARCHAR}, #{brandDomain,jdbcType=VARCHAR}, 
      #{userType,jdbcType=TINYINT}, #{adStatus,jdbcType=TINYINT}, #{version,jdbcType=INTEGER}, 
      #{categoryFirstId,jdbcType=INTEGER}, #{categorySecondId,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{isAgent,jdbcType=TINYINT}, #{agentType,jdbcType=TINYINT}, #{businessRoleId,jdbcType=INTEGER}, 
      #{companyName,jdbcType=VARCHAR}, #{areaId,jdbcType=INTEGER}, #{dependencyAgentId,jdbcType=INTEGER}, 
      #{websiteName,jdbcType=VARCHAR}, #{weibo,jdbcType=VARCHAR}, #{internalLinkman,jdbcType=VARCHAR}, 
      #{linkmanAddress,jdbcType=VARCHAR}, #{bank,jdbcType=VARCHAR}, #{qualificationId,jdbcType=INTEGER}, 
      #{businessLicenceCode,jdbcType=VARCHAR}, #{businessLicenceExpireDate,jdbcType=DATE}, 
      #{isBusinessLicenceIndefinite,jdbcType=TINYINT}, #{legalPersonName,jdbcType=VARCHAR}, 
      #{legalPersonIdcardExpireDate,jdbcType=DATE}, #{isLegalPersonIdcardIndefinite,jdbcType=TINYINT}, 
      #{auditRemark,jdbcType=VARCHAR}, #{accountStatus,jdbcType=TINYINT}, #{linkmanEmail,jdbcType=VARCHAR}, 
      #{gdStatus,jdbcType=TINYINT}, #{agentAuthExpireDate,jdbcType=DATE}, #{isInner,jdbcType=TINYINT}, 
      #{departmentId,jdbcType=INTEGER}, #{isSupportSeller,jdbcType=TINYINT}, #{isSupportGame,jdbcType=TINYINT}, 
      #{isSupportDpa,jdbcType=TINYINT}, #{isSupportContent,jdbcType=TINYINT}, #{productLine,jdbcType=VARCHAR}, 
      #{phoneNumber,jdbcType=VARCHAR}, #{idcardType,jdbcType=TINYINT}, #{idcardNumber,jdbcType=VARCHAR}, 
      #{idcardExpireDate,jdbcType=TIMESTAMP}, #{personalAddress,jdbcType=VARCHAR}, #{isIdcardIndefinite,jdbcType=TINYINT}, 
      #{personalName,jdbcType=VARCHAR}, #{groupId,jdbcType=INTEGER}, #{productLineId,jdbcType=INTEGER}, 
      #{productId,jdbcType=INTEGER}, #{isSupportPickup,jdbcType=TINYINT}, #{isSupportMas,jdbcType=TINYINT}, 
      #{autoUpdateLabel,jdbcType=TINYINT}, #{isSupportFly,jdbcType=TINYINT}, #{allowCashPay,jdbcType=TINYINT}, 
      #{allowIncentiveBonusPay,jdbcType=TINYINT}, #{customerId,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{paymentPeriod,jdbcType=INTEGER}, #{isSupportLocalAd,jdbcType=TINYINT}, #{firstIndustryTagId,jdbcType=INTEGER}, 
      #{secondIndustryTagId,jdbcType=INTEGER}, #{thirdIndustryTagId,jdbcType=INTEGER}, 
      #{commerceCategoryFirstId,jdbcType=INTEGER}, #{commerceCategorySecondId,jdbcType=INTEGER}, 
      #{allowSigningBonusPay,jdbcType=TINYINT}, #{allowFlyCoinPay,jdbcType=TINYINT}, 
      #{allowFlowTicketPay,jdbcType=TINYINT}, #{financeType,jdbcType=TINYINT}, #{hasMgkForm,jdbcType=INTEGER}, 
      #{mgkFormPrivacyPolicy,jdbcType=INTEGER}, #{showToCustomer,jdbcType=TINYINT}, #{isSupportCluePass,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      username = values(username),
      mobile = values(mobile),
      password_strength = values(password_strength),
      salt = values(salt),
      salt_password = values(salt_password),
      status = values(status),
      crm_customer_id = values(crm_customer_id),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      order_type = values(order_type),
      mid = values(mid),
      account_type = values(account_type),
      active_time = values(active_time),
      name = values(name),
      icp_record_number = values(icp_record_number),
      icp_info_image = values(icp_info_image),
      brand_domain = values(brand_domain),
      user_type = values(user_type),
      ad_status = values(ad_status),
      version = values(version),
      category_first_id = values(category_first_id),
      category_second_id = values(category_second_id),
      remark = values(remark),
      is_agent = values(is_agent),
      agent_type = values(agent_type),
      business_role_id = values(business_role_id),
      company_name = values(company_name),
      area_id = values(area_id),
      dependency_agent_id = values(dependency_agent_id),
      website_name = values(website_name),
      weibo = values(weibo),
      internal_linkman = values(internal_linkman),
      linkman_address = values(linkman_address),
      bank = values(bank),
      qualification_id = values(qualification_id),
      business_licence_code = values(business_licence_code),
      business_licence_expire_date = values(business_licence_expire_date),
      is_business_licence_indefinite = values(is_business_licence_indefinite),
      legal_person_name = values(legal_person_name),
      legal_person_idcard_expire_date = values(legal_person_idcard_expire_date),
      is_legal_person_idcard_indefinite = values(is_legal_person_idcard_indefinite),
      audit_remark = values(audit_remark),
      account_status = values(account_status),
      linkman_email = values(linkman_email),
      gd_status = values(gd_status),
      agent_auth_expire_date = values(agent_auth_expire_date),
      is_inner = values(is_inner),
      department_id = values(department_id),
      is_support_seller = values(is_support_seller),
      is_support_game = values(is_support_game),
      is_support_dpa = values(is_support_dpa),
      is_support_content = values(is_support_content),
      product_line = values(product_line),
      phone_number = values(phone_number),
      idcard_type = values(idcard_type),
      idcard_number = values(idcard_number),
      idcard_expire_date = values(idcard_expire_date),
      personal_address = values(personal_address),
      is_idcard_indefinite = values(is_idcard_indefinite),
      personal_name = values(personal_name),
      group_id = values(group_id),
      product_line_id = values(product_line_id),
      product_id = values(product_id),
      is_support_pickup = values(is_support_pickup),
      is_support_mas = values(is_support_mas),
      auto_update_label = values(auto_update_label),
      is_support_fly = values(is_support_fly),
      allow_cash_pay = values(allow_cash_pay),
      allow_incentive_bonus_pay = values(allow_incentive_bonus_pay),
      customer_id = values(customer_id),
      creator = values(creator),
      payment_period = values(payment_period),
      is_support_local_ad = values(is_support_local_ad),
      first_industry_tag_id = values(first_industry_tag_id),
      second_industry_tag_id = values(second_industry_tag_id),
      third_industry_tag_id = values(third_industry_tag_id),
      commerce_category_first_id = values(commerce_category_first_id),
      commerce_category_second_id = values(commerce_category_second_id),
      allow_signing_bonus_pay = values(allow_signing_bonus_pay),
      allow_fly_coin_pay = values(allow_fly_coin_pay),
      allow_flow_ticket_pay = values(allow_flow_ticket_pay),
      finance_type = values(finance_type),
      has_mgk_form = values(has_mgk_form),
      mgk_form_privacy_policy = values(mgk_form_privacy_policy),
      show_to_customer = values(show_to_customer),
      is_support_clue_pass = values(is_support_clue_pass),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      acc_account
      (username,mobile,password_strength,salt,salt_password,status,crm_customer_id,ctime,mtime,is_deleted,order_type,mid,account_type,active_time,name,icp_record_number,icp_info_image,brand_domain,user_type,ad_status,version,category_first_id,category_second_id,remark,is_agent,agent_type,business_role_id,company_name,area_id,dependency_agent_id,website_name,weibo,internal_linkman,linkman_address,bank,qualification_id,business_licence_code,business_licence_expire_date,is_business_licence_indefinite,legal_person_name,legal_person_idcard_expire_date,is_legal_person_idcard_indefinite,audit_remark,account_status,linkman_email,gd_status,agent_auth_expire_date,is_inner,department_id,is_support_seller,is_support_game,is_support_dpa,is_support_content,product_line,phone_number,idcard_type,idcard_number,idcard_expire_date,personal_address,is_idcard_indefinite,personal_name,group_id,product_line_id,product_id,is_support_pickup,is_support_mas,auto_update_label,is_support_fly,allow_cash_pay,allow_incentive_bonus_pay,customer_id,creator,payment_period,is_support_local_ad,first_industry_tag_id,second_industry_tag_id,third_industry_tag_id,commerce_category_first_id,commerce_category_second_id,allow_signing_bonus_pay,allow_fly_coin_pay,allow_flow_ticket_pay,finance_type,has_mgk_form,mgk_form_privacy_policy,show_to_customer,is_support_clue_pass)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.username,jdbcType=VARCHAR},
        #{item.mobile,jdbcType=VARCHAR},
        #{item.passwordStrength,jdbcType=TINYINT},
        #{item.salt,jdbcType=VARCHAR},
        #{item.saltPassword,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.crmCustomerId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.orderType,jdbcType=TINYINT},
        #{item.mid,jdbcType=BIGINT},
        #{item.accountType,jdbcType=TINYINT},
        #{item.activeTime,jdbcType=TIMESTAMP},
        #{item.name,jdbcType=VARCHAR},
        #{item.icpRecordNumber,jdbcType=VARCHAR},
        #{item.icpInfoImage,jdbcType=VARCHAR},
        #{item.brandDomain,jdbcType=VARCHAR},
        #{item.userType,jdbcType=TINYINT},
        #{item.adStatus,jdbcType=TINYINT},
        #{item.version,jdbcType=INTEGER},
        #{item.categoryFirstId,jdbcType=INTEGER},
        #{item.categorySecondId,jdbcType=INTEGER},
        #{item.remark,jdbcType=VARCHAR},
        #{item.isAgent,jdbcType=TINYINT},
        #{item.agentType,jdbcType=TINYINT},
        #{item.businessRoleId,jdbcType=INTEGER},
        #{item.companyName,jdbcType=VARCHAR},
        #{item.areaId,jdbcType=INTEGER},
        #{item.dependencyAgentId,jdbcType=INTEGER},
        #{item.websiteName,jdbcType=VARCHAR},
        #{item.weibo,jdbcType=VARCHAR},
        #{item.internalLinkman,jdbcType=VARCHAR},
        #{item.linkmanAddress,jdbcType=VARCHAR},
        #{item.bank,jdbcType=VARCHAR},
        #{item.qualificationId,jdbcType=INTEGER},
        #{item.businessLicenceCode,jdbcType=VARCHAR},
        #{item.businessLicenceExpireDate,jdbcType=DATE},
        #{item.isBusinessLicenceIndefinite,jdbcType=TINYINT},
        #{item.legalPersonName,jdbcType=VARCHAR},
        #{item.legalPersonIdcardExpireDate,jdbcType=DATE},
        #{item.isLegalPersonIdcardIndefinite,jdbcType=TINYINT},
        #{item.auditRemark,jdbcType=VARCHAR},
        #{item.accountStatus,jdbcType=TINYINT},
        #{item.linkmanEmail,jdbcType=VARCHAR},
        #{item.gdStatus,jdbcType=TINYINT},
        #{item.agentAuthExpireDate,jdbcType=DATE},
        #{item.isInner,jdbcType=TINYINT},
        #{item.departmentId,jdbcType=INTEGER},
        #{item.isSupportSeller,jdbcType=TINYINT},
        #{item.isSupportGame,jdbcType=TINYINT},
        #{item.isSupportDpa,jdbcType=TINYINT},
        #{item.isSupportContent,jdbcType=TINYINT},
        #{item.productLine,jdbcType=VARCHAR},
        #{item.phoneNumber,jdbcType=VARCHAR},
        #{item.idcardType,jdbcType=TINYINT},
        #{item.idcardNumber,jdbcType=VARCHAR},
        #{item.idcardExpireDate,jdbcType=TIMESTAMP},
        #{item.personalAddress,jdbcType=VARCHAR},
        #{item.isIdcardIndefinite,jdbcType=TINYINT},
        #{item.personalName,jdbcType=VARCHAR},
        #{item.groupId,jdbcType=INTEGER},
        #{item.productLineId,jdbcType=INTEGER},
        #{item.productId,jdbcType=INTEGER},
        #{item.isSupportPickup,jdbcType=TINYINT},
        #{item.isSupportMas,jdbcType=TINYINT},
        #{item.autoUpdateLabel,jdbcType=TINYINT},
        #{item.isSupportFly,jdbcType=TINYINT},
        #{item.allowCashPay,jdbcType=TINYINT},
        #{item.allowIncentiveBonusPay,jdbcType=TINYINT},
        #{item.customerId,jdbcType=INTEGER},
        #{item.creator,jdbcType=VARCHAR},
        #{item.paymentPeriod,jdbcType=INTEGER},
        #{item.isSupportLocalAd,jdbcType=TINYINT},
        #{item.firstIndustryTagId,jdbcType=INTEGER},
        #{item.secondIndustryTagId,jdbcType=INTEGER},
        #{item.thirdIndustryTagId,jdbcType=INTEGER},
        #{item.commerceCategoryFirstId,jdbcType=INTEGER},
        #{item.commerceCategorySecondId,jdbcType=INTEGER},
        #{item.allowSigningBonusPay,jdbcType=TINYINT},
        #{item.allowFlyCoinPay,jdbcType=TINYINT},
        #{item.allowFlowTicketPay,jdbcType=TINYINT},
        #{item.financeType,jdbcType=TINYINT},
        #{item.hasMgkForm,jdbcType=INTEGER},
        #{item.mgkFormPrivacyPolicy,jdbcType=INTEGER},
        #{item.showToCustomer,jdbcType=TINYINT},
        #{item.isSupportCluePass,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      acc_account
      (username,mobile,password_strength,salt,salt_password,status,crm_customer_id,ctime,mtime,is_deleted,order_type,mid,account_type,active_time,name,icp_record_number,icp_info_image,brand_domain,user_type,ad_status,version,category_first_id,category_second_id,remark,is_agent,agent_type,business_role_id,company_name,area_id,dependency_agent_id,website_name,weibo,internal_linkman,linkman_address,bank,qualification_id,business_licence_code,business_licence_expire_date,is_business_licence_indefinite,legal_person_name,legal_person_idcard_expire_date,is_legal_person_idcard_indefinite,audit_remark,account_status,linkman_email,gd_status,agent_auth_expire_date,is_inner,department_id,is_support_seller,is_support_game,is_support_dpa,is_support_content,product_line,phone_number,idcard_type,idcard_number,idcard_expire_date,personal_address,is_idcard_indefinite,personal_name,group_id,product_line_id,product_id,is_support_pickup,is_support_mas,auto_update_label,is_support_fly,allow_cash_pay,allow_incentive_bonus_pay,customer_id,creator,payment_period,is_support_local_ad,first_industry_tag_id,second_industry_tag_id,third_industry_tag_id,commerce_category_first_id,commerce_category_second_id,allow_signing_bonus_pay,allow_fly_coin_pay,allow_flow_ticket_pay,finance_type,has_mgk_form,mgk_form_privacy_policy,show_to_customer,is_support_clue_pass)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.username,jdbcType=VARCHAR},
        #{item.mobile,jdbcType=VARCHAR},
        #{item.passwordStrength,jdbcType=TINYINT},
        #{item.salt,jdbcType=VARCHAR},
        #{item.saltPassword,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.crmCustomerId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.orderType,jdbcType=TINYINT},
        #{item.mid,jdbcType=BIGINT},
        #{item.accountType,jdbcType=TINYINT},
        #{item.activeTime,jdbcType=TIMESTAMP},
        #{item.name,jdbcType=VARCHAR},
        #{item.icpRecordNumber,jdbcType=VARCHAR},
        #{item.icpInfoImage,jdbcType=VARCHAR},
        #{item.brandDomain,jdbcType=VARCHAR},
        #{item.userType,jdbcType=TINYINT},
        #{item.adStatus,jdbcType=TINYINT},
        #{item.version,jdbcType=INTEGER},
        #{item.categoryFirstId,jdbcType=INTEGER},
        #{item.categorySecondId,jdbcType=INTEGER},
        #{item.remark,jdbcType=VARCHAR},
        #{item.isAgent,jdbcType=TINYINT},
        #{item.agentType,jdbcType=TINYINT},
        #{item.businessRoleId,jdbcType=INTEGER},
        #{item.companyName,jdbcType=VARCHAR},
        #{item.areaId,jdbcType=INTEGER},
        #{item.dependencyAgentId,jdbcType=INTEGER},
        #{item.websiteName,jdbcType=VARCHAR},
        #{item.weibo,jdbcType=VARCHAR},
        #{item.internalLinkman,jdbcType=VARCHAR},
        #{item.linkmanAddress,jdbcType=VARCHAR},
        #{item.bank,jdbcType=VARCHAR},
        #{item.qualificationId,jdbcType=INTEGER},
        #{item.businessLicenceCode,jdbcType=VARCHAR},
        #{item.businessLicenceExpireDate,jdbcType=DATE},
        #{item.isBusinessLicenceIndefinite,jdbcType=TINYINT},
        #{item.legalPersonName,jdbcType=VARCHAR},
        #{item.legalPersonIdcardExpireDate,jdbcType=DATE},
        #{item.isLegalPersonIdcardIndefinite,jdbcType=TINYINT},
        #{item.auditRemark,jdbcType=VARCHAR},
        #{item.accountStatus,jdbcType=TINYINT},
        #{item.linkmanEmail,jdbcType=VARCHAR},
        #{item.gdStatus,jdbcType=TINYINT},
        #{item.agentAuthExpireDate,jdbcType=DATE},
        #{item.isInner,jdbcType=TINYINT},
        #{item.departmentId,jdbcType=INTEGER},
        #{item.isSupportSeller,jdbcType=TINYINT},
        #{item.isSupportGame,jdbcType=TINYINT},
        #{item.isSupportDpa,jdbcType=TINYINT},
        #{item.isSupportContent,jdbcType=TINYINT},
        #{item.productLine,jdbcType=VARCHAR},
        #{item.phoneNumber,jdbcType=VARCHAR},
        #{item.idcardType,jdbcType=TINYINT},
        #{item.idcardNumber,jdbcType=VARCHAR},
        #{item.idcardExpireDate,jdbcType=TIMESTAMP},
        #{item.personalAddress,jdbcType=VARCHAR},
        #{item.isIdcardIndefinite,jdbcType=TINYINT},
        #{item.personalName,jdbcType=VARCHAR},
        #{item.groupId,jdbcType=INTEGER},
        #{item.productLineId,jdbcType=INTEGER},
        #{item.productId,jdbcType=INTEGER},
        #{item.isSupportPickup,jdbcType=TINYINT},
        #{item.isSupportMas,jdbcType=TINYINT},
        #{item.autoUpdateLabel,jdbcType=TINYINT},
        #{item.isSupportFly,jdbcType=TINYINT},
        #{item.allowCashPay,jdbcType=TINYINT},
        #{item.allowIncentiveBonusPay,jdbcType=TINYINT},
        #{item.customerId,jdbcType=INTEGER},
        #{item.creator,jdbcType=VARCHAR},
        #{item.paymentPeriod,jdbcType=INTEGER},
        #{item.isSupportLocalAd,jdbcType=TINYINT},
        #{item.firstIndustryTagId,jdbcType=INTEGER},
        #{item.secondIndustryTagId,jdbcType=INTEGER},
        #{item.thirdIndustryTagId,jdbcType=INTEGER},
        #{item.commerceCategoryFirstId,jdbcType=INTEGER},
        #{item.commerceCategorySecondId,jdbcType=INTEGER},
        #{item.allowSigningBonusPay,jdbcType=TINYINT},
        #{item.allowFlyCoinPay,jdbcType=TINYINT},
        #{item.allowFlowTicketPay,jdbcType=TINYINT},
        #{item.financeType,jdbcType=TINYINT},
        #{item.hasMgkForm,jdbcType=INTEGER},
        #{item.mgkFormPrivacyPolicy,jdbcType=INTEGER},
        #{item.showToCustomer,jdbcType=TINYINT},
        #{item.isSupportCluePass,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      username = values(username),
      mobile = values(mobile),
      password_strength = values(password_strength),
      salt = values(salt),
      salt_password = values(salt_password),
      status = values(status),
      crm_customer_id = values(crm_customer_id),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      order_type = values(order_type),
      mid = values(mid),
      account_type = values(account_type),
      active_time = values(active_time),
      name = values(name),
      icp_record_number = values(icp_record_number),
      icp_info_image = values(icp_info_image),
      brand_domain = values(brand_domain),
      user_type = values(user_type),
      ad_status = values(ad_status),
      version = values(version),
      category_first_id = values(category_first_id),
      category_second_id = values(category_second_id),
      remark = values(remark),
      is_agent = values(is_agent),
      agent_type = values(agent_type),
      business_role_id = values(business_role_id),
      company_name = values(company_name),
      area_id = values(area_id),
      dependency_agent_id = values(dependency_agent_id),
      website_name = values(website_name),
      weibo = values(weibo),
      internal_linkman = values(internal_linkman),
      linkman_address = values(linkman_address),
      bank = values(bank),
      qualification_id = values(qualification_id),
      business_licence_code = values(business_licence_code),
      business_licence_expire_date = values(business_licence_expire_date),
      is_business_licence_indefinite = values(is_business_licence_indefinite),
      legal_person_name = values(legal_person_name),
      legal_person_idcard_expire_date = values(legal_person_idcard_expire_date),
      is_legal_person_idcard_indefinite = values(is_legal_person_idcard_indefinite),
      audit_remark = values(audit_remark),
      account_status = values(account_status),
      linkman_email = values(linkman_email),
      gd_status = values(gd_status),
      agent_auth_expire_date = values(agent_auth_expire_date),
      is_inner = values(is_inner),
      department_id = values(department_id),
      is_support_seller = values(is_support_seller),
      is_support_game = values(is_support_game),
      is_support_dpa = values(is_support_dpa),
      is_support_content = values(is_support_content),
      product_line = values(product_line),
      phone_number = values(phone_number),
      idcard_type = values(idcard_type),
      idcard_number = values(idcard_number),
      idcard_expire_date = values(idcard_expire_date),
      personal_address = values(personal_address),
      is_idcard_indefinite = values(is_idcard_indefinite),
      personal_name = values(personal_name),
      group_id = values(group_id),
      product_line_id = values(product_line_id),
      product_id = values(product_id),
      is_support_pickup = values(is_support_pickup),
      is_support_mas = values(is_support_mas),
      auto_update_label = values(auto_update_label),
      is_support_fly = values(is_support_fly),
      allow_cash_pay = values(allow_cash_pay),
      allow_incentive_bonus_pay = values(allow_incentive_bonus_pay),
      customer_id = values(customer_id),
      creator = values(creator),
      payment_period = values(payment_period),
      is_support_local_ad = values(is_support_local_ad),
      first_industry_tag_id = values(first_industry_tag_id),
      second_industry_tag_id = values(second_industry_tag_id),
      third_industry_tag_id = values(third_industry_tag_id),
      commerce_category_first_id = values(commerce_category_first_id),
      commerce_category_second_id = values(commerce_category_second_id),
      allow_signing_bonus_pay = values(allow_signing_bonus_pay),
      allow_fly_coin_pay = values(allow_fly_coin_pay),
      allow_flow_ticket_pay = values(allow_flow_ticket_pay),
      finance_type = values(finance_type),
      has_mgk_form = values(has_mgk_form),
      mgk_form_privacy_policy = values(mgk_form_privacy_policy),
      show_to_customer = values(show_to_customer),
      is_support_clue_pass = values(is_support_clue_pass),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountPo">
    <selectKey keyProperty="accountId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="passwordStrength != null">
        password_strength,
      </if>
      <if test="salt != null">
        salt,
      </if>
      <if test="saltPassword != null">
        salt_password,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="crmCustomerId != null">
        crm_customer_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="activeTime != null">
        active_time,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="icpRecordNumber != null">
        icp_record_number,
      </if>
      <if test="icpInfoImage != null">
        icp_info_image,
      </if>
      <if test="brandDomain != null">
        brand_domain,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="adStatus != null">
        ad_status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="categoryFirstId != null">
        category_first_id,
      </if>
      <if test="categorySecondId != null">
        category_second_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isAgent != null">
        is_agent,
      </if>
      <if test="agentType != null">
        agent_type,
      </if>
      <if test="businessRoleId != null">
        business_role_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="dependencyAgentId != null">
        dependency_agent_id,
      </if>
      <if test="websiteName != null">
        website_name,
      </if>
      <if test="weibo != null">
        weibo,
      </if>
      <if test="internalLinkman != null">
        internal_linkman,
      </if>
      <if test="linkmanAddress != null">
        linkman_address,
      </if>
      <if test="bank != null">
        bank,
      </if>
      <if test="qualificationId != null">
        qualification_id,
      </if>
      <if test="businessLicenceCode != null">
        business_licence_code,
      </if>
      <if test="businessLicenceExpireDate != null">
        business_licence_expire_date,
      </if>
      <if test="isBusinessLicenceIndefinite != null">
        is_business_licence_indefinite,
      </if>
      <if test="legalPersonName != null">
        legal_person_name,
      </if>
      <if test="legalPersonIdcardExpireDate != null">
        legal_person_idcard_expire_date,
      </if>
      <if test="isLegalPersonIdcardIndefinite != null">
        is_legal_person_idcard_indefinite,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="accountStatus != null">
        account_status,
      </if>
      <if test="linkmanEmail != null">
        linkman_email,
      </if>
      <if test="gdStatus != null">
        gd_status,
      </if>
      <if test="agentAuthExpireDate != null">
        agent_auth_expire_date,
      </if>
      <if test="isInner != null">
        is_inner,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="isSupportSeller != null">
        is_support_seller,
      </if>
      <if test="isSupportGame != null">
        is_support_game,
      </if>
      <if test="isSupportDpa != null">
        is_support_dpa,
      </if>
      <if test="isSupportContent != null">
        is_support_content,
      </if>
      <if test="productLine != null">
        product_line,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="idcardType != null">
        idcard_type,
      </if>
      <if test="idcardNumber != null">
        idcard_number,
      </if>
      <if test="idcardExpireDate != null">
        idcard_expire_date,
      </if>
      <if test="personalAddress != null">
        personal_address,
      </if>
      <if test="isIdcardIndefinite != null">
        is_idcard_indefinite,
      </if>
      <if test="personalName != null">
        personal_name,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="productLineId != null">
        product_line_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="isSupportPickup != null">
        is_support_pickup,
      </if>
      <if test="isSupportMas != null">
        is_support_mas,
      </if>
      <if test="autoUpdateLabel != null">
        auto_update_label,
      </if>
      <if test="isSupportFly != null">
        is_support_fly,
      </if>
      <if test="allowCashPay != null">
        allow_cash_pay,
      </if>
      <if test="allowIncentiveBonusPay != null">
        allow_incentive_bonus_pay,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="paymentPeriod != null">
        payment_period,
      </if>
      <if test="isSupportLocalAd != null">
        is_support_local_ad,
      </if>
      <if test="firstIndustryTagId != null">
        first_industry_tag_id,
      </if>
      <if test="secondIndustryTagId != null">
        second_industry_tag_id,
      </if>
      <if test="thirdIndustryTagId != null">
        third_industry_tag_id,
      </if>
      <if test="commerceCategoryFirstId != null">
        commerce_category_first_id,
      </if>
      <if test="commerceCategorySecondId != null">
        commerce_category_second_id,
      </if>
      <if test="allowSigningBonusPay != null">
        allow_signing_bonus_pay,
      </if>
      <if test="allowFlyCoinPay != null">
        allow_fly_coin_pay,
      </if>
      <if test="allowFlowTicketPay != null">
        allow_flow_ticket_pay,
      </if>
      <if test="financeType != null">
        finance_type,
      </if>
      <if test="hasMgkForm != null">
        has_mgk_form,
      </if>
      <if test="mgkFormPrivacyPolicy != null">
        mgk_form_privacy_policy,
      </if>
      <if test="showToCustomer != null">
        show_to_customer,
      </if>
      <if test="isSupportCluePass != null">
        is_support_clue_pass,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="passwordStrength != null">
        #{passwordStrength,jdbcType=TINYINT},
      </if>
      <if test="salt != null">
        #{salt,jdbcType=VARCHAR},
      </if>
      <if test="saltPassword != null">
        #{saltPassword,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="crmCustomerId != null">
        #{crmCustomerId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="activeTime != null">
        #{activeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="icpRecordNumber != null">
        #{icpRecordNumber,jdbcType=VARCHAR},
      </if>
      <if test="icpInfoImage != null">
        #{icpInfoImage,jdbcType=VARCHAR},
      </if>
      <if test="brandDomain != null">
        #{brandDomain,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=TINYINT},
      </if>
      <if test="adStatus != null">
        #{adStatus,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="categoryFirstId != null">
        #{categoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="categorySecondId != null">
        #{categorySecondId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isAgent != null">
        #{isAgent,jdbcType=TINYINT},
      </if>
      <if test="agentType != null">
        #{agentType,jdbcType=TINYINT},
      </if>
      <if test="businessRoleId != null">
        #{businessRoleId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="dependencyAgentId != null">
        #{dependencyAgentId,jdbcType=INTEGER},
      </if>
      <if test="websiteName != null">
        #{websiteName,jdbcType=VARCHAR},
      </if>
      <if test="weibo != null">
        #{weibo,jdbcType=VARCHAR},
      </if>
      <if test="internalLinkman != null">
        #{internalLinkman,jdbcType=VARCHAR},
      </if>
      <if test="linkmanAddress != null">
        #{linkmanAddress,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="qualificationId != null">
        #{qualificationId,jdbcType=INTEGER},
      </if>
      <if test="businessLicenceCode != null">
        #{businessLicenceCode,jdbcType=VARCHAR},
      </if>
      <if test="businessLicenceExpireDate != null">
        #{businessLicenceExpireDate,jdbcType=DATE},
      </if>
      <if test="isBusinessLicenceIndefinite != null">
        #{isBusinessLicenceIndefinite,jdbcType=TINYINT},
      </if>
      <if test="legalPersonName != null">
        #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonIdcardExpireDate != null">
        #{legalPersonIdcardExpireDate,jdbcType=DATE},
      </if>
      <if test="isLegalPersonIdcardIndefinite != null">
        #{isLegalPersonIdcardIndefinite,jdbcType=TINYINT},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="accountStatus != null">
        #{accountStatus,jdbcType=TINYINT},
      </if>
      <if test="linkmanEmail != null">
        #{linkmanEmail,jdbcType=VARCHAR},
      </if>
      <if test="gdStatus != null">
        #{gdStatus,jdbcType=TINYINT},
      </if>
      <if test="agentAuthExpireDate != null">
        #{agentAuthExpireDate,jdbcType=DATE},
      </if>
      <if test="isInner != null">
        #{isInner,jdbcType=TINYINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="isSupportSeller != null">
        #{isSupportSeller,jdbcType=TINYINT},
      </if>
      <if test="isSupportGame != null">
        #{isSupportGame,jdbcType=TINYINT},
      </if>
      <if test="isSupportDpa != null">
        #{isSupportDpa,jdbcType=TINYINT},
      </if>
      <if test="isSupportContent != null">
        #{isSupportContent,jdbcType=TINYINT},
      </if>
      <if test="productLine != null">
        #{productLine,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="idcardType != null">
        #{idcardType,jdbcType=TINYINT},
      </if>
      <if test="idcardNumber != null">
        #{idcardNumber,jdbcType=VARCHAR},
      </if>
      <if test="idcardExpireDate != null">
        #{idcardExpireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="personalAddress != null">
        #{personalAddress,jdbcType=VARCHAR},
      </if>
      <if test="isIdcardIndefinite != null">
        #{isIdcardIndefinite,jdbcType=TINYINT},
      </if>
      <if test="personalName != null">
        #{personalName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="productLineId != null">
        #{productLineId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="isSupportPickup != null">
        #{isSupportPickup,jdbcType=TINYINT},
      </if>
      <if test="isSupportMas != null">
        #{isSupportMas,jdbcType=TINYINT},
      </if>
      <if test="autoUpdateLabel != null">
        #{autoUpdateLabel,jdbcType=TINYINT},
      </if>
      <if test="isSupportFly != null">
        #{isSupportFly,jdbcType=TINYINT},
      </if>
      <if test="allowCashPay != null">
        #{allowCashPay,jdbcType=TINYINT},
      </if>
      <if test="allowIncentiveBonusPay != null">
        #{allowIncentiveBonusPay,jdbcType=TINYINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="paymentPeriod != null">
        #{paymentPeriod,jdbcType=INTEGER},
      </if>
      <if test="isSupportLocalAd != null">
        #{isSupportLocalAd,jdbcType=TINYINT},
      </if>
      <if test="firstIndustryTagId != null">
        #{firstIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="secondIndustryTagId != null">
        #{secondIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="thirdIndustryTagId != null">
        #{thirdIndustryTagId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategoryFirstId != null">
        #{commerceCategoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategorySecondId != null">
        #{commerceCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="allowSigningBonusPay != null">
        #{allowSigningBonusPay,jdbcType=TINYINT},
      </if>
      <if test="allowFlyCoinPay != null">
        #{allowFlyCoinPay,jdbcType=TINYINT},
      </if>
      <if test="allowFlowTicketPay != null">
        #{allowFlowTicketPay,jdbcType=TINYINT},
      </if>
      <if test="financeType != null">
        #{financeType,jdbcType=TINYINT},
      </if>
      <if test="hasMgkForm != null">
        #{hasMgkForm,jdbcType=INTEGER},
      </if>
      <if test="mgkFormPrivacyPolicy != null">
        #{mgkFormPrivacyPolicy,jdbcType=INTEGER},
      </if>
      <if test="showToCustomer != null">
        #{showToCustomer,jdbcType=TINYINT},
      </if>
      <if test="isSupportCluePass != null">
        #{isSupportCluePass,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="username != null">
        username = values(username),
      </if>
      <if test="mobile != null">
        mobile = values(mobile),
      </if>
      <if test="passwordStrength != null">
        password_strength = values(password_strength),
      </if>
      <if test="salt != null">
        salt = values(salt),
      </if>
      <if test="saltPassword != null">
        salt_password = values(salt_password),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="crmCustomerId != null">
        crm_customer_id = values(crm_customer_id),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="orderType != null">
        order_type = values(order_type),
      </if>
      <if test="mid != null">
        mid = values(mid),
      </if>
      <if test="accountType != null">
        account_type = values(account_type),
      </if>
      <if test="activeTime != null">
        active_time = values(active_time),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="icpRecordNumber != null">
        icp_record_number = values(icp_record_number),
      </if>
      <if test="icpInfoImage != null">
        icp_info_image = values(icp_info_image),
      </if>
      <if test="brandDomain != null">
        brand_domain = values(brand_domain),
      </if>
      <if test="userType != null">
        user_type = values(user_type),
      </if>
      <if test="adStatus != null">
        ad_status = values(ad_status),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="categoryFirstId != null">
        category_first_id = values(category_first_id),
      </if>
      <if test="categorySecondId != null">
        category_second_id = values(category_second_id),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="isAgent != null">
        is_agent = values(is_agent),
      </if>
      <if test="agentType != null">
        agent_type = values(agent_type),
      </if>
      <if test="businessRoleId != null">
        business_role_id = values(business_role_id),
      </if>
      <if test="companyName != null">
        company_name = values(company_name),
      </if>
      <if test="areaId != null">
        area_id = values(area_id),
      </if>
      <if test="dependencyAgentId != null">
        dependency_agent_id = values(dependency_agent_id),
      </if>
      <if test="websiteName != null">
        website_name = values(website_name),
      </if>
      <if test="weibo != null">
        weibo = values(weibo),
      </if>
      <if test="internalLinkman != null">
        internal_linkman = values(internal_linkman),
      </if>
      <if test="linkmanAddress != null">
        linkman_address = values(linkman_address),
      </if>
      <if test="bank != null">
        bank = values(bank),
      </if>
      <if test="qualificationId != null">
        qualification_id = values(qualification_id),
      </if>
      <if test="businessLicenceCode != null">
        business_licence_code = values(business_licence_code),
      </if>
      <if test="businessLicenceExpireDate != null">
        business_licence_expire_date = values(business_licence_expire_date),
      </if>
      <if test="isBusinessLicenceIndefinite != null">
        is_business_licence_indefinite = values(is_business_licence_indefinite),
      </if>
      <if test="legalPersonName != null">
        legal_person_name = values(legal_person_name),
      </if>
      <if test="legalPersonIdcardExpireDate != null">
        legal_person_idcard_expire_date = values(legal_person_idcard_expire_date),
      </if>
      <if test="isLegalPersonIdcardIndefinite != null">
        is_legal_person_idcard_indefinite = values(is_legal_person_idcard_indefinite),
      </if>
      <if test="auditRemark != null">
        audit_remark = values(audit_remark),
      </if>
      <if test="accountStatus != null">
        account_status = values(account_status),
      </if>
      <if test="linkmanEmail != null">
        linkman_email = values(linkman_email),
      </if>
      <if test="gdStatus != null">
        gd_status = values(gd_status),
      </if>
      <if test="agentAuthExpireDate != null">
        agent_auth_expire_date = values(agent_auth_expire_date),
      </if>
      <if test="isInner != null">
        is_inner = values(is_inner),
      </if>
      <if test="departmentId != null">
        department_id = values(department_id),
      </if>
      <if test="isSupportSeller != null">
        is_support_seller = values(is_support_seller),
      </if>
      <if test="isSupportGame != null">
        is_support_game = values(is_support_game),
      </if>
      <if test="isSupportDpa != null">
        is_support_dpa = values(is_support_dpa),
      </if>
      <if test="isSupportContent != null">
        is_support_content = values(is_support_content),
      </if>
      <if test="productLine != null">
        product_line = values(product_line),
      </if>
      <if test="phoneNumber != null">
        phone_number = values(phone_number),
      </if>
      <if test="idcardType != null">
        idcard_type = values(idcard_type),
      </if>
      <if test="idcardNumber != null">
        idcard_number = values(idcard_number),
      </if>
      <if test="idcardExpireDate != null">
        idcard_expire_date = values(idcard_expire_date),
      </if>
      <if test="personalAddress != null">
        personal_address = values(personal_address),
      </if>
      <if test="isIdcardIndefinite != null">
        is_idcard_indefinite = values(is_idcard_indefinite),
      </if>
      <if test="personalName != null">
        personal_name = values(personal_name),
      </if>
      <if test="groupId != null">
        group_id = values(group_id),
      </if>
      <if test="productLineId != null">
        product_line_id = values(product_line_id),
      </if>
      <if test="productId != null">
        product_id = values(product_id),
      </if>
      <if test="isSupportPickup != null">
        is_support_pickup = values(is_support_pickup),
      </if>
      <if test="isSupportMas != null">
        is_support_mas = values(is_support_mas),
      </if>
      <if test="autoUpdateLabel != null">
        auto_update_label = values(auto_update_label),
      </if>
      <if test="isSupportFly != null">
        is_support_fly = values(is_support_fly),
      </if>
      <if test="allowCashPay != null">
        allow_cash_pay = values(allow_cash_pay),
      </if>
      <if test="allowIncentiveBonusPay != null">
        allow_incentive_bonus_pay = values(allow_incentive_bonus_pay),
      </if>
      <if test="customerId != null">
        customer_id = values(customer_id),
      </if>
      <if test="creator != null">
        creator = values(creator),
      </if>
      <if test="paymentPeriod != null">
        payment_period = values(payment_period),
      </if>
      <if test="isSupportLocalAd != null">
        is_support_local_ad = values(is_support_local_ad),
      </if>
      <if test="firstIndustryTagId != null">
        first_industry_tag_id = values(first_industry_tag_id),
      </if>
      <if test="secondIndustryTagId != null">
        second_industry_tag_id = values(second_industry_tag_id),
      </if>
      <if test="thirdIndustryTagId != null">
        third_industry_tag_id = values(third_industry_tag_id),
      </if>
      <if test="commerceCategoryFirstId != null">
        commerce_category_first_id = values(commerce_category_first_id),
      </if>
      <if test="commerceCategorySecondId != null">
        commerce_category_second_id = values(commerce_category_second_id),
      </if>
      <if test="allowSigningBonusPay != null">
        allow_signing_bonus_pay = values(allow_signing_bonus_pay),
      </if>
      <if test="allowFlyCoinPay != null">
        allow_fly_coin_pay = values(allow_fly_coin_pay),
      </if>
      <if test="allowFlowTicketPay != null">
        allow_flow_ticket_pay = values(allow_flow_ticket_pay),
      </if>
      <if test="financeType != null">
        finance_type = values(finance_type),
      </if>
      <if test="hasMgkForm != null">
        has_mgk_form = values(has_mgk_form),
      </if>
      <if test="mgkFormPrivacyPolicy != null">
        mgk_form_privacy_policy = values(mgk_form_privacy_policy),
      </if>
      <if test="showToCustomer != null">
        show_to_customer = values(show_to_customer),
      </if>
      <if test="isSupportCluePass != null">
        is_support_clue_pass = values(is_support_clue_pass),
      </if>
    </trim>
  </insert>
</mapper>