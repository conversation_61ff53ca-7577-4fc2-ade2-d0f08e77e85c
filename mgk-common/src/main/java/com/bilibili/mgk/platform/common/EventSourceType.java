package com.bilibili.mgk.platform.common;

import com.bilibili.adp.common.util.Utils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/11/11
 **/
@AllArgsConstructor
public enum EventSourceType {
    AD(0, "广告流量"),
    PREVIEW_BY_AD(1, "投放端预览"),
    OUTER(2, "自然流量"),
    PREVIEW_BY_MGK(3, "建站预览"),
    VIEW_BY_MGK(4, "建站查看预览"),
    UNKNOWN(5, "未知来源");
    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static EventSourceType getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("未知发送状态");
        }
        for (EventSourceType bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code EventSourceType " + code);
    }

    public static Integer recognitionEventSource(Integer eventSource, String trackId, Integer salesType) {
         if(!StringUtils.isEmpty(trackId) && Utils.isPositive(salesType)){
             return EventSourceType.AD.code;
         }
        if(!StringUtils.isEmpty(trackId) && salesType!=null && salesType == 0){
            return EventSourceType.PREVIEW_BY_AD.code;
        }
        if(Utils.isPositive(eventSource)){
            return eventSource;
        }
        return EventSourceType.UNKNOWN.code;

    }

    public static String mapToEventSourceClue(EventSourceType type) {
        switch (type) {
            case AD:
                return "广告流量";
            case OUTER:
                return "自然流量";
            case PREVIEW_BY_AD:
            case VIEW_BY_MGK:
            case PREVIEW_BY_MGK:
                return "预览";
            case UNKNOWN:
                return "其他";
            default:
                return "未知";
        }
    }

    public static String mapToEventSourceClue(Integer code) {
        EventSourceType type = getByCodeWithDefault(code);
        return mapToEventSourceClue(type);
    }

    /**
     * default UNKNOWN
     *
     * @param code
     * @return
     */
    public static EventSourceType getByCodeWithDefault(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("未知发送状态");
        }
        for (EventSourceType bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return UNKNOWN;
    }
}
