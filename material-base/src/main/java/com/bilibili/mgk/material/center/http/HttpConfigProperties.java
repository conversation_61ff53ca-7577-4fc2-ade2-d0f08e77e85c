package com.bilibili.mgk.material.center.http;

import java.util.List;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/19
 */
@Data
@Configuration
public class HttpConfigProperties {

    //    @Value("${material.aigc.algo.img2img.base-url:discovery://sycpb.sycpb-model.aigc-img2img}")
    @Value("${material.aigc.algo.img2img.base-url:http://172.28.133.10:8000}")
    private String img2imgBaseUrl;

    @Value("#{'${material.aigc.algo.img2img.zones:sh004}'.split(',')}")
    private List<String> img2imgZones;


    //    @Value("${material.aigc.algo.img2txt.base-url:discovery://sycpb.sycpb-model.aigc-img2img}")
    @Value("${material.aigc.algo.img2txt.base-url:http://172.28.134.186:8000}")
    private String img2txtBaseUrl;

    @Value("#{'${material.aigc.algo.img2txt.zones:sh004}'.split(',')}")
    private List<String> img2txtZones;

    //     @Value("${material.aigc.algo.smart-crop.base-url:discovery://sycpb.sycpb-model.smart-crop}")
    @Value("${material.aigc.algo.smart-crop.base-url:http://*************:8000}")
    private String smartCropBaseUrl;


    @Value("#{'${material.aigc.algo.smart-crop.zones:sh004}'.split(',')}")
    private List<String> smartCropZones;


    // 注意这个参数生产是false 本地是true，这个最好修改下uat的默认参数
    @Value("${material.aigc.algo.img2img.proxy.enabled:true}")
    private Boolean sock5ProxyEnabled;


    @Value("${material.http.discovery.zone:sh001}")
    private String zone;


    @Value("${material.aigc.algo.img2img.proxy.host:************}")
    private String sock5ProxyHost;

    @Value("${material.aigc.algo.img2img.proxy.port:9090}")
    private Integer sock5ProxyPort;


}
