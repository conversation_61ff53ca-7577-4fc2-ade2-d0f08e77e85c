package com.bilibili.mgk.platform.portal.webapi.hot_ads;

import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.mgk.platform.api.hot_ads.service.IHotAdsIndustryService;
import com.bilibili.mgk.platform.api.hot_ads.service.IHotAdsService;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoBlackService;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoCollectService;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.service.WebHotAdsService;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import com.bilibili.mgk.platform.portal.webapi.hot_ads.vo.HotAdsNewCollectVo;
import com.google.common.collect.Lists;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * @file: HotAdsControllerTest
 * @author: gaoming
 * @date: 2021/01/15
 * @version: 1.0
 * @description:
 **/
public class HotAdsControllerTest extends BaseMockitoTest {

    @InjectMocks
    private HotAdsController hotAdsController;

    @Mock
    private IHotAdsService hotAdsService;

    @Mock
    private IHotAdsIndustryService industryService;

    @Mock
    private WebHotAdsService webHotAdsService;

    @Mock
    private IHotVideoBlackService hotVideoBlackService;

    @Mock
    private IHotVideoCollectService hotVideoCollectService;

    private Context context;

    @Before
    public void setUp() throws Exception {
        context = new Context();
        context.setAccountId(10005);
        context.setType(OperatorType.SYSTEM.getCode());
        context.setProxyName("SYS_TEST");
        context.setSalesType(SalesType.CPM.getCode());
        context.setUsername("bilibili_user");
        context.setProxyId(1);
    }

    @Test
    public void testGetList() {
        hotAdsController.getList(context, "", "", "", 1,
                Lists.newArrayList(1), Lists.newArrayList(1), 1,1, 1, 1,
                1,1, 15);
    }

    @Test
    public void testGetHotVideoById() {
        hotAdsController.getHotVideoById(context, "1", 1);
    }

    @Test
    public void testGetIndustry() {
        hotAdsController.getIndustry(context);
    }

    @Test
    public void testDoCollect() {
        hotAdsController.doCollect(context, "1", HotAdsNewCollectVo.builder()
                .title("1")
                .build());
    }

    @Test
    public void testCancelCollect() {
        hotAdsController.cancelCollect(context, "1");
    }

    @Test
    public void testGetCollectList() {
        hotAdsController.getCollectList(context, "1", 1, 15);
    }

    @Test
    public void testGetListWithBlack() {
        hotAdsController.getListWithBlack(context,
                "1",
                "1",
                "1",
                1,
                Lists.newArrayList(1),
                1,
                1,
                1,
                1, 15);
    }

    @Test
    public void testEnable() {
        hotAdsController.enable(context, "1", 1);
    }

    @Test
    public void testDisable() {
        hotAdsController.disable(context, "1", 1);
    }
}