package com.bilibili.mgk.platform.biz.service.session;

import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.session.IMgkSessionService;
import com.bilibili.mgk.platform.api.session.dto.MgkLoginInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName MgkSessionServiceImpl
 * <AUTHOR>
 * @Date 2022/6/20 4:30 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkSessionServiceImpl implements IMgkSessionService {

    @Autowired
    private MgkSessionServiceDelegate mgkSessionServiceDelegate;

    @Override
    public MgkLoginInfoDto ssoLogin(String cookieValue, Integer businessSource) throws ServiceException {
        return mgkSessionServiceDelegate.ssoLogin(cookieValue, businessSource);
    }

    @Override
    public MgkLoginInfoDto biliUserLogin(Integer accountId, String biliUsername, boolean isAdmin, SystemType systemType) throws ServiceException {
        return mgkSessionServiceDelegate.biliUserLogin(accountId, biliUsername, isAdmin, systemType);
    }

    @Override
    public MgkLoginInfoDto loginBid(String cookieValue, Integer accountId) throws ServiceException {
        return mgkSessionServiceDelegate.loginBid(cookieValue, accountId);
    }

    @Override
    public MgkLoginInfoDto loginAgentBid(String cookieValue, Long userId, Integer accountId) throws Exception {
        return mgkSessionServiceDelegate.loginAgentBid(cookieValue, userId, accountId);
    }
}
