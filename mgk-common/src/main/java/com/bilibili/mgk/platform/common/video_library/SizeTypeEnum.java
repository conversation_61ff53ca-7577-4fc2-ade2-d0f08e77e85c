package com.bilibili.mgk.platform.common.video_library;

import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/3/12
 *
 **/
@NoArgsConstructor
public enum SizeTypeEnum {

    OTHER(0, "其他", 0, 0) {
        @Override
        public boolean isMatch(Integer width, Integer height) {
            return false;
        }
    },
    S_16_9(1, "16:9", 1.77, 1.78),
    S_9_16(2, "9:16", 0.56, 0.57),
    S_3_4(3, "3:4", 0.75, 0.76),
    S_4_3(4, "4:3", 1.33, 1.34) ;

    @Getter
    private Integer code;

    @Getter
    private String desc;

    @Getter
    private double rangeFrom;

    @Getter
    private double rangeTo;

    SizeTypeEnum(Integer code, String desc, double rangeFrom, double rangeTo) {

        this.code = code;
        this.desc = desc;
        this.rangeFrom = rangeFrom;
        this.rangeTo = rangeTo;
    }

    public static SizeTypeEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("宽高比类型不可为空");
        }

        for(SizeTypeEnum lrt: values()) {
            if(lrt.getCode().equals(code)) {
                return lrt;
            }
        }

        throw new IllegalArgumentException("未知的宽高比类型");
    }

    public boolean isMatch(Integer width, Integer height) {

        if (width != null && width > 0 && height != null && height > 0) {
            BigDecimal bWidth = new BigDecimal(width);
            BigDecimal bHeight = new BigDecimal(height);
            BigDecimal result = bWidth.divide(bHeight, 2, BigDecimal.ROUND_HALF_UP);

            int from = result.compareTo(BigDecimal.valueOf(this.rangeFrom));
            int to = result.compareTo(BigDecimal.valueOf(this.rangeTo));
            return from >= 0 && to <= 0;
        }
        return false;
    }

    public static SizeTypeEnum getSizeTypeEnum (Integer width, Integer height) {

        for(SizeTypeEnum e : values()) {
            if (e.isMatch(width, height)) {
                return e;
            }
        }
        return SizeTypeEnum.OTHER;
    }
}
