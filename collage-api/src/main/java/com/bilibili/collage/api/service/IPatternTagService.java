package com.bilibili.collage.api.service;

import com.bilibili.collage.api.dto.PatternTagDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/1
 * 模版标签的service
 **/
public interface IPatternTagService {

    List<PatternTagDto> queryPatternTag(String codeLike);

    PatternTagDto getPatternTagById (Integer id);

    List<PatternTagDto> getPatternTagByIds (List<Integer> ids);

    PatternTagDto getPatternTagByCode (String code);

    Integer createPatternTag (String code);

    void deletePatternTag (String code);
}
