//package com.bilibili.mgk.platform.biz.es;
//
//import io.vavr.NotImplementedError;
//import lombok.Getter;
//import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.search.aggregations.Aggregation;
//import org.elasticsearch.search.aggregations.AggregationBuilder;
//import org.elasticsearch.search.aggregations.AggregationBuilders;
//import org.elasticsearch.search.aggregations.Aggregations;
//import org.elasticsearch.search.aggregations.bucket.terms.Terms;
//import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
//import org.elasticsearch.search.aggregations.metrics.NumericMetricsAggregation;
//import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
//import org.elasticsearch.search.aggregations.metrics.valuecount.ValueCountAggregationBuilder;
//import org.springframework.util.Assert;
//import org.springframework.util.CollectionUtils;
//
//import java.text.MessageFormat;
//import java.util.*;
//import java.util.function.Supplier;
//import java.util.function.UnaryOperator;
//
//public class BucketAggInterpreter<T> {
//    @Getter
//    private AggregationBuilder rootAggBuilder;
//    private AggregationBuilder leafAggBuilder;
//
//    private final List<AggField<T,String>> terms = new ArrayList<>();
//    private final List<AggField<T,Double>> metrics = new ArrayList<>();
//
//    public List<T> extract(SearchResponse searchResponse) {
//        throw new NotImplementedError();
//    }
//
//    protected void registerTerm(AggField<T,String> term) {
//        this.terms.add(term);
//        final TermsAggregationBuilder termsBuilder = AggregationBuilders.terms(term.getAlias())
//                .field(term.getField()).size(Integer.MAX_VALUE);
//        if (Objects.isNull(rootAggBuilder)) {
//            rootAggBuilder = termsBuilder;
//            leafAggBuilder = rootAggBuilder;
//            return;
//        }
//        leafAggBuilder.subAggregation(termsBuilder);
//        leafAggBuilder = termsBuilder;
//    }
//
//    protected void registerSum(AggField<T,Double> metric) {
//        final SumAggregationBuilder sumBuilder = AggregationBuilders.sum(metric.getAlias()).field(metric.getField());
//        Assert.notNull(rootAggBuilder, "分桶信息不存在");
//
//        metrics.add(metric);
//        leafAggBuilder.subAggregation(sumBuilder);
//    }
//
//    protected void registerCount(AggField<T,Double> metric) {
//        final ValueCountAggregationBuilder valueCountAggregationBuilder = AggregationBuilders.count(metric.getAlias()).field(metric.getField());
//        Assert.notNull(rootAggBuilder, "分桶信息不存在");
//
//        metrics.add(metric);
//        leafAggBuilder.subAggregation(valueCountAggregationBuilder);
//    }
//
//    protected List<T> extract(SearchResponse searchResponse, Class<T> clazz, Supplier<T> supplier) {
//        Assert.notNull(supplier, "supplier不能为空");
//
//        return extract(searchResponse, clazz, supplier, null);
//    }
//
//    protected List<T> extract(SearchResponse searchResponse, Class<T> clazz, UnaryOperator<T> copyFunc) {
//        Assert.notNull(copyFunc, "copyFunc不能为空");
//
//        return extract(searchResponse, clazz, null, copyFunc);
//    }
//
//    private List<T> extract(SearchResponse searchResponse, Class<T> clazz, Supplier<T> supplier, UnaryOperator<T> copyFunc) {
//        final List<T> hits = RestEsService.extractFromHits(searchResponse, clazz);
//        if (CollectionUtils.isEmpty(hits)) return Collections.emptyList();
//
//        if (Objects.isNull(supplier)) {
//            final T sample = hits.get(0);
//            supplier = () -> copyFunc.apply(sample);
//        }
//        final List<T> list = new ArrayList<>();
//        extractIterative(list, new HashMap<>(), supplier, searchResponse.getAggregations(), terms);
//        return list;
//    }
//
//    private void extractIterative(List<T> list, Map<String, String> termsMap, Supplier<T> supplier,
//                                  Aggregations aggregations, List<AggField<T, String>> terms) {
//        final AggField<T, String> aggField = terms.get(0);
//        for (Terms.Bucket bucket : getTermBuckets(aggregations, aggField.getAlias())) {
//            final String value = bucket.getKeyAsString();
//            termsMap.put(aggField.getAlias(), value);
//            if (terms.size() > 1) {
//                extractIterative(list, termsMap, supplier, bucket.getAggregations(), terms.subList(1, terms.size()));
//            } else {
//                final T t = generate(supplier, termsMap, bucket.getAggregations());
//                list.add(t);
//            }
//        }
//    }
//
//    private T generate(Supplier<T> supplier, Map<String, String> termsMap, Aggregations aggregations) {
//        final T t = supplier.get();
//        for (AggField<T, String> term : this.terms) {
//            final String value = termsMap.get(term.getAlias());
//            term.getBiConsumer().accept(t, value);
//        }
//        for (AggField<T, Double> metric : this.metrics) {
//            final double value = getMetric(aggregations, metric.getAlias());
//            metric.getBiConsumer().accept(t, value);
//        }
//        return t;
//    }
//
//    private double getMetric(Aggregations aggregations, String key) {
//        final Aggregation aggregation = aggregations.get(key);
//        if (aggregation instanceof NumericMetricsAggregation.SingleValue) {
//            final NumericMetricsAggregation.SingleValue singleValue = (NumericMetricsAggregation.SingleValue) aggregation;
//            return singleValue.value();
//        }
//
//        throw new IllegalArgumentException(MessageFormat.format("类型转换失败: key={0}不是metrics", key));
//    }
//
//    private List<? extends Terms.Bucket> getTermBuckets(Aggregations aggregations, String key) {
//        final Aggregation aggregation = aggregations.get(key);
//        if (aggregation instanceof Terms) {
//            final Terms terms = (Terms) aggregation;
//            return terms.getBuckets();
//        }
//
//        throw new IllegalArgumentException(MessageFormat.format("类型转换失败: key={0}不是terms", key));
//    }
//}
