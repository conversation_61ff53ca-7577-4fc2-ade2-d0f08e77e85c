package com.bilibili.mgk.material.center.facade;

import com.bapis.datacenter.service.oneservice.OperatorVo;
import com.bapis.datacenter.service.oneservice.OsHeader;
import com.bapis.datacenter.service.oneservice.PageReq;
import com.bapis.datacenter.service.oneservice.PageVo;
import com.bapis.datacenter.service.oneservice.QueryReq;
import com.bapis.datacenter.service.oneservice.QueryReq.Builder;
import com.bapis.datacenter.service.oneservice.QueryResp;
import com.bilibili.mgk.material.center.facade.converter.CreativeCoverWithCostOnesDTO;
import com.bilibili.mgk.material.center.facade.proxy.OneServiceFlowControlProxy;
import com.bilibili.mgk.material.center.service.aigc.model.CreativeHighCostCover;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.google.common.collect.Lists;
import io.vavr.Tuple2;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/25
 */
@Service
public class CreativeCoverOrderByCostFacade {

    @Resource
    private OneServiceFlowControlProxy serviceOpenApiManagerBlockingStub;

    @Resource
    private ApiOneServiceBase apiOneServiceBase;

    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Value("${material.oneservice.creative-cover-for-img2img.app-key:496b1dd505103413f42117ff1d5840ca}")
    private String appKey;

    @Value("${material.oneservice.creative-cover-for-img2img.secret:tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=}")
    private String secret;

    @Value("${material.oneservice.creative-cover-for-img2img.api-id:api_2327}")
    private String apiId;


    public Pagination<List<CreativeHighCostCover>> pageCreativeHighCostCover(Long accountId, Integer pn,
            Integer ps) {

        Tuple2<String, LocalDate> logdate = apiOneServiceBase.findLatestOpenApiLogDateAndDate(apiId);

        Builder openApiReq = baseReq();

        this.whereAccountIdEq(openApiReq, accountId);

        this.whereLogDateEq(openApiReq, logdate._1);

        this.orderByCostDesc(openApiReq);

        this.limitOffset(openApiReq, pn, ps);

        QueryResp rsp = serviceOpenApiManagerBlockingStub.query(openApiReq.build());

        PageVo page = rsp.getPageVo();
        List<CreativeHighCostCover> result = rsp.getRowsList().stream()
                .map(map -> CreativeCoverWithCostOnesDTO
                        .fromMapValue(map.getValueMap()).toBO()
                )
                .collect(Collectors.toList());

        return new Pagination<>(page.getPage(), (int) page.getTotalSize(), result);


    }

    private void limitOffset(Builder openApiReq, Integer pn, Integer ps) {
        openApiReq.setPageReq(PageReq.newBuilder()
                .setPage(pn)
                .setPageSize(ps)
                .build());
    }

    private void orderByCostDesc(Builder openApiReq) {
        openApiReq.addAllOrders(Lists.newArrayList("cost desc"));

    }

    private void whereAccountIdEq(Builder openApiReq, Long accountId) {
        this.appendAndParams(openApiReq, "account_id", "=", String.valueOf(accountId));
    }

    private void whereLogDateEq(Builder openApiReq, String logdate) {

        this.appendAndParams(openApiReq, "log_date", "=", logdate);
    }


    /**
     * @return
     */
    private Builder baseReq() {

        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey(appKey)
                .setSecret(secret)
                .setApiId(apiId)
                .build();

        Builder openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader);

        return openApiReq;

    }


    protected void appendAndParams(Builder openApiReq, String key, String op, String... values) {
        openApiReq.addReqs(OperatorVo.newBuilder()
                .setField(key)
                .setOperator(op)
                .addAllValues(Lists.newArrayList(values)));
    }

    protected void appendAndParams(Builder openApiReq, String key, String op, List<String> values) {
        openApiReq.addReqs(OperatorVo.newBuilder()
                .setField(key)
                .setOperator(op)
                .addAllValues(values));
    }

}
