package com.bilibili.mgk.platform.biz.ad.dao;

import com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPo;
import com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardSubInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauLiveGameCardSubInfoDao {
    long countByExample(LauLiveGameCardSubInfoPoExample example);

    int deleteByExample(LauLiveGameCardSubInfoPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauLiveGameCardSubInfoPo record);

    int insertBatch(List<LauLiveGameCardSubInfoPo> records);

    int insertUpdateBatch(List<LauLiveGameCardSubInfoPo> records);

    int insert(LauLiveGameCardSubInfoPo record);

    int insertUpdateSelective(LauLiveGameCardSubInfoPo record);

    int insertSelective(LauLiveGameCardSubInfoPo record);

    List<LauLiveGameCardSubInfoPo> selectByExample(LauLiveGameCardSubInfoPoExample example);

    LauLiveGameCardSubInfoPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauLiveGameCardSubInfoPo record, @Param("example") LauLiveGameCardSubInfoPoExample example);

    int updateByExample(@Param("record") LauLiveGameCardSubInfoPo record, @Param("example") LauLiveGameCardSubInfoPoExample example);

    int updateByPrimaryKeySelective(LauLiveGameCardSubInfoPo record);

    int updateByPrimaryKey(LauLiveGameCardSubInfoPo record);
}