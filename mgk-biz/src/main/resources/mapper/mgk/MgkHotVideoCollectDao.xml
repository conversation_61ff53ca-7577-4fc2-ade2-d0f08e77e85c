<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkHotVideoCollectDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="collect_id" jdbcType="BIGINT" property="collectId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="bvid" jdbcType="VARCHAR" property="bvid" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="collect_type" jdbcType="TINYINT" property="collectType" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ad_type_creative_id" jdbcType="VARCHAR" property="adTypeCreativeId" />
    <result column="log_date" jdbcType="VARCHAR" property="logDate" />
    <result column="day_type" jdbcType="TINYINT" property="dayType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, collect_id, account_id, bvid, title, collect_type, is_deleted, ctime, mtime, 
    ad_type_creative_id, log_date, day_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_hot_video_collect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_hot_video_collect
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_hot_video_collect
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPoExample">
    delete from mgk_hot_video_collect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_hot_video_collect (collect_id, account_id, bvid, 
      title, collect_type, is_deleted, 
      ctime, mtime, ad_type_creative_id, 
      log_date, day_type)
    values (#{collectId,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{bvid,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{collectType,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{adTypeCreativeId,jdbcType=VARCHAR}, 
      #{logDate,jdbcType=VARCHAR}, #{dayType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_hot_video_collect
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="collectId != null">
        collect_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="bvid != null">
        bvid,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="collectType != null">
        collect_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="adTypeCreativeId != null">
        ad_type_creative_id,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="dayType != null">
        day_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="collectId != null">
        #{collectId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bvid != null">
        #{bvid,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="collectType != null">
        #{collectType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="adTypeCreativeId != null">
        #{adTypeCreativeId,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=VARCHAR},
      </if>
      <if test="dayType != null">
        #{dayType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPoExample" resultType="java.lang.Long">
    select count(*) from mgk_hot_video_collect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_hot_video_collect
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.collectId != null">
        collect_id = #{record.collectId,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.bvid != null">
        bvid = #{record.bvid,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.collectType != null">
        collect_type = #{record.collectType,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.adTypeCreativeId != null">
        ad_type_creative_id = #{record.adTypeCreativeId,jdbcType=VARCHAR},
      </if>
      <if test="record.logDate != null">
        log_date = #{record.logDate,jdbcType=VARCHAR},
      </if>
      <if test="record.dayType != null">
        day_type = #{record.dayType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_hot_video_collect
    set id = #{record.id,jdbcType=INTEGER},
      collect_id = #{record.collectId,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      bvid = #{record.bvid,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      collect_type = #{record.collectType,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      ad_type_creative_id = #{record.adTypeCreativeId,jdbcType=VARCHAR},
      log_date = #{record.logDate,jdbcType=VARCHAR},
      day_type = #{record.dayType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo">
    update mgk_hot_video_collect
    <set>
      <if test="collectId != null">
        collect_id = #{collectId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bvid != null">
        bvid = #{bvid,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="collectType != null">
        collect_type = #{collectType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="adTypeCreativeId != null">
        ad_type_creative_id = #{adTypeCreativeId,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        log_date = #{logDate,jdbcType=VARCHAR},
      </if>
      <if test="dayType != null">
        day_type = #{dayType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo">
    update mgk_hot_video_collect
    set collect_id = #{collectId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=INTEGER},
      bvid = #{bvid,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      collect_type = #{collectType,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      ad_type_creative_id = #{adTypeCreativeId,jdbcType=VARCHAR},
      log_date = #{logDate,jdbcType=VARCHAR},
      day_type = #{dayType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_hot_video_collect (collect_id, account_id, bvid, 
      title, collect_type, is_deleted, 
      ctime, mtime, ad_type_creative_id, 
      log_date, day_type)
    values (#{collectId,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{bvid,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{collectType,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{adTypeCreativeId,jdbcType=VARCHAR}, 
      #{logDate,jdbcType=VARCHAR}, #{dayType,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      collect_id = values(collect_id),
      account_id = values(account_id),
      bvid = values(bvid),
      title = values(title),
      collect_type = values(collect_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      ad_type_creative_id = values(ad_type_creative_id),
      log_date = values(log_date),
      day_type = values(day_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_hot_video_collect
      (collect_id,account_id,bvid,title,collect_type,is_deleted,ctime,mtime,ad_type_creative_id,log_date,day_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.collectId,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.bvid,jdbcType=VARCHAR},
        #{item.title,jdbcType=VARCHAR},
        #{item.collectType,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.adTypeCreativeId,jdbcType=VARCHAR},
        #{item.logDate,jdbcType=VARCHAR},
        #{item.dayType,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_hot_video_collect
      (collect_id,account_id,bvid,title,collect_type,is_deleted,ctime,mtime,ad_type_creative_id,log_date,day_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.collectId,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.bvid,jdbcType=VARCHAR},
        #{item.title,jdbcType=VARCHAR},
        #{item.collectType,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.adTypeCreativeId,jdbcType=VARCHAR},
        #{item.logDate,jdbcType=VARCHAR},
        #{item.dayType,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      collect_id = values(collect_id),
      account_id = values(account_id),
      bvid = values(bvid),
      title = values(title),
      collect_type = values(collect_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      ad_type_creative_id = values(ad_type_creative_id),
      log_date = values(log_date),
      day_type = values(day_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_hot_video_collect
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="collectId != null">
        collect_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="bvid != null">
        bvid,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="collectType != null">
        collect_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="adTypeCreativeId != null">
        ad_type_creative_id,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="dayType != null">
        day_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="collectId != null">
        #{collectId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bvid != null">
        #{bvid,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="collectType != null">
        #{collectType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="adTypeCreativeId != null">
        #{adTypeCreativeId,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=VARCHAR},
      </if>
      <if test="dayType != null">
        #{dayType,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="collectId != null">
        collect_id = values(collect_id),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="bvid != null">
        bvid = values(bvid),
      </if>
      <if test="title != null">
        title = values(title),
      </if>
      <if test="collectType != null">
        collect_type = values(collect_type),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="adTypeCreativeId != null">
        ad_type_creative_id = values(ad_type_creative_id),
      </if>
      <if test="logDate != null">
        log_date = values(log_date),
      </if>
      <if test="dayType != null">
        day_type = values(day_type),
      </if>
    </trim>
  </insert>
</mapper>