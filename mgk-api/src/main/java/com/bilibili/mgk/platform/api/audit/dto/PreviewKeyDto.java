package com.bilibili.mgk.platform.api.audit.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName PreviewKeyDto
 * <AUTHOR>
 * @Date 2022/12/13 2:13 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreviewKeyDto implements Serializable {
    private static final long serialVersionUID = 1011212312312331230L;

    private Long pageId;

    private String previewKey;

    private Long shadowVersion;
}
