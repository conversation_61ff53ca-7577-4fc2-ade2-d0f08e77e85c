package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkCmVideoPo implements Serializable {
    /**
     * id
     */
    private Integer id;

    private Timestamp ctime;

    private Timestamp mtime;

    /**
     * 原始文件名
     */
    private String rawName;

    /**
     * 原始文件url(未转码)
     */
    private String url;

    /**
     * 原始文件大小(单位kb)
     */
    private Integer sizeInKb;

    /**
     * 原始文件md5
     */
    private String md5;

    /**
     * ssa预上传id(用于关联mgk_video_library里冗余的数据)
     */
    private Integer bizId;

    private static final long serialVersionUID = 1L;
}