package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkWechatAccountPo implements Serializable {
    /**
     * 主键自增id
     */
    private Integer id;

    /**
     * 微信号
     */
    private String wechatAccount;

    /**
     * 微信名称
     */
    private String wechatName;

    /**
     * 账户类型 0-个人号 1-公众号 2-企业微信 3-其他
     */
    private Integer type;

    /**
     * 其他账户额外信息
     */
    private String info;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否被删除 0-正常 1-被删除
     */
    private Integer isDeleted;

    /**
     * 账户id
     */
    private Integer accountId;

    private static final long serialVersionUID = 1L;
}