package com.bilibili.collage.biz.service;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.bilibili.collage.biz.config.EsSearchConfig;
import com.bilibili.collage.biz.entity.EsDSLResponse;
import com.bilibili.collage.biz.utils.BeanUtil;
import com.bilibili.collage.biz.utils.PageUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EsSearchService<T> {

    @Autowired
    private EsSearchConfig esSearchConfig;

    public PageInfo<T> getEsList(SearchSourceBuilder searchSourceBuilder,String business, Class<T> clazz) {
        try{
            String searchInfo = remoteFiled(searchSourceBuilder.toString(), "disable_coord");
            EsDSLResponse response = esSearchConfig.getDSLSearchResult(searchInfo,business);
            log.info("查询ES DSL信息结果：参数:{},结果：{}", searchInfo, JSON.toJSONString(response));
            return PageUtils.getPageFromSearchResult(response, clazz);
        }catch (Exception ex){
            log.error("jestClient execute error", ex);
            return null;
        }

    }

    private String remoteFiled(String jsonString, String filed){
        try{
            JSONObject jsonObject = BeanUtil.jsonRemoveParams(jsonString, filed);
            return jsonObject.toString();
        }catch (Exception ex){
            log.error("jestClient json error",ex);
            return jsonString;
        }
    }

    public PageInfo<T> getEsList(SearchSourceBuilder searchSourceBuilder, String business, Class<T> clazz, String token) {
        try {
            String searchInfo = remoteFiled(searchSourceBuilder.toString(), "disable_coord");
            EsDSLResponse response = esSearchConfig.getDSLSearchResult(searchInfo, business, token);
            log.info("查询ES DSL信息结果：参数:{},结果：{}", searchInfo, JSON.toJSONString(response));
            return PageUtils.getPageFromSearchResult(response, clazz);
        } catch (Exception ex) {
            log.error("jestClient execute error", ex);
            return null;
        }

    }
}
