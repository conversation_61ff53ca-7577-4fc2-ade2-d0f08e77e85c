<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.ext.ExtMgkInspirationArticleDao">
    <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkInspirationArticlePo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="article_id" jdbcType="BIGINT" property="articleId"/>
        <result column="account_id" jdbcType="INTEGER" property="accountId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="cover" jdbcType="VARCHAR" property="cover"/>
        <result column="industry" jdbcType="VARCHAR" property="industry"/>
        <result column="article_read" jdbcType="BIGINT" property="articleRead"/>
        <result column="article_like" jdbcType="BIGINT" property="articleLike"/>
        <result column="article_status" jdbcType="INTEGER" property="articleStatus"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.bilibili.mgk.platform.biz.po.MgkInspirationArticlePo">
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
    </resultMap>
    <update id="increaseRead" parameterType="java.lang.Long">
        update mgk_inspiration_article set article_read = article_read + 1 where article_id = #{articleId,jdbcType=BIGINT}
    </update>

    <update id="increaseLike" parameterType="java.lang.Long">
        update mgk_inspiration_article set article_like = article_like + 1 where article_id = #{articleId,jdbcType=BIGINT}
    </update>

    <update id="decreaseLike" parameterType="java.lang.Long">
        update mgk_inspiration_article set article_like = article_like - 1 where article_id = #{articleId,jdbcType=BIGINT}
    </update>
</mapper>