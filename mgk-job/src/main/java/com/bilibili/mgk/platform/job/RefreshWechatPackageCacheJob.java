package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatPackageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName refreshWechatPackageCacheJob
 * <AUTHOR>
 * @Date 2022/6/16 3:55 下午
 * @Version 1.0
 **/
@Component
@JobHandler("RefreshWechatPackageCacheJob")
@Slf4j
public class RefreshWechatPackageCacheJob extends IJobHandler {

    @Autowired
    private IMgkWechatPackageService mgkWechatPackageService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info(" RefreshWechatPackageCacheJob start.......");
        try {
            mgkWechatPackageService.refreshWechatPackageCache();
        } catch (Exception e) {
            log.error("RefreshWechatPackageCacheJob encounter an exception", e);
        }
        log.info("RefreshWechatPackageCacheJob finish.......");
        return SUCCESS;
    }
}
