package com.bilibili.mgk.platform.api.archive.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @file: IMgkCmSpaceService
 * @author: gaoming
 * @date: 2021/12/13
 * @version: 1.0
 * @description:
 **/
public interface IMgkCmSpaceService {

    /**
     * 根据accountId获取mid
     *
     * @param aids
     * @return
     */
    Map<Integer, Long> getAidToMidMap(List<Integer> aids);

    /**
     * 根据mids获取accountId
     *
     * @param mids
     * @return
     */
    Map<Long, Integer> getMidToAidMap(List<Long> mids);

    Map<Long, List<Integer>> getMidToAidsMap(List<Long> mids);

    /**
     * 获取所有是商业小号的mid
     */
    List<Long> getArcMidsInMgkCmSpace(List<Long> mids);
}
