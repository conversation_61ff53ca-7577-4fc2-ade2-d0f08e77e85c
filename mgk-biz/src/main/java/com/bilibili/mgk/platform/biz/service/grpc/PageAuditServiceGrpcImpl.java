package com.bilibili.mgk.platform.biz.service.grpc;

import com.bapis.ad.mgk.audit.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.audit.dto.MgkAuditPageActDto;
import com.bilibili.mgk.platform.api.audit.dto.MgkAuditPageDto;
import com.bilibili.mgk.platform.api.audit.dto.PreviewKeyDto;
import com.bilibili.mgk.platform.api.audit.dto.QueryMgkAuditPageDto;
import com.bilibili.mgk.platform.api.audit.service.IMgkAuditPageService;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.log.dto.MgkPageAuditLogExportDto;
import com.bilibili.mgk.platform.api.log.dto.MgkPageOperateLogHistoryDto;
import com.bilibili.mgk.platform.api.log.dto.QueryMgkPageOperateLogDataDto;
import com.bilibili.mgk.platform.api.log.service.IMgkPageOperateLogService;
import com.bilibili.mgk.platform.common.LandingPageAuditStatusEnum;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.mgk.platform.common.MgkOperationType;
import com.bilibili.mgk.platform.common.WechatTypeEnum;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import com.google.common.base.Throwables;
import com.google.protobuf.Empty;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName AuditServiceGrpcImpl
 * <AUTHOR>
 * @Date 2022/12/12 10:48 上午
 * @Version 1.0
 **/
@Service
@Slf4j
public class PageAuditServiceGrpcImpl extends PageAuditServiceGrpc.PageAuditServiceImplBase {
    private static final String ID = "PageAuditServiceGrpcImpl";

    private static final String SEND_AUDIT_TIME = "send_audit_time desc";
    private static final String AUDIT_TIME = "audit_time desc";

    @Autowired
    private IMgkAuditPageService mgkAuditPageService;

    @Autowired
    private IMgkLandingPageService mgkLandingPageService;

    @Autowired
    private IMgkPageOperateLogService mgkPageOperateLogService;
    @Override
    public void auditPage(AuditPageReq request, StreamObserver<AuditPageReply> responseObserver) {
        try {
            Assert.notNull(request, "查询参数不可为空");
            QueryMgkAuditPageDto queryDto = buildQueryDto(request);
            PageResult<MgkAuditPageDto> pageResult = mgkAuditPageService.queryMgkAuditPage(queryDto);
            AuditPageReply auditPageReply = convertPageResult2Reply(pageResult);
            responseObserver.onNext(auditPageReply);
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: auditPage 失败,{}", ID, Throwables.getStackTraceAsString(t));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditPage 失败,{}", ID, Throwables.getStackTraceAsString(e));
        }
    }

    private QueryMgkAuditPageDto buildQueryDto(AuditPageReq request) {
        Integer accountId = Utils.isPositive(request.getAccountId()) ?
                request.getAccountId() : null;
        Long pageId = Utils.isPositive(request.getPageId()) ?
                request.getPageId() : null;
        String auditorName = StringUtils.isEmpty(request.getAuditorName()) ? null : request.getAuditorName();
        Integer auditStatus = Utils.isPositive(request.getAuditStatus()) ?
                request.getAuditStatus() : null;
        Timestamp startAuditTime = Utils.isPositive(request.getStartAuditTime()) ?
                new Timestamp(request.getStartAuditTime()) : null;
        Timestamp endAuditTime = Utils.isPositive(request.getEndAuditTime()) ?
                new Timestamp(request.getEndAuditTime()) : null;
        Timestamp startSendAuditTime = Utils.isPositive(request.getStartSendAuditTime()) ?
                new Timestamp(request.getStartSendAuditTime()) : null;
        Timestamp endSendAuditTime = Utils.isPositive(request.getEndSendAuditTime()) ?
                new Timestamp(request.getEndSendAuditTime()) : null;
        String orderBy = LandingPageAuditStatusEnum.WAIT_AUDIT.getCode().equals(request.getAuditStatus()) ?
                SEND_AUDIT_TIME : AUDIT_TIME;
        Page page = Page.valueOf(request.getPage().getPageNum(), request.getPage().getPageSize());
        return QueryMgkAuditPageDto.builder()
                .accountId(accountId)
                .pageId(pageId)
                .auditorName(auditorName)
                .auditStatus(auditStatus)
                .startAuditTime(startAuditTime)
                .endAuditTime(endAuditTime)
                .startSendAuditTime(startSendAuditTime)
                .endSendAuditTime(endSendAuditTime)
                .orderBy(orderBy)
                .page(page)
                .build();
    }

    private AuditPageReply convertPageResult2Reply(PageResult<MgkAuditPageDto> pageResult) {
        AuditPageReply.Builder builder = AuditPageReply.newBuilder();
        builder.setTotal(pageResult.getTotal());
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            List<MgkAuditPageDto> records = pageResult.getRecords();
            List<MgkPageEntity> entities = records.stream()
                    .map(this::convertResultRecord2Entity)
                    .collect(Collectors.toList());
            builder.addAllMgkPage(entities);
        }
        return builder.build();
    }

    private MgkPageEntity convertResultRecord2Entity(MgkAuditPageDto resultDto) {
        MgkPageEntity.Builder builder = MgkPageEntity.newBuilder();
        builder.setAccountId(resultDto.getAccountId());
        builder.setAccountName(resultDto.getAccountName());
        builder.setPageId(resultDto.getPageId());
        builder.setType(resultDto.getType());
        builder.setJumpUrl(resultDto.getJumpUrl() == null ? "" : resultDto.getJumpUrl());
        builder.setJumpUrlSecondary(resultDto.getJumpUrlSecondary() == null ? "" : resultDto.getJumpUrlSecondary());
        builder.setOnlineJumpUrl(resultDto.getOnlineJumpUrl() == null ? "" : resultDto.getOnlineJumpUrl());
        builder.setOnlineJumpUrlSecondary(resultDto.getOnlineJumpUrlSecondary() == null ? "" : resultDto.getOnlineJumpUrlSecondary());
        builder.setAuditTime(Utils.isPositive(resultDto.getAuditTime()) ? resultDto.getAuditTime() : 0);
        builder.setSendAuditTime(Utils.isPositive(resultDto.getSendAuditTime()) ? resultDto.getSendAuditTime() : 0);
        builder.setAuditorName(resultDto.getAuditorName() == null ? "" : resultDto.getAuditorName());
        builder.setAuditStatus(resultDto.getAuditStatus());
        builder.setAuditStatusDesc(LandingPageAuditStatusEnum.getByCode(resultDto.getAuditStatus()).getDesc());
        builder.setReason(resultDto.getReason() == null ? "" : resultDto.getReason());
        builder.setOriginStatus(resultDto.getOriginStatus());
        builder.setOriginStatusDesc(LandingPageStatusEnum.getByCode(resultDto.getOriginStatus()).getDesc());
        builder.setWechatPackageId(resultDto.getWechatPackageId());
        builder.setWechatPackageName(resultDto.getWechatPackageName() == null ? "" : resultDto.getWechatPackageName());
        builder.setWechatPackageType(resultDto.getWechatPackageType());
        builder.setWechatPackageTypeDesc(
                Utils.isPositive(resultDto.getWechatPackageId()) ?
                        WechatTypeEnum.getByCode(resultDto.getWechatPackageType()).getDesc() : "");
        builder.setShadowVersion(resultDto.getShadowVersion());
        if (!CollectionUtils.isEmpty(resultDto.getWechatAccountIdList())) {
            builder.addAllWechatAccountId(resultDto.getWechatAccountIdList());
        }
        if (!CollectionUtils.isEmpty(resultDto.getWechatAccountList())) {
            builder.addAllWechatAccount(resultDto.getWechatAccountList());
        }
        if (!CollectionUtils.isEmpty(resultDto.getAppPackageIdList())) {
            builder.addAllAppPackageId(resultDto.getAppPackageIdList());
        }
        if (!CollectionUtils.isEmpty(resultDto.getAppPackageNameList())) {
            builder.addAllAppPackageName(resultDto.getAppPackageNameList());
        }
        return builder.build();

    }

    @Override
    public void auditPass(AuditReq request, StreamObserver<Empty> responseObserver) {
        try {
            Assert.notNull(request, "审核数据不可为空");
            MgkAuditPageActDto auditDto = MgkAuditPageActDto.builder()
                    .pageId(request.getPageId())
                    .auditorName(request.getAuditorName())
                    .shadowVersion(request.getShadowVersion())
                    .reason(request.getReason())
                    .build();
            mgkLandingPageService.auditPass(auditDto);
            responseObserver.onNext(Empty.newBuilder().build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: auditPass 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditPass 失败,{}", ID, ExceptionUtils.getSubStringMsg(e));
        }
    }

    @Override
    public void auditReject(AuditReq request, StreamObserver<Empty> responseObserver) {
        try {
            Assert.notNull(request, "审核数据不可为空");
            MgkAuditPageActDto auditDto = MgkAuditPageActDto.builder()
                    .pageId(request.getPageId())
                    .auditorName(request.getAuditorName())
                    .shadowVersion(request.getShadowVersion())
                    .reason(request.getReason())
                    .build();
            mgkLandingPageService.auditReject(auditDto);
            responseObserver.onNext(Empty.newBuilder().build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: auditReject 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditReject 失败,{}", ID, ExceptionUtils.getSubStringMsg(e));
        }
    }

    @Override
    public void generatePreviewKey(GenKeyReq request, StreamObserver<GenKeyReply> responseObserver) {
        try {
            Assert.notNull(request, "请求参数不可为空");
            PreviewKeyDto previewKeyDto =
                    mgkAuditPageService.generatePreviewKey(request.getPageId());
            GenKeyReply reply = GenKeyReply.newBuilder()
                    .setPageId(previewKeyDto.getPageId())
                    .setPreviewKey(previewKeyDto.getPreviewKey())
                    .setShadowVersion(previewKeyDto.getShadowVersion())
                    .build();
            responseObserver.onNext(reply);
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: generatePreviewKey 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:generatePreviewKey 失败,{}", ID, ExceptionUtils.getSubStringMsg(e));
        }
    }

    @Override
    public void singleHistory(HistoryReq request, StreamObserver<HistoryReply> responseObserver) {
        try {
            Assert.isTrue(Objects.nonNull(request)
                    && Utils.isPositive(request.getPageId()), "请求落地页历史id不可为空");
            Long pageId = request.getPageId();
            int pageNum = request.getPage().getPageNum(),
                pageSize = request.getPage().getPageSize();
            if (!Utils.isPositive(pageNum)) {
                pageNum = 1;
            }
            if (!Utils.isPositive(pageSize)) {
                pageSize = 15;
            }
            Page page = Page.valueOf(pageNum, pageSize);
            PageResult<MgkPageOperateLogHistoryDto> mgkPageHistoryPageResult =
                    mgkPageOperateLogService.queryLogByPage(pageId, page);
            List<HistoryEntity> historyEntities =
                    convertHistoryDtos2Entities(mgkPageHistoryPageResult.getRecords());
            HistoryReply.Builder builder = HistoryReply.newBuilder();
            if (!CollectionUtils.isEmpty(historyEntities)) {
                builder.addAllHistory(historyEntities);
            }
            builder.setTotal(mgkPageHistoryPageResult.getTotal());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: singleHistory 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:singleHistory 失败,{}", ID, ExceptionUtils.getSubStringMsg(e));
        }
    }

    private List<HistoryEntity> convertHistoryDtos2Entities(List<MgkPageOperateLogHistoryDto> historyDtos) {
        return historyDtos.stream().map(historyDto -> {
            HistoryEntity.Builder builder = HistoryEntity.newBuilder();
            builder.setPageId(historyDto.getPageId());
            builder.setOperatorName(historyDto.getOperatorName());
            builder.setOperatorType(historyDto.getOperatorType());
            builder.setOperateType(historyDto.getOperateType());
            builder.setOperateValue(historyDto.getOperateValue());
            builder.setCtime(historyDto.getCtime());
            return builder.build();
        }).collect(Collectors.toList());
    }

    @Override
    public void exportHistory(ExportHistoryReq request, StreamObserver<ExportHistoryReply> responseObserver) {
        try {
            QueryMgkPageOperateLogDataDto queryDto = convertReq2QueryDto(request);
            List<MgkPageAuditLogExportDto> exportDtos =
                    mgkPageOperateLogService.queryLogExportDtosByParam(queryDto);
            ExportHistoryReply.Builder builder = ExportHistoryReply.newBuilder();
            List<ExportHistoryEntity> exportHistoryEntities = convertExportDtos2Entities(exportDtos);
            if (!CollectionUtils.isEmpty(exportHistoryEntities)) {
                builder.addAllExportHistory(exportHistoryEntities);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: exportHistory 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:exportHistory 失败,{}", ID, ExceptionUtils.getSubStringMsg(e));
        }
    }

    private QueryMgkPageOperateLogDataDto convertReq2QueryDto(ExportHistoryReq request) {
        Timestamp startAuditTime = Utils.isPositive(request.getStartAuditTime()) ?
                new Timestamp(request.getStartAuditTime()) : null;
        Timestamp endAuditTime = Utils.isPositive(request.getEndAuditTime()) ?
                new Timestamp(request.getEndAuditTime()) : null;
        Timestamp startSendAuditTime = Utils.isPositive(request.getStartSendAuditTime()) ?
                new Timestamp(request.getStartSendAuditTime()) : null;
        Timestamp endSendAuditTime = Utils.isPositive(request.getEndSendAuditTime()) ?
                new Timestamp(request.getEndSendAuditTime()) : null;
        List<Integer> operateTypeList = CollectionUtils.isEmpty(request.getOperateTypeList()) ?
                null : request.getOperateTypeList();
        return QueryMgkPageOperateLogDataDto.builder()
                .beginId(request.getBeginId())
                .operateTypeList(operateTypeList)
                .auditorName(request.getAuditorName())
                .startSendAuditTime(startSendAuditTime)
                .endSendAuditTime(endSendAuditTime)
                .startAuditTime(startAuditTime)
                .endAuditTime(endAuditTime)
                .limit(request.getLimit())
                .build();
    }

    private List<ExportHistoryEntity> convertExportDtos2Entities(List<MgkPageAuditLogExportDto> exportDtos) {
        return exportDtos.stream()
                .map(exportDto ->{
                    ExportHistoryEntity.Builder builder = ExportHistoryEntity.newBuilder();
                    builder.setId(exportDto.getId());
                    builder.setAccountId(exportDto.getAccountId());
                    builder.setPageId(exportDto.getPageId());
                    builder.setJumpUrl(exportDto.getJumpUrl());
                    builder.setJumpUrlSecondary(exportDto.getJumpUrlSecondary());
                    builder.setOperatorName(exportDto.getAuditorName());
                    builder.setOperateTypeDesc(exportDto.getOperateTypeDesc());
                    builder.setReason(exportDto.getReason());
                    builder.setAuditTime(exportDto.getAuditTime());
                    builder.setSendAuditTime(exportDto.getSendAuditTime());
                    builder.setWorkOrderId(exportDto.getWorkOrderId());
                    if (!CollectionUtils.isEmpty(exportDto.getAppPackageIds())) {
                        builder.addAllAppPackageId(exportDto.getAppPackageIds());
                    }
                    if (Utils.isPositive(exportDto.getWechatPackageId())) {
                        builder.setWechatPackageId(exportDto.getWechatPackageId());
                    }
                    return builder.build();
                }).collect(Collectors.toList());
    }
}
