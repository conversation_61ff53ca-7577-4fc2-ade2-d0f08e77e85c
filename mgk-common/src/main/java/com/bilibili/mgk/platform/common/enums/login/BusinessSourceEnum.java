package com.bilibili.mgk.platform.common.enums.login;

import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.UserType;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;


@AllArgsConstructor
public enum BusinessSourceEnum {

    OTHER(0, "其他", Lists.newArrayList(UserType.PERSONAL_USER.getCode(), UserType.ORG_USER.getCode()),
            OperatorType.PERSON_FLY.getCode()),

    PERSONAL_FLY(1, "个人起飞", Lists.newArrayList(UserType.PERSONAL_FLY.getCode()),
            OperatorType.PERSON_FLY.getCode()),

    BUSINESS_TOOL(2, "号经营", Lists.newArrayList(UserType.PERSONAL_USER.getCode(), UserType.ORG_USER.getCode()),
            OperatorType.SECONDARY_AGENT.getCode()),
    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;
    @Getter
    private final List<Integer> userTypes;
    @Getter
    private final Integer operatorType;

    public static BusinessSourceEnum getByCode(int code) {
        for (BusinessSourceEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code BusinessSourceEnum " + code);
    }


}
