package com.bilibili.mgk.platform.portal.manager;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.portal.manager.bean.ApiBaseRequest;
import com.bilibili.mgk.platform.portal.manager.bean.ContributeInfo;
import com.bilibili.mgk.platform.portal.manager.bean.ContributeResponse;
import com.bilibili.mgk.platform.portal.manager.bean.Video;
import com.bilibili.mgk.platform.portal.manager.handler.ContributeHandler;
import com.bilibili.mgk.platform.portal.util.GetSignUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @注释
 * @date 2018年05月09日
 */
@Component
public class ContributeManager {

	private final static Logger LOGGER = LoggerFactory.getLogger(ContributeManager.class);


	@Value("${cm.contribute.url}")
	private String cmContributeUrl;
	@Value("${passport.key}")
	private String APP_KEY;


	@Autowired
	private ContributeHandler contributeHandler;

	public Integer contribute(ContributeInfo contributeInfo, String cookies, String biliJct) throws ServiceException {
		this.validateContribute(contributeInfo, cookies);
		String sign = GetSignUtils.getPostSign();
		Integer ts = (int) (System.currentTimeMillis() / 1000);
		ApiBaseRequest apiBaseRequest = ApiBaseRequest.builder().appkey(APP_KEY).ts(ts).sign(sign).build();
		String finalUrl = buildUrl(cmContributeUrl, apiBaseRequest, biliJct);
		LOGGER.info("ContributeManager.contribute doPost request url: [{}]", finalUrl);
		ContributeResponse response = null;
		try {
			response = contributeHandler.doPost(finalUrl, contributeInfo, cookies);
		} catch (SystemException e) {
			LOGGER.error("contribute.doPost error", e);
			throw new ServiceException("上传接口超时，请稍后重试");
		}

		LOGGER.info("contribute.doPost response: [{}]", response);
		if (null == response) {
			LOGGER.error("contribute.doPost response is null");
			throw new ServiceException("上传失败，请稍后重试");
		}

		if (!ContributeResponse.SUCCESS.equals(response.getCode())) {
			LOGGER.error("contribute.doPost response failed, code: [{}], message: [{}]", response.getCode(), response.getMessage());
			throw new ServiceException("上传失败["+ response.getMessage() +"]，请稍后重试");
		}
		return response.getData().getAid();
	}

	private String buildUrl(String s, ApiBaseRequest apiBaseRequest, String biliJct) {
		String url = s + "?" + "appkey=" + apiBaseRequest.getAppkey() + "&" + "sign=" + apiBaseRequest.getSign() + "&"
				+ "ts=" + apiBaseRequest.getTs() + "&" + MgkConstants.COOKIE_CSRF + "=" + biliJct;
		return url;
	}

	private void validateContribute(ContributeInfo contributeInfo, String cookies){
		Assert.notNull(contributeInfo,"稿件信息不可为空");
		Assert.notNull(contributeInfo.getCopyright(),"投稿类型不可为空");
		Assert.notNull(contributeInfo.getTid(),"分区ID不可为空");
		Assert.hasText(contributeInfo.getTitle(),"标题不可为空");
		Assert.hasText(contributeInfo.getDesc(),"视频描述不可为空");
		Assert.notEmpty(contributeInfo.getVideos(),"视频信息不可为空");
		Assert.hasText(contributeInfo.getTag(),"视频标签不可为空");
		List<Video> videos = contributeInfo.getVideos();
		for(Video video : videos){
			Assert.notNull(video.getCid(),"视频标识ID不可为空");
			Assert.hasText(video.getFilename(),"视频名称不可为空");
		}

		Assert.hasText(cookies, "Cookie不可为空");
	}

	public String getValueFromCookie(HttpServletRequest request, String key) {
		Cookie[] cookies = request.getCookies();

		if(ArrayUtils.isEmpty(cookies)) {
			return "";
		}

		return Stream.of(cookies)
				.filter(c -> c.getName().equals(key))
				.findFirst()
				.map(c -> c.getValue())
				.orElse("");
	}
}
