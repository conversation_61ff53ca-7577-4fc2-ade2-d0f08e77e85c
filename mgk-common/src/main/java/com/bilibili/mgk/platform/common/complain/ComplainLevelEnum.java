package com.bilibili.mgk.platform.common.complain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单等级
 */
@AllArgsConstructor
public enum ComplainLevelEnum {

    EMERGENCY(3, "紧急"),

    HIG<PERSON>(2, "高"),

    LOW(1, "低");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static ComplainLevelEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("工单等级不可为空");
        }
        for(ComplainLevelEnum lrt: values()) {
            if(lrt.getCode().equals(code)) {
                return lrt;
            }
        }
        throw new IllegalArgumentException("未知的工单等级");
    }
}
