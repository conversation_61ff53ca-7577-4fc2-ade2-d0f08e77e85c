package com.bilibili.mgk.platform.portal.webapi.school.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CreateCommonQuestionVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "模块类型")
    private Byte type;


    @ApiModelProperty(value = "类目id")
    @JSONField(name = "title_id")
    private Long title_id;

    @ApiModelProperty(value = "排序")
    @JSONField(name = "order_num")
    private Integer orderNum;
}
