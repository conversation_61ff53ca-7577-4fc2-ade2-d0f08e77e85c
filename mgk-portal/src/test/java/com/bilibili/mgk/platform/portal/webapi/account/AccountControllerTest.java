package com.bilibili.mgk.platform.portal.webapi.account;

import com.bapis.bcg.stat.AdTrackId;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.crm.platform.soa.ISoaAgentService;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.mgk.platform.api.account.IMgkAccountService;
import com.bilibili.mgk.platform.biz.utils.MgkTrackIdUtil;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.util.SignUtils;
import com.bilibili.mgk.platform.portal.webapi.account.vo.AccountProfileVo;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年12月26日
 */
public class AccountControllerTest extends MockitoDefaultTest {

    @Mock
    private ISoaQueryAccountService soaQueryAccountService;
    @Mock
    private ISoaAgentService soaAgentService;
    @Mock
    private IMgkAccountService mgkAccountService;
    @Mock
    private SignUtils signUtils;

    @InjectMocks
    private AccountController accountController;

    @Test
    public void queryAccountProfile() {
        Context context = BeanTestUtils.initSimpleFields(new Context());
        context.setType(0);
        Response<AccountProfileVo> response = accountController.queryAccountProfile(context);
        assertNotNull(response);
        assertNotNull(response.getResult());
    }

    @Test
    public void updateAccountProfile() {
        Context context = BeanTestUtils.initSimpleFields(new Context());
        context.setType(0);
        Response response = accountController.updateAccountProfile(context,
                BeanTestUtils.initSimpleFields(AccountProfileVo.builder().build()));
        assertEquals(response.getStatus(), Response.SUCCESS(null).getStatus());
    }

    @Test
    public void testGetAccountCard() {
        Context context = BeanTestUtils.initSimpleFields(new Context());
        context.setType(0);
        accountController.getAccountCard(context, "123456");
    }

    @Test
    public void testTrackIdUtils() {
        AdTrackId adTrackId = MgkTrackIdUtil.decryptTrackId("pbaes.qNCth9vnTurw76yVDegC_i3HkTdWzV5mjDqV0VqGhREku-QsPQ9m2vumO9cl8hJe9R1hHUWbFJi2Ve6mpG-T2hrxJousNGg9JEyIz9iiZkxaPCQNEUO1hxlLCLD6osUTTYYzT78evGFX09utiDaMfJjHJJyNkrJSzwg46ikp2OM=");
        System.out.println(adTrackId.getSalesType());
    }
}