package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MgkShadowWriteBackResultDto
 * <AUTHOR>
 * @Date 2023/6/5 9:43 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MgkShadowWriteBackResultDto {

    private Boolean needRefreshHasVideoPage;

}
