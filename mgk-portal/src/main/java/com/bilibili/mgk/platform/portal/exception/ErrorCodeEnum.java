package com.bilibili.mgk.platform.portal.exception;

/**
 * @ClassName ErrorCodeEnums
 * <AUTHOR>
 * @Date 2022/6/19 4:35 下午
 * @Version 1.0
 **/
public enum ErrorCodeEnum {
    /**
     *
     */
    REPEAT_REQUEST(2005, "重复请求"),
    PARAM_ERROR(2010, "参数错误"),
    UPLOAD_FILE_ERROR(2011, "图片格式不能识别"),
    DAY_LIMIT(2020, "超过日上线"),
    ALL_LIMIT(2021, "超过全部上线"),
    UNKNOWN_ERROR(5000, "未知错误"),
    ;
    private int code;
    private String desc ;

    ErrorCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

