package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataFormConfig
 * @author: gaoming
 * @date: 2021/12/03
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataFormConfig implements Serializable {

    private static final long serialVersionUID = -7875387926785561515L;
    private String title;

    private String successMessage;

    private String id;

    private Integer useTitle;
}
