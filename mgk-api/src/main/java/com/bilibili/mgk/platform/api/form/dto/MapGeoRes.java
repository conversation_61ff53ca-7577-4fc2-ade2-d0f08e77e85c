package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MapGeoRes {

    //返回值，0 表示请求成功
    private Integer status;

    private String message;

    //返回结果数目
    private Integer total;

    private List<Result> results;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class Result{

        private Location location;

        private String address;

        private String telephone;
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class Location{

        //纬度值
        private Float lat;

        //经度值
        private Float lng;

    }



}
