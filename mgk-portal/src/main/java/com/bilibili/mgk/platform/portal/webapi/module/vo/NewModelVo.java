package com.bilibili.mgk.platform.portal.webapi.module.vo;

import com.bilibili.mgk.platform.portal.webapi.landing_page.vo.NewLandingPageVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/08
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NewModelVo {
    @ApiModelProperty(value = "模板版本")
    private String model_version;
    @ApiModelProperty(value = "模板名称")
    private String name;
    @ApiModelProperty(value = "模板样式")
    private Integer model_style;
    @ApiModelProperty(value = "模板类型")
    private Integer model_type;
    @ApiModelProperty(value = "模板封面地址")
    private String cover_url;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "所属行业")
    private List<String> trade_ids;
    @ApiModelProperty(value = "模板落地页详情")
    private NewLandingPageVo page;
}
