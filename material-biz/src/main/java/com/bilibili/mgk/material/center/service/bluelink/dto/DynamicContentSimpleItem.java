package com.bilibili.mgk.material.center.service.bluelink.dto;

import com.bilibili.mgk.material.center.service.asset.vo.RichTextDTO;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/26
 */
@Data
@Accessors(chain = true)
public class DynamicContentSimpleItem implements SnakeCaseBody {

    @ApiModelProperty("文案")
    private String text;

    @ApiModelProperty("跳转链接")
    private String link;

    @ApiModelProperty("类型 ， 0 文本； 1链接")
    private Integer type;


    public static DynamicContentSimpleItem fromBluelink(RichTextDTO richTextDTO) {
        DynamicContentSimpleItem item = new DynamicContentSimpleItem()
                .setType(richTextDTO.getRichTextType());

        if (richTextDTO.getRichTextType() == RichTextType.RAW_TEXT) {
            item.setText(richTextDTO.getRawText())
                    .setLink("");
        } else {
            item.setText(richTextDTO.getBluelink().getRawText())
                    .setLink(richTextDTO.getBluelink().getConversionUrl());
        }

        return item;
    }


}
