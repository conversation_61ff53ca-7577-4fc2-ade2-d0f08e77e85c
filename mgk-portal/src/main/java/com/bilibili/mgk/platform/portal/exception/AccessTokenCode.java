package com.bilibili.mgk.platform.portal.exception;

import com.bilibili.adp.common.exception.IExceptionCode;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/07/14
 **/
@AllArgsConstructor
public enum AccessTokenCode implements IExceptionCode {
    SYSTEM_ERROR(-1000, "系统错误"),
    SYSTEM_BUSY(-1, "系统繁忙，此时请开发者稍候再试"),
    REQUEST_SUCCESS(0, "请求成功"),
    APP_SECRET_ERROR(40001, "AppSecret错误或者AppSecret不属于这个公众号，请开发者确认AppSecret的正确性"),
    GRANT_TYPE(40002, "请确保grant_type字段值为client_credential"),
    NOT_WHITE_LIST(40164, "调用接口的IP地址不在白名单中，请在接口IP白名单中进行设置。（小程序及小游戏调用不要求IP地址在白名单内。）"),
    IP_ADMIN_ALLOW(89503, "此IP调用需要管理员确认,请联系管理员"),
    IP_ADMIN_WAIT(89501, "此IP正在等待管理员确认,请联系管理员"),
    ADMIN_REJECT_TWICE(89506, "24小时内该IP被管理员拒绝调用两次，24小时内不可再使用该IP调用"),
    ADMIN_REJECT_HOUR(89507, "1小时内该IP被管理员拒绝调用一次，1小时内不可再使用该IP调用");

    private Integer errcode;
    private String errmsg;


    @Override
    public Integer getCode() {
        return this.getCode();
    }

    @Override
    public String getMessage() {
        return this.getMessage();
    }
}
