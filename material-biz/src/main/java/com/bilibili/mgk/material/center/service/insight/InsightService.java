package com.bilibili.mgk.material.center.service.insight;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.mgk.material.center.service.creative.model.CreativeInsight;
import com.bilibili.mgk.material.center.service.creative.model.InefficientCreative;
import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import java.util.List;

public interface InsightService {

    /**
     * 创意明细
     *
     * @param time
     * @param accountId
     * @param materialType
     * @return
     */
    @Deprecated
    CreativeInsight getInsightDetail(DateAggregation dayType, Integer accountId, MaterialType materialType);


    /**
     * 7天明细
     *
     * @param accountId
     * @param materialType
     * @return
     */
    CreativeInsight getInsightDetailWeek(Integer accountId, MaterialType materialType);


    CreativeInsight getInsightSummary(DateAggregation dayType, Integer accountId);


    /**
     * @param accountId
     * @param pageSize
     * @param pageNum
     * @return
     */
    Pagination<List<InefficientCreative>> getLowEfficient(
            Integer accountId, MaterialType materialType, Integer pageSize, Integer pageNum);

    /**
     * @param creativeIds
     * @param operator
     */
    void pauseCreative(List<Integer> creativeIds, Operator operator);
}
