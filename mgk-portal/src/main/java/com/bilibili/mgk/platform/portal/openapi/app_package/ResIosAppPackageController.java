package com.bilibili.mgk.platform.portal.openapi.app_package;

import com.bilibili.mgk.platform.api.app_package.service.IResAppPackageService;
import com.bilibili.mgk.platform.api.app_package.dto.ResIosAppPackageDto;
import com.bilibili.mgk.platform.portal.common.OpenApiBasicController;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.openapi.app_package.vo.ResIosAppPackageVo;
import com.bilibili.mgk.platform.portal.service.WebResAppPackageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @file: ResIosAppPackageController
 * @author: gaoming
 * @date: 2021/11/25
 * @version: 1.0
 * @description:
 **/

@RestController
@RequestMapping("/open_api/v1/app_package")
@Api(value = "/app_package", description = "App应用包信息相关")
public class ResIosAppPackageController extends OpenApiBasicController {

    @Autowired
    private IResAppPackageService appPackageService;

    @Autowired
    private WebResAppPackageService webResAppPackageService;

    @ApiOperation(value = "App应用包信息获取-建站兜底H5页")
    @RequestMapping(value = "/{package_id}", method = RequestMethod.GET)
    public Response<ResIosAppPackageVo> getIosAppPackageInfo(@ApiParam(value = "app应用包id") @PathVariable(value = "package_id") Integer packageId) throws Exception {
        ResIosAppPackageDto resIosAppPackageDto = appPackageService.getIosAppPackageInfo(packageId);
        if (resIosAppPackageDto == null) {
            return Response.SUCCESS(null);
        }
        return Response.SUCCESS(webResAppPackageService.convertIosAppPackageDto2Vo(resIosAppPackageDto));
    }
}
