package com.bilibili.mgk.material.center.service.asset.model;

import com.bilibili.mgk.material.center.util.JsonUtil;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/25
 */
@Data
@Accessors(chain = true)
public class DynamicCursor {

    private DynamicDataset dataset;

    /**
     * TODO 也可以考虑使用 offset+ limit 方式进行查询，但是目前主站的接口不支持，但是还保留ps pn的设置
     */
    private Integer pn;

    private Integer ps;

    public static DynamicCursor fromCursorString(String cursor, Integer ps) {

        if (StringUtils.isEmpty(cursor)) {

            return headCursor(ps);
        }

        return JsonUtil.readValue(
                new String(Base64.getDecoder().decode(cursor), StandardCharsets.UTF_8),
                DynamicCursor.class)
                ;
    }

    public static DynamicCursor headCursor(Integer defaultPageSize) {

        return new DynamicCursor()
                .setDataset(DynamicDataset.COMMERCE_BLUELINK)
                .setPn(1)
                .setPs(defaultPageSize)
                ;
    }

    public DynamicCursor validate() {

        Assert.notNull(dataset, "dataset is required");
        Assert.notNull(pn, "pn is required");
        Assert.notNull(ps, "ps is required");

        return this;
    }

    public String asCursor() {

        return Base64.getEncoder().encodeToString(
                JsonUtil.writeValueAsString(this).getBytes(StandardCharsets.UTF_8)
        );

    }


}
