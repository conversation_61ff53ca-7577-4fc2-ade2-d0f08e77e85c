package com.bilibili.mgk.platform.api.archive.service;

import com.bilibili.mgk.platform.api.archive.dto.MgkCmVideoDto;

import java.util.List;

/**
 * @file: IMgkCmVideoService
 * @author: gaoming
 * @date: 2021/12/13
 * @version: 1.0
 * @description:
 **/
public interface IMgkCmVideoService {

    /**
     * 根据md5获取视频数据
     *
     * @param md5s
     * @return
     */
    List<MgkCmVideoDto> getMgkCmVideoDtosByMd5s(List<String> md5s);
}
