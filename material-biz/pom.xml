<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bilibili.mgk</groupId>
        <artifactId>mgk-platform</artifactId>
        <version>0.2.54-SNAPSHOT</version>
    </parent>

    <artifactId>material-biz</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <retrofit.version>2.9.0</retrofit.version>
    </properties>


    <dependencies>

        <!--    bapi-->
        <!--    dynamic-->
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>dynamic_service.publish_grpc_java</artifactId>
            <!--      <version>${bapis.version}</version>-->
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.audit_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>dynamic_admin.feed_grpc_java</artifactId>
            <!--      <version>${bapis.version}</version>-->
            <version>${bapis.version}</version>
        </dependency>


        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>dynamic_common_grpc_java</artifactId>
            <!--      <version>${bapis.version}</version>-->
            <version>${bapis.version}</version>
        </dependency>


        <!--    <dependency><groupId>co.bilibili.buf</groupId><artifactId>-->
        <!--    </artifactId><version>${bapis.version}</version></dependency>-->


        <!--  end dynamic  -->

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_component_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_creative_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.resource_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core.v6_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core.list_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>


        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>datacenter_service.oneservice_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>account_service.v2_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>archive_service_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.bluelink_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_cmc.dynamic_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_cmc.comment_goods_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>up.archive_service_grpc_java</artifactId>
            <version>${bapis.version}</version>
        </dependency>
        <!--end bapi-->

        <!--    <dependency>-->
        <!--      <groupId>com.bilibili</groupId>-->
        <!--      <artifactId>warp-spring-boot-starter-databus</artifactId>-->
        <!--      <version>1.1.0</version>-->
        <!--    </dependency>-->


        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-opentelemetry</artifactId>
            <version>1.0.15</version>
        </dependency>

        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>discovery-client</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.bilibili.microservices</groupId>
            <artifactId>databus-java</artifactId>
            <version>1.10.3-RELEASE</version>
        </dependency>


        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-core</artifactId>
            <version>3.8.2</version>
        </dependency>
        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-metadata</artifactId>
            <version>3.8.2</version>
        </dependency>
        <!--        <dependency>-->
        <!--          <groupId>com.twelvemonkeys.imageio</groupId>-->
        <!--          <artifactId>imageio-gif</artifactId>-->
        <!--          <version>3.8.2</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
            <version>${retrofit.version}</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-gson</artifactId>
            <version>${retrofit.version}</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-jackson</artifactId>
            <version>${retrofit.version}</version>
        </dependency>


        <!--    <dependency>-->
        <!--      <groupId>com.twelvemonkeys.imageio</groupId>-->
        <!--      <artifactId>imageio-core</artifactId>-->
        <!--      <version>3.8.2</version>-->
        <!--    </dependency>-->
        <!--    <dependency>-->
        <!--      <groupId>com.twelvemonkeys.imageio</groupId>-->
        <!--      <artifactId>imageio-metadata</artifactId>-->
        <!--      <version>3.8.2</version>-->
        <!--    </dependency>-->
        <!--    <dependency>-->
        <!--      <groupId>com.twelvemonkeys.imageio</groupId>-->
        <!--      <artifactId>imageio-gif</artifactId>-->
        <!--      <version>3.8.2</version>-->
        <!--    </dependency>-->


        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-imaging</artifactId>
            <version>1.0-alpha2</version>
        </dependency>


        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-jpeg</artifactId>
            <version>3.8.2</version>
        </dependency>


        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-webp</artifactId>
            <version>3.8.2</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.0.2</version>
        </dependency>


        <!--    <dependency>-->
        <!--      <groupId>com.twelvemonkeys.imageio</groupId>-->
        <!--      <artifactId>imageio-webp</artifactId>-->
        <!--      <version>3.9.4</version>-->
        <!--    </dependency>-->


        <dependency>
            <groupId>org.sejda.imageio</groupId>
            <artifactId>webp-imageio</artifactId>
            <version>0.1.6</version>
        </dependency>


        <!--    <dependency>-->
        <!--      <groupId>com.github.gotson</groupId>-->
        <!--      <artifactId>webp-imageio</artifactId>-->
        <!--      <version>0.2.2</version>-->
        <!--    </dependency>-->

        <!--    <dependency>-->
        <!--      <groupId>org.sejda.webp-imageio</groupId>-->
        <!--      <artifactId>webp-imageio-sejda</artifactId>-->
        <!--      <version>0.1.0</version>-->
        <!--    </dependency>-->

        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.4.3</version>
        </dependency>


        <!-- material-base 中素材id注册能力 -->


        <dependency>
            <groupId>com.bilibili.mgk</groupId>
            <artifactId>material-base</artifactId>
            <version>${project.parent.version}</version>
        </dependency>


      <dependency>
        <groupId>com.bilibili.mgk</groupId>
        <artifactId>doc-tree</artifactId>
        <version>${project.parent.version}</version>
      </dependency>


      <!--    用于露出httpinvoker-exporter-->


      <dependency>
            <groupId>com.bilibili.mgk</groupId>
            <artifactId>mgk-api</artifactId>
            <version>${project.parent.version}</version>


            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>http-util</artifactId>
                    <groupId>com.bilibili.adp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--   用于复用mgk-biz 中的黑名单等能力 -->

        <dependency>
            <groupId>com.bilibili.mgk</groupId>
            <artifactId>mgk-biz</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bilibili.adp</groupId>
                    <artifactId>passport-biz</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-web</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-webmvc</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.sejda.imageio</groupId>
                    <artifactId>webp-imageio</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bilibili.mgk</groupId>
                    <artifactId>material-biz</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.3.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--    <dependency>-->
        <!--      <groupId>com.github.pagehelper</groupId>-->
        <!--      <artifactId>pagehelper</artifactId>-->
        <!--      <version>5.2.1</version>-->
        <!--    </dependency>-->


        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
            <version>0.10.2</version>
        </dependency>


        <!-- database -->

        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>${clickhouse.version}</version>

            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>${spring-mybatis.version}</version>
        </dependency>


        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
            <version>${c3p0-version}</version>
        </dependency>
        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>${org.modelmapper.version}</version>
        </dependency>

        <!-- spring -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${org.springframework.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${weaver.version}</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.groboutils</groupId>
            <artifactId>groboutils-core</artifactId>
            <version>5</version>
            <scope>test</scope>
        </dependency>
        <!--log-->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${org.slf4j.version}</version>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>4.7</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>${org.springframework.data.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-data-commons</artifactId>
                    <groupId>org.springframework.data</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>${spring.retry.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-cat-mybatis</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version>
        </dependency>


        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-test</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>starter</artifactId>
            <version>${pleiades.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.11.0</version>
        </dependency>


        <dependency>
            <artifactId>grpc-stub</artifactId>
            <groupId>io.grpc</groupId>
            <version>1.29.0</version>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-services</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>pleiades.component</groupId>
            <artifactId>boot</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.component</groupId>
            <artifactId>env</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.venus.naming</groupId>
            <artifactId>naming-client</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.venus.naming</groupId>
            <artifactId>naming-discovery</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.component</groupId>
            <artifactId>stats</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.component</groupId>
            <artifactId>utility</artifactId>
            <version>${pleiades.version}</version>
        </dependency>

        <dependency>
            <groupId>pleiades.component.rpc</groupId>
            <artifactId>rpc-client</artifactId>
            <version>${pleiades.version}</version>
        </dependency>

        <dependency>
            <groupId>pleiades.component.rpc</groupId>
            <artifactId>rpc-core</artifactId>
            <version>${pleiades.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


    <build>

        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.0</version>
            </extension>
        </extensions>
        <plugins>
            <!--      <plugin>-->
            <!--        <groupId>com.bilibili</groupId>-->
            <!--        <artifactId>buf-maven-plugin</artifactId>-->
            <!--        <version>1.1.0</version>-->
            <!--        <executions>-->
            <!--          <execution>-->
            <!--            <goals>-->
            <!--              <goal>generate</goal>-->
            <!--            </goals>-->
            <!--          </execution>-->
            <!--        </executions>-->
            <!--        <configuration>-->
            <!--          <bufVersion>1.23.1</bufVersion>-->
            <!--          <moduleArray>-->
            <!--            <array>buf.bilibili.co/ad/creative</array>-->
            <!--            <array>buf.bilibili.co/archive/service</array>-->
            <!--            <array>buf.bilibili.co/ad/audit</array>-->
            <!--            <array>buf.bilibili.co/account/service.v2</array>-->
            <!--            <array>buf.bilibili.co/datacenter/service.oneservice</array>-->
            <!--            <array>buf.bilibili.co/ad/pandora.resource</array>-->
            <!--            <array>buf.bilibili.co/ad/pandora.core.v6</array>-->
            <!--            <array>buf.bilibili.co/ad/pandora.core.list</array>-->
            <!--            <array>buf.bilibili.co/dynamic/service.publish</array>-->
            <!--            <array>buf.bilibili.co/dynamic/admin.feed</array>-->
            <!--          </moduleArray>-->
            <!--        </configuration>-->
            <!--      </plugin>-->

        </plugins>
    </build>
</project>