CREATE TABLE `mgk_material_ai_derivation_blacklist`
(
    `id`          bigint(20)          NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `account_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户Id',
    `creative_id` bigint(20)          NOT NULL DEFAULT '0' COMMENT '用户Id',
    `unit_id`     bigint(20)          NOT NULL DEFAULT '0' COMMENT '用户Id',
    `campaign_id` bigint(20)          NOT NULL DEFAULT '0' COMMENT '用户Id',
    `img_md5`     varchar(100)                 default '' not null comment '图片md5',
    `img_url`     varchar(300)                 default '' not null comment '图片url',
    `version`     bigint(20)          NOT NULL DEFAULT '0' COMMENT '用户Id',
    `ctime`       datetime            NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`       datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`     tinyint(4)          NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',


    PRIMARY KEY (`id`),
    KEY `idx_account` (`account_id`) USING BTREE,
    UNIQUE KEY `idx_account_md5` (`account_id`, `img_md5`) USING BTREE,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='AI衍生图黑名单';
