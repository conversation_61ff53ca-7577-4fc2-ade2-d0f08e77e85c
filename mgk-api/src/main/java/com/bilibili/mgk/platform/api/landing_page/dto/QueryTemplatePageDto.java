package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @file: QueryTemplatePageDto
 * @author: gaoming
 * @date: 2021/12/01
 * @version: 1.0
 * @description:
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryTemplatePageDto implements Serializable {

    private static final long serialVersionUID = 3489032245045792772L;
    /**
     * 查询的Hash值
     */
    private String hashKey;

    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 落地页id
     */
    private List<Long> pageIds;

    /**
     * 游戏中心gameId
     */
    private Integer gameBaseId;

    /**
     * 安卓应用包
     */
    private Integer packageId;

    /**
     * 外链地址url
     */
    private String url;

    public QueryTemplatePageDto(Long pageId, Integer gameBaseId, Integer packageId, String url) {
        this.pageId = pageId;
        this.gameBaseId = gameBaseId;
        this.packageId = packageId;
        this.url = url;
        this.hashKey = DigestUtils.md5Hex("QueryTemplatePageDto{" +
                "pageId=" + pageId +
                ", gameBaseId=" + gameBaseId +
                ", packageId=" + packageId +
                ", url='" + url + '\'' +
                '}');
    }
}
