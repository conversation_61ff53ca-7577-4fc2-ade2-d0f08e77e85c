<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.material.center.repository.mysql.AIDerivationImageDeliveryRecordMapper">

  <resultMap id="BaseResultMap"
    type="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageDeliveryRecord">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="accountId" column="account_id" jdbcType="BIGINT"/>
    <result property="originCreativeId" column="creative_id" jdbcType="BIGINT"/>
    <result property="originUnitId" column="unit_id" jdbcType="BIGINT"/>
    <result property="campaignId" column="campaign_id" jdbcType="BIGINT"/>
    <result property="version" column="version" jdbcType="BIGINT"/>
    <result property="creativeName" column="creative_name" jdbcType="VARCHAR"/>
    <result property="adTitle" column="ad_title" jdbcType="VARCHAR"/>
    <result property="unitName" column="unit_name" jdbcType="VARCHAR"/>
    <result property="campaignName" column="campaign_name" jdbcType="VARCHAR"/>
    <result property="originImgMd5" column="origin_img_md5" jdbcType="VARCHAR"/>
    <result property="originImgUrl" column="origin_img_url" jdbcType="VARCHAR"/>
    <result property="derivatedImgs" column="derivated_imgs" jdbcType="VARCHAR"/>
    <result property="derivatedExtraInfo" column="derivated_extra_info" jdbcType="VARCHAR"/>
    <result property="deliverCreativeId" column="deliver_creative_id" jdbcType="BIGINT"/>
    <result property="deliverUnitId" column="deliver_unit_id" jdbcType="BIGINT"/>
    <result property="deliverCampaignId" column="deliver_campaign_id" jdbcType="BIGINT"/>
    <result property="deliverImgMd5s" column="deliver_img_md5s" jdbcType="VARCHAR"/>
    <result property="deliverImgUrls" column="deliver_img_urls" jdbcType="VARCHAR"/>
    <result property="deliverTime" column="deliver_time" jdbcType="TIMESTAMP"/>
    <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
    <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    <result property="deleted" column="deleted" jdbcType="TINYINT"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,account_id,creative_id,
        unit_id,campaign_id,version,
        creative_name, ad_title,unit_name,campaign_name,
        origin_img_md5,origin_img_url,derivated_imgs,
        derivated_extra_info,deliver_creative_id,deliver_unit_id,
        deliver_campaign_id,deliver_img_md5s,deliver_img_urls,
        deliver_time,ctime,mtime,
        deleted
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mgk_material_ai_derivation_delivery_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByAccountId"
    resultType="java.util.ArrayList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mgk_material_ai_derivation_delivery_record

    where account_id = #{accountId,jdbcType=BIGINT}

    <if test="deleted != null">
      and deleted = #{deleted,jdbcType=TINYINT}
    </if>


  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from mgk_material_ai_derivation_delivery_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageDeliveryRecord"
    useGeneratedKeys="true">
    insert into mgk_material_ai_derivation_delivery_record
    ( id, account_id, creative_id
    , unit_id, campaign_id, version
    , creative_name, ad_title, unit_name, campaign_name
    , origin_img_md5, origin_img_url, derivated_imgs
    , derivated_extra_info, deliver_creative_id, deliver_unit_id
    , deliver_campaign_id, deliver_img_md5s, deliver_img_urls
    , deliver_time, ctime, mtime
    , deleted)
    values ( #{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{originCreativeId,jdbcType=BIGINT}
           , #{originUnitId,jdbcType=BIGINT}, #{campaignId,jdbcType=BIGINT}, #{version,jdbcType=BIGINT}
           , #{creativeName,jdbcType=VARCHAR}, #{adTitle,jdbcType=VARCHAR}, #{unitName,jdbcType=VARCHAR},
             #{campaignName,jdbcType=VARCHAR}
           , #{originImgMd5,jdbcType=VARCHAR}, #{originImgUrl,jdbcType=VARCHAR}, #{derivatedImgs,jdbcType=VARCHAR}
           , #{derivatedExtraInfo,jdbcType=VARCHAR}, #{deliverCreativeId,jdbcType=BIGINT}
           , #{deliverUnitId,jdbcType=BIGINT}
           , #{deliverCampaignId,jdbcType=BIGINT}, #{deliverImgMd5s,jdbcType=VARCHAR}
           , #{deliverImgUrls,jdbcType=VARCHAR}
           , #{deliverTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
           , #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageDeliveryRecord"
    useGeneratedKeys="true">
    insert into mgk_material_ai_derivation_delivery_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="accountId != null">account_id,</if>
      <if test="originCreativeId != null">creative_id,</if>
      <if test="originUnitId != null">unit_id,</if>
      <if test="campaignId != null">campaign_id,</if>
      <if test="version != null">version,</if>
      <if test="creativeName != null">creative_name,</if>
      <if test="adTitle != null">ad_title,</if>
      <if test="unitName != null">unit_name,</if>
      <if test="campaignName != null">campaign_name,</if>
      <if test="originImgMd5 != null">origin_img_md5,</if>
      <if test="originImgUrl != null">origin_img_url,</if>
      <if test="derivatedImgs != null">derivated_imgs,</if>
      <if test="derivatedExtraInfo != null">derivated_extra_info,</if>
      <if test="deliverCreativeId != null">deliver_creative_id,</if>
      <if test="deliverUnitId != null">deliver_unit_id,</if>
      <if test="deliverCampaignId != null">deliver_campaign_id,</if>
      <if test="deliverImgMd5s != null">deliver_img_md5s,</if>
      <if test="deliverImgUrls != null">deliver_img_urls,</if>
      <if test="deliverTime != null">deliver_time,</if>
      <if test="ctime != null">ctime,</if>
      <if test="mtime != null">mtime,</if>
      <if test="deleted != null">deleted,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=BIGINT},</if>
      <if test="accountId != null">#{accountId,jdbcType=BIGINT},</if>
      <if test="originCreativeId != null">#{originCreativeId,jdbcType=BIGINT},</if>
      <if test="originUnitId != null">#{originUnitId,jdbcType=BIGINT},</if>
      <if test="campaignId != null">#{campaignId,jdbcType=BIGINT},</if>
      <if test="version != null">#{version,jdbcType=BIGINT},</if>
      <if test="creativeName != null">#{creativeName,jdbcType=VARCHAR},</if>
      <if test="adTitle != null">#{adTitle,jdbcType=VARCHAR},</if>
      <if test="unitName != null">#{unitName,jdbcType=VARCHAR},</if>
      <if test="campaignName != null">#{campaignName,jdbcType=VARCHAR},</if>
      <if test="originImgMd5 != null">#{originImgMd5,jdbcType=VARCHAR},</if>
      <if test="originImgUrl != null">#{originImgUrl,jdbcType=VARCHAR},</if>
      <if test="derivatedImgs != null">#{derivatedImgs,jdbcType=VARCHAR},</if>
      <if test="derivatedExtraInfo != null">#{derivatedExtraInfo,jdbcType=VARCHAR},</if>
      <if test="deliverCreativeId != null">#{deliverCreativeId,jdbcType=BIGINT},</if>
      <if test="deliverUnitId != null">#{deliverUnitId,jdbcType=BIGINT},</if>
      <if test="deliverCampaignId != null">#{deliverCampaignId,jdbcType=BIGINT},</if>
      <if test="deliverImgMd5s != null">#{deliverImgMd5s,jdbcType=VARCHAR},</if>
      <if test="deliverImgUrls != null">#{deliverImgUrls,jdbcType=VARCHAR},</if>
      <if test="deliverTime != null">#{deliverTime,jdbcType=TIMESTAMP},</if>
      <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
      <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
      <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageDeliveryRecord">
    update mgk_material_ai_derivation_delivery_record
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="originCreativeId != null">
        creative_id = #{originCreativeId,jdbcType=BIGINT},
      </if>
      <if test="originUnitId != null">
        unit_id = #{originUnitId,jdbcType=BIGINT},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="creativeName != null">
        creative_name = #{creativeName,jdbcType=VARCHAR},
      </if>
      <if test="adTitle != null">
        ad_title = #{adTitle,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        unit_name = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="campaignName != null">
        campaign_name = #{campaignName,jdbcType=VARCHAR},
      </if>
      <if test="originImgMd5 != null">
        origin_img_md5 = #{originImgMd5,jdbcType=VARCHAR},
      </if>
      <if test="originImgUrl != null">
        origin_img_url = #{originImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="derivatedImgs != null">
        derivated_imgs = #{derivatedImgs,jdbcType=VARCHAR},
      </if>
      <if test="derivatedExtraInfo != null">
        derivated_extra_info = #{derivatedExtraInfo,jdbcType=VARCHAR},
      </if>
      <if test="deliverCreativeId != null">
        deliver_creative_id = #{deliverCreativeId,jdbcType=BIGINT},
      </if>
      <if test="deliverUnitId != null">
        deliver_unit_id = #{deliverUnitId,jdbcType=BIGINT},
      </if>
      <if test="deliverCampaignId != null">
        deliver_campaign_id = #{deliverCampaignId,jdbcType=BIGINT},
      </if>
      <if test="deliverImgMd5s != null">
        deliver_img_md5s = #{deliverImgMd5s,jdbcType=VARCHAR},
      </if>
      <if test="deliverImgUrls != null">
        deliver_img_urls = #{deliverImgUrls,jdbcType=VARCHAR},
      </if>
      <if test="deliverTime != null">
        deliver_time = #{deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageDeliveryRecord">
    update mgk_material_ai_derivation_delivery_record
    set account_id           = #{accountId,jdbcType=BIGINT},
        creative_id          = #{originCreativeId,jdbcType=BIGINT},
        unit_id              = #{originUnitId,jdbcType=BIGINT},
        campaign_id          = #{campaignId,jdbcType=BIGINT},
        version              = #{version,jdbcType=BIGINT},
        creative_name        = #{creativeName,jdbcType=VARCHAR},
        ad_title             = #{adTitle,jdbcType=VARCHAR},
        unit_name            = #{unitName,jdbcType=VARCHAR},
        campaign_name        = #{campaignName,jdbcType=VARCHAR},
        origin_img_md5       = #{originImgMd5,jdbcType=VARCHAR},
        origin_img_url       = #{originImgUrl,jdbcType=VARCHAR},
        derivated_imgs       = #{derivatedImgs,jdbcType=VARCHAR},
        derivated_extra_info = #{derivatedExtraInfo,jdbcType=VARCHAR},
        deliver_creative_id  = #{deliverCreativeId,jdbcType=BIGINT},
        deliver_unit_id      = #{deliverUnitId,jdbcType=BIGINT},
        deliver_campaign_id  = #{deliverCampaignId,jdbcType=BIGINT},
        deliver_img_md5s     = #{deliverImgMd5s,jdbcType=VARCHAR},
        deliver_img_urls     = #{deliverImgUrls,jdbcType=VARCHAR},
        deliver_time         = #{deliverTime,jdbcType=TIMESTAMP},
        ctime                = #{ctime,jdbcType=TIMESTAMP},
        mtime                = #{mtime,jdbcType=TIMESTAMP},
        deleted              = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
