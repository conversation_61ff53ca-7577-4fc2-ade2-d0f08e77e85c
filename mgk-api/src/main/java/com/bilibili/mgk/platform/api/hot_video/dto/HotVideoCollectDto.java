package com.bilibili.mgk.platform.api.hot_video.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @file: HotVideoCollectDto
 * @author: gaoming
 * @date: 2020/11/12
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotVideoCollectDto implements Serializable {
    private static final long serialVersionUID = 8684673920652292137L;

    /**
     * 自增Id
     */
    private Integer id;

    /**
     * 收藏Id
     */
    private Long collectId;

    /**
     * 用户Id
     */
    private Integer accountId;

    /**
     * bvid
     */
    private String bvid;

    /**
     * 标题
     */
    private String title;

    /**
     * 收藏类型 0-热门视频 1-热门广告
     */
    private Integer collectType;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 广告类型和创意Id
     */
    private String adTypeCreativeId;

    /**
     * 数据日期
     */
    private String logDate;

    private Integer dayType;
}
