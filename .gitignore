.idea/
.idea/*
mgk-api/mgk-api.iml
mgk-biz/mgk-biz.iml
mgk-common/mgk-common.iml
mgk-job/mgk-job.iml
mgk-platform.iml
mgk-portal/mgk-portal.iml
collage-api/collage-api.iml
collage-biz/collage-biz.iml
mgk-api/.*
mgk-api/target/*
mgk-job/target/*
mgk-portal/target/*
mgk-biz/target/
mgk-portal/target/
mgk-portal/src/main/resources/statsd.properties
mgk-portal/src/main/resources/redis.properties
mgk-portal/src/main/resources/mgk-portal.properties
mgk-portal/src/main/resources/database.properties
mgk-portal/src/main/resources/cat.xml
mgk-portal/src/main/resources/bfs.properties
mgk-portal/.gitignore
mgk-portal/.classpath
mgk-job/target/mgk-portal.jar
mgk-job/target/maven-archiver/pom.properties
mgk-common/.gitignore
mgk-common/.classpath
mgk-biz/src/test/resources/statsd.properties
mgk-biz/src/test/resources/redis.properties
mgk-biz/src/test/resources/mgk-portal.properties
mgk-biz/src/test/resources/database.properties
mgk-biz/src/test/resources/cat.xml
mgk-biz/src/test/resources/bfs.properties
mgk-portal/src/main/resources/dns-cache.properties
mgk-biz/.gitignore
mgk-biz/.classpath
dns-cache.properties
*.class
.settings/
.project
.classpath
target/
*.versionsBackup
*.DS_Store
*.iml
