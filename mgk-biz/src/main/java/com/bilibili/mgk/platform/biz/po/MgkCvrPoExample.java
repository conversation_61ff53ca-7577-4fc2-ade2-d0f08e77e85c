package com.bilibili.mgk.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MgkCvrPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MgkCvrPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPageIdIsNull() {
            addCriterion("page_id is null");
            return (Criteria) this;
        }

        public Criteria andPageIdIsNotNull() {
            addCriterion("page_id is not null");
            return (Criteria) this;
        }

        public Criteria andPageIdEqualTo(Long value) {
            addCriterion("page_id =", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotEqualTo(Long value) {
            addCriterion("page_id <>", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdGreaterThan(Long value) {
            addCriterion("page_id >", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdGreaterThanOrEqualTo(Long value) {
            addCriterion("page_id >=", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdLessThan(Long value) {
            addCriterion("page_id <", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdLessThanOrEqualTo(Long value) {
            addCriterion("page_id <=", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdIn(List<Long> values) {
            addCriterion("page_id in", values, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotIn(List<Long> values) {
            addCriterion("page_id not in", values, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdBetween(Long value1, Long value2) {
            addCriterion("page_id between", value1, value2, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotBetween(Long value1, Long value2) {
            addCriterion("page_id not between", value1, value2, "pageId");
            return (Criteria) this;
        }

        public Criteria andMainColorIsNull() {
            addCriterion("main_color is null");
            return (Criteria) this;
        }

        public Criteria andMainColorIsNotNull() {
            addCriterion("main_color is not null");
            return (Criteria) this;
        }

        public Criteria andMainColorEqualTo(Integer value) {
            addCriterion("main_color =", value, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorNotEqualTo(Integer value) {
            addCriterion("main_color <>", value, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorGreaterThan(Integer value) {
            addCriterion("main_color >", value, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorGreaterThanOrEqualTo(Integer value) {
            addCriterion("main_color >=", value, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorLessThan(Integer value) {
            addCriterion("main_color <", value, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorLessThanOrEqualTo(Integer value) {
            addCriterion("main_color <=", value, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorIn(List<Integer> values) {
            addCriterion("main_color in", values, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorNotIn(List<Integer> values) {
            addCriterion("main_color not in", values, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorBetween(Integer value1, Integer value2) {
            addCriterion("main_color between", value1, value2, "mainColor");
            return (Criteria) this;
        }

        public Criteria andMainColorNotBetween(Integer value1, Integer value2) {
            addCriterion("main_color not between", value1, value2, "mainColor");
            return (Criteria) this;
        }

        public Criteria andPageHeightIsNull() {
            addCriterion("page_height is null");
            return (Criteria) this;
        }

        public Criteria andPageHeightIsNotNull() {
            addCriterion("page_height is not null");
            return (Criteria) this;
        }

        public Criteria andPageHeightEqualTo(Float value) {
            addCriterion("page_height =", value, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightNotEqualTo(Float value) {
            addCriterion("page_height <>", value, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightGreaterThan(Float value) {
            addCriterion("page_height >", value, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightGreaterThanOrEqualTo(Float value) {
            addCriterion("page_height >=", value, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightLessThan(Float value) {
            addCriterion("page_height <", value, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightLessThanOrEqualTo(Float value) {
            addCriterion("page_height <=", value, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightIn(List<Float> values) {
            addCriterion("page_height in", values, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightNotIn(List<Float> values) {
            addCriterion("page_height not in", values, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightBetween(Float value1, Float value2) {
            addCriterion("page_height between", value1, value2, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageHeightNotBetween(Float value1, Float value2) {
            addCriterion("page_height not between", value1, value2, "pageHeight");
            return (Criteria) this;
        }

        public Criteria andPageCompCountIsNull() {
            addCriterion("page_comp_count is null");
            return (Criteria) this;
        }

        public Criteria andPageCompCountIsNotNull() {
            addCriterion("page_comp_count is not null");
            return (Criteria) this;
        }

        public Criteria andPageCompCountEqualTo(Integer value) {
            addCriterion("page_comp_count =", value, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountNotEqualTo(Integer value) {
            addCriterion("page_comp_count <>", value, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountGreaterThan(Integer value) {
            addCriterion("page_comp_count >", value, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("page_comp_count >=", value, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountLessThan(Integer value) {
            addCriterion("page_comp_count <", value, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountLessThanOrEqualTo(Integer value) {
            addCriterion("page_comp_count <=", value, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountIn(List<Integer> values) {
            addCriterion("page_comp_count in", values, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountNotIn(List<Integer> values) {
            addCriterion("page_comp_count not in", values, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountBetween(Integer value1, Integer value2) {
            addCriterion("page_comp_count between", value1, value2, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andPageCompCountNotBetween(Integer value1, Integer value2) {
            addCriterion("page_comp_count not between", value1, value2, "pageCompCount");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthIsNull() {
            addCriterion("ocr_content_length is null");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthIsNotNull() {
            addCriterion("ocr_content_length is not null");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthEqualTo(Integer value) {
            addCriterion("ocr_content_length =", value, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthNotEqualTo(Integer value) {
            addCriterion("ocr_content_length <>", value, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthGreaterThan(Integer value) {
            addCriterion("ocr_content_length >", value, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthGreaterThanOrEqualTo(Integer value) {
            addCriterion("ocr_content_length >=", value, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthLessThan(Integer value) {
            addCriterion("ocr_content_length <", value, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthLessThanOrEqualTo(Integer value) {
            addCriterion("ocr_content_length <=", value, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthIn(List<Integer> values) {
            addCriterion("ocr_content_length in", values, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthNotIn(List<Integer> values) {
            addCriterion("ocr_content_length not in", values, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthBetween(Integer value1, Integer value2) {
            addCriterion("ocr_content_length between", value1, value2, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentLengthNotBetween(Integer value1, Integer value2) {
            addCriterion("ocr_content_length not between", value1, value2, "ocrContentLength");
            return (Criteria) this;
        }

        public Criteria andOcrContentIsNull() {
            addCriterion("ocr_content is null");
            return (Criteria) this;
        }

        public Criteria andOcrContentIsNotNull() {
            addCriterion("ocr_content is not null");
            return (Criteria) this;
        }

        public Criteria andOcrContentEqualTo(String value) {
            addCriterion("ocr_content =", value, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentNotEqualTo(String value) {
            addCriterion("ocr_content <>", value, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentGreaterThan(String value) {
            addCriterion("ocr_content >", value, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentGreaterThanOrEqualTo(String value) {
            addCriterion("ocr_content >=", value, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentLessThan(String value) {
            addCriterion("ocr_content <", value, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentLessThanOrEqualTo(String value) {
            addCriterion("ocr_content <=", value, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentLike(String value) {
            addCriterion("ocr_content like", value, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentNotLike(String value) {
            addCriterion("ocr_content not like", value, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentIn(List<String> values) {
            addCriterion("ocr_content in", values, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentNotIn(List<String> values) {
            addCriterion("ocr_content not in", values, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentBetween(String value1, String value2) {
            addCriterion("ocr_content between", value1, value2, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andOcrContentNotBetween(String value1, String value2) {
            addCriterion("ocr_content not between", value1, value2, "ocrContent");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeIsNull() {
            addCriterion("page_style_type is null");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeIsNotNull() {
            addCriterion("page_style_type is not null");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeEqualTo(String value) {
            addCriterion("page_style_type =", value, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeNotEqualTo(String value) {
            addCriterion("page_style_type <>", value, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeGreaterThan(String value) {
            addCriterion("page_style_type >", value, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("page_style_type >=", value, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeLessThan(String value) {
            addCriterion("page_style_type <", value, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeLessThanOrEqualTo(String value) {
            addCriterion("page_style_type <=", value, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeLike(String value) {
            addCriterion("page_style_type like", value, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeNotLike(String value) {
            addCriterion("page_style_type not like", value, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeIn(List<String> values) {
            addCriterion("page_style_type in", values, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeNotIn(List<String> values) {
            addCriterion("page_style_type not in", values, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeBetween(String value1, String value2) {
            addCriterion("page_style_type between", value1, value2, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageStyleTypeNotBetween(String value1, String value2) {
            addCriterion("page_style_type not between", value1, value2, "pageStyleType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeIsNull() {
            addCriterion("page_feature_type is null");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeIsNotNull() {
            addCriterion("page_feature_type is not null");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeEqualTo(String value) {
            addCriterion("page_feature_type =", value, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeNotEqualTo(String value) {
            addCriterion("page_feature_type <>", value, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeGreaterThan(String value) {
            addCriterion("page_feature_type >", value, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeGreaterThanOrEqualTo(String value) {
            addCriterion("page_feature_type >=", value, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeLessThan(String value) {
            addCriterion("page_feature_type <", value, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeLessThanOrEqualTo(String value) {
            addCriterion("page_feature_type <=", value, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeLike(String value) {
            addCriterion("page_feature_type like", value, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeNotLike(String value) {
            addCriterion("page_feature_type not like", value, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeIn(List<String> values) {
            addCriterion("page_feature_type in", values, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeNotIn(List<String> values) {
            addCriterion("page_feature_type not in", values, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeBetween(String value1, String value2) {
            addCriterion("page_feature_type between", value1, value2, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFeatureTypeNotBetween(String value1, String value2) {
            addCriterion("page_feature_type not between", value1, value2, "pageFeatureType");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenIsNull() {
            addCriterion("page_form_first_screen is null");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenIsNotNull() {
            addCriterion("page_form_first_screen is not null");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenEqualTo(Integer value) {
            addCriterion("page_form_first_screen =", value, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenNotEqualTo(Integer value) {
            addCriterion("page_form_first_screen <>", value, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenGreaterThan(Integer value) {
            addCriterion("page_form_first_screen >", value, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenGreaterThanOrEqualTo(Integer value) {
            addCriterion("page_form_first_screen >=", value, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenLessThan(Integer value) {
            addCriterion("page_form_first_screen <", value, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenLessThanOrEqualTo(Integer value) {
            addCriterion("page_form_first_screen <=", value, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenIn(List<Integer> values) {
            addCriterion("page_form_first_screen in", values, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenNotIn(List<Integer> values) {
            addCriterion("page_form_first_screen not in", values, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenBetween(Integer value1, Integer value2) {
            addCriterion("page_form_first_screen between", value1, value2, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormFirstScreenNotBetween(Integer value1, Integer value2) {
            addCriterion("page_form_first_screen not between", value1, value2, "pageFormFirstScreen");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountIsNull() {
            addCriterion("page_form_input_count is null");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountIsNotNull() {
            addCriterion("page_form_input_count is not null");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountEqualTo(Integer value) {
            addCriterion("page_form_input_count =", value, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountNotEqualTo(Integer value) {
            addCriterion("page_form_input_count <>", value, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountGreaterThan(Integer value) {
            addCriterion("page_form_input_count >", value, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("page_form_input_count >=", value, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountLessThan(Integer value) {
            addCriterion("page_form_input_count <", value, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountLessThanOrEqualTo(Integer value) {
            addCriterion("page_form_input_count <=", value, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountIn(List<Integer> values) {
            addCriterion("page_form_input_count in", values, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountNotIn(List<Integer> values) {
            addCriterion("page_form_input_count not in", values, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountBetween(Integer value1, Integer value2) {
            addCriterion("page_form_input_count between", value1, value2, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormInputCountNotBetween(Integer value1, Integer value2) {
            addCriterion("page_form_input_count not between", value1, value2, "pageFormInputCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountIsNull() {
            addCriterion("page_form_select_count is null");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountIsNotNull() {
            addCriterion("page_form_select_count is not null");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountEqualTo(Integer value) {
            addCriterion("page_form_select_count =", value, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountNotEqualTo(Integer value) {
            addCriterion("page_form_select_count <>", value, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountGreaterThan(Integer value) {
            addCriterion("page_form_select_count >", value, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("page_form_select_count >=", value, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountLessThan(Integer value) {
            addCriterion("page_form_select_count <", value, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountLessThanOrEqualTo(Integer value) {
            addCriterion("page_form_select_count <=", value, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountIn(List<Integer> values) {
            addCriterion("page_form_select_count in", values, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountNotIn(List<Integer> values) {
            addCriterion("page_form_select_count not in", values, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountBetween(Integer value1, Integer value2) {
            addCriterion("page_form_select_count between", value1, value2, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormSelectCountNotBetween(Integer value1, Integer value2) {
            addCriterion("page_form_select_count not between", value1, value2, "pageFormSelectCount");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentIsNull() {
            addCriterion("page_form_percent is null");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentIsNotNull() {
            addCriterion("page_form_percent is not null");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentEqualTo(Float value) {
            addCriterion("page_form_percent =", value, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentNotEqualTo(Float value) {
            addCriterion("page_form_percent <>", value, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentGreaterThan(Float value) {
            addCriterion("page_form_percent >", value, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentGreaterThanOrEqualTo(Float value) {
            addCriterion("page_form_percent >=", value, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentLessThan(Float value) {
            addCriterion("page_form_percent <", value, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentLessThanOrEqualTo(Float value) {
            addCriterion("page_form_percent <=", value, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentIn(List<Float> values) {
            addCriterion("page_form_percent in", values, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentNotIn(List<Float> values) {
            addCriterion("page_form_percent not in", values, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentBetween(Float value1, Float value2) {
            addCriterion("page_form_percent between", value1, value2, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andPageFormPercentNotBetween(Float value1, Float value2) {
            addCriterion("page_form_percent not between", value1, value2, "pageFormPercent");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenIsNull() {
            addCriterion("cvr_button_first_screen is null");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenIsNotNull() {
            addCriterion("cvr_button_first_screen is not null");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenEqualTo(Integer value) {
            addCriterion("cvr_button_first_screen =", value, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenNotEqualTo(Integer value) {
            addCriterion("cvr_button_first_screen <>", value, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenGreaterThan(Integer value) {
            addCriterion("cvr_button_first_screen >", value, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenGreaterThanOrEqualTo(Integer value) {
            addCriterion("cvr_button_first_screen >=", value, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenLessThan(Integer value) {
            addCriterion("cvr_button_first_screen <", value, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenLessThanOrEqualTo(Integer value) {
            addCriterion("cvr_button_first_screen <=", value, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenIn(List<Integer> values) {
            addCriterion("cvr_button_first_screen in", values, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenNotIn(List<Integer> values) {
            addCriterion("cvr_button_first_screen not in", values, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenBetween(Integer value1, Integer value2) {
            addCriterion("cvr_button_first_screen between", value1, value2, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andCvrButtonFirstScreenNotBetween(Integer value1, Integer value2) {
            addCriterion("cvr_button_first_screen not between", value1, value2, "cvrButtonFirstScreen");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}