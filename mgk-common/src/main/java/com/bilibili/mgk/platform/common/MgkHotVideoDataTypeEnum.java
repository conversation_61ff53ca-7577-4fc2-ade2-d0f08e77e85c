package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotVideoDataTypeEnum
 * @author: gaoming
 * @date: 2020/11/09
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
public enum MgkHotVideoDataTypeEnum {

    YESTERDAY(0, "昨天"),
    LAST_WEEK(1, "上星期"),
    LAST_MOUTH(2, "上个月");
    @Getter
    private Integer code;
    private String desc;

    public static MgkHotVideoDataTypeEnum getByCode(Integer code) {
        for (MgkHotVideoDataTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotVideoDataTypeEnum " + code);
    }
}
