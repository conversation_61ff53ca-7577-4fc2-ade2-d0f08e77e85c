package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkFormLbsOptionPo implements Serializable {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 表单id
     */
    private Long formId;

    /**
     * 表单项id
     */
    private Long formItemId;

    /**
     * 选项码
     */
    private Long optionCode;

    /**
     * 选项类型
     */
    private String optionType;

    /**
     * 选项内容
     */
    private String optionContent;

    /**
     * 父类选项码
     */
    private Long parentOptionCode;

    /**
     * 选项层级
     */
    private Integer level;

    /**
     * 状态 0-无效 1-有效
     */
    private Integer status;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}