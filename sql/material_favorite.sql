CREATE TABLE `mgk_material_favorite`
(
    `id`                    bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `account_id`            bigint(11)      NOT NULL DEFAULT 0 COMMENT '账户id',
    `material_type`         varchar(50)  NOT NULL DEFAULT '' COMMENT '素材类型',
    `img_type`         varchar(50)  NOT NULL DEFAULT '' COMMENT '图片类型',
    `img_md5`             varchar(100)  default ''                not null comment '图片md5',
    `material_id`           varchar(100)   NOT NULL DEFAULT '' COMMENT '素材ID',
    `top1_creative_id`      varchar(100)   NOT NULL DEFAULT '' COMMENT '创意id',
    `top1_creative_title`   varchar(200)   NOT NULL DEFAULT '' COMMENT '创意标题',
    `cover_img_url`       varchar(200)   NOT NULL DEFAULT '' COMMENT '封面url',
    `extra`       varchar(1500)   NOT NULL DEFAULT '' COMMENT '额外参数',
    `avid`       bigint(20)   NOT NULL DEFAULT 0 COMMENT 'avid',
    `bvid`       varchar(50)   NOT NULL DEFAULT '' COMMENT 'bvid',
    `audit_status`        tinyint(4)   NOT NULL DEFAULT 0 COMMENT '发布状态。0未发布，1已发布',
    `ctime`                 datetime     NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`                 datetime     NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`               tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_account_type` (`account_id`, `material_type`, `img_type`),
    UNIQUE KEY `idx_account_material_id` (`account_id`, `material_id`) ,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='创意素材收藏';



