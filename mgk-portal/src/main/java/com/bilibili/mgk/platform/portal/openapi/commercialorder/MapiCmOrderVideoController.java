package com.bilibili.mgk.platform.portal.openapi.commercialorder;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoDto;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoQueryDto;
import com.bilibili.collage.api.service.ICommercialOrderService;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Pagination;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebCollageService;
import com.bilibili.mgk.platform.portal.webapi.enterprise.vo.CollageArchiveVideoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.support.Assert;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @ClassName MapiCmOrderVideoController
 * <AUTHOR>
 * @Date 2023/3/30 3:27 下午
 * @Version 1.0
 **/
@RestController
@RequestMapping("/mapi/v1/cm_order")
@Api(value = "/cm_order", description = "mapi商单视频相关")
public class MapiCmOrderVideoController extends BasicController {

    @Autowired
    private ICommercialOrderService commercialOrderService;

    @Autowired
    private WebCollageService webCollageService;

    @ApiOperation(value = "商单稿件列表")
    @RequestMapping(value = "/video/list", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Pagination<List<CollageArchiveVideoVo>>> getCmOrderArchiveList(@ApiIgnore Context context,
                                                                         @ApiParam("排序") @RequestParam(value = "orderBy", required = false, defaultValue = "pubTime desc") String orderBy,
                                                                         @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                         @ApiParam("每页大小") @RequestParam(value = "size", defaultValue = "15") Integer pageSize,
                                                                         @ApiParam("avid, 不适用avid过滤时，可不传") @RequestParam(value = "avid", required = false) Long avid
    ) {

        Assert.isTrue(pageSize <= 100, "分页参数不得大于100");
        CollageEnterpriseVideoQueryDto queryDto = CollageEnterpriseVideoQueryDto.builder()
                .accountId(context.getAccountId())
                .orderBy(orderBy)
                .page(page)
                .pageSize(pageSize)
                .avid(avid)
                .build();
        PageResult<CollageEnterpriseVideoDto> pageResult = commercialOrderService.getCommercialOrderVideos(queryDto);
        if (pageResult.getTotal() == 0 || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), Collections.emptyList()));
        }

        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(),
                webCollageService.convertVideoDtos2Vos(pageResult.getRecords())));
    }

}
