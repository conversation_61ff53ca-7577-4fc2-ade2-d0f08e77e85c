package com.bilibili.mgk.platform.portal.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;

/**
 * @ClassName WebMvcConfig
 * <AUTHOR>
 * @Date 2022/6/27 8:14 下午
 * @Version 1.0
 **/
@Configuration
public class WebMvcConfig {

    @Bean
    public FilterRegistrationBean corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig());

        FilterRegistrationBean bean = new FilterRegistrationBean(new CorsFilter(source));
        bean.setOrder(0);
        return bean;
    }

    private CorsConfiguration corsConfig() {
        //1. 添加 CORS配置信息
        CorsConfiguration config = new CorsConfiguration();
        //放行哪些原始域
        config.addAllowedOrigin("*");
        //是否发送 Cookie
        config.setAllowCredentials(true);
        //放行哪些请求方式
        config.addAllowedMethod("*");
        //放行哪些原始请求头部信息
        config.setAllowedHeaders(Arrays.asList("Content-Type,HTTP-CONSUMER-KEY,HTTP-DEVICE-TYPE,HTTP-ACCESS-TOKEN,image_hash,Location,bid-grey".split(",")));
        config.setExposedHeaders(Arrays.asList("HTTP-ACCESS-TOKEN,image_hash,Location,bid-grey".split(",")));
        return config;
    }


}
