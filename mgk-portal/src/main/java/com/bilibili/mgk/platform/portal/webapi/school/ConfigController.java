package com.bilibili.mgk.platform.portal.webapi.school;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.collage.api.dto.CategoryDto;
import com.bilibili.collage.api.dto.CreateArticleDto;
import com.bilibili.collage.api.dto.CreateCommonQuestionDto;
import com.bilibili.collage.api.dto.EditProductManualDto;
import com.bilibili.collage.api.dto.SchoolArticleDto;
import com.bilibili.collage.api.service.ISchoolConfigService;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Pagination;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CategoryVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CommercialInfoVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CreateAdvertiseStandardVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CreateArticleVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CreateCommercialInfoVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CreateCommonQuestionVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.CreateProductManualVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.EditAdvertiseGuidanceVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.EditProductManualVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.SchoolArticleVo;
import com.bilibili.mgk.platform.portal.webapi.school.vo.TitleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

@Controller
@RequestMapping("/web_api/v1/school")
@Api(value = "/school", description = "学堂首页相关")
@Slf4j
@Deprecated
@FreeLogin
public class ConfigController {

    @Autowired
    private ISchoolConfigService schoolConfigService;

    @ApiOperation(value = "新增产品说明中心配置")
    @RequestMapping(value = "/config/product_manual/create", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> createProductManual(@ApiIgnore Context context,
                                                 @Valid @RequestBody CreateProductManualVo createProductManualVo){
        return null;
    }

    @ApiOperation(value = "产品说明中心配置列表")
    @RequestMapping(value = "/config/product_manual/list", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Pagination<List<CategoryVo>>> productManualList(@ApiIgnore Context context,
                                                  @ApiParam(value = "标题搜索") @RequestParam(value = "name_like", defaultValue = "") String nameLike,
                                                  @ApiParam(value = "开始时间") @RequestParam(value = "time_from", defaultValue = "") Long timeFrom,
                                                  @ApiParam(value = "结算时间") @RequestParam(value = "time_to", defaultValue = "") Long timeTo,
                                                  @ApiParam(value = "页数") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                  @ApiParam(value = "页面大小") @RequestParam(value = "size", defaultValue = "10") Integer size){
        PageResult<CategoryDto> res = schoolConfigService.getProductManualList(nameLike,timeFrom,timeTo,page,size);
        List<CategoryVo> categoryVos = res.getRecords().stream().map(dto->convertCategoryDto2CategoryVo(dto)).collect(Collectors.toList());
        return Response.SUCCESS(
                new Pagination(page, res.getTotal(), categoryVos));
    }

    @ApiOperation(value = "修改产品说明中心配置")
    @RequestMapping(value = "/config/product_manual/edit", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> editProductManual(@ApiIgnore Context context,
                                               @Valid @RequestBody EditProductManualVo editProductManualVo){
        return Response.SUCCESS(schoolConfigService.editProductManual(JSON.parseObject(JSON.toJSONString(editProductManualVo), EditProductManualDto.class)));
    }

    @ApiOperation(value = "删除产品说明中心配置")
    @RequestMapping(value = "/config/product_manual/delete", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> deleteProductManual(@ApiIgnore Context context,
                                                 @ApiParam(value = "id") @RequestParam(value = "id", defaultValue = "") Long id){
        return null;
    }


    @ApiOperation(value = "新增行业投放指南配置")
    @RequestMapping(value = "/config/advertise_guidance/create", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> createAdvertiseGuidance(@ApiIgnore Context context,
                                                     @Valid @RequestBody CreateAdvertiseStandardVo createAdvertiseStandardVo){
        return null;
    }


    @ApiOperation(value = "新增行业投放指南配置")
    @RequestMapping(value = "/config/advertise_guidance/list", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Pagination<List<CategoryVo>>> advertiseGuidanceList(@ApiIgnore Context context,
                                                   @ApiParam(value = "标题搜索") @RequestParam(value = "name_like", defaultValue = "") String nameLike,
                                                   @ApiParam(value = "开始时间") @RequestParam(value = "time_from", defaultValue = "") Long timeFrom,
                                                   @ApiParam(value = "结算时间") @RequestParam(value = "time_to", defaultValue = "") Long timeTo,
                                                   @ApiParam(value = "页数") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                   @ApiParam(value = "页面大小") @RequestParam(value = "size", defaultValue = "10") Long size){
        return null;
    }


    @ApiOperation(value = "修改行业投放指南配置")
    @RequestMapping(value = "/config/advertise_guidance/edit", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> editAdvertiseGuidance(@ApiIgnore Context context,
                                                   @Valid @RequestBody EditAdvertiseGuidanceVo editAdvertiseGuidanceVo){
        return null;
    }

    @ApiOperation(value = "删除行业投放指南配置")
    @RequestMapping(value = "/config/advertise_guidance/delete", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> deleteAdvertiseGuidance(@ApiIgnore Context context,
                                                 @ApiParam(value = "id") @RequestParam(value = "id", defaultValue = "") Long id){
        return Response.SUCCESS(schoolConfigService.deleteAdvertiseGuidance(id));
    }

    @ApiOperation(value = "搜索类目标题")
    @RequestMapping(value = "/config/search/title", method = RequestMethod.GET)
    @ResponseBody
    @FreeLogin
    public Response<List<TitleVo>> createCommonQuestion(@ApiIgnore Context context,
                                                        @ApiParam(value = "模块类型") @RequestParam(value = "type", defaultValue = "") Byte type){
        return null;
    }


    @ApiOperation(value = "新增常见问题配置")
    @RequestMapping(value = "/config/common_question/create", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> createCommonQuestion(@ApiIgnore Context context,
                                                  @Valid @RequestBody CreateCommonQuestionVo createCommonQuestionVo) throws ServiceException {
        schoolConfigService.createCommonQuestion(JSON.parseObject(JSON.toJSONString(createCommonQuestionVo), CreateCommonQuestionDto.class));
        return Response.SUCCESS(1);
    }

    @ApiOperation(value = "常见问题配置列表")
    @RequestMapping(value = "/config/common_question/list", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> commonQuestionList(@ApiIgnore Context context,
                                                @ApiParam(value = "页数") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                @ApiParam(value = "页面大小") @RequestParam(value = "size", defaultValue = "10") Integer size){
        schoolConfigService.getCommonQuestionListForMng(page, size);
        return Response.SUCCESS(1);
    }


    @ApiOperation(value = "修改常见问题配置")
    @RequestMapping(value = "/config/common_question/edit", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> editCommonQuestion(@ApiIgnore Context context,
                                                @Valid @RequestBody CreateCommonQuestionVo createCommonQuestionVo) throws ServiceException {
        schoolConfigService.editCommonQuestion(JSON.parseObject(JSON.toJSONString(createCommonQuestionVo),CreateCommonQuestionDto.class));
        return Response.SUCCESS(1);
    }

    @ApiOperation(value = "删除常见问题配置")
    @RequestMapping(value = "/config/common_question/delete", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> deleteCommonQuestion(@ApiIgnore Context context,
                                                     @ApiParam(value = "id") @RequestParam(value = "id", defaultValue = "") Long id){
        return null;
    }


    @ApiOperation(value = "新增商业资讯配置")
    @RequestMapping(value = "/config/commercial_info/create", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> createCommercialInfo(@ApiIgnore Context context,
                                                  @Valid @RequestBody CreateCommercialInfoVo createCommercialInfoVo){
        return null;
    }

    @ApiOperation(value = "商业资讯配置列表")
    @RequestMapping(value = "/config/commercial_info/list", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<List<CommercialInfoVo>> commercialInfoList(@ApiIgnore Context context,
                                                @ApiParam(value = "页数") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                @ApiParam(value = "页面大小") @RequestParam(value = "size", defaultValue = "10") Long size){
        return null;
    }


    @ApiOperation(value = "修改商业资讯配置")
    @RequestMapping(value = "/config/commercial_info/edit", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> editCommercialInfo(@ApiIgnore Context context,
                                                @Valid @RequestBody CreateCommercialInfoVo createCommercialInfoVo){
        return null;
    }

    @ApiOperation(value = "删除商业资讯配置")
    @RequestMapping(value = "/config/commercial_info/delete", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> deleteCommercialInfo(@ApiIgnore Context context,
                                                  @ApiParam(value = "id") @RequestParam(value = "id", defaultValue = "") String id){
        return null;
    }


    @ApiOperation(value = "新增配置文章")
    @RequestMapping(value = "/config/article/create", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> createArticle(@ApiIgnore Context context, @Valid @RequestBody CreateArticleVo createArticleVo) throws ServiceException {
        CreateArticleDto createArticleDto = JSON.parseObject(JSON.toJSONString(createArticleVo),CreateArticleDto.class);
        return Response.SUCCESS(schoolConfigService.createArticle(createArticleDto));
    }

    @ApiOperation(value = "文章列表")
    @RequestMapping(value = "/config/article/list", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Pagination<SchoolArticleVo>> ArticleList(@ApiIgnore Context context, @ApiParam(value = "父类目id") @RequestParam(value = "parent_id", defaultValue = "") Long parentId){
        PageResult<SchoolArticleDto> schoolArticleDtoPageResult = schoolConfigService.articleList(parentId, (byte)2);
        List<SchoolArticleVo> schoolArticleVos = JSON.parseArray(JSON.toJSONString(schoolArticleDtoPageResult.getRecords()),SchoolArticleVo.class);
        return Response.SUCCESS(
                new Pagination(1, schoolArticleDtoPageResult.getTotal(), schoolArticleVos));
    }


    @ApiOperation(value = "修改文章")
    @RequestMapping(value = "/config/article/edit", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> editArticle(@ApiIgnore Context context, @Valid @RequestBody CreateArticleVo createArticleVo) throws ServiceException {
        CreateArticleDto createArticleDto = JSON.parseObject(JSON.toJSONString(createArticleVo),CreateArticleDto.class);
        return Response.SUCCESS(schoolConfigService.editArticle(createArticleDto));
    }

    @ApiOperation(value = "删除文章")
    @RequestMapping(value = "/config/article/delete", method = RequestMethod.POST)
    @ResponseBody
    @FreeLogin
    public Response<Integer> deleteArticle(@ApiIgnore Context context,
                                                  @ApiParam(value = "文章id") @RequestParam(value = "article_id", defaultValue = "") Long articleId){
        return Response.SUCCESS(schoolConfigService.deleteArticle(articleId));
    }

    private CategoryVo convertCategoryDto2CategoryVo(CategoryDto dto) {
        CategoryVo categoryVo = new CategoryVo();
        categoryVo.setId(dto.getId());
        categoryVo.setImage(dto.getImage());
        categoryVo.setIndustryName(categoryVo.getIndustryName());
        categoryVo.setParentId(dto.getParentId());
        categoryVo.setOrderNum(dto.getOrderNum());
        categoryVo.setParentTitle(dto.getParentTitle());
        categoryVo.setTitle(dto.getTitle());
        categoryVo.setType(dto.getType());
        categoryVo.setCtime(dto.getCtime());
        categoryVo.setMtime(dto.getMtime());
        categoryVo.setSchoolArticleVoList(JSON.parseArray(JSON.toJSONString(dto.getSchoolArticleDtoList()),SchoolArticleVo.class));
        return categoryVo;
    }
}
