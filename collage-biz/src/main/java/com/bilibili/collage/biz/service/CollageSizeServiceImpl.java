package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.collage.api.dto.CollageSizeDto;
import com.bilibili.collage.api.dto.QueryCollageSizeDto;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.collage.biz.dao.MgkCollageSizeDao;
import com.bilibili.collage.biz.po.MgkCollageSizePo;
import com.bilibili.collage.biz.po.MgkCollageSizePoExample;
import com.bilibili.mgk.platform.common.CollagePatternEditionEnum;
import com.bilibili.mgk.platform.common.collage.CollageSizeStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/19
 * 尺寸
 **/
@Service
@Slf4j
public class CollageSizeServiceImpl implements ICollageSizeService {

    @Autowired
    private CollageValidService collageValidService;

    @Autowired
    private MgkCollageSizeDao mgkCollageSizeDao;

    @Override
    public PageResult<CollageSizeDto> queryCollageSizeByPage(QueryCollageSizeDto queryParam) {
        MgkCollageSizePoExample example = getMgkCollageSizePoExample(queryParam);
        Long total = mgkCollageSizeDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        List<CollageSizeDto> list = queryCollageSize(queryParam);
        if (CollectionUtils.isEmpty(list)) {
            return PageResult.emptyPageResult();
        }
        return PageResult.<CollageSizeDto>builder()
                .total(total.intValue())
                .records(list)
                .build();
    }

    @Override
    public List<CollageSizeDto> queryCollageSize(QueryCollageSizeDto queryParam) {

        MgkCollageSizePoExample example = getMgkCollageSizePoExample(queryParam);
        List<MgkCollageSizePo> pos = mgkCollageSizeDao.selectByExample(example);
        return pos.stream().map(this::convertSizePo).collect(Collectors.toList());
    }

    @Override
    public CollageSizeDto getCollageSizeById(Integer sizeId) {
        Assert.notNull(sizeId, "尺寸id不能为空");
        MgkCollageSizePo po = mgkCollageSizeDao.selectByPrimaryKey(sizeId);
        return convertSizePo(po);
    }

    @Override
    public List<CollageSizeDto> getCollageSizeByIds(List<Integer> sizeIds) {
        if (CollectionUtils.isEmpty(sizeIds)) {
            return Collections.emptyList();
        }
        return queryCollageSize(QueryCollageSizeDto.builder().sizeIds(sizeIds).build());
    }

    @Override
    public void createCollageSize(CollageSizeDto collageSizeDto) {

        collageValidService.validCeateCollageSize(collageSizeDto);
        MgkCollageSizePo collageSizePo = new MgkCollageSizePo();
        collageSizePo.setWidth(collageSizeDto.getWidth());
        collageSizePo.setHeight(collageSizeDto.getHeight());
        collageSizePo.setSketch(collageSizeDto.getSketch());
        collageSizePo.setDes(collageSizeDto.getDesc());
        collageSizePo.setEdition(collageSizeDto.getEdition());
        mgkCollageSizeDao.insertSelective(collageSizePo);
    }

    @Override
    public void updateCollageSize(CollageSizeDto collageSizeDto) {

        collageValidService.validUpdateCollageSize(collageSizeDto);
        MgkCollageSizePo collageSizePo = new MgkCollageSizePo();
        collageSizePo.setId(collageSizeDto.getId());
        collageSizePo.setWidth(collageSizeDto.getWidth());
        collageSizePo.setHeight(collageSizeDto.getHeight());
        collageSizePo.setSketch(collageSizeDto.getSketch());
        collageSizePo.setDes(collageSizeDto.getDesc());
        mgkCollageSizeDao.updateByPrimaryKeySelective(collageSizePo);
    }

    @Override
    public void deleteCollageSize(Integer sizeId) {

    }

    @Override
    public void updateStatus(Operator operator, Integer sizeId, Integer status) {
        collageValidService.validUpdateSizeStatus(sizeId, status);
        MgkCollageSizePo po = new MgkCollageSizePo();
        po.setId(sizeId);
        po.setStatus(status);
        mgkCollageSizeDao.updateByPrimaryKeySelective(po);
    }

    @Override
    public List<CollageSizeDto> getCollageSizeByWH(Integer width, Integer height) {
        MgkCollageSizePoExample example = new MgkCollageSizePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andEditionEqualTo(CollagePatternEditionEnum.NEW_PATTERN_EDITION.getCode())
                .andStatusEqualTo(CollageSizeStatusEnum.USING.getCode())
                .andWidthEqualTo(width)
                .andHeightEqualTo(height);
        List<MgkCollageSizePo> pos = mgkCollageSizeDao.selectByExample(example);
        return convertSizePos2Dtos(pos);
    }

    private List<CollageSizeDto> convertSizePos2Dtos(List<MgkCollageSizePo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertSizePo2Dto).collect(Collectors.toList());
    }

    private CollageSizeDto convertSizePo2Dto(MgkCollageSizePo po) {
        CollageSizeDto dto = CollageSizeDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private MgkCollageSizePoExample getMgkCollageSizePoExample(QueryCollageSizeDto sizeDto) {
        MgkCollageSizePoExample example = new MgkCollageSizePoExample();
        MgkCollageSizePoExample.Criteria criteria = example.or();
        ObjectUtils.setObject(sizeDto::getStatus, criteria::andStatusEqualTo);
        ObjectUtils.setObject(sizeDto::getSizeId, criteria::andIdEqualTo);
        ObjectUtils.setObject(sizeDto::getSizeIds, criteria::andIdIn);
        ObjectUtils.setObject(sizeDto::getEdition, criteria::andEditionEqualTo);
        ObjectUtils.setObject(sizeDto::getSizeType, criteria::andSizeTypeEqualTo);
        Page page = sizeDto.getPageInfo();
        if (page != null) {
            example.setLimit(page.getLimit());
            example.setOffset(page.getOffset());
        }
        return example;
    }

    private CollageSizeDto convertSizePo(MgkCollageSizePo po) {
        return CollageSizeDto.builder()
                .id(po.getId())
                .width(po.getWidth())
                .height(po.getHeight())
                .sketch(po.getSketch())
                .desc(po.getDes())
                .status(po.getStatus())
                .isDeleted(po.getIsDeleted())
                .build();
    }
}
