package com.bilibili.mgk.platform.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.launch.dto.BizCreativeMappingReplyDto;
import com.bilibili.adp.launch.api.soa.ISoaLauBizCreativeMappingService;
import com.bilibili.adp.passport.api.dto.*;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.mgk.platform.api.dynamic.dto.*;
import com.bilibili.mgk.platform.api.video_library.service.IVideoLibraryService;
import com.bilibili.mgk.platform.biz.ad.dao.LauBizCreativeMappingDao;
import com.bilibili.mgk.platform.biz.ad.po.LauBizCreativeMappingPo;
import com.bilibili.mgk.platform.biz.ad.po.LauBizCreativeMappingPoExample;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.common.MgkConstants.MGK_LANDING_PAGE_PAGE_ID_BIZ_ID_REDIS_KEY;

/**
 * @file: MgkDynamicServiceDelegate
 * @author: gaoming
 * @date: 2021/05/17
 * @version: 1.0
 * @description: https://info.bilibili.co/pages/viewpage.action?pageId=4552703
 **/
@Service
public class MgkDynamicServiceDelegate {

    @Autowired
    private IPassportService passportService;

    @Autowired
    private RedisTemplate<String, String> stringRedisTemplate;

    @Autowired
    private ISoaLauBizCreativeMappingService soaLauBizCreativeMappingService;

    @Autowired
    private IVideoLibraryService videoLibraryService;

    @Autowired
    private LauBizCreativeMappingDao lauBizCreativeMappingDao;

    public MgkDynamicLikeReplyDto dynamicLike(MgkDynamicLikeReqDto reqDto) throws ServiceException {

        ThumbUpLikeReplyDto thumbUpLikeReplyDto = null;

        if (Utils.isPositive(reqDto.getMid())) {
            thumbUpLikeReplyDto = passportService.thumbUpLike(
                    ThumbUpLikeReqDto.builder()
                            .business(MgkConstants.MGK_DYNAMIC_BUSINESS)
                            .mid(reqDto.getMid())
                            .upMid(0L)
                            .originId(0L)
                            .messageId(reqDto.getBizId().longValue())
                            .type(reqDto.getType())
                            .ip(reqDto.getIp())
                            .withStat(Boolean.TRUE)
                            .mobiApp(reqDto.getMobiApp())
                            .platform(reqDto.getPlatform())
                            .device(reqDto.getDevice())
                            .build());


        } else if (!Strings.isNullOrEmpty(reqDto.getBuvid())) {
            thumbUpLikeReplyDto = passportService.thumbUpLikeNoLogin(
                    ThumbUpLikeNoLoginReqDto.builder()
                            .business(MgkConstants.MGK_DYNAMIC_BUSINESS)
                            .buvid(reqDto.getBuvid())
                            .upMid(0L)
                            .originId(0L)
                            .messageId(reqDto.getBizId().longValue())
                            .type(reqDto.getType())
                            .ip(reqDto.getIp())
                            .withStat(Boolean.TRUE)
                            .mobiApp(reqDto.getMobiApp())
                            .platform(reqDto.getPlatform())
                            .device(reqDto.getDevice())
                            .build());
        }

        if (thumbUpLikeReplyDto == null) {
            return null;
        }

        return MgkDynamicLikeReplyDto.builder()
                .originId(thumbUpLikeReplyDto.getOriginId())
                .messageId(thumbUpLikeReplyDto.getMessageId())
                .likeNumber(thumbUpLikeReplyDto.getLikeNumber())
                .dislikeNumber(thumbUpLikeReplyDto.getDislikeNumber())
                .build();


    }

    public MgkDynamicLikeStatsReplyDto dynamicLikeStats(MgkDynamicLikeStatsReqDto reqDto) throws ServiceException {

        if (Utils.isPositive(reqDto.getMid())) {
            return getDynamicLikeStatsLogin(reqDto);
        } else if (!Strings.isNullOrEmpty(reqDto.getBuvid())) {
            return getDynamicLikeStatsNoLogin(reqDto);
        }
        return null;
    }

    private MgkDynamicLikeStatsReplyDto getDynamicLikeStatsNoLogin(MgkDynamicLikeStatsReqDto reqDto) throws ServiceException {

        ThumbUpLikeStatsReplyDto likeStats = passportService.getThumbUpLikeStats(ThumbUpLikeStatsReqDto.builder()
                .business(MgkConstants.MGK_DYNAMIC_BUSINESS)
                .originId(0L)
                .messageIds(Lists.newArrayList(reqDto.getBizId()))
                .build());

        ThumbUpHasLikeNoLoginReplyDto hasLike = passportService.getThumbUpHasLikeNoLogin(ThumbUpHasLikeNoLoginReqDto.builder()
                .business(MgkConstants.MGK_DYNAMIC_BUSINESS)
                .messageIds(Lists.newArrayList(reqDto.getBizId()))
                .buvid(reqDto.getBuvid())
                .build());

        if (likeStats == null) {
            return null;
        }

        return MgkDynamicLikeStatsReplyDto.builder()
                .bizId(CollectionUtils.isEmpty(likeStats.getMessageIds()) ? reqDto.getBizId() : likeStats.getMessageIds().get(0))
                .likeNumber(likeStats.getLikeNumber())
                .dislikeNumber(likeStats.getDislikeNumber())
                .hasLike(hasLike == null ? 0 : hasLike.getHasLike())
                .hasDislike(hasLike == null ? 0 : hasLike.getHasDislike())
                .build();
    }

    private MgkDynamicLikeStatsReplyDto getDynamicLikeStatsLogin(MgkDynamicLikeStatsReqDto reqDto) throws ServiceException {
        ThumbUpLikeStatsReplyDto likeStats = passportService.getThumbUpLikeStats(ThumbUpLikeStatsReqDto.builder()
                .business(MgkConstants.MGK_DYNAMIC_BUSINESS)
                .originId(0L)
                .messageIds(Lists.newArrayList(reqDto.getBizId()))
                .build());

        ThumbUpHasLikeReplyDto hasLike = passportService.getThumbUpHasLike(ThumbUpHasLikeReqDto.builder()
                .business(MgkConstants.MGK_DYNAMIC_BUSINESS)
                .messageIds(Lists.newArrayList(reqDto.getBizId()))
                .mid(reqDto.getMid())
                .build());

        if (likeStats == null) {
            return null;
        }


        return MgkDynamicLikeStatsReplyDto.builder()
                .bizId(CollectionUtils.isEmpty(likeStats.getMessageIds()) ? reqDto.getBizId() : likeStats.getMessageIds().get(0))
                .likeNumber(likeStats.getLikeNumber())
                .dislikeNumber(likeStats.getDislikeNumber())
                .hasLike(hasLike == null ? 0 : hasLike.getHasLike())
                .hasDislike(hasLike == null ? 0 : hasLike.getHasDislike())
                .build();
    }

    public MgkDynamicCreativeBizReplyDto getDynamicBizByCreativeIdFromRedis(Integer creativeId) {
        String dynamicCreativeBizKey = MgkConstants.DYNAMIC_CREATIVE_BIZ_KEY + creativeId;
        String dynamicCreativeBizString = stringRedisTemplate.opsForValue().get(dynamicCreativeBizKey);
        if (Strings.isNullOrEmpty(dynamicCreativeBizString)) {
            return null;
        }
        return JSONObject.parseObject(dynamicCreativeBizString, MgkDynamicCreativeBizReplyDto.class);
    }

    /**
     * 分批写入redis
     */
    public void refreshDynamicBizMappingInRedis() {

        LauBizCreativeMappingPoExample example = new LauBizCreativeMappingPoExample();
        example.or();
        example.setOrderByClause("id desc");
        example.setLimit(1);
        List<LauBizCreativeMappingPo> lauBizCreativeMappingPos = lauBizCreativeMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauBizCreativeMappingPos)) {
            return;
        }
        Integer maxId = lauBizCreativeMappingPos.get(0).getId();

        if (!Utils.isPositive(maxId)) {
            return;
        }

        for (int i = 1; i < maxId + 1; i += 100) {
            List<BizCreativeMappingReplyDto> dtos = getCreativeBizMappingFromIdRange(i, i + 100);
            setCreativeId2BizIdMapping(dtos);
        }

//        List<Integer> allCreativeIds = soaLauBizCreativeMappingService.getCreativeIds();
//        if (CollectionUtils.isEmpty(allCreativeIds)) {
//            return;
//        }
//        List<List<Integer>> partitionCreativeIds = Lists.partition(allCreativeIds, MgkConstants.DYNAMIC_CREATIVE_BIZ_PARTITION_NUM);
//
//        for (List<Integer> creativeIds : partitionCreativeIds) {
//            List<BizCreativeMappingReplyDto> dtos = soaLauBizCreativeMappingService.getBizCreativeMappingReplyDto(new ArrayList<>(creativeIds));
//            setCreativeId2BizIdMapping(dtos);
//        }
    }

    private List<BizCreativeMappingReplyDto> getCreativeBizMappingFromIdRange(int fromId, int toId) {
        List<LauBizCreativeMappingPo> pos = getCreativeBizMappingFromPoIdRange(fromId, toId);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(po -> {
            return BizCreativeMappingReplyDto.builder()
                    .bizId(po.getBizId())
                    .creativeId(po.getCreativeId())
                    .build();
        }).collect(Collectors.toList());
    }

    private List<LauBizCreativeMappingPo> getCreativeBizMappingFromPoIdRange(int fromId, int toId) {
        LauBizCreativeMappingPoExample example = new LauBizCreativeMappingPoExample();
        example.or().andIdBetween(fromId, toId);
        return lauBizCreativeMappingDao.selectByExample(example);
    }

    private void setCreativeId2BizIdMapping(List<BizCreativeMappingReplyDto> dtos) {
        dtos.forEach(dto -> {
            String value = convertReplyDto2DynamicDto(dto);
            stringRedisTemplate.opsForValue().set(MgkConstants.DYNAMIC_CREATIVE_BIZ_KEY + dto.getCreativeId(), value, MgkConstants.DYNAMIC_CREATIVE_BIZ_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);
        });
    }

    private String convertReplyDto2DynamicDto(BizCreativeMappingReplyDto dto) {
        return JSON.toJSONString(MgkDynamicCreativeBizReplyDto.builder()
                .creativeId(dto.getCreativeId())
                .bizId(dto.getBizId())
                .build());
    }

    public MgkDynamicLikeStatsReplyDto getDynamicBizStats(Long pageId, Long mid, String buvid) throws ServiceException {
//        Integer bizId = videoLibraryService.getBizIdByPageId(pageId);
        String bizId = stringRedisTemplate.opsForValue().get(MGK_LANDING_PAGE_PAGE_ID_BIZ_ID_REDIS_KEY + pageId);
        if (Strings.isNullOrEmpty(bizId)) {
            return null;
        }
        return dynamicLikeStats(MgkDynamicLikeStatsReqDto.builder()
                .bizId(Long.parseLong(bizId))
                .mid(mid)
                .buvid(buvid)
                .build());
    }
}
