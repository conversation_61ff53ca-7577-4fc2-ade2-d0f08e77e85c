package com.bilibili.collage.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CategoryDto implements Serializable{

    private static final long serialVersionUID = 0L;

    private Long id;

    private Byte type;

    private String title;

    private String image;

    private String parentTitle;

    private Long parentId;

    private Integer orderNum;

    private Integer level;

    private String industryName;

    private String ctime;

    private String mtime;

    private List<CategoryDto> childCategory;

    private List<SchoolArticleDto> schoolArticleDtoList;
}
