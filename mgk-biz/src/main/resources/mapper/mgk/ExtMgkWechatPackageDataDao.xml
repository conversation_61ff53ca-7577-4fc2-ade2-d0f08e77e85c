<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.ext.ExtMgkWechatPackageDataDao">
    <resultMap id="ExtAccountData" type="com.bilibili.mgk.platform.biz.po.ExtMgkWechatAccountDataPo">
        <result column="wechat_account_id" jdbcType="INTEGER" property="wechatAccountId"/>
        <result column="data_count" jdbcType="INTEGER" property="dataCount"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="recent_submit_time" jdbcType="TIMESTAMP" property="recentSubmitTime"/>
    </resultMap>
    <resultMap id="ExtPackageData" type="com.bilibili.mgk.platform.biz.po.ExtMgkWechatPackageDataPo">
        <result column="wechat_package_id" jdbcType="INTEGER" property="wechatPackageId"/>
        <result column="data_count" jdbcType="INTEGER" property="dataCount"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="recent_submit_time" jdbcType="TIMESTAMP" property="recentSubmitTime"/>
    </resultMap>
    <resultMap id="ExtWorkWxData" type="com.bilibili.mgk.platform.biz.po.ExtMgkWorkWechatDataPo">
        <result column="link_data_id" jdbcType="BIGINT" property="linkDataId"/>
        <result column="data_count" jdbcType="INTEGER" property="dataCount"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="recent_submit_time" jdbcType="TIMESTAMP" property="recentSubmitTime"/>
    </resultMap>

    <select id="getWechatAccountDataList" resultMap="ExtAccountData">
        SELECT wechat_account_id, count(1) as data_count, data_type, max(ctime) AS recent_submit_time
        FROM mgk_wechat_package_data
        WHERE is_deleted = 0
        AND is_cheat = 0
        AND wechat_account_id in
        <foreach item="wechatAccountId" index="index" collection="wechatAccountIds" open="(" separator="," close=")">
            #{wechatAccountId}
        </foreach>
        GROUP BY wechat_account_id, data_type;
    </select>

    <select id="getWechatAccountDataListWithoutCheat" resultMap="ExtAccountData">
        SELECT wechat_account_id, count(1) as data_count, data_type, max(ctime) AS recent_submit_time
        FROM mgk_wechat_package_data
        WHERE is_deleted = 0
        AND wechat_account_id in
        <foreach item="wechatAccountId" index="index" collection="wechatAccountIds" open="(" separator="," close=")">
            #{wechatAccountId}
        </foreach>
        GROUP BY wechat_account_id, data_type;
    </select>

    <select id="getWorkWechatDataList" resultMap="ExtWorkWxData">
        SELECT link_data_id, count(1) as data_count, data_type, max(ctime) AS recent_submit_time
        FROM mgk_wechat_package_data
        WHERE is_deleted = 0
        <if test="startTime != null">
            and mtime <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and mtime <![CDATA[<=]]> #{endTime}
        </if>
        AND is_cheat = 0
        AND link_data_id in
        <foreach item="linkDataId" index="index" collection="linkDataIds" open="(" separator="," close=")">
            #{linkDataId}
        </foreach>
        GROUP BY link_data_id, data_type;
    </select>

    <select id="getWorkWechatDataListWithoutCheat" resultMap="ExtWorkWxData">
        SELECT link_data_id, count(1) as data_count, data_type, max(ctime) AS recent_submit_time
        FROM mgk_wechat_package_data
        WHERE is_deleted = 0
        <if test="startTime != null">
            and mtime <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and mtime <![CDATA[<=]]> #{endTime}
        </if>
        AND link_data_id in
        <foreach item="linkDataId" index="index" collection="linkDataIds" open="(" separator="," close=")">
            #{linkDataId}
        </foreach>
        GROUP BY link_data_id, data_type;
    </select>

    <select id="getWechatPackageDataList" resultMap="ExtPackageData">
        SELECT wechat_package_id, count(1) as data_count, data_type, max(ctime) AS recent_submit_time
        FROM mgk_wechat_package_data
        WHERE is_deleted = 0
        AND is_cheat = 0
        AND wechat_package_id in
        <foreach item="wechatPackageId" index="index" collection="wechatPackageIds" open="(" separator="," close=")">
            #{wechatPackageId}
        </foreach>
        GROUP BY wechat_package_id, data_type;
    </select>
</mapper>