package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: FormItemSubTypeEnum
 * @author: gaoming
 * @date: 2021/07/17
 * @version: 1.0
 * @description: 表单项子类型
 **/

@Getter
@AllArgsConstructor
public enum FormItemSubTypeEnum {

    /**
     * 默认
     */
    DEFAULT(0, "默认"),

    /**
     * 姓名
     */
    NAME(1, "姓名");

    private final Integer code;

    private final String desc;

    public static FormItemSubTypeEnum getByCode(int code) {
        for (FormItemSubTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code FormItemSubTypeEnum " + code);
    }
}
