package com.bilibili.mgk.platform.api.user.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSensitive  {

    private String mid;

    //绑定手机号
    private String tel;

    //用户绑定的邮箱
    private String email;

    //国家id
    private String cid;

    //国家名称
    private String cname;


}
