package com.bilibili.mgk.material.center.service.bluelink.dto;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DynamicBluelinkResolveContentCreateReqDTO implements SnakeCaseBody {

    ////////////////

    private String rawText;

    ///////////////////以下为蓝链的正常解析内容////////////////////////////////
    //////////////////////////

//    @ApiModelProperty(value = "普通链接文案")
//    private String generalCommentText;

    @ApiModelProperty(value = "转化链文案")
    private String conversionUrlText;

    @ApiModelProperty(value = "转化组件类型2-线索4-游戏6-应用9-商品")
    private Integer componentType;


    @ApiModelProperty(value = "应用子类型, 1-应用下载, 2-应用唤起")
    private Integer appSubType;

    @ApiModelProperty(value = "转化链链接(兜底链接)类型，1-三方落地页 5-高能建站落地页")
    private Integer conversionUrlType;

    @ApiModelProperty(value = "转化链链接(兜底链接)高能建站落地页page_id, 针对type = 2")
    private Long conversionUrlPageId;

    @ApiModelProperty(value = "转化链连接(兜底链接)")
    private String conversionUrl;

    @ApiModelProperty(value = "ios链接类型，1-三方落地页 2-高能建站落地页")
    private Integer iosUrlType;

    @ApiModelProperty(value = "ios链接类型高能建站落地页page_id, 针对type = 2")
    private Long iosUrlPageId;

    @ApiModelProperty(value = "ios链接")
    private String iosUrl;

    @ApiModelProperty(value = "android链接类型，1-三方落地页 2-高能建站落地页")
    private Integer androidUrlType;

    @ApiModelProperty(value = "android链接高能建站落地页page_id, 针对type = 2")
    private Long androidUrlPageId;

    @ApiModelProperty(value = "android链接")
    private String androidUrl;

    @ApiModelProperty(value = "游戏id")
    private Integer gameBaseId;

    @ApiModelProperty(value = "ios app 包id")
    private Integer iosAppPackageId;

    @ApiModelProperty(value = "android app 包id")
    private Integer androidAppPackageId;

    @ApiModelProperty(value = "统一设置唤起链接(android和ios同一个)")
    private String schemaUrl;

    @ApiModelProperty(value = "ios 唤起链接")
    private String iosSchemaUrl;

    @ApiModelProperty(value = "android 唤起链接")
    private String androidSchemaUrl;

    @ApiModelProperty(value = "文案位置 0-转化链在前 1-评论在前 2-居中")
    private Integer textLocation;

    @ApiModelProperty(value = "资质")
    private List<Integer> qualificationIds;

    //以下两个字段仅在componentType等于1时生效
    //https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002893909
    @ApiModelProperty(value = "线索类型 0-落地页 1-评论浮层表单 2-微信浮层")
    private Integer clueType;

    @ApiModelProperty(value = "线索数据 目前可以存储:评论浮层表单id、微信浮层微信包id、微信小游戏id")
    private String clueData;

    @ApiModelProperty("是否广告包: 0-联运包, 1-广告包")
    private Integer subPkg;

    @ApiModelProperty("是否是mapi请求")
    private Boolean isMapiRequest;

    @ApiModelProperty("展示监控")
    private String customizedImpUrl;

    @ApiModelProperty("点击监控")
    private String customizedClickUrl;

    @ApiModelProperty("商品id")
    private Long productId;

    private String productShortUrl;

    @ApiModelProperty("普通链接文案扩展")
    private String generalCommentTextExt;

//
//    @ApiModelProperty("商品id列表")
//    private List<Long> productIds;

    @ApiModelProperty("是否安卓应用商店直投: 0-不直投, 1-直投")
    private Integer isAndroidAppDirect;

//    @ApiModelProperty(value = "avids")
//    private List<Long> avids;


    @ApiModelProperty("是否是覆盖 0-新增 1-覆盖")
    private Integer isCover;

    private String autoFillText;

    private String autoFillLink;


    @ApiModelProperty
    private Integer bizCode;

    @ApiModelProperty
    private Integer contactType;

//    @ApiModelProperty("仅用于简单预览的蓝链信息，如果是比较复杂的分端的渲染，那么使用具体的字段")
//    public List<DynamicContentSimpleItem> getSimpleContentForPreview() {
//        return Try.of(() -> JsonUtil.readValue(dynamicContent, new TypeReference<List<DynamicContentSimpleItem>>() {
//        })).getOrElse(new ArrayList<>());
//
//    }

//    public void fillingMandatoryBuildInfo(BluelinkConversionBuildResult result) {
//
//        this.setCustomerId(result.getCustomerId())
//                .setAgentId(result.getAgentId())
//                .setCampaignId(result.getCampaignId())
//                .setUnitId(result.getUnitId())
//                .setCreativeId(result.getCreativeIdsList().get(0));
//
//        if (result.getConvLink().hasConversionUrlType()) {
//            this.setConversionUrlType(result.getConvLink().getConversionUrlType().getValue());
//        }
//        if (result.getConvLink().hasConversionUrlPageId()) {
//            this.setConversionUrlPageId(result.getConvLink().getConversionUrlPageId().getValue());
//        }
//
//        this.setConversionUrl(result.getConvLink().getConversionUrl());
//        this.setIosUrl(result.getConvLink().getIosUrl());
//        this.setAndroidUrl(result.getConvLink().getAndroidUrl());
//
//        if (result.getConvLink().hasConversionUrlText()) {
//            this.setConversionUrlText(result.getConvLink().getConversionUrlText().getValue());
//        }
//
//        if (result.getConvLink().hasIosUrlType()) {
//            this.setIosUrlType(result.getConvLink().getIosUrlType().getValue());
//        }
//        if (result.getConvLink().hasIosUrlPageId()) {
//            this.setIosUrlPageId(result.getConvLink().getIosUrlPageId().getValue());
//        }
//
//        if (result.getConvLink().hasAndroidUrlType()) {
//            this.setAndroidUrlType(result.getConvLink().getAndroidUrlType().getValue());
//        }
//        if (result.getConvLink().hasAndroidUrlPageId()) {
//            this.setAndroidUrlPageId(result.getConvLink().getAndroidUrlPageId().getValue());
//        }
//
//        // TODO privacyText ->  AutoFillText
//        // TODO privacyUrl-> autofillLink
//
//        this.setAutoFillText(result.getConvLink().getPrivacyText());
//        this.setAutoFillLink(result.getConvLink().getPrivacyUrl());
//
//
//    }
}
