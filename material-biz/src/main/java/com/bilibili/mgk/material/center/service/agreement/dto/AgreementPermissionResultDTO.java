package com.bilibili.mgk.material.center.service.agreement.dto;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/3
 */
@Data
@Accessors(chain = true)
public class AgreementPermissionResultDTO implements SnakeCaseBody {


    private Long agreementId;

    private String permissionKey;

    private Boolean agreed;


}
