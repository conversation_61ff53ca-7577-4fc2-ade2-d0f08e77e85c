package com.bilibili.collage.api.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.CollageFontLibraryDto;
import com.bilibili.collage.api.dto.QueryCollageFontLibraryDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/19
 * 字体库
 **/
public interface IFontLibraryService {

    PageResult<CollageFontLibraryDto> queryFontLibraryByPage(QueryCollageFontLibraryDto queryParam);

    List<CollageFontLibraryDto> queryFontLibrary(QueryCollageFontLibraryDto queryParam);

    List<CollageFontLibraryDto> getFontLibraryByName(String name);

    CollageFontLibraryDto getFontLibraryById(Integer fontId);

    List<CollageFontLibraryDto> getFontLibraryByIds (List<Integer> fontIds);

    Map<Integer, CollageFontLibraryDto> getFontLibraryMapByIds (List<Integer> fontIds);

    void createFontLibrary(CollageFontLibraryDto fontLibraryDto);

    void updateFontLibrary(CollageFontLibraryDto fontLibraryDto);

    void deleteFontLibrary(Integer fontId);

    void updateStatus(Operator operator, Integer fontId, Integer status);
}
