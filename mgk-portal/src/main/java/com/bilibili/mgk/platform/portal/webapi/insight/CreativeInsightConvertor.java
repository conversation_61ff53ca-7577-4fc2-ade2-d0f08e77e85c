package com.bilibili.mgk.platform.portal.webapi.insight;

import com.bilibili.mgk.material.center.service.creative.model.CreativeInsight;
import com.bilibili.mgk.material.center.service.creative.model.InefficientCreative;
import com.bilibili.mgk.platform.portal.webapi.insight.vo.InsightDetailVo;
import com.bilibili.mgk.platform.portal.webapi.insight.vo.LowEfficientCreativeVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/6
 */
@Mapper
public interface CreativeInsightConvertor {

    CreativeInsightConvertor convertor = Mappers.getMapper(CreativeInsightConvertor.class);


    @Mapping(source = "cost", target = "cost7Days", numberFormat = "#0.00")
    @Mapping(source = "convNum", target = "convert7Days")
    @Mapping(source = "cpa", target = "cpa7Days", numberFormat = "#0.00")
    LowEfficientCreativeVo toVo(InefficientCreative creative);


    @Mapping(source = "materialDeliveryCost", target = "materialDeliveryCost", numberFormat = "#0.00")
    @Mapping(source = "materialInefficientCost", target = "materialInefficientCost", numberFormat = "#0.00")
    @Mapping(source = "materialDeliveryAverageCost", target = "materialDeliveryAverageCost", numberFormat = "#0.00")
    @Mapping(source = "materialInefficientAverageCost", target = "materialInefficientAverageCost", numberFormat = "#0"
            + ".00")
    @Mapping(source = "materialEfficientAverageCost", target = "materialEfficientAverageCost", numberFormat = "#0.00")
    @Mapping(source = "materialEfficientCost", target = "materialEfficientCost", numberFormat = "#0.00")
    InsightDetailVo toVo(CreativeInsight detail);


}
