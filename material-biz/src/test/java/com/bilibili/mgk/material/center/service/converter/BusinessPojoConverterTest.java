package com.bilibili.mgk.material.center.service.converter;

import com.bilibili.mgk.material.center.service.creative.model.CreativeMaterial;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDtoV2;
import java.time.LocalDateTime;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/27
 */
public class BusinessPojoConverterTest {


    @Test
    public void testConvert() {

        HotAdsDtoV2 rsp = BusinessPojoConverter.converter.toMngHotAdsDto(
                new CreativeMaterial()
                        .setTop1CreativeId("id111")
                        .setTop1CreativeTitle("tiltle")
                        .setCreativeCtime(LocalDateTime.now())
                        .setCategoryFirstName("first_name")
        );

        System.out.println(rsp);

    }

}