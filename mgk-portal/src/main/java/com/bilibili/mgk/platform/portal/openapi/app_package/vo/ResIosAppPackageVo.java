package com.bilibili.mgk.platform.portal.openapi.app_package.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @file: ResIosAppPackageVo
 * @author: gaoming
 * @date: 2021/11/25
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ResIosAppPackageVo {

    @ApiModelProperty(value = "应用包id")
    private Integer id;

    @ApiModelProperty(value = "应用名称")
    private String app_name;

    @ApiModelProperty(value = "图标url")
    private String icon_url;

    @ApiModelProperty(value = "简介")
    private String sub_title;

    @ApiModelProperty(value = "描述信息")
    private String description;

    @ApiModelProperty(value = "截屏图片地址")
    private List<String> screenshot_urls;

    @ApiModelProperty(value = "应用包原始下载链接")
    private String url;
}
