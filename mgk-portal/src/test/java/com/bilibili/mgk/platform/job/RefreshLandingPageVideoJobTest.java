package com.bilibili.mgk.platform.job;

import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.junit.Assert.assertEquals;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年09月20日
 */
public class RefreshLandingPageVideoJobTest extends MockitoDefaultTest {

    @InjectMocks
    RefreshLandingPageVideoJob refreshLandingPageVideoJob;

    @Mock
    private IMgkLandingPageService mgkLandingPageService;

    @Test
    public void execute() throws Exception {
        ReturnT<String> execute = refreshLandingPageVideoJob.execute("1");
        assertEquals(IJobHandler.SUCCESS, execute);
    }
}