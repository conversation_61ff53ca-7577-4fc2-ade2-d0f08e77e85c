package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 创意案例
 * <p>
 * 参考{@link com.bilibili.mgk.platform.api.landing_page.dto.MgkHotLandingPageDto}并结合创意中心一期PRD
 *
 * <AUTHOR>
 * @desc
 * @date 2024/3/4
 */
@Data
@Accessors(chain = true)
public class InspirationCase implements SnakeCaseBody {

    /**
     * id TODO string?
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 亮点
     */
    private String lightPoint;

    /**
     * 痛点
     */
    private String painPoint;

    /**
     * 投放媒体app 如抖音、b站粉app
     */
    private String mediaApp;

    /**
     * 案例类型， 素材混剪、情景剧
     */
    private String adCategory;

    /**
     * 推广目的
     */
    private String adTarget;

    /**
     * 视频id
     */
    private Long avid;

    /**
     * 视频id
     */
    private String bvid;


    /**
     * 视频封面
     */
    private String coverImgUrl;


    /**
     * 竖屏板式
     */
    private String verticalType;


    /**
     * 画面分析图片链接
     */
    private String frameAnalysisImgUrl1;

    /**
     * 画面分析文字描述
     */
    private String frameAnalysisText1;

    /**
     * 画面分析图片链接2
     */
    private String frameAnalysisImgUrl2;

    /**
     * 画面分析文字描述2
     */
    private String frameAnalysisText2;


    /**
     * 画面分析图片链接
     */
    private String frameAnalysisImgUrl3;

    /**
     * 画面分析文字描述
     */
    private String frameAnalysisText3;


    /**
     * 所属行业
     */
    private String firstIndustryId;


    /**
     * 所属行业
     */
    private String firstIndustryName;

    /**
     * 所属行业
     */
    private String secondIndustryId;


    /**
     * 所属行业
     */
    private String secondIndustryName;


    /**
     * 一级带货分类
     */
    private String firstItemCategory;

    /**
     * 二级带货分类
     */
    private String secondItemCategory;


    /**
     * 数据聚合聚合日期，3d，7d
     */
    private String dataAggDayType;

    /**
     * 播放量
     */
    private String pv;

    /**
     * 点击率
     */
    private String ctr;

    /**
     * 点击率等级"A" "S"
     */
    private String ctrRank;

    /**
     * 播放量
     */
    private Long vv;

    /**
     * 播放量等级 "S" "A" 等
     */
    private String vvRank;


    /**
     * 转化率
     */
    private String conversionRate;

    /**
     * 增量播放量
     */
    private Long vvIncr;

    /**
     * 增量转化率
     */
    private String conversionRateIncr;


    /**
     * 投放时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;


    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")

    private LocalDateTime ctime;


    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime mtime;


    /**
     * 同步日期
     */
    private String logDate;


}
