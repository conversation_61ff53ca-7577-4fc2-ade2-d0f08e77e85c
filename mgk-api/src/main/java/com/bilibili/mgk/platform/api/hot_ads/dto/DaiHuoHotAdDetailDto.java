package com.bilibili.mgk.platform.api.hot_ads.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @file: HotAdsDto
 * @author: gaoming
 * @date: 2021/01/07
 * @version: 1.0
 * @description:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DaiHuoHotAdDetailDto implements Serializable {

    private static final long serialVersionUID = -4467901217389435617L;

    //创意ID
    private String creativeId;

    //创意标题
    private String creativeTitle;

    //封面图片 图片gif类型为图片链接，视频类型为封面图
    private String imageUrl;

    //avid
    private String avid;

    //bvid
    private String bvid;

    //视频地址
    private String videoUrl;

    //账户ID
    private String accountId;

    //曝光
    private String pv;

    //点击
    private String click;

    //转化数
    private String convNum;

    //ctr
    private Double ctr;

    //cvr
    private Double cvr;

    //pctcvr
    private Double pctcvr;

    //曝光等级 S A B C
    private String pvRank;

    //ctr等级
    private String ctrRank;

    //cvr等级
    private String cvrRank;

    //pctcvr_rank等级
    private String pctcvrRank;

    //统计日期类型,7d:最近7天，30d:最近30天
    private String dayType;

    //日期
    private String logDate;

    //商品详情
    private List<DaiHuoHotItemDto> itemDtoList;

}
