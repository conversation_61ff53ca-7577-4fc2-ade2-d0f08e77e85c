package com.bilibili.collage.biz.service.school;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.CategoryDto;
import com.bilibili.collage.api.dto.CommercialInfoDto;
import com.bilibili.collage.api.dto.CommonQuestionDto;
import com.bilibili.collage.api.dto.CreateAdvertiseGuidanceDto;
import com.bilibili.collage.api.dto.CreateArticleDto;
import com.bilibili.collage.api.dto.CreateCommercialInfoDto;
import com.bilibili.collage.api.dto.CreateCommonQuestionDto;
import com.bilibili.collage.api.dto.CreateProductManualDto;
import com.bilibili.collage.api.dto.EditAdvertiseGuidanceDto;
import com.bilibili.collage.api.dto.EditProductManualDto;
import com.bilibili.collage.api.dto.SanlianLatestUpdateDto;
import com.bilibili.collage.api.dto.SchoolArticleDto;
import com.bilibili.collage.api.dto.TitleDto;
import com.bilibili.collage.api.service.ISchoolConfigService;
import com.bilibili.collage.biz.entity.ArticleListQuery;
import com.bilibili.collage.biz.po.AdvertiseGuidancePo;
import com.bilibili.collage.biz.po.CommercialInfoPo;
import com.bilibili.collage.biz.po.CommonQuestionPo;
import com.bilibili.collage.biz.po.MyFavouritePo;
import com.bilibili.collage.biz.po.ProductManualPo;
import com.bilibili.collage.biz.repo.AdvertiseGuidanceRepo;
import com.bilibili.collage.biz.repo.CommercialInfoRepo;
import com.bilibili.collage.biz.repo.CommonQuestionRepo;
import com.bilibili.collage.biz.repo.MyFavouriteRepo;
import com.bilibili.collage.biz.repo.ProductManualRepo;
import com.bilibili.collage.biz.service.EsSearchService;
import com.bilibili.collage.biz.utils.Es5Utils;
import com.bilibili.collage.biz.utils.EsV2Utils;
import com.bilibili.collage.biz.utils.SearchQueryInfo;
import com.bilibili.databus.base.Message;
import com.bilibili.databus.base.SendResult;
import com.bilibili.databus.core.DataBusClient;
import com.bilibili.mgk.platform.common.enums.school.CategoryTypeEnum;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import com.bilibili.mgk.platform.common.utils.RandomUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@Deprecated
public class SchoolConfigService implements ISchoolConfigService {

    @Autowired
    private ProductManualRepo productManualRepo;
    @Autowired
    private AdvertiseGuidanceRepo advertiseGuidanceRepo;
    @Autowired
    private CommonQuestionRepo commonQuestionRepo;
    @Autowired
    private CommercialInfoRepo commercialInfoRepo;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;
    @Autowired
    private EsSearchService esSearchService;
    @Autowired
    private SchoolDetailService schoolDetailService;
    @Autowired
    private MyFavouriteRepo myFavouriteRepo;

    @Autowired
    private DataBusClient articlePubClient;
    @Value("${mgk.es.token:sDRF2mzwJ6iyQ3wnl7viGInO5ZkmDegWlNN3ALQkISM}")
    private String esSearchToken;
    
    private final static Integer sanlianIsShow = 1;
    private final static Integer sanlianCommonQuestionMax = 6;
    private final static Integer sanlianLatestUpdateMax = 4;


    @Override
    public Integer createProductManual(CreateProductManualDto createProductManualDto) {
        ProductManualPo productManualPo = new ProductManualPo();
        productManualPo.setLevel(createProductManualDto.getLevel());
        productManualPo.setOrderNum(createProductManualDto.getOrderNum());
        productManualPo.setParentId(createProductManualDto.getParentId());
        productManualPo.setTitle(createProductManualDto.getTitle());
        if(Utils.isPositive(createProductManualDto.getParentId())){
            ProductManualPo parentProductManualPo = productManualRepo.selectProductManualById(createProductManualDto.getParentId());
            productManualPo.setParentTitle(parentProductManualPo.getTitle());
        }
        productManualPo.setIsDeleted((byte) IsDeleted.VALID.getCode());
        return productManualRepo.insert(productManualPo);
    }

    @Override
    public PageResult<CategoryDto> getProductManualList(String nameLike, Long timeFrom, Long timeTo, Integer page, Integer size) {
        Long count = productManualRepo.count(nameLike,timeFrom, timeTo);
        if(count == 0L){
            return PageResult.EMPTY_PAGE_RESULT;
        }
        List<ProductManualPo> poList = productManualRepo.getProductManualList(nameLike, timeFrom, timeTo, page, size);
        List<CategoryDto> categoryDtoList = poList.stream().map(po->productManualRepo.convertProductManualPo2CategoryDto(po)).collect(Collectors.toList());
        return PageResult.<CategoryDto>builder()
                .total(count.intValue())
                .records(categoryDtoList)
                .build();
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public Integer editProductManual(EditProductManualDto editProductManualDto) {
        ProductManualPo productManualPo = new ProductManualPo();
        productManualPo.setId(editProductManualDto.getId());
        if(editProductManualDto.getTitle() != null){
            productManualPo.setTitle(editProductManualDto.getTitle());
            List<ProductManualPo> childProductManualPoList = productManualRepo.getProductManualListByParentId(editProductManualDto.getId());
            List<Long> ids = childProductManualPoList.stream().map(ProductManualPo::getId).collect(Collectors.toList());
            ProductManualPo childePo = new ProductManualPo();
            childePo.setParentTitle(editProductManualDto.getTitle());
            productManualRepo.batchUpdateById(childePo,ids);
            List<Long> articleIds = new ArrayList<>();

            //常见问题父标题
            if(CollectionUtils.isNotEmpty(ids)) {
                //一级标题
                for (Long id : ids) {
                    List<SchoolArticleDto> schoolArticleDtoList = schoolDetailService.getSchoolArticlesByParentId(id);
                    articleIds.addAll(schoolArticleDtoList.stream().map(SchoolArticleDto::getArticleId).collect(Collectors.toList())) ;
                    List<CommonQuestionPo> commonQuestionPos = commonQuestionRepo.getByArticleIds(articleIds);
                    for(CommonQuestionPo commonQuestionPo : commonQuestionPos){
                        CommonQuestionPo upPo = new CommonQuestionPo();
                        upPo.setId(commonQuestionPo.getId());
                        String parentTitle = editProductManualDto.getTitle()+"/"+commonQuestionPo.getParentTitle().split("/")[1];
                        upPo.setParentTitle(parentTitle);
                        commonQuestionRepo.updateById(upPo);
                    }
                }
            }else{
                //二级标题
                List<SchoolArticleDto> schoolArticleDtoList = schoolDetailService.getSchoolArticlesByParentId(editProductManualDto.getId());
                articleIds.addAll(schoolArticleDtoList.stream().map(SchoolArticleDto::getArticleId).collect(Collectors.toList())) ;
                List<CommonQuestionPo> commonQuestionPos = commonQuestionRepo.getByArticleIds(articleIds);
                for(CommonQuestionPo commonQuestionPo : commonQuestionPos){
                    CommonQuestionPo upPo = new CommonQuestionPo();
                    upPo.setId(commonQuestionPo.getId());
                    String parentTitle = commonQuestionPo.getParentTitle().split("/")[0]+"/"+editProductManualDto.getTitle();
                    upPo.setParentTitle(parentTitle);
                    commonQuestionRepo.updateById(upPo);
                }
            }
        }
        if(editProductManualDto.getOrderNum() != null){
            productManualPo.setOrderNum(editProductManualDto.getOrderNum());
        }

        return productManualRepo.updateById(productManualPo);
    }

    @Override
    public Integer deleteProductManual(Long id) {
        ProductManualPo productManualPo = productManualRepo.getProductManualPoById(id);
        if (productManualPo.getLevel().equals(0)) {
            List<ProductManualPo> childList = productManualRepo.getProductManualListByParentId(id);
            for (ProductManualPo child : childList) {
                deleteArticlesById(child.getId());
            }
            ProductManualPo updatePo = new ProductManualPo();
            updatePo.setId(id);
            updatePo.setIsDeleted((byte) IsDeleted.DELETED.getCode());
            return productManualRepo.updateById(updatePo);

        } else {
            deleteArticlesById(id);
            return 1;
        }
    }

    public void deleteArticlesById(Long id) {
        List<SchoolArticleDto> schoolArticleDtoList= schoolDetailService.getSchoolArticlesByParentId(id);
        for(SchoolArticleDto schoolArticleDto : schoolArticleDtoList){
            CreateArticleDto createArticleDto = new CreateArticleDto();
            createArticleDto.setArticleId(schoolArticleDto.getArticleId());
            createArticleDto.setIsShow((byte)0);
            createArticleDto.setIsDeleted((byte) IsDeleted.DELETED.getCode());
            this.doEditArticle(createArticleDto);
        }
        ProductManualPo updatePo = new ProductManualPo();
        updatePo.setId(id);
        updatePo.setIsDeleted((byte)IsDeleted.DELETED.getCode());
        productManualRepo.updateById(updatePo);
    }

    @Override
    public Integer createAdvertiseGuidance(CreateAdvertiseGuidanceDto createAdvertiseGuidanceDto) {
        AdvertiseGuidancePo advertiseGuidancePo = new AdvertiseGuidancePo();
        advertiseGuidancePo.setIndustryId(createAdvertiseGuidanceDto.getIndustryId());
        advertiseGuidancePo.setIndustryName(createAdvertiseGuidanceDto.getIndustryName());
        advertiseGuidancePo.setOrderNum(createAdvertiseGuidanceDto.getOrderNum());
        advertiseGuidancePo.setImage(createAdvertiseGuidanceDto.getImage());
        advertiseGuidancePo.setIsDeleted((byte) IsDeleted.VALID.getCode());
        return advertiseGuidanceRepo.insert(advertiseGuidancePo);
    }

    @Override
    public PageResult<CategoryDto> getAdvertiseGuidanceList(String nameLike, Long timeFrom, Long timeTo, Integer page, Integer size) {
        Long count = advertiseGuidanceRepo.count(nameLike,timeFrom, timeTo);
        if(count == 0L){
            return PageResult.EMPTY_PAGE_RESULT;
        }
        List<AdvertiseGuidancePo> poList = advertiseGuidanceRepo.getAdvertiseGuidanceList(nameLike, timeFrom, timeTo, page, size);
        List<CategoryDto> categoryDtoList = poList.stream().map(po->advertiseGuidanceRepo.convertAdvertiseGuidancePo2CategoryDto(po)).collect(Collectors.toList());
        return PageResult.<CategoryDto>builder()
                .total(count.intValue())
                .records(categoryDtoList)
                .build();
    }

    @Override
    public Integer editAdvertiseGuidance(EditAdvertiseGuidanceDto editAdvertiseGuidanceDto) {
        AdvertiseGuidancePo advertiseGuidancePo = new AdvertiseGuidancePo();
        advertiseGuidancePo.setId(editAdvertiseGuidanceDto.getId());
        if(editAdvertiseGuidanceDto.getImage() != null){
            advertiseGuidancePo.setImage(editAdvertiseGuidanceDto.getImage());
        }
        if(editAdvertiseGuidanceDto.getOrderNum() != null){
            advertiseGuidancePo.setOrderNum(editAdvertiseGuidanceDto.getOrderNum());
        }

        return advertiseGuidanceRepo.updateById(advertiseGuidancePo);
    }

    @Override
    public Integer deleteAdvertiseGuidance(Long id) {
        AdvertiseGuidancePo advertiseGuidancePo = advertiseGuidanceRepo.getAdvertiseGuidanceById(id);
        PageResult<SchoolArticleDto> schoolArticleDtoPageResult = schoolDetailService.getAdvertiseGuidanceList(advertiseGuidancePo.getIndustryId(),1,1000);
        for(SchoolArticleDto schoolArticleDto : schoolArticleDtoPageResult.getRecords()){
            CreateArticleDto createArticleDto = new CreateArticleDto();
            createArticleDto.setArticleId(schoolArticleDto.getArticleId());
            createArticleDto.setIsShow((byte)0);
            createArticleDto.setIsDeleted((byte) IsDeleted.DELETED.getCode());
            this.doEditArticle(createArticleDto);
        }
        AdvertiseGuidancePo updatePo = new AdvertiseGuidancePo();
        updatePo.setId(id);
        updatePo.setIsDeleted((byte)IsDeleted.DELETED.getCode());
        return advertiseGuidanceRepo.updateById(updatePo);
    }


    // 无法理解这种接口设计怎么能出现
    @Override
    public List<TitleDto> searchTitle(Byte type, Integer level) {
        List<TitleDto> titleDtoList = new ArrayList<>();
        if(CategoryTypeEnum.PRODUCT_MANUAL.getCode().equals(type)){
            List<ProductManualPo> productManualListByLevel = productManualRepo.getProductManualListByLevel(level);
            for(ProductManualPo po : productManualListByLevel){
                TitleDto titleDto = new TitleDto();
                titleDto.setId(po.getId());
                titleDto.setTitle(po.getTitle());
                titleDtoList.add(titleDto);
            }
        }else if (CategoryTypeEnum.ADVERTISE_GUIDANCE.getCode().equals(type)) {
            List<AdvertiseGuidancePo> advertiseGuidanceListByLevel = advertiseGuidanceRepo.getAdvertiseGuidanceList();
            for(AdvertiseGuidancePo po : advertiseGuidanceListByLevel){
                TitleDto titleDto = new TitleDto();
                titleDto.setId(po.getId());
                titleDto.setTitle(po.getIndustryName());
                titleDtoList.add(titleDto);
            }
        }else {
            List<ProductManualPo> productManualListByLevel = productManualRepo.getProductManualListByLevel(level);
            for(ProductManualPo po : productManualListByLevel){
                TitleDto titleDto = new TitleDto();
                titleDto.setId(po.getId());
                titleDto.setTitle(po.getTitle());
                titleDtoList.add(titleDto);
            }
            List<AdvertiseGuidancePo> advertiseGuidanceListByLevel = advertiseGuidanceRepo.getAdvertiseGuidanceList();
            for(AdvertiseGuidancePo po : advertiseGuidanceListByLevel){
                TitleDto titleDto = new TitleDto();
                titleDto.setId(po.getId());
                titleDto.setTitle(po.getIndustryName());
                titleDtoList.add(titleDto);
            }
        }
        return titleDtoList;
    }

    @Override
    public Integer createCommonQuestion(CreateCommonQuestionDto createCommonQuestionDto) throws ServiceException {
        long sanlianCount = commonQuestionRepo.countForSanlian();
        if (sanlianCount >= sanlianCommonQuestionMax) {
            throw new ServiceException("常见问题，最多只能创建6个");
        }
        CommonQuestionPo commonQuestionPo = new CommonQuestionPo();
        commonQuestionPo.setOrderNum(createCommonQuestionDto.getOrderNum());
        commonQuestionPo.setRelateId(createCommonQuestionDto.getTitleId());
        commonQuestionPo.setType(createCommonQuestionDto.getType());
        //TODO 获取文章，title,parentId,parentTitle
        SchoolArticleDto schoolArticleDto = schoolDetailService.getSchoolArticleDetail(createCommonQuestionDto.getTitleId(), null);
        commonQuestionPo.setTitle(schoolArticleDto.getTitle());
        commonQuestionPo.setIsShow(schoolArticleDto.getIsShow());
        commonQuestionPo.setParentId(schoolArticleDto.getParentId());
        if (CategoryTypeEnum.PRODUCT_MANUAL.getCode().equals(createCommonQuestionDto.getType())) {
            ProductManualPo productManualPo = productManualRepo.selectProductManualById(schoolArticleDto.getParentId());
            String parentTitle = productManualPo.getParentTitle() + "/" + productManualPo.getTitle();
            commonQuestionPo.setParentTitle(parentTitle);
        } else {
            commonQuestionPo.setParentTitle(schoolArticleDto.getParentTitle());
        }
        commonQuestionPo.setSanlianIsShow(createCommonQuestionDto.getSanlianIsShow());
        commonQuestionPo.setSanlianShowOrderNum(createCommonQuestionDto.getSanlianShowOrderNum());
        return commonQuestionRepo.insert(commonQuestionPo);
    }

    @Override
    public PageResult<CommonQuestionDto> getCommonQuestionListForMng(Integer page, Integer size) {
        Long count = commonQuestionRepo.count();
        if (count == 0L) {
            return PageResult.EMPTY_PAGE_RESULT;
        }
        List<CommonQuestionPo> poList = commonQuestionRepo.getCommonQuestionList(page, size);
        List<CommonQuestionDto> commonQuestionDtoList = poList.stream().map(po -> convertCommonQuestionPo2Dto(po)).collect(Collectors.toList());
        return PageResult.<CommonQuestionDto>builder()
                .total(count.intValue())
                .records(commonQuestionDtoList)
                .build();
    }

    @Override
    public Integer editCommonQuestion(CreateCommonQuestionDto createCommonQuestionDto) throws ServiceException {

        // 如果已经6个常见问题的情况下，甚至不允许取消，只能删除重试
        long sanlianCount = commonQuestionRepo.countForSanlian();
        if (sanlianCount >= sanlianCommonQuestionMax) {
            throw new ServiceException("常见问题，最多只能创建6个");
        }
        CommonQuestionPo commonQuestionPo = new CommonQuestionPo();
        commonQuestionPo.setId(createCommonQuestionDto.getId());
        if (createCommonQuestionDto.getTitleId() != null) {
            //TODO 获取文章，title,parentId,parentTitle
            SchoolArticleDto schoolArticleDto = schoolDetailService.getSchoolArticleDetail(createCommonQuestionDto.getTitleId(), null);
            commonQuestionPo.setTitle(schoolArticleDto.getTitle());
            commonQuestionPo.setRelateId(createCommonQuestionDto.getTitleId());
            commonQuestionPo.setIsShow(schoolArticleDto.getIsShow());
            if (CategoryTypeEnum.PRODUCT_MANUAL.getCode().equals(schoolArticleDto.getType())) {
                ProductManualPo productManualPo = productManualRepo.selectProductManualById(schoolArticleDto.getParentId());
                String parentTitle = productManualPo.getParentTitle() + "/" + productManualPo.getTitle();
                commonQuestionPo.setParentTitle(parentTitle);
            } else {
                commonQuestionPo.setParentTitle(schoolArticleDto.getParentTitle());

            }
        }
        if (createCommonQuestionDto.getOrderNum() != null) {
            commonQuestionPo.setOrderNum(createCommonQuestionDto.getOrderNum());
        }
        commonQuestionPo.setSanlianIsShow(createCommonQuestionDto.getSanlianIsShow());
        commonQuestionPo.setSanlianShowOrderNum(createCommonQuestionDto.getSanlianShowOrderNum());
        return commonQuestionRepo.updateById(commonQuestionPo);
    }

    @Override
    public Integer deleteCommonQuestion(Long id) {
        CommonQuestionPo commonQuestionPo = new CommonQuestionPo();
        commonQuestionPo.setId(id);
        commonQuestionPo.setIsDeleted((byte)IsDeleted.DELETED.getCode());
        return commonQuestionRepo.updateById(commonQuestionPo);
    }

    @Override
    public Integer createCommercialInfo(CreateCommercialInfoDto createCommercialInfoDto) {
        CommercialInfoPo commercialInfoPo = new CommercialInfoPo();
        commercialInfoPo.setTitle(createCommercialInfoDto.getTitle());
        commercialInfoPo.setJumpUrl(createCommercialInfoDto.getUrl());
        commercialInfoPo.setSanlianIsShow(createCommercialInfoDto.getSanlianIsShow());
        commercialInfoPo.setSanlianShowOrderNum(createCommercialInfoDto.getSanlianShowOrderNum());
        return commercialInfoRepo.insert(commercialInfoPo);
    }

    @Override
    public PageResult<CommercialInfoDto> getCommercialInfoList(Integer page, Integer size) {
        Long count = commercialInfoRepo.count();
        List<CommercialInfoDto> commercialInfoDtoList = commercialInfoRepo.getCommercialInfoList(page, size).stream().map(po -> {
            CommercialInfoDto commercialInfoDto = new CommercialInfoDto();
            commercialInfoDto.setId(po.getId());
            commercialInfoDto.setTitle(po.getTitle());
            commercialInfoDto.setJumpUrl(po.getJumpUrl());
            commercialInfoDto.setCtime(Utils.getTimestamp2StringBySecond(new Timestamp(po.getCtime().getTime())));
            commercialInfoDto.setMtime(Utils.getTimestamp2StringBySecond(new Timestamp(po.getMtime().getTime())));
            commercialInfoDto.setSanlianIsShow(po.getSanlianIsShow());
            commercialInfoDto.setSanlianShowOrderNum(po.getSanlianShowOrderNum());
            return commercialInfoDto;
        }).collect(Collectors.toList());
        return PageResult.<CommercialInfoDto>builder()
                .total(count.intValue())
                .records(commercialInfoDtoList)
                .build();
    }

    @Override
    public Integer editCommercialInfo(CreateCommercialInfoDto createCommercialInfoDto) {
        CommercialInfoPo commercialInfoPo = new CommercialInfoPo();
        commercialInfoPo.setId(createCommercialInfoDto.getId());
        if(createCommercialInfoDto.getTitle() != null){
            commercialInfoPo.setTitle(createCommercialInfoDto.getTitle());
        }
        if(createCommercialInfoDto.getUrl() != null){
            commercialInfoPo.setJumpUrl(createCommercialInfoDto.getUrl());
        }
        commercialInfoPo.setSanlianIsShow(createCommercialInfoDto.getSanlianIsShow());
        commercialInfoPo.setSanlianShowOrderNum(createCommercialInfoDto.getSanlianShowOrderNum());
        return commercialInfoRepo.updateById(commercialInfoPo);
    }

    @Override
    public Integer deleteCommercialInfo(Long id) {
        CommercialInfoPo commercialInfoPo = new CommercialInfoPo();
        commercialInfoPo.setId(id);
        commercialInfoPo.setIsDeleted((byte)IsDeleted.DELETED.getCode());
        return commercialInfoRepo.updateById(commercialInfoPo);
    }

    @Override
    public Integer createArticle(CreateArticleDto createArticleDto) throws ServiceException {
        if (sanlianIsShow.equals(createArticleDto.getSanlianIsShow())) {
            PageInfo<SchoolArticleDto> schoolArticleDtoPageInfo = this.getArticleListForSanlianFromEs();
            if (schoolArticleDtoPageInfo != null && schoolArticleDtoPageInfo.getTotal() >= sanlianLatestUpdateMax) {
                log.info("createArticle article size: {}", schoolArticleDtoPageInfo.getTotal());
                throw new ServiceException("平台动态，最多只能创建4个");
            }
        }
        try {
            Long articleId = snowflakeIdWorker.nextId();
            createArticleDto.setArticleId(articleId);
            createArticleDto.setIsDeleted((byte)IsDeleted.VALID.getCode());
            createArticleDto.setIsShow((byte) 1);
            createArticleDto.setFavourite((byte) 0);
            createArticleDto.setCtime(Utils.getTimestamp2StringBySecond(Utils.getNow()));
            createArticleDto.setMtime(Utils.getTimestamp2StringBySecond(Utils.getNow()));

            JSONObject esInsert = new JSONObject();
            esInsert.put("action","insert");
            esInsert.put("new",createArticleDto);

            Message msg = new Message(String.valueOf(RandomUtils.getHundredBoundRandom()),
                    JSON.toJSONBytes(esInsert),
                    Collections.emptyMap());

            log.info("createArticle pub msg:{}", esInsert);
            SendResult sendResult = articlePubClient.pub(msg);
            if (sendResult.isSuccess()) {
                return 1;
            } else {
                log.error("createArticle pub err [{}]", ExceptionUtils.getSubStringMsg(sendResult.getException()));
            }
        } catch (Exception e) {
            log.error("createArticle error" + ExceptionUtils.getSubStringMsg(e));
            throw new ServiceException("createArticle error");
        }
        return 1;
    }

    @Override
    public PageResult<SchoolArticleDto> articleList(Long parentId, Byte type) {
        SearchQueryInfo searchQueryInfo;
        ArticleListQuery articleListQuery = new ArticleListQuery();
        if(parentId != null && parentId > 0L) {
            articleListQuery.setParentId(parentId);
        }else{
            articleListQuery.setIsShow((byte) 1);
        }
        articleListQuery.setType(type);
        articleListQuery.setIsDeleted((byte)IsDeleted.VALID.getCode());
        articleListQuery.setSortRules(Lists.newArrayList("orderNum asc","ctime desc"));
        searchQueryInfo = EsV2Utils.build(articleListQuery, ArticleListQuery.class, esSearchToken);
        SearchSourceBuilder searchSourceBuilder = Es5Utils.buildSearchSourceBuilderWithPage(searchQueryInfo,0,100);
        PageInfo<SchoolArticleDto> schoolArticleDtoPageInfo = null;
        schoolArticleDtoPageInfo = esSearchService.getEsList(searchSourceBuilder,"mgk-portal", SchoolArticleDto.class);
        if(schoolArticleDtoPageInfo.getTotal() == 0){
            return PageResult.EMPTY_PAGE_RESULT;
        }
        return PageResult.<SchoolArticleDto>builder()
                .total((int) schoolArticleDtoPageInfo.getTotal())
                .records(schoolArticleDtoPageInfo.getList())
                .build();
    }

    /**
     * 编辑文章
     * @param createArticleDto
     * @return
     * @throws ServiceException
     */
    @Override
    public Integer editArticle(CreateArticleDto createArticleDto) throws ServiceException {
        if (sanlianIsShow.equals(createArticleDto.getSanlianIsShow())) {
            PageInfo<SchoolArticleDto> schoolArticleDtoPageInfo = this.getArticleListForSanlianFromEs();
            if (schoolArticleDtoPageInfo != null && schoolArticleDtoPageInfo.getTotal() >= sanlianLatestUpdateMax) {
                log.info("editArticle article size: {}", schoolArticleDtoPageInfo.getTotal());
                throw new ServiceException("平台动态，最多只能创建4个");
            }
        }
        try {
            this.doEditArticle(createArticleDto);
        } catch (Exception e) {
            throw new ServiceException("更新文章失败");
        }
        return 1;
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public Integer deleteArticle(Long articleId) {
        CreateArticleDto createArticleDto = new CreateArticleDto();
        createArticleDto.setArticleId(articleId);
        createArticleDto.setIsDeleted((byte) 1);
        try {

            JSONObject esInsert = new JSONObject();
            esInsert.put("action","update");
            esInsert.put("new",createArticleDto);

            Message msg = new Message(String.valueOf(RandomUtils.getHundredBoundRandom()),
                    JSON.toJSONBytes(esInsert),
                    Collections.emptyMap());

            log.info("deleteArticle pub msg:{}", createArticleDto);
            SendResult sendResult = articlePubClient.pub(msg);
            if (sendResult.isSuccess()) {
                List<CommonQuestionPo> commonQuestionPoList = commonQuestionRepo.getByArticleId(createArticleDto.getArticleId());
                if(!CollectionUtils.isEmpty(commonQuestionPoList)){
                    for(CommonQuestionPo commonQuestionPo : commonQuestionPoList) {
                        CommonQuestionPo updatePo = new CommonQuestionPo();
                        updatePo.setId(commonQuestionPo.getId());
                        updatePo.setIsDeleted(createArticleDto.getIsDeleted());
                        commonQuestionRepo.updateById(updatePo);
                    }
                }
                List<MyFavouritePo> myFavouritePoList = myFavouriteRepo.getByArticleId(createArticleDto.getArticleId());
                if(!CollectionUtils.isEmpty(myFavouritePoList)){
                    for(MyFavouritePo myFavouritePo : myFavouritePoList) {
                        MyFavouritePo updatePo = new MyFavouritePo();
                        updatePo.setId(myFavouritePo.getId());
                        updatePo.setIsDeleted(createArticleDto.getIsDeleted());
                        myFavouriteRepo.updateById(updatePo);
                    }
                }
                return 1;
            } else {
                log.error("deleteArticle pub err [{}]", ExceptionUtils.getSubStringMsg(sendResult.getException()));
            }
        } catch (Exception e) {
            log.error("deleteArticle error" + ExceptionUtils.getSubStringMsg(e));
        }
        return 1;
    }

    @Override
    public List<CommonQuestionDto> getCommonQuestionListForSanlian(Integer size) {
        List<CommonQuestionPo> poList = commonQuestionRepo.getCommonQuestionListForSanlian(size);
        List<CommonQuestionDto> dtoList = poList.stream().map(po->convertCommonQuestionPo2Dto(po)).collect(Collectors.toList());
        return dtoList;
    }

    @Override
    public List<SanlianLatestUpdateDto> getLatestUpdateListForSanlian() {
        List<SanlianLatestUpdateDto> dtoList = new ArrayList<>();
        // 产品说明、行业投放的文章信息
        PageInfo<SchoolArticleDto> schoolArticleDtoPageInfo = getArticleListForSanlianFromEs();
        if(schoolArticleDtoPageInfo != null && schoolArticleDtoPageInfo.getTotal() != 0){
            schoolArticleDtoPageInfo.getList().forEach(schoolArticle -> {
                SanlianLatestUpdateDto dto = new SanlianLatestUpdateDto();
                dto.setType(Integer.valueOf(schoolArticle.getType()));
                dto.setTitle(schoolArticle.getTitle());
                dto.setArticleId(String.valueOf(schoolArticle.getArticleId()));
                dto.setCtime(schoolArticle.getCtime());
                dto.setSanlianShowOrderNum(schoolArticle.getSanlianShowOrderNum());
                dtoList.add(dto);
            });
        }
        // 商业咨询
        List<CommercialInfoPo> commercialInfoPos = commercialInfoRepo.getCommercialInfoListForSanlian();
        for(CommercialInfoPo po : commercialInfoPos){
            SanlianLatestUpdateDto dto = new SanlianLatestUpdateDto();
            dto.setType(Integer.valueOf(CategoryTypeEnum.COMMERCIAL_INFO.getCode()));
            dto.setTitle(po.getTitle());
            dto.setJumpUrl(po.getJumpUrl());
            dto.setCtime(po.getCtime().getTime());
            dto.setSanlianShowOrderNum(po.getSanlianShowOrderNum());
            dtoList.add(dto);
        }
        if (CollectionUtils.isEmpty(dtoList)) {
            return dtoList;
        }
        // 根据orderNum排序，如果orderNum相等，按创建时间倒序排序
        Collections.sort(dtoList, (o1, o2) -> {
            if (o1.getSanlianShowOrderNum().equals(o2.getSanlianShowOrderNum())) {
                return o2.getCtime().compareTo(o1.getCtime());
            }
            return o1.getSanlianShowOrderNum().compareTo(o2.getSanlianShowOrderNum());
        });
        List<SanlianLatestUpdateDto> resp = dtoList.stream().limit(sanlianLatestUpdateMax).collect(Collectors.toList());
        return resp;
    }

    private PageInfo<SchoolArticleDto> getArticleListForSanlianFromEs() {
        SearchQueryInfo searchQueryInfo;
        ArticleListQuery articleListQuery = new ArticleListQuery();
        articleListQuery.setSanlianIsShow(1);
        articleListQuery.setIsDeleted((byte)IsDeleted.VALID.getCode());
        articleListQuery.setSortRules(Lists.newArrayList("sanlianShowOrderNum asc","ctime desc"));
        searchQueryInfo = EsV2Utils.build(articleListQuery, ArticleListQuery.class, esSearchToken);
        SearchSourceBuilder searchSourceBuilder = Es5Utils.buildSearchSourceBuilderWithPage(searchQueryInfo,0,100);
        PageInfo<SchoolArticleDto> schoolArticleDtoPageInfo = esSearchService.getEsList(searchSourceBuilder,"mgk-portal", SchoolArticleDto.class);
        return schoolArticleDtoPageInfo;
    }

    private int doEditArticle(CreateArticleDto createArticleDto) {
        try {
            // 1. 发mq: 更新article es
            createArticleDto.setMtime(Utils.getTimestamp2StringBySecond(Utils.getNow()));
            JSONObject esInsert = new JSONObject();
            esInsert.put("action", "update");
            esInsert.put("new", createArticleDto);
            Message msg = new Message(String.valueOf(RandomUtils.getHundredBoundRandom()),
                    JSON.toJSONBytes(esInsert),
                    Collections.emptyMap());
            log.info("doEditArticle pub msg:{}", esInsert);
            SendResult sendResult = articlePubClient.pub(msg);
            if (sendResult.isSuccess()) {
                // 2. 查询【常见问题】关联的文章，软删除
                List<CommonQuestionPo> commonQuestionPoList = commonQuestionRepo.getByArticleId(createArticleDto.getArticleId());
                if (!CollectionUtils.isEmpty(commonQuestionPoList)) {
                    for (CommonQuestionPo commonQuestionPo : commonQuestionPoList) {
                        CommonQuestionPo updatePo = new CommonQuestionPo();
                        updatePo.setId(commonQuestionPo.getId());
                        if (createArticleDto.getTitle() != null) {
                            updatePo.setTitle(createArticleDto.getTitle());
                        }
                        if (createArticleDto.getIsShow() != null) {
                            updatePo.setIsShow(createArticleDto.getIsShow());
                        }
                        if (createArticleDto.getIsDeleted() != null) {
                            updatePo.setIsDeleted(createArticleDto.getIsDeleted());
                        }
                        commonQuestionRepo.updateById(updatePo);
                    }
                }
                // 3. 查询【我的收藏文章】，软删除
                List<MyFavouritePo> myFavouritePoList = myFavouriteRepo.getByArticleId(createArticleDto.getArticleId());
                if (!CollectionUtils.isEmpty(myFavouritePoList)) {
                    for (MyFavouritePo myFavouritePo : myFavouritePoList) {
                        MyFavouritePo updatePo = new MyFavouritePo();
                        updatePo.setId(myFavouritePo.getId());
                        if (createArticleDto.getTitle() != null) {
                            updatePo.setTitle(createArticleDto.getTitle());
                        }
                        if (createArticleDto.getIsDeleted() != null) {
                            updatePo.setIsDeleted(createArticleDto.getIsDeleted());
                        }
                        if (Byte.valueOf("0").equals(createArticleDto.getIsShow())) {
                            updatePo.setIsDeleted((byte) IsDeleted.DELETED.getCode());
                        }
                        myFavouriteRepo.updateById(updatePo);
                    }
                }
            } else {
                log.error("doEditArticle pub err [{}]", ExceptionUtils.getSubStringMsg(sendResult.getException()));
            }
        } catch (Exception e) {
            log.error("doEditArticle error" + ExceptionUtils.getSubStringMsg(e));
        }
        return 1;
    }

    private CommonQuestionDto convertCommonQuestionPo2Dto(CommonQuestionPo po) {
        CommonQuestionDto commonQuestionDto = new CommonQuestionDto();
        commonQuestionDto.setId(po.getId());
        commonQuestionDto.setOrderNum(po.getOrderNum());
        commonQuestionDto.setTitle(po.getTitle());
        commonQuestionDto.setIsShow(po.getIsShow());
        commonQuestionDto.setRelateId(po.getRelateId());
        commonQuestionDto.setParentTitle(po.getParentTitle());
        commonQuestionDto.setCtime(Utils.getTimestamp2StringBySecond(new Timestamp(po.getCtime().getTime())));
        commonQuestionDto.setMtime(Utils.getTimestamp2StringBySecond(new Timestamp(po.getMtime().getTime())));
        commonQuestionDto.setSanlianIsShow(po.getSanlianIsShow());
        commonQuestionDto.setSanlianShowOrderNum(po.getSanlianShowOrderNum());
        return commonQuestionDto;
    }
}
