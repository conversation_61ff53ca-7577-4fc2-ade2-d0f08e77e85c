package com.bilibili.mgk.material.center.service.aigc.vo;

import com.bilibili.mgk.material.center.service.aigc.model.CropResultImage;
import org.junit.Ignore;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/11
 */
@Ignore
public class AigcPojoConvertorTest {


    @Test
    public void test() {

        OriginImageInfo r = AigcPojoConvertor.convertor.toOriginImageInfo(
                new CropResultImage()
                        .setOriginImgMd5("origin-md5")
                        .setCropImgMd5("crop-md5"),

                "material-id-test"
        );

        System.out.println(r);

    }
}