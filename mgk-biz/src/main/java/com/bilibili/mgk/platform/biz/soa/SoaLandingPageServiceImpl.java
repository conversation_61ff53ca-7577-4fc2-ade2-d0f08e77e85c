package com.bilibili.mgk.platform.biz.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.Page;
import com.bilibili.mgk.platform.api.landing_page.dto.*;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageDao;
import com.bilibili.mgk.platform.biz.po.MgkLandingPagePoExample;
import com.bilibili.mgk.platform.biz.service.LandingPageServiceDelegate;
import com.bilibili.mgk.platform.common.*;
import com.bilibili.mgk.platform.common.page_bean.GameDto;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/1/19
 **/
@Slf4j
@Service
public class SoaLandingPageServiceImpl implements ISoaLandingPageService {

    @Autowired
    private ExtMgkLandingPageDao extMgkLandingPageDao;

    @Autowired
    private IMgkLandingPageService mgkLandingPageService;

    @Autowired
    private LandingPageServiceDelegate landingPageServiceDelegate;

    @Override
    public void downline(Operator operator, long pageId) {
        mgkLandingPageService.downline(operator, pageId);
    }

    @Override
    public List<Long> batchDownlineByMiniGameId(Operator operator, Integer miniGameId) {
        return mgkLandingPageService.batchDownlineByMiniGameId(operator, miniGameId);
    }

    @Override
    public void refreshCDN(Operator operator, long pageId) {
        mgkLandingPageService.refreshCDN(operator, pageId);
    }

    @Override
    public void updateMgkPageStatusByAppPackageId(Operator operator, Integer appPackageId) {
        mgkLandingPageService.downlineMgkPageByAppPackageId(operator, appPackageId);
    }

    @Override
    public Integer getAdVersionControllIdByTemplateStyle(Integer templateStyle) {
        return TemplateStyleEnum.getByCode(templateStyle).getAdVersionControllId();
    }

    @Override
    public Integer getAdVersionControlIdByPageId(Long pageId) {
        return mgkLandingPageService.getAdVersionControlIdByPageId(pageId);
    }

    @Override
    public LandingPageConfigDto getLandingPageConfigDtoByPageId(Long pageId) {
        return mgkLandingPageService.getLandingPageConfigDtoByPageId(pageId);
    }

    @Override
    public MgkLandingPageBean validatePageIdAndGetLandingPage(Integer jumpType, String promotionPurposeContent) {
        //校验页面ID
        if (MgkJumpTypeEnum.PAGE_ID.getCode().equals(jumpType)) {
            LandingPageConfigDto landingPageDto = mgkLandingPageService.getLandingPageConfigDtoByPageId(Long.valueOf(promotionPurposeContent));
            Assert.isTrue(LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageDto.getStatus()), "非发布状态的页面不可投放");
            LandingPageTypeEnum landingPageType = LandingPageTypeEnum.getByCode(landingPageDto.getType());
            Integer adVersionControllId = this.getAdVersionControlIdByPageId(Long.valueOf(promotionPurposeContent));
            String launchUrl = landingPageType.getLaunchUrl(landingPageDto.getPageId(), landingPageDto.getTemplateStyle());
            return MgkLandingPageBean.builder()
                    .mgkPageId(landingPageDto.getPageId())
                    .launchUrl(launchUrl)
                    .adVersionControllId(adVersionControllId)
                    .pageStatus(landingPageDto.getStatus())
                    .templateStyle(landingPageDto.getTemplateStyle())
                    .pageType(landingPageDto.getType())
                    .build();
        } else {
            return MgkLandingPageBean.builder()
                    .mgkPageId(0L)
                    .launchUrl(promotionPurposeContent)
                    .adVersionControllId(0)
                    .pageStatus(LandingPageStatusEnum.PUBLISHED.getCode())
                    .templateStyle(0)
                    .pageType(0)
                    .build();
        }
    }

    @Override
    public MgkLandingPageBean validatePageIdAndGetLandingPage(String promotionPurposeContent, Integer isApplets) {
        //校验页面ID
        MgkLandingPageDto landingPageDto = mgkLandingPageService.getLandingPageDtoByPageId(Long.valueOf(promotionPurposeContent));
        Assert.isTrue(LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageDto.getStatus()), "非发布状态的页面不可投放");
        Integer adVersionControllId = this.getAdVersionControlIdByPageId(Long.valueOf(promotionPurposeContent));
        String launchUrl = mgkLandingPageService.getLaunchUrl(isApplets, landingPageDto);
        return MgkLandingPageBean.builder()
                .mgkPageId(landingPageDto.getPageId())
                .launchUrl(launchUrl)
                .adVersionControllId(adVersionControllId)
                .pageStatus(landingPageDto.getStatus())
                .templateStyle(landingPageDto.getTemplateStyle())
                .pageType(landingPageDto.getType())
                .build();
    }

    @Override
    public MgkLandingPageBean validatePageIdAndGetLandingPage(String promotionPurposeContent) {
        MgkLandingPageDto landingPageDto = mgkLandingPageService.getLandingPageDtoByPageId(Long.valueOf(promotionPurposeContent));
        Assert.isTrue(LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageDto.getStatus()), "非发布状态的页面不可投放");
        Integer adVersionControllId = this.getAdVersionControlIdByPageId(Long.valueOf(promotionPurposeContent));
        String launchUrl = mgkLandingPageService.getLaunchUrl(WhetherEnum.NO.getCode(), landingPageDto);
        String launchUrlSecondary = mgkLandingPageService.getLaunchUrl(WhetherEnum.YES.getCode(), landingPageDto);
        Map<Long, List<GameDto>> gameMap = this.landingPageServiceDelegate.getLandingPageGames(Lists.newArrayList(landingPageDto.getPageId()));
        return MgkLandingPageBean.builder()
                .mgkPageId(landingPageDto.getPageId())
                .launchUrl(launchUrl)
                .launchUrlSecondary(launchUrlSecondary)
                .adVersionControllId(adVersionControllId)
                .pageStatus(landingPageDto.getStatus())
                .templateStyle(landingPageDto.getTemplateStyle())
                .pageType(landingPageDto.getType())
                .games(gameMap.get(landingPageDto.getPageId()))
                .build();
    }

    @Override
    public List<Long> getPublishedLandingPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }
        return CollectionHelper.callInBatches(pageIds, 100, pids -> {
            MgkLandingPagePoExample example = new MgkLandingPagePoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andPageIdIn(pids)
                    .andStatusEqualTo(LandingPageStatusEnum.PUBLISHED.getCode());
            return extMgkLandingPageDao.selectPageIdsByExample(example);
        });
    }

    // 兼容历史接口 暂时不改名 后续直接迁移grpc
    @Override
    public List<MgkLandingPageDto> getLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto) {
        return mgkLandingPageService.getLaunchLandingPageDtos(queryLandingPageParamDto);
    }

    @Override
    public PageResult<MgkLandingPageDto> getLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto, Page page) {
        return mgkLandingPageService.getLandingPageDtos(queryLandingPageParamDto, page);
    }

    @Override
    public PageResult<MgkLandingPageWithFormDto> queryLandingPageWithForm(QueryLandingPageWithFormParamDto queryDto, Page page) {
        return mgkLandingPageService.queryLandingPageWithForm(queryDto, page);
    }

    @Override
    public PageResult<MgkLandingPageWithMacroParamDto> queryLandingPageWithMacroParam(QueryMgkLandingPageWithMacroParamDto queryDto, Page page) {
        return mgkLandingPageService.queryLandingPageWithMacroParam(queryDto, page);
    }

    @Override
    public String getNativeLaunchUrlByPageId(Long pageId) {
        return mgkLandingPageService.getNativeLaunchUrlByPageId(pageId);
    }

    @Override
    public Map<Integer, String> getPlatformLaunchUrlWithParams(Long pageId) {
        return mgkLandingPageService.getPlatformLaunchUrlWithParams(pageId);
    }

    @Override
    public Map<Long, String> getJumpUrlByPageIds(List<Long> pageIdList) {
        return mgkLandingPageService.getJumpUrlByPageIds(pageIdList);
    }

    @Override
    public TemplatePageDto getMgkTemplatePage(Operator operator, Long pageId, Integer gameBaseId, Integer packageId, String url) {
        return mgkLandingPageService.getMgkTemplatePage(operator, pageId, gameBaseId, packageId, url);
    }

    @Override
    public Map<Integer, List<Long>> getLandingPageIdsByApkIds(List<Integer> apkIds) {
        if(CollectionUtils.isEmpty(apkIds)) {
            return null;
        }
        return landingPageServiceDelegate.getAppPackageId2MgkPageIdMapInAppIds(apkIds);
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void updatePageAppConfigByPageId(Long pageId, String oldApkUrl, String newApkUrl,
                                            Operator operator) {
        Assert.isTrue(!StringUtils.isEmpty(oldApkUrl), "旧应用包链接不能为空");
        Assert.isTrue(!StringUtils.isEmpty(newApkUrl), "新应用包链接不能为空");


       List<Long> pageIds = landingPageServiceDelegate.getRelationPageIds(pageId);
       pageIds.forEach(pId->{
           LandingPageConfigDto configDto = landingPageServiceDelegate.getLandingPageConfigDtoByPageId(pId);
           log.info("updatePageAppConfigByPageId configDto [{}]", configDto);
           if(configDto == null){
               return;
           }
           String config = configDto.getConfig();
           mgkLandingPageService.updatePageConfig(operator, pId, config.replace(oldApkUrl, newApkUrl));
       });
    }


    @Override
    public long create(Operator operator, NewLandingPageDto newLandingPageDto) {
        return mgkLandingPageService.create(operator, newLandingPageDto);
    }

    @Override
    public void update(Operator operator, UpdateLandingPageDto updateLandingPageDto) {
        mgkLandingPageService.update(operator, updateLandingPageDto);
    }

    @Override
    public long createBaseInfo(Operator operator, NewLandingPageDto newLandingPageDto) {
        return landingPageServiceDelegate.create(operator, newLandingPageDto);
    }

    @Override
    public void disable(Operator operator, Long pageId) {
        mgkLandingPageService.batchDisable(operator, Lists.newArrayList(pageId));
    }

    /*
     * 刷新落地页缓存
     */
    @Override
    public void refreshLandingPageCache(long pageId){
        landingPageServiceDelegate.refreshPageConfigAndFormItemToRedis(pageId);
    }
}
