package com.bilibili.collage.biz.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Data
public class EsDSLResponse {
	@ApiModelProperty(value = "整个搜索请求耗费了多少毫秒")
	private Integer took;

	@ApiModelProperty(value = "查询是否超时")
	@JsonProperty("timed_out")
	private Boolean timedOut;

	@JsonProperty("_shards")
	private Shards shards;

	private Hits hits;

	@Data
	public static class Shards{
		@ApiModelProperty(value = "分片总数")
		private Integer total;

		@ApiModelProperty(value = "查询成功分片总数")
		private Integer successful;

		@ApiModelProperty(value = "查询跳过分片总数")
		private Integer skipped;

		@ApiModelProperty(value = "查询失败分片总数")
		private Integer failed;
	}

	@Data
	public static class Hits{
		@ApiModelProperty("示匹配到文档总数")
		private Total total;

		@ApiModelProperty("返回实体")
		private List<Hit> hits;
	}

	@Data
	public static class Total{
		private Integer value;

		private String relation;
	}

	@Data
	public static class Hit<T>{
		@ApiModelProperty("应用名-即索引名称")
		@JsonProperty("_index")
		private String index;

		@ApiModelProperty("返回类型")
		@JsonProperty("_type")
		private String type;

		@ApiModelProperty("唯一ID")
		@JsonProperty("_id")
		private String id;

		@ApiModelProperty("分片路由ID")
		@JsonProperty("_routing")
		private String routing;

		@ApiModelProperty("分数")
		@JsonProperty("_score")
		private Double score;

		@ApiModelProperty("实际返回内容")
		@JsonProperty("_source")
		private JSONObject source;

		private List<String> sort;
	}

	public <T> List<T> getList(Class<T> sourceType) {
		List<T> sourceList = new ArrayList();
		if (this.getHits() != null && this.getHits().getHits() != null) {
			for (Hit hit : this.getHits().getHits()) {
				sourceList.add(JSON.toJavaObject(hit.getSource(), sourceType));
			}
		}
		return sourceList;
	}
}
