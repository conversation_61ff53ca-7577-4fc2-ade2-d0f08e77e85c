package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkInspirationArticlePo;
import com.bilibili.mgk.platform.biz.po.MgkInspirationArticlePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkInspirationArticleDao {
    long countByExample(MgkInspirationArticlePoExample example);

    int deleteByExample(MgkInspirationArticlePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkInspirationArticlePo record);

    int insertBatch(List<MgkInspirationArticlePo> records);

    int insertUpdateBatch(List<MgkInspirationArticlePo> records);

    int insert(MgkInspirationArticlePo record);

    int insertUpdateSelective(MgkInspirationArticlePo record);

    int insertSelective(MgkInspirationArticlePo record);

    List<MgkInspirationArticlePo> selectByExampleWithBLOBs(MgkInspirationArticlePoExample example);

    List<MgkInspirationArticlePo> selectByExample(MgkInspirationArticlePoExample example);

    MgkInspirationArticlePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkInspirationArticlePo record, @Param("example") MgkInspirationArticlePoExample example);

    int updateByExampleWithBLOBs(@Param("record") MgkInspirationArticlePo record, @Param("example") MgkInspirationArticlePoExample example);

    int updateByExample(@Param("record") MgkInspirationArticlePo record, @Param("example") MgkInspirationArticlePoExample example);

    int updateByPrimaryKeySelective(MgkInspirationArticlePo record);

    int updateByPrimaryKeyWithBLOBs(MgkInspirationArticlePo record);

    int updateByPrimaryKey(MgkInspirationArticlePo record);
}