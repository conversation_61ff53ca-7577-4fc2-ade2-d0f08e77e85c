package com.bilibili.mgk.platform.api.inspiration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: UpdateArticleDto
 * @author: gaoming
 * @date: 2021/03/23
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class UpdateArticleDto implements Serializable {

    private static final long serialVersionUID = 1762281450141561374L;
    /**
     * 文章id
     */
    private Long articleId;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 行业 1-电商 2-游戏 3-网服 4-教育 5-其他
     */
    private String industry;

    /**
     * 内容
     */
    private String content;
}
