package com.bilibili.mgk.material.center.repository.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MgkRejectedMaterialAigcRecordPoExample {

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public MgkRejectedMaterialAigcRecordPoExample() {
        oredCriteria = new ArrayList<>();
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Long getOffset() {
        return offset;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    protected abstract static class GeneratedCriteria {

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNull() {
            addCriterion("material_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNotNull() {
            addCriterion("material_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdEqualTo(String value) {
            addCriterion("material_id =", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotEqualTo(String value) {
            addCriterion("material_id <>", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThan(String value) {
            addCriterion("material_id >", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThanOrEqualTo(String value) {
            addCriterion("material_id >=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThan(String value) {
            addCriterion("material_id <", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThanOrEqualTo(String value) {
            addCriterion("material_id <=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLike(String value) {
            addCriterion("material_id like", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotLike(String value) {
            addCriterion("material_id not like", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIn(List<String> values) {
            addCriterion("material_id in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotIn(List<String> values) {
            addCriterion("material_id not in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdBetween(String value1, String value2) {
            addCriterion("material_id between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotBetween(String value1, String value2) {
            addCriterion("material_id not between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeIsNull() {
            addCriterion("material_id_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeIsNotNull() {
            addCriterion("material_id_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeEqualTo(String value) {
            addCriterion("material_id_type =", value, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeNotEqualTo(String value) {
            addCriterion("material_id_type <>", value, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeGreaterThan(String value) {
            addCriterion("material_id_type >", value, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("material_id_type >=", value, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeLessThan(String value) {
            addCriterion("material_id_type <", value, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeLessThanOrEqualTo(String value) {
            addCriterion("material_id_type <=", value, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeLike(String value) {
            addCriterion("material_id_type like", value, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeNotLike(String value) {
            addCriterion("material_id_type not like", value, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeIn(List<String> values) {
            addCriterion("material_id_type in", values, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeNotIn(List<String> values) {
            addCriterion("material_id_type not in", values, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeBetween(String value1, String value2) {
            addCriterion("material_id_type between", value1, value2, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialIdTypeNotBetween(String value1, String value2) {
            addCriterion("material_id_type not between", value1, value2, "materialIdType");
            return (Criteria) this;
        }

        public Criteria andMaterialUkIsNull() {
            addCriterion("material_uk is null");
            return (Criteria) this;
        }

        public Criteria andMaterialUkIsNotNull() {
            addCriterion("material_uk is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialUkEqualTo(String value) {
            addCriterion("material_uk =", value, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkNotEqualTo(String value) {
            addCriterion("material_uk <>", value, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkGreaterThan(String value) {
            addCriterion("material_uk >", value, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkGreaterThanOrEqualTo(String value) {
            addCriterion("material_uk >=", value, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkLessThan(String value) {
            addCriterion("material_uk <", value, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkLessThanOrEqualTo(String value) {
            addCriterion("material_uk <=", value, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkLike(String value) {
            addCriterion("material_uk like", value, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkNotLike(String value) {
            addCriterion("material_uk not like", value, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkIn(List<String> values) {
            addCriterion("material_uk in", values, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkNotIn(List<String> values) {
            addCriterion("material_uk not in", values, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkBetween(String value1, String value2) {
            addCriterion("material_uk between", value1, value2, "materialUk");
            return (Criteria) this;
        }

        public Criteria andMaterialUkNotBetween(String value1, String value2) {
            addCriterion("material_uk not between", value1, value2, "materialUk");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdIsNull() {
            addCriterion("lau_material_id is null");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdIsNotNull() {
            addCriterion("lau_material_id is not null");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdEqualTo(Long value) {
            addCriterion("lau_material_id =", value, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdNotEqualTo(Long value) {
            addCriterion("lau_material_id <>", value, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdGreaterThan(Long value) {
            addCriterion("lau_material_id >", value, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdGreaterThanOrEqualTo(Long value) {
            addCriterion("lau_material_id >=", value, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdLessThan(Long value) {
            addCriterion("lau_material_id <", value, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdLessThanOrEqualTo(Long value) {
            addCriterion("lau_material_id <=", value, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdIn(List<Long> values) {
            addCriterion("lau_material_id in", values, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdNotIn(List<Long> values) {
            addCriterion("lau_material_id not in", values, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdBetween(Long value1, Long value2) {
            addCriterion("lau_material_id between", value1, value2, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andLauMaterialIdNotBetween(Long value1, Long value2) {
            addCriterion("lau_material_id not between", value1, value2, "lauMaterialId");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andRejectReasonIsNull() {
            addCriterion("reject_reason is null");
            return (Criteria) this;
        }

        public Criteria andRejectReasonIsNotNull() {
            addCriterion("reject_reason is not null");
            return (Criteria) this;
        }

        public Criteria andRejectReasonEqualTo(String value) {
            addCriterion("reject_reason =", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonNotEqualTo(String value) {
            addCriterion("reject_reason <>", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonGreaterThan(String value) {
            addCriterion("reject_reason >", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonGreaterThanOrEqualTo(String value) {
            addCriterion("reject_reason >=", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonLessThan(String value) {
            addCriterion("reject_reason <", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonLessThanOrEqualTo(String value) {
            addCriterion("reject_reason <=", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonLike(String value) {
            addCriterion("reject_reason like", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonNotLike(String value) {
            addCriterion("reject_reason not like", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonIn(List<String> values) {
            addCriterion("reject_reason in", values, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonNotIn(List<String> values) {
            addCriterion("reject_reason not in", values, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonBetween(String value1, String value2) {
            addCriterion("reject_reason between", value1, value2, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonNotBetween(String value1, String value2) {
            addCriterion("reject_reason not between", value1, value2, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectKeyIsNull() {
            addCriterion("reject_key is null");
            return (Criteria) this;
        }

        public Criteria andRejectKeyIsNotNull() {
            addCriterion("reject_key is not null");
            return (Criteria) this;
        }

        public Criteria andRejectKeyEqualTo(String value) {
            addCriterion("reject_key =", value, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyNotEqualTo(String value) {
            addCriterion("reject_key <>", value, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyGreaterThan(String value) {
            addCriterion("reject_key >", value, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyGreaterThanOrEqualTo(String value) {
            addCriterion("reject_key >=", value, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyLessThan(String value) {
            addCriterion("reject_key <", value, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyLessThanOrEqualTo(String value) {
            addCriterion("reject_key <=", value, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyLike(String value) {
            addCriterion("reject_key like", value, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyNotLike(String value) {
            addCriterion("reject_key not like", value, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyIn(List<String> values) {
            addCriterion("reject_key in", values, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyNotIn(List<String> values) {
            addCriterion("reject_key not in", values, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyBetween(String value1, String value2) {
            addCriterion("reject_key between", value1, value2, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectKeyNotBetween(String value1, String value2) {
            addCriterion("reject_key not between", value1, value2, "rejectKey");
            return (Criteria) this;
        }

        public Criteria andRejectTimeIsNull() {
            addCriterion("reject_time is null");
            return (Criteria) this;
        }

        public Criteria andRejectTimeIsNotNull() {
            addCriterion("reject_time is not null");
            return (Criteria) this;
        }

        public Criteria andRejectTimeEqualTo(Date value) {
            addCriterion("reject_time =", value, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeNotEqualTo(Date value) {
            addCriterion("reject_time <>", value, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeGreaterThan(Date value) {
            addCriterion("reject_time >", value, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("reject_time >=", value, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeLessThan(Date value) {
            addCriterion("reject_time <", value, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeLessThanOrEqualTo(Date value) {
            addCriterion("reject_time <=", value, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeIn(List<Date> values) {
            addCriterion("reject_time in", values, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeNotIn(List<Date> values) {
            addCriterion("reject_time not in", values, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeBetween(Date value1, Date value2) {
            addCriterion("reject_time between", value1, value2, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andRejectTimeNotBetween(Date value1, Date value2) {
            addCriterion("reject_time not between", value1, value2, "rejectTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeIsNull() {
            addCriterion("aigc_submit_time is null");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeIsNotNull() {
            addCriterion("aigc_submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeEqualTo(Date value) {
            addCriterion("aigc_submit_time =", value, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeNotEqualTo(Date value) {
            addCriterion("aigc_submit_time <>", value, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeGreaterThan(Date value) {
            addCriterion("aigc_submit_time >", value, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("aigc_submit_time >=", value, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeLessThan(Date value) {
            addCriterion("aigc_submit_time <", value, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeLessThanOrEqualTo(Date value) {
            addCriterion("aigc_submit_time <=", value, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeIn(List<Date> values) {
            addCriterion("aigc_submit_time in", values, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeNotIn(List<Date> values) {
            addCriterion("aigc_submit_time not in", values, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeBetween(Date value1, Date value2) {
            addCriterion("aigc_submit_time between", value1, value2, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcSubmitTimeNotBetween(Date value1, Date value2) {
            addCriterion("aigc_submit_time not between", value1, value2, "aigcSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeIsNull() {
            addCriterion("aigc_complete_time is null");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeIsNotNull() {
            addCriterion("aigc_complete_time is not null");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeEqualTo(Date value) {
            addCriterion("aigc_complete_time =", value, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeNotEqualTo(Date value) {
            addCriterion("aigc_complete_time <>", value, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeGreaterThan(Date value) {
            addCriterion("aigc_complete_time >", value, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("aigc_complete_time >=", value, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeLessThan(Date value) {
            addCriterion("aigc_complete_time <", value, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("aigc_complete_time <=", value, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeIn(List<Date> values) {
            addCriterion("aigc_complete_time in", values, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeNotIn(List<Date> values) {
            addCriterion("aigc_complete_time not in", values, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeBetween(Date value1, Date value2) {
            addCriterion("aigc_complete_time between", value1, value2, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcCompleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("aigc_complete_time not between", value1, value2, "aigcCompleteTime");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeIsNull() {
            addCriterion("aigc_error_code is null");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeIsNotNull() {
            addCriterion("aigc_error_code is not null");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeEqualTo(Integer value) {
            addCriterion("aigc_error_code =", value, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeNotEqualTo(Integer value) {
            addCriterion("aigc_error_code <>", value, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeGreaterThan(Integer value) {
            addCriterion("aigc_error_code >", value, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("aigc_error_code >=", value, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeLessThan(Integer value) {
            addCriterion("aigc_error_code <", value, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeLessThanOrEqualTo(Integer value) {
            addCriterion("aigc_error_code <=", value, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeIn(List<Integer> values) {
            addCriterion("aigc_error_code in", values, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeNotIn(List<Integer> values) {
            addCriterion("aigc_error_code not in", values, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeBetween(Integer value1, Integer value2) {
            addCriterion("aigc_error_code between", value1, value2, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("aigc_error_code not between", value1, value2, "aigcErrorCode");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgIsNull() {
            addCriterion("aigc_error_msg is null");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgIsNotNull() {
            addCriterion("aigc_error_msg is not null");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgEqualTo(String value) {
            addCriterion("aigc_error_msg =", value, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgNotEqualTo(String value) {
            addCriterion("aigc_error_msg <>", value, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgGreaterThan(String value) {
            addCriterion("aigc_error_msg >", value, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgGreaterThanOrEqualTo(String value) {
            addCriterion("aigc_error_msg >=", value, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgLessThan(String value) {
            addCriterion("aigc_error_msg <", value, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgLessThanOrEqualTo(String value) {
            addCriterion("aigc_error_msg <=", value, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgLike(String value) {
            addCriterion("aigc_error_msg like", value, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgNotLike(String value) {
            addCriterion("aigc_error_msg not like", value, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgIn(List<String> values) {
            addCriterion("aigc_error_msg in", values, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgNotIn(List<String> values) {
            addCriterion("aigc_error_msg not in", values, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgBetween(String value1, String value2) {
            addCriterion("aigc_error_msg between", value1, value2, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcErrorMsgNotBetween(String value1, String value2) {
            addCriterion("aigc_error_msg not between", value1, value2, "aigcErrorMsg");
            return (Criteria) this;
        }

        public Criteria andAigcStatusIsNull() {
            addCriterion("aigc_status is null");
            return (Criteria) this;
        }

        public Criteria andAigcStatusIsNotNull() {
            addCriterion("aigc_status is not null");
            return (Criteria) this;
        }

        public Criteria andAigcStatusEqualTo(String value) {
            addCriterion("aigc_status =", value, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusNotEqualTo(String value) {
            addCriterion("aigc_status <>", value, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusGreaterThan(String value) {
            addCriterion("aigc_status >", value, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusGreaterThanOrEqualTo(String value) {
            addCriterion("aigc_status >=", value, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusLessThan(String value) {
            addCriterion("aigc_status <", value, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusLessThanOrEqualTo(String value) {
            addCriterion("aigc_status <=", value, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusLike(String value) {
            addCriterion("aigc_status like", value, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusNotLike(String value) {
            addCriterion("aigc_status not like", value, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusIn(List<String> values) {
            addCriterion("aigc_status in", values, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusNotIn(List<String> values) {
            addCriterion("aigc_status not in", values, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusBetween(String value1, String value2) {
            addCriterion("aigc_status between", value1, value2, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcStatusNotBetween(String value1, String value2) {
            addCriterion("aigc_status not between", value1, value2, "aigcStatus");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdIsNull() {
            addCriterion("aigc_request_id is null");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdIsNotNull() {
            addCriterion("aigc_request_id is not null");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdEqualTo(String value) {
            addCriterion("aigc_request_id =", value, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdNotEqualTo(String value) {
            addCriterion("aigc_request_id <>", value, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdGreaterThan(String value) {
            addCriterion("aigc_request_id >", value, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdGreaterThanOrEqualTo(String value) {
            addCriterion("aigc_request_id >=", value, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdLessThan(String value) {
            addCriterion("aigc_request_id <", value, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdLessThanOrEqualTo(String value) {
            addCriterion("aigc_request_id <=", value, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdLike(String value) {
            addCriterion("aigc_request_id like", value, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdNotLike(String value) {
            addCriterion("aigc_request_id not like", value, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdIn(List<String> values) {
            addCriterion("aigc_request_id in", values, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdNotIn(List<String> values) {
            addCriterion("aigc_request_id not in", values, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdBetween(String value1, String value2) {
            addCriterion("aigc_request_id between", value1, value2, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andAigcRequestIdNotBetween(String value1, String value2) {
            addCriterion("aigc_request_id not between", value1, value2, "aigcRequestId");
            return (Criteria) this;
        }

        public Criteria andExtraIsNull() {
            addCriterion("extra is null");
            return (Criteria) this;
        }

        public Criteria andExtraIsNotNull() {
            addCriterion("extra is not null");
            return (Criteria) this;
        }

        public Criteria andExtraEqualTo(String value) {
            addCriterion("extra =", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotEqualTo(String value) {
            addCriterion("extra <>", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThan(String value) {
            addCriterion("extra >", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThanOrEqualTo(String value) {
            addCriterion("extra >=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThan(String value) {
            addCriterion("extra <", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThanOrEqualTo(String value) {
            addCriterion("extra <=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLike(String value) {
            addCriterion("extra like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotLike(String value) {
            addCriterion("extra not like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraIn(List<String> values) {
            addCriterion("extra in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotIn(List<String> values) {
            addCriterion("extra not in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraBetween(String value1, String value2) {
            addCriterion("extra between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotBetween(String value1, String value2) {
            addCriterion("extra not between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Date value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Date value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Date value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Date value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Date value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Date> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Date> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Date value1, Date value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Date value1, Date value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Date value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Date value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Date value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Date value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Date value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Date> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Date> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Date value1, Date value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Date value1, Date value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Byte value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Byte value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Byte value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Byte value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Byte value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Byte value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Byte> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Byte> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Byte value1, Byte value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Byte value1, Byte value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }
    }

    /**
     *
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {

        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }
    }
}