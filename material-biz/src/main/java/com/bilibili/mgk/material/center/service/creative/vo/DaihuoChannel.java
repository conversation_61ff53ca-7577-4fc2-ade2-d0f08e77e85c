package com.bilibili.mgk.material.center.service.creative.vo;


import java.util.Arrays;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum DaihuoChannel {


    /**
     * 淘宝
     */
    taobao("淘宝", 1),


    /**
     * 京东
     */
    jd("京东", 3),


    pdd("拼多多", 10),


    other("其他", null),

    non_daihuo("非带货", null),

    daihuo("带货", null),

    ;


    private String desc;

    private Integer code;





    public static DaihuoChannel fromDesc(String channel) {

        return Arrays.stream(DaihuoChannel.values()).filter(item -> item.getDesc().equals(channel))
                .findFirst()
                .orElse(null);
    }


    public static DaihuoChannel fromItemSourceCode(String itemSourceCode) {
        if (StringUtils.isEmpty(itemSourceCode)) {
            return non_daihuo;
        }

        return Arrays.stream(DaihuoChannel.values()).filter(item -> {
            return item.getCode() != null && item.getCode().toString().equals(itemSourceCode);
        }).findFirst().orElse(other);
    }

    DaihuoChannel(String desc, Integer code) {
        this.desc = desc;
        this.code = code;
    }
}
