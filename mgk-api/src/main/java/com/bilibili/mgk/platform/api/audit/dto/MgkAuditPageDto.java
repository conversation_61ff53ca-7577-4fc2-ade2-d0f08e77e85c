package com.bilibili.mgk.platform.api.audit.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName MgkAuditPageDto
 * <AUTHOR>
 * @Date 2022/12/9 9:04 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MgkAuditPageDto implements Serializable {
    private static final long serialVersionUID = 10121210L;

    private Integer accountId;

    private String accountName;

    private Long pageId;

    private Integer type;

    private Integer templateStyle;

    private Integer header;

    private String jumpUrl;

    private String jumpUrlSecondary;

    private String onlineJumpUrl;

    private String onlineJumpUrlSecondary;

    private Long sendAuditTime;

    private Long auditTime;

    private String auditorName;

    private Integer auditStatus;

    private String reason;

    private Integer originStatus;

    private Integer wechatPackageType;

    private Integer wechatPackageId;

    private String wechatPackageName;

    private List<Integer> wechatAccountIdList;

    private List<String> wechatAccountList;

    private List<Integer> appPackageIdList;

    private List<String> appPackageNameList;

    private Long shadowVersion;
}
