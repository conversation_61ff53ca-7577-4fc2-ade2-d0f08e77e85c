package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.mgk.platform.api.hot_video.dto.*;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoCollectService;
import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import com.bilibili.mgk.platform.biz.config.ClickHouseJDBCClient;
import com.bilibili.mgk.platform.biz.dmp.dao.BusinessCategoryDao;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * @file: HotVideoServiceDelegateTest
 * @author: gaoming
 * @date: 2020/11/16
 * @version: 1.0
 * @description:
 **/
public class HotVideoServiceDelegateTest extends BaseMockitoTest {

    @InjectMocks
    private HotVideoServiceDelegate hotVideoServiceDelegate;

    @Mock
    private ClickHouseJDBCClient clickHouseJDBCClient;

    @Mock
    private RedisTemplate<String, String> stringRedisTemplate;
    @Mock
    private ValueOperations valueOperations;

    @Mock
    private BusinessCategoryDao businessCategoryDao;

    @Mock
    private IPassportService passportService;

    @Mock
    private IHotVideoCollectService hotVideoCollectService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testGetBussInterest() {
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        List<HotVideoBussInterestDto> bussInterest = hotVideoServiceDelegate.getBussInterest();
    }

    @Test
    public void testGetTname() {
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        hotVideoServiceDelegate.getTname();
    }

    @Test
    public void testCancelCollect() {
        when(hotVideoCollectService.getCollects(any())).thenReturn(Lists.newArrayList(HotVideoCollectDto.builder()
                .isDeleted(0)
                .collectId(0L)
                .bvid("1")
                .accountId(1)
                .build()));
        hotVideoServiceDelegate.cancelCollect(operator, "1", 1);
    }
}