CREATE TABLE `lau_material_smart_crop_img`
(
    `id`                      bigint(20)              NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `origin_img_md5`         varchar(100) default '' not null comment '原图片md5',
    `origin_img_url`         varchar(500) default '' not null comment '原图片url',
    `origin_img_width`       int(11)      default 0  not null comment '原图片宽',
    `origin_img_height`      int(11)      default 0  not null comment '原图片高',
    `origin_img_ratio_type`  int(11)      default 0  not null comment '原图片宽高比类型 1=16比10, 2=16比9 3=4比3 4=145比12',

    `origin_img_material_id` bigint(20) default 0 not null comment '原图片素材id,预留字段，目前未赋值使用',


    `crop_img_md5`           varchar(100) default '' not null comment '裁切后图片md5',
    `crop_img_url`           varchar(500) default '' not null comment '裁切后图片url',
    `crop_img_width`         int(11)      default 0  not null comment '裁切后图片宽',
    `crop_img_height`        int(11)      default 0  not null comment '裁切后图片高',
    `crop_img_ratio_type`    int(11)      default 0  not null comment '裁切图片宽高比类型 1=16比10, 2=16比9 3=4比3 4=145比12',


    `crop_pos_left_top_x`     int(11)      default 0  not null comment '裁切左上角x坐标',
    `crop_pos_left_top_y`     int(11)      default 0  not null comment '裁切左上角y坐标',
    `crop_pos_right_bottom_x` int(11)      default 0  not null comment '裁切右下角x坐标',
    `crop_pos_right_bottom_y` int(11)      default 0  not null comment '裁切右下角y坐标',


    `extra` varchar(500) NOT NULL DEFAULT '' COMMENT '额外参数',
    `ctime`                   datetime                NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`                   datetime                NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`                 tinyint(4)              NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',
    `is_origin`               tinyint(4)              NOT NULL DEFAULT 0 COMMENT '是否是原图片，0 不是，1 是',
    PRIMARY KEY (`id`),
    KEY `idx_md5` (`origin_img_md5`) USING BTREE,
    UNIQUE KEY `idx_origin_md5_ratio` (`origin_img_md5`, `crop_img_ratio_type`) USING BTREE,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='智能裁切表';