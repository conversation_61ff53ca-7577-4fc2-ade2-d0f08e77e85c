package com.bilibili.mgk.platform.biz.exception;

import com.bilibili.adp.common.exception.IExceptionCode;

/**
 * @ClassName AccountExceptionCode
 * <AUTHOR>
 * @Date 2022/6/20 11:26 下午
 * @Version 1.0
 **/
public enum AccountExceptionCode implements IExceptionCode {
    SYSTEM_ERROR(10000, "系统异常"),
    REQUIRED_PARAM(10001, "参数非空"),
    ILLEGAL_PARAM(10002, "参数不合法"),
    NOT_EXIST_MOBILE(10003, "手机号不存在"),
    ILLEGAL_PASSWORD(10004, "密码不合法"),
    ILLEGAL_MOBILE(10005, "电话号码格式不对"),
    NOT_EXIST_ACCOUNT(10006, "账户不存在"),
    INCORRECT_PASSWORD(10007, "密码不正确"),
    ILLEGAL_CONFIRM_PASSWORD(10008, "密码与确认密码不同"),
    FAIL_OPERATION(10009, "操作失败"),
    NOT_ENOUGH_MONEY(10010, "金额不足"),
    ILLEGAL_CUSTOMER_ID(10011, "customer id 不合法"),
    EXIST_EMAIL(10012, "邮箱已注册"),
    EXIST_MOBILE(10013, "手机号已存在"),
    EXIST_CRM_USER(10014, "该用户已注册"),
    USER_IS_DISABLE(10015, "用户已停用"),
    HAS_CREATIVE_ORDER(10016, "尚有订单未完成"),
    CODE_IS_EXPIRE(10017, "验证码已过期"),
    CODE_IS_WRONG(10018, "验证码错误"),
    ILLEGAL_EMAIL(10019, "邮箱格式不合法"),
    EXIST_EMAIL_CODE(10020, "验证码已发送"),
    INIT_IMAGE_CODE_ERROR(10021, "生成验证码失败"),
    ILLEGAL_SERIAL_NUMBER(10022, "序列号无效"),
    USED_SERIAL_NUMBER(10023, "序列号已注册"),
    USER_CAN_NOT_ENABLE(10025, "该状态用户不能启用"),
    USER_CAN_NOT_DISABLE(10026, "该状态用户不能冻结"),
    HAS_NO_AUTH(10027, "没有权限"),
    EXIST_MID(10028, "mid已有商业账号"),
    NOT_EXIST_MID(10029, "mid不存在"),
    MIDS_CONTAIN_DUPLICATE_MID(10030, "存在重复的mid"),
    AGENT_ACCOUNT_NOT_FOUND(10031, "代理商账号不存在"),
    ACCOUNT_NOT_IN_USE(10032, "对不起，该客户账号尚未启用，请联系运营人员！"),

    ACCOUNT_GROUP_NO_EXIST(10100, "账户组不存在"),
    ACCOUNT_GROUP_NAME_IS_NULL(10101, "账户组名称不可为空"),
    ACCOUNT_GROUP_NAME_TOO_LONG(10102, "账户组名称不可超过16个字符"),
    ACCOUNT_GROUP_DESC_TOO_LONG(10103, "账户组描述不可超过32个字符"),
    ACCOUNT_GROUP_STATUS_NO_ALERT(10104, "账户组状态没有发生任何更改"),
    ACCOUNT_GROUP_ID_INVALID(10105, "账户组ID不合法"),
    ACCOUNT_GROUP_NAME_EXIST(10106, "该账户组名称已存在"),
    ACCOUNT_GD_QUOTA_ACCOUNT_ID_EXIST(10107, "该用户的配额配置已存在"),
    ACCOUNT_GD_QUOTA_ACCOUNT_ID_NOT_EXIST(10108, "用户配额配置中不存在此用户"),
    ACCOUNT_GD_QUOTA_ID_NOT_EXIST(10109, "用户配额配置不存在"),

    ACCOUNT_FLY_SETTLED(10110, "该订单已经支付过了"),
    ;

    private String message;
    private Integer code;

    AccountExceptionCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
