package com.bilibili.mgk.platform.biz.es;

import com.bilibili.adp.common.util.Md5Util;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @file: ESMgkClickCtrPo
 * @author: gaoming
 * @date: 2021/07/05
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
//@Document(indexName = "mgk_home_pv_ctr", type = "home_pv_ctr")
public class ESMgkHomePo implements Serializable {
    private static final long serialVersionUID = 6976579927286510275L;

//    @Id
    private String id;

//    @Field(type = FieldType.Keyword)
    private Long mgkPageId;

//    @Field(type = FieldType.Keyword)
    private Long userId;

//    @Field(type = FieldType.Long)
    private Long click;

//    @Field(type = FieldType.Long)
    private Long ctr;

//    @Field(type = FieldType.Date)
    private Timestamp groupDate;

    public void generateId() {
        String uniqueId = mgkPageId + "_" + groupDate;
        id = Md5Util.md5Hash(uniqueId);
    }
}
