package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkLandingPagePo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPagePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkLandingPageDao {
    long countByExample(MgkLandingPagePoExample example);

    int deleteByExample(MgkLandingPagePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkLandingPagePo record);

    int insertBatch(List<MgkLandingPagePo> records);

    int insertUpdateBatch(List<MgkLandingPagePo> records);

    int insert(MgkLandingPagePo record);

    int insertUpdateSelective(MgkLandingPagePo record);

    int insertSelective(MgkLandingPagePo record);

    List<MgkLandingPagePo> selectByExample(MgkLandingPagePoExample example);

    MgkLandingPagePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkLandingPagePo record, @Param("example") MgkLandingPagePoExample example);

    int updateByExample(@Param("record") MgkLandingPagePo record, @Param("example") MgkLandingPagePoExample example);

    int updateByPrimaryKeySelective(MgkLandingPagePo record);

    int updateByPrimaryKey(MgkLandingPagePo record);
}