package com.bilibili.mgk.material.center.service.creative.vo;

import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideoCurveType;
import com.bilibili.mgk.material.center.service.creative.model.UpAuthType;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery.DaihuoFilterType;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoPageExtra.CandidateDay;
import io.swagger.annotations.ApiModelProperty;
import io.vavr.Lazy;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/24
 */
@Data
@Accessors(chain = true)
public class RisingFastVideoQuery {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @ApiModelProperty("访问者商业账号id")
    private Long accountId;


    @ApiModelProperty(value = "页号")
    private Integer pn;
    @ApiModelProperty(value = "每页数量")
    private Integer ps;
    @ApiModelProperty("查询关键字类型，当前支持： composite综合；up_name UP主名称；item_name商品名称；brand_name品牌名称； video_composite视频综合 标题tag简介")
    private QueryKeywordType keywordType;
    @ApiModelProperty("查询关键字")
    private String keyword;
    @ApiModelProperty(value = "排序类型， 支持play,incr_play,follower,pubtime 分别为累计播放，增量播放， "
            + "粉丝数（聚合截止时间的粉丝数快照，而不是当前最新），发布时间")
    private MaterialSortBy sortBy;
    /**
     * 视频分区：稿件一级分区，默认全部，支持多选
     */
    @ApiModelProperty(value = "视频分区：稿件一级分区，默认全部，支持多选")
    private String videoFirstCategory;

    @ApiModelProperty("带货类型，全部、带货、非带货")
    private DaihuoFilterType daihuoType;

    @ApiModelProperty(value = "带货渠道： 全部、淘宝、天猫、京东，其他。默认全部 这个全部的语义是全部带货")
    private String daihuoChannelV2;

    /**
     * 带货渠道： 全部、淘宝、天猫、京东，其他、无带货。默认全部
     * TODO 注意和创意素材里面的itemSource区分？
     * {@link DaihuoChannel}
     */
    @ApiModelProperty(value = "带货渠道： 全部、淘宝、天猫、京东，其他、无带货。默认全部")
    private String daihuoChannel;
    /**
     * 带货商品分类：用带货一级分类，默认全部，支持多选
     */
    @ApiModelProperty(value = "带货商品分类：用带货一级分类，默认全部，支持多选")
    private String daihuoFirstCategory;


    @ApiModelProperty("非带货行业，注意使用中文的name， 当选择无带货 时可以提供")
    private List<String> commerceFirstCategoryNames;


    /**
     * 带货方式：全部、蓝链、非蓝链，默认全部
     */
    @ApiModelProperty("带货方式：全部、蓝链、非蓝链，默认全部")
    private DaihuoMethod daihuoMethod;
    /**
     * UP主类型：全部、普通、蓝V
     */
    @ApiModelProperty("UP主类型：全部、普通、蓝V")
    private UpAuthType upType;
    /**
     * 视频时长：0-15s、15s-30s、30s-60s、60s以上
     */
    @ApiModelProperty(value = "视频时长：0-15s、15s-30s、30s-60s、60s以上")
    private DurationSection durationSection;
    /**
     * 1 竖屏
     */
    @ApiModelProperty(value = "1 竖屏,  0 横屏")
    private Integer isVerticalScreen;
    @ApiModelProperty("不限、昨天、近3天、近7天、近15天、近30天。默认不限")
    private PubTimeSection publishTimeSection;


    @ApiModelProperty("商品id")
    private String itemId;


    /**
     * 播放量、点赞数、评论数，三者均支持区间筛选，默认不限 注意是左闭又闭， 保持两遍闭合， 如过要空置开闭，可以由前端控制
     */
    @ApiModelProperty("播放数大于等于")
    private Long playFrom;

    //    @ApiModelProperty("增量播放数大于等于")
//    private Long incrPlayFrom;
//
//
//    @ApiModelProperty("增量播放数小于等于")
//    private Long incrPlayTo;
    @ApiModelProperty("播放数小于等于")
    private Long playTo;
    @ApiModelProperty("点赞数大于等于")
    private Long likeFrom;
    @ApiModelProperty("点赞数小于等于")
    private Long likeTo;
    @ApiModelProperty("回复数大于等于")
    private Long replyFrom;
    @ApiModelProperty("回复数小于等于")
    private Long replyTo;
    @ApiModelProperty("聚合天数， 可选参数 1d, 7d, 30d, 分别代表日榜周榜月榜")
    private DateAggregation dayType;
    /**
     * 聚合起始日期，允许为空，为空时，根据dayType选择服务端就绪的最新的天数据
     */
    @ApiModelProperty("聚合起始日期，格式yyyy-MM-dd允许为空，为空时，根据dayType选择服务端就绪的最新的天数据。")
    private String dayFrom;
    /**
     * 聚合结束日期，允许为空，为空时，根据dayType选择服务端就绪的最新的天数据
     */
    @ApiModelProperty("聚合结束日期，格式yyyy-MM-dd， 允许为空，为空时，根据dayType选择服务端就绪的最新的天数据")
    private String dayTo;


    @ApiModelProperty("avid")
    private Long avid;


    // 注意是秒！！！
    @ApiModelProperty("发布时间 单位毫秒， 可选参数，如果请求时1. 携带avid 且 2. dayType=30d_after_creation 则需要提供")
    private Lazy<Long> pubtime;


    /**
     * 正常都是groupBy avid 不需要额外说明
     */
    private boolean queryCurve = false;


    private RisingFastVideoCurveType curveType;

    public void validate() {

        Assert.notNull(sortBy, "sortBy不能为空");
        Assert.notNull(dayType, "dayType不能为空");

        sortBy.validateRisingFastVideoQuery();

        dayType.validateRisingFastVideoPageQuery(StringUtils.isNotEmpty(itemId));

        this.validateRange(playFrom, playTo);
        this.validateRange(likeFrom, likeTo);
        this.validateRange(replyFrom, replyTo);
    }


    private void validateRange(Long from, Long to) {
        if (from == null && to == null) {
            return;
        }

        if (from != null) {
            Assert.isTrue(from >= 0, "from必须大于等于0");
        }
        if (to != null) {
            Assert.isTrue(to >= 0, "to必须大于等于0");
        }

        if (from != null && to != null) {
            Assert.isTrue(from <= to, "from必须小于等于to");
        }

    }


    public Tuple2<LocalDate, LocalDate> fetchDayTimeRange(LocalDate readyLogdate) {
        return this.fetchDayTimeRange(readyLogdate, null);
    }

    /**
     * 返回查询logdate的开始结束日期，注意左右都包含
     *
     * @param readyLogdate
     * @return
     */
    public Tuple2<LocalDate, LocalDate> fetchDayTimeRange(LocalDate readyLogdate,  Lazy<Long> pubtime) {

        if (StringUtils.isNotEmpty(dayFrom) && StringUtils.isNotEmpty(dayTo)) {

            LocalDate timeFrom = Try.of(() -> LocalDate.parse(dayFrom, formatter)).getOrElseThrow(
                    t -> new IllegalArgumentException("dayFrom和dayTo日期格式解析出错，请使用yyyy-MM-dd格式"));
            LocalDate timeTo = Try.of(() -> LocalDate.parse(dayTo, formatter)).getOrElseThrow(
                    t -> new IllegalArgumentException("dayFrom和dayTo日期格式解析出错，请使用yyyy-MM-dd格式"));

            dayType.validateDateRange(timeFrom, timeTo);

            // 都不为空
            return Tuple.of(timeFrom, timeTo);

        } else if (StringUtils.isEmpty(dayFrom) && StringUtils.isEmpty(dayTo)) {

            return dayType.fetchDateBetween(readyLogdate, 1, pubtime).get(0);

        } else {
            throw new IllegalArgumentException("日期选择参数dayFrom或dayTo有缺失");
        }


    }


    public List<CandidateDay> fetchCandidateDaysByReadyLogdate(LocalDate readyLogdate) {

        return dayType.fetchDateBetween(readyLogdate, Try.of(() -> {
            switch (dayType) {
                case d1: {
                    // 当选中日榜时支持点击下拉查看最近7天的分日榜。
                    //当选中周榜时，支持点击下拉查看最近四周的时间窗的周榜，。
                    //当选中月榜时，支持点击下拉查看过去3个月的月榜，
                    return 7;
                }
                case d7: {
                    return 4;
                }

                case month: {
                    return 3;
                }
                default: {
                    throw new IllegalArgumentException("不支持的dayType类型");
                }
            }
        }).get(), Lazy.of(() -> null)).stream().map(tuple -> {

            return new CandidateDay(dayType, tuple._1(), tuple._2(), formatter);

        }).collect(Collectors.toList());

    }




}
