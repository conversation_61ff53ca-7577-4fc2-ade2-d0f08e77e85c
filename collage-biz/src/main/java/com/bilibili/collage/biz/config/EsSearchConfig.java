package com.bilibili.collage.biz.config;


import com.alibaba.fastjson.JSON;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.brand.api.schedule.dto.WeiXinGdInfoDTO;
import com.bilibili.brand.api.schedule.dto.WeiXinTextDTO;
import com.bilibili.collage.api.dto.PicDownloadInfoBo;
import com.bilibili.collage.biz.entity.EsDSLResponse;
import com.bilibili.cpt.platform.util.GsonUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Getter
public class EsSearchConfig {

    @Autowired
    private OkHttpClient okHttpClient;
    @Value("${mgk.bilibili.es.url:http://uat-olap-search.bilibili.co/v3/proxy}")
    private String esUrl;

    @Value("${mgk.es.token:sDRF2mzwJ6iyQ3wnl7viGInO5ZkmDegWlNN3ALQkISM}")
    private String esSearchToken;
    /**
     * 落地页表token
     */
    @Value("${mgk.landing.page.es.token:JzydWGKiyY-tJIuD4ZGGext-isFvkGe4AgNc5c-YHg8}")
    private String mgkLandingPageEsSearchToken;
    /**
     * 落地页表索引名称
     */
    @Value("${mgk.landing.page.es.index:bilisearch-mgk-landing-page-index-@-s-v3}")
    private String mgkLandingPageEsIndex;




    @Deprecated
    public EsDSLResponse getDSLSearchResult(String result,String business) {
        String url = esUrl +"/"+business+"/_search";
        Request request = new Request.Builder()
                .header(HttpHeaders.CONNECTION, "close")
                .header("AuthToken",esSearchToken)
                .url(url)
                .post(RequestBody.create(OkHttpUtils.JSON, result))
                .build();

        try (Response response = okHttpClient.newCall(request).execute();) {
            String res =  response.body().string();
            return  JSON.parseObject(res,EsDSLResponse.class);
        } catch (Exception e) {
            log.error("search ES error,e:{}", result, ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    public EsDSLResponse getDSLSearchResult(String result, String business, String esSearchToken) {
        String url = esUrl + "/" + business + "/_search";
        Request request = new Request.Builder()
                .header("AuthToken", esSearchToken)
                .url(url)
                .post(RequestBody.create(OkHttpUtils.JSON, result))
                .build();

        try (Response response = okHttpClient.newCall(request).execute();) {
            String res = response.body().string();
            return JSON.parseObject(res, EsDSLResponse.class);
        } catch (Exception e) {
            log.error("search ES error, {}, e: {}", result, ExceptionUtils.getStackTrace(e));
            return null;
        }
    }


}
