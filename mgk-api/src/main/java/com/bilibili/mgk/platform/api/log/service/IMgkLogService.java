package com.bilibili.mgk.platform.api.log.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.log.dto.MgkLogOperationDto;
import com.bilibili.mgk.platform.api.log.dto.NewLogOperationDto;
import com.bilibili.mgk.platform.api.log.dto.QueryLogParamDto;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/1/19
 **/
public interface IMgkLogService {

    void insertLog(NewLogOperationDto newLogOperationDto);

    void batchInsertLog(List<NewLogOperationDto> newLogOperationDtos);

    PageResult<MgkLogOperationDto> queryOperationLogs(QueryLogParamDto queryLogParamDto);
}
