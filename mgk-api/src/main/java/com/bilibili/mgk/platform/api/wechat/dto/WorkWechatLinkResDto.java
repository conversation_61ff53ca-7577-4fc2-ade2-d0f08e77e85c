package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WorkWechatLinkResDto implements Serializable {


    private static final long serialVersionUID = 931051185911713433L;
    /**
     * 获客链接id
     */
    private String link_id;

    /**
     * 获客链接名称
     */
    private String link_name;


    /**
     * 获客链接
     */
    private String url;

    /**
     * 获客链接创建时间
     */
    private Long create_time;

}
