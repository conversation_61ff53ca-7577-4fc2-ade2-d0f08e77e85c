package com.bilibili.mgk.platform.biz.service.third_party.page.delegate;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingListDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.MgkThirdPartyPageCreateDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.MgkThirdPartyPageDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.MgkThirdPartyPageUpdateDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.QueryMgkThirdPartyPageDto;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkThirdPartyLandingPageQueryDSLPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkThirdPartyLandingPage.mgkThirdPartyLandingPage;

/**
 * @ClassName MgkThirdPartyPageServiceDelegate
 * <AUTHOR>
 * @Date 2023/5/19 3:49 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkThirdPartyPageServiceDelegate {

    private final BaseQueryFactory bqf;
    private final SnowflakeIdWorker snowflakeIdWorker;

    MgkThirdPartyPageServiceDelegate(BaseQueryFactory bqf,
                                     SnowflakeIdWorker snowflakeIdWorker) {
        this.bqf = bqf;
        this.snowflakeIdWorker = snowflakeIdWorker;
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public List<LandingPageGroupMappingListDto> saveThirdPartyPage(List<LandingPageGroupMappingListDto> mappingList, Operator operator) {
        List<LandingPageGroupMappingListDto> insertMappingList = mappingList.stream()
                .filter(mappingDto -> !Utils.isPositive(mappingDto.getPageId()))
                .collect(Collectors.toList());
        List<MgkThirdPartyPageCreateDto> createDtoList = insertMappingList.stream()
                .map(mappingDto -> MgkThirdPartyPageCreateDto.builder()
                        .url(mappingDto.getPageUrl())
                        .name(mappingDto.getName())
                        .build()).collect(Collectors.toList());
        List<MgkThirdPartyPageUpdateDto> updateDtoList = mappingList.stream()
                .filter(mappingDto -> Utils.isPositive(mappingDto.getPageId()))
                .map(mappingDto -> MgkThirdPartyPageUpdateDto.builder()
                        .pageId(mappingDto.getPageId())
                        .name(mappingDto.getName())
                        .build()).collect(Collectors.toList());
        List<Long> insertPageIds = insertThirdPartyPage(createDtoList, operator);
        updateThirdPartyPageList(updateDtoList);
        // 填充主键
        if (!CollectionUtils.isEmpty(insertPageIds)) {
            IntStream.range(0, insertPageIds.size()).forEach(index -> {
                insertMappingList.get(index).setPageId(insertPageIds.get(index));
            });
        }
        return mappingList;
    }

    private List<Long> insertThirdPartyPage(List<MgkThirdPartyPageCreateDto> createDtoList, Operator operator) {
        if (CollectionUtils.isEmpty(createDtoList)) {
            return Collections.emptyList();
        }
        List<Long> pageIdList = new ArrayList<>();
        List<MgkThirdPartyLandingPageQueryDSLPo> insertPoList = createDtoList.stream()
                .map(createDto -> {
                    long pageId = snowflakeIdWorker.nextId();
                    pageIdList.add(pageId);
                    MgkThirdPartyLandingPageQueryDSLPo insertPo = new MgkThirdPartyLandingPageQueryDSLPo();
                    insertPo.setAccountId(operator.getOperatorId());
                    insertPo.setCreator(operator.getOperatorName());
                    insertPo.setPageName(createDto.getName());
                    insertPo.setPageUrl(createDto.getUrl());
                    insertPo.setPageId(pageId);
                    return insertPo;
                }).collect(Collectors.toList());
        bqf.insert(mgkThirdPartyLandingPage)
                .insertBeans(insertPoList);
        return pageIdList;
    }

    private void updateThirdPartyPageList(List<MgkThirdPartyPageUpdateDto> updateDtoList) {
        if (CollectionUtils.isEmpty(updateDtoList)) {
            return;
        }
        validateUpdateDtoList(updateDtoList);
        updateDtoList.forEach(updateDto ->
                bqf.update(mgkThirdPartyLandingPage)
                        .set(mgkThirdPartyLandingPage.pageName, updateDto.getName())
                        .where(mgkThirdPartyLandingPage.pageId.eq(updateDto.getPageId()))
                        .execute());
    }

    private void validateUpdateDtoList(List<MgkThirdPartyPageUpdateDto> updateDtoList) {
        updateDtoList.forEach(updateDto -> {
            Assert.isTrue(Utils.isPositive(updateDto.getPageId()), "更新落地页id不可为空");
            Assert.isTrue(!StringUtils.isEmpty(updateDto.getName()), "更新落地页名称不可为空");
        });
    }

    public List<MgkThirdPartyPageDto> queryMgkThirdPartyPageList(QueryMgkThirdPartyPageDto queryDto) {
        return generateBaseQuery(queryDto)
                .fetch(MgkThirdPartyPageDto.class);
    }

    private BaseQuery<MgkThirdPartyLandingPageQueryDSLPo> generateBaseQuery(QueryMgkThirdPartyPageDto queryDto) {
        return bqf.selectFrom(mgkThirdPartyLandingPage)
                .where(mgkThirdPartyLandingPage.pageId.in(queryDto.getPageIdList()))
                .whereIfTrue(!Boolean.TRUE.equals(queryDto.getNeedIsDeleted()),
                        () -> mgkThirdPartyLandingPage.isDeleted.eq(IsDeleted.VALID.getCode()));
    }

    public int countMgkThirdPartyPage(QueryMgkThirdPartyPageDto queryDto) {
        Long count = generateBaseQuery(queryDto).fetchCount();
        return count.intValue();
    }

}
