package com.bilibili.mgk.material.center.service.creative.impl;

import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.mgk.material.center.config.MaterialWatermarkConfig;
import com.bilibili.mgk.material.center.event.CreativeMaterialDataReadyEvent;
import com.bilibili.mgk.material.center.event.HotBiliVideoDataReadyEvent;
import com.bilibili.mgk.material.center.event.LandingPageDataReadyEvent;
import com.bilibili.mgk.material.center.facade.CreativeMaterialFacade;
import com.bilibili.mgk.material.center.facade.HotBiliVideoFacadeV2;
import com.bilibili.mgk.material.center.facade.LandingPageFacade;
import com.bilibili.mgk.material.center.repository.mysql.MaterialWatermarkMapper;
import com.bilibili.mgk.material.center.service.creative.WatermarkService;
import com.bilibili.mgk.material.center.service.creative.impl.WatermarkJobs.GifWatermarkJob;
import com.bilibili.mgk.material.center.service.creative.impl.WatermarkJobs.UploadWatermarkImgJob;
import com.bilibili.mgk.material.center.service.creative.impl.WatermarkJobs.WatermarkJob;
import com.bilibili.mgk.material.center.service.creative.impl.WatermarkJobs.WatermarkPersistJob;
import com.bilibili.mgk.material.center.service.creative.impl.WatermarkJobs.WebpWatermarkJob;
import com.bilibili.mgk.material.center.service.creative.model.CreativeMaterial;
import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideo;
import com.bilibili.mgk.material.center.service.creative.model.HotLandingPage;
import com.bilibili.mgk.material.center.service.creative.model.MaterialWatermark;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.awt.image.BufferedImage;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/1
 */
@Slf4j
@Service
public class WatermarkServiceImpl implements WatermarkService {

    private final ExecutorService watermarkScanTaskExecutor = Executors.newFixedThreadPool(10);
    @Resource
    private IBfsService bfsService;
    @Resource
    private MaterialWatermarkConfig watermarkConfig;
    @Resource
    private MaterialWatermarkMapper watermarkMapper;
    @Resource
    private HotBiliVideoFacadeV2 hotBiliVideoFacadeV2;
    @Resource
    private CreativeMaterialFacade creativeMaterialQueryFacade;

    @Resource
    private LandingPageFacade landingPageFacade;

    @Resource
    private RedissonClient redissonClient;
    private ExecutorService watermarkImgUploaderExecutor;

    private ExecutorService watermarkedImgExecuteExecutor;





    @PostConstruct
    public void init() {
        watermarkedImgExecuteExecutor = Executors.newFixedThreadPool(watermarkConfig.getWatermarkTaskThreadPoolSize());
        watermarkImgUploaderExecutor = Executors.newFixedThreadPool(
                watermarkConfig.getWatermarkTaskUploadThreadPoolSize());
    }

    @EventListener(CreativeMaterialDataReadyEvent.class)
    public void startWaterMarkerTaskOfCreativeMaterial(CreativeMaterialDataReadyEvent event) {

        Iterator<List<CreativeMaterial>> iterator = creativeMaterialQueryFacade.scan(
                event.getLogdate(), event.getLogdateDate());

        this.iterateGenerateWatermarkImg("creativeMaterial", iterator, CreativeMaterial::getImageUrl,
                CreativeMaterial::getImageMd5);
    }

    @EventListener(LandingPageDataReadyEvent.class)
    public void startWaterMarkerTaskOfLandingPage(LandingPageDataReadyEvent event) {

        Iterator<List<HotLandingPage>> iterator = landingPageFacade.scan(
                event.getLogdate(), event.getLogdateDate());

        this.iterateGenerateWatermarkImg("landingPage",
                iterator,
                // TODO url可能过长影响索引效率，未来可进行优化
                HotLandingPage::getPageCover,
                HotLandingPage::getPageCover);
    }


    @EventListener(HotBiliVideoDataReadyEvent.class)
    public void awareWatermarkTaskOfHotBiliVideo(HotBiliVideoDataReadyEvent event) {

        Iterator<List<HotBiliVideo>> iterator = hotBiliVideoFacadeV2.scan(event.getLogdateFrom());

        this.iterateGenerateWatermarkImg("hotBiliVideo", iterator, HotBiliVideo::getCover, HotBiliVideo::getCover);

    }


    private <T> void iterateGenerateWatermarkImg(
            String taskName,
            Iterator<List<T>> iterator, Function<T, String> urlGetter, Function<T, String> md5Getter
    ) {

        watermarkScanTaskExecutor.submit(() -> {

            if (!lockTask(taskName)) {
                log.info("Watermark task={}, already running, skip this round", taskName);
                return;
            }

            long startTs = System.currentTimeMillis();

            try {
                log.info("Start to run watermark task={}", taskName);

                AtomicLong scanCnt = new AtomicLong();

                // scan = skip + success + fail
                AtomicLong skipCnt = new AtomicLong();

                AtomicLong processCnt = new AtomicLong();

                AtomicLong successCnt = new AtomicLong();

                AtomicLong failCnt = new AtomicLong();

                AtomicLong round = new AtomicLong();

                while (iterator.hasNext()) {

                    long processTs = System.currentTimeMillis();

                    List<T> videos = Try.of(iterator::next)
                            .onFailure(t -> {
                                log.error("Fail to scan next batch watermark job ", t);
                            }).get();

                    Try.run(() -> {

                        scanCnt.addAndGet(videos.size());

                        if (CollectionUtils.isEmpty(videos)) {
                            return;
                        }

                        Map<String, String> existedWatermarkImages = watermarkMapper
                                .selectAllByImgMd5(videos.stream().map(md5Getter)
                                        .collect(Collectors.toList()))
                                .stream()
                                .collect(Collectors.toMap(
                                        MaterialWatermark::getImgMd5,
                                        MaterialWatermark::getImgWmUrl,
                                        (v1, v2) -> v1
                                ));

                        skipCnt.addAndGet(existedWatermarkImages.size());

                        List<WatermarkImgInput> batch = videos
                                .stream()
                                .filter(video -> !existedWatermarkImages.containsKey(md5Getter.apply(video)))
                                .map(video -> {
                                    return new WatermarkImgInput(urlGetter.apply(video), md5Getter.apply(video), 0);
                                }).collect(Collectors.toList());

                        processCnt.addAndGet(batch.size());

                        if (CollectionUtils.isEmpty(batch)) {
                            return;
                        }

                        WatermarkJobFuture batchFuture = this.batchGenerateWaterMarkedImg(batch);

                        // TODO 理论上不会阻塞可以一直blocking
                        List<String> successUrls = batchFuture.getPersistenceFuture().get();

                        successCnt.addAndGet(successUrls.size());
                        failCnt.addAndGet(batch.size() - successUrls.size());

                        log.info(
                                "Complete watermark task one round={}, current scanCnt={}, skipCnt={}, "
                                        + "processCnt={}, successCnt={}, failCnt={}",
                                round.incrementAndGet(),
                                scanCnt.get(), skipCnt.get(), processCnt.get(), successCnt.get(), failCnt.get());
                    }).onFailure(t -> {
                        log.error("Fail to generate watermark img for videos={}", videos, t);
                    });

                    Try.run(() -> {
                        Thread.sleep(Math.max(1, 1000 + processTs - System.currentTimeMillis()));
                    });

                }

                log.info(
                        "Finish run watermark task of {}, round={}, current scanCnt={}, skipCnt={}, processCnt={}, "
                                + "successCnt={}, failCnt={}, cost time={} millis",
                        taskName, round.incrementAndGet(),
                        scanCnt.get(), skipCnt.get(), processCnt.get(), successCnt.get(), failCnt.get(),
                        System.currentTimeMillis() - startTs);
            } finally {
                unlockTask(taskName);
            }
        });


    }


    private WatermarkJobFuture batchGenerateWaterMarkedImg(
            List<WatermarkImgInput> originImg5) {

        List<Tuple2<WatermarkImgInput, CompletableFuture<String>>> allUrlFutures = originImg5
                .stream()
                .map(img -> {

                    String formatV1 = FilenameUtils.getExtension(img.getImgUrl());

                    String format = Try.of(() -> {

                        // FIXME 静态webp的水印任务 https://i0.hdslb.com/bfs/sycp/mgk/img/202411/5d692ab93dfc1b1704391a4b5005a64a.png@468w_833h.webp
                        // 如果是形如这样的wepb那么不是动画webp而是静态压缩图的webp
                        Matcher matcher = watermarkConfig.getNoAnimationWebpRegexPattern()
                                .matcher(img.getImgUrl());
                        if (matcher.matches()) {
                            return FilenameUtils.getExtension(matcher.group(1));
                        }
                        return formatV1;
                    }).getOrElse(formatV1);

                    // stage1 resize+ 打水印
                    CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {

                        if ("webp".equalsIgnoreCase(format)) {

                            return new WebpWatermarkJob(
                                    watermarkConfig.getWatermarkFilePath(),
                                    img.getImgUrl(),
                                    watermarkConfig.getWatermarkCntHorizontal(),
                                    watermarkConfig.getWebpDecodeFrameCommand(),
                                    watermarkConfig.getWebpEncodeFrameCommand(),
                                    watermarkConfig.getWebpAddFrameArg(),
                                    watermarkConfig.getWebpFrameDir()
                            ).call();
                        } else if ("gif".equalsIgnoreCase(format)) {
                            return new GifWatermarkJob(
                                    watermarkConfig.getWatermarkFilePath(),
                                    img.getImgUrl(),
                                    watermarkConfig.getWatermarkCntHorizontal(),
                                    watermarkConfig.getGifDecodeFrameCommand(),
                                    watermarkConfig.getGifEncodeFrameCommand(),
                                    watermarkConfig.getGifFrameDir()

                            ).call();
                        } else {
                            return new WatermarkJob(

                                    img.getImgUrl(),
                                    false,
                                    new WatermarkTaskResizeParam(
                                            true,
                                            watermarkConfig.getWatermarkFilePath(),
                                            watermarkConfig.getWatermarkCntHorizontal(),
                                            null
                                    ),
                                    format

                            ).call();
                        }

                    }, watermarkedImgExecuteExecutor).thenApplyAsync(watermarkImg -> {


                        return new UploadWatermarkImgJob(
                                bfsService,
                                watermarkImg,
                                String.format(watermarkConfig.getFilenameTpl(), img.getImgMd5(), format),
                                watermarkConfig.getUploadRetryEnabled(),
                                watermarkConfig.getUploadRetryAttempts(), watermarkConfig.getUploadRetrySleepMillis()
                        ).call();

                    }, watermarkImgUploaderExecutor);

                    return Tuple.of(img, future);


                })
                .collect(Collectors.toList());

        // 这么做可以不等持久化的结果直接返回

        CompletableFuture<List<String>> persistenceSuccessFuture = new CompletableFuture<>();

        CompletableFuture
                .allOf(allUrlFutures.stream()
                        .map(tuple -> tuple._2)
                        .collect(Collectors.toList())
                        .toArray(new CompletableFuture[]{}))
                .whenCompleteAsync((r, t) -> {
                    // on all task success ,
                    List<Tuple2<WatermarkImgInput, String>> watermarkResult = allUrlFutures
                            .stream()
                            .map(future -> Try
                                    .of(() -> Tuple.of(future._1, future._2.get()))
                                    .onFailure(t2 -> {
                                        log.error("Detect failure watermark job, skip persist, url={} ",
                                                future._1.getImgUrl(), t2);
                                    }).getOrNull())
                            .filter(Objects::nonNull).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(watermarkResult)) {
                        persistenceSuccessFuture.complete(Collections.emptyList());
                        return;
                    }

                    persistenceSuccessFuture.complete(new WatermarkPersistJob(watermarkResult, watermarkMapper).call());


                }, watermarkedImgExecuteExecutor);

        return new WatermarkJobFuture()
                .setPersistenceFuture(persistenceSuccessFuture)
                .setWatermarkAndUploadFutures(allUrlFutures);

    }


    @Override
    public Map<String, String> forceOverwriteWatermarkImg(List<String> originMd5s) {

        if(CollectionUtils.isEmpty(originMd5s)) {
            return Collections.emptyMap();
        }

        List<MaterialWatermark> existedWatermark = watermarkMapper.selectAllByImgMd5(originMd5s);

        if(CollectionUtils.isEmpty(existedWatermark)) {
            return Collections.emptyMap();
        }

        return this.queryWaterMarkedImgByOriginImgMd5(
                existedWatermark.stream()
                        .map(watermark -> {
                            return new WatermarkImgInput(watermark.getImgUrl(), watermark.getImgMd5(), 0)
                                    .setForceOverwrite(true);
                        }).collect(Collectors.toList()), true
        );


    }

    @Override
    public Map<String, String> queryWaterMarkedImgByOriginImgMd5(List<WatermarkImgInput> imgMd5s,
            boolean generateIfNotExisted) {

        if (CollectionUtils.isEmpty(imgMd5s)) {
            return Collections.emptyMap();
        }

        Map<String, String> existedWatermarkImages = watermarkMapper
                .selectAllByImgMd5(imgMd5s.stream().map(WatermarkImgInput::getImgMd5)
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(
                        MaterialWatermark::getImgMd5,
                        MaterialWatermark::getImgWmUrl,
                        (v1, v2) -> v1
                ));

        List<WatermarkImgInput> absentIds = imgMd5s
                .stream()
                .filter(md5 -> {
                    return md5.isForceOverwrite() || !existedWatermarkImages.containsKey(md5.getImgMd5());
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(absentIds) || !generateIfNotExisted) {
            // 全部查到了，或者不开启读扩散
            return existedWatermarkImages;
        }

        log.warn("Found img5 with no watermark url, plz check watermark sync task, absent size={}, data={}",
                absentIds.size(), absentIds);

        List<Tuple2<WatermarkImgInput, CompletableFuture<String>>> tasks = this.batchGenerateWaterMarkedImg(absentIds)
                .getWatermarkAndUploadFutures();

        // 同步等待结果，会相应的阻塞http请求， 考虑到benchmark 此处直接等待
        Map<String, String> result = new HashMap<>(existedWatermarkImages);

        tasks.stream().forEach(task -> {

            String watermarkImgUrl = Try.of(() -> {
                return task._2.get(watermarkConfig.getWatermarkTaskTimeoutMillis(), TimeUnit.MILLISECONDS);
            }).onFailure(t -> {
                log.error("Fail to get watermark img url of img={}, cause", task._1, t);
            }).getOrNull();

            // 如果存在forceUpdate会覆盖为update的
            if (watermarkImgUrl != null) {
                result.put(task._1.getImgMd5(), watermarkImgUrl);
            }

        });

        return result;

    }

    private boolean lockTask(String taskName) {

        return Try.of(() -> {
            redissonClient.getLock(String.format(watermarkConfig.getWatermarkLockKeyTpl(), taskName))
                    .lock(watermarkConfig.getWatermarkTaskLockTimeoutMillis(), TimeUnit.MILLISECONDS);
            return true;
        }).getOrElse(false);

    }

    private void unlockTask(String taskName) {
        redissonClient.getLock(String.format(watermarkConfig.getWatermarkLockKeyTpl(), taskName))
                .unlock();
    }

    @Data
    @Accessors(chain = true)
    private static class WatermarkJobFuture {

        private List<Tuple2<WatermarkImgInput, CompletableFuture<String>>> watermarkAndUploadFutures;

        private CompletableFuture<List<String>> persistenceFuture;

    }

    @Data
    @Accessors(chain = true)
    public static class WatermarkImgInput implements SnakeCaseBody {

        private final String imgUrl;

        private final String imgMd5;

        private final Integer imgSize;

        private boolean forceOverwrite = false;


        public WatermarkImgInput(String imgUrl, String imgMd5, Integer imgSize) {
            this.imgUrl = imgUrl;
            this.imgMd5 = imgMd5;
            this.imgSize = imgSize;
        }


    }


    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WatermarkTaskResizeParam {

        boolean resizeOriginImageAndFixWatermarkCntInHorizontal;


        String watermarkFileResourcePath;

        Integer drawWatermarkCntInHorizontal;

        BufferedImage resizeWatermarkImage;

    }
}
