package com.bilibili.mgk.platform.api.video_library.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MgkVideoTagBo implements Serializable {
    private static final long serialVersionUID = 1L;

    private java.sql.Timestamp firstUploadTime;
    private Integer id;
    private String md5;
    private Integer provider;
    private Integer source;
    private Integer style;
    private String rawMd5;
}

