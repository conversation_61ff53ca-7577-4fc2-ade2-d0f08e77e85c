package com.bilibili.collage.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 下载记录
 * <AUTHOR>
 * @date 2019/1/8
 **/
@Data
@Builder
@AllArgsConstructor
public class CollageDownloadDto implements Serializable {

    private static final long serialVersionUID = -3873306220934858939L;

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 主站mid
     */
    private Long mid;

    /**
     * 素材地址
     */
    private String url;
}
