package com.bilibili.collage.biz.dao;

import com.bilibili.collage.biz.po.MgkCollageSizePo;
import com.bilibili.collage.biz.po.MgkCollageSizePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkCollageSizeDao {
    long countByExample(MgkCollageSizePoExample example);

    int deleteByExample(MgkCollageSizePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkCollageSizePo record);

    int insertBatch(List<MgkCollageSizePo> records);

    int insertUpdateBatch(List<MgkCollageSizePo> records);

    int insert(MgkCollageSizePo record);

    int insertUpdateSelective(MgkCollageSizePo record);

    int insertSelective(MgkCollageSizePo record);

    List<MgkCollageSizePo> selectByExample(MgkCollageSizePoExample example);

    MgkCollageSizePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkCollageSizePo record, @Param("example") MgkCollageSizePoExample example);

    int updateByExample(@Param("record") MgkCollageSizePo record, @Param("example") MgkCollageSizePoExample example);

    int updateByPrimaryKeySelective(MgkCollageSizePo record);

    int updateByPrimaryKey(MgkCollageSizePo record);
}