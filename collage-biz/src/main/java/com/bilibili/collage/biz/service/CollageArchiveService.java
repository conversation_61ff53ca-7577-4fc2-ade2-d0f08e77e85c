package com.bilibili.collage.biz.service;

import com.bapis.archive.service.Arc;
import com.bapis.archive.service.ArchiveGrpc;
import com.bapis.archive.service.ArcsRequest;
import io.grpc.Channel;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.component.ecode.ServerCode;
import pleiades.component.rpc.core.StatusCode;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName ArchiveService
 * <AUTHOR>
 * @Date 2025/5/15 2:55 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class CollageArchiveService {

    public static final String ARCHIVE_CHANNEL = "archiveChannel";

    @Resource(name = ARCHIVE_CHANNEL)
    private Channel archiveChannel;

    public Map<Long, Arc> getArcMap(List<Long> avidList) {
        if (CollectionUtils.isEmpty(avidList)) {
            return Collections.emptyMap();
        }
        try {
            ArchiveGrpc.ArchiveBlockingStub archiveBlockingStub = ArchiveGrpc.newBlockingStub(archiveChannel);
            return archiveBlockingStub
                    .withDeadlineAfter(2000L, TimeUnit.MILLISECONDS)
                    .withWaitForReady()
                    .arcs(ArcsRequest.newBuilder()
                            .addAllAids(avidList)
                            .build()).getArcsMap();
        } catch (Exception e) {
            log.warn("grpc failed :{}", e.getMessage());
            if(e instanceof StatusRuntimeException) {
                StatusRuntimeException exception = (StatusRuntimeException)e;
                ServerCode code = StatusCode.toServerCode(exception.getStatus(), exception.getTrailers());
                log.error("grpc archiveService error! code={}, msg={}", code.getCode(), code.getMessage());
            }
            return Collections.emptyMap();
        }
    }

}
