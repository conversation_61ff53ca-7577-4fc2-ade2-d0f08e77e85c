package com.bilibili.mgk.platform.common.utils;

import com.bilibili.adp.common.util.Utils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.function.Consumer;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-01-29
 **/
public class ExampleUtils {

    public static  <T> void notNull(T value, Consumer<T> consumer){
        if (value != null){
            consumer.accept(value);
        }
    }

    public static <T> void notEmpty(List<T> list, Consumer<List<T>> consumer){
        if (!CollectionUtils.isEmpty(list)){
            consumer.accept(list);    }
    }

    public static void isPositiveOrNull(Integer value, Consumer<Integer> consumer){
        if (Utils.isPositive(value)){
            consumer.accept(value);
        }else {
            consumer.accept(null);
        }
    }

    public static void isPositiveOrNull(Long value, Consumer<Long> consumer){
        if (Utils.isPositive(value)){
            consumer.accept(value);
        }else {
            consumer.accept(null);
        }
    }

    public static void main(String[] args) {
        System.out.println(new Timestamp(1691856000000L));
    }
}
