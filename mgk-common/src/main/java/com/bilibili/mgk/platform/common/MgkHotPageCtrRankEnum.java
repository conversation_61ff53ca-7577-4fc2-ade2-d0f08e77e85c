package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotPageCtrRankEnum
 * @author: gaoming
 * @date: 2021/11/15
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
public enum MgkHotPageCtrRankEnum {
    /**
     * S
     */
    S(1, "S", "高于90%的同行"),

    /**
     * A
     */
    A(2, "A", "高于75%的同行"),

    /**
     * B
     */
    B(2, "B", "高于50%的同行"),

    /**
     * C
     */
    C(4, "C", "表现一般");
    @Getter
    private Integer code;

    @Getter
    private String value;

    @Getter
    private String desc;

    public static MgkHotPageCtrRankEnum getByCode(Integer code) {
        for (MgkHotPageCtrRankEnum bean :
                values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotPageCtrRankEnum " + code);
    }

    public static MgkHotPageCtrRankEnum getByValue(String value) {
        for (MgkHotPageCtrRankEnum bean :
                values()) {
            if (bean.getValue().equals(value)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown value MgkHotPageCtrRankEnum " + value);
    }
}
