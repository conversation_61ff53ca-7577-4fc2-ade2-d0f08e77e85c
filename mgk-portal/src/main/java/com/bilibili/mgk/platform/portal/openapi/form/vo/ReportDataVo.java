package com.bilibili.mgk.platform.portal.openapi.form.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/9/27
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportDataVo {
    @ApiModelProperty(value = "表单ID")
    @NotNull
    private String form_id;
    @ApiModelProperty(value = "表单数据")
    @NotEmpty
    private List<FormItemDataVo> form_datas;
    @ApiModelProperty(value = "定制化表单 从属")
    private String customize;
}
