package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: EditWorksNoLayersVo
 * @author: gaoming
 * @date: 2020/11/30
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EditWorksNoLayersVo {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "作品url")
    private String works_url;

    @ApiModelProperty(value = "作品md5")
    private String works_md5;

    @ApiModelProperty(value = "作品大小")
    private Long works_size;

    @ApiModelProperty(value = "总时长")
    private Integer total_duration;

    @ApiModelProperty(value = "每帧时长")
    private Integer duration_perFrame;

    @ApiModelProperty(value = "帧数")
    private Integer frames;

    @ApiModelProperty(value = "轮播次数")
    private Integer rounds_number;

    @ApiModelProperty(value = "封面")
    private CollageCoverVo cover;
}
