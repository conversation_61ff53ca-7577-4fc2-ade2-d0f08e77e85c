package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: IspPhoneDto
 * @author: gaoming
 * @date: 2021/11/01
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class IspPhoneDto implements Serializable {

    private static final long serialVersionUID = 800768800467361888L;

    /**
     * 城市码 86
     */
    private String country_code;

    /**
     * tel
     */
    private String tel;
}
