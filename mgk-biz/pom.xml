<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.bilibili.mgk</groupId>
    <artifactId>mgk-platform</artifactId>
    <version>0.2.54-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>mgk-biz</artifactId>

  <dependencies>


    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-services</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bilibili.mgk</groupId>
      <artifactId>mgk-common</artifactId>
      <version>${project.parent.version}</version>
    </dependency>
    <!-- database -->
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
      <version>${mybatis.version}</version>
    </dependency>
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis-spring</artifactId>
      <version>${spring-mybatis.version}</version>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>${mysql.version}</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.mchange</groupId>
      <artifactId>c3p0</artifactId>
      <version>${c3p0-version}</version>
    </dependency>
    <dependency>
      <groupId>org.modelmapper</groupId>
      <artifactId>modelmapper</artifactId>
      <version>${org.modelmapper.version}</version>
    </dependency>

    <!-- spring -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <version>${org.springframework.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
      <version>${org.springframework.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aop</artifactId>
      <version>${org.springframework.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
      <version>${org.springframework.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jdbc</artifactId>
      <version>${org.springframework.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <version>${org.springframework.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
      <version>${weaver.version}</version>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>net.sourceforge.groboutils</groupId>
      <artifactId>groboutils-core</artifactId>
      <version>5</version>
      <scope>test</scope>
    </dependency>
    <!--log-->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${org.slf4j.version}</version>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
      <version>${logback.version}</version>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>${logback.version}</version>
    </dependency>

    <dependency>
      <groupId>net.logstash.logback</groupId>
      <artifactId>logstash-logback-encoder</artifactId>
      <version>4.7</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>${fastjson.version}</version>
    </dependency>

    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>${gson.version}</version>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>28.2-jre</version>
    </dependency>

    <dependency>
      <groupId>com.bilibili.mgk</groupId>
      <artifactId>mgk-api</artifactId>
      <version>${parent.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>okhttp</artifactId>
          <groupId>com.squareup.okhttp3</groupId>
        </exclusion>
        <exclusion>
          <artifactId>http-util</artifactId>
          <groupId>com.bilibili.adp</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-codec</artifactId>
          <groupId>commons-codec</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.adp</groupId>
      <artifactId>bfs-biz</artifactId>
      <version>${adp.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.bilibili.adp</groupId>
          <artifactId>common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>commons-codec</groupId>
          <artifactId>commons-codec</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.bilibili.bjcom</groupId>
          <artifactId>bjcom-util</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fastjson</artifactId>
          <groupId>com.alibaba</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.crm</groupId>
      <artifactId>crm-api</artifactId>
      <version>${crm.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.bilibili.adp</groupId>
          <artifactId>common</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>lombok</artifactId>
          <groupId>org.projectlombok</groupId>
        </exclusion>
        <exclusion>
          <artifactId>okhttp</artifactId>
          <groupId>com.squareup.okhttp3</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.bilibili.ad</groupId>
          <artifactId>commercial-order-api</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>launch-api</artifactId>
          <groupId>com.bilibili.adp</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.adp</groupId>
      <artifactId>passport-biz</artifactId>
      <version>${passport-biz.version}</version>
      <exclusions>
        <exclusion>
          <groupId>pleiades.component.rpc</groupId>
          <artifactId>rpc-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component.rpc</groupId>
          <artifactId>rpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.bilibili.adp</groupId>
          <artifactId>common</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>commons-codec</artifactId>
          <groupId>commons-codec</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-buffer</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-codec</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-common</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-codec-http</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-transport</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-protobuf</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-stub</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fastjson</artifactId>
          <groupId>com.alibaba</groupId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component</groupId>
          <artifactId>boot</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component</groupId>
          <artifactId>env</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.venus.naming</groupId>
          <artifactId>naming-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.venus.naming</groupId>
          <artifactId>naming-discovery</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component</groupId>
          <artifactId>stats</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component</groupId>
          <artifactId>utility</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.venus</groupId>
          <artifactId>breaker</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component</groupId>
          <artifactId>component-ecode</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.venus</groupId>
          <artifactId>context</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.venus.trace</groupId>
          <artifactId>trace-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.mas</groupId>
      <artifactId>mas-api</artifactId>
      <version>${mas.version}</version>
    </dependency>

    <dependency>
      <artifactId>grpc-stub</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <groupId>io.grpc</groupId>
      <version>1.29.0</version>
    </dependency>
    <dependency>
      <artifactId>grpc-protobuf</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <groupId>io.grpc</groupId>
      <version>1.29.0</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
      <version>${org.springframework.version}</version>
    </dependency>

    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
      <version>3.4.3</version>
      <exclusions>
        <exclusion>
          <artifactId>netty-handler</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-resolver</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-buffer</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-transport</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-common</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-codec</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-redis</artifactId>
      <version>${org.springframework.data.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>spring-data-commons</artifactId>
          <groupId>org.springframework.data</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.retry</groupId>
      <artifactId>spring-retry</artifactId>
      <version>${spring.retry.version}</version>
    </dependency>

    <dependency>
      <groupId>com.bilibili.bjcom</groupId>
      <artifactId>bjcom-cat-mybatis</artifactId>
      <version>1.0.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>io.netty</groupId>
          <artifactId>netty-all</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.bjcom</groupId>
      <artifactId>bjcom-util</artifactId>
      <version>1.1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.sycpb.crm</groupId>
      <artifactId>acc-api</artifactId>
      <version>${acc-api.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.bilibili.ad</groupId>
          <artifactId>commercial-order-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.bilibili.cpm</groupId>
      <artifactId>cpm-agent-common</artifactId>
      <version>${agent.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.bilibili.adp</groupId>
          <artifactId>common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>net.javacrumbs.json-unit</groupId>
      <artifactId>json-unit</artifactId>
      <version>1.5.0</version>
    </dependency>

    <dependency>
      <groupId>org.skyscreamer</groupId>
      <artifactId>jsonassert</artifactId>
      <version>1.5.0</version>
    </dependency>

    <dependency>
      <groupId>net.sf.json-lib</groupId>
      <artifactId>json-lib-ext-spring</artifactId>
      <version>1.0.2</version>
    </dependency>

    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>${jedis.version}</version>
    </dependency>

    <dependency>
      <groupId>com.bilibili.cpt</groupId>
      <artifactId>cpt-api</artifactId>
      <version>${cpt.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.bilibili.adp</groupId>
          <artifactId>common</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>lombok</artifactId>
          <groupId>org.projectlombok</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.adp</groupId>
      <artifactId>launch-api</artifactId>
      <version>${adp.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.data</groupId>
          <artifactId>spring-data-elasticsearch</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.bilibili.adp</groupId>
          <artifactId>common</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>lombok</artifactId>
          <groupId>org.projectlombok</groupId>
        </exclusion>
        <exclusion>
          <artifactId>okhttp</artifactId>
          <groupId>com.squareup.okhttp3</groupId>
        </exclusion>
        <exclusion>
          <artifactId>http-util</artifactId>
          <groupId>com.bilibili.adp</groupId>
        </exclusion>
        <exclusion>
          <artifactId>config</artifactId>
          <groupId>com.bilibili.config</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.brand</groupId>
      <artifactId>ssa-api</artifactId>
      <version>${brand.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.bilibili.adp</groupId>
          <artifactId>common</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>lombok</artifactId>
          <groupId>org.projectlombok</groupId>
        </exclusion>
        <exclusion>
          <artifactId>okhttp</artifactId>
          <groupId>com.squareup.okhttp3</groupId>
        </exclusion>
        <exclusion>
          <artifactId>config</artifactId>
          <groupId>com.bilibili.config</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>net.coobird</groupId>
      <artifactId>thumbnailator</artifactId>
      <version>0.4.8</version>
    </dependency>
    <dependency>
      <groupId>com.vdurmont</groupId>
      <artifactId>emoji-java</artifactId>
      <version>4.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.bilibili.bjcom</groupId>
      <artifactId>bjcom-test</artifactId>
      <version>1.1.0-SNAPSHOT</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.bilibili.mgk</groupId>
      <artifactId>collage-biz</artifactId>
      <version>${parent.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component</groupId>
          <artifactId>utility</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.vavr</groupId>
      <artifactId>vavr</artifactId>
      <version>0.10.2</version>
    </dependency>
    <dependency>
      <groupId>com.bilibili.bcg</groupId>
      <artifactId>ad-conversion-track</artifactId>
      <version>0.0.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
      <version>2.1.0.RELEASE</version>
    </dependency>

    <dependency>
      <groupId>org.elasticsearch.client</groupId>
      <artifactId>elasticsearch-rest-high-level-client</artifactId>
      <version>5.6.16</version>
    </dependency>
    <!--        <dependency>-->
    <!--            <groupId>org.springframework.data</groupId>-->
    <!--            <artifactId>spring-data-elasticsearch</artifactId>-->
    <!--            <version>3.2.13.RELEASE</version>-->
    <!--            <exclusions>-->
    <!--                <exclusion>-->
    <!--                    <artifactId>commons-codec</artifactId>-->
    <!--                    <groupId>commons-codec</groupId>-->
    <!--                </exclusion>-->
    <!--                <exclusion>-->
    <!--                    <artifactId>netty-handler</artifactId>-->
    <!--                    <groupId>io.netty</groupId>-->
    <!--                </exclusion>-->
    <!--                <exclusion>-->
    <!--                    <artifactId>netty-resolver</artifactId>-->
    <!--                    <groupId>io.netty</groupId>-->
    <!--                </exclusion>-->
    <!--                <exclusion>-->
    <!--                    <artifactId>netty-buffer</artifactId>-->
    <!--                    <groupId>io.netty</groupId>-->
    <!--                </exclusion>-->
    <!--                <exclusion>-->
    <!--                    <artifactId>netty-codec</artifactId>-->
    <!--                    <groupId>io.netty</groupId>-->
    <!--                </exclusion>-->
    <!--                <exclusion>-->
    <!--                    <artifactId>netty-codec-http</artifactId>-->
    <!--                    <groupId>io.netty</groupId>-->
    <!--                </exclusion>-->
    <!--                <exclusion>-->
    <!--                    <artifactId>netty-common</artifactId>-->
    <!--                    <groupId>io.netty</groupId>-->
    <!--                </exclusion>-->
    <!--                <exclusion>-->
    <!--                    <artifactId>netty-transport</artifactId>-->
    <!--                    <groupId>io.netty</groupId>-->
    <!--                </exclusion>-->
    <!--            </exclusions>-->
    <!--        </dependency>-->

    <dependency>
      <groupId>ru.yandex.clickhouse</groupId>
      <artifactId>clickhouse-jdbc</artifactId>
      <version>${clickhouse.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- https://mvnrepository.com/artifact/org.springframework.data/spring-data-mongodb -->
    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-mongodb</artifactId>
      <version>2.0.0.RELEASE</version>
      <exclusions>
        <exclusion>
          <artifactId>spring-data-commons</artifactId>
          <groupId>org.springframework.data</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- https://mvnrepository.com/artifact/org.mongodb/mongo-java-driver -->
    <dependency>
      <groupId>org.mongodb</groupId>
      <artifactId>mongo-java-driver</artifactId>
      <version>3.5.0</version>
    </dependency>

    <dependency>
      <groupId>net.ipip</groupId>
      <artifactId>ipdb</artifactId>
      <version>${ipdb.version}</version>
    </dependency>

    <dependency>
      <groupId>org.jooq</groupId>
      <artifactId>jooq</artifactId>
      <version>${jooq.version}</version>
    </dependency>

    <dependency>
      <artifactId>netty-handler</artifactId>
      <groupId>io.netty</groupId>
      <version>4.1.48.Final</version>
    </dependency>
    <dependency>
      <artifactId>netty-buffer</artifactId>
      <groupId>io.netty</groupId>
      <version>4.1.48.Final</version>
    </dependency>
    <dependency>
      <artifactId>netty-codec</artifactId>
      <groupId>io.netty</groupId>
      <version>4.1.48.Final</version>
    </dependency>
    <dependency>
      <artifactId>netty-codec-http</artifactId>
      <groupId>io.netty</groupId>
      <version>4.1.48.Final</version>
    </dependency>
    <dependency>
      <artifactId>netty-common</artifactId>
      <groupId>io.netty</groupId>
      <version>4.1.48.Final</version>
    </dependency>
    <dependency>
      <artifactId>netty-resolver</artifactId>
      <groupId>io.netty</groupId>
      <version>4.1.48.Final</version>
    </dependency>
    <dependency>
      <artifactId>netty-transport</artifactId>
      <groupId>io.netty</groupId>
      <version>4.1.48.Final</version>
    </dependency>

    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
      <version>2.0.22</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid</artifactId>
      <version>${druid.version}</version>
    </dependency>

    <dependency>
      <groupId>com.bilibili.mas</groupId>
      <artifactId>mas-common</artifactId>
      <version>${mas.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
        <exclusion>
          <artifactId>common</artifactId>
          <groupId>com.bilibili.adp</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.bcg</groupId>
      <artifactId>abyss-sdk</artifactId>
      <version>${abyss.version}</version>
      <exclusions>
        <exclusion>
          <groupId>pleiades.venus.naming</groupId>
          <artifactId>naming-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.venus.naming</groupId>
          <artifactId>naming-discovery</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>spring-data-commons</artifactId>
          <groupId>org.springframework.data</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.bcg</groupId>
      <artifactId>nebula-sdk</artifactId>
      <version>1.13.1</version>
      <exclusions>
        <exclusion>
          <groupId>pleiades.venus.naming</groupId>
          <artifactId>naming-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.venus.naming</groupId>
          <artifactId>naming-discovery</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component</groupId>
          <artifactId>stats</artifactId>
        </exclusion>
        <exclusion>
          <groupId>pleiades.component</groupId>
          <artifactId>utility</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>naming-client</artifactId>
          <groupId>pleiades.venus.naming</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bilibili.bcg</groupId>
      <artifactId>nebula-spring-boot-starter</artifactId>
      <version>1.13.0-RC1</version>
    </dependency>

    <dependency>
      <groupId>ch.hsr</groupId>
      <artifactId>geohash</artifactId>
      <version>${geohash.version}</version>
    </dependency>

    <dependency>
      <artifactId>ad-loop-transparent</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>bcg-stat</artifactId>
          <groupId>com.bapis.bcg</groupId>
        </exclusion>
      </exclusions>
      <groupId>com.bilibili.bcg</groupId>
      <version>${ad.loop.transparent.version}</version>
    </dependency>

    <!-- hadoop -->
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <version>2.7.3</version>
      <exclusions>
        <exclusion> <!-- asm可能和logback冲突，建议移除 -->
          <artifactId>asm</artifactId>
          <groupId>asm</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-hdfs</artifactId>
      <version>2.7.3</version>
      <exclusions>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.4</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.4</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun.api.gateway</groupId>
      <artifactId>sdk-core-java</artifactId>
      <version>1.1.5</version>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.sejda.imageio</groupId>
      <artifactId>webp-imageio</artifactId>
      <version>0.1.6</version>
    </dependency>


    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
      <version>2.9.3</version>
    </dependency>

    <dependency>
      <groupId>com.bilibili</groupId>
      <artifactId>lancer2-sdk-java</artifactId>
      <version>1.8.0-SNAPSHOT</version>
    </dependency>


    <!-- 如果需要在url链接信息中使用 yuuni 额外支持参数（execute_on_shard,execute_on_replica,use_cache... 下方Yuuni config有介绍），则必须使用bilibi-jdbc  -->
    <!-- 此版本带有bitmap问题修复  -->
    <dependency>
      <groupId>ru.yandex.clickhouse</groupId>
      <artifactId>clickhouse-jdbc</artifactId>
      <version>0.3.2.2-bili</version>
      <classifier>shaded</classifier>
    </dependency>

    <dependency>
      <groupId>com.bilibili</groupId>
      <artifactId>warp-opentelemetry</artifactId>
      <version>1.0.15</version>
    </dependency>

    <dependency>
      <groupId>com.bilibili</groupId>
      <artifactId>discovery-client</artifactId>
      <version>1.0.5</version>
    </dependency>


    <dependency>
      <groupId>com.bilibili.microservices</groupId>
      <artifactId>databus-java</artifactId>
      <version>1.10.3-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>pleiades.component</groupId>
      <artifactId>boot</artifactId>
      <version>${pleiades.version}</version>
    </dependency>
    <dependency>
      <groupId>pleiades.component</groupId>
      <artifactId>env</artifactId>
      <version>${pleiades.version}</version>
    </dependency>
    <dependency>
      <groupId>pleiades.venus.naming</groupId>
      <artifactId>naming-client</artifactId>
      <version>${pleiades.version}</version>
    </dependency>
    <dependency>
      <groupId>pleiades.venus.naming</groupId>
      <artifactId>naming-discovery</artifactId>
      <version>${pleiades.version}</version>
    </dependency>
    <dependency>
      <groupId>pleiades.component</groupId>
      <artifactId>stats</artifactId>
      <version>${pleiades.version}</version>
    </dependency>
    <dependency>
      <groupId>pleiades.component</groupId>
      <artifactId>utility</artifactId>
      <version>${pleiades.version}</version>
    </dependency>

    <dependency>
      <groupId>pleiades.component.rpc</groupId>
      <artifactId>rpc-client</artifactId>
      <version>${pleiades.version}</version>
    </dependency>

    <dependency>
      <groupId>pleiades.component.rpc</groupId>
      <artifactId>rpc-core</artifactId>
      <version>${pleiades.version}</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-commons</artifactId>
      <version>2.0.8.RELEASE</version>
    </dependency>


    <!--        bapi-->
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_crm.wallet_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk.chat_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_adp.mini_game_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_adp.app_package_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk.media_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk.audit_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_brand_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_account.label_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_creative_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>bcg_stat_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>datacenter_service.oneservice_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>passport_service.user_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_resource_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk.page-group_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk.business_tool_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_adp.component_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_archive_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk.app_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_cmc.category_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_crm.account_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_cmc.up_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>activity_service_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_scv.anchor_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_audit_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_crm.industry_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk.component_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_mgk.comment_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>silverbullet_gaia.interface_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>ad_pandora.core.auto_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>operational_reserveservice_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <dependency>
      <groupId>co.bilibili.buf</groupId>
      <artifactId>community_service.location_grpc_java</artifactId>
      <version>${bapis.version}</version>
    </dependency>
    <!--        end bapi-->

  </dependencies>
  <build>
    <extensions>
      <extension>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>1.7.0</version>
      </extension>
    </extensions>
    <plugins>
      <plugin>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-maven-plugin</artifactId>
        <version>4.2.1</version>
        <configuration>
          <jdbcDriver>com.mysql.jdbc.Driver</jdbcDriver>
          <jdbcUrl>*****************************************************************************************************</jdbcUrl>
          <jdbcUser>rd</jdbcUser>
          <jdbcPassword>rd66!!</jdbcPassword>
          <exportForeignKeys>false</exportForeignKeys>
          <sourceFolder>${project.basedir}/src/main/resources</sourceFolder>
          <targetFolder>${project.basedir}/src/main/java</targetFolder>
          <exportBeans>true</exportBeans>
          <packageName>com.bilibili.mgk.platform.biz.dao.querydsl</packageName>
          <beanPackageName>com.bilibili.mgk.platform.biz.dao.querydsl.pos</beanPackageName>
          <beanSuffix>QueryDSLPo</beanSuffix>
          <beanAddToString>true</beanAddToString>
          <tableNamePattern>manager_chat_clue</tableNamePattern>
          <numericMappings>
            <numericMapping>
              <total>1</total>
              <decimal>0</decimal>
              <javaType>java.lang.Integer</javaType>
            </numericMapping>
            <numericMapping>
              <total>2</total>
              <decimal>0</decimal>
              <javaType>java.lang.Integer</javaType>
            </numericMapping>
            <numericMapping>
              <total>3</total>
              <decimal>0</decimal>
              <javaType>java.lang.Integer</javaType>
            </numericMapping>
            <numericMapping>
              <total>4</total>
              <decimal>0</decimal>
              <javaType>java.lang.Integer</javaType>
            </numericMapping>
            <numericMapping>
              <total>5</total>
              <decimal>0</decimal>
              <javaType>java.lang.Integer</javaType>
            </numericMapping>
            <numericMapping>
              <total>6</total>
              <decimal>0</decimal>
              <javaType>java.lang.Integer</javaType>
            </numericMapping>
          </numericMappings>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
          </dependency>
        </dependencies>
      </plugin>

      <plugin>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-codegen-maven</artifactId>
        <version>${jooq.version}</version>
        <configuration>
          <jdbc>
            <autoCommit>true</autoCommit>
            <driver>com.mysql.jdbc.Driver</driver>
            <url>********************************************************************************************</url>
            <user>rd</user>
            <password>rd66!!</password>
          </jdbc>
          <generator>
            <strategy>
              <matchers>
                <tables>
                  <table>
                    <tableClass>
                      <transform>PASCAL</transform>>
                      <expression>T_$0</expression>>
                    </tableClass>
                    <pojoClass>
                      <transform>PASCAL</transform>>
                      <expression>$0_Po</expression>>
                    </pojoClass>
                  </table>
                </tables>
              </matchers>
            </strategy>
            <database>
              <includes>lau_material_bilibili_video_info
                | res_app_package
                | lau_material_bilibili_video_with_cover
              </includes>
              <inputSchema>bilibili_business</inputSchema>
              <outputSchemaToDefault>true</outputSchemaToDefault>
            </database>
            <target>
              <packageName>com.bilibili.mgk.platform.biz.dao.jooq.generated</packageName>
              <directory>src/main/java</directory>
            </target>
            <generate>
              <pojos>true</pojos>
              <interfaces>false</interfaces>
              <daos>false</daos>
              <springAnnotations>true</springAnnotations>
            </generate>
          </generator>
        </configuration>
      </plugin>

      <!-- grpc plugin !-->
      <!--            <plugin>-->
      <!--                <groupId>com.bilibili</groupId>-->
      <!--                <artifactId>buf-maven-plugin</artifactId>-->
      <!--                <version>1.1.0</version>-->
      <!--                <executions>-->
      <!--                    <execution>-->
      <!--                        <goals>-->
      <!--                            <goal>generate</goal>-->
      <!--                        </goals>-->
      <!--                    </execution>-->
      <!--                </executions>-->
      <!--                <configuration>-->
      <!--                    <bufVersion>1.23.1</bufVersion>-->
      <!--                    <moduleArray>-->
      <!--                        <array>buf.bilibili.co/ad/adp.mini_game</array>-->
      <!--                        <array>buf.bilibili.co/ad/adp.app_package</array>-->
      <!--                        <array>buf.bilibili.co/ad/mgk</array>-->
      <!--                        <array>buf.bilibili.co/ad/mgk.media</array>-->
      <!--                        <array>buf.bilibili.co/ad/mgk.audit</array>-->
      <!--                        <array>buf.bilibili.co/ad/brand</array>-->
      <!--                        <array>buf.bilibili.co/ad/account.label</array>-->
      <!--                        <array>buf.bilibili.co/ad/creative</array>-->
      <!--                        <array>buf.bilibili.co/bcg/stat</array>-->
      <!--                        <array>buf.bilibili.co/datacenter/service.oneservice</array>-->
      <!--                        <array>buf.bilibili.co/passport/service.user</array>-->
      <!--                        <array>buf.bilibili.co/ad/resource</array>-->
      <!--                        <array>buf.bilibili.co/ad/mgk.page-group</array>-->
      <!--                        <array>buf.bilibili.co/ad/mgk.business_tool</array>-->
      <!--                        <array>buf.bilibili.co/ad/adp.component</array>-->
      <!--                        <array>buf.bilibili.co/ad/archive</array>-->
      <!--                        <array>buf.bilibili.co/ad/mgk.app</array>-->
      <!--                        <array>buf.bilibili.co/ad/cmc.category</array>-->
      <!--                        <array>buf.bilibili.co/ad/crm.account</array>-->
      <!--                        <array>buf.bilibili.co/ad/cmc.up</array>-->
      <!--                        <array>buf.bilibili.co/activity/service</array>-->
      <!--                        <array>buf.bilibili.co/ad/scv.anchor</array>-->
      <!--                        <array>buf.bilibili.co/ad/audit</array>-->
      <!--                        <array>buf.bilibili.co/ad/crm.industry</array>-->
      <!--                        <array>buf.bilibili.co/ad/mgk.component</array>-->

      <!--                    </moduleArray>-->
      <!--                </configuration>-->
      <!--            </plugin>-->
    </plugins>
  </build>

  <pluginRepositories>
    <pluginRepository>
      <id>bilibili-plugin-snapshots</id>
      <name>Bilibili Nexus Plugin Snapshot</name>
      <url>https://nexus.bilibili.co/content/repositories/snapshots</url>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>daily</updatePolicy>
        <checksumPolicy>warn</checksumPolicy>
      </snapshots>
    </pluginRepository>
    <pluginRepository>
      <id>bilibili-plugin-releases</id>
      <name>Bilibili Nexus Plugin Release</name>
      <url>https://nexus.bilibili.co/content/repositories/releases</url>
      <releases>
        <enabled>true</enabled>
        <checksumPolicy>warn</checksumPolicy>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>


</project>