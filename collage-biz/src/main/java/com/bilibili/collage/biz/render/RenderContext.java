package com.bilibili.collage.biz.render;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/1/1
 * 渲染上下文
 **/
@Service
@Slf4j
public class RenderContext {

    private ThreadLocal<Integer> canvasWidth = new ThreadLocal<>();

    private ThreadLocal<Integer> canvasHeight = new ThreadLocal<>();

    public RenderContext () {
        super();
    }

    public RenderContext  (Integer width, Integer height) {

        canvasWidth.set(width);
        canvasHeight.set(height);
    }

    public void setCanvasWidth (Integer width) {
        canvasWidth.set(width);
    }

    public void setCanvasHeight (Integer height) {
        canvasHeight.set(height);
    }

    public Integer getCanvasWidth () {

        return canvasWidth.get();
    }

    public Integer getCanvasHeight () {

        return canvasHeight.get();
    }
}
