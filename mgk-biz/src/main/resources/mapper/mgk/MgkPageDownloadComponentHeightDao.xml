<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkPageDownloadComponentHeightDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="total_block_size" jdbcType="INTEGER" property="totalBlockSize" />
    <result column="total_download_component_size" jdbcType="INTEGER" property="totalDownloadComponentSize" />
    <result column="max_download_component_size" jdbcType="INTEGER" property="maxDownloadComponentSize" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="total_first_screen_download_component_height" jdbcType="INTEGER" property="totalFirstScreenDownloadComponentHeight" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo">
    <id column="mgk_page_download_component_height_id" jdbcType="BIGINT" property="id" />
    <result column="mgk_page_download_component_height_page_id" jdbcType="BIGINT" property="pageId" />
    <result column="mgk_page_download_component_height_total_block_size" jdbcType="INTEGER" property="totalBlockSize" />
    <result column="mgk_page_download_component_height_total_download_component_size" jdbcType="INTEGER" property="totalDownloadComponentSize" />
    <result column="mgk_page_download_component_height_max_download_component_size" jdbcType="INTEGER" property="maxDownloadComponentSize" />
    <result column="mgk_page_download_component_height_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mgk_page_download_component_height_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="mgk_page_download_component_height_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="mgk_page_download_component_height_total_first_screen_download_component_height" jdbcType="INTEGER" property="totalFirstScreenDownloadComponentHeight" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as mgk_page_download_component_height_id, ${alias}.page_id as mgk_page_download_component_height_page_id, 
    ${alias}.total_block_size as mgk_page_download_component_height_total_block_size, 
    ${alias}.total_download_component_size as mgk_page_download_component_height_total_download_component_size, 
    ${alias}.max_download_component_size as mgk_page_download_component_height_max_download_component_size, 
    ${alias}.ctime as mgk_page_download_component_height_ctime, ${alias}.mtime as mgk_page_download_component_height_mtime, 
    ${alias}.is_deleted as mgk_page_download_component_height_is_deleted, ${alias}.total_first_screen_download_component_height as mgk_page_download_component_height_total_first_screen_download_component_height
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, page_id, total_block_size, total_download_component_size, max_download_component_size, 
    ctime, mtime, is_deleted, total_first_screen_download_component_height
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_page_download_component_height
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_page_download_component_height
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mgk_page_download_component_height
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPoExample">
    delete from mgk_page_download_component_height
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_page_download_component_height (page_id, total_block_size, total_download_component_size, 
      max_download_component_size, ctime, mtime, 
      is_deleted, total_first_screen_download_component_height
      )
    values (#{pageId,jdbcType=BIGINT}, #{totalBlockSize,jdbcType=INTEGER}, #{totalDownloadComponentSize,jdbcType=INTEGER}, 
      #{maxDownloadComponentSize,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_page_download_component_height
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="totalBlockSize != null">
        total_block_size,
      </if>
      <if test="totalDownloadComponentSize != null">
        total_download_component_size,
      </if>
      <if test="maxDownloadComponentSize != null">
        max_download_component_size,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="totalFirstScreenDownloadComponentHeight != null">
        total_first_screen_download_component_height,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="totalBlockSize != null">
        #{totalBlockSize,jdbcType=INTEGER},
      </if>
      <if test="totalDownloadComponentSize != null">
        #{totalDownloadComponentSize,jdbcType=INTEGER},
      </if>
      <if test="maxDownloadComponentSize != null">
        #{maxDownloadComponentSize,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="totalFirstScreenDownloadComponentHeight != null">
        #{totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPoExample" resultType="java.lang.Long">
    select count(*) from mgk_page_download_component_height
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_page_download_component_height
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.pageId != null">
        page_id = #{record.pageId,jdbcType=BIGINT},
      </if>
      <if test="record.totalBlockSize != null">
        total_block_size = #{record.totalBlockSize,jdbcType=INTEGER},
      </if>
      <if test="record.totalDownloadComponentSize != null">
        total_download_component_size = #{record.totalDownloadComponentSize,jdbcType=INTEGER},
      </if>
      <if test="record.maxDownloadComponentSize != null">
        max_download_component_size = #{record.maxDownloadComponentSize,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.totalFirstScreenDownloadComponentHeight != null">
        total_first_screen_download_component_height = #{record.totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_page_download_component_height
    set id = #{record.id,jdbcType=BIGINT},
      page_id = #{record.pageId,jdbcType=BIGINT},
      total_block_size = #{record.totalBlockSize,jdbcType=INTEGER},
      total_download_component_size = #{record.totalDownloadComponentSize,jdbcType=INTEGER},
      max_download_component_size = #{record.maxDownloadComponentSize,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      total_first_screen_download_component_height = #{record.totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo">
    update mgk_page_download_component_height
    <set>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=BIGINT},
      </if>
      <if test="totalBlockSize != null">
        total_block_size = #{totalBlockSize,jdbcType=INTEGER},
      </if>
      <if test="totalDownloadComponentSize != null">
        total_download_component_size = #{totalDownloadComponentSize,jdbcType=INTEGER},
      </if>
      <if test="maxDownloadComponentSize != null">
        max_download_component_size = #{maxDownloadComponentSize,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="totalFirstScreenDownloadComponentHeight != null">
        total_first_screen_download_component_height = #{totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo">
    update mgk_page_download_component_height
    set page_id = #{pageId,jdbcType=BIGINT},
      total_block_size = #{totalBlockSize,jdbcType=INTEGER},
      total_download_component_size = #{totalDownloadComponentSize,jdbcType=INTEGER},
      max_download_component_size = #{maxDownloadComponentSize,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      total_first_screen_download_component_height = #{totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_page_download_component_height (page_id, total_block_size, total_download_component_size, 
      max_download_component_size, ctime, mtime, 
      is_deleted, total_first_screen_download_component_height
      )
    values (#{pageId,jdbcType=BIGINT}, #{totalBlockSize,jdbcType=INTEGER}, #{totalDownloadComponentSize,jdbcType=INTEGER}, 
      #{maxDownloadComponentSize,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      page_id = values(page_id),
      total_block_size = values(total_block_size),
      total_download_component_size = values(total_download_component_size),
      max_download_component_size = values(max_download_component_size),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      total_first_screen_download_component_height = values(total_first_screen_download_component_height),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_page_download_component_height
      (page_id,total_block_size,total_download_component_size,max_download_component_size,ctime,mtime,is_deleted,total_first_screen_download_component_height)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.pageId,jdbcType=BIGINT},
        #{item.totalBlockSize,jdbcType=INTEGER},
        #{item.totalDownloadComponentSize,jdbcType=INTEGER},
        #{item.maxDownloadComponentSize,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_page_download_component_height
      (page_id,total_block_size,total_download_component_size,max_download_component_size,ctime,mtime,is_deleted,total_first_screen_download_component_height)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.pageId,jdbcType=BIGINT},
        #{item.totalBlockSize,jdbcType=INTEGER},
        #{item.totalDownloadComponentSize,jdbcType=INTEGER},
        #{item.maxDownloadComponentSize,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      page_id = values(page_id),
      total_block_size = values(total_block_size),
      total_download_component_size = values(total_download_component_size),
      max_download_component_size = values(max_download_component_size),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      total_first_screen_download_component_height = values(total_first_screen_download_component_height),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_page_download_component_height
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="totalBlockSize != null">
        total_block_size,
      </if>
      <if test="totalDownloadComponentSize != null">
        total_download_component_size,
      </if>
      <if test="maxDownloadComponentSize != null">
        max_download_component_size,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="totalFirstScreenDownloadComponentHeight != null">
        total_first_screen_download_component_height,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="totalBlockSize != null">
        #{totalBlockSize,jdbcType=INTEGER},
      </if>
      <if test="totalDownloadComponentSize != null">
        #{totalDownloadComponentSize,jdbcType=INTEGER},
      </if>
      <if test="maxDownloadComponentSize != null">
        #{maxDownloadComponentSize,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="totalFirstScreenDownloadComponentHeight != null">
        #{totalFirstScreenDownloadComponentHeight,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="pageId != null">
        page_id = values(page_id),
      </if>
      <if test="totalBlockSize != null">
        total_block_size = values(total_block_size),
      </if>
      <if test="totalDownloadComponentSize != null">
        total_download_component_size = values(total_download_component_size),
      </if>
      <if test="maxDownloadComponentSize != null">
        max_download_component_size = values(max_download_component_size),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="totalFirstScreenDownloadComponentHeight != null">
        total_first_screen_download_component_height = values(total_first_screen_download_component_height),
      </if>
    </trim>
  </insert>
</mapper>