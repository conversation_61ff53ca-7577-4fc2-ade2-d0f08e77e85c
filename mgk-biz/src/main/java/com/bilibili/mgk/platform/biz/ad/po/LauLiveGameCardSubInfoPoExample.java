package com.bilibili.mgk.platform.biz.ad.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauLiveGameCardSubInfoPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauLiveGameCardSubInfoPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCardIdIsNull() {
            addCriterion("card_id is null");
            return (Criteria) this;
        }

        public Criteria andCardIdIsNotNull() {
            addCriterion("card_id is not null");
            return (Criteria) this;
        }

        public Criteria andCardIdEqualTo(Long value) {
            addCriterion("card_id =", value, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdNotEqualTo(Long value) {
            addCriterion("card_id <>", value, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdGreaterThan(Long value) {
            addCriterion("card_id >", value, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdGreaterThanOrEqualTo(Long value) {
            addCriterion("card_id >=", value, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdLessThan(Long value) {
            addCriterion("card_id <", value, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdLessThanOrEqualTo(Long value) {
            addCriterion("card_id <=", value, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdIn(List<Long> values) {
            addCriterion("card_id in", values, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdNotIn(List<Long> values) {
            addCriterion("card_id not in", values, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdBetween(Long value1, Long value2) {
            addCriterion("card_id between", value1, value2, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardIdNotBetween(Long value1, Long value2) {
            addCriterion("card_id not between", value1, value2, "cardId");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNull() {
            addCriterion("card_type is null");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNotNull() {
            addCriterion("card_type is not null");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualTo(Integer value) {
            addCriterion("card_type =", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualTo(Integer value) {
            addCriterion("card_type <>", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThan(Integer value) {
            addCriterion("card_type >", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("card_type >=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThan(Integer value) {
            addCriterion("card_type <", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualTo(Integer value) {
            addCriterion("card_type <=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeIn(List<Integer> values) {
            addCriterion("card_type in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotIn(List<Integer> values) {
            addCriterion("card_type not in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeBetween(Integer value1, Integer value2) {
            addCriterion("card_type between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("card_type not between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformIsNull() {
            addCriterion("launch_platform is null");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformIsNotNull() {
            addCriterion("launch_platform is not null");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformEqualTo(Integer value) {
            addCriterion("launch_platform =", value, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformNotEqualTo(Integer value) {
            addCriterion("launch_platform <>", value, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformGreaterThan(Integer value) {
            addCriterion("launch_platform >", value, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformGreaterThanOrEqualTo(Integer value) {
            addCriterion("launch_platform >=", value, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformLessThan(Integer value) {
            addCriterion("launch_platform <", value, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformLessThanOrEqualTo(Integer value) {
            addCriterion("launch_platform <=", value, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformIn(List<Integer> values) {
            addCriterion("launch_platform in", values, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformNotIn(List<Integer> values) {
            addCriterion("launch_platform not in", values, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformBetween(Integer value1, Integer value2) {
            addCriterion("launch_platform between", value1, value2, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andLaunchPlatformNotBetween(Integer value1, Integer value2) {
            addCriterion("launch_platform not between", value1, value2, "launchPlatform");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeIsNull() {
            addCriterion("download_type is null");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeIsNotNull() {
            addCriterion("download_type is not null");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeEqualTo(Integer value) {
            addCriterion("download_type =", value, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeNotEqualTo(Integer value) {
            addCriterion("download_type <>", value, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeGreaterThan(Integer value) {
            addCriterion("download_type >", value, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("download_type >=", value, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeLessThan(Integer value) {
            addCriterion("download_type <", value, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeLessThanOrEqualTo(Integer value) {
            addCriterion("download_type <=", value, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeIn(List<Integer> values) {
            addCriterion("download_type in", values, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeNotIn(List<Integer> values) {
            addCriterion("download_type not in", values, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeBetween(Integer value1, Integer value2) {
            addCriterion("download_type between", value1, value2, "downloadType");
            return (Criteria) this;
        }

        public Criteria andDownloadTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("download_type not between", value1, value2, "downloadType");
            return (Criteria) this;
        }

        public Criteria andApkIdIsNull() {
            addCriterion("apk_id is null");
            return (Criteria) this;
        }

        public Criteria andApkIdIsNotNull() {
            addCriterion("apk_id is not null");
            return (Criteria) this;
        }

        public Criteria andApkIdEqualTo(Integer value) {
            addCriterion("apk_id =", value, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdNotEqualTo(Integer value) {
            addCriterion("apk_id <>", value, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdGreaterThan(Integer value) {
            addCriterion("apk_id >", value, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("apk_id >=", value, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdLessThan(Integer value) {
            addCriterion("apk_id <", value, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdLessThanOrEqualTo(Integer value) {
            addCriterion("apk_id <=", value, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdIn(List<Integer> values) {
            addCriterion("apk_id in", values, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdNotIn(List<Integer> values) {
            addCriterion("apk_id not in", values, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdBetween(Integer value1, Integer value2) {
            addCriterion("apk_id between", value1, value2, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkIdNotBetween(Integer value1, Integer value2) {
            addCriterion("apk_id not between", value1, value2, "apkId");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextIsNull() {
            addCriterion("apk_button_text is null");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextIsNotNull() {
            addCriterion("apk_button_text is not null");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextEqualTo(String value) {
            addCriterion("apk_button_text =", value, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextNotEqualTo(String value) {
            addCriterion("apk_button_text <>", value, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextGreaterThan(String value) {
            addCriterion("apk_button_text >", value, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextGreaterThanOrEqualTo(String value) {
            addCriterion("apk_button_text >=", value, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextLessThan(String value) {
            addCriterion("apk_button_text <", value, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextLessThanOrEqualTo(String value) {
            addCriterion("apk_button_text <=", value, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextLike(String value) {
            addCriterion("apk_button_text like", value, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextNotLike(String value) {
            addCriterion("apk_button_text not like", value, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextIn(List<String> values) {
            addCriterion("apk_button_text in", values, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextNotIn(List<String> values) {
            addCriterion("apk_button_text not in", values, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextBetween(String value1, String value2) {
            addCriterion("apk_button_text between", value1, value2, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andApkButtonTextNotBetween(String value1, String value2) {
            addCriterion("apk_button_text not between", value1, value2, "apkButtonText");
            return (Criteria) this;
        }

        public Criteria andGameIdIsNull() {
            addCriterion("game_id is null");
            return (Criteria) this;
        }

        public Criteria andGameIdIsNotNull() {
            addCriterion("game_id is not null");
            return (Criteria) this;
        }

        public Criteria andGameIdEqualTo(Integer value) {
            addCriterion("game_id =", value, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdNotEqualTo(Integer value) {
            addCriterion("game_id <>", value, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdGreaterThan(Integer value) {
            addCriterion("game_id >", value, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("game_id >=", value, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdLessThan(Integer value) {
            addCriterion("game_id <", value, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdLessThanOrEqualTo(Integer value) {
            addCriterion("game_id <=", value, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdIn(List<Integer> values) {
            addCriterion("game_id in", values, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdNotIn(List<Integer> values) {
            addCriterion("game_id not in", values, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdBetween(Integer value1, Integer value2) {
            addCriterion("game_id between", value1, value2, "gameId");
            return (Criteria) this;
        }

        public Criteria andGameIdNotBetween(Integer value1, Integer value2) {
            addCriterion("game_id not between", value1, value2, "gameId");
            return (Criteria) this;
        }

        public Criteria andSubPkgIsNull() {
            addCriterion("sub_pkg is null");
            return (Criteria) this;
        }

        public Criteria andSubPkgIsNotNull() {
            addCriterion("sub_pkg is not null");
            return (Criteria) this;
        }

        public Criteria andSubPkgEqualTo(Integer value) {
            addCriterion("sub_pkg =", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgNotEqualTo(Integer value) {
            addCriterion("sub_pkg <>", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgGreaterThan(Integer value) {
            addCriterion("sub_pkg >", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgGreaterThanOrEqualTo(Integer value) {
            addCriterion("sub_pkg >=", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgLessThan(Integer value) {
            addCriterion("sub_pkg <", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgLessThanOrEqualTo(Integer value) {
            addCriterion("sub_pkg <=", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgIn(List<Integer> values) {
            addCriterion("sub_pkg in", values, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgNotIn(List<Integer> values) {
            addCriterion("sub_pkg not in", values, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgBetween(Integer value1, Integer value2) {
            addCriterion("sub_pkg between", value1, value2, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgNotBetween(Integer value1, Integer value2) {
            addCriterion("sub_pkg not between", value1, value2, "subPkg");
            return (Criteria) this;
        }

        public Criteria andH5BgImageIsNull() {
            addCriterion("h5_bg_image is null");
            return (Criteria) this;
        }

        public Criteria andH5BgImageIsNotNull() {
            addCriterion("h5_bg_image is not null");
            return (Criteria) this;
        }

        public Criteria andH5BgImageEqualTo(String value) {
            addCriterion("h5_bg_image =", value, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageNotEqualTo(String value) {
            addCriterion("h5_bg_image <>", value, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageGreaterThan(String value) {
            addCriterion("h5_bg_image >", value, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageGreaterThanOrEqualTo(String value) {
            addCriterion("h5_bg_image >=", value, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageLessThan(String value) {
            addCriterion("h5_bg_image <", value, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageLessThanOrEqualTo(String value) {
            addCriterion("h5_bg_image <=", value, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageLike(String value) {
            addCriterion("h5_bg_image like", value, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageNotLike(String value) {
            addCriterion("h5_bg_image not like", value, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageIn(List<String> values) {
            addCriterion("h5_bg_image in", values, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageNotIn(List<String> values) {
            addCriterion("h5_bg_image not in", values, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageBetween(String value1, String value2) {
            addCriterion("h5_bg_image between", value1, value2, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5BgImageNotBetween(String value1, String value2) {
            addCriterion("h5_bg_image not between", value1, value2, "h5BgImage");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionIsNull() {
            addCriterion("h5_apk_description is null");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionIsNotNull() {
            addCriterion("h5_apk_description is not null");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionEqualTo(String value) {
            addCriterion("h5_apk_description =", value, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionNotEqualTo(String value) {
            addCriterion("h5_apk_description <>", value, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionGreaterThan(String value) {
            addCriterion("h5_apk_description >", value, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("h5_apk_description >=", value, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionLessThan(String value) {
            addCriterion("h5_apk_description <", value, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionLessThanOrEqualTo(String value) {
            addCriterion("h5_apk_description <=", value, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionLike(String value) {
            addCriterion("h5_apk_description like", value, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionNotLike(String value) {
            addCriterion("h5_apk_description not like", value, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionIn(List<String> values) {
            addCriterion("h5_apk_description in", values, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionNotIn(List<String> values) {
            addCriterion("h5_apk_description not in", values, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionBetween(String value1, String value2) {
            addCriterion("h5_apk_description between", value1, value2, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andH5ApkDescriptionNotBetween(String value1, String value2) {
            addCriterion("h5_apk_description not between", value1, value2, "h5ApkDescription");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}