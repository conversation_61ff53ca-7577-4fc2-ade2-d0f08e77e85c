package com.bilibili.mgk.platform.api.hot_ads.dto;

import com.bilibili.adp.common.util.Page;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: QueryHotAdsDto
 * @author: gaoming
 * @date: 2021/01/07
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryHotAdsDtoV2 implements Serializable {


    private static final long serialVersionUID = 196776412736924133L;

    /**
     * 广告类型 cpc cpm gd
     */
    @Deprecated
    private String adType;

    /**
     * 创意Id
     */
    private String creativeId;

    /**
     * 创意标题
     */

    /**
     * search key
     */
    private List<String> creativeTitles;

    /**
     * 创意形态 1-静态图文 2-动态图文 3-静态视频 4-广告位播放视频
     */
    @Deprecated
    private Integer styleAbility;

    @Deprecated
    private List<Integer> styleAbilities;



    /**
     * 资源位类型
     */
    @Deprecated
    private Integer platformCategory;

    /**
     * 时间 1-七天 2-一个月
     */
    private Integer dateType;

    /**
     * 排序
     */
    private Integer orderType;

    /**
     * 页码
     */
    private Page page;

    /**
     * 黑名单状态筛选
     */
    @Deprecated
    private Integer blackStatus;

    /**
     * 黑名单
     * <p>
     * creative id not in
     */
    private List<String> blackList;

    /**
     * creative id in
     */
    private List<String> protectionList;


    /*
     * 是否是竖屏，1:是 0:否
     */
    @Deprecated
    private Integer isVerticalScreen;

    ///// 二期新加过滤条件，行业、 账户名称id ，代理商id名称
    /**
     * 行业
     */
    private List<String> industryList;

    /**
     * 账户名称id
     */
    private String accountId;


    /**
     * 代理商id
     */
    private String agentId;

    /**
     * 客户id
     */
    private String customerId;


    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 客户名称
     */
    private String customerName;


}
