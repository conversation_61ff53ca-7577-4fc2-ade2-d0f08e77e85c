package com.bilibili.mgk.platform.portal.webapi.landing_page.vo;

import com.bilibili.mgk.platform.portal.webapi.game.vo.GameVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/1/22
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CopyLandingPageVo {

    @ApiModelProperty(value = "推送源落地页ids")
    private List<Long> copy_source_page_ids;

    @ApiModelProperty(value = "要复制的账号ids")
    private List<Integer> to_acc_ids;

}
