package com.bilibili.mgk.material.center.service.creative.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/15
 */
@Data
@Accessors(chain = true)
public class MaterialReferenceDeleteResp implements SnakeCaseBody {

    @ApiModelProperty("素材id")
    private String materialId;

    @ApiModelProperty("素材id类型")
    private String materialIdType;

    @ApiModelProperty("素材uk,通常为md5")
    private String materialUk;

    @ApiModelProperty("素材引用uk，通常为业务primary-id")
    private String referenceUk;


    @ApiModelProperty("引用是否存在")
    private Boolean referenceExisted;

    @ApiModelProperty("删除成功，只有引用存在且删除成功才为true")
    private Boolean deleteSuccess;

}
