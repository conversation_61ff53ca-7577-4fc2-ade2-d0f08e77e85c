package com.bilibili.mgk.platform.portal.webapi.insight;


import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.mgk.material.center.service.creative.model.CreativeInsight;
import com.bilibili.mgk.material.center.service.creative.model.InefficientCreative;
import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.insight.InsightService;
import com.bilibili.mgk.platform.biz.utils.EnvironmentUtil;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.webapi.insight.vo.InsightDetailVo;
import com.bilibili.mgk.platform.portal.webapi.insight.vo.LowEfficientCreativeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@Slf4j
@RestController
@RequestMapping("/web_api/v1/insight")
@Api(value = "/insight", description = "创意洞察")
public class CreativeInsightController extends BasicController {

    @Autowired
    private InsightService insightService;
    @Autowired
    private SystemType systemType;

    @Resource
    private EnvironmentUtil environmentUtil;


    @Value("${material.insight.mock.enabled:true}")
    private Boolean mockEnabled;


    @Value("${material.insight.mock.account_id:5373}")
    private Integer mockAccountId;


    @ApiOperation(value = "综合诊断详情")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public Response<InsightDetailVo> getInsightDetail(@ApiIgnore Context context,
            @ApiParam(value = "time") @RequestParam(value = "time", defaultValue = "1") Integer time,
            @ApiParam(value = "day_type ,1d, 3d 7d, 30d, 替换到time参数")
            @RequestParam(value = "day_type", required = false) String dayType,
            @ApiParam(value = "material_type") @RequestParam(value =
                    "material_type", defaultValue = "video") MaterialType materialType) {

        CreativeInsight insightDetailDto = insightService.getInsightDetail(
                DateAggregation.fromParam(Optional.ofNullable(dayType).orElse(time + "d")),
                getAccountId(context), materialType);

        return Response.SUCCESS(CreativeInsightConvertor.convertor.toVo(insightDetailDto));
    }


    @ApiOperation(value = "综合诊断汇总")
    @RequestMapping(value = "/summary", method = RequestMethod.GET)
    public Response<InsightDetailVo> getInsightSummary(@ApiIgnore Context context,
            @ApiParam(value = "time") @RequestParam(value = "time", defaultValue = "1") Integer time,
            @ApiParam(value = "day_type ,1d, 3d 7d, 30d, 替换到time参数")
            @RequestParam(value = "day_type", required = false) String dayType) {

        CreativeInsight insightDetailDto = insightService.getInsightSummary(
                DateAggregation.fromParam(Optional.ofNullable(dayType).orElse(time + "d")),
                getAccountId(context));

        return Response.SUCCESS(CreativeInsightConvertor.convertor.toVo(insightDetailDto));
    }


    @ApiOperation(value = "综合诊断详情7天")
    @RequestMapping(value = "/detail/week", method = RequestMethod.GET)
    public Response<InsightDetailVo> getInsightDetailWeek(@ApiIgnore Context context,
            @ApiParam(value = "day_type ,1d, 3d 7d, 30d, 替换到time参数")
            @RequestParam(value = "day_type", required = false) String dayType,
            @ApiParam(value = "material_type") @RequestParam(value =
                    "material_type", defaultValue = "video") MaterialType materialType) {

        CreativeInsight insightDetailDto = insightService.getInsightDetailWeek(
                getAccountId(context), materialType);

        return Response.SUCCESS(CreativeInsightConvertor.convertor.toVo(insightDetailDto));
    }


    @ApiOperation(value = "获取低效创意列表")
    @RequestMapping(value = {"/getLowEfficient", "/inefficient/page"},
            method = RequestMethod.GET)
    public Response<Pagination<List<LowEfficientCreativeVo>>> getLowEfficient(@ApiIgnore Context context,
            @ApiParam(value = "material_type") @RequestParam(value = "material_type", defaultValue = "video") MaterialType materialType,
            @ApiParam(value = "pageSize") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "pageNum") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {

        Pagination<List<InefficientCreative>> page = insightService.getLowEfficient(
                getAccountId(context), materialType, pageSize, pageNum);

        return Response.SUCCESS(page.map(list -> list.stream()
                .map(CreativeInsightConvertor.convertor::toVo)
                .collect(Collectors.toList())));
    }


    @ApiOperation(value = "暂停低效创意")
    @RequestMapping(value = "/pause", method = RequestMethod.POST)
    public Response pauseCreatives(@ApiIgnore Context context,
                                   @RequestBody List<Integer> creativeIds){

        insightService.pauseCreative(creativeIds, getOperator(context));

        return Response.SUCCESS(null);
    }


    public Operator getOperator(Context context) {
        return Operator.builder().operatorId(getRealAccountId(context))
                .operatorName(context.getUsername())
                .operatorType(OperatorType.getByCode(context.getType()))
                .systemType(systemType)
                .bilibiliUserName(context.getProxyId() > 0 ? context.getProxyName() + "(" + context.getProxyId() + ")"
                        : context.getProxyName())
                .build();
    }


    private Integer getAccountId(Context context){

        // 只有测试环境走mock
        if (!environmentUtil.isProd() && mockEnabled) {
            return mockAccountId;
        }

        return context.getAccountId();


    }

    private Integer getRealAccountId(Context context){

        return context.getAccountId();


    }
}
