package com.bilibili.mgk.platform.api.video_library.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/3/12
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoLibraryDto implements Serializable {

    private static final long serialVersionUID = -8102655520546538679L;

    /**
     * id
     */
    private Integer id;

    /**
     * 视频名称
     */
    private String name;

    /**
     * 视频bizId
     */
    private Integer bizId;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 视频URL
     */
    private String videoUrl;

    /**
     * 封面URL
     */
    private String cover;

    private String coverMd5;

    /**
     * 视频宽度
     */
    private Integer width;

    /**
     * 视频高度
     */
    private Integer height;

    /**
     * 视频md5
     */
    private String md5;

    /**
     * 大小(单位B)
     */
    private Integer size;

    /**
     * 时长(单位毫秒)
     */
    private Integer duration;

    /**
     * 0-其他 1-16:9 2-9:16 3-3:4 4-4:3
     */
    private Integer sizeType;

    /**
     * 清晰度类型: 0-默认 1-480P 2-1080P
     */
    private Integer definitionType;

    /**
     * 状态: 1-使用中 2-已删除
     */
    private Integer status;

    /**
     * 审核状态 1待审核 2审核通过 3审核驳回
     */
    private Integer auditStatus;

    /**
     * 审核拒绝原因
     */
    private String reason;

    /**
     * 修改时间
     */
    private Long mtime;

    private Long ctime;

    private Integer source;


    /**
     * 视频原始文件md5
     */
    private String rawMd5;
}
