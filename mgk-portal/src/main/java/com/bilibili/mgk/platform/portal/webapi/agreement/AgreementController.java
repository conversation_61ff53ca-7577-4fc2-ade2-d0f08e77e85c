package com.bilibili.mgk.platform.portal.webapi.agreement;

import com.bilibili.mgk.material.center.service.agreement.AccountAgreementService;
import com.bilibili.mgk.material.center.service.agreement.dto.AgreementAgreeReq;
import com.bilibili.mgk.material.center.service.agreement.dto.AgreementCheckReq;
import com.bilibili.mgk.material.center.service.agreement.dto.AgreementPermissionResultDTO;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/agreement")
@Api(value = "/agreement", tags = "通用协议服务")
public class AgreementController extends BasicController {

    @Resource
    private AccountAgreementService agreementService;

    @ApiOperation("封面衍生-待选择的衍生图列表")
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    public Response<AgreementPermissionResultDTO> check(@ApiIgnore Context context,
            @RequestBody AgreementCheckReq req) {

        return Response.SUCCESS(agreementService.check(req
                .setAccountId(context.getAccountId().longValue())
        ));

    }

    @ApiOperation("封面衍生-待选择的衍生图列表")
    @RequestMapping(value = "/agree", method = RequestMethod.POST)
    public Response<AgreementPermissionResultDTO> agree(@ApiIgnore Context context,
            @RequestBody AgreementAgreeReq req) {

        req.setAccountId(context.getAccountId().longValue());
        return Response.SUCCESS(agreementService.agree(req));

    }

}
