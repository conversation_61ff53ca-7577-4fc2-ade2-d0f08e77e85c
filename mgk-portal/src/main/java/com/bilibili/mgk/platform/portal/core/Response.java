package com.bilibili.mgk.platform.portal.core;

import com.bilibili.adp.common.exception.IExceptionCode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.http.HttpStatus;

/**
 * @ClassName Response
 * <AUTHOR>
 * @Date 2022/6/19 4:04 下午
 * @Version 1.0
 **/
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class Response<E> {
    private String status;
    private E result;
    private Integer error_code;
    private String error_msg;
    private Integer code;
    private String msg;

    private long current_time;

    public long getCurrent_time() {
        return current_time;
    }

    public void setCurrent_time(long current_time) {
        this.current_time = current_time;
    }

    public Response() {
    }

    private Response(String status, E result, Integer error_code, String error_msg, Integer code, String msg) {
        this.status = status;
        this.result = result;
        this.error_code = error_code;
        this.error_msg = error_msg;
        this.code = code;
        this.msg = msg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public E getResult() {
        return result;
    }

    public void setResult(E result) {
        this.result = result;
    }

    public Integer getError_code() {
        return error_code;
    }

    public void setError_code(Integer error_code) {
        this.error_code = error_code;
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public final static <E> Response<E> SUCCESS() {
        return SUCCESS(null);
    }

    public final static <E> Response<E> SUCCESS(E result) {
        Response response = new Response("success",
                result,
                null,
                null,
                HttpStatus.OK.value(),
                HttpStatus.OK.name());
        response.setCurrent_time(System.currentTimeMillis());
        return response;
    }

    public final static <E> Response<E> FAIL(Integer error_code, String error_msg) {
        return new Response("fail", null, error_code, error_msg, error_code, error_msg);
    }

    public final static <E> Response<E> FAIL(IExceptionCode exceptionCode) {
        return new Response("fail",
                null,
                exceptionCode.getCode(),
                exceptionCode.getMessage(),
                exceptionCode.getCode(),
                exceptionCode.getMessage());
    }

    public static <E> Response<E> failWithDetail(IExceptionCode exceptionCode, E detail) {
        return new Response<>("fail",
                detail,
                exceptionCode.getCode(),
                exceptionCode.getMessage(),
                exceptionCode.getCode(),
                exceptionCode.getMessage());
    }

    @Override
    public String toString() {
        return "Response{" +
                "status='" + status + '\'' +
                ", result=" + result +
                ", error_code=" + error_code +
                ", error_msg='" + error_msg + '\'' +
                ", code=" + code +
                ", msg='" + msg + '\'' +
                ", current_time=" + current_time +
                '}';
    }
}
