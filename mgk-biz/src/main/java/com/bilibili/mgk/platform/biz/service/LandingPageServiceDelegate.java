package com.bilibili.mgk.platform.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bapis.ad.adp.component.QueryMessageComponentsRes;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.*;
import com.bilibili.adp.http.utils.OkHttpUtils;

import com.bilibili.adp.launch.api.minigame.dto.LauMiniGameDto;
import com.bilibili.adp.launch.api.minigame.dto.QueryLauMiniGameDto;
import com.bilibili.adp.launch.api.soa.ISoaLauMiniGameService;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.app_package.dto.QueryAppPackageDto;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.collage.biz.config.EsSearchConfig;
import com.bilibili.collage.biz.service.EsSearchService;
import com.bilibili.cpt.platform.soa.ISoaCptCreativeService;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.mgk.platform.api.abyss.IMgkAbyssService;
import com.bilibili.mgk.platform.api.account.IMgkAccountService;
import com.bilibili.mgk.platform.api.account.dto.AccountCustomerInfoDto;
import com.bilibili.mgk.platform.api.account.dto.AccountCustomerNameInfoDto;
import com.bilibili.mgk.platform.api.app_package.dto.ResAppPackageDto;
import com.bilibili.mgk.platform.api.app_package.service.IResAppPackageService;
import com.bilibili.mgk.platform.api.archive.dto.MgkCmArchiveDto;
import com.bilibili.mgk.platform.api.archive.service.ILauArchiveInfoService;
import com.bilibili.mgk.platform.api.archive.service.IMgkCmArchiveService;
import com.bilibili.mgk.platform.api.audit.service.IMgkAuditPageService;
import com.bilibili.mgk.platform.api.click_house.ICHStatService;
import com.bilibili.mgk.platform.api.click_house.dto.CHMgkPagePvCtrDto;
import com.bilibili.mgk.platform.api.click_house.dto.CHQueryStatParamDto;
import com.bilibili.mgk.platform.api.download.IMgkPageDownloadComponentService;
import com.bilibili.mgk.platform.api.download.dto.MgkPageDownloadComponentHeightDto;
import com.bilibili.mgk.platform.api.es.dto.ESMgkHotLandingPageDto;
import com.bilibili.mgk.platform.api.es.service.IMgkPageEsService;
import com.bilibili.mgk.platform.api.form.dto.MgkFormDto;
import com.bilibili.mgk.platform.api.form.dto.MgkPageFormDto;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.bilibili.mgk.platform.api.es.dto.MgkPagePvCtrDto;
import com.bilibili.mgk.platform.api.es.service.IMgkOnlineHomeEsService;
import com.bilibili.mgk.platform.api.landing_page.dto.*;
import com.bilibili.mgk.platform.api.landing_page.service.*;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.QueryLandingPageGroupMappingDto;
import com.bilibili.mgk.platform.api.landing_page_group.service.ILandingPageGroupService;
import com.bilibili.mgk.platform.api.landing_page_group.service.mapping.ILandingPageGroupMappingService;
import com.bilibili.mgk.platform.api.log.dto.MgkPageAuditRecordInfoDto;
import com.bilibili.mgk.platform.api.log.dto.MgkPageOperateLogDto;
import com.bilibili.mgk.platform.api.log.dto.MgkPageOperateLogInfoDto;
import com.bilibili.mgk.platform.api.log.dto.NewLogOperationDto;
import com.bilibili.mgk.platform.api.log.service.IMgkLogService;
import com.bilibili.mgk.platform.api.log.service.IMgkPageOperateLogService;
import com.bilibili.mgk.platform.api.model.dto.MgkModelDto;
import com.bilibili.mgk.platform.api.shadow.IMgkShadowLandingPageService;
import com.bilibili.mgk.platform.api.video_library.dto.VideoLibraryDto;
import com.bilibili.mgk.platform.api.video_library.service.IVideoLibraryService;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatPackageService;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWorkWxCustomerService;
import com.bilibili.mgk.platform.biz.ad.dao.AccAccountAwakenAppMappingDao;
import com.bilibili.mgk.platform.biz.ad.dao.ResAppPackageApkAuthDao;
import com.bilibili.mgk.platform.biz.ad.dao.ResAppPackageDao;
import com.bilibili.mgk.platform.biz.ad.dao.ResAwakenAppWhitelistDao;
import com.bilibili.mgk.platform.biz.ad.po.*;
import com.bilibili.mgk.platform.biz.dao.*;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageAppPackageDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageAvidDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageShowUrlDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkPageBizMappingDao;
import com.bilibili.mgk.platform.biz.databus.pub.PubBusinessToolDisable;
import com.bilibili.mgk.platform.biz.grpc.MgkGrpcManager;
import com.bilibili.mgk.platform.biz.log.LandingPageConfigLogBean;
import com.bilibili.mgk.platform.biz.po.*;
import com.bilibili.mgk.platform.biz.service.mini_game.MgkMiniGameMappingServiceImpl;
import com.bilibili.mgk.platform.biz.service.mini_game.MgkMiniGameServiceDelegate;
import com.bilibili.mgk.platform.biz.service.page.LandingPageTransformDto;
import com.bilibili.mgk.platform.biz.service.page.LandingPageUrlProc;
import com.bilibili.mgk.platform.biz.service.page.dto.RefreshPageConfigDto;
import com.bilibili.mgk.platform.biz.service.wechat.impl.MgkWechatPackageMappingServiceImpl;
import com.bilibili.mgk.platform.biz.utils.MgkLandingPageConfigJudgeUtil;
import com.bilibili.mgk.platform.biz.validator.MgkLandingPageValidator;
import com.bilibili.mgk.platform.common.*;
import com.bilibili.mgk.platform.common.enums.page.BusinessToolTypeEnum;
import com.bilibili.mgk.platform.common.enums.page.OptTypeEnum;
import com.bilibili.mgk.platform.common.page_bean.*;
import com.bilibili.mgk.platform.common.page_group.PageGroupSourceEnum;
import com.bilibili.mgk.platform.common.utils.NumberUtils;
import com.dianping.cat.Cat;
import com.github.pagehelper.PageInfo;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.json.JSONException;
import org.redisson.api.RLock;
import org.skyscreamer.jsonassert.JSONCompare;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.beans.PropertyDescriptor;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.alibaba.fastjson.JSON.toJSON;
import static com.bilibili.mgk.platform.common.MgkConstants.*;
import static org.elasticsearch.index.query.QueryBuilders.*;
import static pleiades.venus.logging.support.marker.Markers.appendEntries;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Service
@Slf4j
public class LandingPageServiceDelegate {

    private static final Logger LOGGER = LoggerFactory.getLogger(LandingPageServiceDelegate.class);

    private static final String URL_TAG_LIANTOU_NEW_VERSION = "&joint_ad_new_version=1";

    private static final List<String> HTTP_SCHEMES = Lists.newArrayList("http", "https");

    private static final List<String> COMPONENT_VIDEO_NAME_LIST = Lists.newArrayList("plain-video", "full-video", "top-landscape-video", "top-portrait-video");

    @Value("${mgk.refresh.cdn.url}")
    private String REFRESH_CDN_URL;

    @Value("${mgk.is.refresh.cdn:true}")
    private boolean isRefreshCdn;

    @Value("${mgk.template.new.version.switch:true}")
    private boolean templateSwitch;


    @Value("${mgk.template.new.version.page:0x100102}")
    private String templateSwitchVersion;

    @Value("${mgk.wechat.appId}")
    private String appId;

    @Value("${mgk.wechat.appSecret}")
    private String appSecret;

    @Value("${mgk.wechat.access_token_url}")
    private String accessTokenUrl;

    @Value("${mgk.wechat.jsapi_ticket_url}")
    private String jsapiTicketUrl;

    @Value("${mgk.ad.version.control}")
    private String mgkAdVersionControl;

    @Value("${mgk.wechat.manual}")
    private Integer mgkWechatManual;

    @Value("${mgk.download.over.limit.hint:1}")
    private Integer mgkDownloadOverLimitHint;

    @Value("${mgk.portal.enable.pre.redis.cache:true}")
    private Boolean enableRedisCache;


    @Resource(name = "pageCacheRefreshExecutor")
    private ThreadPoolTaskExecutor pageCacheRefreshExecutor;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private MgkLandingPageValidator landingPageValidator;

    @Autowired
    private IMgkLogService mgkLogService;
    @Autowired
    private IMgkPageOperateLogService mgkPageOperateLogService;

    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;
    @Autowired
    private IMgkAccountService mgkAccountService;
    @Autowired
    private ISoaCptCreativeService soaCptCreativeService;

    @Autowired
    private ISoaAppPackageService soaAppPackageService;

    @Autowired
    private MgkGrpcManager mgkGrpcManager;

    @Autowired
    private MgkLandingPageDao mgkLandingPageDao;
    @Autowired
    private MgkLandingPageConfigDao mgkLandingPageConfigDao;
    @Autowired
    private MgkLandingPageShowUrlDao mgkLandingPageShowUrlDao;
    @Autowired
    private MgkLandingPageAvidDao mgkLandingPageAvidDao;

    @Autowired
    private ExtMgkLandingPageShowUrlDao extMgkLandingPageShowUrlDao;
    @Autowired
    private ExtMgkLandingPageAvidDao extMgkLandingPageAvidDao;
    @Autowired
    private MgkLandingPageAppPackageDao mgkLandingPageAppPackageDao;
    @Autowired
    private ExtMgkLandingPageAppPackageDao extMgkLandingPageAppPackageDao;

    @Autowired
    private MgkPageBizMappingDao mgkPageBizMappingDao;
    @Autowired
    private ExtMgkPageBizMappingDao extMgkPageBizMappingDao;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    @Autowired
    private IMgkWechatPackageService mgkWechatPackageService;
    @Autowired
    private MgkPageFormMappingDao mgkPageFormMappingDao;

    @Autowired
    private AccAccountAwakenAppMappingDao accAccountAwakenAppMappingDao;

    @Autowired
    private ResAwakenAppWhitelistDao resAwakenAppWhitelistDao;

    @Autowired
    private ResAppPackageDao resAppPackageDao;

    @Autowired
    private ResAppPackageApkAuthDao resAppPackageApkAuthDao;

    @Autowired
    private IVideoLibraryService videoLibraryService;

    @Autowired
    private MgkModelDao mgkModelDao;

    @Autowired
    private IMgkMongoLandingPageService mgkMongoLandingPageService;

    @Autowired
    private IMgkOnlineHomeEsService mgkOnlineHomeService;

    @Autowired
    private MgkLandingPageRefreshLogDao mgkLandingPageRefreshLogDao;

    @Autowired
    private IMgkFormService mgkFormService;

    @Autowired
    private MgkWechatPackageMappingServiceImpl mgkWechatPackageMappingService;

    @Autowired
    private MgkMiniGameMappingServiceImpl mgkMiniGameMappingService;

    @Autowired
    private MgkTemplatePageMappingDao mgkTemplatePageMappingDao;

    @Autowired
    private MgkLandingPageTemplateMappingDao mgkLandingPageTemplateMappingDao;

    @Autowired
    private IResAppPackageService resAppPackageService;

    @Autowired
    private IMgkCmArchiveService cmArchiveService;

    @Autowired
    private IMgkLandingPageAvidService mgkLandingPageAvidService;

    @Autowired
    private MgkLandingPageArchiveRefreshLogDao landingPageArchiveRefreshLogDao;

    @Autowired
    private IMgkLandingPageArchiveRefreshLogService mgkLandingPageArchiveRefreshLogService;

    @Autowired
    private IMgkAuditPageService mgkAuditPageService;

    @Autowired
    private MgkLandingPageConfigJudgeUtil mgkLandingPageConfigJudgeUtil;

    @Autowired
    private IMgkPageDownloadComponentService mgkPageDownloadComponentService;

    @Autowired
    private ILandingPageGroupService landingPageGroupService;

    @Autowired
    private IMgkShadowLandingPageService mgkShadowLandingPageService;

    @Autowired
    private ILandingPageGroupMappingService landingPageGroupMappingService;

    @Autowired
    private ILauArchiveInfoService lauArchiveInfoService;

    @Autowired
    private LandingPageUrlProc landingPageUrlProc;

    @Autowired
    private ICHStatService chStatService;

    @Value("${mgk.wechat.manual}")
    private Integer wechatIsManual;

    @Getter
    private Map<Integer, String> apkAuthCodeMap;

    @Autowired
    private IMgkLandingPageCvrService pageCvrService;

    @Autowired
    private ModelServiceDelegate modelServiceDelegate;

    @Autowired
    private IMgkPageEsService pageEsService;

    @Autowired
    private IMgkWorkWxCustomerService workWxCustomerService;

    @Autowired
    private IMgkAbyssService abyssService;

    @Autowired
    private PubBusinessToolDisable pubBusinessToolDisable;

    @Autowired
    private IMgkLandingPageImageEnhancementService pageImageEnhancementService;

    @Autowired
    private MgkLandingPageGameDao mgkLandingPageGameDao;

    @Autowired
    private MgkBaseService mgkBaseService;

    @Autowired
    private EsSearchService esSearchService;

    @Autowired
    private EsSearchConfig esSearchConfig;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private ISoaLauMiniGameService soaLauMiniGameService;

    @Autowired
    private MgkConsultLandingPageDao mgkConsultLandingPageDao;

    @Autowired
    private MgkCustomerServiceAuthTokenDao mgkCustomerServiceAuthTokenDao;

    public static final TemplatePageDto initTemplatePageDto = TemplatePageDto.builder()
            .mgkPageId(0L)
            .launchUrl("")
            .launchUrlSecondary("")
            .build();

    @PostConstruct
    public void init() {
        this.loadApkAuthCode();
    }

//    @PostConstruct
//    private void initMgkHotPageIndex() {
//        esUtil.createIndex(MGK_HOT_PAGE_ES_INDEX, MgkConstants.MGK_HOT_PAGE_ES_TYPE, ESMgkHotLandingPagePo.class);
//    }

    private void loadApkAuthCode() {
        ResAppPackageApkAuthPoExample example = new ResAppPackageApkAuthPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<ResAppPackageApkAuthPo> poList = resAppPackageApkAuthDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            apkAuthCodeMap = Collections.emptyMap();
            return;
        }
        apkAuthCodeMap = poList.stream()
                .collect(Collectors.toMap(ResAppPackageApkAuthPo::getId, ResAppPackageApkAuthPo::getAuthName));
    }


    public void rejectMgkCreative(Operator operator, List<Long> mgkPageIds) {
        try {
            if (CollectionUtils.isEmpty(mgkPageIds)) {
                return;
            }
//            soaCpcCreativeService.auditRejectByMgkPageIds(operator, mgkPageIds);
            mgkGrpcManager.rejectCpcCreative(mgkPageIds);
            soaCptCreativeService.auditRejectByMgkPageIds(operator, mgkPageIds);
        } catch (Exception e) {
            LOGGER.error("reject mgk creative failed, e:{}, pageIds:{}", e, mgkPageIds);
            throw new IllegalArgumentException("系统异常，同步下线创意失败");
        }
    }

    public void manualRejectMgkCreative(List<Long> mgkPageIds) {
        if (CollectionUtils.isEmpty(mgkPageIds)) {
            return;
        }
        Operator operator = Operator.SYSTEM;
//        soaCpcCreativeService.auditRejectByMgkPageIds(operator, mgkPageIds);
        mgkGrpcManager.rejectCpcCreative(mgkPageIds);
        soaCptCreativeService.auditRejectByMgkPageIds(operator, mgkPageIds);

        mgkPageIds.forEach(pageId->pubBusinessToolDisable.businessToolDisablePub(BusinessToolStatusChangeDto.builder()
                .id(pageId).businessToolType(BusinessToolTypeEnum.PAGE.getCode())
                .optType(OptTypeEnum.REJECT.getCode()).build()));

    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public long create(Operator operator, NewLandingPageDto newLandingPageDto) {
        LOGGER.info("create operator: [{}], newLandingPageDto: [{}]", operator, newLandingPageDto);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");

        landingPageValidator.validateCreateLandingPage(newLandingPageDto);

        // 各种模板落地页副本不进行账户校验
        if (!IsModelEnum.TEMPLATE_IS_MODEL_LIST.contains(newLandingPageDto.getIsModel())) {
            AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(newLandingPageDto.getAccountId());
            Assert.notNull(accountBaseDto, "该账户不存在");
        }

        long pageId = snowflakeIdWorker.nextId();

        this.insertLandingPage(operator, pageId, newLandingPageDto);

        this.refreshPageFormScroll(newLandingPageDto.getIsFormScroll(), newLandingPageDto.getFormIds());

        this.saveLandingPageAppPackage(newLandingPageDto.getAppPackageId(), pageId);

        // 支持app ids 插入多个
        if (!CollectionUtils.isEmpty(newLandingPageDto.getAppPackageIds())) {
            this.batchSaveLandingPageAppPackage(newLandingPageDto.getAppPackageIds(), pageId);
        }

        workWxCustomerService.savePageCustomerAcqLink(pageId, newLandingPageDto.getCustomerAcquisitionLinkIds());

        this.saveLandingPageGame(pageId, newLandingPageDto.getGames());

        this.refreshPageConfigAndFormItemToRedis(pageId);

        return pageId;
    }

    private void refreshPageFormScroll(Integer isFormScroll, List<Long> formIds) {
        // 落地页表单数据刷新
        if (WhetherEnum.YES.getCode().equals(isFormScroll)
                && !CollectionUtils.isEmpty(formIds)) {
            mgkFormService.refreshFormSubmitInfo(formIds);
        }
    }

    private List<Integer> getBizMappingByPageId(Long pageId) {
        if (!Utils.isPositive(pageId)) {
            return Collections.emptyList();
        }
        MgkPageBizMappingPoExample exm = new MgkPageBizMappingPoExample();
        exm.or().andPageIdEqualTo(pageId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkPageBizMappingPo> pos = mgkPageBizMappingDao.selectByExample(exm);
        return pos.stream()
                .map(MgkPageBizMappingPo::getBizId)
                .collect(Collectors.toList());
    }


    private void batchSavePageBizMapping(NewLandingPageDto newLandingPageDto, Long pageId) {
        List<Integer> bizIds = newLandingPageDto.getBizIds();
        List<MgkPageBizMappingPo> mgkPageBizMappingPos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bizIds)) {
            bizIds = bizIds.stream().distinct().collect(Collectors.toList());
            List<Integer> finalBizIds = bizIds;
            Stream.iterate(0, i -> i + 1).limit(bizIds.size()).forEach(index -> {
                MgkPageBizMappingPo mgkPageBizMappingPo = MgkPageBizMappingPo.builder()
                        .pageId(pageId)
                        .bizId(finalBizIds.get(index))
                        .bizIndex(index)
                        .isDeleted(MgkPageBizMappingStatusEnum.VALID.getCode())
                        .build();
                mgkPageBizMappingPos.add(mgkPageBizMappingPo);
            });
            extMgkPageBizMappingDao.insertBatch(mgkPageBizMappingPos);
        }
    }

    private void batchSaveLandingPageAppPackage(List<Integer> appPackageIds, long mgkPageId) {
        this.deleteMgkPageAppPackageInPageIds(Lists.newArrayList(mgkPageId), MgkAppPakcageStatusEnum.INVALID);

        if (CollectionUtils.isEmpty(appPackageIds)) {
            return;
        }

        List<MgkLandingPageAppPackagePo> mgkLandingPageAppPackagePos = appPackageIds.stream().map(appPackageId -> {
            MgkLandingPageAppPackagePo record = new MgkLandingPageAppPackagePo();
            record.setAppPackageId(appPackageId);
            record.setPageId(mgkPageId);
            record.setStatus(MgkAppPakcageStatusEnum.VALID.getCode());
            return record;
        }).collect(Collectors.toList());

        extMgkLandingPageAppPackageDao.batchSave(mgkLandingPageAppPackagePos);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void deleteMgkPageAppPackageInPageIds(List<Long> mgkPageIds, MgkAppPakcageStatusEnum statusEnum) {
        if (CollectionUtils.isEmpty(mgkPageIds)) {
            return;
        }
        MgkLandingPageAppPackagePoExample example = new MgkLandingPageAppPackagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(mgkPageIds);
        List<MgkLandingPageAppPackagePo> existAppPackagePos = mgkLandingPageAppPackageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(existAppPackagePos)) {
            return;
        }

        MgkLandingPageAppPackagePoExample updateExample = new MgkLandingPageAppPackagePoExample();
        List<Integer> needDeleteIds = existAppPackagePos.stream()
                .map(MgkLandingPageAppPackagePo::getId)
                .collect(Collectors.toList());
        updateExample.or().andIdIn(needDeleteIds);
        MgkLandingPageAppPackagePo po = new MgkLandingPageAppPackagePo();
        po.setStatus(statusEnum.getCode());
        mgkLandingPageAppPackageDao.updateByExampleSelective(po, updateExample);
    }

    private void saveLandingPageAppPackage(Integer appPackageId, Long mgkPageId) {

        this.deleteMgkPageAppPackageInPageIds(Lists.newArrayList(mgkPageId), MgkAppPakcageStatusEnum.INVALID);

        if (appPackageId != null && appPackageId > 0) {
            MgkLandingPageAppPackagePo record = new MgkLandingPageAppPackagePo();
            record.setAppPackageId(appPackageId);
            record.setPageId(mgkPageId);
            record.setStatus(MgkAppPakcageStatusEnum.VALID.getCode());
            extMgkLandingPageAppPackageDao.batchSave(Lists.newArrayList(record));
        }
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void update(Operator operator, UpdateLandingPageDto updateLandingPageDto, boolean needRefreshCache) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        landingPageValidator.validateUpdateLandingPage(operator, updateLandingPageDto);

        this.updateLandingPage(operator, updateLandingPageDto);

        this.refreshPageFormScroll(updateLandingPageDto.getIsFormScroll(), updateLandingPageDto.getFormIds());

        this.saveLandingPageAppPackage(updateLandingPageDto.getAppPackageId(), updateLandingPageDto.getPageId());

        this.batchSaveLandingPageAppPackage(updateLandingPageDto.getAppPackageIds(), updateLandingPageDto.getPageId());

        this.saveLandingPageGame(updateLandingPageDto.getPageId(), updateLandingPageDto.getGames());

        if (needRefreshCache) {
            this.refreshPageConfigAndFormItemToRedis(updateLandingPageDto.getPageId());
        }
    }

    @VisibleForTesting
    public void asyncRefreshPageConfigAndFormItemToRedis(Long pageId) {
        pageCacheRefreshExecutor.execute(() -> {
            try {
                this.refreshPageConfigAndFormItemToRedis(pageId);
            }catch (Exception e){
                log.error("asyncRefreshPageConfigAndFormItemToRedis error", e);
            }
        });
    }

    @VisibleForTesting
    public void refreshPageConfigAndFormItemToRedis(Long pageId) {
        LandingPageConfigDto landingPageConfigDto = this.getLandingPageConfigDtoByPageId(pageId);
        if (LandingPageTypeEnum.NATIVE.getCode().equals(landingPageConfigDto.getType())
                || LandingPageTypeEnum.CUSTOM_NATIVE.getCode()
                .equals(landingPageConfigDto.getType())
                || LandingPageTypeEnum.APPLETS.getCode().equals(landingPageConfigDto.getType())
                || LandingPageTypeEnum.BUSINESS_TOOL.getCode().equals(landingPageConfigDto.getType())
                || LandingPageTypeEnum.CONSULT_PAGE.getCode().equals(landingPageConfigDto.getType())) {
            String pageIdRedisKey = LANDING_PAGE_PREFIX_IN_REDIS + pageId;
            // 审核副本 或者原始落地页非可浏览状态 不允许存在线上浏览版本
            // 20x不走这一逻辑
            // 但是需要预览最新内容 刷新预览缓存 否则刷新预加载用缓存
            if ((IsModelEnum.SHADOW_TEMPLATE.getCode().equals(landingPageConfigDto.getIsModel()))
                    || IsModelEnum.PAGE.getCode().equals(landingPageConfigDto.getIsModel())
                    && !LandingPageStatusEnum.CAN_BE_VIEW_STATUS_LIST.contains(landingPageConfigDto.getStatus())
                    && !LandingPageTypeEnum.NATIVE.getCode().equals(landingPageConfigDto.getType())) {
                pageIdRedisKey = PREVIEW_LANDING_PAGE_PREFIX_IN_REDIS + pageId;
            }
            PageConfig pageConfig = this.buildPageConfig(landingPageConfigDto);

            String pageConfigWithoutOptionsStr = JSON.toJSONString(pageConfig);
            stringRedisTemplate.opsForValue().set(pageIdRedisKey, pageConfigWithoutOptionsStr, LANDING_PAGE_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);
        }
        List<Long> formIds = landingPageConfigDto.getFormIds();
        if (!CollectionUtils.isEmpty(formIds)) {
            mgkFormService.refreshFormItemCacheByFormIds(formIds);
        }
    }


    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public long copy(Operator operator, NewLandingPageDto newLandingPageDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(newLandingPageDto.getOriginPageId(), "原始落地页页面ID不可为空");
        MgkLandingPagePo landingPagePo = this.getByPageId(newLandingPageDto.getOriginPageId());
        Assert.notNull(landingPagePo, "被复制的落地页不存在");
        return this.create(operator, newLandingPageDto);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void publish(Long pageId) {
        this.updateLandingPageStatus(pageId, "", LandingPageStatusEnum.PUBLISHED);

        List<Integer> wechatPackageIds = mgkWechatPackageMappingService.getPageMappingIdsByPageId(pageId);
        mgkWechatPackageService.refreshWechatPackageCacheByIds(wechatPackageIds);

        this.refreshCDN(pageId);

        this.refreshPageConfigAndFormItemToRedis(pageId);

        //cvr处理任务
        pageCvrService.addPageToDealCvrQueue(pageId);

        pageImageEnhancementService.addPageToDealImageQueue(pageId);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void manualPublish(Operator operator, Long pageId) {
        boolean needRefresh = this.manualUpdateLandingPageStatus(operator, pageId, "", LandingPageStatusEnum.PUBLISHED);
        if (!needRefresh) {
            return;
        }

        List<Integer> wechatPackageIds = mgkWechatPackageMappingService.getPageMappingIdsByPageId(pageId);
        mgkWechatPackageService.refreshWechatPackageCacheByIds(wechatPackageIds);

        this.refreshCDN(pageId);

        this.refreshPageConfigAndFormItemToRedis(pageId);

        pageCvrService.addPageToDealCvrQueue(pageId);

        pageImageEnhancementService.addPageToDealImageQueue(pageId);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void sendAudit(Operator operator, Long pageId, Long shadowPageId) {
        sendAudit(operator, pageId);
        if (shadowPageId != null) {
            sendAudit(operator, shadowPageId);
        }
    }

    public void sendAudit(Operator operator, Long pageId) {
        boolean needRefresh = this.manualUpdateLandingPageStatus(operator, pageId, "", LandingPageStatusEnum.WAIT_AUDIT);
        if (!needRefresh) {
            return;
        }

        List<Integer> wechatPackageIds = mgkWechatPackageMappingService.getPageMappingIdsByPageId(pageId);
        mgkWechatPackageService.refreshWechatPackageCacheByIds(wechatPackageIds);

        this.refreshCDN(pageId);

        this.refreshPageConfigAndFormItemToRedis(pageId);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void resetUnpublished(Operator operator, Long pageId) {
        this.updateLandingPageStatus(pageId, "", LandingPageStatusEnum.UNPUBLISHED);

        List<Integer> wechatPackageIds = mgkWechatPackageMappingService.getPageMappingIdsByPageId(pageId);
        mgkWechatPackageService.refreshWechatPackageCacheByIds(wechatPackageIds);

        this.refreshCDN(pageId);

        this.refreshPageConfigAndFormItemToRedis(pageId);
    }

    public List<Long> getPageIdsByMiniGameId(Integer miniGameId) {
        return mgkMiniGameMappingService.getPageIdsByMiniGameId(miniGameId);
    }


    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public List<Long> batchDownline(Operator operator, List<Long> pageIds, String reason) {

        LOGGER.info("batchDownline operator: [{}], pageIds: [{}], reason: [{}]", operator, pageIds, reason);
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        List<MgkLandingPagePo> pos = this.getInPageIds(pageIds);
        Assert.notEmpty(pos, "落地页不存在");

        // 影子落地页不下线 没有必要 最多对外展示15分钟
        List<Long> canDownlinePageIds = pos.stream()
                .filter(po -> !IsModelEnum.SHADOW_TEMPLATE.getCode().equals(po.getIsModel()))
                .filter(po -> LandingPageStatusEnum.getByCode(po.getStatus()).validateToStatus(LandingPageStatusEnum.DOWNLINE))
                .map(MgkLandingPagePo::getPageId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canDownlinePageIds)) {
            LOGGER.info("canDownlinePageIds is null");
            return Collections.emptyList();
        }

        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(canDownlinePageIds);

        MgkLandingPagePo record = new MgkLandingPagePo();
        record.setStatus(LandingPageStatusEnum.DOWNLINE.getCode());
        record.setReason(Strings.isNullOrEmpty(reason) ? "" : reason);

        int result = mgkLandingPageDao.updateByExampleSelective(record, example);
        Assert.isTrue(result >= 1, "操作失败, 请稍后重试");

        pos.stream().filter(po -> canDownlinePageIds.contains(po.getPageId())).forEach(po -> {

            this.refreshCDN(po.getPageId());
            //insert log
            mgkLogService.insertLog(NewLogOperationDto.builder()
                    .accountId(po.getAccountId())
                    .objId(po.getPageId())
                    .objFlag(LogObjFlagEnum.LANDING_PAGE.getCode())
                    .operateType(LogOperateTypeEnum.LANDING_PAGE_DOWNLINE.getCode())
                    .operatorUsername(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .oldValue("")
                    .newValue("")
                    .build());

            pubBusinessToolDisable.businessToolDisablePub(BusinessToolStatusChangeDto.builder()
                    .id(po.getPageId()).rejectReason(reason).businessToolType(BusinessToolTypeEnum.PAGE.getCode())
                    .optType(OptTypeEnum.DOWNLINE.getCode()).build());
        });
        this.refreshPageConfigNullToRedis(canDownlinePageIds);

        this.rejectMgkCreative(operator, canDownlinePageIds);

        QueryLandingPageGroupMappingDto queryPageGroupMappingDto = QueryLandingPageGroupMappingDto.builder()
                .pageIdList(pageIds)
                .groupSource(PageGroupSourceEnum.MGK_SOURCE.getCode())
                .build();
        List<LandingPageGroupMappingDto> mappingDtoList =
                landingPageGroupMappingService.queryPageGroupMappingDto(queryPageGroupMappingDto);
        landingPageGroupService.updateLandingPageGroupStatusByUnavailableMapping(mappingDtoList, operator);

        return canDownlinePageIds;
    }

    private void refreshPageConfigNullToRedis(List<Long> pageIds) {
        for (Long pageId : pageIds) {
            stringRedisTemplate.opsForValue().set(LANDING_PAGE_PREFIX_IN_REDIS + pageId, JSON.toJSONString(PageConfig.nullPageConfig()), LANDING_PAGE_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);
        }
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void batchDisable(Operator operator, List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return;
        }
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        List<MgkLandingPagePo> pos = this.getInPageIds(pageIds);
        Assert.notEmpty(pos, "落地页不存在");

        pos.forEach(po -> this.validateToStatus(LandingPageStatusEnum.getByCode(po.getStatus()), LandingPageStatusEnum.DELETED));

        List<Long> appletsOriginLandingPageIdList = pos.stream()
                .filter(po -> IsModelEnum.PAGE.getCode().equals(po.getIsModel())
                        && TemplateStyleEnum.APPLETS.getCode().equals(po.getTemplateStyle()))
                .map(MgkLandingPagePo::getPageId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(appletsOriginLandingPageIdList)) {
            List<Long> relatedPageGroupIdList = landingPageGroupService.getRelatedPageGroupIdList(appletsOriginLandingPageIdList);
            Assert.isTrue(CollectionUtils.isEmpty(relatedPageGroupIdList),
                    "目标落地页已经被落地页组" + relatedPageGroupIdList.toString() + "使用,删除失败");
        }

        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andPageIdIn(pageIds);

        int result = mgkLandingPageDao.updateByExampleSelective(
                MgkLandingPagePo.builder().status(LandingPageStatusEnum.DELETED.getCode()).build(), example);
        Assert.isTrue(result >= 1, "操作失败, 请稍后重试");

        //评论区暗投表单没有创意,不需要做其他操作
        pos = pos.stream().filter(po -> !IsModelEnum.COMMENT.getCode().equals(po.getIsModel()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }
        List<Long> needDealCreativePageIds = pos.stream()
                .map(MgkLandingPagePo::getPageId)
                .collect(Collectors.toList());
        log.info("batchDisable needDealCreativePageIds [{}]", needDealCreativePageIds);

        pos.forEach(po -> {

            this.refreshCDN(po.getPageId());

            //insert log
            mgkLogService.insertLog(NewLogOperationDto.builder()
                    .accountId(po.getAccountId())
                    .objId(po.getPageId())
                    .objFlag(LogObjFlagEnum.LANDING_PAGE.getCode())
                    .operateType(LogOperateTypeEnum.LANDING_PAGE_DELETED.getCode())
                    .operatorUsername(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .oldValue("")
                    .newValue("")
                    .build());

            pubBusinessToolDisable.businessToolDisablePub(BusinessToolStatusChangeDto.builder()
                    .id(po.getPageId()).businessToolType(BusinessToolTypeEnum.PAGE.getCode())
                    .optType(OptTypeEnum.DOWNLINE.getCode()).build());
        });

        mgkAuditPageService.cancelAudit(needDealCreativePageIds, "落地页被删除");

        this.refreshPageConfigNullToRedis(needDealCreativePageIds);

        this.rejectMgkCreative(operator, needDealCreativePageIds);

        List<MgkPageOperateLogDto> logDtoList = pos.stream().map(po -> {
            MgkPageOperateLogDto logDto = MgkPageOperateLogDto.builder()
                    .pageId(po.getPageId())
                    .operatorName(operator.getOperatorName())
                    .operatorId(operator.getOperatorId())
                    .operatorType(operator.getOperatorType().getCode())
                    .operateType(MgkOperationType.DISABLE.getCode())
                    .originStatusDesc(LandingPageStatusEnum.getByCode(po.getStatus()).getDesc())
                    .newStatusDesc(LandingPageStatusEnum.DELETED.getDesc())
                    .build();
            return logDto;
        }).collect(Collectors.toList());
        mgkPageOperateLogService.addBatchUpdateStatusLog(logDtoList);

        //删除相关私信组件
        taskExecutor.execute(() -> {deleteMessageComponents(pageIds, operator);});
    }

    private void deleteMessageComponents(List<Long> pageIds, Operator operator) {
        for(Long pageId : pageIds) {
            QueryMessageComponentsRes queryMessageComponentsRes = mgkGrpcManager.queryMessageComponents(Long.valueOf(operator.getOperatorId()), pageId);
            log.info("queryMessageComponentsRes={}",JSON.toJSONString(queryMessageComponentsRes));
            if(queryMessageComponentsRes.getTotal() > 0) {
                mgkGrpcManager.deleteMessageComponent(Long.valueOf(operator.getOperatorId()), queryMessageComponentsRes.getCreateMessageComponentRes(0).getAdCreativeId());
            }
        }

    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void downline(Operator operator, long pageId) {
        this.manualUpdateLandingPageStatus(operator, pageId, "", LandingPageStatusEnum.DOWNLINE);

        this.refreshCDN(pageId);

        this.refreshPageConfigNullToRedis(Collections.singletonList(pageId));

        this.rejectMgkCreative(operator, Lists.newArrayList(pageId));

        QueryLandingPageGroupMappingDto queryPageGroupMappingDto = QueryLandingPageGroupMappingDto.builder()
                .pageIdList(Lists.newArrayList(pageId))
                .groupSource(PageGroupSourceEnum.MGK_SOURCE.getCode())
                .build();
        List<LandingPageGroupMappingDto> mappingDtoList =
                landingPageGroupMappingService.queryPageGroupMappingDto(queryPageGroupMappingDto);
        landingPageGroupService.updateLandingPageGroupStatusByUnavailableMapping(mappingDtoList, operator);

        pubBusinessToolDisable.businessToolDisablePub(BusinessToolStatusChangeDto.builder()
                .id(pageId).businessToolType(BusinessToolTypeEnum.PAGE.getCode())
                .optType(OptTypeEnum.DOWNLINE.getCode()).build());
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void auditReject(Operator operator, Long pageId, String reason) {
        Assert.isTrue(Utils.isPositive(pageId), "审核拒绝落地页id不可为空");
        this.updateLandingPageStatus(pageId, reason, LandingPageStatusEnum.AUDIT_REJECT);
        this.refreshCDN(pageId);
        this.refreshPageConfigNullToRedis(Collections.singletonList(pageId));
        this.rejectMgkCreative(operator, Lists.newArrayList(pageId));

        pubBusinessToolDisable.businessToolDisablePub(BusinessToolStatusChangeDto.builder()
                .id(pageId).rejectReason(reason).businessToolType(BusinessToolTypeEnum.PAGE.getCode())
                .optType(OptTypeEnum.REJECT.getCode()).build());
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void auditPass(Long pageId, Long shadowPageId) {
        Assert.isTrue(Utils.isPositive(shadowPageId), "审核副本落地页不存在");
        this.shadowPageWriteBack(pageId, shadowPageId, true);
        // 最后进行一次发布
        this.publish(pageId);
    }

    /**
     * 记录日志所需信息 必须是查询影子落地页 因为审核拒绝时 影子内容不回写 但是链接pageId需要原始的pageId
     */
    public MgkPageAuditRecordInfoDto generatePageAuditRecordInfoDto(Long originPageId, Long shadowPageId) {
        MgkLandingPageDto landingPageDto =
                getLandingPageDtoByPageId(originPageId);
        LaunchUrlPageDto pageDto = LaunchUrlPageDto.builder()
                .pageId(landingPageDto.getPageId())
                .type(landingPageDto.getType())
                .templateStyle(landingPageDto.getTemplateStyle())
                .header(landingPageDto.getHeader())
                .build();
        String pcLaunchUrl =
                landingPageUrlProc.getLaunchUrl(WhetherEnum.YES.getCode(), pageDto, false);
        String mobileLaunchUrl =
                landingPageUrlProc.getLaunchUrl(WhetherEnum.NO.getCode(), pageDto, false);
        Map<Long, List<Integer>> appPackageIdMap = this.getAppPackageIdMapInPageIds(Lists.newArrayList(shadowPageId));
        List<Integer> wechatPackageIds = mgkWechatPackageMappingService.getPageMappingIdsByPageId(shadowPageId);

        return MgkPageAuditRecordInfoDto.builder()
                .accountId(landingPageDto.getAccountId())
                .appPackageIds(appPackageIdMap.getOrDefault(shadowPageId, Collections.emptyList()))
                .wechatPackageId(CollectionUtils.isEmpty(wechatPackageIds) ? 0 : wechatPackageIds.get(0))
                .jumpUrl(mobileLaunchUrl)
                .jumpUrlSecondary(pcLaunchUrl)
                .build();
    }

    public void validateShadowVersion(Long shadowPageId, Long shadowVersion) {
        Assert.isTrue(Utils.isPositive(shadowPageId), "落地页id不可为空");
        Assert.isTrue(Objects.nonNull(shadowVersion), "审核版本不可为空");
        MgkLandingPageDto shadowPageDto = this.getLandingPageDtoByPageId(shadowPageId);
        Assert.notNull(shadowPageDto, "审核副本不存在");
        Assert.isTrue(shadowVersion.equals(shadowPageDto.getVersion()),
                "当前审核副本已不是最新版本,审核失败");
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void downlineForAppPackageDel(Operator operator, List<Long> mgkPageIds) {
        if (CollectionUtils.isEmpty(mgkPageIds)) {
            return;
        }
        List<MgkLandingPagePo> pos = this.getInPageIds(mgkPageIds);
        Assert.notEmpty(pos, "落地页不存在");

        List<Long> canDownlinePageIds = pos.stream().filter(po -> LandingPageStatusEnum.getByCode(po.getStatus())
                .validateToStatus(LandingPageStatusEnum.DOWNLINE))
                .map(MgkLandingPagePo::getPageId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(canDownlinePageIds)){
            return;
        }
        this.deleteMgkPageAppPackageInPageIds(canDownlinePageIds, MgkAppPakcageStatusEnum.DELETED);
        mgkAuditPageService.cancelAudit(canDownlinePageIds, "下载包被删除");
        this.batchDownline(operator, canDownlinePageIds, "下载包被删除");
    }

    public Map<Integer, List<Long>> getAppPackageId2MgkPageIdMapInAppIds(List<Integer> appPackageIds) {

        MgkLandingPageAppPackagePoExample example = new MgkLandingPageAppPackagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(MgkAppPakcageStatusEnum.VALID.getCode())
                .andAppPackageIdIn(appPackageIds);
        List<MgkLandingPageAppPackagePo> pos = mgkLandingPageAppPackageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }
        return pos.stream().collect(Collectors.groupingBy(MgkLandingPageAppPackagePo::getAppPackageId, Collectors.mapping(MgkLandingPageAppPackagePo::getPageId, Collectors.toList())));
    }

    public Map<Integer, List<Long>> getValidBindedAppPackageId2MgkPageIdMap(int start, int limit) {
        MgkLandingPageAppPackagePoExample example = new MgkLandingPageAppPackagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(MgkAppPakcageStatusEnum.VALID.getCode())
        .andIdGreaterThanOrEqualTo(start);
        example.setLimit(limit);

        List<MgkLandingPageAppPackagePo> pos = mgkLandingPageAppPackageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }
        return pos.stream().collect(Collectors.groupingBy(MgkLandingPageAppPackagePo::getAppPackageId,
                Collectors.mapping(MgkLandingPageAppPackagePo::getPageId, Collectors.toList())));
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void reject(Operator operator, long pageId, String reason) {
        this.manualUpdateLandingPageStatus(operator, pageId, reason, LandingPageStatusEnum.ADMIN_REJECT);

        this.refreshCDN(pageId);

        pubBusinessToolDisable.businessToolDisablePub(BusinessToolStatusChangeDto.builder()
                .id(pageId).rejectReason(reason).businessToolType(BusinessToolTypeEnum.PAGE.getCode())
                .optType(OptTypeEnum.REJECT.getCode()).build());
    }

    public LandingPageConfigDto getLandingPageEditConfigDtoByOriginPageId(Long pageId) {
        Assert.notNull(pageId, "落地页页面ID不可为空");
        LandingPageConfigDto originPageConfigDto = getLandingPageConfigDtoByPageId(pageId);
        // 原始落地页状态非已发布时,返回原始落地页内容 此时原始落地页作为编辑用版本
        if (!LandingPageStatusEnum.PUBLISHED.getCode().equals(originPageConfigDto.getStatus())) {
            return originPageConfigDto;
        }
        Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(pageId);
        // 存量落地页可能还没生成影子落地页 返回原始落地页
        if (!Utils.isPositive(shadowPageId)) {
            return originPageConfigDto;
        }
        LandingPageConfigDto shadowPageConfigDto = getLandingPageConfigDtoByPageId(shadowPageId);
        // 填充原始落地页状态
        shadowPageConfigDto.setStatus(originPageConfigDto.getStatus());
        shadowPageConfigDto.setPageId(originPageConfigDto.getPageId());

        // 替换跳转url
        if (LandingPageTypeEnum.APPLETS.getCode().equals(shadowPageConfigDto.getType())) {
            shadowPageConfigDto.setMobileLaunchUrl(landingPageUrlProc.getMobileLaunchUrl(shadowPageConfigDto.getPageId(),
                    shadowPageConfigDto.getHeader()));
            shadowPageConfigDto.setPcLaunchUrl(landingPageUrlProc.getPcLaunchUrl(shadowPageConfigDto.getPageId(),
                    shadowPageConfigDto.getHeader()));
        }
        // 替换跳转url
        if (LandingPageTypeEnum.BUSINESS_TOOL.getCode().equals(shadowPageConfigDto.getType())) {
            shadowPageConfigDto.setPcLaunchUrl(landingPageUrlProc.getPcLaunchUrl(shadowPageConfigDto.getPageId(),
                    shadowPageConfigDto.getHeader()));
        }
        return shadowPageConfigDto;
    }

    public LandingPageConfigDto getLandingPageConfigDtoByPageId(Long pageId) {
        Assert.notNull(pageId, "落地页页面ID不可为空");
        MgkLandingPagePo landingPagePo = this.getByPageId(pageId);
        LOGGER.info("[debug]: landingPagePo:[{}]",  JSONObject.toJSONString(landingPagePo));

        Assert.notNull(landingPagePo, "该落地页不存在");

        MgkLandingPageConfigPo landingPageConfigPo = this.getConfigByPageId(pageId);

        LOGGER.info("[debug]: landingPageConfigPo:[{}]",  JSONObject.toJSONString(landingPageConfigPo));

        Assert.notNull(landingPageConfigPo, "该落地页配置不存在");

        List<String> showUrls = this.getShowUrlsByPageId(pageId);

        List<Integer> landingPageBizIds = this.getBizMappingByPageId(pageId);
        List<Long> landingPageAvids = this.getAvIdsByPageId(pageId).stream().map(MgkLandingPageAvidPo::getAvId).collect(Collectors.toList());
        List<Integer> appPackageIds = this.getBindingAppPackageIdsInPageIds(Lists.newArrayList(pageId));
        List<Long> formIds = this.getPageFormMappingPoByPageId(pageId).stream().map(MgkPageFormMappingPo::getFormId).collect(Collectors.toList());
        List<Integer> miniGameIds = mgkMiniGameMappingService.getPageMappingIdsByPageId(pageId);
        List<Integer> wechatPackageIds = mgkWechatPackageMappingService.getPageMappingIdsByPageId(pageId);
        Map<Long, List<String>> pageId2LinkIds = workWxCustomerService.getPageCustomerAcqLinkByPageIds(Lists.newArrayList(pageId));
        AccountCustomerNameInfoDto accountCustomerInfo = mgkAccountService.getAccountCustomerNameInfo(landingPagePo.getAccountId());
        String privacyName = accountCustomerInfo.getCustomerName();
        MgkPageDownloadComponentHeightDto downloadComponentHeightDto = mgkPageDownloadComponentService.getByPageId(pageId);
        Map<Long, List<GameDto>> landingPageGameMap = this.getLandingPageGames(Lists.newArrayList(pageId));

        LandingPageTransformDto transformDto = LandingPageTransformDto.builder()
                .landingPagePo(landingPagePo)
                .config(landingPageConfigPo.getConfig())
                .landingPageBizIds(landingPageBizIds)
                .landingPageAvIds(landingPageAvids)
                .appPackageIds(appPackageIds)
                .showUrls(showUrls)
                .formIds(formIds)
                .miniGameIds(miniGameIds)
                .wechatPackageIds(wechatPackageIds)
                .privacyName(privacyName)
                .customerAcquisitionLinkIds(pageId2LinkIds.getOrDefault(pageId, new ArrayList<>()))
                .downloadComponentHeightDto(downloadComponentHeightDto)
                .games(landingPageGameMap.get(pageId))
                .build();

        return this.convertLandingPageConfigPoToDto(transformDto);
    }

    public PageConfig getLandingPageConfigForMobByPageId(Long pageId) {

        Assert.notNull(pageId, "落地页页面ID不可为空");

        String pageIdRedisKey = LANDING_PAGE_PREFIX_IN_REDIS + pageId;

        String pageConfigString = stringRedisTemplate.opsForValue().get(pageIdRedisKey);

        if (!Strings.isNullOrEmpty(pageConfigString) && !"{}".equals(pageConfigString)) {
            LOGGER.info("[debug]: pageConfigString:[{}]", pageConfigString);
            return JSONObject.parseObject(pageConfigString, PageConfig.class);
        }

        // 埋点 观察哪些落地页没有走缓存
        String message = "landing page config come from db, pageId:" + pageId;
        Map<String, Object> meta = new HashMap<>();
        meta.put("code", MgkConstants.MGK_QUERY_FROM_DB_LOG_CODE);
        meta.put("ts", System.currentTimeMillis());
        meta.put("msg", message);
        log.info(appendEntries(meta), Strings.nullToEmpty(message));

        LandingPageConfigDto landingPageConfigDto = this.getLandingPageConfigDtoByPageId(pageId);
        LOGGER.info("[debug]: landingPageConfigDto:[{}]",  JSONObject.toJSONString(landingPageConfigDto));

        if (landingPageConfigDto == null
                || !LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageConfigDto.getStatus())
                || IsModelEnum.SHADOW_TEMPLATE.getCode().equals(landingPageConfigDto.getIsModel())) {
            stringRedisTemplate.opsForValue().set(pageIdRedisKey, JSON.toJSONString(PageConfig.nullPageConfig()), LANDING_PAGE_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);
            return PageConfig.nullPageConfig();
        }

        PageConfig pageConfig = this.buildPageConfig(landingPageConfigDto);

        stringRedisTemplate.opsForValue().set(pageIdRedisKey, JSON.toJSONString(pageConfig), LANDING_PAGE_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);

        String pageConfigJsonString = JSON.toJSONString(pageConfig);
        stringRedisTemplate.opsForValue().set(pageIdRedisKey, pageConfigJsonString, LANDING_PAGE_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);
        return JSONObject.parseObject(pageConfigJsonString, PageConfig.class);

    }

    public PageConfig getLandingPageConfigForPreviewMobByPageId(Long pageId, Long shadowPageId, Integer previewMode) {

        Assert.notNull(pageId, "落地页页面ID不可为空");

        // 没有审核影子落地页 无条件走原始落地页预览
        if (!Utils.isPositive(shadowPageId)) {
            return getPreviewPageConfigByPageId(pageId);
        }

        // 存在影子落地页时审核预览 无条件返回审核落地页
        if (MgkPreviewModeEnum.AUDIT_PREVIEW.getCode().equals(previewMode)) {
            return getPreviewPageConfigByPageId(shadowPageId);
        }

        // 建站预览 根据原始落地页状态判断 为已发布时，返回影子落地页
        MgkLandingPageDto pageDto = this.getLandingPageDtoByPageId(pageId);
        if (Objects.isNull(pageDto)) {
            return PageConfig.nullPageConfig();
        }
        Long previewPageId = LandingPageStatusEnum.PUBLISHED.getCode().equals(pageDto.getStatus()) ?
                shadowPageId : pageId;
        return getPreviewPageConfigByPageId(previewPageId);
    }

    private PageConfig getPreviewPageConfigByPageId(Long pageId) {
        String pageIdRedisKey = PREVIEW_LANDING_PAGE_PREFIX_IN_REDIS + pageId;
        String pageConfigString = stringRedisTemplate.opsForValue().get(pageIdRedisKey);
        if (!Strings.isNullOrEmpty(pageConfigString)) {
            return JSONObject.parseObject(pageConfigString, PageConfig.class);
        }
        LandingPageConfigDto landingPageConfigDto = this.getLandingPageConfigDtoByPageId(pageId);
        if (landingPageConfigDto == null) {
            stringRedisTemplate.opsForValue().set(pageIdRedisKey, JSON.toJSONString(PageConfig.nullPageConfig()),
                    LANDING_PAGE_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);
            return PageConfig.nullPageConfig();
        }
        PageConfig pageConfig = this.buildPageConfig(landingPageConfigDto);
        stringRedisTemplate.opsForValue().set(pageIdRedisKey, JSON.toJSONString(pageConfig),
                LANDING_PAGE_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);
        return pageConfig;
    }


    public Integer getAccountIdByPageId(Long pageId) {

        Assert.notNull(pageId, "落地页页面ID不可为空");

        String accountIdString = stringRedisTemplate.opsForValue().get(PAGE_ACCOUNT_SUFFIX_IN_REDIS + pageId);

        if (!Strings.isNullOrEmpty(accountIdString)) {
            return JSONObject.parseObject(accountIdString, Integer.class);
        }

        MgkLandingPagePo landingPagePo = this.getByPageId(pageId);
        if (landingPagePo == null) {
            this.savePageId2AccountIdInRedis(pageId, 0);
            return 0;
        } else {
            this.savePageId2AccountIdInRedis(pageId, landingPagePo.getAccountId());
            return landingPagePo.getAccountId();
        }

    }

    void savePageId2AccountIdInRedis(Long pageId, Integer accountId) {
        stringRedisTemplate.opsForValue().set(PAGE_ACCOUNT_SUFFIX_IN_REDIS + pageId, String.valueOf(accountId), PAGE_ACCOUNT_TIMEOUT, TimeUnit.MINUTES);
    }

    private LandingPageConfigDto convertLandingPageConfigPoToDto(LandingPageTransformDto transformDto) {
        LandingPageConfigDto landingPageConfigDto = LandingPageConfigDto.builder().build();
        if (Objects.isNull(transformDto)) {
            return landingPageConfigDto;
        }
        MgkLandingPagePo landingPagePo = transformDto.getLandingPagePo();
        String privacyName = transformDto.getPrivacyName();
        List<Integer> miniGameIds = transformDto.getMiniGameIds();
        List<Integer> wechatPackageIds = transformDto.getWechatPackageIds();
        MgkPageDownloadComponentHeightDto downloadComponentHeightDto = transformDto.getDownloadComponentHeightDto();

        BeanUtils.copyProperties(landingPagePo, landingPageConfigDto);
        landingPageConfigDto.setConfig(transformDto.getConfig());
        landingPageConfigDto.setBizIds(transformDto.getLandingPageBizIds());
        landingPageConfigDto.setAvIds(transformDto.getLandingPageAvIds());
        landingPageConfigDto.setShowUrls(transformDto.getShowUrls());
        landingPageConfigDto.setAppPackageIds(transformDto.getAppPackageIds());
        landingPageConfigDto.setFormIds(transformDto.getFormIds());
        if (LandingPageTypeEnum.APPLETS.getCode().equals(landingPagePo.getType())) {
            landingPageConfigDto.setMobileLaunchUrl(landingPageUrlProc.getMobileLaunchUrl(landingPagePo.getPageId(),
                    landingPagePo.getHeader()));
            landingPageConfigDto.setPcLaunchUrl(landingPageUrlProc.getPcLaunchUrl(landingPagePo.getPageId(), landingPagePo.getHeader()));
        }else if (LandingPageTypeEnum.BUSINESS_TOOL.getCode().equals(landingPagePo.getType())) {
            landingPageConfigDto.setPcLaunchUrl(landingPageUrlProc.getPcLaunchUrl(landingPagePo.getPageId(), landingPagePo.getHeader()));
        }else if (LandingPageTypeEnum.CONSULT_PAGE.getCode().equals(landingPagePo.getType())) {
            landingPageConfigDto.setPcLaunchUrl(landingPageUrlProc.getPcLaunchUrl(landingPagePo.getPageId(), landingPagePo.getHeader()));
        }
        String privacyUrl = landingPageUrlProc.getPrivacyUrlByEnvironment(privacyName);
        landingPageConfigDto.setPrivacyName(privacyName);
        landingPageConfigDto.setPrivacyUrl(privacyUrl);
        landingPageConfigDto.setMiniGameIds(miniGameIds);
        if (!CollectionUtils.isEmpty(miniGameIds)) {
            List<LauMiniGameDto> result = soaLauMiniGameService.queryLauMiniGames(QueryLauMiniGameDto.builder().ids(miniGameIds).build());
            landingPageConfigDto.setMiniGameCacheBos(result.stream().map(r -> LauMiniGameCacheBo.builder()
                    .id(r.getId())
                    .name(r.getName())
                    .game_url(r.getGameUrl())
                    .origin_id(r.getOriginId())
                    .build()).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(wechatPackageIds)) {
            landingPageConfigDto.setWechatPackageId(wechatPackageIds.get(0));
        }
        landingPageConfigDto.setWechatIsManual(mgkWechatManual);
        landingPageConfigDto.setTotalBlockSize(downloadComponentHeightDto.getTotalBlockSize());
        landingPageConfigDto.setTotalDownloadComponentSize(downloadComponentHeightDto.getTotalDownloadComponentSize());
        landingPageConfigDto.setMaxDownloadComponentSize(downloadComponentHeightDto.getMaxDownloadComponentSize());
        landingPageConfigDto.setTotalFirstScreenDownloadComponentSize(downloadComponentHeightDto.getTotalFirstScreenDownloadComponentSize());
        landingPageConfigDto.setIsDownloadOverLimit(mgkLandingPageConfigJudgeUtil.judgeIsDownloadOverLimit(downloadComponentHeightDto, landingPagePo.getAccountId()));
        landingPageConfigDto.setCustomerAcquisitionLinkIds(transformDto.getCustomerAcquisitionLinkIds());
        landingPageConfigDto.setGames(transformDto.getGames());
        return landingPageConfigDto;
    }

    public List<Long> getPublishedPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }
        MgkLandingPagePoExample exm = new MgkLandingPagePoExample();
        exm.or().andPageIdIn(pageIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsModelEqualTo(IsModelEnum.PAGE.getCode())
                .andStatusEqualTo(LandingPageStatusEnum.PUBLISHED.getCode());
        List<MgkLandingPagePo> pos = mgkLandingPageDao.selectByExample(exm);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream()
                .map(MgkLandingPagePo::getPageId)
                .collect(Collectors.toList());
    }

    public List<Long> getLaunchAblePageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }
        MgkLandingPagePoExample exm = new MgkLandingPagePoExample();
        exm.or().andPageIdIn(pageIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsModelEqualTo(IsModelEnum.PAGE.getCode())
                .andStatusIn(LandingPageStatusEnum.LAUNCH_AVAILABLE_STATUS_LIST);
        List<MgkLandingPagePo> pos = mgkLandingPageDao.selectByExample(exm);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream()
                .map(MgkLandingPagePo::getPageId)
                .collect(Collectors.toList());
    }

    public List<MgkLandingPageDto> getLandingPageDtosInPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }
        return this.getLandingPageDtos(QueryLandingPageParamDto.builder().pageIdList(pageIds).build());
    }


    public LandingPageConfigDto fillConsultPageAttrs(LandingPageConfigDto landingPageConfigDto){

        List<MgkPageFormMappingPo> mgkPageFormMappingPos = getPageFormMappingPoByPageId(landingPageConfigDto.getPageId());
        List<Long> formIds = mgkPageFormMappingPos.stream()
                .map(MgkPageFormMappingPo::getFormId) // Assuming getFormId returns a String
                .collect(Collectors.toList());
        landingPageConfigDto.setFormIds(formIds);

        MgkConsultLandingPageExample example = new MgkConsultLandingPageExample();
        example.or().andPageIdIn(Collections.singletonList(landingPageConfigDto.getPageId()));
        List<MgkConsultLandingPagePo> pos = mgkConsultLandingPageDao.selectByExample(example);
        if(pos.isEmpty()){
            landingPageConfigDto.setIsConsultPage(false);
            return landingPageConfigDto;
        }
        if(pos.size() > 1){
            LOGGER.warn(String.format("[fillConsultPageAttrs] selectByExample ,over 1 MgkConsultLandingPage find,pageId={}",landingPageConfigDto.getPageId()));
        }
        MgkConsultLandingPagePo po = pos.get(0);
        landingPageConfigDto.setIsConsultPage(true);
        landingPageConfigDto.setConsultLandingPageDto(po2dto(po));
        return landingPageConfigDto;
    }

    public List<ConsultLandingPageDto> getConsultPageInfoByPageId(List<Long> pageIds){
        if (pageIds.isEmpty()){
            return Collections.emptyList();
        }
        MgkConsultLandingPageExample example = new MgkConsultLandingPageExample();
        example.or().andPageIdIn(pageIds);
        List<MgkConsultLandingPagePo> pos = mgkConsultLandingPageDao.selectByExample(example);
        if(pos.isEmpty()){
            return Collections.emptyList();
        }
        return pos.stream()
                .map(this::po2dto)
                .collect(Collectors.toList());
    }

    public PageResult<MgkLandingPageDto> fillConsultPageAttrs(PageResult<MgkLandingPageDto> result){
        List<MgkLandingPageDto> res = result.getRecords();
        res = fillConsultPageAttrs(res);
        result.setRecords(res);
        return result;
    }


    public List<MgkLandingPageDto> fillConsultPageAttrs(List<MgkLandingPageDto> result){
        List<Long> pageIds = result.stream()
                .map(MgkLandingPageDto::getPageId)
                .collect(Collectors.toList());
        if (pageIds.isEmpty()){
            return result;
        }
        Map<Long, MgkLandingPageDto> dtoMap = result.stream()
                .collect(Collectors.toMap(MgkLandingPageDto::getPageId, Function.identity()));

        Map<Long, List<MgkPageFormMappingPo>> mappingPos = getPageFormMappingPoMapByPageIds(pageIds);

        dtoMap.forEach((pageId, dto) -> {
            List<MgkPageFormMappingPo> mappings = mappingPos.get(pageId);
            if (mappings != null) {

                List<String> formIds = mappings.stream()
                        .map(po -> po.getFormId().toString())
                        .collect(Collectors.toList());
                dto.setFormIds(formIds);
            }
        });

        MgkConsultLandingPageExample example = new MgkConsultLandingPageExample();
        example.or().andPageIdIn(pageIds);
        List<MgkConsultLandingPagePo> pos = mgkConsultLandingPageDao.selectByExample(example);
        if(pos.isEmpty()){
            return result;
        }

        for (MgkConsultLandingPagePo po : pos) {
            MgkLandingPageDto dto = dtoMap.get(po.getPageId());
            if (dto != null) {
                dto.setIsConsultPage(true);
                dto.setConsultLandingPageDto(po2dto(po));
            }
        }
        return result;
    }


    public void insertCustomerServiceAuthTokenPage(ConsultAuthDto consultAuthDto , ConsultPageCustomerServiceEnum pageCustomerService){
        MgkCustomerServiceAuthTokenPo po = new MgkCustomerServiceAuthTokenPo();
        po.setAccountId(Integer.valueOf(consultAuthDto.getUcid()));
        po.setCustomerServiceType(pageCustomerService.getCode());
        po.setAuthToken(consultAuthDto.getAuthToken());
        po.setUserId(pageCustomerService.getThirdPartCode());
        po.setUserName(pageCustomerService.getName());
        int res = mgkCustomerServiceAuthTokenDao.insertSelective(po);
        Assert.isTrue(res == 1, "保存失败，请稍后重试");
    }


    public List<MgkCustomerServiceAuthTokenPo> getAuthPoByAccountId(Integer accountId) {
        MgkCustomerServiceAuthTokenExample example = new MgkCustomerServiceAuthTokenExample();
        example.or().andAccountIdEqualTo(accountId);
        List<MgkCustomerServiceAuthTokenPo> pos = mgkCustomerServiceAuthTokenDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos;
    }

    private void insertConsultPage( long pageId, ConsultLandingPageDto consultLandingPageDto){
        MgkConsultLandingPagePo po = new MgkConsultLandingPagePo();
        BeanUtils.copyProperties(consultLandingPageDto, po);
        po.setPageId(pageId);
        int res = mgkConsultLandingPageDao.insertSelective(po);
        Assert.isTrue(res == 1, "保存失败，请稍后重试");
    }

    private void updateConsultPage( long pageId, ConsultLandingPageDto consultLandingPageDto){
        MgkConsultLandingPageExample example = new MgkConsultLandingPageExample();
        example.or().andPageIdEqualTo(pageId);
        List<MgkConsultLandingPagePo> pos = mgkConsultLandingPageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos) || pos.size() > 1) {
            LOGGER.warn(String.format("updateConsultPage fail ,no MgkConsultLandingPage find,pageId={}",pageId));
            return ;
        }
        MgkConsultLandingPagePo po = pos.get(0);
        BeanUtils.copyProperties(consultLandingPageDto, po);
        int res = mgkConsultLandingPageDao.updateByPrimaryKeySelective(po);
        Assert.isTrue(res == 1, "保存失败，请稍后重试");
    }

    private ConsultLandingPageDto po2dto(MgkConsultLandingPagePo po){
        ConsultLandingPageDto dto = new ConsultLandingPageDto();
        dto.setPageId(po.getPageId());
        dto.setFormId(po.getFormId());
        dto.setHasWelcomeWords(po.getHasWelcomeWords());
        dto.setWelcomeWords(po.getWelcomeWords());
        dto.setIsLeaveData(po.getIsLeaveData());
        dto.setHasConsultFaq(po.getHasConsultFaq());
        dto.setConsultFaq(po.getConsultFaq());
        dto.setCustomerServiceType(po.getCustomerServiceType());
        dto.setProfile(po.getProfile());
        dto.setConnectType(po.getConnectType());
        return dto;
    }

    public Map<Long, Integer> getTemplateStyleMapByPageId(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyMap();
        }

        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andPageIdIn(pageIds).andIsModelEqualTo(IsModelEnum.MODEL.getCode());
        List<MgkLandingPagePo> mgkLandingPagePos = mgkLandingPageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(mgkLandingPagePos)) {
            return Collections.emptyMap();
        }
        return mgkLandingPagePos.stream().collect(Collectors.toMap(MgkLandingPagePo::getPageId, MgkLandingPagePo::getTemplateStyle));
    }

    public Map<Long, MgkLandingPageDto> getLandingPageMapInPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyMap();
        }

        List<MgkLandingPageDto> dtos = getLandingPageDtosInPageIds(pageIds);
        return dtos.stream().collect(Collectors.toMap(MgkLandingPageDto::getPageId, Function.identity()));
    }

    public List<MgkLandingPageDto> getLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto) {
        Assert.notNull(queryLandingPageParamDto, "落地页查询参数不可为空");
        MgkLandingPagePoExample example = this.getMgkLandingPagePoExample(queryLandingPageParamDto);
        return this.getLandingPageDtosByExample(example);
    }

    public List<MgkLandingPageStatusBaseDto> getLandingPageStatusBaseDtoList(QueryLandingPageParamDto queryLandingPageParamDto) {
        Assert.notNull(queryLandingPageParamDto, "落地页查询参数不可为空");
        MgkLandingPagePoExample example = this.getMgkLandingPagePoExample(queryLandingPageParamDto);
        List<MgkLandingPagePo> pos = mgkLandingPageDao.selectByExample(example);
        return pos.stream()
                .map(po -> {
                    return MgkLandingPageStatusBaseDto.builder()
                            .pageId(po.getPageId())
                            .status(po.getStatus())
                            .build();
                }).collect(Collectors.toList());
    }

    /**
     * 有soa调用 暂时不改动
     */
    public PageResult<MgkLandingPageDto> getLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto, Page page) {
        Assert.notNull(queryLandingPageParamDto, "落地页查询参数不可为空");
        Assert.notNull(page, "分页参数不可为空");

        MgkLandingPagePoExample example = this.getLandingPageExample(queryLandingPageParamDto, page);
        long total = mgkLandingPageDao.countByExample(example);
        if (total == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        List<MgkLandingPageDto> landingPageDtos = this.getLandingPageDtosByExample(example);
        queryPostHandle(landingPageDtos, queryLandingPageParamDto);

//        //如果是旧版落地页则从ES里面取数据 已推全  ()
//        if (!MyMgkPageDataVersionEnum.NEW.getCode().equals(queryLandingPageParamDto.getPageVersion())) {
////            queryPvCtrFromESAndSetting(landingPageDtos);
//            Cat.logEvent("pageVersion", "old");
//        }

        return PageResult.<MgkLandingPageDto>builder().total((int) total).records(landingPageDtos).build();
    }

    public PageResult<MgkLandingPageDto> getPreviewLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto, Page page) {
        Assert.notNull(queryLandingPageParamDto, "落地页查询参数不可为空");
        Assert.notNull(page, "分页参数不可为空");
        //like 匹配都使用es查询
        if (!Strings.isNullOrEmpty(queryLandingPageParamDto.getNameLike())
                || !Strings.isNullOrEmpty(queryLandingPageParamDto.getTitleLike())
                || !Strings.isNullOrEmpty(queryLandingPageParamDto.getCreatorLike())
        ) {
            return getPreviewLandingPageDtosFormEs(queryLandingPageParamDto, page);
        }

        MgkLandingPagePoExample example = this.getLandingPageExample(queryLandingPageParamDto, page);
        long total = mgkLandingPageDao.countByExample(example);
        if (total == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        List<MgkLandingPageDto> landingPageDtos = this.getLandingPageDtosByExample(example);
        if (CollectionUtils.isEmpty(landingPageDtos)) {
            return PageResult.<MgkLandingPageDto>builder().total((int) total).records(landingPageDtos).build();
        }

        landingPageDtos = this.handleShadowPageList(landingPageDtos);

        queryPostHandle(landingPageDtos, queryLandingPageParamDto);

        //如果是旧版落地页则从ES里面取数据  已推全
        if (!MyMgkPageDataVersionEnum.NEW.getCode().equals(queryLandingPageParamDto.getPageVersion())) {
//            queryPvCtrFromESAndSetting(landingPageDtos);
            Cat.logEvent("pageVersion", "old");
        }

        return PageResult.<MgkLandingPageDto>builder().total((int) total).records(landingPageDtos).build();
    }

    /**
     * @See getPreviewLandingPageDtos 方法的es查询版本
     * @param queryLandingPageParamDto
     * @param page
     * @return
     */
    public PageResult<MgkLandingPageDto> getPreviewLandingPageDtosFormEs(QueryLandingPageParamDto queryLandingPageParamDto, Page page) {
        Assert.notNull(queryLandingPageParamDto, "落地页查询参数不可为空");
        Assert.notNull(page, "分页参数不可为空");

        PageInfo<MgkLandingPagePo> pageInfo = esSearchService.getEsList(buildEsQueryParam(queryLandingPageParamDto, page), esSearchConfig.getMgkLandingPageEsIndex(), MgkLandingPagePo.class, esSearchConfig.getMgkLandingPageEsSearchToken());
        List<MgkLandingPageDto> landingPageDtos = this.convertLandingPagePosToDtos(pageInfo.getList());

        landingPageDtos = this.handleShadowPageList(landingPageDtos);
        queryPostHandle(landingPageDtos, queryLandingPageParamDto);

        //如果是旧版落地页则从ES里面取数据 已推全
        if (!MyMgkPageDataVersionEnum.NEW.getCode().equals(queryLandingPageParamDto.getPageVersion())) {
//            queryPvCtrFromESAndSetting(landingPageDtos);
            Cat.logEvent("pageVersion", "old");
        }

        return PageResult.<MgkLandingPageDto>builder().total((int) pageInfo.getTotal()).records(landingPageDtos).build();
    }

    private SearchSourceBuilder buildEsQueryParam(QueryLandingPageParamDto param, Page page) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = boolQuery();

        if (!CollectionUtils.isEmpty(param.getIsModelList())) {
            boolQueryBuilder.must(termsQuery("is_model", param.getIsModelList()));
        } else {
            boolQueryBuilder.must(termsQuery("is_model", Lists.newArrayList(IsModelEnum.PAGE.getCode())));
        }

        ObjectUtils.setList(param::getIdList, (value) -> boolQueryBuilder.must(termsQuery("_id", value)));
        ObjectUtils.setList(param::getAccountIdList, (value) -> boolQueryBuilder.must(termsQuery("account_id", value)));
        ObjectUtils.setList(param::getTemplateStyleList, (value) -> boolQueryBuilder.must(termsQuery("template_style", value)));
        ObjectUtils.setList(param::getStatusList, (value) -> boolQueryBuilder.must(termsQuery("status", value)));
        ObjectUtils.setList(param::getTypeList, (value) -> boolQueryBuilder.must(termsQuery("type", value)));
        ObjectUtils.setList(param::getFormIdList, (value) -> boolQueryBuilder.must(termsQuery("form_id", value)));
        ObjectUtils.setList(param::getModelIdList, (value) -> boolQueryBuilder.must(termsQuery("model_id", value)));

        ObjectUtils.setObject(param::getEffectiveStartTime, (value) -> boolQueryBuilder.must(rangeQuery("effective_start_time").from(value)));
        ObjectUtils.setObject(param::getEffectiveEndTime, (value) -> boolQueryBuilder.must(rangeQuery("effective_end_time").to(value)));
        ObjectUtils.setObject(param::getHasDpaGoods, (value) -> boolQueryBuilder.must(termQuery("has_dpa_goods", value)));

        ObjectUtils.setList(param::getPageIdList, (value) -> boolQueryBuilder.must(termsQuery("page_id", value)));
        ObjectUtils.setList(param::getExcludePageIdList, (value) -> boolQueryBuilder.mustNot(termsQuery("page_id", value)));
        ObjectUtils.setList(param::getIsVideoPages, (value) -> boolQueryBuilder.must(termsQuery("is_video_page", value)));
        ObjectUtils.setList(param::getAvids, (value) -> boolQueryBuilder.must(termsQuery("avid", value)));

        if (param.isFormIdLtZero()) {
            boolQueryBuilder.must(rangeQuery("form_id").gt(0));
        }

        if (!Strings.isNullOrEmpty(param.getNameLike())) {
            boolQueryBuilder.must(new MatchPhraseQueryBuilder("name", param.getNameLike()));
        }

        if (!Strings.isNullOrEmpty(param.getTitleLike())) {
            boolQueryBuilder.must(matchPhraseQuery("title", param.getTitleLike()));
        }

        if (!Strings.isNullOrEmpty(param.getCreatorLike())) {
            boolQueryBuilder.must(matchPhraseQuery("creator", param.getCreatorLike()));
        }

        ObjectUtils.setObject(param::getLimit, sourceBuilder::size);
        sourceBuilder.sort("mtime", SortOrder.DESC);

        sourceBuilder.from(page.getOffset()).size(page.getLimit());
        sourceBuilder.query(boolQueryBuilder);
        return sourceBuilder;
    }

    public List<MgkLandingPageDto> getLaunchLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto) {
        Assert.notNull(queryLandingPageParamDto, "落地页查询参数不可为空");
        MgkLandingPagePoExample example = this.getMgkLandingPagePoExample(queryLandingPageParamDto);
        List<MgkLandingPageDto> landingPageDtos = this.getLandingPageDtosByExample(example);

        landingPageDtos = this.handleShadowPageList(landingPageDtos);

        queryPostHandle(landingPageDtos, queryLandingPageParamDto);
        if (CollectionUtils.isEmpty(landingPageDtos)) {
            return Collections.emptyList();
        }

        handleAuditRejectReasonList(landingPageDtos);

        return landingPageDtos;
    }

    public PageResult<MgkLandingPageDto> getLaunchLandingPageDtoByPage(QueryLandingPageParamDto queryLandingPageParamDto, Page page) {
        Assert.notNull(queryLandingPageParamDto, "落地页查询参数不可为空");
        MgkLandingPagePoExample example = this.getLandingPageExample(queryLandingPageParamDto, page);
        long total = mgkLandingPageDao.countByExample(example);
        if (total == 0L) {
            return PageResult.emptyPageResult();
        }
        List<MgkLandingPageDto> landingPageDtos = this.getLandingPageDtosByExample(example);

        landingPageDtos = this.handleShadowPageList(landingPageDtos);

        queryPostHandle(landingPageDtos, queryLandingPageParamDto);
        if (CollectionUtils.isEmpty(landingPageDtos)) {
            return PageResult.<MgkLandingPageDto>builder()
                    .total((int) total)
                    .records(Collections.emptyList())
                    .build();
        }

        handleAuditRejectReasonList(landingPageDtos);

        return PageResult.<MgkLandingPageDto>builder()
                .total((int) total)
                .records(landingPageDtos)
                .build();
    }

    // 尽可能替换影子落地页
    private List<MgkLandingPageDto> handleShadowPageList(List<MgkLandingPageDto> landingPageDtos) {
        if (CollectionUtils.isEmpty(landingPageDtos)) {
            return Collections.emptyList();
        }
        List<Long> originPageIds = landingPageDtos.stream()
                .map(MgkLandingPageDto::getPageId)
                .collect(Collectors.toList());
        // 替换为影子落地页内容 但是要保留原来的pageId
        Map<Long, Long> shadowPageIdMap = mgkShadowLandingPageService.getShadowPageIdMap(originPageIds);
        if (CollectionUtils.isEmpty(shadowPageIdMap)) {
            return landingPageDtos;
        }
        List<Long> shadowPageIds = new ArrayList<>(shadowPageIdMap.values());
        MgkLandingPagePoExample shadowExm = new MgkLandingPagePoExample();
        shadowExm.or()
                .andPageIdIn(shadowPageIds);
        List<MgkLandingPageDto> shadowPageDtos = this.getLandingPageDtosByExample(shadowExm);
        if (CollectionUtils.isEmpty(shadowPageDtos)) {
            return landingPageDtos;
        }
        Map<Long, MgkLandingPageDto> shadowPageDtoMap = shadowPageDtos.stream()
                .collect(Collectors.toMap(MgkLandingPageDto::getPageId, Function.identity()));
        return landingPageDtos.stream().map(landingPageDto -> {
            Long originPageId = landingPageDto.getPageId();
            Long shadowPageId = shadowPageIdMap.get(originPageId);
            MgkLandingPageDto shadowPageDto = shadowPageDtoMap.get(shadowPageId);
            if (!Utils.isPositive(shadowPageId) || Objects.isNull(shadowPageDto)) {
                landingPageDto.setShadowStatus(LandingPageStatusEnum.WAIT_AUDIT.getCode());
                return landingPageDto;
            }
            shadowPageDto.setPageId(originPageId);
            if (LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageDto.getStatus())) {
                shadowPageDto.setShadowStatus(shadowPageDto.getStatus());
                shadowPageDto.setStatus(landingPageDto.getStatus());
                return shadowPageDto;
            }
            // 非发布状态下 原始落地页才是最新版本
            landingPageDto.setShadowStatus(shadowPageDto.getStatus());
            return landingPageDto;
        }).collect(Collectors.toList());
    }

    private void handleAuditRejectReasonList(List<MgkLandingPageDto> landingPageDtos) {
        if (CollectionUtils.isEmpty(landingPageDtos)) {
            return;
        }

        List<Long> pageIds = landingPageDtos.stream()
                .map(MgkLandingPageDto::getPageId)
                .collect(Collectors.toList());

        Map<Long, String> detailReasonMap = mgkAuditPageService.queryMgkAuditDetailReasonMap(pageIds);
        if (CollectionUtils.isEmpty(detailReasonMap)) {
            return;
        }

        landingPageDtos.forEach(landingPageDto -> {
            String detailReason = detailReasonMap.get(landingPageDto.getPageId());
            if (!StringUtils.isEmpty(detailReason)) {
                landingPageDto.setReason(detailReason);
            }
        });
    }

    private void queryPostHandle(List<MgkLandingPageDto> landingPageDtos, QueryLandingPageParamDto queryDto) {
        if (CollectionUtils.isEmpty(landingPageDtos)) {
            return;
        }

        if (queryDto.isWithAvid()) {
            List<Long> pageIds = landingPageDtos.stream().map(MgkLandingPageDto::getPageId).collect(Collectors.toList());
            Map<Long, List<Long>> page2AvidsMap = this.getMgkLandingPage2AvidsMapInPageIds(pageIds);
            landingPageDtos = landingPageDtos.stream().map(dto -> {
                dto.setAvIds(page2AvidsMap.getOrDefault(dto.getPageId(), Collections.emptyList()));
                return dto;
            }).collect(Collectors.toList());
        }

        // 查询模板
        List<MgkModelDto> modelDtos = modelServiceDelegate.getMgkModelDtosByIds(landingPageDtos.stream()
                .map(MgkLandingPageDto::getModelId).collect(Collectors.toList()));
        Map<Long, String> modelMap = modelDtos.stream().collect(Collectors.toMap(MgkModelDto::getModelId,
                MgkModelDto::getModelName));
        landingPageDtos.forEach(dto -> {
            dto.setModelName(modelMap.getOrDefault(dto.getModelId(), "自定义"));
        });

        // 设置小程序地址
        landingPageDtos.forEach(dto -> {
            if (LandingPageTypeEnum.APPLETS.getCode().equals(dto.getType())) {
                dto.setMobileLaunchUrl(landingPageUrlProc.getMobileLaunchUrl(dto.getPageId(), dto.getHeader()));
                dto.setPcLaunchUrl(landingPageUrlProc.getPcLaunchUrl(dto.getPageId(), dto.getHeader()));
            }else if (LandingPageTypeEnum.BUSINESS_TOOL.getCode().equals(dto.getType())) {
                dto.setPcLaunchUrl(landingPageUrlProc.getPcLaunchUrl(dto.getPageId(), dto.getHeader()));
            }else if (LandingPageTypeEnum.CONSULT_PAGE.getCode().equals(dto.getType())) {
                dto.setPcLaunchUrl(landingPageUrlProc.getPcLaunchUrl(dto.getPageId(), dto.getHeader()));
            }
        });

        // 设置accessUrl地址 之前是H5，由Web层直接拼接，后面增加小程序，需要返回小程序的降级地址
        landingPageDtos.forEach(dto -> {
            if (LandingPageTypeEnum.APPLETS.getCode().equals(dto.getType())
                    || LandingPageTypeEnum.BUSINESS_TOOL.getCode().equals(dto.getType())) {
                dto.setAccessUrl(landingPageUrlProc.getLaunchUrl(WhetherEnum.YES.getCode(), dto, false));
            } else if (LandingPageTypeEnum.H5.getCode().equals(dto.getType())) {
                dto.setAccessUrl(String.format(LANDING_PAGE_URL_PREFIXX, "https", dto.getPageId()).concat("/"));
            }else if (LandingPageTypeEnum.CONSULT_PAGE.getCode().equals(dto.getType())) {
                dto.setAccessUrl(landingPageUrlProc.getMobileConsultLaunchUrl(dto.getPageId(), dto.getHeader()));
            }
        });
    }

    private Map<Long, List<Long>> getMgkLandingPage2AvidsMapInPageIds(List<Long> pageIds) {
        List<MgkLandingPageAvidPo> pos = this.getMgkLandingPageAvidPosInPageIds(pageIds);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos.stream().collect(Collectors.groupingBy(MgkLandingPageAvidPo::getPageId, Collectors.mapping(MgkLandingPageAvidPo::getAvId, Collectors.toList())));
    }

    private List<MgkLandingPageAvidPo> getMgkLandingPageAvidPosInPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }
        MgkLandingPageAvidPoExample pageAvidPoExample = new MgkLandingPageAvidPoExample();
        pageAvidPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(pageIds);
        return mgkLandingPageAvidDao.selectByExample(pageAvidPoExample);
    }

    private List<MgkLandingPageDto> getLandingPageDtosByExample(MgkLandingPagePoExample example) {
        List<MgkLandingPagePo> pos = mgkLandingPageDao.selectByExample(example);
        return this.convertLandingPagePosToDtos(pos);
    }

    private MgkLandingPagePoExample getLandingPageExample(QueryLandingPageParamDto param, Page page) {

        MgkLandingPagePoExample example = this.getMgkLandingPagePoExample(param);

        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());

        return example;
    }

    private MgkLandingPagePoExample getMgkLandingPagePoExample(QueryLandingPageParamDto param) {
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();

        MgkLandingPagePoExample.Criteria c = example.or();

        if (!CollectionUtils.isEmpty(param.getIsModelList())) {
            c.andIsModelIn(param.getIsModelList());
        } else {
            c.andIsModelEqualTo(IsModelEnum.PAGE.getCode());
        }

        ObjectUtils.setList(param::getIdList, c::andIdIn);
        ObjectUtils.setList(param::getAccountIdList, c::andAccountIdIn);
        ObjectUtils.setList(param::getTemplateStyleList, c::andTemplateStyleIn);
        ObjectUtils.setList(param::getStatusList, c::andStatusIn);
        ObjectUtils.setList(param::getTypeList, c::andTypeIn);
        ObjectUtils.setList(param::getFormIdList, c::andFormIdIn);
        ObjectUtils.setList(param::getModelIdList, c::andModelIdIn);

        ObjectUtils.setObject(param::getEffectiveStartTime, c::andEffectiveEndTimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(param::getEffectiveEndTime, c::andEffectiveEndTimeLessThanOrEqualTo);
        ObjectUtils.setObject(param::getHasDpaGoods, c::andHasDpaGoodsEqualTo);

        ObjectUtils.setList(param::getPageIdList, c::andPageIdIn);
        ObjectUtils.setList(param::getExcludePageIdList, c::andPageIdNotIn);
        ObjectUtils.setList(param::getIsVideoPages, c::andIsVideoPageIn);
        ObjectUtils.setList(param::getAvids, c::andAvidIn);

        if (param.isFormIdLtZero()) {
            c.andFormIdGreaterThan(0L);
        }

        if (!Strings.isNullOrEmpty(param.getTitleLike())) {
            c.andTitleLike("%" + param.getTitleLike() + "%");
        }

        if (!Strings.isNullOrEmpty(param.getNameLike())) {
            c.andNameLike("%" + param.getNameLike() + "%");
        }

        if (!Strings.isNullOrEmpty(param.getCreatorLike())) {
            c.andCreatorLike("%" + param.getCreatorLike() + "%");
        }

        ObjectUtils.setObject(param::getOrderBy, example::setOrderByClause);
        ObjectUtils.setObject(param::getLimit, example::setLimit);
        return example;
    }

    private void validateToStatus(LandingPageStatusEnum actualStatus, LandingPageStatusEnum operatedStatus) {
        Assert.isTrue(actualStatus.validateToStatus(operatedStatus), "状态为" + actualStatus.getDesc() + "的落地页不可转换成" + operatedStatus.getDesc());
    }

    public MgkLandingPagePo getByPageId(long pageId) {
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId);

        List<MgkLandingPagePo> pos = mgkLandingPageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }

        return pos.get(0);
    }

    private List<MgkLandingPagePo> getPublishedMgkPagePos(List<Long> mgkPageIds, Integer isModel, Integer templateStyle) {
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        MgkLandingPagePoExample.Criteria or = example.or();
        or.andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(LandingPageStatusEnum.PUBLISHED.getCode())
                .andPageIdIn(mgkPageIds);
        if (Utils.isPositive(isModel)) {
            or.andIsModelEqualTo(isModel);
        }
        if (Utils.isPositive(templateStyle)) {
            or.andTemplateStyleEqualTo(templateStyle);
        }
        return mgkLandingPageDao.selectByExample(example);
    }

    public List<MgkLandingPagePo> getInPageIds(List<Long> pageIds) {
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(pageIds);

        return mgkLandingPageDao.selectByExample(example);
    }

    private MgkLandingPageConfigPo getConfigByPageId(long pageId) {
        MgkLandingPageConfigPoExample example = new MgkLandingPageConfigPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId);

        List<MgkLandingPageConfigPo> pos = mgkLandingPageConfigDao.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }

        return pos.get(0);
    }

    private List<MgkLandingPageAvidPo> getAvIdsByPageId(long pageId) {
        MgkLandingPageAvidPoExample example = new MgkLandingPageAvidPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId);

        List<MgkLandingPageAvidPo> pos = mgkLandingPageAvidDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos;
    }

    private List<String> getShowUrlsByPageId(Long pageId) {
        MgkLandingPageShowUrlPoExample example = new MgkLandingPageShowUrlPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId);
        List<MgkLandingPageShowUrlPo> pos = mgkLandingPageShowUrlDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(MgkLandingPageShowUrlPo::getShowUrl).collect(Collectors.toList());
    }

    private void insertLandingPage(Operator operator, long pageId, NewLandingPageDto newLandingPageDto) {
        MgkLandingPagePo landingPagePo = new MgkLandingPagePo();
        BeanUtils.copyProperties(newLandingPageDto, landingPagePo);
        landingPagePo.setPageId(pageId);
        landingPagePo.setCreator(operator.getOperatorName());
        //评论区暗投落地页默认是发布状态
        if (IsModelEnum.COMMENT.getCode().equals(newLandingPageDto.getIsModel())) {
            landingPagePo.setStatus(LandingPageStatusEnum.PUBLISHED.getCode());
        } else {
            landingPagePo.setStatus(LandingPageStatusEnum.UNPUBLISHED.getCode());
        }
        landingPagePo.setAvid(CollectionUtils.isEmpty(newLandingPageDto.getAvIds()) ? null : newLandingPageDto.getAvIds().get(0));
        landingPagePo.setVideoDuration(getPageVideoDuration(newLandingPageDto.getAvIds(), newLandingPageDto.getBizIds()));

        if (!Utils.isPositive(newLandingPageDto.getIsVideoPage())) {
            landingPagePo.setIsVideoPage((Utils.isPositive(newLandingPageDto.getBizId())
                    || !CollectionUtils.isEmpty(newLandingPageDto.getBizIds())
                    || !CollectionUtils.isEmpty(newLandingPageDto.getAvIds())) ? WhetherEnum.YES.getCode() : WhetherEnum.NO.getCode());
        }

        int res = mgkLandingPageDao.insertSelective(landingPagePo);
        Assert.isTrue(res == 1, "保存失败，请稍后重试");

        // insert mgk_consult_landing_page
        if (Boolean.TRUE.equals(newLandingPageDto.getIsConsultPage())) {
            insertConsultPage(pageId , newLandingPageDto.getConsultLandingPageDto());
        }

        // insert mgk_page_form_mapping
        if (!CollectionUtils.isEmpty(newLandingPageDto.getFormIds())) {
            this.batchInsertPageFormMapping(pageId, newLandingPageDto.getFormIds());
        }

        // insert mgk_page_mini_game_mapping
        if (!CollectionUtils.isEmpty(newLandingPageDto.getMiniGameIds())) {
            mgkMiniGameMappingService.batchInsertMappingIds(pageId, newLandingPageDto.getMiniGameIds());
        }

        // insert mgk_page_wechat_package_mapping
        List<Integer> wechatPackageIds = Objects.isNull(newLandingPageDto.getWechatPackageId()) ? Collections.emptyList() : Lists.newArrayList(newLandingPageDto.getWechatPackageId());
        if (Utils.isPositive(newLandingPageDto.getWechatPackageId())) {
            mgkWechatPackageMappingService.batchInsertMappingIds(pageId, wechatPackageIds);
        }

        // insert mgk download component height info
        MgkPageDownloadComponentHeightDto insertHeightDto = MgkPageDownloadComponentHeightDto.builder()
                .pageId(pageId)
                .totalBlockSize(newLandingPageDto.getTotalBlockSize())
                .totalDownloadComponentSize(newLandingPageDto.getTotalDownloadComponentSize())
                .totalFirstScreenDownloadComponentSize(newLandingPageDto.getTotalFirstScreenDownloadComponentSize())
                .maxDownloadComponentSize(newLandingPageDto.getMaxDownloadComponentSize())
                .build();
        mgkPageDownloadComponentService.insertDownloadComponentHeightInfo(insertHeightDto);

        // insert mgk_page_biz_mapping
        if (!CollectionUtils.isEmpty(newLandingPageDto.getBizIds())) {
            this.batchSavePageBizMapping(newLandingPageDto, pageId);
            stringRedisTemplate.opsForValue().set(MGK_LANDING_PAGE_PAGE_ID_BIZ_ID_REDIS_KEY + pageId, newLandingPageDto.getBizIds().get(0).toString(), MGK_LANDING_PAGE_PAGE_ID_BIZ_ID_REDIS_EXPIRE, TimeUnit.HOURS);
        }

        MgkLandingPageConfigPo landingPageConfigPo = new MgkLandingPageConfigPo();
        landingPageConfigPo.setPageId(pageId);
        landingPageConfigPo.setConfig(newLandingPageDto.getConfig());

        int result = mgkLandingPageConfigDao.insertSelective(landingPageConfigPo);
        Assert.isTrue(result == 1, "保存失败，请稍后重试");


        // 插入mongoDb
        mgkMongoLandingPageService.saveLandingPage(MongoLandingPageDto.builder()
                .accountId(newLandingPageDto.getAccountId())
                .config(JSON.parse(newLandingPageDto.getConfig()))
                .pageId(pageId)
                .build());

        if (!CollectionUtils.isEmpty(newLandingPageDto.getShowUrls())) {
            this.batchInsetLandingPageShowUrls(pageId, newLandingPageDto.getShowUrls());
        }

        if (!CollectionUtils.isEmpty(newLandingPageDto.getAvIds())) {
            this.batchInsetLandingPageAvIds(pageId, newLandingPageDto.getAvIds());
            this.batchSaveLandingPageArcInfo(newLandingPageDto.getAvIds());
            mgkLandingPageAvidService.refreshPageIdToAvidMappingInRedis(pageId, newLandingPageDto.getAvIds().get(0));
        }

        //insert log
        LandingPageConfigLogBean logBean = LandingPageConfigLogBean.builder().build();
        BeanUtils.copyProperties(landingPagePo, logBean);
        logBean.setConfig(landingPageConfigPo.getConfig());
        if (!CollectionUtils.isEmpty(newLandingPageDto.getAvIds())) {
            logBean.setAvIds(newLandingPageDto.getAvIds());
        }

        // 创建时 只需要生成原始落地页的日志
        if (IsModelEnum.PAGE.getCode().equals(newLandingPageDto.getIsModel())) {
            mgkLogService.insertLog(NewLogOperationDto.builder()
                    .accountId(newLandingPageDto.getAccountId())
                    .objId(pageId)
                    .objFlag(LogObjFlagEnum.LANDING_PAGE.getCode())
                    .operateType(Utils.isPositive(newLandingPageDto.getOriginPageId()) ? LogOperateTypeEnum.LANDING_PAGE_COPY.getCode() : LogOperateTypeEnum.LANDING_PAGE_ADD.getCode())
                    .operatorUsername(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .oldValue("")
                    .newValue(logBean)
                    .build());


            MgkPageOperateLogDto logDto = MgkPageOperateLogDto.builder()
                    .pageId(pageId)
                    .operatorName(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .operatorId(operator.getOperatorId())
                    .operateType(MgkOperationType.CREATE.getCode())
                    .build();
            MgkPageOperateLogInfoDto newInfo = MgkPageOperateLogInfoDto.builder()
                    .appPackageIds(newLandingPageDto.getAppPackageIds())
                    .formIds(newLandingPageDto.getFormIds())
                    .wechatPackageId(newLandingPageDto.getWechatPackageId())
                    .build();
            mgkPageOperateLogService.addInsertLog(logDto, newInfo);
        }
    }

    /**
     * 1 优先走avIds
     * 2 没有avIds 走bizIds
     *
     * @return 吧dfvsdfhbfsgsg
     */
    private Integer getPageVideoDuration(List<Long> avIds, List<Integer> bizIds) {
        if (!CollectionUtils.isEmpty(avIds)) {
            // todo 根据avid获取稿件时常
            List<MgkCmArchiveDto> archiveDtos = cmArchiveService.getAchiveDtosByAvids(Lists.newArrayList(avIds.get(0)));
            if (CollectionUtils.isEmpty(archiveDtos)) {
                return 0;
            }
            return archiveDtos.get(0).getDurationInMs();
        }
        // 走bizIds
        if (!CollectionUtils.isEmpty(bizIds)) {
            VideoLibraryDto videoLibraryDto = videoLibraryService.getVideoLibraryDtoByBizId(bizIds.get(0));
            return videoLibraryDto.getDuration();
        }
        return 0;
    }

    private void batchInsertPageFormMapping(long pageId, List<Long> formIds) {
        if (!CollectionUtils.isEmpty(formIds)) {
            List<MgkPageFormMappingPo> mgkPageFormMappingPos = new ArrayList<>();
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            formIds = formIds.stream().distinct().collect(Collectors.toList());
            for (Long formId : formIds) {
                MgkPageFormMappingPo mgkPageFormMappingPo = new MgkPageFormMappingPo();
                mgkPageFormMappingPo.setPageId(pageId);
                mgkPageFormMappingPo.setFormId(formId);
                mgkPageFormMappingPo.setIsDeleted(0);
                mgkPageFormMappingPo.setCtime(timestamp);
                mgkPageFormMappingPo.setMtime(timestamp);
                mgkPageFormMappingPos.add(mgkPageFormMappingPo);
            }
            int pageFormMappingResult = mgkPageFormMappingDao.insertBatch(mgkPageFormMappingPos);
            Assert.isTrue(pageFormMappingResult > 0, "保存失败，请稍后重试");
        }
    }

    private boolean manualUpdateLandingPageStatus(Operator operator, Long pageId, String reason, LandingPageStatusEnum operatedStatus) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        if(!Utils.isPositive(pageId)){
            return false;
        }
        MgkLandingPagePo po = this.getByPageId(pageId);
        if (Objects.isNull(po)) {
            return false;
        }

        validateUpdatePageStatus(operator, po, operatedStatus, true);

        // 对于影子落地页推审 不记录日志 仅原始落地页推审时记录日志 但是要改状态
        if (LandingPageStatusEnum.WAIT_AUDIT.equals(operatedStatus)
                && IsModelEnum.SHADOW_TEMPLATE.getCode().equals(po.getIsModel())) {
            MgkLandingPagePoExample example = new MgkLandingPagePoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andPageIdEqualTo(pageId);

            MgkLandingPagePo record = new MgkLandingPagePo();
            record.setStatus(operatedStatus.getCode());
            record.setReason(reason);

            int result = mgkLandingPageDao.updateByExampleSelective(record, example);
            Assert.isTrue(result == 1, "操作失败, 请稍后重试");
            return true;
        }

        MgkOperationType mgkOperationType = operatedStatus.getMgkOperationType();
        if (mgkOperationType != null) {
            MgkPageOperateLogDto logDto = MgkPageOperateLogDto.builder()
                    .pageId(pageId)
                    .operatorName(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .operateType(mgkOperationType.getCode())
                    .operatorId(operator.getOperatorId())
                    .originStatusDesc(LandingPageStatusEnum.getByCode(po.getStatus()).getDesc())
                    .newStatusDesc(operatedStatus.getDesc())
                    .reason(reason)
                    .build();
            mgkPageOperateLogService.addUpdateStatusLog(logDto);
        }

        // 对于已发布的原始落地页推审 不改变原始落地页状态 投放端仍然展示已发布
        // 其他情况下 落地页全部改为待审核状态 但是日志要记
        if (LandingPageStatusEnum.WAIT_AUDIT.equals(operatedStatus)
                && IsModelEnum.PAGE.getCode().equals(po.getIsModel())
                && LandingPageStatusEnum.PUBLISHED.getCode().equals(po.getStatus())) {
            return false;
        }

        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId);

        MgkLandingPagePo record = new MgkLandingPagePo();
        record.setStatus(operatedStatus.getCode());
        record.setReason(reason);

        int result = mgkLandingPageDao.updateByExampleSelective(record, example);
        Assert.isTrue(result == 1, "操作失败, 请稍后重试");

        //insert log
        mgkLogService.insertLog(NewLogOperationDto.builder()
                .accountId(po.getAccountId())
                .objId(pageId)
                .objFlag(LogObjFlagEnum.LANDING_PAGE.getCode())
                .operateType(operatedStatus.getLogOperateType().getCode())
                .operatorUsername(operator.getOperatorName())
                .operatorType(operator.getOperatorType().getCode())
                .oldValue("")
                .newValue("")
                .build());
        return true;
    }

    /**
     * 系统内部更新落地页状态 不落日志 没有手动校验
     */
    private void updateLandingPageStatus(long pageId, String reason, LandingPageStatusEnum operatedStatus) {
        MgkLandingPagePo po = this.getByPageId(pageId);
        Assert.notNull(po, "变更状态落地页不可为空");
        // 什么都没改 直接返回
        if (po.getStatus().equals(operatedStatus.getCode())
                && po.getReason().equals(reason)) {
            return;
        }
        // 校验
        this.validateUpdatePageStatus(null, po, operatedStatus, false);

        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId);

        po.setStatus(operatedStatus.getCode());
        po.setReason(reason);

        int result = mgkLandingPageDao.updateByPrimaryKeySelective(po);
        Assert.isTrue(result == 1, "操作失败, 请稍后重试");
    }

    private void validateUpdatePageStatus(Operator operator,
                                          MgkLandingPagePo po,
                                          LandingPageStatusEnum operatedStatus,
                                          boolean isManual) {
        Assert.notNull(po, "该落地页不存在");
        Assert.isTrue(!LandingPageStatusEnum.PUBLISHED.equals(operatedStatus)
                || !LandingPageTypeEnum.CUSTOM_NATIVE.getCode().equals(po.getType()), "该模版已下线，无法发布");
        List<Integer> appIds = this.getBindingAppPackageIdsInPageIds(Lists.newArrayList(po.getPageId()));
        if (!CollectionUtils.isEmpty(appIds) && LandingPageStatusEnum.PUBLISHED.equals(operatedStatus)) {
            List<AppPackageDto> appPackageDtos = soaAppPackageService.query(QueryAppPackageDto.builder()
                    .ids(appIds)
                    .status(AppPackageStatus.VALID.getCode())
                    .platformStatus(AppPackagePlatformStatus.VALID.getCode()).build());
            Assert.isTrue(appIds.size() == appPackageDtos.size(), "绑定的下载包被删除，请重新选择");
        }

        this.validateToStatus(LandingPageStatusEnum.getByCode(po.getStatus()), operatedStatus);
        if (isManual) {
            Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
//            Assert.isTrue(po.getAccountId().equals(operator.getOperatorId()), "您不能操作不属于当前账户的落地页");
            if (LandingPageStatusEnum.PUBLISHED.equals(operatedStatus)) {
                Assert.isTrue(!LandingPageStatusEnum.WAIT_AUDIT.getCode().equals(po.getStatus()), "待审落地页不能手动发布");
            }
        }
    }

    /**
     * 影子落地页内容回写 如果不过审 不得回写 回写后直接发布
     */
    public void shadowPageWriteBack(Long originPageId, Long shadowPageId, boolean isAuditPass) {
        Assert.isTrue(Utils.isPositive(originPageId) && Utils.isPositive(shadowPageId), "审核回填参数错误");
        // 如果回写时 原落地页不是发布状态 需要把原落地页内容保存至审核副本 作为预览使用
        LandingPageConfigDto originPageConfigDto = this.getLandingPageConfigDtoByPageId(originPageId);
        UpdateLandingPageDto updateShadowLandingPageDto = UpdateLandingPageDto.convertFromConfigDto(originPageConfigDto, shadowPageId);
        updateShadowLandingPageDto.setIsModel(IsModelEnum.SHADOW_TEMPLATE.getCode());
        LandingPageConfigDto shadowPageConfigDto = this.getLandingPageConfigDtoByPageId(shadowPageId);
        UpdateLandingPageDto updateLandingPageDto = UpdateLandingPageDto.convertFromConfigDto(shadowPageConfigDto, originPageId);
        updateLandingPageDto.setIsModel(IsModelEnum.PAGE.getCode());

        // 校验数据存在
        Assert.notNull(updateLandingPageDto, "落地页信息不可为空");
        Assert.notNull(updateShadowLandingPageDto, "审核副本落地页信息不可为空");

        // 标识回写
        updateLandingPageDto.setIsWriteBack(WhetherEnum.YES.getCode());
        updateShadowLandingPageDto.setIsWriteBack(WhetherEnum.YES.getCode());

        // 如果是审核拒绝 进行标识
        if (!isAuditPass) {
            updateLandingPageDto.setIsAuditRejectWriteBack(true);
            updateShadowLandingPageDto.setIsAuditRejectWriteBack(true);
        }

        Operator operator = Operator.builder()
                .operatorId(shadowPageConfigDto.getAccountId())
                .operatorName(Operator.SYSTEM.getOperatorName())
                .operatorType(OperatorType.ADVERTISERS)
                .build();
        this.update(operator, updateLandingPageDto, false);
        this.updateMgkLandingPageTemplatePage(operator, updateLandingPageDto);
        this.updateLandingPageTemplateWithoutProperVideoComponent(operator, updateLandingPageDto);
        if (!LandingPageStatusEnum.PUBLISHED.getCode().equals(originPageConfigDto.getStatus()) && isAuditPass) {
            this.update(operator, updateShadowLandingPageDto, true);
        }
    }

    public Map<Long, List<Integer>> getAppPackageIdMapInPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyMap();
        }
        MgkLandingPageAppPackagePoExample example = new MgkLandingPageAppPackagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusIn(Lists.newArrayList(MgkAppPakcageStatusEnum.VALID.getCode(), MgkAppPakcageStatusEnum.DELETED.getCode()))
                .andPageIdIn(pageIds);

        List<MgkLandingPageAppPackagePo> pos = mgkLandingPageAppPackageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }
        return pos.stream()
                .collect(Collectors.groupingBy(MgkLandingPageAppPackagePo::getPageId,
                        Collectors.mapping(MgkLandingPageAppPackagePo::getAppPackageId,
                                Collectors.toList())));
    }

    private List<Integer> getBindingAppPackageIdsInPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }
        MgkLandingPageAppPackagePoExample example = new MgkLandingPageAppPackagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusIn(Lists.newArrayList(MgkAppPakcageStatusEnum.VALID.getCode(), MgkAppPakcageStatusEnum.DELETED.getCode()))
                .andPageIdIn(pageIds);

        List<MgkLandingPageAppPackagePo> pos = mgkLandingPageAppPackageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(MgkLandingPageAppPackagePo::getAppPackageId).distinct().collect(Collectors.toList());
    }

    private void updateLandingPage(Operator operator, UpdateLandingPageDto updateLandingPageDto) {

        LandingPageConfigDto landingPageConfigDto = this.getLandingPageConfigDtoByPageId(updateLandingPageDto.getPageId());

        LandingPageStatusEnum landingPageStatus = LandingPageStatusEnum.getByCode(landingPageConfigDto.getStatus());
        Assert.isTrue(landingPageStatus.isModifiable(), "状态为" + landingPageStatus.getDesc() + "的落地页不可修改");

        boolean landingPageIsModified = !this.isLandingPagePoModified(landingPageConfigDto, updateLandingPageDto);
        MgkLandingPagePo existRecord = this.getByPageId(updateLandingPageDto.getPageId()),
                landingPageRecord = new MgkLandingPagePo();
        Assert.notNull(existRecord, "更新目标落地页不可为空");
        Long currentVersion = existRecord.getVersion() + 1;
        landingPageRecord.setId(landingPageConfigDto.getId());
        landingPageRecord.setVersion(currentVersion);

        if (landingPageIsModified) {
            BeanUtils.copyProperties(updateLandingPageDto, landingPageRecord);
            landingPageRecord.setType(updateLandingPageDto.getPageType());

            // 更新视频时长
            landingPageRecord.setVideoDuration(getPageVideoDuration(updateLandingPageDto.getAvIds(), updateLandingPageDto.getBizIds()));

            if (!Utils.isPositive(updateLandingPageDto.getIsVideoPage())) {
                landingPageRecord.setIsVideoPage((Utils.isPositive(updateLandingPageDto.getBizId()) ||
                        !CollectionUtils.isEmpty(updateLandingPageDto.getBizIds())
                        || !CollectionUtils.isEmpty(updateLandingPageDto.getAvIds())) ? WhetherEnum.YES.getCode() : WhetherEnum.NO.getCode());
            }

            landingPageRecord.setAvid(CollectionUtils.isEmpty(updateLandingPageDto.getAvIds()) ? 0L : updateLandingPageDto.getAvIds().get(0));
            if(updateLandingPageDto.getStatus() != null){
                landingPageRecord.setStatus(updateLandingPageDto.getStatus());
            }
            // 更新模板
            if (updateLandingPageDto.getIsModel().equals(IsModelEnum.MODEL.getCode())) {
                MgkModelPoExample example = new MgkModelPoExample();
                example.or().andModelIdEqualTo(Long.parseLong(updateLandingPageDto.getModelId()));
                MgkModelPo po = MgkModelPo.builder().modelVersion(updateLandingPageDto.getModelVersion()).build();
                mgkModelDao.updateByExampleSelective(po, example);
            }

        }
        mgkLandingPageDao.updateByPrimaryKeySelective(landingPageRecord);

        if (Boolean.TRUE.equals(updateLandingPageDto.getIsConsultPage())) {
            updateConsultPage(updateLandingPageDto.getPageId() , updateLandingPageDto.getConsultLandingPageDto());
        }
        // 根据 formsIds 判断是否是原生自定义
        // 校验form_ids是否发生变化
        boolean pageFormMappingIsModified = this.isPageFormMappingIsModified(updateLandingPageDto.getPageId(), updateLandingPageDto.getFormIds());
        if (pageFormMappingIsModified) {
            this.updatePageFormMapping(updateLandingPageDto.getPageId(), updateLandingPageDto.getFormIds());
        }

        // 微信小游戏
        boolean pageMiniGameMappingIsModified = mgkMiniGameMappingService.isPageMappingIsModified(updateLandingPageDto.getPageId(), updateLandingPageDto.getMiniGameIds());
        if (pageMiniGameMappingIsModified) {
            mgkMiniGameMappingService.batchUpdatePageMapping(updateLandingPageDto.getPageId(), updateLandingPageDto.getMiniGameIds());
        }

        //微信获客助手
        boolean pageLinkMappingIsModified = workWxCustomerService.isPageMappingIsModified(
                updateLandingPageDto.getPageId(), updateLandingPageDto.getCustomerAcquisitionLinkIds());
        if (pageLinkMappingIsModified) {
            workWxCustomerService.savePageCustomerAcqLink(updateLandingPageDto.getPageId(), updateLandingPageDto.getCustomerAcquisitionLinkIds());
        }

        // 微信加粉微信包
        List<Integer> wechatPackageIds = Objects.isNull(updateLandingPageDto.getWechatPackageId()) ? Collections.emptyList() : Lists.newArrayList(updateLandingPageDto.getWechatPackageId());
        boolean pageWechatPackageMappingIsModified = mgkWechatPackageMappingService.isPageMappingIsModified(updateLandingPageDto.getPageId(), wechatPackageIds);
        if (pageWechatPackageMappingIsModified) {
            mgkWechatPackageMappingService.batchUpdatePageMapping(updateLandingPageDto.getPageId(), wechatPackageIds);
        }

        // 记录refresh log
        if (updateLandingPageDto.getIsRefreshLog() == 1) {
            MgkLandingPageRefreshLogPo po = MgkLandingPageRefreshLogPo.builder()
                    .pageId(landingPageConfigDto.getPageId())
                    .pageConfig(landingPageConfigDto.getConfig())
                    .templateStyle(landingPageConfigDto.getTemplateStyle())
                    .pageType(landingPageConfigDto.getType())
                    .build();
            int res = mgkLandingPageRefreshLogDao.insertSelective(po);
            Assert.isTrue(Utils.isPositive(res), "插入log失败");
        }

        // 更新h5到小程序 即 pageType 1->4 需要将所有包含此页面的204和205页面的web_url替换为高能建站
        // 原有204 205页面可以嵌套h5落地页，此h5落地页原地址（cm.bilibili.com）变为高能地址（gaoneng.bilibili.com）
        // 因此需要通知所有用到此页面的 204 205页面更换地址
        if (updateLandingPageDto.getIsRefreshLog() == 1) {
            refreshContainsH5PageToModify(operator.getOperatorId(), updateLandingPageDto.getPageId(),
                    updateLandingPageDto.getHeader());
        }


        boolean configIsEqual = this.jsonEquals(landingPageConfigDto.getConfig(), updateLandingPageDto.getConfig());
        if (!configIsEqual) {
            updateMgkLandingPageConfig(updateLandingPageDto.getConfig(), landingPageConfigDto.getPageId());

            // 更新 mongo
            mgkMongoLandingPageService.saveLandingPage(MongoLandingPageDto.builder().pageId(landingPageConfigDto.getPageId()).accountId(landingPageConfigDto.getAccountId()).config(JSON.parse(updateLandingPageDto.getConfig())).build());
        }
        boolean avIdsIsEqual = this.jsonEquals(CollectionUtils.isEmpty(landingPageConfigDto.getAvIds()) ? "" : landingPageConfigDto.getAvIds().toString(),
                CollectionUtils.isEmpty(updateLandingPageDto.getAvIds()) ? "" : updateLandingPageDto.getAvIds().toString());

        if (!avIdsIsEqual) {
            MgkLandingPageAvidPoExample mgkLandingPageAvidPoExample = new MgkLandingPageAvidPoExample();
            mgkLandingPageAvidPoExample.or().andPageIdEqualTo(updateLandingPageDto.getPageId());
            MgkLandingPageAvidPo mgkLandingPageAvidPo = new MgkLandingPageAvidPo();
            mgkLandingPageAvidPo.setIsDeleted(IsDeleted.DELETED.getCode());
            mgkLandingPageAvidDao.updateByExampleSelective(mgkLandingPageAvidPo, mgkLandingPageAvidPoExample);
            if (!CollectionUtils.isEmpty(updateLandingPageDto.getAvIds())) {
                this.batchInsetLandingPageAvIds(updateLandingPageDto.getPageId(), updateLandingPageDto.getAvIds());
                this.batchSaveLandingPageArcInfo(updateLandingPageDto.getAvIds());
                mgkLandingPageAvidService.refreshPageIdToAvidMappingInRedis(updateLandingPageDto.getPageId(), updateLandingPageDto.getAvIds().get(0));
            }
        }

        MgkPageDownloadComponentHeightDto updateHeightDto = MgkPageDownloadComponentHeightDto.builder()
                .pageId(updateLandingPageDto.getPageId())
                .totalBlockSize(updateLandingPageDto.getTotalBlockSize())
                .totalDownloadComponentSize(updateLandingPageDto.getTotalDownloadComponentSize())
                .totalFirstScreenDownloadComponentSize(updateLandingPageDto.getTotalFirstScreenDownloadComponentSize())
                .maxDownloadComponentSize(updateLandingPageDto.getMaxDownloadComponentSize())
                .build();
        if (!mgkPageDownloadComponentService.checkDownloadComponentHeightIsEqual(updateHeightDto)) {
            mgkPageDownloadComponentService.updateDownloadComponentHeightInfo(updateHeightDto);
        }

        boolean showUrlsIsEqual = this.jsonEquals(CollectionUtils.isEmpty(landingPageConfigDto.getShowUrls()) ? "" : landingPageConfigDto.getShowUrls().toString(),
                CollectionUtils.isEmpty(updateLandingPageDto.getShowUrls()) ? "" : updateLandingPageDto.getShowUrls().toString());

        if (!showUrlsIsEqual) {
            MgkLandingPageShowUrlPoExample mgkLandingPageShowUrlPoExample = new MgkLandingPageShowUrlPoExample();
            mgkLandingPageShowUrlPoExample.or().andPageIdEqualTo(updateLandingPageDto.getPageId());
            MgkLandingPageShowUrlPo mgkLandingPageShowUrlPo = new MgkLandingPageShowUrlPo();
            mgkLandingPageShowUrlPo.setIsDeleted(IsDeleted.DELETED.getCode());
            mgkLandingPageShowUrlDao.updateByExampleSelective(mgkLandingPageShowUrlPo, mgkLandingPageShowUrlPoExample);
            if (!CollectionUtils.isEmpty(updateLandingPageDto.getShowUrls())) {
                this.batchInsetLandingPageShowUrls(updateLandingPageDto.getPageId(), updateLandingPageDto.getShowUrls());
            }
        }

        this.batchUpdatePageBizMapping(updateLandingPageDto);
        this.setPageBizMappingInRedis(updateLandingPageDto.getPageId(), updateLandingPageDto.getBizIds());

        boolean isModified = landingPageIsModified || !configIsEqual || !avIdsIsEqual;
        //insert log 非回写时才需要记录操作日志 以下为记录日志逻辑
        if (!isModified || Utils.isPositive(updateLandingPageDto.getIsWriteBack())) {
            return;
        }

        // 影子落地页仅在原始落地页已发布时记录日志
        if (IsModelEnum.SHADOW_TEMPLATE.getCode().equals(updateLandingPageDto.getIsModel())
                && !Utils.isPositive(updateLandingPageDto.getIsShadowOriginPublished())) {
            return;
        }
        // 记录日志时,影子落地页id需要替换成原落地页id
        Long objId = updateLandingPageDto.getPageId();
        if (IsModelEnum.SHADOW_TEMPLATE.getCode().equals(updateLandingPageDto.getIsModel())) {
            objId = mgkShadowLandingPageService.getPageIdByShadowPageId(objId);
        }

        if (IsModelEnum.SHADOW_TEMPLATE.getCode().equals(updateLandingPageDto.getIsModel())
                || IsModelEnum.PAGE.getCode().equals(updateLandingPageDto.getIsModel())) {
            UpdateLandingPageDto oldValue = UpdateLandingPageDto.builder().build();
            BeanUtils.copyProperties(landingPageConfigDto, oldValue);
            oldValue.setConfig(landingPageConfigDto.getConfig());

            mgkLogService.insertLog(NewLogOperationDto.builder()
                    .accountId(landingPageConfigDto.getAccountId())
                    .objId(objId)
                    .objFlag(LogObjFlagEnum.LANDING_PAGE.getCode())
                    .operateType(LogOperateTypeEnum.LANDING_PAGE_MODIFY.getCode())
                    .operatorUsername(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .oldValue(oldValue)
                    .newValue(updateLandingPageDto)
                    .build());

            MgkPageOperateLogDto logDto = MgkPageOperateLogDto.builder()
                    .pageId(objId)
                    .operatorName(operator.getOperatorName())
                    .operateType(MgkOperationType.UPDATE.getCode())
                    .operatorType(operator.getOperatorType().getCode())
                    .operatorId(operator.getOperatorId())
                    .build();
            MgkPageOperateLogInfoDto oldInfo = MgkPageOperateLogInfoDto.builder()
                    .appPackageIds(landingPageConfigDto.getAppPackageIds())
                    .formIds(landingPageConfigDto.getFormIds())
                    .wechatPackageId(landingPageConfigDto.getWechatPackageId())
                    .build();
            MgkPageOperateLogInfoDto newInfo = MgkPageOperateLogInfoDto.builder()
                    .appPackageIds(updateLandingPageDto.getAppPackageIds())
                    .formIds(updateLandingPageDto.getFormIds())
                    .wechatPackageId(updateLandingPageDto.getWechatPackageId())
                    .build();
            mgkPageOperateLogService.addUpdateLog(logDto, oldInfo, newInfo);
        }
    }

    public void updateLandingPageName(UpdateLandingPageNameDto updateNameDto) {
        Assert.isTrue(Utils.isPositive(updateNameDto.getAccountId()), "账户id不可为空");
        Assert.isTrue(Utils.isPositive(updateNameDto.getPageId()), "落地页id不可为空");
        Assert.isTrue(!StringUtils.isEmpty(updateNameDto.getPageName()), "落地页名称不可为空");

        Long pageId = updateNameDto.getPageId();
        MgkLandingPageDto pageDto = this.getLandingPageDtoByPageId(pageId);
        Assert.isTrue(pageDto.getAccountId().equals(updateNameDto.getAccountId()), "您不能更新不属于您的落地页");

        List<Long> relationPageIds = getRelationPageIds(pageId);
        MgkLandingPagePoExample exm = new MgkLandingPagePoExample();
        exm.or().andPageIdIn(relationPageIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        MgkLandingPagePo updatePo = new MgkLandingPagePo();
        updatePo.setName(updateNameDto.getPageName());

        mgkLandingPageDao.updateByExampleSelective(updatePo, exm);
    }

    public List<Long> getRelationPageIds(Long pageId){
        List<Long> relationPageIds = Lists.newArrayList(pageId);
        // 联投副本
        List<MgkTemplatePageMappingPo> templatePageMappingPos =
                getTemplatePageMappingPos(new QueryTemplatePageDto(pageId, null, null, null));
        if (!CollectionUtils.isEmpty(templatePageMappingPos)) {
            relationPageIds.add(templatePageMappingPos.get(0).getTemplatePageId());
        }

        // 无视频图文副本
        Long templateWithoutArcPageId = getMgkLandingPageTemplateWithoutArcIdByOriginPageId(pageId);
        if (Utils.isPositive(templateWithoutArcPageId)) {
            relationPageIds.add(templateWithoutArcPageId);
        }

        // 审核副本
        Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(pageId);
        if (Utils.isPositive(shadowPageId)) {
            relationPageIds.add(shadowPageId);
        }
        return relationPageIds;
    }

    private void updateMgkLandingPageConfig(String config, Long pageId) {
        MgkLandingPageConfigPo landingPageConfigRecord = new MgkLandingPageConfigPo();
        landingPageConfigRecord.setConfig(config);

        MgkLandingPageConfigPoExample configPoExample = new MgkLandingPageConfigPoExample();
        configPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId);
        mgkLandingPageConfigDao.updateByExampleSelective(landingPageConfigRecord, configPoExample);
    }

    private void refreshContainsH5PageToModify(Integer accountId, Long pageId, Integer header) {
        // 1 获取这个用户所有的204和205页面
        List<Long> pageIds = getHalfVideoAndIosIds(accountId);
        if (CollectionUtils.isEmpty(pageIds)) {
            return;
        }
        // 2 获取所有的config中web_url中包含此pageId的页面
        List<MongoLandingPageDto> mongoLandingPageDtos = mgkMongoLandingPageService.findContainsWebUrlByPageIds(accountId, pageIds, pageId);

        // 3 修改
        mongoLandingPageDtos.forEach(dto -> {
            JSONArray o = (JSONArray) toJSON(dto.getConfig());
            JSONObject o1 = (JSONObject) o.get(0);
            o1.put(MgkConstants.MGK_PAGE_MONGO_FIELD_NAME_WEB_URL, landingPageUrlProc.getPcLaunchUrl(pageId, header));
            dto.setConfig(o);
        });

        // 4 同步DB和MongoDB
        mongoLandingPageDtos.forEach(dto -> {
            updateMgkLandingPageConfig(JSONObject.toJSONString(dto.getConfig()), dto.getPageId());
            mgkMongoLandingPageService.saveLandingPage(MongoLandingPageDto.builder().pageId(dto.getPageId()).accountId(dto.getAccountId()).config(dto.getConfig()).build());
        });
    }


    private List<Long> getHalfVideoAndIosIds(Integer accountId) {
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId)
                .andTemplateStyleIn(Lists.newArrayList(TemplateStyleEnum.HALF_SCREEN_VIDEO.getCode(),
                        TemplateStyleEnum.HALF_SCREEN_VIDEO_IOS.getCode()));
        List<MgkLandingPagePo> mgkLandingPagePos = mgkLandingPageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(mgkLandingPagePos)) {
            return Collections.emptyList();
        }
        return mgkLandingPagePos.stream().map(MgkLandingPagePo::getPageId).collect(Collectors.toList());
    }

    private void setPageBizMappingInRedis(Long pageId, List<Integer> bizIds) {
        if (!CollectionUtils.isEmpty(bizIds)) {
            stringRedisTemplate.opsForValue().set(MGK_LANDING_PAGE_PAGE_ID_BIZ_ID_REDIS_KEY + pageId, bizIds.get(0).toString(), MGK_LANDING_PAGE_PAGE_ID_BIZ_ID_REDIS_EXPIRE, TimeUnit.HOURS);
        } else {
            stringRedisTemplate.opsForValue().set(MGK_LANDING_PAGE_PAGE_ID_BIZ_ID_REDIS_KEY + pageId, "", MGK_LANDING_PAGE_PAGE_ID_BIZ_ID_REDIS_EXPIRE, TimeUnit.HOURS);
        }
    }

    private void batchUpdatePageBizMapping(UpdateLandingPageDto updateLandingPageDto) {
        MgkPageBizMappingPoExample example = new MgkPageBizMappingPoExample();
        example.or().andPageIdEqualTo(updateLandingPageDto.getPageId());
        List<MgkPageBizMappingPo> pos = mgkPageBizMappingDao.selectByExample(example);
        Map<Integer, MgkPageBizMappingPo> bizPoMap = new HashMap();
        List<MgkPageBizMappingPo> insertMappingPoList = new ArrayList();

        //标为全部删除,记录起来 然后逐步插入或者更新
        if (!CollectionUtils.isEmpty(pos)) {
            pos.forEach(po -> {
                po.setIsDeleted(MgkPageBizMappingStatusEnum.DELETED.getCode());
                bizPoMap.put(po.getBizId(), po);
            });

        }
        if (!CollectionUtils.isEmpty(updateLandingPageDto.getBizIds())) {
            Stream.iterate(0, i -> i + 1).limit(updateLandingPageDto.getBizIds().size()).forEach(index -> {
                Integer bizId = updateLandingPageDto.getBizIds().get(index);
                MgkPageBizMappingPo po = bizPoMap.get(bizId);
                if (po != null) {
                    po.setIsDeleted(MgkPageBizMappingStatusEnum.VALID.getCode());
                    po.setBizIndex(index);
                } else {
                    insertMappingPoList.add(MgkPageBizMappingPo.builder()
                            .pageId(updateLandingPageDto.getPageId())
                            .bizId(bizId)
                            .bizIndex(index)
                            .isDeleted(MgkPageBizMappingStatusEnum.VALID.getCode())
                            .build());
                }
            });
        }
        for (MgkPageBizMappingPo po : pos) {
            MgkPageBizMappingPoExample updateExample = new MgkPageBizMappingPoExample();
            updateExample.or().andPageIdEqualTo(po.getPageId()).andBizIdEqualTo(po.getBizId());
            MgkPageBizMappingPo updatePo = MgkPageBizMappingPo.builder()
                    .id(po.getId())
                    .bizId(po.getBizId())
                    .pageId(po.getPageId())
                    .bizIndex(po.getBizIndex())
                    .isDeleted(po.getIsDeleted())
                    .mtime(new Timestamp(new Date().getTime()))
                    .build();
            mgkPageBizMappingDao.updateByPrimaryKeySelective(updatePo);
        }

        if (!CollectionUtils.isEmpty(insertMappingPoList)) {
            extMgkPageBizMappingDao.insertBatch(insertMappingPoList);
        }
    }

    private void updatePageFormMapping(Long pageId, List<Long> formIds) {
        List<Long> originPageIds = this.getPageFormMappingPoByPageId(pageId).stream().map(MgkPageFormMappingPo::getFormId).collect(Collectors.toList());
        List<Long> intersection = originPageIds.stream().filter(formIds::contains).collect(Collectors.toList());
        List<Long> removePart = originPageIds.stream().filter(o -> !intersection.contains(o)).collect(Collectors.toList());
        List<Long> appendPart = formIds.stream().filter(o -> !intersection.contains(o)).collect(Collectors.toList());
        this.batchUpdatePageFormMappingStatus(pageId, removePart, IsDeleted.DELETED.getCode());
        if (!CollectionUtils.isEmpty(appendPart)) {
            List<Long> allPageIds = this.getPageFormMappingPoAllByPageId(pageId).stream().map(MgkPageFormMappingPo::getFormId).collect(Collectors.toList());
            List<Long> needUpdatePageIds = appendPart.stream().filter(allPageIds::contains).collect(Collectors.toList());
            List<Long> needInertPageIds = appendPart.stream().filter(a -> !allPageIds.contains(a)).collect(Collectors.toList());
            this.batchInsertPageFormMapping(pageId, needInertPageIds);
            this.batchUpdatePageFormMappingStatus(pageId, needUpdatePageIds, IsDeleted.VALID.getCode());
        }
    }

    private List<MgkPageFormMappingPo> getPageFormMappingPoAllByPageId(Long pageId) {
        MgkPageFormMappingPoExample mgkPageFormMappingPoExample = new MgkPageFormMappingPoExample();
        mgkPageFormMappingPoExample.or().andPageIdEqualTo(pageId);
        return mgkPageFormMappingDao.selectByExample(mgkPageFormMappingPoExample);
    }

    private void batchUpdatePageFormMappingStatus(Long pageId, List<Long> removePart, int code) {
        if (!CollectionUtils.isEmpty(removePart)) {
            MgkPageFormMappingPoExample mgkPageFormMappingPoExample = new MgkPageFormMappingPoExample();
            mgkPageFormMappingPoExample.createCriteria().andPageIdEqualTo(pageId).andFormIdIn(removePart);
            mgkPageFormMappingDao.updateByExampleSelective(MgkPageFormMappingPo.builder()
                    .isDeleted(code).build(), mgkPageFormMappingPoExample);
        }
    }

    private boolean isPageFormMappingIsModified(Long pageId, List<Long> upDateFormIds) {
        Set<Long> originPageIdsSet = this.getPageFormMappingPoByPageId(pageId).stream()
                .map(MgkPageFormMappingPo::getFormId)
                .collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(upDateFormIds)){
            return originPageIdsSet.size() != 0;
        }
        Set<Long> updateFormIdsSet = new HashSet<>(upDateFormIds);
        Assert.isTrue(upDateFormIds.size() == updateFormIdsSet.size(), "落地页包含相同的表单");
        return !originPageIdsSet.equals(updateFormIdsSet);
    }

    private Map<Long, List<Long>> getPageFormIdMapByPageIds(List<Long> pageIds) {
        Map<Long, List<MgkPageFormMappingPo>> pageFormMappingPoMap = getPageFormMappingPoMapByPageIds(pageIds);
        HashMap<Long, List<Long>> result = new HashMap<>(pageIds.size());
        pageFormMappingPoMap.keySet().forEach(pageId -> {
            List<Long> formIds = pageFormMappingPoMap.get(pageId).stream()
                    .map(MgkPageFormMappingPo::getFormId)
                    .collect(Collectors.toList());
            result.put(pageId, formIds);
        });
        return result;
    }

    private Map<Long, List<MgkPageFormMappingPo>> getPageFormMappingPoMapByPageIds(List<Long> pageIds) {
        MgkPageFormMappingPoExample mgkPageFormMappingPoExample = new MgkPageFormMappingPoExample();
        mgkPageFormMappingPoExample.or().andPageIdIn(pageIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkPageFormMappingPo> mgkPageFormMappingPos = mgkPageFormMappingDao.selectByExample(mgkPageFormMappingPoExample);
        return mgkPageFormMappingPos.stream().collect(Collectors.groupingBy(MgkPageFormMappingPo::getPageId));
    }

    private List<MgkPageFormMappingPo> getPageFormMappingPoByPageId(Long pageId) {
        MgkPageFormMappingPoExample mgkPageFormMappingPoExample = new MgkPageFormMappingPoExample();
        mgkPageFormMappingPoExample.or().andPageIdEqualTo(pageId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return mgkPageFormMappingDao.selectByExample(mgkPageFormMappingPoExample);
    }

    private boolean isLandingPagePoModified(LandingPageConfigDto oldValue, UpdateLandingPageDto newValue) {

        List<String> ignores = this.getSourcePropertyNamesExcludeTarget(LandingPageConfigDto.class, UpdateLandingPageDto.class);

        MgkLandingPagePo oldValue1 = new MgkLandingPagePo();
        BeanUtils.copyProperties(oldValue, oldValue1, ignores.toArray(new String[]{}));

        MgkLandingPagePo newValue1 = new MgkLandingPagePo();
        BeanUtils.copyProperties(newValue, newValue1);

        return jsonEquals(JSON.toJSONString(oldValue1), JSON.toJSONString(newValue1));
    }

    private <S, T> List<String> getSourcePropertyNamesExcludeTarget(Class<S> s, Class<T> t) {
        List<String> sourcePds = Arrays.asList(BeanUtils.getPropertyDescriptors(s)).stream().map(PropertyDescriptor::getName).collect(Collectors.toList());
        List<String> targetPds = Arrays.asList(BeanUtils.getPropertyDescriptors(t)).stream().map(PropertyDescriptor::getName).collect(Collectors.toList());
        return sourcePds.stream().filter(p -> !targetPds.contains(p)).collect(Collectors.toList());
    }

    private boolean jsonEquals(String expectedJson, String actualJson) {
        try {
            return JSONCompare.compareJSON(expectedJson, actualJson, JSONCompareMode.STRICT).passed();
        } catch (final JSONException e) {
            LOGGER.error(String.format("JSON compare threw exception when trying to compare %n %s %n with %n %s%n Exception was: %n%s ",
                    expectedJson, actualJson, e));
            return false;
        }
    }

    private List<MgkLandingPageDto> convertLandingPagePosToDtos(List<MgkLandingPagePo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertLandingPagePoToDto).collect(Collectors.toList());
    }

    private MgkLandingPageDto convertLandingPagePoToDto(MgkLandingPagePo po) {
        MgkLandingPageDto dto = MgkLandingPageDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }


    public List<MgkLandingPagePo> getRefreshLandingPagePosByPage(int startId, int limit) {
        MgkLandingPagePoExample pagePoExample = new MgkLandingPagePoExample();
        pagePoExample.or().andIdGreaterThanOrEqualTo(startId)
                .andIdLessThanOrEqualTo(startId + limit)
                .andIsModelNotEqualTo(IsModelEnum.SHADOW_TEMPLATE.getCode())
                .andTypeIn(Lists.newArrayList(LandingPageTypeEnum.NATIVE.getCode(),
                        LandingPageTypeEnum.CUSTOM_NATIVE.getCode(), LandingPageTypeEnum.APPLETS.getCode(),
                        LandingPageTypeEnum.BUSINESS_TOOL.getCode(),LandingPageTypeEnum.CONSULT_PAGE.getCode()))
                .andStatusIn(Lists.newArrayList(LandingPageStatusEnum.CAN_BE_VIEW_STATUS_LIST))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        return mgkLandingPageDao.selectByExample(pagePoExample);
    }

    public void refreshLandingPageInRedis(List<MgkLandingPagePo> pos,
                                          Map<Integer, List<String>> accountAwakenWhiteListMap) {
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }
        List<Long> pageIds = pos.stream().map(MgkLandingPagePo::getPageId).collect(Collectors.toList());

        List<MgkLandingPageConfigPo> configPos = CollectionHelper.callInBatches(pageIds, 100, pids -> {
            MgkLandingPageConfigPoExample configPoExample = new MgkLandingPageConfigPoExample();
            configPoExample.or().andPageIdIn(pids).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

            return mgkLandingPageConfigDao.selectByExampleWithBLOBs(configPoExample);
        });

        List<MgkLandingPageShowUrlPo> showUrlPos = CollectionHelper.callInBatches(pageIds, 200, pids -> {
            MgkLandingPageShowUrlPoExample showUrlExample = new MgkLandingPageShowUrlPoExample();
            showUrlExample.or().andPageIdIn(pids).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

            return mgkLandingPageShowUrlDao.selectByExample(showUrlExample);
        });

        Map<Long, String> configPoMap = configPos.stream().collect(Collectors.toMap(MgkLandingPageConfigPo::getPageId, MgkLandingPageConfigPo::getConfig));
        Map<Long, List<String>> showUrlPoMap = showUrlPos.stream().collect(Collectors.groupingBy(MgkLandingPageShowUrlPo::getPageId, Collectors.mapping(MgkLandingPageShowUrlPo::getShowUrl, Collectors.toList())));

        // 页面下载白名单
        Map<Long, List<AppPackageBean>> pageDownloadWhiteListMap = new HashMap<>(pageIds.size());
        CollectionHelper.processInBatches(pageIds, 100, subPageIdList -> {
            Map<Long, List<AppPackageBean>> subWhiteListMap = getDownloadWhiteListByPageId(subPageIdList);
            if (!CollectionUtils.isEmpty(subWhiteListMap)) {
                pageDownloadWhiteListMap.putAll(subWhiteListMap);
            }
        });

        Map<Long, List<Long>> pageFormIdMap = new HashMap<>();
        CollectionHelper.processInBatches(pageIds, 100, subPageIdList -> {
            Map<Long, List<Long>> subPageFormIdMap = getPageFormIdMapByPageIds(subPageIdList);
            if (!CollectionUtils.isEmpty(subPageFormIdMap)) {
                pageFormIdMap.putAll(subPageFormIdMap);
            }
        });

        Map<Long, List<Integer>> pageWechatPackageIdMap = new HashMap<>();
        CollectionHelper.processInBatches(pageIds, 100, subPageIdList -> {
            Map<Long, List<Integer>> subPageWechatPackageIdMap = mgkWechatPackageMappingService.getPageWechatPackageIdMapByPageIds(subPageIdList);
            if (!CollectionUtils.isEmpty(subPageWechatPackageIdMap)) {
                pageWechatPackageIdMap.putAll(subPageWechatPackageIdMap);
            }
        });

        Map<Long, MgkPageDownloadComponentHeightDto> pageDownloadComponentHeightDtoMap = new HashMap<>();
        CollectionHelper.processInBatches(pageIds, 100, subPageList -> {
            Map<Long, MgkPageDownloadComponentHeightDto> downloadComponentMap = mgkPageDownloadComponentService.getMapByPageList(subPageList);
            if (!CollectionUtils.isEmpty(downloadComponentMap)) {
                pageDownloadComponentHeightDtoMap.putAll(downloadComponentMap);
            }
        });

        Map<Long, List<Integer>> miniGameIdsMap = new HashMap<>();
        Map<Integer, LauMiniGameDto> miniGameBomap = new HashMap<>();
        CollectionHelper.processInBatches(pageIds, 100, subPageIdList -> {
            Map<Long, List<Integer>> miniGameMap = mgkMiniGameMappingService.getPageMappingIdsMapByPageId(subPageIdList);
            if (!CollectionUtils.isEmpty(miniGameMap)) {
                miniGameIdsMap.putAll(miniGameMap);
            }
        });

        List<Integer> miniGameIds = miniGameIdsMap.values().stream().distinct().flatMap(Collection::stream).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(miniGameIds)) {
            List<LauMiniGameDto> lauMiniGameDtos = soaLauMiniGameService.queryLauMiniGames(QueryLauMiniGameDto.builder().ids(miniGameIds).build());
            if (!CollectionUtils.isEmpty(lauMiniGameDtos)) {
                miniGameBomap = lauMiniGameDtos.stream().collect(Collectors.toMap(LauMiniGameDto::getId, Function.identity()));
            }
        }
        Map<Integer, LauMiniGameDto> finalMiniGameBomap = miniGameBomap;

        Map<Long, List<String>> pageId2LinkIds = new HashMap<>();
        CollectionHelper.processInBatches(pageIds, 100, subPageIdList -> {
            Map<Long, List<String>> pageId2LinkIdsMap = workWxCustomerService
                    .getPageCustomerAcqLinkByPageIds(subPageIdList);
            if (!CollectionUtils.isEmpty(pageId2LinkIdsMap)) {
                pageId2LinkIds.putAll(pageId2LinkIdsMap);
            }
        });

        Map<Long, List<GameDto>> pageId2Games = new HashMap<>();
        CollectionHelper.processInBatches(pageIds, 100, subPageIdList -> {
            Map<Long, List<GameDto>> games = this.getLandingPageGames(subPageIdList);
            if (!CollectionUtils.isEmpty(games)) {
                pageId2Games.putAll(games);
            }
        });


        for (MgkLandingPagePo mgkLandingPagePo : pos) {
            if (LandingPageTypeEnum.NATIVE.getCode().equals(mgkLandingPagePo.getType())
                    || LandingPageTypeEnum.CUSTOM_NATIVE.getCode().equals(mgkLandingPagePo.getType())
                    || LandingPageTypeEnum.APPLETS.getCode().equals(mgkLandingPagePo.getType())
                    || LandingPageTypeEnum.BUSINESS_TOOL.getCode().equals(mgkLandingPagePo.getType())
                    || LandingPageTypeEnum.CONSULT_PAGE.getCode().equals(mgkLandingPagePo.getType())) {
                String pageIdRedisKey = LANDING_PAGE_PREFIX_IN_REDIS + mgkLandingPagePo.getPageId();
                try {
                    List<Integer> pageMiniGameIds = miniGameIdsMap.getOrDefault(mgkLandingPagePo.getPageId(),
                            Collections.emptyList());
                    List<LauMiniGameDto> pageMiniGameDtos = pageMiniGameIds.stream()
                            .map(miniGameId -> finalMiniGameBomap.getOrDefault(miniGameId, LauMiniGameDto.builder().build()))
                            .collect(Collectors.toList());
                    RefreshPageConfigDto refreshDto = RefreshPageConfigDto.builder()
                            .pageId(mgkLandingPagePo.getPageId())
                            .isModel(mgkLandingPagePo.getIsModel())
                            .templateStyle(mgkLandingPagePo.getTemplateStyle())
                            .showUrls(showUrlPoMap.getOrDefault(mgkLandingPagePo.getPageId(), Collections.emptyList()))
                            .configStr(configPoMap.getOrDefault(mgkLandingPagePo.getPageId(), ""))
                            .awakenWhiteList(
                                    accountAwakenWhiteListMap.getOrDefault(
                                            mgkLandingPagePo.getAccountId(), Lists.newArrayList("tel")))
                            .downloadWhiteList(
                                    pageDownloadWhiteListMap.getOrDefault(
                                            mgkLandingPagePo.getPageId(), Collections.emptyList()))
                            .formIds(
                                    pageFormIdMap.getOrDefault(
                                            mgkLandingPagePo.getPageId(), Collections.emptyList()))
                            .wechatPackageIds(
                                    pageWechatPackageIdMap.getOrDefault(
                                            mgkLandingPagePo.getPageId(), Collections.emptyList()))
                            .accountId(mgkLandingPagePo.getAccountId())
                            .downloadComponentDto(
                                    pageDownloadComponentHeightDtoMap.getOrDefault(
                                            mgkLandingPagePo.getPageId(),
                                            MgkPageDownloadComponentHeightDto.builder().build()))
                            .miniGameIds(pageMiniGameIds)
                            .miniGameCacheBos(pageMiniGameDtos.stream()
                                    .map(r -> LauMiniGameCacheBo.builder()
                                            .id(r.getId())
                                            .game_url(r.getGameUrl())
                                            .name(r.getName())
                                            .origin_id(r.getOriginId())
                                            .build()).collect(Collectors.toList()))
                            .customerAcquisitionLinkIds(pageId2LinkIds.getOrDefault(mgkLandingPagePo.getPageId(),
                                    Collections.emptyList()))
                            .games(pageId2Games.get(mgkLandingPagePo.getPageId()))
                            .build();

                    PageConfig pageConfig = this.buildPageConfig(refreshDto);
                    String pageConfigWithoutOptionsStr = JSON.toJSONString(pageConfig);
                    stringRedisTemplate.opsForValue().set(pageIdRedisKey, pageConfigWithoutOptionsStr, LANDING_PAGE_EXPIRE_TIME_IN_REDIS, TimeUnit.MINUTES);
                } catch (Exception e) {
                    LOGGER.error("buildPageConfig error pageId {}", mgkLandingPagePo.getPageId(), e);
                    continue;
                }

                LOGGER.info("set" + LANDING_PAGE_PREFIX_IN_REDIS + mgkLandingPagePo.getPageId() + "in redis");
            }
        }
    }

    protected void refreshCDN(long pageId) {
        if (isRefreshCdn) {
            try {
                for (String scheme : HTTP_SCHEMES) {
                    String fileUrl = String.format(MgkConstants.LANDING_PAGE_URL_PREFIXX, scheme, pageId);
                    String refreshUrl = String.format(this.REFRESH_CDN_URL, fileUrl);
                    URL resourceURL = new URL(refreshUrl);
                    HttpURLConnection httpConnection = (HttpURLConnection) resourceURL.openConnection();
                    httpConnection.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
                    httpConnection.setConnectTimeout(5 * 1000);
                    httpConnection.connect();
                    Assert.isTrue(httpConnection.getResponseCode() == 200, "网络异常，请稍后重试");
                    httpConnection.disconnect();
                }
                Cat.logEvent("Mgk_refreshCDN_SUCCESS", pageId + " refresh_success");
            } catch (Exception e) {
                Cat.logEvent("Mgk_refreshCDN_FAILED", pageId + " refresh_fail");
                LOGGER.error("Mgk_refreshCDN_fail", e);
                Assert.isTrue(false, "操作失败，请稍后重试");
            }
        }
    }

    private void batchInsetLandingPageShowUrls(Long pageId, List<String> showUrls) {
        List<MgkLandingPageShowUrlPo> mgkLandingPageShowUrlPos = new ArrayList<>();
        for (String showUrl : showUrls) {
            MgkLandingPageShowUrlPo mgkLandingPageShowUrlPo = new MgkLandingPageShowUrlPo();
            mgkLandingPageShowUrlPo.setShowUrl(showUrl);
            mgkLandingPageShowUrlPo.setPageId(pageId);
            mgkLandingPageShowUrlPos.add(mgkLandingPageShowUrlPo);
        }
        int result = extMgkLandingPageShowUrlDao.batchInsert(mgkLandingPageShowUrlPos);
        Assert.isTrue(result > 0, "曝光监控链接保存失败，请稍后重试");
    }

    private void batchInsetLandingPageAvIds(Long pageId, List<Long> avIds) {
        List<MgkLandingPageAvidPo> mgkLandingPageAvidPos = new ArrayList<>();
        for (Long avId : avIds) {
            MgkLandingPageAvidPo mgkLandingPageAvidPo = new MgkLandingPageAvidPo();
            mgkLandingPageAvidPo.setAvId(avId);
            mgkLandingPageAvidPo.setPageId(pageId);
            mgkLandingPageAvidPos.add(mgkLandingPageAvidPo);
        }
        int avIdResult = extMgkLandingPageAvidDao.batchInsert(mgkLandingPageAvidPos);
        Assert.isTrue(avIdResult > 0, "保存失败，请稍后重试");
    }

    private void batchSaveLandingPageArcInfo(List<Long> avIds) {
        int avIdResult = lauArchiveInfoService.saveInfoByAvids(avIds);
        Assert.isTrue(avIdResult > 0, "保存失败，请稍后重试");
    }

    private ConsultPageConfig po2config(MgkConsultLandingPagePo po){
        ConsultPageConfig config = new ConsultPageConfig();
        config.setHasWelcomeWords(po.getHasWelcomeWords());
        config.setWelcomeWords(po.getWelcomeWords());
        config.setIsLeaveData(po.getIsLeaveData());
        config.setHasConsultFaq(po.getHasConsultFaq());
        config.setConsultFaq(po.getConsultFaq());
        config.setCustomerServiceType(po.getCustomerServiceType());
        config.setProfile(po.getProfile());
        config.setConnectType(po.getConnectType());
        return config;
    }
    private PageConfig buildPageConfig(LandingPageConfigDto landingPageConfigDto) {
        TemplateStyleEnum templateStyleEnum = TemplateStyleEnum.getByCode(landingPageConfigDto.getTemplateStyle());
        if (!LandingPageTypeEnum.NATIVE.equals(templateStyleEnum.getPageType())
                && !LandingPageTypeEnum.CUSTOM_NATIVE.equals(templateStyleEnum.getPageType())
                && !LandingPageTypeEnum.APPLETS.equals(templateStyleEnum.getPageType())
                && !LandingPageTypeEnum.BUSINESS_TOOL.equals(templateStyleEnum.getPageType())
                && !LandingPageTypeEnum.CONSULT_PAGE.equals(templateStyleEnum.getPageType())) {
            return PageConfig.nullPageConfig();
        }
        Class configClazz = templateStyleEnum.getConfigClazz();
        String configStr = landingPageConfigDto.getConfig();
        List config = Collections.emptyList();
        if (LandingPageTypeEnum.NATIVE.equals(templateStyleEnum.getPageType())) {
            config = JSON.parseArray(configStr, configClazz);
        } else if (LandingPageTypeEnum.CUSTOM_NATIVE.equals(templateStyleEnum.getPageType())) {
            config = Lists.newArrayList(JSON.parseObject(configStr));
        } else if (LandingPageTypeEnum.CONSULT_PAGE.equals(templateStyleEnum.getPageType())) {
            MgkConsultLandingPageExample example = new MgkConsultLandingPageExample();
            example.or().andPageIdIn(Collections.singletonList(landingPageConfigDto.getPageId()));
            List<MgkConsultLandingPagePo> pos = mgkConsultLandingPageDao.selectByExample(example);
            if(pos.isEmpty() || pos.size() > 1){
                LOGGER.warn(String.format("[fillConsultPageAttrs] selectByExample ,over 1 MgkConsultLandingPage find,pageId={}",landingPageConfigDto.getPageId()));
            }else{
                MgkConsultLandingPagePo po = pos.get(0);
                config = Lists.newArrayList(po2config(po));
            }
        } else {
            config = Lists.newArrayList(JSON.parse(configStr));
        }

        // 预加载表单
        List<Long> formIds = landingPageConfigDto.getFormIds();
        List<Object> formDtos = getPreloadForms(formIds);
        AccountCustomerNameInfoDto accountCustomerInfo = mgkAccountService.getAccountCustomerNameInfo(landingPageConfigDto.getAccountId());
        AccountCustomerInfoDto accountCustomerLabelInfo = mgkAccountService.getAccountCustomerInfo(landingPageConfigDto.getAccountId());
        String privacyName = accountCustomerInfo.getCustomerName();
        String privacyUrl = landingPageUrlProc.getPrivacyUrlByEnvironment(privacyName);
        Integer downloadOverLimit = landingPageConfigDto.getIsDownloadOverLimit();
        AccountCustomerInfoDto customerInfoDto = mgkAccountService.getAccountCustomerInfo(landingPageConfigDto.getAccountId());
        List<String> formIdStrList = CollectionUtils.isEmpty(formIds) ?
                Collections.emptyList() : formIds.stream().map(Object::toString)
                .collect(Collectors.toList());

        PageImageEnhancementDto enhancementDto = pageImageEnhancementService
                .getImageEnhancementInfo(landingPageConfigDto.getPageId());

        return PageConfig
                .builder()
                .isModel(landingPageConfigDto.getIsModel())
                .template_style(landingPageConfigDto.getTemplateStyle())
                .show_urls(landingPageConfigDto.getShowUrls())
                .privacy_name(privacyName)
                .privacy_url(privacyUrl)
                .config(config)
                .form_dtos(formDtos)
                .wechat_package_id(landingPageConfigDto.getWechatPackageId())
                .open_whitelist(getAwakenWhiteListByAccountId(landingPageConfigDto.getAccountId()))
                .download_whitelist(getDownloadWhiteListByPageId(Lists.newArrayList(landingPageConfigDto.getPageId()))
                        .getOrDefault(landingPageConfigDto.getPageId(), Collections.emptyList()))
                .wechat_is_manual(wechatIsManual)
                .form_cache_updated(WhetherEnum.YES.getCode())
                .form_ids(formIdStrList)
                .is_download_button_over_limit(downloadOverLimit)
                .download_button_over_limit_hint(mgkDownloadOverLimitHint)
                .form_button_hit(Utils.isPositive(accountCustomerLabelInfo.getHasFormBottomButtonBlackList()) ? 0 : 1)
                .h5_download_hit(accountCustomerLabelInfo.getH5DownloadHit())
                .mini_game_ids(landingPageConfigDto.getMiniGameIds())
                .mini_game_cache_bos(landingPageConfigDto.getMiniGameCacheBos())
                .account_id(landingPageConfigDto.getAccountId())
                .account_label_config(AccountLabelConfig.builder()
                        .is_show_distance_lbs(WhetherEnum.YES.getCode())
                        .use_ip_location(customerInfoDto.getHasIpLocationKeyList()).build())
                .customer_acq_link_ids(landingPageConfigDto.getCustomerAcquisitionLinkIds())
                .lum(enhancementDto.getLum())
                .grey(enhancementDto.getGrey())
                .color(enhancementDto.getColor())
                .commerce_category_first_id(customerInfoDto.getCmCategoryId())
                .commerce_category_second_id(customerInfoDto.getCmSecondCategoryId())
                .games(Optional.ofNullable(landingPageConfigDto.getGames())
                        .map(gs-> gs.stream().map(g -> GameCache.builder()
                                .game_base_id(g.getGameBaseId())
                                .channel_id(g.getChannelId())
                                .build()).collect(Collectors.toList()))
                        .orElse(null))
                .build();
    }

    private List<Object> getPreloadForms(List<Long> formIds) {
        if (CollectionUtils.isEmpty(formIds)) {
            return Collections.emptyList();
        }
        List<MgkFormDto> mgkFormDtos = formIds.stream()
                .map(form_id -> mgkFormService.getFormDtoByFormIdWithCache(form_id, true))
                .collect(Collectors.toList());
        return mgkFormDtos.stream()
                .map(MgkPageFormDto::convertFormDto2PageFormDto)
                .collect(Collectors.toList());
    }

    private PageConfig buildPageConfig(RefreshPageConfigDto refreshDto) {
        TemplateStyleEnum templateStyleEnum = TemplateStyleEnum.getByCode(refreshDto.getTemplateStyle());
        if (!LandingPageTypeEnum.NATIVE.equals(templateStyleEnum.getPageType())
                && !LandingPageTypeEnum.CUSTOM_NATIVE.equals(templateStyleEnum.getPageType())
                && !LandingPageTypeEnum.APPLETS.equals(templateStyleEnum.getPageType())
                && !LandingPageTypeEnum.BUSINESS_TOOL.equals(templateStyleEnum.getPageType())
                && !LandingPageTypeEnum.CONSULT_PAGE.equals(templateStyleEnum.getPageType())) {
            return PageConfig.nullPageConfig();
        }
        Class configClazz = templateStyleEnum.getConfigClazz();
        String configStr = refreshDto.getConfigStr();
        if (Strings.isNullOrEmpty(configStr)) {
            return PageConfig.nullPageConfig();
        }
        List config = Collections.emptyList();
        if (LandingPageTypeEnum.NATIVE.equals(templateStyleEnum.getPageType())) {
            config = JSON.parseArray(configStr, configClazz);
        } else if (LandingPageTypeEnum.CUSTOM_NATIVE.equals(templateStyleEnum.getPageType())) {
            config = Lists.newArrayList(JSON.parseObject(configStr));
        } else if (LandingPageTypeEnum.APPLETS.equals(templateStyleEnum.getPageType())
                || LandingPageTypeEnum.BUSINESS_TOOL.equals(templateStyleEnum.getPageType())) {
            config = Lists.newArrayList(JSON.parseObject(configStr));
        } else if (LandingPageTypeEnum.CONSULT_PAGE.equals(templateStyleEnum.getPageType())) {
            MgkConsultLandingPageExample example = new MgkConsultLandingPageExample();
            example.or().andPageIdIn(Collections.singletonList(refreshDto.getPageId()));
            List<MgkConsultLandingPagePo> pos = mgkConsultLandingPageDao.selectByExample(example);
            if(pos.isEmpty() || pos.size() > 1){
                LOGGER.warn(String.format("[fillConsultPageAttrs] selectByExample ,over 1 MgkConsultLandingPage find,pageId={}",refreshDto.getPageId()));
            }else{
                MgkConsultLandingPagePo po = pos.get(0);
                config = Lists.newArrayList(po2config(po));
            }
        }

        List<Long> formIds = refreshDto.getFormIds();
        List<Object> formDtos = getPreloadForms(refreshDto.getFormIds());
        Integer accountId = refreshDto.getAccountId();
        AccountCustomerNameInfoDto accountCustomerInfo = mgkAccountService.getAccountCustomerNameInfo(accountId);
        AccountCustomerInfoDto accountCustomerLabelInfo = mgkAccountService.getAccountCustomerInfo(accountId);
        String privacyName = accountCustomerInfo.getCustomerName();
        String privacyUrl = landingPageUrlProc.getPrivacyUrlByEnvironment(privacyName);
        Integer wechatPackageId = CollectionUtils.isEmpty(refreshDto.getWechatPackageIds()) ? null : refreshDto.getWechatPackageIds().get(0);
        List<String> formIdStrList = CollectionUtils.isEmpty(formIds) ?
                Collections.emptyList() : formIds.stream().map(Object::toString)
                .collect(Collectors.toList());

        Integer downloadOverLimit = mgkLandingPageConfigJudgeUtil.judgeIsDownloadOverLimit(refreshDto.getDownloadComponentDto(),
                accountId);

        PageImageEnhancementDto enhancementDto = pageImageEnhancementService
                .getImageEnhancementInfo(refreshDto.getPageId());
        return PageConfig
                .builder()
                .isModel(refreshDto.getIsModel())
                .template_style(refreshDto.getTemplateStyle())
                .show_urls(refreshDto.getShowUrls())
                .privacy_name(privacyName)
                .privacy_url(privacyUrl)
                .config(config)
                .form_ids(formIdStrList)
                .form_dtos(formDtos)
                .wechat_package_id(wechatPackageId)
                .wechat_is_manual(wechatIsManual)
                .form_cache_updated(WhetherEnum.YES.getCode())
                .open_whitelist(refreshDto.getAwakenWhiteList())
                .download_whitelist(refreshDto.getDownloadWhiteList())
                .is_download_button_over_limit(downloadOverLimit)
                .download_button_over_limit_hint(mgkDownloadOverLimitHint)
                .form_button_hit(Utils.isPositive(accountCustomerLabelInfo.getHasFormBottomButtonBlackList()) ? 0 : 1)
                .h5_download_hit(accountCustomerLabelInfo.getH5DownloadHit())
                .account_id(accountId)
                .account_label_config(AccountLabelConfig.builder()
                        .is_show_distance_lbs(1)
                        .use_ip_location(accountCustomerLabelInfo.getHasIpLocationKeyList()).build())
                .mini_game_ids(refreshDto.getMiniGameIds())
                .mini_game_cache_bos(refreshDto.getMiniGameCacheBos())
                .customer_acq_link_ids(refreshDto.getCustomerAcquisitionLinkIds())
                .lum(enhancementDto.getLum())
                .grey(enhancementDto.getGrey())
                .color(enhancementDto.getColor())
                .commerce_category_first_id(accountCustomerLabelInfo.getCmCategoryId())
                .commerce_category_second_id(accountCustomerLabelInfo.getCmSecondCategoryId())
                .games(Optional.ofNullable(refreshDto.getGames())
                        .map(gs-> gs.stream().map(g -> GameCache.builder()
                                .game_base_id(g.getGameBaseId())
                                .channel_id(g.getChannelId())
                                .build()).collect(Collectors.toList()))
                        .orElse(null))
                .build();
    }

    public Map<Long, List<Long>> getVideoId2MgkPageIdMap() {
        MgkLandingPageAvidPoExample example = new MgkLandingPageAvidPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkLandingPageAvidPo> mgkPageIdAvIds = mgkLandingPageAvidDao.selectByExample(example);
        return mgkPageIdAvIds.stream().collect(Collectors.groupingBy(MgkLandingPageAvidPo::getAvId, Collectors.mapping(MgkLandingPageAvidPo::getPageId, Collectors.toList())));
    }

    public MgkLandingPageDto getLandingPageDtoByPageId(Long pageId) {
        Assert.notNull(pageId, "页面ID不可为空");
        MgkLandingPagePo po = this.getByPageId(pageId);
        Assert.notNull(po, "该页面不存在");
        return this.convertLandingPagePoToDto(po);
    }

    public List<MgkLandingPageBean> getPageBaseInfoList(List<Long> pageIdList) {
        if (CollectionUtils.isEmpty(pageIdList)) {
            return Collections.emptyList();
        }

        List<MgkLandingPagePo> poList = getInPageIds(pageIdList);
        List<MgkLandingPageBean> resultList = poList.stream().map(po -> {
            return MgkLandingPageBean.builder()
                    .mgkPageId(po.getPageId())
                    .accountId(po.getAccountId())
                    .pageType(po.getType())
                    .templateStyle(po.getTemplateStyle())
                    .pageStatus(po.getStatus())
                    .build();
        }).collect(Collectors.toList());

        fillInfo(poList, resultList);
        return resultList;
    }

    private void fillInfo(List<MgkLandingPagePo> poList, List<MgkLandingPageBean> resultList) {
        if (CollectionUtils.isEmpty(resultList) || CollectionUtils.isEmpty(poList)) {
            return;
        }

        Map<Long, MgkLandingPagePo> poIdMap = poList.stream()
                .collect(Collectors.toMap(MgkLandingPagePo::getPageId, Function.identity()));

        Map<Long, Integer> adVersionControlIdMap = getAdVersionControlIdMapByPagePos(poList);

        resultList.forEach(page -> {
            Long pageId = page.getMgkPageId();
            MgkLandingPagePo po = poIdMap.get(pageId);
            MgkLandingPageDto pageDto = this.convertLandingPagePoToDto(po);
            String launchUrl = landingPageUrlProc.getLaunchUrl(WhetherEnum.NO.getCode(), pageDto, false);
            String launchUrlSecondary = landingPageUrlProc.getLaunchUrl(WhetherEnum.YES.getCode(), pageDto, false);
            Integer adVersionControlId = adVersionControlIdMap.getOrDefault(pageId, 0);
            page.setLaunchUrl(launchUrl);
            page.setLaunchUrlSecondary(launchUrlSecondary);
            page.setAdVersionControllId(adVersionControlId);
            page.setName(pageDto.getName());
        });
    }

    public long getAssociatePageCountByFormId(Long formId) {
        Assert.isTrue(Utils.isPositive(formId), "表单Id不合法");
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsModelIn(Lists.newArrayList(IsModelEnum.PAGE.getCode(), IsModelEnum.COMMENT.getCode()))
                .andStatusNotEqualTo(LandingPageStatusEnum.DELETED.getCode())
                .andFormIdEqualTo(formId);
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsModelEqualTo(IsModelEnum.SHADOW_TEMPLATE.getCode())
                .andStatusEqualTo(LandingPageStatusEnum.WAIT_AUDIT.getCode())
                .andFormIdEqualTo(formId);
        return mgkLandingPageDao.countByExample(example);
    }

    public long getAssociateCustomNativePageCountByFormId(Long formId) {
        Assert.isTrue(Utils.isPositive(formId), "表单Id不合法");
        // 通过 mgk_page_form_mapping 表获取pageIds
        MgkPageFormMappingPoExample mgkPageFormMappingPoExample = new MgkPageFormMappingPoExample();
        mgkPageFormMappingPoExample.or().andFormIdEqualTo(formId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<Long> pageIds = mgkPageFormMappingDao.selectByExample(mgkPageFormMappingPoExample).stream().map(mgkPageFormMappingPo -> mgkPageFormMappingPo.getPageId()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pageIds)) {
            return 0L;
        }

        // 通过 pageIds 查找 mgk_landing_page
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsModelEqualTo(IsModelEnum.PAGE.getCode())
                .andStatusNotEqualTo(LandingPageStatusEnum.DELETED.getCode())
                .andPageIdIn(pageIds);
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsModelEqualTo(IsModelEnum.SHADOW_TEMPLATE.getCode())
                .andStatusEqualTo(LandingPageStatusEnum.WAIT_AUDIT.getCode())
                .andPageIdIn(pageIds);
        return mgkLandingPageDao.countByExample(example);
    }

    @VisibleForTesting
    List<String> getAwakenWhiteListByAccountId(Integer accountId) {
        AccAccountAwakenAppMappingPoExample mappingExample = new AccAccountAwakenAppMappingPoExample();
        mappingExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId);
        List<AccAccountAwakenAppMappingPo> mappingPoList = accAccountAwakenAppMappingDao.selectByExample(mappingExample);
        if (CollectionUtils.isEmpty(mappingPoList)) {
            return Lists.newArrayList("tel");
        }

        ResAwakenAppWhitelistPoExample appExample = new ResAwakenAppWhitelistPoExample();
        appExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(1)
                .andIdIn(mappingPoList.stream().map(AccAccountAwakenAppMappingPo::getAppId)
                        .collect(Collectors.toList()));
        List<ResAwakenAppWhitelistPo> appPoList = resAwakenAppWhitelistDao.selectByExample(appExample);
        if (CollectionUtils.isEmpty(appPoList)) {
            return Lists.newArrayList("tel");
        }

        // ******** 在白名单中增加tel（hard coding），表明是否唤起电话组件

        List<String> awakenWhiteList = appPoList.stream().map(ResAwakenAppWhitelistPo::getScheme)
                .collect(Collectors.toList());
        awakenWhiteList.add("tel");

        return awakenWhiteList;
    }

    @VisibleForTesting
    Map<Integer, List<String>> getAllAwakenWhiteListGroupByAccountId() {
        Map<Integer, List<Integer>> accountAppMapping = new HashMap<>();
        int start = 1;
        int limit = 400;
        AccAccountAwakenAppMappingPoExample mappingExample = new AccAccountAwakenAppMappingPoExample();
        mappingExample.or().andIdGreaterThanOrEqualTo(start).andIdLessThan(start + limit);
        List<AccAccountAwakenAppMappingPo> mappingPoList = accAccountAwakenAppMappingDao.selectByExample(mappingExample);
        Map<Integer, List<Integer>> tempAccountAppMapping;
        //分批处理
        while(!CollectionUtils.isEmpty(mappingPoList)){
            tempAccountAppMapping = mappingPoList.stream()
                    .filter(mappingPo->IsDeleted.VALID.getCode() == mappingPo.getIsDeleted())
                    .collect(Collectors.groupingBy(AccAccountAwakenAppMappingPo::getAccountId,
                            Collectors.mapping(AccAccountAwakenAppMappingPo::getAppId, Collectors.toList())));
            if (!CollectionUtils.isEmpty(tempAccountAppMapping)) {
                tempAccountAppMapping.forEach((accountId, appIds)->{
                    List<Integer> old = accountAppMapping.getOrDefault(accountId, new ArrayList<>());
                    old.addAll(appIds);
                    accountAppMapping.put(accountId, old);
                });
            }

            start += limit;
            mappingExample.clear();
            mappingExample.or().andIdGreaterThanOrEqualTo(start).andIdLessThan(start + limit);
            mappingPoList = accAccountAwakenAppMappingDao.selectByExample(mappingExample);
        }

        ResAwakenAppWhitelistPoExample appExample = new ResAwakenAppWhitelistPoExample();
        appExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(1);
        List<ResAwakenAppWhitelistPo> appPoList = resAwakenAppWhitelistDao.selectByExample(appExample);
        if (CollectionUtils.isEmpty(appPoList)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> appSchemaMap = appPoList.stream()
                .collect(Collectors.toMap(ResAwakenAppWhitelistPo::getId, ResAwakenAppWhitelistPo::getScheme));

        Map<Integer, List<String>> accountSchemaMap = new HashMap<>(accountAppMapping.size());
        accountAppMapping.forEach((accountId, appIdList) -> {
            List<String> schemaList = appIdList.stream().map(appId -> {
                if (appId == null) {
                    return null;
                }
                return appSchemaMap.get(appId);
            }).filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            // 为白名单设置tel(hard coding)
            schemaList.add("tel");
            accountSchemaMap.put(accountId, schemaList);
        });

        return accountSchemaMap;
    }

    @VisibleForTesting
    Map<Long, List<AppPackageBean>> getDownloadWhiteListByPageId(List<Long> pageIdList) {
        if (CollectionUtils.isEmpty(pageIdList)) {
            return Collections.emptyMap();
        }

        MgkLandingPageAppPackagePoExample pageAppExample = new MgkLandingPageAppPackagePoExample();
        pageAppExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(MgkAppPakcageStatusEnum.VALID.getCode())
                .andPageIdIn(pageIdList);
        List<MgkLandingPageAppPackagePo> pageAppPoList = mgkLandingPageAppPackageDao.selectByExample(pageAppExample);
        if (CollectionUtils.isEmpty(pageAppPoList)) {
            return Collections.emptyMap();
        }

        ResAppPackagePoExample appExample = new ResAppPackagePoExample();
        appExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(AppPackageStatus.VALID.getCode())
                .andPlatformStatusEqualTo(AppPackagePlatformStatus.VALID.getCode())
                .andIdIn(pageAppPoList.stream().map(MgkLandingPageAppPackagePo::getAppPackageId)
                        .distinct()
                        .collect(Collectors.toList()));
        List<ResAppPackagePo> appPoList = resAppPackageDao.selectByExample(appExample);
        if (CollectionUtils.isEmpty(appPoList)) {
            return Collections.emptyMap();
        }
        appPoList.forEach(appPo -> appPo.setUrl(appPo.getUrl().trim()));

        Map<Integer, AppPackageBean> appPackageBeanMap = appPoList.stream()
                .collect(Collectors.toMap(ResAppPackagePo::getId, this::convertToAppPackageBean));

        Map<Long, List<Integer>> pageAppMap = pageAppPoList.stream()
                .collect(Collectors.groupingBy(MgkLandingPageAppPackagePo::getPageId,
                        Collectors.mapping(MgkLandingPageAppPackagePo::getAppPackageId, Collectors.toList())));
        Map<Long, List<AppPackageBean>> pagePackageBeanMap = new HashMap<>(pageAppMap.size());
        pageAppMap.forEach((pageId, appIdList) -> {
            List<AppPackageBean> packageBeanList = appIdList.stream().map(appId -> {
                if (appId == null) {
                    return null;
                }
                return appPackageBeanMap.get(appId);
            }).filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            //对下载白名单进行排序，保证安卓在前面，ios在后面
            List<AppPackageBean> androidBean = packageBeanList.stream().filter(appPackageBean -> AppPlatformType.ANDROID.getCode().equals(appPackageBean.getPlatform())).collect(Collectors.toList());
            List<AppPackageBean> notAndroidBean = packageBeanList.stream().filter(appPackageBean -> !AppPlatformType.ANDROID.getCode().equals(appPackageBean.getPlatform())).collect(Collectors.toList());
            androidBean.addAll(notAndroidBean);

            pagePackageBeanMap.put(pageId, androidBean);
        });

        return pagePackageBeanMap;
    }

    private AppPackageBean convertToAppPackageBean(ResAppPackagePo packagePo) {
        if (packagePo == null) {
            return null;
        }

        return AppPackageBean.builder()
                .size(packagePo.getSize())
                .display_name(packagePo.getAppName())
                .apk_name(packagePo.getPackageName())
                .url(packagePo.getUrl())
                .md5(packagePo.getMd5())
                .icon(packagePo.getIconUrl())
                .bili_url(packagePo.getInternalUrl())
                .platform(packagePo.getPlatform())
                .dev_name(packagePo.getDeveloperName())
                .auth_url(packagePo.getAuthorityUrl())
                .version(packagePo.getVersion())
                .update_time(packagePo.getApkUpdateTime() == null ? "未知" : Utils.getTimestamp2String(packagePo.getApkUpdateTime(), YYYY_MM_DD_REGX))
                .auth_name(Strings.isNullOrEmpty(packagePo.getAuthCodeList()) ?
                        "" : Arrays.stream(packagePo.getAuthCodeList().split(","))
                        .limit(AUTH_NAME_MAX_LENGTH)
                        .map(Integer::valueOf)
                        .map(index -> apkAuthCodeMap.getOrDefault(index, "")).collect(Collectors.joining(",")))
                .privacy_url(packagePo.getPrivacyPolicy())
                .build();
    }

    public String getWeChatSign() {
        String accessToken = this.getAccessToken();
        LOGGER.info(String.format("accessToken: %s", accessToken));
        String ticket = this.getJsapiTicket(accessToken);
        LOGGER.info(String.format("ticket: %s", ticket));
        return ticket;
    }

    private String getAccessToken() {
        String accessTokenString = stringRedisTemplate.opsForValue().get(WECHAT_ACCESS_TOKEN_IN_REDIS);
        if (!Strings.isNullOrEmpty(accessTokenString)) {
            return accessTokenString;
        }
        String accessTokenUrl = String.format(this.accessTokenUrl, appId, appSecret);
        AccessTokenDto accessTokenDto = OkHttpUtils.get(accessTokenUrl).callForObject(AccessTokenDto.class);
        stringRedisTemplate.opsForValue().set(WECHAT_ACCESS_TOKEN_IN_REDIS, accessTokenDto.getAccess_token(), accessTokenDto.getExpires_in(), TimeUnit.SECONDS);
        return accessTokenDto.getAccess_token();
    }

    private String getJsapiTicket(String access_token) {
        String jsapiTicketString = stringRedisTemplate.opsForValue().get(WECHAT_JSAPI_TICKET_IN_REDIS);
        if (!Strings.isNullOrEmpty(jsapiTicketString)) {
            return jsapiTicketString;
        }
        String jsapiTicketUrl = String.format(this.jsapiTicketUrl, access_token);
        JsapiTicketDto jsapiTicketDto = OkHttpUtils.get(jsapiTicketUrl).callForObject(JsapiTicketDto.class);
        stringRedisTemplate.opsForValue().set(WECHAT_JSAPI_TICKET_IN_REDIS, jsapiTicketDto.getTicket(), jsapiTicketDto.getExpires_in(), TimeUnit.SECONDS);
        return jsapiTicketDto.getTicket();
    }

    public List<Long> getModelUsedDtos(Operator operator) {
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andAccountIdEqualTo(operator.getOperatorId()).andIsModelEqualTo(IsModelEnum.PAGE.getCode());
        List<MgkLandingPagePo> mgkLandingPagePos = mgkLandingPageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(mgkLandingPagePos)) {
            return Collections.emptyList();
        }
        return mgkLandingPagePos.stream().map(MgkLandingPagePo::getModelId).distinct().collect(Collectors.toList());
    }

    public List<MgkLandingPageDto> getLandingPageDtoByPageIds(List<Long> pageIds) {

        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }

        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        MgkLandingPagePoExample.Criteria c = example.or();
        if (pageIds.size() == 1) {
            c.andPageIdEqualTo(pageIds.get(0));
        } else {
            c.andPageIdIn(pageIds);
        }
        return this.convertLandingPagePosToDtos(mgkLandingPageDao.selectByExample(example));
    }


    public Integer getAdVersionControlIdByPageId(Long pageId) {
        if (Strings.isNullOrEmpty(mgkAdVersionControl)) {
            return 0;
        }
        String[] versionControl = mgkAdVersionControl.split(";");
        Map<String, Integer> adVersionMap = Arrays.stream(versionControl).map(v -> {
            return v.split(",");
        }).collect(Collectors.toMap(o -> o[0], o -> Integer.valueOf(o[1])));
        if (!Utils.isPositive(pageId)) {
            return 0;
        }
        List<MgkLandingPagePo> landingPagePos = this.getLandingPagePoByPageId(Lists.newArrayList(pageId));
        if (CollectionUtils.isEmpty(landingPagePos)) {
            return 0;
        }
        MgkLandingPagePo landingPagePo = landingPagePos.get(0);

        if (TemplateStyleEnum.CUSTOM_NATIVE.getCode().equals(landingPagePo.getTemplateStyle())
                || TemplateStyleEnum.HALF_SCREEN_VIDEO_IOS.getCode().equals(landingPagePo.getTemplateStyle())
                || TemplateStyleEnum.HALF_SCREEN_VIDEO_ANDROID.getCode().equals(landingPagePo.getTemplateStyle())
                || TemplateStyleEnum.FULL_SCREEN_OPEN_SCREEN.getCode().equals(landingPagePo.getTemplateStyle())
                || TemplateStyleEnum.APPLETS.getCode().equals(landingPagePo.getTemplateStyle())
                || TemplateStyleEnum.BUSINESS_TOOL.getCode().equals(landingPagePo.getTemplateStyle())
                || TemplateStyleEnum.CONSULT_PAGE.getCode().equals(landingPagePo.getTemplateStyle())) {
            return adVersionMap.getOrDefault(landingPagePo.getPageVersion(), 0);
        }

        return TemplateStyleEnum.getByCode(landingPagePo.getTemplateStyle()).getAdVersionControllId();
    }

    private Map<Long, Integer> getAdVersionControlIdMapByPagePos(List<MgkLandingPagePo> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyMap();
        }

        String[] versionControl = mgkAdVersionControl.split(";");
        Map<String, Integer> adVersionMap = Arrays.stream(versionControl).map(v -> {
            return v.split(",");
        }).collect(Collectors.toMap(o -> o[0], o -> Integer.valueOf(o[1])));

        Map<Long, Integer> resultMap = new HashMap<>();
        poList.forEach(po -> {
            Long pageId = po.getPageId();
            if (Strings.isNullOrEmpty(mgkAdVersionControl)) {
                resultMap.put(pageId, 0);
            }
            if (!Utils.isPositive(pageId)) {
                return;
            }
            if (TemplateStyleEnum.CUSTOM_NATIVE.getCode().equals(po.getTemplateStyle())
                    || TemplateStyleEnum.HALF_SCREEN_VIDEO_IOS.getCode().equals(po.getTemplateStyle())
                    || TemplateStyleEnum.HALF_SCREEN_VIDEO_ANDROID.getCode().equals(po.getTemplateStyle())
                    || TemplateStyleEnum.FULL_SCREEN_OPEN_SCREEN.getCode().equals(po.getTemplateStyle())
                    || TemplateStyleEnum.FULL_SCREEN_OPEN_DOUBLE_JUMP_VIDEO.getCode().equals(po.getTemplateStyle())
                    || TemplateStyleEnum.APPLETS.getCode().equals(po.getTemplateStyle())
                    || TemplateStyleEnum.BUSINESS_TOOL.getCode().equals(po.getTemplateStyle())) {
                Integer adVersionControlId = adVersionMap.getOrDefault(po.getPageVersion(), 0);
                resultMap.put(pageId, adVersionControlId);
                return;
            }

            Integer adVersionControlId = TemplateStyleEnum.getByCode(po.getTemplateStyle()).getAdVersionControllId();
            resultMap.put(pageId, adVersionControlId);
        });

        return resultMap;
    }

    private List<MgkLandingPagePo> getLandingPagePoByPageId(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyList();
        }
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        MgkLandingPagePoExample.Criteria c = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (pageIds.size() == 1) {
            c.andPageIdEqualTo(pageIds.get(0));
        } else {
            c.andPageIdIn(pageIds);
        }
        return mgkLandingPageDao.selectByExample(example);
    }

    public String getNativeLaunchUrlByPageId(Long pageId) {
        List<MgkLandingPagePo> pos = this.getLandingPagePoByPageId(Lists.newArrayList(pageId));
        if (CollectionUtils.isEmpty(pos)) {
            return "";
        }
        Integer templateStyle = pos.get(0).getTemplateStyle();
        if (!TemplateStyleEnum.CUSTOM_NATIVE.getCode().equals(templateStyle) &&
                !TemplateStyleEnum.HALF_SCREEN_VIDEO_IOS.getCode().equals(templateStyle) &&
                !TemplateStyleEnum.HALF_SCREEN_VIDEO_ANDROID.getCode().equals(templateStyle)) {
            return "";
        }

        if (TemplateStyleEnum.HALF_SCREEN_VIDEO_IOS.getCode().equals(templateStyle)) {
            return String.format(NATIVE_APP_STORE_LANDING_PAGE_WHITE, String.valueOf(pageId), templateStyle);
        }

        return String.format(NATIVE_LANDING_PAGE_WHITE, String.valueOf(pageId), templateStyle);
    }

    public List<AppInfoDto> getAppInfo(List<Integer> appIds) {
        List<ResAppPackagePo> pos = getResAppPackagePo(appIds);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return convertResAppPackagePos2Dtos(pos);
    }

    private List<AppInfoDto> convertResAppPackagePos2Dtos(List<ResAppPackagePo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertResAppPackagePo2Dto).collect(Collectors.toList());
    }

    private AppInfoDto convertResAppPackagePo2Dto(ResAppPackagePo po) {
        return AppInfoDto.builder()
                .authList(Strings.isNullOrEmpty(po.getAuthCodeList()) ? Collections.emptyList() : Arrays.stream(po.getAuthCodeList().split(",")).map(Integer::valueOf)
                        .map(index -> apkAuthCodeMap.getOrDefault(index, "")).collect(Collectors.toList()))
                .devName(po.getDeveloperName())
                .packageName(po.getPackageName())
                .version(po.getVersion())
                .apkUpdateTime(po.getApkUpdateTime())
                .privacyPolicy(po.getPrivacyPolicy())
                .recordNumber(po.getRecordNumber())
                .build();

    }

    public List<ResAppPackagePo> getResAppPackagePo(List<Integer> appIds) {
        ResAppPackagePoExample example = new ResAppPackagePoExample();
        ResAppPackagePoExample.Criteria or = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (appIds.size() == 1) {
            or.andIdEqualTo(appIds.get(0));
        } else {
            or.andIdIn(appIds);
        }
        List<ResAppPackagePo> pos = resAppPackageDao.selectByExample(example);
        pos.forEach(po -> po.setUrl(po.getUrl().trim()));
        return pos;
    }

    public List<MgkLandingPageDto> getValidLandingPageByIds(List<Long> pageIdList) {
        if (CollectionUtils.isEmpty(pageIdList)) {
            return Collections.emptyList();
        }

        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(pageIdList);
        return convertLandingPagePosToDtos(mgkLandingPageDao.selectByExample(example));
    }

    public LandingPageConfigDto transPage(Integer accountId, String pageId, Integer strict) throws ServiceException {
        String requestUrl = String.format(MGK_TRANS_PAGE_URL, pageId, strict);
        LOGGER.info("request transPage url: " + requestUrl);
        JSONObject result = OkHttpUtils.get(requestUrl).callForObject(JSONObject.class);
        String status = result.getString("status");
        if ("error".equals(status)) {
            throw new ServiceException(500, result.getString("error_msg"));
        }
        String jsonString = result.getJSONObject("result").toJSONString();
        return JSONObject.parseObject(jsonString, LandingPageConfigDto.class);
    }

    public List<MgkLandingPageDto> getPageLimit(int start, int limit) {
        Assert.isTrue(limit < 500, "一次最多只能查找500条记录");
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                  .andIdGreaterThanOrEqualTo(start).andIdLessThan(start + limit);
//                .andIsFormScrollEqualTo(WhetherEnum.YES.getCode());
        List<MgkLandingPagePo> landingPagePos = mgkLandingPageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(landingPagePos)) {
            return Collections.emptyList();
        }
        return convertLandingPagePosToDtos(mgkLandingPageDao.selectByExample(example));
    }

    public List<Long> getFormIdsByPageIds(List<Long> pageIds) {
        MgkPageFormMappingPoExample example = new MgkPageFormMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andPageIdIn(pageIds);
        List<MgkPageFormMappingPo> pos = mgkPageFormMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(MgkPageFormMappingPo::getFormId).distinct().collect(Collectors.toList());
    }

    public PageResult<MgkHotLandingPageDto> getHotLandingPageList(QueryHotPageListDto queryHotPageListDto) {

        List<MgkHotLandingPageDto> mgkHotLandingPageDtos = getHotLandingPageListFromCK(queryHotPageListDto);
        // 开始分页
        int pageSize = queryHotPageListDto.getPage().getPageSize();
        int page = queryHotPageListDto.getPage().getPage();
        List<MgkHotLandingPageDto> pageResult = mgkHotLandingPageDtos.stream().skip((long) pageSize * (page - 1)).limit(pageSize).collect(Collectors.toList());
        return PageResult.<MgkHotLandingPageDto>builder().total(mgkHotLandingPageDtos.size()).records(pageResult).build();
    }

    /**
     * 1 从es中取数据
     * 2 聚合算出ctr的值
     * 3 过滤出线上的落地页
     * 4 分页返回
     *
     * @param queryHotPageListDto
     * @return
     */
    private List<MgkHotLandingPageDto> getHotLandingPageListFromCK(QueryHotPageListDto queryHotPageListDto) {

        List<ESMgkHotLandingPageDto> pos = pageEsService.getHotLandingPagePosFromCK(queryHotPageListDto);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        // 按照pageId聚合
        Map<String, List<ESMgkHotLandingPageDto>> hotPageGroup = pos.stream().collect(Collectors.groupingBy(ESMgkHotLandingPageDto::getPage_id));
        List<MgkHotLandingPageDto> hotLandingPageDtos = hotPageGroup.keySet().stream().map(pageId -> {
            List<ESMgkHotLandingPageDto> esMgkHotLandingPagePos = hotPageGroup.get(pageId);
            long click = esMgkHotLandingPagePos.stream().mapToLong(ESMgkHotLandingPageDto::getClick).sum();
            long convCnt = esMgkHotLandingPagePos.stream().mapToLong(ESMgkHotLandingPageDto::getConv_cnt).sum();
            List<String> ctrRanks = esMgkHotLandingPagePos.stream().map(ESMgkHotLandingPageDto::getCvr_rank)
                    .sorted(Comparator.comparingInt(o -> MgkHotPageCtrRankEnum.getByValue(o).getCode())).collect(Collectors.toList());
            return MgkHotLandingPageDto.builder()
                    .pageId(Long.parseLong(pageId))
                    .ctr(Utils.isPositive(click) ? convCnt * 100 / click : 0L)
                    .ctrRank(CollectionUtils.isEmpty(ctrRanks) ? "未知" : ctrRanks.get(0))
                    .ctrRankDesc(CollectionUtils.isEmpty(ctrRanks) ? "未知" : MgkHotPageCtrRankEnum.getByValue(ctrRanks.get(0)).getDesc())
                    .hotPageCategory(CollectionUtils.isEmpty(esMgkHotLandingPagePos) ? "未知" : esMgkHotLandingPagePos.get(0).getCommerce_category_first_name())
                    .build();
        }).collect(Collectors.toList());

        Map<Long, MgkHotLandingPageDto> esHotPageMap = hotLandingPageDtos.stream().collect(Collectors.toMap(MgkHotLandingPageDto::getPageId, Function.identity()));
        // 获取落地页信息
        List<Long> pageIds = hotLandingPageDtos.stream().map(MgkHotLandingPageDto::getPageId).collect(Collectors.toList());
        // 筛选出已经发布的落地页
        List<MgkLandingPageDto> landingPageDtos = getLandingPageDtos(QueryLandingPageParamDto.builder().pageIdList(pageIds)
                .statusList(Lists.newArrayList(LandingPageStatusEnum.PUBLISHED.getCode()))
                .typeList(Lists.newArrayList(LandingPageTypeEnum.APPLETS.getCode()))
                .build());
        if (CollectionUtils.isEmpty(landingPageDtos)) {
            return Collections.emptyList();
        }

        // 账户去重
        landingPageDtos = landingPageDtos.stream().filter(distinctByField(MgkLandingPageDto::getAccountId)).collect(Collectors.toList());

        return landingPageDtos.stream().map(mgkLandingPageDto -> {
            MgkHotLandingPageDto mgkHotLandingPageDto = esHotPageMap.get(mgkLandingPageDto.getPageId());
            return MgkHotLandingPageDto.builder()
                    .pageId(mgkLandingPageDto.getPageId())
                    .name(mgkLandingPageDto.getName())
                    .title(mgkLandingPageDto.getTitle())
                    .pageCover(mgkLandingPageDto.getPageCover())
                    .ctr(mgkHotLandingPageDto.getCtr())
                    .ctrRank(mgkHotLandingPageDto.getCtrRank())
                    .ctrRankDesc(mgkHotLandingPageDto.getCtrRankDesc())
                    .hotPageUrl(landingPageUrlProc.getHotPageUrl(mgkLandingPageDto.getPageId(),
                            mgkLandingPageDto.getType(), mgkLandingPageDto.getHeader()))
                    .hotPageCategory(mgkHotLandingPageDto.getHotPageCategory())
                    .build();
        }).sorted(Comparator.comparingLong(MgkHotLandingPageDto::getCtr).reversed()).collect(Collectors.toList());
    }


    static <T> Predicate<T> distinctByField(Function<? super T, ?> fieldExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(fieldExtractor.apply(t), Boolean.TRUE) == null;
    }

    public List<String> getHotLandingPagesCategories() {
        String categoryString = stringRedisTemplate.opsForValue().get(MgkConstants.MGK_HOT_PAGE_CATEGORY_REDIS_KEY);
        if (!Strings.isNullOrEmpty(categoryString) && enableRedisCache) {
            return JSONArray.parseArray(categoryString, String.class);
        }
        return pageEsService.getHotLandingPagesCategoriesFromCK();
    }


    /**
     * 根据pageId获取热门落地页详情
     *
     * @param queryHotPageListDto
     * @return
     */
    public List<MgkHotLandingPageDto> getHotLandingPageDetail(QueryHotPageListDto queryHotPageListDto) {

        List<MgkHotLandingPageDto> hotLandingPageList = getHotLandingPageListFromCK(queryHotPageListDto);

        if (CollectionUtils.isEmpty(hotLandingPageList)) {
            return Collections.emptyList();
        }
        return hotLandingPageList;
    }

    public void refreshArchiveLandingPageByAccountIds(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        accountIds.forEach(this::refreshArchiveLandingPageByAccountId);
    }

    private void refreshArchiveLandingPageByAccountId(Integer accountId) {
        // 筛选出该账户落地页
        List<MgkLandingPageDto> landingPageDtos = getLandingPageDtos(QueryLandingPageParamDto.builder()
                .accountIdList(Lists.newArrayList(accountId))
                .templateStyleList(
                        Lists.newArrayList(TemplateStyleEnum.FULL_SCREEN_VIDEO.getCode(),
                                TemplateStyleEnum.HALF_SCREEN_VIDEO.getCode(),
                                TemplateStyleEnum.HALF_SCREEN_VIDEO_IOS.getCode(),
                                TemplateStyleEnum.HALF_SCREEN_VIDEO_ANDROID.getCode(),
                                TemplateStyleEnum.VERTICAL_FULL_SCREEN_VIDEO_H5.getCode(),
                                TemplateStyleEnum.APPLETS.getCode()))
                .isVideoPages(Lists.newArrayList(WhetherEnum.YES.getCode()))
                .build());
        // 刷小程序
        refreshArchiveAppletsLandingPages(landingPageDtos.stream().filter(dto ->
                TemplateStyleEnum.APPLETS.getCode().equals(dto.getTemplateStyle()))
                .map(MgkLandingPageDto::getPageId).collect(Collectors.toList()));
        // 刷原生
        refreshArchiveNativeLandingPages(landingPageDtos.stream().filter(dto -> !TemplateStyleEnum.APPLETS.getCode()
                .equals(dto.getTemplateStyle())).map(MgkLandingPageDto::getPageId).collect(Collectors.toList()));
    }

    /**
     * 刷原生
     *
     * @param pageIds
     */
    private void refreshArchiveNativeLandingPages(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return;
        }
        pageIds.forEach(this::refreshArchiveNativeLandingPage);
    }

    private void refreshArchiveAppletsLandingPages(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return;
        }
        pageIds.forEach(this::refreshArchiveAppletsLandingPage);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    @Synchronized
    public void refreshArchiveAppletsLandingPage(Long pageId) {
        LandingPageConfigDto configDto = getLandingPageConfigDtoByPageId(pageId);
        if (configDto == null || Strings.isNullOrEmpty(configDto.getConfig())) {
            return;
        }
        // todo 记录
        String oldConfig = new String(configDto.getConfig());


        String newConfigString = refreshArchiveAppletsPageConfig(configDto.getAccountId(), pageId, configDto.getConfig());

        if (Strings.isNullOrEmpty(newConfigString) || newConfigString.equals(oldConfig)) {
            return;
        }

        saveOldConfig(configDto.getAccountId(), pageId, oldConfig);

        // 更新config
        updateMgkLandingPageConfig(newConfigString, pageId);
        // 更新 mongo
        mgkMongoLandingPageService.saveLandingPage(MongoLandingPageDto.builder().pageId(pageId).accountId(configDto.getAccountId()).config(JSON.parse(newConfigString)).build());
    }

    private void saveOldConfig(Integer accountId, Long pageId, String oldConfig) {
        MgkLandingPageArchiveRefreshLogPo po = MgkLandingPageArchiveRefreshLogPo.builder()
                .accountId(accountId)
                .pageId(pageId)
                .config(oldConfig)
                .build();
        landingPageArchiveRefreshLogDao.insertSelective(po);
    }

    private String refreshArchiveAppletsPageConfig(Integer accountId, Long pageId, String config) {
        JSONObject configObject = JSON.parseObject(config);
        JSONArray blocks = configObject.getJSONArray("blocks");
        blocks.forEach(block -> {
            JSONObject blockObject = (JSONObject) block;
            JSONArray components = blockObject.getJSONArray("components");
            components.forEach(component -> {
                JSONObject componentObject = (JSONObject) component;
                String componentName = componentObject.getString("name");
                if (COMPONENT_VIDEO_NAME_LIST.contains(componentName)) {
                    JSONObject data = (JSONObject) componentObject.get("data");
                    JSONObject content = (JSONObject) data.get("content");
                    JSONObject video = (JSONObject) content.get("video");
                    Integer videoType = video.getInteger("videoType");
                    // 视频云视频
                    if (AppletsComponentVideoTypeEnum.VIDEO.getCode().equals(videoType)) {
                        JSONObject videoCloudSource = (JSONObject) video.get("videoCloudSource");
                        JSONObject manuscriptVideo = (JSONObject) video.get("manuscriptVideo");
                        Integer bizId = videoCloudSource.getInteger("biz_id");

                        // 通过biz_id获取稿件id
                        // 通过avid获取稿件信息
                        List<MgkCmArchiveDto> mgkCmArchiveDtos = cmArchiveService.getArchiveDtosByBizId(accountId, bizId);
                        if (CollectionUtils.isEmpty(mgkCmArchiveDtos) || !Utils.isPositive(mgkCmArchiveDtos.get(0).getAvid())) {
                            return;
                        }
                        // 更新config中的数据
                        MgkCmArchiveDto archiveDto = mgkCmArchiveDtos.get(0);
                        manuscriptVideo.put("cover", archiveDto.getCoverUrl());
                        manuscriptVideo.put("aid", archiveDto.getAvid());
                        manuscriptVideo.put("cid", archiveDto.getCid());
                        manuscriptVideo.put("duration", archiveDto.getDurationInMs());
                        manuscriptVideo.put("width", archiveDto.getWidth());
                        manuscriptVideo.put("height", archiveDto.getHeight());
                        manuscriptVideo.put("size", archiveDto.getSizeKb());
                        manuscriptVideo.put("name", archiveDto.getRawName());

                        // 更新videoType
                        video.put("videoType", 1);


                        // 不更新关系 即保留之前的映射关系 咨询产品
                        // 记录bizId修改
//                            mgkPageBizMappingRefreshLogService.insert(pageId, bizId);
                        // 更新视频的映射关系
                        refreshArchiveBizAvidMapping(pageId, bizId, archiveDto.getAvid());

                        // bizId -> avid
                        cmArchiveService.refreshBizIdToAvidMappingInRedis(bizId);
                        // avid -> bizIds
                        cmArchiveService.refreshAvidToBizIdsMappingInRedis(archiveDto.getAvid());
                        // pageId -> avid
                        mgkLandingPageAvidService.refreshPageIdToAvidMappingInRedis(pageId, archiveDto.getAvid());
                    }
                }
            });
        });
        return JSON.toJSONString(configObject, SerializerFeature.WriteMapNullValue);
    }

    private void refreshArchiveBizAvidMapping(Long pageId, Integer bizId, Long avid) {
        mgkLandingPageAvidService.insertOrUpdate(pageId, avid);
    }

    public void refreshArchiveLandingPageByPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return;
        }
        pageIds.forEach(this::refreshArchiveLandingPageByPageId);
    }

    private void refreshArchiveLandingPageByPageId(Long pageId) {
        MgkLandingPageDto dto = getLandingPageDtoByPageId(pageId);
        if (Objects.isNull(dto)) {
            return;
        }

        // 如果是小程序落地页
        if (TemplateStyleEnum.APPLETS.getCode().equals(dto.getTemplateStyle())) {
            refreshArchiveAppletsLandingPage(pageId);
            return;
        }

        List<Integer> templateStyles = Lists.newArrayList(TemplateStyleEnum.FULL_SCREEN_VIDEO.getCode(),
                TemplateStyleEnum.HALF_SCREEN_VIDEO.getCode(),
                TemplateStyleEnum.HALF_SCREEN_VIDEO_IOS.getCode(),
                TemplateStyleEnum.HALF_SCREEN_VIDEO_ANDROID.getCode(),
                TemplateStyleEnum.VERTICAL_FULL_SCREEN_VIDEO_H5.getCode());

        // 203 204 205 206 207 原生落地页
        if (templateStyles.contains(dto.getTemplateStyle())) {
            refreshArchiveNativeLandingPage(pageId);
        }
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void refreshArchiveNativeLandingPage(Long pageId) {
        LandingPageConfigDto configDto = getLandingPageConfigDtoByPageId(pageId);
        if (configDto == null || Strings.isNullOrEmpty(configDto.getConfig())) {
            return;
        }

        // ToDo 更新数据之前记录
        String oldConfig = new String(configDto.getConfig());

        List nativeConfigs = JSON.parseArray(configDto.getConfig(), TemplateStyleEnum.getByCode(configDto.getTemplateStyle()).getConfigClazz());

        String newConfigString = refreshArchiveNativePageConfig(configDto.getAccountId(), pageId, nativeConfigs);

        if (Strings.isNullOrEmpty(newConfigString) || oldConfig.equals(newConfigString)) {
            return;
        }
        saveOldConfig(configDto.getAccountId(), pageId, oldConfig);

        // 更新config
        updateMgkLandingPageConfig(newConfigString, pageId);
        // 更新 mongo
        mgkMongoLandingPageService.saveLandingPage(MongoLandingPageDto.builder().pageId(pageId).accountId(configDto.getAccountId()).config(JSON.parse(newConfigString)).build());
    }

    private String refreshArchiveNativePageConfig(Integer accountId, Long pageId, List nativeConfigs) {
        nativeConfigs.forEach(nativeConfig -> {
            if (nativeConfig instanceof NativeVideoConfig) {
                NativeVideoConfig nativeVideoConfig = (NativeVideoConfig) nativeConfig;
                refreshArchiveNativePageVideo(accountId, pageId, nativeVideoConfig.getVideo());
            } else if (nativeConfig instanceof NativeGameVideoConfig) {
                NativeGameVideoConfig nativeGameVideoConfig = (NativeGameVideoConfig) nativeConfig;
                refreshArchiveNativePageVideo(accountId, pageId, nativeGameVideoConfig.getVideo());
            }
        });
        return JSON.toJSONString(nativeConfigs, SerializerFeature.WriteMapNullValue);
    }

    private void refreshArchiveNativePageVideo(Integer accountId, Long pageId, Video video) {
        if (Objects.isNull(video)) return;

        // 获取结构中的bizId信息
        Integer bizId = Integer.valueOf(video.getBiz_id());
        if (!Utils.isPositive(bizId)) return;

        // 通过biz_id获取avid
        List<MgkCmArchiveDto> mgkCmArchiveDtos = cmArchiveService.getArchiveDtosByBizId(accountId, bizId);

        if (CollectionUtils.isEmpty(mgkCmArchiveDtos) || !Utils.isPositive(mgkCmArchiveDtos.get(0).getAvid())) {
            return;
        }

        // 设置video信息
        MgkCmArchiveDto archiveDto = mgkCmArchiveDtos.get(0);
        video.setUrl("");
        video.setBiz_id(String.valueOf(archiveDto.getCid()));
        video.setAvid(String.valueOf(archiveDto.getAvid().intValue()));
        video.setCid(String.valueOf(archiveDto.getCid().intValue()));

        // 不更新数据  咨询产品
        // 记录bizId修改
//        mgkPageBizMappingRefreshLogService.insert(pageId, bizId);
        //更新视频的映射关系
        refreshArchiveBizAvidMapping(pageId, bizId, archiveDto.getAvid());

        // bizId -> avid
        cmArchiveService.refreshBizIdToAvidMappingInRedis(bizId);
        // avid -> bizIds
        cmArchiveService.refreshAvidToBizIdsMappingInRedis(archiveDto.getAvid());
        // pageId -> avid
        mgkLandingPageAvidService.refreshPageIdToAvidMappingInRedis(pageId, archiveDto.getAvid());
    }

    public void rollbackArchiveLandingPageByAccountIds(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        accountIds.forEach(this::rollbackArchiveLandingPageByAccountId);
    }

    private void rollbackArchiveLandingPageByAccountId(Integer accountId) {
        MgkLandingPageArchiveRefreshLogPoExample example = new MgkLandingPageArchiveRefreshLogPoExample();
        MgkLandingPageArchiveRefreshLogPoExample.Criteria or = example.or();
        or.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdEqualTo(accountId);
        List<MgkLandingPageArchiveRefreshLogPo> pos = landingPageArchiveRefreshLogDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }
        List<Long> pageIds = pos.stream().map(MgkLandingPageArchiveRefreshLogPo::getPageId).collect(Collectors.toList());
        this.rollbackArchiveLandingPageByPageIds(pageIds);
    }

    public void rollbackArchiveLandingPageByPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return;
        }
        pageIds.forEach(this::rollbackArchiveLandingPageByPageId);
        pageIds.forEach(this::rollbackArchiveBizAvidMapping);
    }

    private void rollbackArchiveBizAvidMapping(Long pageId) {
        mgkLandingPageAvidService.deletedByPageId(pageId);
        mgkLandingPageAvidService.deletedPageIdToAvidMappingInRedis(pageId);
    }

    private void rollbackArchiveLandingPageByPageId(Long pageId) {
        // 更新config
        List<MgkLandingPageArchiveRefreshLogDto> logDtos = mgkLandingPageArchiveRefreshLogService.getDtosByPageIds(Lists.newArrayList(pageId));

        if (CollectionUtils.isEmpty(logDtos)) {
            return;
        }

        // 删除记录的日志数据
        mgkLandingPageArchiveRefreshLogService.deletedByPageId(pageId);


        MgkLandingPageArchiveRefreshLogDto dto = logDtos.get(0);
        updateMgkLandingPageConfig(dto.getConfig(), pageId);
        mgkMongoLandingPageService.saveLandingPage(MongoLandingPageDto.builder().pageId(pageId).accountId(dto.getAccountId()).config(JSON.parse(dto.getConfig())).build());
    }


    /**
     * 模板落地页
     * 根据 落地页 pageId, 游戏中心 gameId, Ios应用包packageId, 外链地址 url
     * <p>
     * https://www.tapd.bilibili.co/********/documents/view/11********001007783?file_type=mindmap
     * https://www.tapd.bilibili.co/********/prong/stories/view/11********002464281
     *
     * @return
     */
    public TemplatePageDto getMgkTemplatePage(Operator operator, Long pageId, Integer gameBaseId, Integer packageId, String url) {


        Long templatePageId = getTemplatePage(operator, pageId, gameBaseId, packageId, url);
        if (!Utils.isPositive(templatePageId)) {
            return initTemplatePageDto;
        }

        // 获取发布地址和降级地址
        MgkLandingPageDto landingPageDto = getLandingPageDtoByPageId(templatePageId);
        Assert.isTrue(LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageDto.getStatus()), "非发布状态的页面不可投放");
        String launchUrl = landingPageUrlProc.getLaunchUrl(WhetherEnum.NO.getCode(), landingPageDto, true);
        String launchUrlSecondary = landingPageUrlProc.getLaunchUrl(WhetherEnum.YES.getCode(), landingPageDto, true);
        return TemplatePageDto.builder()
                .mgkPageId(templatePageId)
                .launchUrl(launchUrl)
                .launchUrlSecondary(launchUrlSecondary)
                .build();
    }

    public List<SanlianMgkTemplatePageDto> getMgkTemplatePageList(QuerySanlianTemplatePageDto queryDto) {
        Operator operator = queryDto.getOperator();
        List<Long> pageIdList = queryDto.getPageIdList();
        List<Integer> gameBaseIdList = queryDto.getGameBaseIdList();
        List<Integer> appPackageIdList = queryDto.getAppPackageIdList();
        List<String> urlList = queryDto.getUrlList();

        List<SanlianMgkTemplatePageDto> pageIdTemplatePageList = getPageIdTemplatePageList(pageIdList);
        List<SanlianMgkTemplatePageDto> gameBaseIdTemplatePageList = getGameBaseIdTemplatePageList(operator, gameBaseIdList);
        List<SanlianMgkTemplatePageDto> appPackageIdTemplatePageList = getAppPackageIdTemplatePageList(operator, appPackageIdList);
        List<SanlianMgkTemplatePageDto> urlTemplatePageList = getUrlTemplatePageList(operator, urlList);

        List<SanlianMgkTemplatePageDto> templatePageDtoList = Stream.of(pageIdTemplatePageList, gameBaseIdTemplatePageList, appPackageIdTemplatePageList, urlTemplatePageList)
                .filter(templatePageList -> !CollectionUtils.isEmpty(templatePageList))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<Long> templatePageIds = templatePageDtoList.stream()
                .map(SanlianMgkTemplatePageDto::getTemplatePageId)
                .collect(Collectors.toList());

        List<MgkLandingPageBean> pageBaseInfoList = getPageBaseInfoList(templatePageIds);
        Map<Long, MgkLandingPageBean> pageBeanMap = pageBaseInfoList.stream()
                .collect(Collectors.toMap(MgkLandingPageBean::getMgkPageId, Function.identity()));
        templatePageDtoList.forEach(templatePageDto -> {
            Long templatePageId = templatePageDto.getTemplatePageId();
            MgkLandingPageBean pageBean = pageBeanMap.get(templatePageId);
            if (Objects.isNull(pageBean)) {
                return;
            }
            templatePageDto.setPageStatus(pageBean.getPageStatus());
            templatePageDto.setLaunchUrl(pageBean.getLaunchUrl());
            templatePageDto.setLaunchUrlSecondary(pageBean.getLaunchUrlSecondary());
            templatePageDto.setTemplateStyle(isAppletTemplate(pageBean) ? TemplateStyleEnum.APPLETS.getCode() : pageBean.getTemplateStyle());
            templatePageDto.setAdVersionControlId(pageBean.getAdVersionControllId());
        });

        return templatePageDtoList.stream()
                .filter(templatePageDto ->
                        LandingPageStatusEnum.PUBLISHED.getCode().equals(templatePageDto.getPageStatus()))
                .collect(Collectors.toList());
    }

    private boolean isAppletTemplate(MgkLandingPageBean pageBean){
        if(pageBean.getTemplateStyle() != TemplateStyleEnum.HALF_SCREEN_VIDEO.getCode() ){
            return false;
        }else{
            if(pageBean.getName() != null && !pageBean.getName().isEmpty()){
                return true;
            }
            return false;
        }
    }


    public Map<Long, MgkLandingPageDto> getMgkAppletTemplatePageMapByPageIds(List<Long> pageIds) {
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyMap();
        }
        QueryTemplatePageDto queryDto = QueryTemplatePageDto.builder()
                .pageIds(pageIds)
                .build();
        List<MgkTemplatePageMappingPo> templatePageMappingPos = getTemplatePageMappingPos(queryDto);
        if (CollectionUtils.isEmpty(templatePageMappingPos)) {
            return Collections.emptyMap();
        }
        Map<Long, Long> templatePageIdMap = templatePageMappingPos.stream()
                .collect(Collectors.toMap(MgkTemplatePageMappingPo::getPageId,
                        MgkTemplatePageMappingPo::getTemplatePageId));
        List<Long> templatePageIds = new ArrayList<>(templatePageIdMap.values());
        List<MgkLandingPagePo> pos = this.getPublishedMgkPagePos(templatePageIds,
                IsModelEnum.TEMPLATE.getCode(),
                TemplateStyleEnum.APPLETS.getCode());
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        Map<Long, MgkLandingPageDto> templatePageDtoIdMap = pos.stream()
                .map(this::convertLandingPagePoToDto)
                .collect(Collectors.toMap(MgkLandingPageDto::getPageId, Function.identity()));

        Set<Long> publishedTemplatePageSet = pos.stream()
                .map(MgkLandingPagePo::getPageId)
                .collect(Collectors.toSet());
        return templatePageIdMap.entrySet().stream()
                .filter(entry -> publishedTemplatePageSet.contains(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> templatePageDtoIdMap.get(entry.getValue())));
    }

    public List<TemplatePageDto> getMgkOnlineTemplatePage(List<Long> templatePageIds) {
        if (CollectionUtils.isEmpty(templatePageIds)) {
            return Collections.emptyList();
        }
        List<MgkLandingPagePo> pos = this.getPublishedMgkPagePos(templatePageIds, IsModelEnum.TEMPLATE.getCode(), null);
        List<MgkLandingPageDto> pageDtos = this.convertLandingPagePosToDtos(pos);
        return pageDtos.stream().map(landingPageDto -> {
            String launchUrl = landingPageUrlProc.getLaunchUrl(WhetherEnum.NO.getCode(), landingPageDto, true);
            String launchUrlSecondary = landingPageUrlProc.getLaunchUrl(WhetherEnum.YES.getCode(), landingPageDto, true);
            return TemplatePageDto.builder()
                    .mgkPageId(landingPageDto.getPageId())
                    .launchUrl(launchUrl)
                    .launchUrlSecondary(launchUrlSecondary)
                    .build();
        }).collect(Collectors.toList());
    }

    private Long getTemplatePage(Operator operator, Long pageId, Integer gameBaseId, Integer packageId, String url) {

        // 先从数据库中获取
        Long templatePageId = getTemplatePage(pageId, gameBaseId, packageId, url);

        if (Utils.isPositive(templatePageId)) {
            List<MgkLandingPagePo> existTemplatePagePos = this.getLandingPagePoByPageId(Lists.newArrayList(templatePageId));
            if (CollectionUtils.isEmpty(existTemplatePagePos)) {
                throw new IllegalArgumentException("落地页数据错误");
            }

            if (!existTemplatePagePos.get(0).getIsDeleted().equals(IsDeleted.VALID.getCode())
                    || !existTemplatePagePos.get(0).getStatus().equals(LandingPageStatusEnum.PUBLISHED.getCode())) {
                return 0L;
            }
            return templatePageId;
        }

        // 尝试创建落地页
        return createTemplatePage(operator, pageId, gameBaseId, packageId, url);
    }

    /**
     * 获取/生成联投落地页 一次只允许查一类联投落地页 因为可能还有生成逻辑
     */
    private List<SanlianMgkTemplatePageDto> getPageIdTemplatePageList(List<Long> pageIdList) {
        if (CollectionUtils.isEmpty(pageIdList)) {
            return Collections.emptyList();
        }

        QuerySanlianTemplatePageDto queryDto = QuerySanlianTemplatePageDto.builder()
                .pageIdList(pageIdList)
                .build();
        return getBaseTemplatePageListFromDB(queryDto);
    }

    private List<SanlianMgkTemplatePageDto> getGameBaseIdTemplatePageList(Operator operator, List<Integer> gameBaseIdList) {
        if (CollectionUtils.isEmpty(gameBaseIdList)) {
            return Collections.emptyList();
        }

        String lockSuffix = CREATE_TEMPLATE_PAGE_BY_GAME_BASE_ID_SUFFIX;
        Integer accountId = operator.getOperatorId();
        RLock lock = mgkBaseService.getLock(accountId, lockSuffix);

        try {
            QuerySanlianTemplatePageDto queryDto = QuerySanlianTemplatePageDto.builder()
                    .gameBaseIdList(gameBaseIdList)
                    .build();
            List<SanlianMgkTemplatePageDto> baseTemplatePageDtoList = getBaseTemplatePageListFromDB(queryDto);
            // 游戏id
            List<Integer> existGameBaseIdList = baseTemplatePageDtoList.stream()
                    .map(SanlianMgkTemplatePageDto::getGameBaseId)
                    .collect(Collectors.toList());
            List<Integer> needSaveGameBaseIdList = gameBaseIdList.stream()
                    .filter(gameBaseId -> !existGameBaseIdList.contains(gameBaseId))
                    .collect(Collectors.toList());

            createTemplatePageByGameBaseIdList(operator, needSaveGameBaseIdList);
            // 全部创建完毕 查询数据库获取结果
            return getBaseTemplatePageListFromDB(queryDto);
        } finally {
            log.info("getGameBaseIdTemplatePageList finish, gameBaseIdList:{}", gameBaseIdList);
            lock.unlock();
        }
    }

    private List<SanlianMgkTemplatePageDto> getAppPackageIdTemplatePageList(Operator operator, List<Integer> appPackageIdList) {
        if (CollectionUtils.isEmpty(appPackageIdList)) {
            return Collections.emptyList();
        }

        String lockSuffix = CREATE_TEMPLATE_PAGE_BY_APP_PACKAGE_ID_SUFFIX;
        Integer accountId = operator.getOperatorId();
        RLock lock = mgkBaseService.getLock(accountId, lockSuffix);

        try {
            QuerySanlianTemplatePageDto queryDto = QuerySanlianTemplatePageDto.builder()
                    .appPackageIdList(appPackageIdList)
                    .build();
            List<SanlianMgkTemplatePageDto> baseTemplatePageDtoList = getBaseTemplatePageListFromDB(queryDto);
            // 应用包id
            List<Integer> existAppPackageIdList = baseTemplatePageDtoList.stream()
                    .map(SanlianMgkTemplatePageDto::getAppPackageId)
                    .collect(Collectors.toList());
            List<Integer> needSaveAppPackageIdList = appPackageIdList.stream()
                    .filter(appPackageId -> !existAppPackageIdList.contains(appPackageId))
                    .collect(Collectors.toList());
            if (!templateSwitch) {
                // 开关打开后，不再生成205联投模版
                createTemplatePageByAppPackageIdList(operator, needSaveAppPackageIdList);
            }
            // 全部创建完毕 查询数据库获取结果
            return getBaseTemplatePageListFromDB(queryDto);
        } finally {
            log.info("getAppPackageIdTemplatePageList finish, appPackageIdList:{}", appPackageIdList);
            lock.unlock();
        }
    }

    private List<SanlianMgkTemplatePageDto> getUrlTemplatePageList(Operator operator, List<String> urlList) {
        if (CollectionUtils.isEmpty(urlList)) {
            return Collections.emptyList();
        }

        String lockSuffix = CREATE_TEMPLATE_PAGE_BY_URL_SUFFIX;
        Integer accountId = operator.getOperatorId();
        RLock lock = mgkBaseService.getLock(accountId, lockSuffix);

        try {
            QuerySanlianTemplatePageDto queryDto = QuerySanlianTemplatePageDto.builder()
                    .urlList(urlList)
                    .build();
            List<SanlianMgkTemplatePageDto> baseTemplatePageDtoList = getBaseTemplatePageListFromDB(queryDto);
            // 链接
            List<String> existUrlList = baseTemplatePageDtoList.stream()
                    .map(SanlianMgkTemplatePageDto::getUrl)
                    .collect(Collectors.toList());
            List<String> needSaveUrlList = urlList.stream()
                    .filter(url -> !existUrlList.contains(url))
                    .collect(Collectors.toList());
            createTemplatePageByUrlList(operator, needSaveUrlList);
            // 全部创建完毕 查询数据库获取结果
            return getBaseTemplatePageListFromDB(queryDto);
        } finally {
            log.info("getGameBaseIdTemplatePageList finish, urlList:{}", urlList);
            lock.unlock();
        }
    }

    private void createTemplatePageByGameBaseIdList(Operator operator, List<Integer> gameBaseIdList) {
        if (CollectionUtils.isEmpty(gameBaseIdList)) {
            return;
        }
        gameBaseIdList.forEach(gameBaseId -> {
            createMgkLandingTemplatePage(operator, null, gameBaseId, null, null);
        });
    }

    private void createTemplatePageByAppPackageIdList(Operator operator, List<Integer> appPackageIdList) {
        if (CollectionUtils.isEmpty(appPackageIdList)) {
            return;
        }
        appPackageIdList.forEach(appPackageId -> {
            createMgkLandingTemplatePage(operator, null, null, appPackageId, null);
        });
    }

    private void createTemplatePageByUrlList(Operator operator, List<String> urlList) {
        if (CollectionUtils.isEmpty(urlList)) {
            return;
        }
        urlList.forEach(url -> {
            createMgkLandingTemplatePage(operator, null, null, null, url);
        });
    }

    private List<SanlianMgkTemplatePageDto> getBaseTemplatePageListFromDB(QuerySanlianTemplatePageDto queryDto) {
        List<MgkTemplatePageMappingPo> mappingPoList = getSanlianTemplatePageMappingPos(queryDto);
        return mappingPoList.stream()
                .map(mappingPo -> {
                    return SanlianMgkTemplatePageDto.builder()
                            .mgkPageId(mappingPo.getPageId())
                            .templatePageId(mappingPo.getTemplatePageId())
                            .gameBaseId(mappingPo.getGameBaseId())
                            .appPackageId(mappingPo.getPackageId())
                            .url(mappingPo.getUrl())
                            .build();
                }).collect(Collectors.toList());
    }

    private Long createTemplatePage(Operator operator, Long pageId, Integer gameBaseId, Integer packageId, String url) {

        return createMgkLandingTemplatePage(operator, pageId, gameBaseId, packageId, url);
    }

    /**
     * 创建创意联投专用模板映射
     */
    private void insertMgkLandingTemplatePageMapping(MgkTemplatePageMappingDto dto) {
        MgkTemplatePageMappingPo po = MgkTemplatePageMappingPo.builder().build();
        BeanUtils.copyProperties(dto, po);
        int res = mgkTemplatePageMappingDao.insertSelective(po);
        Assert.isTrue(Utils.isPositive(res), "创建模板落地页失败");
    }


    public int deleteTemplatePageByOriginPageId(Long pageId){
        MgkTemplatePageMappingPoExample exm = new MgkTemplatePageMappingPoExample();
        exm.or().andPageIdEqualTo(pageId);
        MgkTemplatePageMappingPo po = MgkTemplatePageMappingPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();
        return mgkTemplatePageMappingDao.updateByExampleSelective(po, exm);

    }

    /**
     * 联投模板
     */
    public Map<Long, Long> getMgkLandingTemplatePageIdMap(List<Long> templatePageIds) {
        if (CollectionUtils.isEmpty(templatePageIds)) {
            return Collections.emptyMap();
        }
        MgkTemplatePageMappingPoExample example = new MgkTemplatePageMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTemplatePageIdIn(templatePageIds)
                .andPageIdGreaterThan(0L);
        List<MgkTemplatePageMappingPo> pos = mgkTemplatePageMappingDao.selectByExample(example);
        return pos.stream().collect(
                Collectors.toMap(MgkTemplatePageMappingPo::getTemplatePageId, MgkTemplatePageMappingPo::getPageId, (exist, replace) -> exist));
    }

    public Map<Long, Long> getMgkLandingPageTemplateWithoutArcIdMap(List<Long> templatePageIds) {
        if (CollectionUtils.isEmpty(templatePageIds)) {
            return Collections.emptyMap();
        }
        MgkLandingPageTemplateMappingPoExample exm = new MgkLandingPageTemplateMappingPoExample();
        exm.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTemplatePageIdIn(templatePageIds)
                .andPageIdGreaterThan(0L);
        List<MgkLandingPageTemplateMappingPo> pos = mgkLandingPageTemplateMappingDao.selectByExample(exm);
        return pos.stream().collect(
                Collectors.toMap(MgkLandingPageTemplateMappingPo::getTemplatePageId, MgkLandingPageTemplateMappingPo::getPageId, (exist, replace) -> exist));
    }

    private Long createMgkLandingTemplatePage(Operator operator, Long pageId, Integer gameBaseId, Integer packageId, String url) {
        if (Objects.isNull(operator)) {
            return 0L;
        }

        // 投放端不能自己创建落地页模板，由建站创建

        if ( Utils.isPositive(gameBaseId)) {
            // 为游戏中心创建模板落地页
            return this.createGameCenterTemplatePage(operator, gameBaseId);
        }

        // 新版本不再生成205联投模版
        if (!templateSwitch && Utils.isPositive(packageId)) {
            // 为IOS应用包创建模板落地页
            return this.createAppPackageTemplatePage(operator, packageId);
        }

        if (!Strings.isNullOrEmpty(url)) {
            // 为外链地址创建模板落地页
            return this.createUrlTemplatePage(operator, url);
        }
        return 0L;
    }

    private Long getTemplatePage(Long pageId, Integer gameBaseId, Integer packageId, String url) {
        if (!Utils.isPositive(pageId)
                && !Utils.isPositive(gameBaseId)
                && !Utils.isPositive(packageId)
                && Strings.isNullOrEmpty(url)) {
            return 0L;
        }

        // 查询数据库中是否已经存在该模板落地页
        List<MgkTemplatePageMappingPo> pos = getTemplatePageMappingPos(new QueryTemplatePageDto(pageId, gameBaseId, packageId, url));

        // 存在直接返回模板的pageId
        if (CollectionUtils.isEmpty(pos)) {
            return 0L;
        }
        return pos.get(0).getTemplatePageId();
    }

    /**
     * 为外链yrl生成204模板
     *
     * @param operator
     * @param url
     * @return
     */
    private Long createUrlTemplatePage(Operator operator, String url) {

        NewLandingPageDto newLandingPageDto = NewLandingPageDto.builder()
                .effectiveStartTime(Utils.getToday())
                .effectiveEndTime(Utils.getToday())
                .formId(0L)
                .isModel(IsModelEnum.TEMPLATE.getCode())
                .accountId(operator.getOperatorId())
                .pageVersion(templateSwitch?templateSwitchVersion:"")
                .avIds(Collections.emptyList())
                .type(LandingPageTypeEnum.NATIVE.getCode())
                .templateStyle(TemplateStyleEnum.HALF_SCREEN_VIDEO.getCode())
                .isVideoPage(WhetherEnum.YES.getCode())
                .header(MgkHeaderEnum.WITHOUT_HEADER.getCode())
                .config(JSON.toJSONString(Collections.singletonList(NativeVideoConfig.builder()
                        .video(Video.builder()
                                .avid(MgkTemplateConfigConstants.CONFIG_VIDEO_AID)
                                .cid(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                                .biz_id(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                                .cover(MgkTemplateConfigConstants.CONFIG_VIDEO_COVER)
                                .from(MgkTemplateConfigConstants.CONFIG_VIDEO_FROM)
                                .page(0)
                                .play_3s_urls(Collections.emptyList())
                                .play_5s_urls(Collections.emptyList())
                                .process0_urls(Collections.emptyList())
                                .process1_urls(Collections.emptyList())
                                .process2_urls(Collections.emptyList())
                                .process3_urls(Collections.emptyList())
                                .process4_urls(Collections.emptyList())
                                .build()
                        )
                        .jump_url("")
                        .cover("")
                        .callup_url("")
                        .report_urls(Collections.emptyList())
                        .icon("")
                        .title("")
                        .desc("")
                        .report_time(2000L)
                        .button(Button.builder()
                                .type("")
                                .text("")
                                .jump_url("")
                                .report_urls(Collections.emptyList())
                                .dlsuc_callup_url("")
                                .build())
                        ._use_weburl("1")
                        .weburl(url)
                        .version("")
                        .build())))
                .build();

        // 创建落地页
        long templatePageId = this.create(operator, newLandingPageDto);

        // 发布落地页
        this.publish(templatePageId);

        // 创建映射关系
        this.insertMgkLandingTemplatePageMapping(MgkTemplatePageMappingDto.builder()
                .accountId(operator.getOperatorId())
                .hashKey(new QueryTemplatePageDto(null, null, null, url).getHashKey())
                .url(url)
                .templatePageId(templatePageId)
                .build());

        return templatePageId;
    }

    /**
     * 为ios应用包生成205模板
     *
     * @param operator
     * @param packageId
     * @return
     */
    private Long createAppPackageTemplatePage(Operator operator, Integer packageId) {
        // 获取应用包的地址
        List<ResAppPackageDto> appPackageDtos = resAppPackageService.getAppPackageDtos(Lists.newArrayList(packageId));

        if (CollectionUtils.isEmpty(appPackageDtos)) {
            return 0L;
        }

        NewLandingPageDto newLandingPageDto = NewLandingPageDto.builder()
                .pageVersion(templateSwitch?templateSwitchVersion:"")
                .effectiveStartTime(Utils.getToday())
                .effectiveEndTime(Utils.getToday())
                .formId(0L)
                .accountId(operator.getOperatorId())
                .templateStyle(TemplateStyleEnum.HALF_SCREEN_VIDEO_IOS.getCode())
                .type(LandingPageTypeEnum.NATIVE.getCode())
                .isModel(IsModelEnum.TEMPLATE.getCode())
                .isVideoPage(WhetherEnum.YES.getCode())
                .header(MgkHeaderEnum.WITHOUT_HEADER.getCode())
                .config(JSON.toJSONString(Collections.singletonList(NativeGameVideoConfig.builder()
                        .video(Video.builder()
                                .avid(MgkTemplateConfigConstants.CONFIG_VIDEO_AID)
                                .cid(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                                .biz_id(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                                .cover(MgkTemplateConfigConstants.CONFIG_VIDEO_COVER)
                                .from(MgkTemplateConfigConstants.CONFIG_VIDEO_FROM)
                                .page(0)
                                .play_3s_urls(Collections.emptyList())
                                .play_5s_urls(Collections.emptyList())
                                .process0_urls(Collections.emptyList())
                                .process1_urls(Collections.emptyList())
                                .process2_urls(Collections.emptyList())
                                .process3_urls(Collections.emptyList())
                                .process4_urls(Collections.emptyList())
                                .build()
                        )
                        .store_url(appPackageDtos.get(0).getUrl())
                        .auto_store(1)
                        .weburl(MgkConstants.MGK_IOS_PAGE_PREFIX_URL + packageId)
                        .report_urls(Collections.emptyList())
                        .report_time(2000L)
                        .version("")
                        .build())))
                .build();

        // 创建落地页
        long templatePageId = this.create(operator, newLandingPageDto);

        // 发布落地页
        this.publish(templatePageId);

        // 创建映射关系
        this.insertMgkLandingTemplatePageMapping(MgkTemplatePageMappingDto.builder()
                .accountId(operator.getOperatorId())
                .hashKey(new QueryTemplatePageDto(null, null, packageId, null).getHashKey())
                .packageId(packageId)
                .templatePageId(templatePageId)
                .build());

        return templatePageId;
    }

    /**
     * 为游戏中心生成206模板
     *
     * @param operator
     * @param gameBaseId
     * @return
     */
    private Long createGameCenterTemplatePage(Operator operator, Integer gameBaseId) {
        
        NewLandingPageDto newLandingPageDto = NewLandingPageDto.builder()
                .accountId(operator.getOperatorId())
                .pageVersion(templateSwitch?templateSwitchVersion:"")
                .isModel(IsModelEnum.TEMPLATE.getCode())
                .templateStyle(TemplateStyleEnum.HALF_SCREEN_VIDEO_ANDROID.getCode())
                .type(LandingPageTypeEnum.NATIVE.getCode())
                .effectiveStartTime(Utils.getToday())
                .effectiveEndTime(Utils.getToday())
                .formId(0L)
                .isVideoPage(WhetherEnum.YES.getCode())
                .header(MgkHeaderEnum.WITHOUT_HEADER.getCode())
                .config(JSON.toJSONString(Collections.singletonList(NativeGameVideoConfig.builder()
                        .weburl("")
                        .video(Video.builder()
                                .avid(MgkTemplateConfigConstants.CONFIG_VIDEO_AID)
                                .cid(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                                .biz_id(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                                .cover(MgkTemplateConfigConstants.CONFIG_VIDEO_COVER)
                                .from(MgkTemplateConfigConstants.CONFIG_VIDEO_FROM)
                                .page(0)
                                .play_3s_urls(Collections.emptyList())
                                .play_5s_urls(Collections.emptyList())
                                .process0_urls(Collections.emptyList())
                                .process1_urls(Collections.emptyList())
                                .process2_urls(Collections.emptyList())
                                .process3_urls(Collections.emptyList())
                                .process4_urls(Collections.emptyList())
                                .build()
                        )
                        .game_id(gameBaseId)
                        .game_monitor_param(MgkTemplateConfigConstants.CONFIG_GAME_MONITOR_PARAM)
                        .source(MgkTemplateConfigConstants.CONFIG_SOURCE)
                        .source_from(MgkTemplateConfigConstants.CONFIG_SOURCE_FROM)
                        .report_urls(Collections.emptyList())
                        .report_time(2000L)
                        .version("")
                        .build())))
                .build();

        // 创建落地页
        long templatePageId = this.create(operator, newLandingPageDto);

        // 发布落地页
        this.publish(templatePageId);

        // 创建映射关系表
        this.insertMgkLandingTemplatePageMapping(MgkTemplatePageMappingDto.builder()
                .accountId(operator.getOperatorId())
                .hashKey(new QueryTemplatePageDto(null, gameBaseId, null, null).getHashKey())
                .gameBaseId(gameBaseId)
                .templatePageId(templatePageId)
                .build());

        return templatePageId;
    }

    /**
     * 创建建站模板落地页
     *
     * @param operator
     * @param pageId
     * @param newLandingPageDto
     * @return
     */
    private Long createLandingTemplatePage(Operator operator, Long pageId, NewLandingPageDto newLandingPageDto) {
        // 创建落地页模板
        long templateId = this.create(operator, newLandingPageDto);

        // 发布落地页
        this.publish(templateId);

        // 创建映射关系
        this.insertMgkLandingTemplatePageMapping(MgkTemplatePageMappingDto.builder()
                .hashKey(new QueryTemplatePageDto(pageId, null, null, null).getHashKey())
                .accountId(operator.getOperatorId())
                .pageId(pageId)
                .templatePageId(templateId)
                .build());
        return templateId;
    }

    /**
     * 更新落地页模板
     *
     * @param operator
     * @param updateLandingPageDto
     */
    private void updateLandingTemplatePage(Operator operator, UpdateLandingPageDto updateLandingPageDto) {

        // 更新落地页模板
        this.update(operator, updateLandingPageDto, false);

        // 发布落地页模板
        this.publish(updateLandingPageDto.getPageId());
    }


    /**
     * 为小程序页面生成401模板
     *
     * @param operator
     * @param pageId
     * @return
     */
    private Long createLandingAppletsTemplatePage(Operator operator, Long pageId) {
        LandingPageConfigDto landingPageConfigDto = this.getLandingPageConfigDtoByPageId(pageId);

        NewLandingPageDto newLandingPageDto = NewLandingPageDto.builder().build();
        BeanUtils.copyProperties(landingPageConfigDto, newLandingPageDto);

        // 更新落地页的配置
        // 无视频落地页 不需要重复设值
        newLandingPageDto.setConfig(this.convertAppletsToTemplateConfig(landingPageConfigDto.getConfig()));
        newLandingPageDto.setIsModel(IsModelEnum.TEMPLATE.getCode());
        newLandingPageDto.setIsVideoPage(WhetherEnum.YES.getCode());
        newLandingPageDto.setHeader(MgkHeaderEnum.WITHOUT_HEADER.getCode());
        newLandingPageDto.setHasTransition(WhetherEnum.YES.getCode());
        newLandingPageDto.setTransition(APPLETS_LANDING_PAGE_TRANSITION);

        // 创建落地页
        long templatePageId = this.create(operator, newLandingPageDto);

        // 发布落地页
        this.publish(templatePageId);

        // 创建映射关系
        this.insertMgkLandingTemplatePageMapping(MgkTemplatePageMappingDto.builder()
                .accountId(operator.getOperatorId())
                .hashKey(new QueryTemplatePageDto(pageId, null, null, null).getHashKey())
                .pageId(pageId)
                .templatePageId(templatePageId)
                .build());

        return templatePageId;
    }

    /**
     * 转化小程序的配置
     * 在blocks字段中，找到name="fixed-block"的block，在其中的components数组中加入上面的数据结构，放到第一位
     *
     * @param config
     * @return
     */
    private String convertAppletsToTemplateConfig(String config) {
        // todo 更新配置信息
        JSONObject configObject = JSON.parseObject(config);
        JSONArray blocks = configObject.getJSONArray("blocks");
        blocks.forEach(block -> {
            JSONObject blockObject = (JSONObject) block;
            String blockName = blockObject.getString("name");
            if ("fixed-block".equals(blockName)) {
                JSONArray components = blockObject.getJSONArray("components");
                components.add(0, MgkTemplateConfigConstants.APPLETS_COMPONENT_CONFIG);
            }
        });
        return configObject.toJSONString();
    }

    private List<MgkTemplatePageMappingPo> getTemplatePageMappingPos(QueryTemplatePageDto queryTemplatePageDto) {
        if (Objects.isNull(queryTemplatePageDto)) {
            return Collections.emptyList();
        }

        MgkTemplatePageMappingPoExample example = new MgkTemplatePageMappingPoExample();
        MgkTemplatePageMappingPoExample.Criteria or = example.or();
        or.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        ObjectUtils.setObject(queryTemplatePageDto::getHashKey, or::andHashKeyEqualTo);
        ObjectUtils.setObject(queryTemplatePageDto::getPageId, or::andPageIdEqualTo);
        ObjectUtils.setList(queryTemplatePageDto::getPageIds, or::andPageIdIn);
        ObjectUtils.setObject(queryTemplatePageDto::getGameBaseId, or::andGameBaseIdEqualTo);
        ObjectUtils.setObject(queryTemplatePageDto::getPackageId, or::andPackageIdEqualTo);
        ObjectUtils.setObject(queryTemplatePageDto::getUrl, or::andUrlEqualTo);
        return mgkTemplatePageMappingDao.selectByExample(example);
    }

    private List<MgkTemplatePageMappingPo> getSanlianTemplatePageMappingPos(QuerySanlianTemplatePageDto queryDto) {
        if (Objects.isNull(queryDto)) {
            return Collections.emptyList();
        }

        MgkTemplatePageMappingPoExample example = new MgkTemplatePageMappingPoExample();
        MgkTemplatePageMappingPoExample.Criteria or = example.or();
        or.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<String> hashKeyList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(queryDto.getPageIdList())) {
            hashKeyList = queryDto.getPageIdList().stream()
                    .map(pageId -> new QueryTemplatePageDto(pageId, null, null, null).getHashKey())
                    .collect(Collectors.toList());
        } else if (!CollectionUtils.isEmpty(queryDto.getGameBaseIdList())) {
            hashKeyList = queryDto.getGameBaseIdList().stream()
                    .map(gameBaseId -> new QueryTemplatePageDto(null, gameBaseId, null, null).getHashKey())
                    .collect(Collectors.toList());
        } else if (!CollectionUtils.isEmpty(queryDto.getAppPackageIdList())) {
            hashKeyList = queryDto.getAppPackageIdList().stream()
                    .map(appPackageId -> new QueryTemplatePageDto(null, null, appPackageId, null).getHashKey())
                    .collect(Collectors.toList());
        } else if (!CollectionUtils.isEmpty(queryDto.getUrlList())) {
            hashKeyList = queryDto.getUrlList().stream()
                    .map(url -> new QueryTemplatePageDto(null, null, null, url).getHashKey())
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(hashKeyList)) {
            return Collections.emptyList();
        }

        or.andHashKeyIn(hashKeyList);
        return mgkTemplatePageMappingDao.selectByExample(example);
    }

    public void createMgkLandingPageTemplatePage(Operator operator, Long pageId, NewLandingPageDto newLandingPageDto) {

        // 创建模板 ismodel=1时不使用本接口 无需判断
        if (!(LandingPageTypeEnum.APPLETS.getCode().equals(newLandingPageDto.getType()) && TemplateStyleEnum.APPLETS.getCode().equals(newLandingPageDto.getTemplateStyle()))
                && !LandingPageTypeEnum.H5.getCode().equals(newLandingPageDto.getType()) && TemplateStyleEnum.USER_CUSTOM.getCode().equals(newLandingPageDto.getTemplateStyle())) {
            return;
        }

        // 如果是视频落地页不创建模板
        if (!CollectionUtils.isEmpty(newLandingPageDto.getAvIds())
                || !CollectionUtils.isEmpty(newLandingPageDto.getBizIds())
                || Utils.isPositive(newLandingPageDto.getBizId())) {
            return;
        }

        // 如果有dpa商品 不创建模板
        if (WhetherEnum.YES.getCode().equals(newLandingPageDto.getHasDpaGoods())) {
            return;
        }

        if (LandingPageTypeEnum.H5.getCode().equals(newLandingPageDto.getType()) && TemplateStyleEnum.USER_CUSTOM.getCode().equals(newLandingPageDto.getTemplateStyle())) {
            createLandingH5TemplatePage(operator, pageId, newLandingPageDto);
        }

        if (LandingPageTypeEnum.APPLETS.getCode().equals(newLandingPageDto.getType())
                && TemplateStyleEnum.APPLETS.getCode().equals(newLandingPageDto.getTemplateStyle())) {
            createLandingAppletsTemplatePage(operator, pageId, newLandingPageDto);
        }
    }

    private void createLandingAppletsTemplatePage(Operator operator, Long pageId, NewLandingPageDto newLandingPageDto) {

        NewLandingPageDto newLandingTemplatePageDto = getLandingTemplatePageDtoByVersion(pageId , newLandingPageDto);

        createLandingTemplatePage(operator, pageId, newLandingTemplatePageDto);
    }

    private String genUrlInNewVersion(Long pageId ,NewLandingPageDto newLandingPageDto){
        String pageUrl = String.format(MgkConstants.MGK_LAUNCH_URL_PRE_PC, pageId.toString());
        return pageUrl+URL_TAG_LIANTOU_NEW_VERSION;

    }

    private NewLandingPageDto getLandingTemplatePageDtoByVersion(Long pageId ,NewLandingPageDto newLandingPageDto){
        if (templateSwitch){
            String url = genUrlInNewVersion(pageId ,newLandingPageDto);

            NewLandingPageDto newLandingTemplatePageDto = NewLandingPageDto.builder()
                    .title(newLandingPageDto.getTitle())
                    .name(newLandingPageDto.getName())
                    .effectiveStartTime(newLandingPageDto.getEffectiveStartTime())
                    .effectiveEndTime(newLandingPageDto.getEffectiveEndTime())
                    .formId(0L)
                    .isModel(IsModelEnum.TEMPLATE.getCode())
                    .accountId(newLandingPageDto.getAccountId())
                    .pageVersion(templateSwitchVersion)
                    .avIds(Collections.emptyList())
                    .type(LandingPageTypeEnum.NATIVE.getCode())
                    .templateStyle(TemplateStyleEnum.HALF_SCREEN_VIDEO.getCode())
                    .isVideoPage(WhetherEnum.YES.getCode())
                    .header(MgkHeaderEnum.WITHOUT_HEADER.getCode())
                    .config(JSON.toJSONString(Collections.singletonList(NativeVideoConfig.builder()
                            .video(Video.builder()
                                    .avid(MgkTemplateConfigConstants.CONFIG_VIDEO_AID)
                                    .cid(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                                    .biz_id(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                                    .cover(MgkTemplateConfigConstants.CONFIG_VIDEO_COVER)
                                    .from(MgkTemplateConfigConstants.CONFIG_VIDEO_FROM)
                                    .page(0)
                                    .play_3s_urls(Collections.emptyList())
                                    .play_5s_urls(Collections.emptyList())
                                    .process0_urls(Collections.emptyList())
                                    .process1_urls(Collections.emptyList())
                                    .process2_urls(Collections.emptyList())
                                    .process3_urls(Collections.emptyList())
                                    .process4_urls(Collections.emptyList())
                                    .build()
                            )
                            .jump_url("")
                            .cover("")
                            .callup_url("")
                            .report_urls(Collections.emptyList())
                            .icon("")
                            .title("")
                            .desc("")
                            .report_time(2000L)
                            .button(Button.builder()
                                    .type("")
                                    .text("")
                                    .jump_url(url)
                                    .report_urls(Collections.emptyList())
                                    .dlsuc_callup_url("")
                                    .build())
                            ._use_weburl("1")
                            .weburl(url)
                            .version("")
                            .build())))
                    .build();
            return newLandingTemplatePageDto;

        } else{
            NewLandingPageDto newLandingTemplatePageDto = NewLandingPageDto.builder().build();
            BeanUtils.copyProperties(newLandingPageDto, newLandingTemplatePageDto);
            newLandingTemplatePageDto.setIsModel(IsModelEnum.TEMPLATE.getCode());
            newLandingTemplatePageDto.setIsVideoPage(WhetherEnum.YES.getCode());
            newLandingTemplatePageDto.setHeader(MgkHeaderEnum.WITHOUT_HEADER.getCode());
            newLandingTemplatePageDto.setHasTransition(WhetherEnum.YES.getCode());
            newLandingTemplatePageDto.setTransition(APPLETS_LANDING_PAGE_TRANSITION);

            // 更新落地页的配置
            newLandingTemplatePageDto.setConfig(this.convertAppletsToTemplateConfig(newLandingPageDto.getConfig()));
            return newLandingTemplatePageDto;
        }
    }



    private void createLandingH5TemplatePage(Operator operator, Long pageId, NewLandingPageDto newLandingPageDto) {
        NewLandingPageDto newLandingTemplatePageDto = NewLandingPageDto.builder().build();
        BeanUtils.copyProperties(newLandingPageDto, newLandingTemplatePageDto);

        // 更新落地页的配置
        newLandingTemplatePageDto.setConfig(JSON.toJSONString(Collections.singletonList(NativeVideoConfig.builder()
                .video(Video.builder()
                        .avid(MgkTemplateConfigConstants.CONFIG_VIDEO_AID)
                        .cid(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                        .biz_id(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                        .cover(MgkTemplateConfigConstants.CONFIG_VIDEO_COVER)
                        .from(MgkTemplateConfigConstants.CONFIG_VIDEO_FROM)
                        .page(0)
                        .build()
                )
                .jump_url("")
                .cover("")
                .callup_url("")
                .report_urls(Collections.emptyList())
                .icon("")
                .title("")
                .desc("")
                .report_time(2000L)
                .button(Button.builder()
                        .type("")
                        .text("")
                        .jump_url("")
                        .report_urls(Collections.emptyList())
                        .dlsuc_callup_url("")
                        .build())
                ._use_weburl("1")
                .weburl(LandingPageTypeEnum.H5.getLaunchUrl(pageId, TemplateStyleEnum.USER_CUSTOM.getCode()))
                .build())));
        newLandingTemplatePageDto.setAvIds(Collections.emptyList());
        newLandingTemplatePageDto.setType(LandingPageTypeEnum.NATIVE.getCode());
        newLandingTemplatePageDto.setTemplateStyle(TemplateStyleEnum.HALF_SCREEN_VIDEO.getCode());
        newLandingTemplatePageDto.setIsModel(IsModelEnum.TEMPLATE.getCode());
        newLandingTemplatePageDto.setIsVideoPage(WhetherEnum.YES.getCode());
        newLandingTemplatePageDto.setHeader(MgkHeaderEnum.WITHOUT_HEADER.getCode());
        createLandingTemplatePage(operator, pageId, newLandingTemplatePageDto);
    }

    public void updateMgkLandingPageTemplatePage(Operator operator, UpdateLandingPageDto updateLandingPageDto) {

        if (!(LandingPageTypeEnum.APPLETS.getCode().equals(updateLandingPageDto.getPageType())
                && TemplateStyleEnum.APPLETS.getCode().equals(updateLandingPageDto.getTemplateStyle()))
                || !IsModelEnum.PAGE.getCode().equals(updateLandingPageDto.getIsModel())) {
            return;
        }
        updateLandingAppletsTemplatePage(operator, updateLandingPageDto);
    }

    private void updateLandingAppletsTemplatePage(Operator operator, UpdateLandingPageDto updateLandingPageDto) {

        Long templatePageId = getTemplatePage(updateLandingPageDto.getPageId(), null, null, null);
        // 副本条件 非视频落地页且不含dpa商品
        boolean valid = CollectionUtils.isEmpty(updateLandingPageDto.getAvIds())
                && CollectionUtils.isEmpty(updateLandingPageDto.getBizIds())
                && !Utils.isPositive(updateLandingPageDto.getBizId())
                && WhetherEnum.NO.getCode().equals(updateLandingPageDto.getHasDpaGoods());
        // 如果修改后才满足创建条件 创建副本落地页
        if (!Utils.isPositive(templatePageId)) {
            if (valid) {
                createLandingAppletsTemplatePage(operator, updateLandingPageDto.getPageId());
            }
            return;
        }

        MgkLandingPagePo templatePagePo = this.getByPageId(templatePageId);
        // 如果修改后不满足条件 下线联投副本落地页
        if (!valid) {
            if (Objects.nonNull(templatePagePo) && LandingPageStatusEnum.PUBLISHED.getCode().equals(templatePagePo.getStatus())) {
                if (Utils.isPositive(updateLandingPageDto.getIsWriteBack())) {
                    mgkGrpcManager.removeContainerPage(templatePageId);
                }
                downline(operator, templatePageId);
            }
            return;
        }

        UpdateLandingPageDto updateTemplatePageDto = getLandingTemplatePageDtoByVersion(templatePageId , templatePagePo, updateLandingPageDto);
        // 更新落地页的配置
        updateLandingTemplatePage(operator, updateTemplatePageDto);
    }


    private String genUrlInNewVersion(Long pageId ,UpdateLandingPageDto updateLandingPageDto){
        String pageUrl = String.format(MgkConstants.MGK_LAUNCH_URL_PRE_PC, pageId.toString());
        return pageUrl+URL_TAG_LIANTOU_NEW_VERSION;
    }

    private boolean isNewVersion(String pageVersion){
        return templateSwitchVersion.compareTo(pageVersion) <= 0;
    }

    private UpdateLandingPageDto getLandingTemplatePageDtoByVersion(Long templatePageId ,MgkLandingPagePo templatePagePo,UpdateLandingPageDto updateLandingPageDto){
        if (templateSwitch && isNewVersion(templatePagePo.getPageVersion())){
            String url = genUrlInNewVersion(updateLandingPageDto.getPageId() ,updateLandingPageDto);
            UpdateLandingPageDto updateTemplatePageDto = UpdateLandingPageDto.builder().build();
            BeanUtils.copyProperties(updateLandingPageDto, updateTemplatePageDto);
            updateTemplatePageDto.setFormId(0L);
            updateTemplatePageDto.setIsModel(IsModelEnum.TEMPLATE.getCode());
            updateTemplatePageDto.setPageVersion(templatePagePo.getPageVersion());
            updateTemplatePageDto.setAvIds(Collections.emptyList());
            updateTemplatePageDto.setPageType(LandingPageTypeEnum.NATIVE.getCode());
            updateTemplatePageDto.setTemplateStyle(TemplateStyleEnum.HALF_SCREEN_VIDEO.getCode());
            updateTemplatePageDto.setIsVideoPage(WhetherEnum.YES.getCode());
            updateTemplatePageDto.setHeader(MgkHeaderEnum.WITHOUT_HEADER.getCode());
            updateTemplatePageDto.setConfig(JSON.toJSONString(Collections.singletonList(NativeVideoConfig.builder()
                    .video(Video.builder()
                            .avid(MgkTemplateConfigConstants.CONFIG_VIDEO_AID)
                            .cid(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                            .biz_id(MgkTemplateConfigConstants.CONFIG_VIDEO_CID)
                            .cover(MgkTemplateConfigConstants.CONFIG_VIDEO_COVER)
                            .from(MgkTemplateConfigConstants.CONFIG_VIDEO_FROM)
                            .page(0)
                            .play_3s_urls(Collections.emptyList())
                            .play_5s_urls(Collections.emptyList())
                            .process0_urls(Collections.emptyList())
                            .process1_urls(Collections.emptyList())
                            .process2_urls(Collections.emptyList())
                            .process3_urls(Collections.emptyList())
                            .process4_urls(Collections.emptyList())
                            .build()
                    )
                    .jump_url("")
                    .cover("")
                    .callup_url("")
                    .report_urls(Collections.emptyList())
                    .icon("")
                    .title("")
                    .desc("")
                    .report_time(2000L)
                    .button(Button.builder()
                            .type("")
                            .text("")
                            .jump_url(url)
                            .report_urls(Collections.emptyList())
                            .dlsuc_callup_url("")
                            .build())
                    ._use_weburl("1")
                    .weburl(url)
                    .version("")
                    .build())));
            updateTemplatePageDto.setPageId(templatePageId);
            return updateTemplatePageDto;

        } else{
            UpdateLandingPageDto updateTemplatePageDto = UpdateLandingPageDto.builder().build();
            BeanUtils.copyProperties(updateLandingPageDto, updateTemplatePageDto);
            updateTemplatePageDto.setConfig(this.convertAppletsToTemplateConfig(updateLandingPageDto.getConfig()));
            updateTemplatePageDto.setPageId(templatePageId);
            updateTemplatePageDto.setIsModel(IsModelEnum.TEMPLATE.getCode());
            updateTemplatePageDto.setIsVideoPage(WhetherEnum.YES.getCode());
            updateTemplatePageDto.setHeader(MgkHeaderEnum.WITHOUT_HEADER.getCode());
            updateTemplatePageDto.setHasTransition(WhetherEnum.YES.getCode());
            updateTemplatePageDto.setTransition(APPLETS_LANDING_PAGE_TRANSITION);
            return updateTemplatePageDto;
        }
    }

    public void createLandingPageTemplateWithoutProperVideoComponent(Operator operator, Long pageId, NewLandingPageDto newLandingPageDto) {
        // 落地页类型校验
        if (Objects.isNull(newLandingPageDto)
                || !IsModelEnum.PAGE.getCode().equals(newLandingPageDto.getIsModel())
                || !TemplateStyleEnum.APPLETS.getCode().equals(newLandingPageDto.getTemplateStyle())
                || !LandingPageTypeEnum.APPLETS.getCode().equals(newLandingPageDto.getType())
                || WhetherEnum.YES.getCode().equals(newLandingPageDto.getHasDpaGoods())) {
            return;
        }
        // 视频落地页校验
        if (CollectionUtils.isEmpty(newLandingPageDto.getAvIds())
                && CollectionUtils.isEmpty(newLandingPageDto.getBizIds())
                && !Utils.isPositive(newLandingPageDto.getBizId())) {
            return;
        }
        NewLandingPageDto templateNewLandingPageDto = new NewLandingPageDto();
        BeanUtils.copyProperties(newLandingPageDto, templateNewLandingPageDto);
        createLandingPageTemplatesWithoutProperVideoComponent(operator, pageId, templateNewLandingPageDto);
    }

    public void updateLandingPageTemplateWithoutProperVideoComponent(Operator operator, UpdateLandingPageDto updateLandingPageDto) {
        // 校验落地页类型
        if (Objects.isNull(updateLandingPageDto)
                || !IsModelEnum.PAGE.getCode().equals(updateLandingPageDto.getIsModel())
                || !TemplateStyleEnum.APPLETS.getCode().equals(updateLandingPageDto.getTemplateStyle())
                || !LandingPageTypeEnum.APPLETS.getCode().equals(updateLandingPageDto.getPageType())) {
            return;
        }

        Long templatePageId = getMgkLandingPageTemplateWithoutArcIdByOriginPageId(updateLandingPageDto.getPageId());
        String newConfig = getConfigWithoutProperVideoComponent(updateLandingPageDto.getConfig());
        if (Utils.isPositive(templatePageId)) {
            // 修改后不满足无视频副本存在条件 下线可能存在的副本
            if (StringUtils.isEmpty(newConfig) || WhetherEnum.YES.getCode().equals(updateLandingPageDto.getHasDpaGoods())) {
                // 校验副本页面是否为发布状态
                MgkLandingPagePo templatePagePo = this.getByPageId(templatePageId);
                if (Objects.nonNull(templatePagePo) && LandingPageStatusEnum.PUBLISHED.getCode().equals(templatePagePo.getStatus())) {
                    downline(operator, templatePageId);
                }
                return;
            }
            // 更新无视频副本
            updateLandingPageTemplateWithoutProperVideoComponent(operator, updateLandingPageDto, newConfig, templatePageId);
            return;
        }
        // 创建无视频副本
        if (WhetherEnum.NO.getCode().equals(updateLandingPageDto.getHasDpaGoods())) {
            createLandingPageTemplateWithoutProperVideoComponent(operator, updateLandingPageDto.getPageId());
        }
    }

    /**
     * 保存通用建站模板关系映射 目前暂时只包括单个视频落地页去视频的映射关系
     */
    private void insertMgkLandingPageTemplateMapping(MgkLandingPageTemplateMappingDto dto) {
        MgkLandingPageTemplateMappingPo po = new MgkLandingPageTemplateMappingPo();
        BeanUtils.copyProperties(dto, po);
        int res = mgkLandingPageTemplateMappingDao.insertSelective(po);
        Assert.isTrue(Utils.isPositive(res), "创建模板落地页失败");
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void refreshAllTemplatePageIdsWithoutArcs(List<Integer> accountIds) {
        long startPageId = 0;
        List<Long> pageIds = getMgkLandingPageWithArcIds(startPageId, accountIds);
        while (!CollectionUtils.isEmpty(pageIds)) {
            startPageId = pageIds.get(pageIds.size() - 1);
            // 已经创建了无视频类型副本的page_id不进行生成
            List<Long> existPageIds = getExistLandingPageTemplateMappingWithoutArc(pageIds);
            pageIds = pageIds.stream()
                    .filter(pageId -> !existPageIds.contains(pageId))
                    .collect(Collectors.toList());
            createLandingPageTemplatesWithoutProperVideoComponent(pageIds);
            pageIds = getMgkLandingPageWithArcIds(startPageId, accountIds);
        }
    }

    private void updateLandingPageTemplateWithoutProperVideoComponent(Operator operator, UpdateLandingPageDto updateLandingPageDto, String newConfig, Long templatePageId) {
        UpdateLandingPageDto updateLandingPageTemplateDto = new UpdateLandingPageDto();
        BeanUtils.copyProperties(updateLandingPageDto, updateLandingPageTemplateDto);
        updateLandingPageTemplateDto.setPageId(templatePageId);
        updateLandingPageTemplateDto.setConfig(newConfig);
        updateLandingPageTemplateDto.setIsModel(IsModelEnum.NO_ARC_TEMPLATE.getCode());
        updateLandingPageTemplateDto.setAvIds(Collections.emptyList());
        updateLandingPageTemplateDto.setBizIds(Collections.emptyList());
        updateLandingPageTemplateDto.setBizId(null);
        updateLandingPageTemplateDto.setIsVideoPage(WhetherEnum.NO.getCode());
        updateLandingPageTemplateDto.setHasTransition(WhetherEnum.NO.getCode());
        this.update(operator, updateLandingPageTemplateDto, false);
        this.publish(templatePageId);
    }

    private void createLandingPageTemplateWithoutProperVideoComponent(Operator operator, Long pageId) {
        LandingPageConfigDto landingPageConfigDto = this.getLandingPageConfigDtoByPageId(pageId);
        NewLandingPageDto newLandingPageDto = NewLandingPageDto.builder().build();
        newLandingPageDto.copyPropertiesFromLandingPageConfigDto(landingPageConfigDto);
        createLandingPageTemplatesWithoutProperVideoComponent(operator, pageId, newLandingPageDto);
    }

    private void createLandingPageTemplatesWithoutProperVideoComponent(List<Long> pageIds) {
        pageIds.forEach(pageId -> {
            LandingPageConfigDto landingPageConfigDto = this.getLandingPageConfigDtoByPageId(pageId);
            NewLandingPageDto newLandingPageDto = NewLandingPageDto.builder().build();
            newLandingPageDto.copyPropertiesFromLandingPageConfigDto(landingPageConfigDto);
            Operator operator = Operator.builder()
                    .operatorId(landingPageConfigDto.getAccountId())
                    .operatorType(OperatorType.SYSTEM)
                    .operatorName(landingPageConfigDto.getCreator())
                    .build();
            createLandingPageTemplatesWithoutProperVideoComponent(operator, pageId, newLandingPageDto);
        });
    }

    private void createLandingPageTemplatesWithoutProperVideoComponent(Operator operator, Long pageId, NewLandingPageDto newLandingPageDto) {
        String newConfig = getConfigWithoutProperVideoComponent(newLandingPageDto.getConfig());
        // config不满足条件
        if (StringUtils.isEmpty(newConfig)) {
            return;
        }
        // 修改落地页的配置
        newLandingPageDto.setConfig(newConfig);
        newLandingPageDto.setIsModel(IsModelEnum.NO_ARC_TEMPLATE.getCode());
        newLandingPageDto.setAvIds(Collections.emptyList());
        newLandingPageDto.setBizIds(Collections.emptyList());
        newLandingPageDto.setBizId(null);
        newLandingPageDto.setIsVideoPage(WhetherEnum.NO.getCode());
        newLandingPageDto.setHasTransition(WhetherEnum.NO.getCode());
        long templateId = this.create(operator, newLandingPageDto);
        // 发布落地页
        this.publish(templateId);
        this.insertMgkLandingPageTemplateMapping(MgkLandingPageTemplateMappingDto.builder()
                .pageId(pageId)
                .templatePageId(templateId)
                .isModel(IsModelEnum.NO_ARC_TEMPLATE.getCode())
                .accountId(operator.getOperatorId())
                .build());
    }

    private String getConfigWithoutProperVideoComponent(String config) {
        if (StringUtils.isEmpty(config) || config.startsWith("[") || !config.contains("video")) {
            return null;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(config);
            JSONArray blocks = jsonObject.getJSONArray("blocks");
            if (Objects.isNull(blocks)) {
                return null;
            }
            int currBlock = -1;
            Map<Integer, List<String>> blockComponentsNameMap = new HashMap<>();
            List<String> totalComponentNames = new ArrayList<>();
            for (Object x : blocks) {
                currBlock++;
                JSONObject block = x instanceof JSONObject ? (JSONObject) x : (JSONObject) toJSON(x);
                if (Objects.isNull(block)) {
                    continue;
                }
                JSONArray components = block.getJSONArray("components");
                if (Objects.isNull(components)) {
                    continue;
                }
                List<String> blockComponents = new ArrayList<>();
                for (Object y : components) {
                    JSONObject component = y instanceof JSONObject ? (JSONObject) y : (JSONObject) toJSON(y);
                    if (Objects.isNull(component)) {
                        continue;
                    }
                    String name = component.getString("name");
                    if (StringUtils.isEmpty(name)) {
                        name = "";
                    }
                    blockComponents.add(name);
                    totalComponentNames.add(name);
                }
                blockComponentsNameMap.put(currBlock, blockComponents);
            }
            // 只有一个视频组件且没有其它组件
            if (totalComponentNames.size() == 1 && totalComponentNames.get(0).contains("video")) {
                return null;
            }
            // 有多个视频组件或者没有视频组件
            List<String> videoNames = totalComponentNames.stream()
                    .filter(name -> name.contains("video"))
                    .collect(Collectors.toList());
            if (videoNames.size() != 1) {
                return null;
            }
            // 非置顶视频组件所属的block里还有别的组件
            for (Integer index : blockComponentsNameMap.keySet()) {
                List<String> componentNames = blockComponentsNameMap.get(index);
                for (String componentName : componentNames) {
                    if (componentName.contains("video") && componentNames.size() > 1) {
                        if (!componentName.equals("top-landscape-video") && !componentName.equals("top-portrait-video")) {
                            return null;
                        }
                    }
                }
            }
            // 去除对应block/component
            String removeTypeName = null;
            int removeBlockIndex = 0, removeComponentIndexInBlock = 0;
            for (Integer index : blockComponentsNameMap.keySet()) {
                List<String> componentNames = blockComponentsNameMap.get(index);
                int currComponentIndexInBlock = -1;
                for (String componentName : componentNames) {
                    currComponentIndexInBlock++;
                    if (componentName.contains("video")) {
                        removeTypeName = componentName;
                        removeBlockIndex = index;
                        removeComponentIndexInBlock = currComponentIndexInBlock;
                        break;
                    }
                }
                if (!StringUtils.isEmpty(removeTypeName)) {
                    break;
                }
            }
            if (!StringUtils.isEmpty(removeTypeName)) {
                if (removeTypeName.equals("plain-video") || removeTypeName.equals("full-video")) {
                    blocks.remove(removeBlockIndex);
                    return JSON.toJSONString(jsonObject);
                } else if (removeTypeName.equals("top-landscape-video") || removeTypeName.equals("top-portrait-video")) {
                    JSONObject removeBlock = blocks.getJSONObject(removeBlockIndex);
                    JSONArray removeComponents = removeBlock.getJSONArray("components");
                    removeComponents.remove(removeComponentIndexInBlock);
                    return JSON.toJSONString(jsonObject);
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public Long getMgkLandingPageTemplateWithoutArcIdByOriginPageId(Long pageId) {
        MgkLandingPageTemplateMappingPoExample exm = new MgkLandingPageTemplateMappingPoExample();
        exm.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId)
                .andIsModelEqualTo(IsModelEnum.NO_ARC_TEMPLATE.getCode());
        List<MgkLandingPageTemplateMappingPo> pos = mgkLandingPageTemplateMappingDao.selectByExample(exm);
        return CollectionUtils.isEmpty(pos) ? 0L : pos.get(0).getTemplatePageId();
    }

    private List<Long> getMgkLandingPageWithArcIds(Long startPageId, List<Integer> accountIds) {
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        MgkLandingPagePoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(LandingPageStatusEnum.PUBLISHED.getCode())
                .andPageIdGreaterThan(startPageId)
                .andTemplateStyleEqualTo(TemplateStyleEnum.APPLETS.getCode())
                .andTypeEqualTo(LandingPageTypeEnum.APPLETS.getCode())
                .andIsVideoPageEqualTo(WhetherEnum.YES.getCode())
                .andHasDpaGoodsEqualTo(WhetherEnum.NO.getCode())
                .andIsModelEqualTo(IsModelEnum.PAGE.getCode());
        if (!CollectionUtils.isEmpty(accountIds)) {
            criteria.andAccountIdIn(accountIds);
        }
        example.setOrderByClause("page_id asc");
        example.setLimit(100);
        List<MgkLandingPagePo> mgkLandingPagePos = mgkLandingPageDao.selectByExample(example);
        List<Long> queryPageIds = mgkLandingPagePos.stream()
                .map(MgkLandingPagePo::getPageId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryPageIds)) {
            return Collections.emptyList();
        }

        MgkPageFormMappingPoExample exm = new MgkPageFormMappingPoExample();
        exm.or().andPageIdIn(queryPageIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkPageFormMappingPo> formMappingPos = mgkPageFormMappingDao.selectByExample(exm);
        if (CollectionUtils.isEmpty(formMappingPos)) {
            return queryPageIds;
        }
        Map<Long, List<MgkPageFormMappingPo>> pageFormMap = formMappingPos.stream()
                .collect(Collectors.groupingBy(MgkPageFormMappingPo::getPageId));

        List<Long> queryFormIds = formMappingPos.stream()
                .map(MgkPageFormMappingPo::getFormId)
                .distinct()
                .collect(Collectors.toList());

        List<MgkFormDto> formDtos = mgkFormService.getBaseFormDtosInFormIds(queryFormIds);
        Map<Long, MgkFormDto> formIdMap = formDtos.stream()
                .collect(Collectors.toMap(MgkFormDto::getFormId, Function.identity()));

        return mgkLandingPagePos.stream()
                .filter(po -> {
                    if (pageFormMap.containsKey(po.getPageId())) {
                        List<Long> formIds = pageFormMap.get(po.getPageId()).stream()
                                .map(MgkPageFormMappingPo::getFormId)
                                .collect(Collectors.toList());
                        for (Long formId : formIds) {
                            if (formIdMap.containsKey(formId)) {
                                MgkFormDto mgkFormDto = formIdMap.get(formId);
                                if (mgkFormDto.getStatus().equals(FormStatusEnum.DELETED.getCode()) || !mgkFormDto.getAccountId().equals(po.getAccountId())) {
                                    return false;
                                }
                            }
                        }
                    }
                    return true;
                })
                .map(MgkLandingPagePo::getPageId)
                .collect(Collectors.toList());
    }

    private List<Long> getExistLandingPageTemplateMappingWithoutArc(List<Long> pageIds) {
        MgkLandingPageTemplateMappingPoExample example = new MgkLandingPageTemplateMappingPoExample();
        example.or()
                .andPageIdIn(pageIds)
                .andIsModelEqualTo(IsModelEnum.NO_ARC_TEMPLATE.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkLandingPageTemplateMappingPo> existPos = mgkLandingPageTemplateMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(existPos)) {
            return Collections.emptyList();
        }
        return existPos.stream().map(MgkLandingPageTemplateMappingPo::getPageId).collect(Collectors.toList());
    }

    public void refreshLandingPageDownloadComponentLimit(List<Integer> accountIds) {
        Long startPageId = 0L;
        List<Long> pageIdList = refreshByPage(startPageId, accountIds);
        while (!CollectionUtils.isEmpty(pageIdList)) {
            startPageId = pageIdList.get(pageIdList.size() - 1);
            pageIdList = refreshByPage(startPageId, accountIds);
        }
        log.info("refreshLandingPageDownloadComponentLimit end,last start page id :{}", startPageId);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public List<Long> refreshByPage(Long startPageId, List<Integer> accountIds) {
        MgkLandingPagePoExample exm = new MgkLandingPagePoExample();
        MgkLandingPagePoExample.Criteria criteria = exm.or().andIsModelIn(Lists.newArrayList(IsModelEnum.PAGE.getCode(), IsModelEnum.MODEL.getCode()))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdGreaterThan(startPageId);

        if (!CollectionUtils.isEmpty(accountIds)) {
            criteria.andAccountIdIn(accountIds);
        }
        exm.setOrderByClause("page_id asc");
        exm.setLimit(1000);
        List<MgkLandingPagePo> mgkLandingPagePos = mgkLandingPageDao.selectByExample(exm);
        if (CollectionUtils.isEmpty(mgkLandingPagePos)) {
            return Collections.emptyList();
        }

        List<Long> pageIdList = mgkLandingPagePos.stream()
                .map(MgkLandingPagePo::getPageId)
                .collect(Collectors.toList());

        MgkLandingPageConfigPoExample pageConfigExm = new MgkLandingPageConfigPoExample();
        pageConfigExm.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(pageIdList);
        List<MgkLandingPageConfigPo> pageConfigPos = mgkLandingPageConfigDao.selectByExampleWithBLOBs(pageConfigExm);
        Map<Long, String> pageConfigIdMap = pageConfigPos.stream()
                .collect(Collectors.toMap(MgkLandingPageConfigPo::getPageId, MgkLandingPageConfigPo::getConfig, (exist, replace) -> exist));

        MgkTemplatePageMappingPoExample templatePageExm = new MgkTemplatePageMappingPoExample();
        templatePageExm.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(pageIdList);
        List<MgkTemplatePageMappingPo> templatePageMappingPos = mgkTemplatePageMappingDao.selectByExample(templatePageExm);
        Map<Long, Long> templatePageIdMap = templatePageMappingPos.stream()
                .collect(Collectors.toMap(MgkTemplatePageMappingPo::getPageId, MgkTemplatePageMappingPo::getTemplatePageId, (exist, replace) -> exist));

        MgkLandingPageTemplateMappingPoExample pageTemplateExm = new MgkLandingPageTemplateMappingPoExample();
        templatePageExm.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(pageIdList);
        List<MgkLandingPageTemplateMappingPo> pageTemplateMappingPos = mgkLandingPageTemplateMappingDao.selectByExample(pageTemplateExm);
        Map<Long, Long> pageTemplateIdMap = pageTemplateMappingPos.stream()
                .collect(Collectors.toMap(MgkLandingPageTemplateMappingPo::getPageId, MgkLandingPageTemplateMappingPo::getTemplatePageId, (exist, replace) -> exist));

        pageIdList.forEach(pageId -> {
            String config = pageConfigIdMap.get(pageId);
            if (StringUtils.isEmpty(config)) {
                return;
            }

            MgkPageDownloadComponentHeightDto pageDownloadComponentHeightDto = mgkLandingPageConfigJudgeUtil.getDownloadComponentHeightInfo(pageId, config);
            mgkPageDownloadComponentService.updateDownloadComponentHeightInfo(pageDownloadComponentHeightDto);

            // 联投落地页
            Long templatePageId = templatePageIdMap.get(pageId);
            if (Utils.isPositive(templatePageId)) {
                pageDownloadComponentHeightDto.setPageId(templatePageId);
                mgkPageDownloadComponentService.updateDownloadComponentHeightInfo(pageDownloadComponentHeightDto);
            }

            // 图文副本落地页
            Long pageTemplateId = pageTemplateIdMap.get(pageId);
            if (Utils.isPositive(pageTemplateId)) {
                pageDownloadComponentHeightDto.setPageId(pageTemplateId);
                mgkPageDownloadComponentService.updateDownloadComponentHeightInfo(pageDownloadComponentHeightDto);
            }
        });

        return pageIdList;
    }

    public PageResult<MgkLandingPageDto> getLandingPagePVCtrForNewVersion(QueryLandingPageParamDto paramDto, Page page) throws ServiceException {
        PageResult<MgkLandingPageDto> pageResult;
        //根据更新时间排序
        if (paramDto.getSortData() == null
                || MyMgkSortDataEnum.MTIME.getCode().equals(paramDto.getSortData())
                || paramDto.getSortType() == null) {
            //先分页按照更新时间查询落地页数据
            pageResult = getPreviewLandingPageDtos(paramDto, page);
            if (pageResult.getTotal() == 0) {
                return pageResult;
            }
            pageResult.setRecords(pageResult.getRecords());
            //查询clickHouse获取pvCtr
            pageResult.setRecords(queryPvCtrFromCkAndSetting(paramDto.getLaunchBeginDate(),
                    paramDto.getLaunchEndDate(), paramDto.getAccountIdList(), pageResult.getRecords()));
            return pageResult;
        }

        //新版页面中，根据消费、点击、转化、转化率进行排序的时候,数据的前半部分是有流量的数据按照排序字段排序,
        // 数据的后半部分是无流量的数据按照更新时间逆序排序
        Map<Long, CHMgkPagePvCtrDto> pvCtrDtoMap = chStatService.getPvCtr(CHQueryStatParamDto.builder()
                .accountIds(paramDto.getAccountIdList()).pageIdList(paramDto.getPageIdList())
                .beginDate(paramDto.getLaunchBeginDate()).endDate(paramDto.getLaunchEndDate())
                .build());
        List<CHMgkPagePvCtrDto> daysAllCtrDtos = chStatService
                .sortPvCtr(paramDto.getSortData(), paramDto.getSortType(), pvCtrDtoMap);
        log.info("getLandingPagePVCtrForNewVersion sortPvCtr daysAllCtrDtos [{}]", daysAllCtrDtos);
        //查询落地页总量
        long count = mgkLandingPageDao.countByExample(getMgkLandingPagePoExample(paramDto));

        //还有些页面的查询条件比如页面类型、状态、页面名称在CH里面是没有的,最后还是要在数据库里过滤一次
        QueryLandingPageParamDto paramCopyDto = new QueryLandingPageParamDto();
        BeanUtils.copyProperties(paramDto, paramCopyDto);
        paramCopyDto.setPageIdList(daysAllCtrDtos.stream().map(CHMgkPagePvCtrDto::getPageId)
                .collect(Collectors.toList()));
        MgkLandingPagePoExample pagePoExample = getMgkLandingPagePoExample(paramCopyDto);
        List<MgkLandingPagePo> pagePos = mgkLandingPageDao.selectByExample(pagePoExample);
        if (!CollectionUtils.isEmpty(pagePos)) {
            List<Long> availablePageIds = pagePos.stream().map(MgkLandingPagePo::getPageId)
                    .collect(Collectors.toList());
            log.info("getLandingPagePVCtrForNewVersion filter availablePageIds [{}]", availablePageIds);
            daysAllCtrDtos = daysAllCtrDtos.stream().filter(chMgkPagePvCtrDto ->
                    availablePageIds.contains(chMgkPagePvCtrDto.getPageId())).collect(Collectors.toList());
        } else {
            daysAllCtrDtos = new ArrayList<>();
        }

        //当请求的页面信息数量在流量的数据范围内的时候
        int lastDataIndex = page.getPageSize() * page.getPage() - 1;
        if (lastDataIndex < daysAllCtrDtos.size()) {
            //实际需要返回给前端的数据
            List<CHMgkPagePvCtrDto> finalCtrDtos = new ArrayList<>();
            for (int i = page.getOffset(); i <= lastDataIndex; i++) {
                finalCtrDtos.add(daysAllCtrDtos.get(i));
            }
            log.info("getLandingPagePVCtrForNewVersion finalCtrDtos [{}]", finalCtrDtos);

            //查询此次需要的落地页信息
            paramDto.setPageIdList(finalCtrDtos.stream().map(CHMgkPagePvCtrDto::getPageId).collect(Collectors.toList()));
            List<MgkLandingPageDto> pageDtoS = this.getLandingPageDtosByExample(getMgkLandingPagePoExample(paramDto));

            pageDtoS = handleShadowPageList(pageDtoS);
            queryPostHandle(pageDtoS, paramDto);

            pageDtoS.forEach(dto -> {
                CHMgkPagePvCtrDto ctrDto = pvCtrDtoMap.getOrDefault(dto.getPageId(), new CHMgkPagePvCtrDto());
                dto.setPv(ctrDto.getClick());
                dto.setCtr(ctrDto.getConvCnt());
                dto.setPvCtrRate(NumberUtils.longDivideLong(ctrDto.getConvCnt(), ctrDto.getClick()));
                dto.setConsume(Utils.fromFenToYuan(Utils.fromMilliToFen(ctrDto.getCostMilli())));
            });

            Map<Long, MgkLandingPageDto> landingPageDtosMap = pageDtoS.stream().collect(Collectors
                    .toMap(MgkLandingPageDto::getPageId, Function.identity()));

            List<MgkLandingPageDto> records = new ArrayList<>();
            finalCtrDtos.forEach(t -> records.add(landingPageDtosMap.get(t.getPageId())));
            setModelName(records);
            return PageResult.<MgkLandingPageDto>builder().total(Math.toIntExact(count)).records(records).build();
        }

        //当请求的页面既包含流量又包含按更新日期逆序的的时候
        if (page.getOffset() < daysAllCtrDtos.size()) {
            //有流量部分的数据
            List<CHMgkPagePvCtrDto> flowCtrDtos = new ArrayList<>();
            for (int i = page.getOffset(); i < daysAllCtrDtos.size(); i++) {
                flowCtrDtos.add(daysAllCtrDtos.get(i));
            }
            log.info("getLandingPagePVCtrForNewVersion flowCtrDtos [{}]", flowCtrDtos);
            List<MgkLandingPageDto> flowPageDtos = getLandingPageDtoByPageIds(flowCtrDtos.stream()
                    .map(CHMgkPagePvCtrDto::getPageId).collect(Collectors.toList()));
            flowPageDtos.forEach(dto -> {
                CHMgkPagePvCtrDto ctrDto = pvCtrDtoMap.getOrDefault(dto.getPageId(), new CHMgkPagePvCtrDto());
                dto.setPv(ctrDto.getClick());
                dto.setCtr(ctrDto.getConvCnt());
                dto.setPvCtrRate(NumberUtils.longDivideLong(ctrDto.getConvCnt(), ctrDto.getClick()));
                dto.setConsume(Utils.fromFenToYuan(Utils.fromMilliToFen(ctrDto.getCostMilli())));
            });

            //无流量部分按照更新时间逆序的数据
            paramDto.setExcludePageIdList(flowCtrDtos.stream().map(CHMgkPagePvCtrDto::getPageId).collect(Collectors.toList()));
            paramDto.setLimit(page.getPageSize() - flowCtrDtos.size());
            List<MgkLandingPageDto> pageDtoS = this.getLandingPageDtosByExample(getMgkLandingPagePoExample(paramDto));

            pageDtoS = handleShadowPageList(pageDtoS);

            flowPageDtos = chStatService.sortMgkDtoPvCtr(paramDto.getSortData(), paramDto.getSortType(), flowPageDtos);
            if (!CollectionUtils.isEmpty(pageDtoS)) {
                flowPageDtos.addAll(pageDtoS);
            }
            queryPostHandle(flowPageDtos, paramDto);
            setModelName(flowPageDtos);

            return PageResult.<MgkLandingPageDto>builder().total(Math.toIntExact(count)).records(flowPageDtos).build();
        }

        //当请求的页面只包含按更新日期逆序的的时候
        if (page.getOffset() >= daysAllCtrDtos.size()) {
            int newOffset = page.getOffset() - daysAllCtrDtos.size();
            page.setOffset(newOffset);
            page.setPage(newOffset / page.getPageSize());
            paramDto.setExcludePageIdList(daysAllCtrDtos.stream().map(CHMgkPagePvCtrDto::getPageId)
                    .collect(Collectors.toList()));
            pageResult = getPreviewLandingPageDtos(paramDto, page);
            return PageResult.<MgkLandingPageDto>builder().total(Math.toIntExact(count)).records(pageResult.getRecords()).build();
        }

        return PageResult.<MgkLandingPageDto>builder().total(0).records(null).build();
    }

    public List<MgkLandingPageDto> getPagePvCtrAndLimitItems(QueryLandingPageParamDto paramDto) {
        MgkLandingPagePoExample example = this.getMgkLandingPagePoExample(paramDto);
        long total = mgkLandingPageDao.countByExample(example);
        Assert.isTrue(total < 1000, "请求的数据太多");
        if (total == 0) {
            return new ArrayList<>();
        }

        List<MgkLandingPageDto> landingPageDtos = this.getLandingPageDtosByExample(example);
        landingPageDtos = handleShadowPageList(landingPageDtos);

        // 设置落地页pv ctr 已推全
//        if (!MyMgkPageDataVersionEnum.NEW.getCode().equals(paramDto.getPageVersion())) {
////            return queryPvCtrFromESAndSetting(landingPageDtos);
//            Cat.logEvent("pageVersion", "old");
//            return Collections.emptyList();
//        } else {
//            return queryPvCtrFromCkAndSetting(paramDto.getLaunchBeginDate(), paramDto.getLaunchEndDate(),
//                    paramDto.getAccountIdList(), landingPageDtos);
//        }
        return queryPvCtrFromCkAndSetting(paramDto.getLaunchBeginDate(), paramDto.getLaunchEndDate(),
                paramDto.getAccountIdList(), landingPageDtos);
    }

//    @Deprecated
//    private List<MgkLandingPageDto> queryPvCtrFromESAndSetting(List<MgkLandingPageDto> pageDtoS) {
//        //新版落地页的pv、ctr数据从clickHouse里面取,es只能取到7日内的
//        List<MgkPagePvCtrDto> pvCtrByMgkPageIds = mgkOnlineHomeService.getPvCtrByMgkPageIds(pageDtoS.stream()
//                .map(MgkLandingPageDto::getPageId).collect(Collectors.toList()));
//        if (!CollectionUtils.isEmpty(pvCtrByMgkPageIds)) {
//            ImmutableMap<Long, MgkPagePvCtrDto> mgkPagePvCtrDtoImmutableMap = Maps.uniqueIndex(pvCtrByMgkPageIds, MgkPagePvCtrDto::getPageId);
//            pageDtoS.forEach(dto -> {
//                MgkPagePvCtrDto mgkPagePvCtrDto = mgkPagePvCtrDtoImmutableMap.get(dto.getPageId());
//                dto.setPv(mgkPagePvCtrDto.getPv());
//                dto.setCtr(mgkPagePvCtrDto.getCtr());
//                dto.setPvCtrRate(mgkPagePvCtrDto.getPvCtrRate());
//            });
//        }
//        setModelName(pageDtoS);
//        return pageDtoS;
//    }

    private void setModelName(List<MgkLandingPageDto> pageDtoS) {
        // 查询模板
        List<MgkModelDto> modelDtos = modelServiceDelegate.getMgkModelDtosByIds(pageDtoS.stream()
                .map(MgkLandingPageDto::getModelId).collect(Collectors.toList()));
        Map<Long, String> modelMap = modelDtos.stream().collect(Collectors
                .toMap(MgkModelDto::getModelId, MgkModelDto::getModelName));
        pageDtoS.forEach(dto -> dto.setModelName(modelMap.getOrDefault(dto.getModelId(), "自定义")));
    }

    private List<MgkLandingPageDto> queryPvCtrFromCkAndSetting(String beginDate, String endDate,
                                                               List<Integer> accountIds,
                                                               List<MgkLandingPageDto> pageDtoS) {
        // 查询模板
        setModelName(pageDtoS);

        //查询clickHouse获取pvCtr
        Map<Long, CHMgkPagePvCtrDto> pvCtrDtoMap = chStatService.getPvCtr(CHQueryStatParamDto.builder()
                .accountIds(accountIds).beginDate(beginDate).endDate(endDate)
                .pageIdList(pageDtoS.stream().map(MgkLandingPageDto::getPageId).collect(Collectors.toList()))
                .build());
        if (CollectionUtils.isEmpty(pvCtrDtoMap)) {
            return pageDtoS;
        }

        //塞值
        pageDtoS.forEach(dto -> {
            CHMgkPagePvCtrDto ctrDto = pvCtrDtoMap.getOrDefault(dto.getPageId(),
                    new CHMgkPagePvCtrDto());
            dto.setPv(ctrDto.getClick());
            dto.setCtr(ctrDto.getConvCnt());
            dto.setPvCtrRate(NumberUtils.longDivideLong(ctrDto.getConvCnt(), ctrDto.getClick()));
            dto.setConsume(Utils.fromFenToYuan(Utils.fromMilliToFen(ctrDto.getCostMilli())));
        });
        return pageDtoS;
    }

    private void saveLandingPageGame(Long mgkPageId, List<GameDto> games) {
        Assert.isTrue(Utils.isPositive(mgkPageId), "mgkPageId must not be null");
        this.deleteLandingPageGame(Lists.newArrayList(mgkPageId));
        if (!CollectionUtils.isEmpty(games)) {
            List<MgkLandingPageGamePo> gamePoList = games.stream()
                    .map(game -> MgkLandingPageGamePo.builder()
                            .gameBaseId(game.getGameBaseId())
                            .channelId(game.getChannelId())
                            .pageId(mgkPageId)
                            .isDeleted(IsDeleted.VALID.getCode())
                            .ctime(new Timestamp(System.currentTimeMillis()))
                            .mtime(new Timestamp(System.currentTimeMillis()))
                            .build())
                    .collect(Collectors.toList());
            this.mgkLandingPageGameDao.insertBatch(gamePoList);
        }
    }

    private void deleteLandingPageGame(List<Long> mgkPageIdList) {
        if (CollectionUtils.isEmpty(mgkPageIdList)) {
            return;
        }
        MgkLandingPageGamePoExample example = new MgkLandingPageGamePoExample();
        example.createCriteria()
                .andPageIdIn(mgkPageIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        this.mgkLandingPageGameDao.updateByExampleSelective(MgkLandingPageGamePo.builder()
                        .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }

    public Map<Long, List<GameDto>> getLandingPageGames(List<Long> mgkPageIdList) {
        if (CollectionUtils.isEmpty(mgkPageIdList)) {
            return Collections.emptyMap();
        }
        MgkLandingPageGamePoExample example = new MgkLandingPageGamePoExample();
        example.createCriteria()
                .andPageIdIn(mgkPageIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkLandingPageGamePo> gamePoList = this.mgkLandingPageGameDao.selectByExample(example);
        Map<Long, List<MgkLandingPageGamePo>> gameMap = gamePoList.stream().collect(Collectors.groupingBy(MgkLandingPageGamePo::getPageId));
        Map<Long, List<GameDto>> result = Maps.newHashMap();
        gameMap.forEach((pageId, games) -> {
            result.put(pageId, games.stream()
                    .map(game -> GameDto.builder()
                            .gameBaseId(game.getGameBaseId())
                            .channelId(game.getChannelId())
                            .build())
                    .collect(Collectors.toList()));
        });
        return result;
    }
}
