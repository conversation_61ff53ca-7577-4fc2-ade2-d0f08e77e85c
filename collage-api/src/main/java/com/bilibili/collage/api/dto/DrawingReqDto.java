package com.bilibili.collage.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/12/11
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DrawingReqDto implements Serializable {

    private static final long serialVersionUID = 5170540019649008885L;

    // 智能作图功能分类ID 1-制作行业广告 2-制作商品广告
    private Integer modelId;

    // 行业ID
    private Integer industryId;

    // 素材个数
    private Integer mateCount;

    // 广告尺寸ID
    private Integer adSizeId;

    // 主素材ID集合
    private List<Integer> mainMateId;

    // LOGO图url
    private String logoUrl;

    // 主标题
    private String mainTitle;

    // 副标题
    private String subTitle;

    // 其他文字
    private String otherWords;

    // 模版标签id
    private Integer tagId;

    // 页码
    private Integer page;

    // 每页大小
    private Integer size;

    // 本次作图的唯一标识
    private String requestId;
}
