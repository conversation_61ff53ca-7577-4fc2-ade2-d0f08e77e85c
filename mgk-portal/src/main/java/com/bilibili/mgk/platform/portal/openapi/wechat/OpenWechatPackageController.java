package com.bilibili.mgk.platform.portal.openapi.wechat;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountInfoDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatPackageInfoDto;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatPackageService;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.common.OpenApiBasicController;
import com.bilibili.mgk.platform.portal.openapi.bean.OpenApiResponse;
import com.bilibili.mgk.platform.portal.openapi.wechat.vo.WechatPackageInfoVo;
import com.bilibili.mgk.platform.portal.service.WebWechatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Random;

/**
 * @ClassName OpenWechatPackageController
 * <AUTHOR>
 * @Date 2022/6/16 4:39 下午
 * @Version 1.0
 **/
@RestController
@RequestMapping("/open_api/v1/wechat/package")
@Api(value = "/wechat/package", description = "外网微信包接口")
public class OpenWechatPackageController extends OpenApiBasicController {

    @Autowired
    private IMgkWechatPackageService mgkWechatPackageService;

    @ApiOperation(value = "外网-获取微信包信息")
    @RequestMapping(value = "/{wechat_package_id}", method = RequestMethod.GET)
    public
    @ResponseBody
    OpenApiResponse<WechatPackageInfoVo> getWechatPackageNames(@ApiIgnore Context context,
                                                                       @PathVariable(value = "wechat_package_id") Integer wechatPackageId) throws ServiceException {
        try {
            WechatPackageInfoDto infoDto = mgkWechatPackageService.getWechatPackageInfoByPackageId(wechatPackageId);
            WechatAccountInfoDto randomInfoDto = infoDto.getWechatAccounts().get(new Random().nextInt(infoDto.getWechatAccounts().size()));
            return OpenApiResponse.SUCCESS(WebWechatService.convertWechatPackageInfoDto2Vo(infoDto, randomInfoDto));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(500, e.getMessage());
        }
    }

}
