package com.bilibili.mgk.material.center.service.grpc;

import com.bapis.ad.mgk.audit.BatchAuditReq;
import com.bapis.ad.mgk.audit.BatchAuditResp;
import com.bapis.ad.mgk.audit.DynamicBlueLinkAuditServiceGrpc;
import com.bapis.ad.mgk.audit.DynamicReply;
import com.bapis.ad.mgk.audit.DynamicReq;
import com.bilibili.mgk.material.center.service.bluelink.DynamicBluelinkAuditService;
import com.bilibili.mgk.material.center.service.bluelink.converter.DynamicBlueLinkConverter;
import com.bilibili.mgk.material.center.service.bluelink.model.BluelinkAuditStatus;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicBlueLinkAuditBo;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicBlueLinkDetailsBo;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicBluelinkResolveContent;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicComponentAuditWithBluelink;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DynamicBluelinkAuditGrpcService extends DynamicBlueLinkAuditServiceGrpc.DynamicBlueLinkAuditServiceImplBase {

    @Autowired
    private DynamicBluelinkAuditService dynamicBluelinkAuditService;

    @Override
    public void getDynamicBlueLinks(DynamicReq request, StreamObserver<DynamicReply> responseObserver) {

        log.info("request={}", request);
        try {
            Pagination<List<DynamicComponentAuditWithBluelink>> result =
                    dynamicBluelinkAuditService.getDynamicBlueLinks(DynamicBlueLinkConverter.MAPPER.toQueryBo(request));

            // refactor：, 在业务层使用统一的boDynamicComponentAuditWithBluelink，
            // 而不是经过各种序列化导致数据失真的DynamicBlueLinkDetailsBo
            Pagination<List<DynamicBlueLinkDetailsBo>> dynamicBlueLinks = result.map(audits -> {

                return audits.stream().map(audit -> {
                    DynamicBlueLinkDetailsBo x = DynamicBlueLinkConverter.MAPPER.toDetailBo(audit.getAudit());

                    List<DynamicBluelinkResolveContent> contentsTmp =
                            Optional.ofNullable(audit.getBluelinks()).orElse(new ArrayList<>());
                    if (!CollectionUtils.isEmpty(contentsTmp)) {
                        List<DynamicBlueLinkDetailsBo.JumpUrl> jumpUrls = DynamicBlueLinkConverter.MAPPER.toJumpUrls(
                                contentsTmp);
                        x.setUrls(jumpUrls);
                        DynamicBlueLinkConverter.fillDetailsBo(x, contentsTmp.get(0));
                        List<Integer> qualificationIds = contentsTmp.stream()
                                .map(DynamicBluelinkResolveContent::getQualificationIds)
                                .filter(y -> !CollectionUtils.isEmpty(contentsTmp))
                                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
                        x.setQualificationIds(qualificationIds);
                    }

                    return x;
                }).collect(Collectors.toList());
            });

            responseObserver.onNext(DynamicReply.newBuilder().setTotal(dynamicBlueLinks.getTotal_count())
                    .addAllBlueLinkDetails(Optional.ofNullable(DynamicBlueLinkConverter.MAPPER.toDynamicBlueLinkDetails(dynamicBlueLinks.getData())).orElse(Collections.emptyList())).build());
            responseObserver.onCompleted();
        }catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: getDynamicBlueLinks 失败,{}", request,  ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:getDynamicBlueLinks 失败,{}", request,  ExceptionUtils.getSubStringMsg(t));
        }

    }

    @Override
    public void auditPass(BatchAuditReq request, StreamObserver<BatchAuditResp> responseObserver) {
        try {
            DynamicBlueLinkAuditBo auditBo = DynamicBlueLinkConverter.MAPPER.toAuditBo(request);
            auditBo.setAuditStatus(BluelinkAuditStatus.AUDIT_PASSED.getCode());
            String auditResult = dynamicBluelinkAuditService.audit(auditBo);

            responseObserver.onNext(BatchAuditResp.newBuilder().setMessage(auditResult)
                    .setIsSuccess(DynamicBluelinkAuditService.SUCCESS.equals(auditResult) ? 1: 0).build());
            responseObserver.onCompleted();
        }catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: auditPass 失败,{}", request,  ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:auditPass 失败,{}", request,  ExceptionUtils.getSubStringMsg(t));
        }
    }

    @Override
    public void auditReject(BatchAuditReq request, StreamObserver<BatchAuditResp> responseObserver) {
        try {
            DynamicBlueLinkAuditBo auditBo = DynamicBlueLinkConverter.MAPPER.toAuditBo(request);
            auditBo.setAuditStatus(BluelinkAuditStatus.AUDIT_REJECTED.getCode());
            String auditResult = dynamicBluelinkAuditService.audit(auditBo);
            responseObserver.onNext(BatchAuditResp.newBuilder().setMessage(auditResult)
                    .setIsSuccess(DynamicBluelinkAuditService.SUCCESS.equals(auditResult) ? 1: 0).build());
            responseObserver.onCompleted();
        }catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: auditPass 失败,{}", request,  ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:auditPass 失败,{}", request,  ExceptionUtils.getSubStringMsg(t));
        }
    }
}
