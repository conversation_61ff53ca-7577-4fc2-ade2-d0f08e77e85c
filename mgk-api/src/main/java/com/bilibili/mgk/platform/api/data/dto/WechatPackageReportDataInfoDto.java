package com.bilibili.mgk.platform.api.data.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @ClassName WechatPackageReportDataInfoDto
 * <AUTHOR>
 * @Date 2022/9/21 3:23 下午
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatPackageReportDataInfoDto implements Serializable {
    private static final long serialVersionUID = -68892312312318052L;
    private Long pageId;
    private String title;
    private Long creativeId;
    private String creativeTitle;
    private Integer unitId;
    private String unitName;
    private Integer campaignId;
    private String campaignName;
    private String salesTypeDesc;
    private Integer wechatPackageId;
    private String wechatPackageName;
    private Integer wechatAccountId;
    private String wechatAccountName;
    private String trackId;
    private String submitDate;
    private String dataType;
    private Long avid;

    /**
     * 微信包类型 -1 不存在 0-个人号 1-公众号 2-企业微信 3-其他
     */
    private Integer type;
    //微信包创建时间
    private Timestamp wechatPackageCtime;

    private Long linkDataId;

    private Timestamp ctime;
}
