package com.bilibili.mgk.platform.portal.core;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Collections;

/**
 * @ClassName Pagination
 * <AUTHOR>
 * @Date 2022/6/19 3:59 下午
 * @Version 1.0
 **/
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class Pagination<E> {
    private Integer page;
    private Integer total_count;
    private E data;
    private static final Pagination EMPTY = new Pagination<>(1, 0, Collections.emptyList());

    public Pagination(Integer page, Integer total_count, E data) {
        this.page = page;
        this.total_count = total_count;
        this.data = data;
    }

    public static <E> Pagination<E> emptyPagination() {
        return EMPTY;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getTotal_count() {
        return total_count;
    }

    public void setTotal_count(Integer total_count) {
        this.total_count = total_count;
    }

    public E getData() {
        return data;
    }

    public void setData(E data) {
        this.data = data;
    }

}

