package com.bilibili.mgk.platform.common.utils;

/**
 * @ClassName ShaUtil
 * <AUTHOR>
 * @Date 2022/5/18 7:40 下午
 * @Version 1.0
 **/

import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;

import java.nio.charset.StandardCharsets;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * @ClassName HmacShaUtil
 * <AUTHOR>
 * @Date 2022/5/18 7:11 下午
 * @Version 1.0
 **/
public class HmacShaUtil {
    public static String getHmacSha256Str(String str, String secret) {
        HmacUtils hmacUtils = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, secret.getBytes(StandardCharsets.UTF_8));
        return hmacUtils.hmacHex(str.getBytes(StandardCharsets.UTF_8));
    }

    public static void main(String[] args) {
        System.out.println(System.currentTimeMillis());
        System.out.println(getHmacSha256Str("1&1653893394&2022-05-25_00-00-00&2022-05-30_17-00-00&500&849489", "69c24535-9cc6-4c33-9ddf-ba96e8fe5f32"));
    }

}

