package com.bilibili.mgk.material.center.service.bluelink.model;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/16
 */
@Data
@Accessors(chain = true)
public class DynamicBluelinkResolveContent {

    private Long id;

    private Boolean deleted;

    private LocalDateTime ctime;

    private LocalDateTime mtime;

    private LocalDateTime triggerTime;


    private Integer accountId;

    private Integer customerId;


    private Integer agentId;


    private String upNickname;

    private Long mid;

    private String upFace;


    /**
     * {@link ComponentDisplayStatus}
     */
    private Integer status;

    private Integer auditStatus;

    /**
     * audit reason原因（审核拒绝时填写）
     */
    private String reason;

    private Integer creativeId;

    private Integer unitId;

    private Integer campaignId;

    ////////////////

    private String rawText;

    @ApiModelProperty(value = "蓝链序号，普通文本不参与排序， 注意是从1开始, 前端无需填写，后端会自动补充")
    private Integer bluelinkSeqId;

    private Integer textSegmentId;

    private String dynamicBizId;

    private Long auditRecordId;

    private Integer dynamicStatus;

    /**
     * 一律认为dynamicId是string即可
     */
    private String dynamicId;

    private String dynamicContent;

    ///////////////////以下为蓝链的正常解析内容////////////////////////////////
    //////////////////////////


    @ApiModelProperty(value = "普通链接文案")
    private String generalCommentText;

    @ApiModelProperty(value = "转化链文案")
    private String conversionUrlText;

    @ApiModelProperty(value = "转化组件类型2-线索4-游戏6-应用9-商品")
    private Integer componentType;
    @ApiModelProperty(value = "应用子类型, 1-应用下载, 2-应用唤起")
    private Integer appSubType;

    @ApiModelProperty(value = "转化链链接(兜底链接)类型，1-三方落地页 5-高能建站落地页")
    private Integer conversionUrlType;

    @ApiModelProperty(value = "转化链链接(兜底链接)高能建站落地页page_id, 针对type = 2")
    private Long conversionUrlPageId;


    @ApiModelProperty(value = "转化链连接(兜底链接)")
    private String conversionUrl;

    @ApiModelProperty(value = "ios链接类型，1-三方落地页 2-高能建站落地页")
    private Integer iosUrlType;

    @ApiModelProperty(value = "ios链接类型高能建站落地页page_id, 针对type = 2")
    private Long iosUrlPageId;

    @ApiModelProperty(value = "ios链接")
    private String iosUrl;

    @ApiModelProperty(value = "android链接类型，1-三方落地页 2-高能建站落地页")
    private Integer androidUrlType;

    @ApiModelProperty(value = "android链接高能建站落地页page_id, 针对type = 2")
    private Long androidUrlPageId;

    @ApiModelProperty(value = "android链接")
    private String androidUrl;

    @ApiModelProperty(value = "游戏id")
    private Integer gameBaseId;

    private Integer gamePlatformType;

    @ApiModelProperty(value = "ios app 包id")
    private Integer iosAppPackageId;

    @ApiModelProperty(value = "android app 包id")
    private Integer androidAppPackageId;

    @ApiModelProperty(value = "统一设置唤起链接(android和ios同一个)")
    private String schemaUrl;

    @ApiModelProperty(value = "ios 唤起链接")
    private String iosSchemaUrl;

    @ApiModelProperty(value = "android 唤起链接")
    private String androidSchemaUrl;

    @ApiModelProperty(value = "文案位置 0-转化链在前 1-评论在前 2-居中")
    private Integer textLocation;

    @ApiModelProperty(value = "资质")
    private List<Integer> qualificationIds;



    //以下两个字段仅在componentType等于1时生效
    //https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002893909
    @ApiModelProperty(value = "线索类型 0-落地页 1-评论浮层表单 2-微信浮层")
    private Integer clueType;

    @ApiModelProperty(value = "线索数据 目前可以存储:评论浮层表单id、微信浮层微信包id、微信小游戏id")
    private String clueData;

    @ApiModelProperty("是否广告包: 0-联运包, 1-广告包")
    private Integer subPkg;

    @ApiModelProperty("是否是mapi请求")
    private Boolean isMapiRequest;

    @ApiModelProperty("展示监控")
    private String customizedImpUrl;

    @ApiModelProperty("点击监控")
    private String customizedClickUrl;

    @ApiModelProperty("商品id")
    private Long productId;

    private String productShortUrl;

    @ApiModelProperty("普通链接文案扩展")
    private String generalCommentTextExt;


    /**
     *
     */
//    @ApiModelProperty("商品id列表，对于一个链接一个商品而言，这里不需要批量")
//    private List<Long> productIds;

    @ApiModelProperty("是否安卓应用商店直投: 0-不直投, 1-直投")
    private Integer isAndroidAppDirect;

//    @ApiModelProperty(value = "avids")
//    private List<Long> avids;


    @ApiModelProperty("是否是覆盖 0-新增 1-覆盖")
    private Integer isCover;

    private String conversionShortUrl;

    private String autoFillText;

    private String autoFillLink;

    private Integer bizCode;

    private Integer contactType;

    private String contentInEdit;

    private Integer auditStatusInEdit;

    @ApiModelProperty("链接图标，如果为空，引擎侧需要按照目前的逻辑根据广告主进行生成")
    private String linkIcon;

    @ApiModelProperty("是否是自动化投稿")
    private String capsuleIcon;

    @ApiModelProperty("是否是自动化投稿")
    private String capsuleText;


}
