package com.bilibili.mgk.platform.portal.webapi.material.vo;

import com.bilibili.mgk.material.center.service.bluelink.dto.RichTextType;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/31
 */
@Data
@Accessors(chain = true)
public class RichTextDetailDTO implements SnakeCaseBody {


    /**
     * {@link  RichTextType}
     */
    @ApiModelProperty("富文本类型, 0: 文本, 1: 蓝链")
    private Integer richTextType;

    @ApiModelProperty(value = "蓝链内容，类型richTypeType=2时必填")
    private DynamicBluelinkResolveContentDetailDTO bluelink;

    @ApiModelProperty(value = "文本内容,对于蓝链也要填写这个文案，是作为蓝链的兜底文案使用。")
    private String rawText;

    @ApiModelProperty(value = "创建时无需提供，文本序号，普通文本也参与排序， 注意是从1开始")
    private Integer textSegmentId;
}
