package com.bilibili.collage.biz;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

@Slf4j
public class BaseMockitoTest extends MockitoDefaultTest {

    protected Operator operator;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        operator = Operator.builder()
                .operatorId(1)
                .operatorName("1")
                .operatorType(OperatorType.OPERATING_PERSONNEL)
                .ip("666.666.666.666")
                .build();
    }

    @Test
    public void defaultTest() {
        System.out.println("~~~开心就好~~~");
    }

    protected void print(Object object) {
        log.info(JSON.toJSONString(object));
    }
}
