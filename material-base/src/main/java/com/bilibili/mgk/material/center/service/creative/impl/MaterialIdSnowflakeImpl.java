package com.bilibili.mgk.material.center.service.creative.impl;

import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.databus.base.Message;
import com.bilibili.databus.core.DataBusClient;
import com.bilibili.mgk.material.center.event.MaterialIdRegisterEvent;
import com.bilibili.mgk.material.center.repository.base.MgkMaterialIdReferenceMapper;
import com.bilibili.mgk.material.center.repository.base.MgkMaterialIdRegistryMapper;
import com.bilibili.mgk.material.center.service.creative.MaterialIdService;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdRegistry;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialIdRegisterReq;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialIdRegisterResp;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceDeleteReq;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceDeleteResp;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceSearchReq;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceUpdateReq;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceUpdateResp;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Joiner;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.vavr.Tuple3;
import io.vavr.control.Try;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Primary;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 这一版的实现相较于直接使用： 类型prefix+类型id(图片md5、视频avid） 的主要区别在于 （1） 使用snowflake数字id （2） 对于视频不再使用avid，而是使用视频md5，
 * 这意味着相同内容但是重复上传（不同avid） 的视频拥有相同的素材id （3）
 *
 * <AUTHOR>
 * @desc
 * @date 2024/5/13
 */
@Service
@Primary
@Slf4j
public class MaterialIdSnowflakeImpl implements MaterialIdService {

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;
    @Resource
    private MgkMaterialIdReferenceMapper materialIdReferenceMapper;
    @Resource
    private MgkMaterialIdRegistryMapper materialIdRegistryMapper;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private DataBusClient materialIdRegisterSubClient;

    @Resource
    private DataBusClient materialIdRegisterPubClient;

    @PostConstruct
    public void initEventConsumer() {
        Thread createFlyAccSub = new Thread(() -> {
            log.info("materialIdRegisterSubClient is running");
            try {
                while (true) {
                    Message message = materialIdRegisterSubClient.take();
                    if (message == null) {
                        continue;
                    }
                    String origin = null;
                    try {
                        origin = new String(message.getValue(), StandardCharsets.UTF_8);
                        log.info("Accept materialIdRegister event Key:{} Data:{}", message.getKey(), origin);

                    } catch (Exception e) {
                        log.error("autoCreateFlyAccSub msg [{}] error [{}]", origin, e);
                    }
                    //ack
                    materialIdRegisterSubClient.ack(message);
                }
            } catch (Exception e) {
                log.error("materialIdRegisterSubClient error" + Throwables.getStackTraceAsString(e));
            }
        });
        createFlyAccSub.start();
    }

    @EventListener(MaterialIdRegisterEvent.class)
    public void broadcastMaterialIdRegisterEvent(MaterialIdRegisterEvent event) {

        Try.run(() -> {

            MaterialIdRegisterResp registerResp = MaterialIdRegisterResp.fromRegistryAndReference(
                    event.getRegistry(), event.getReference(), event.getMaterialExisted(), event.getReferenceExisted()
            );

            Message message = new Message(
                    registerResp.getAccountId(),
                    JsonUtil.writeValueAsString(registerResp).getBytes(),
                    new HashMap<>()
            );

            materialIdRegisterPubClient.pub(message);

        }).onFailure(t -> {
            log.error("Fail to broadcast material-id register event, event={}", event, t);
        });

    }


    /**
     * 仅支持单个注册
     */
    @Override
    public MaterialIdRegisterResp register(MaterialIdRegisterReq request) {
        log.info("Accept register request={}", request);
        return checkRegistryAndCreateReference(request);
    }


    @Override
    public MaterialReferenceDeleteResp deleteReference(MaterialReferenceDeleteReq req) {
        log.info("Accept delete reference request={}", req);

        req.validate();

        Tuple3<String, String, String> params = req.selectMaterialIdOrTypeUk();

        Optional<MaterialIdReference> reference = Optional.ofNullable(
                materialIdReferenceMapper.selectByMaterialIdAndReferenceUk(params._1, params._2, params._3,
                        req.getReferenceUk(), false));

        if (!reference.isPresent()) {
            return new MaterialReferenceDeleteResp()
                    .setMaterialUk(req.getMaterialUk())
                    .setMaterialId(req.getMaterialId())
                    .setMaterialIdType(req.getMaterialIdType())
                    .setReferenceUk(req.getReferenceUk())
                    .setReferenceExisted(false)
                    .setDeleteSuccess(false);
        }

        int r = materialIdReferenceMapper.updateByPrimaryKeySelective(
                new MaterialIdReference()
                        .setId(reference.get().getId())
                        .setDeleted(true)
        );

        return new MaterialReferenceDeleteResp()
                .setMaterialUk(reference.get().getMaterialUk())
                .setMaterialId(reference.get().getMaterialId())
                .setMaterialIdType(reference.get().getMaterialIdType())
                .setReferenceUk(reference.get().getReferenceUk())
                .setReferenceExisted(true)
                .setDeleteSuccess(r == 1);

    }


    @Override
    public MaterialReferenceUpdateResp updateReference(MaterialReferenceUpdateReq req) {
        log.info("Accept update reference request={}", req);

        req.validate();

        Tuple3<String, String, String> params = req.selectMaterialIdOrTypeUk();

        Optional<MaterialIdReference> reference = Optional.ofNullable(
                materialIdReferenceMapper.selectByMaterialIdAndReferenceUk(params._1, params._2, params._3,
                        req.getReferenceUk(), false));

        if (!reference.isPresent()) {
            return new MaterialReferenceUpdateResp()
                    .setMaterialUk(req.getMaterialUk())
                    .setMaterialId(req.getMaterialId())
                    .setMaterialIdType(req.getMaterialIdType())
                    .setReferenceUk(req.getReferenceUk())
                    .setReferenceExisted(false)
                    .setUpdateSuccess(false);
        }

        int r = materialIdReferenceMapper.updateByPrimaryKeySelective(
                new MaterialIdReference()
                        .setId(reference.get().getId())
                        .setName(req.getName())
                        .setContent(req.getContent())
                        .setSearchWord(Optional.ofNullable(req.getName())
                                .map(name -> buildSearchWord(reference.get().getMaterialId(), req.getName()))
                                .orElse(null)
                        )

        );

        return new MaterialReferenceUpdateResp()
                .setMaterialUk(reference.get().getMaterialUk())
                .setMaterialId(reference.get().getMaterialId())
                .setMaterialIdType(reference.get().getMaterialIdType())
                .setReferenceUk(reference.get().getReferenceUk())
                .setReferenceExisted(true)
                .setUpdateSuccess(r == 1);

    }

    @Override
    public Map<String, MaterialIdRegistry> findByTypeAndUks(String materialIdType, List<String> materialUks) {

        if (CollectionUtils.isEmpty(materialUks)) {
            return Collections.emptyMap();
        }

//        log.info("Accept find by type and uks, materialIdType={}, materialUks={}", materialIdType, materialUks);
        List<MaterialIdRegistry> values = materialIdRegistryMapper.selectAllByIdAndTypeAndUks(
                null, materialIdType, materialUks);

        return values.stream().collect(Collectors.toMap(
                MaterialIdRegistry::getMaterialUk,
                Function.identity(), (a, b) -> a
        ));
    }

    @Override
    public Pagination<List<MaterialIdReference>> searchReference(MaterialReferenceSearchReq req) {
        log.info("Accept search reference request={}", req);

        if (CollectionUtils.isEmpty(req.getAccountIds())) {
            log.info("Cannot search reference with no account info, return empty");
            return Pagination.emptyPagination();
        }

//        Assert.isTrue(StringUtils.isNotEmpty(req.getAccountIds()), "account_id cannot empty");

        Page<MaterialIdReference> page = PageHelper.startPage(req.getPn(), req.getPs())
                .doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        materialIdReferenceMapper.selectAllByAccountIdEqAndMaterialIdInAndNameLike(
                                req.getAccountIds(),
                                req.getMaterialIds(),
                                req.getName(),
                                req.getSearchWord(),
                                false
                        );
                    }
                });

        return Pagination.fromPageHelper(page);
    }

    @Override
    public Map<String, MaterialIdRegistry> findByMaterialIds(List<String> materialIds) {

        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyMap();
        }

//        log.info("Accept find by material ids, materialIds={}", materialIds);

        List<MaterialIdRegistry> values = materialIdRegistryMapper.selectAllByIdAndTypeAndUks(materialIds, null, null);

        return values.stream().collect(Collectors.toMap(
                MaterialIdRegistry::getMaterialId,
                Function.identity(), (a, b) -> a
        ));

    }


    private MaterialIdRegisterResp checkRegistryAndCreateReference(
            MaterialIdRegisterReq req) {

        Optional<MaterialIdRegistry> registry = Optional.ofNullable(
                this.findByTypeAndUks(req.getMaterialIdType(), Lists.newArrayList(req.getMaterialUk()))
                        .get(req.getMaterialUk()));

        boolean materialExisted = registry.isPresent();

        if (!registry.isPresent()) {

            MaterialIdRegistry insertOne = req.newMaterialIdRegistry(String.valueOf(snowflakeIdWorker.nextId()));

            materialIdRegistryMapper.insertSelective(insertOne);

            registry = Optional.of(insertOne);
        }

        MaterialIdReference reference = req.newMaterialIdReference(
                registry.get().getMaterialId(),
                Optional.ofNullable(req.getReferenceUk())
                        .filter(StringUtils::isNotBlank)
                        .orElse(String.valueOf(snowflakeIdWorker.nextId())),
                buildSearchWord(registry.get().getMaterialId(), req.getName()));

        Optional<MaterialIdReference> existedReference = Optional.ofNullable(materialIdReferenceMapper
                .selectByMaterialIdAndReferenceUk(reference.getMaterialId(), null, null, reference.getReferenceUk(),
                        null));

        boolean referenceExisted = existedReference.isPresent() && !existedReference.get().getDeleted();

        if (!existedReference.isPresent()) {
            materialIdReferenceMapper.insertSelective(reference);
        } else if (existedReference.get().getDeleted()) {

            materialIdReferenceMapper.updateByPrimaryKeySelective(
                    new MaterialIdReference().setId(existedReference.get().getId()).setDeleted(false));

            reference = existedReference.get();
            reference.setDeleted(false);

        } else {
            reference = existedReference.get();
            if (req.getForceUpdate()) {

                materialIdReferenceMapper.updateByPrimaryKeySelective(
                        new MaterialIdReference()
                                .setId(existedReference.get().getId())
                                // 支持将name覆盖为空
                                .setName(req.getName())
                                .setContent(req.getContent())
                                .setSearchWord(buildSearchWord(
                                        registry.get().getMaterialId(),
                                        Optional.ofNullable(req.getName())
                                                .orElse(reference.getName())))
                );

            }
        }

        applicationEventPublisher.publishEvent(new MaterialIdRegisterEvent(this)
                .setRegistry(registry.get())
                .setReference(reference)
                .setMaterialExisted(materialExisted)
                .setReferenceExisted(referenceExisted)
        );

        return MaterialIdRegisterResp.fromRegistryAndReference(registry.get(),
                reference, materialExisted, referenceExisted);

    }


    private String buildSearchWord(String materialId, String materialName) {
        return Joiner.on("@_@").join(
                Optional.ofNullable(materialId).orElse(""),
                Optional.ofNullable(materialName).orElse(""));
    }


}
