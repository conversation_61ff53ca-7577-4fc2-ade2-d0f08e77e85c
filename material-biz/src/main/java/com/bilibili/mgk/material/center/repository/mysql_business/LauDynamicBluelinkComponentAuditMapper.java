package com.bilibili.mgk.material.center.repository.mysql_business;

import com.bilibili.mgk.material.center.repository.model.LauDynamicBluelinkComponentAuditExample;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicBluelinkComponentAudit;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/19
 */
public interface LauDynamicBluelinkComponentAuditMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(DynamicBluelinkComponentAudit record);

    DynamicBluelinkComponentAudit selectByPrimaryKey(Long id);

    DynamicBluelinkComponentAudit selectByDynamicId(
            @Param("dynamicId") String dynamicId);

    List<DynamicBluelinkComponentAudit> selectByDynamicIds(
            @Param("dynamicIds") List<String> dynamicId);

    int updateByPrimaryKeySelective(DynamicBluelinkComponentAudit record);


    /****
     * examples
     * ***/
    long countByExample(LauDynamicBluelinkComponentAuditExample example);

    int deleteByExample(LauDynamicBluelinkComponentAuditExample example);


    List<DynamicBluelinkComponentAudit> selectByExample(LauDynamicBluelinkComponentAuditExample example);


    int updateByExampleSelective(@Param("record") DynamicBluelinkComponentAudit record,
            @Param("example") LauDynamicBluelinkComponentAuditExample example);

    int updateByExample(@Param("record") DynamicBluelinkComponentAudit record,
            @Param("example") LauDynamicBluelinkComponentAuditExample example);



}
