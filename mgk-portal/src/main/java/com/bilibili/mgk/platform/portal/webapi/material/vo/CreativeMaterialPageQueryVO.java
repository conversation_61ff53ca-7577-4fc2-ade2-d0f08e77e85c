package com.bilibili.mgk.platform.portal.webapi.material.vo;

import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.bilibili.mgk.material.center.service.creative.model.UpAuthType;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery.DaihuoFilters;
import com.bilibili.mgk.material.center.service.creative.vo.DurationSection;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortOrder;
import com.bilibili.mgk.material.center.service.creative.vo.QueryKeywordType;
import com.bilibili.mgk.material.center.service.creative.vo.SceneType;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonSetter;
import io.swagger.annotations.ApiModelProperty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/8
 */
@Data
@Accessors(chain = true)
public class CreativeMaterialPageQueryVO implements SnakeCaseBody {


    private QueryKeywordType keywordType;


    private String keyword;

    @JsonSetter
    public void setKeywordType(String keywordType) {
        if (StringUtils.isEmpty(keywordType)) {
            this.keywordType = QueryKeywordType.composite;
        } else {
            this.keywordType = QueryKeywordType.valueOf(keywordType);
        }
    }

    @JsonSetter
    public void setMaterialType(String materialType) {
        if (StringUtils.isEmpty(materialType)) {
            this.materialType = MaterialType.video;
        } else {
            this.materialType = MaterialType.valueOf(materialType);
        }
    }

    @JsonSetter
    public void setUpType(String upType) {

        if (StringUtils.isEmpty(upType)) {
            this.upType = null;
        } else {
            this.upType = UpAuthType.valueOf(upType);
        }

    }

    @JsonSetter
    public void setDurationSection(String durationSection) {
        if (StringUtils.isEmpty(durationSection)) {
            this.durationSection = null;
        } else {
            this.durationSection = DurationSection.valueOf(durationSection);

        }
    }

    @JsonSetter
    public void setSceneType(String sceneType) {

        if (StringUtils.isEmpty(sceneType)) {
            this.sceneType = SceneType.all;
        } else {
            this.sceneType = SceneType.valueOf(sceneType);
        }
    }

    @JsonSetter
    public void setSortBy(String sortBy) {
        if (StringUtils.isEmpty(sortBy)) {
            this.sortBy = MaterialSortBy.hot;
        } else {
            this.sortBy = MaterialSortBy.valueOf(sortBy);
        }
    }


    @JsonSetter
    public void setOrder(String order) {
        if (StringUtils.isEmpty(order)) {
            this.order = MaterialSortOrder.desc;
        } else {
            this.order = MaterialSortOrder.valueOf(order);
        }
    }

    private MaterialType materialType;


    /**
     * 推广目的，一路透传，底层支持多推广目的
     */
    private String promotionPurposeType;


    /**
     * 投放终端类型，"PC" "移动"
     */
    private String deviceType;


    private Integer isVerticalScreen;

    private UpAuthType upType;


    private DurationSection durationSection;


    /**
     * 对应于srcType
     */
    private SceneType sceneType;

    private MaterialSortBy sortBy;

    private MaterialSortOrder order = MaterialSortOrder.desc;

    private String dayType;


    private Integer pn;

    private Integer ps;


    private String industryFilters;

    /**
     * mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
     * 有更加简单的方案， 但是这里因为不想修改全局，所以只会兼容处理
     */


    @ApiModelProperty(value = "v2版本使用commerce的行业过滤， 其中没有明确的带货这一行业，带货使用额外的独立搜索条件")
    /**
     * v2版本使用commerce的行业过滤， 其中没有明确的带货这一行业，带货使用额外的独立搜索条件
     */
    private Map<String, List<String>> commerceIndustryFilters = new HashMap<>();


    @ApiModelProperty(value = "与v2版commerce行业搜索并用的带货搜索条件")
    /**
     * 与v2版commerce行业搜索并用的带货搜索条件
     */
    private DaihuoFilters daihuoFilters;

    @ApiModelProperty(value = "v2版本使用game的游戏过滤， 注意commerceIndustryFilters中的仅游戏行业是and逻辑")
    private List<String> gameFilters;



}
