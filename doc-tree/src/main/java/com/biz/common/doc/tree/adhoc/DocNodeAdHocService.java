package com.biz.common.doc.tree.adhoc;

import com.biz.common.doc.tree.convertor.DocConvertor;
import com.biz.common.doc.tree.model.DocFlattenNode;
import com.biz.common.doc.tree.repository.es.DocContentEsRepository;
import com.biz.common.doc.tree.repository.mysql.DocNodeMapper;
import com.biz.common.doc.tree.util.IteratorHelper;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/16
 */
@Slf4j
@Service
public class DocNodeAdHocService {


    @Resource
    private DocNodeMapper nodeMapper;

    @Resource
    private DocContentEsRepository docContentEsRepository;


    public void mysql2Es() {

        Iterator<List<DocFlattenNode>> iterator = IteratorHelper.buildIterator(
                (id, limit) -> {
                    return nodeMapper.selectAllByIdGtAndLimit(id, limit);
                },
                DocFlattenNode::getNodeId,
                2000

        );

        while (iterator.hasNext()) {
            List<DocFlattenNode> docFlattenNodes = iterator.next();
            if (CollectionUtils.isEmpty(docFlattenNodes)) {
                log.info("mysql2Es, docFlattenNodes is empty");
                break;
            }

            docFlattenNodes.stream()
                    .filter(DocFlattenNode::getHasDoc)
                    .forEach(doc -> {

                        docContentEsRepository.updateDocContent(
                                DocConvertor.instance.toDocContentUpdateEntity(doc)
                        );
                    });

        }
        log.info("Success to sync");

    }


}
