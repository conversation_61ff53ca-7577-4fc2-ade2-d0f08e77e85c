<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
  PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
  <settings>
    <setting name="mapUnderscoreToCamelCase" value="true"/>
  </settings>

  <typeHandlers>
    <typeHandler handler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
    <typeHandler handler="com.bilibili.mgk.material.center.repository.model.MyIntListTypeHandler"/>
    <typeHandler handler="org.apache.ibatis.type.ZonedDateTimeTypeHandler"/>
    <typeHandler handler="org.apache.ibatis.type.OffsetDateTimeTypeHandler"/>
    <typeHandler handler="org.apache.ibatis.type.OffsetTimeTypeHandler"/>
    <typeHandler handler="org.apache.ibatis.type.LocalDateTypeHandler"/>
    <typeHandler handler="org.apache.ibatis.type.LocalTimeTypeHandler"/>
    <typeHandler handler="org.apache.ibatis.type.InstantTypeHandler"/>
  </typeHandlers>
</configuration>