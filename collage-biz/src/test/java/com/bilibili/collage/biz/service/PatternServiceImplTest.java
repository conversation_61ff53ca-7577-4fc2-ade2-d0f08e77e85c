package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.api.service.ICollageService;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.collage.api.service.IFontLibraryService;
import com.bilibili.collage.api.service.IPatternTagService;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.bilibili.collage.biz.dao.MgkCollageLayerDao;
import com.bilibili.collage.biz.dao.MgkCollageLayerStrokeDao;
import com.bilibili.collage.biz.dao.MgkCollagePatternDao;
import com.bilibili.collage.biz.dao.MgkCollageSizeDao;
import com.bilibili.collage.biz.dao.ext.ExtMgkCollageLayerDao;
import com.bilibili.collage.biz.po.MgkCollageLayerPo;
import com.bilibili.collage.biz.po.MgkCollageLayerStrokePo;
import com.bilibili.collage.biz.po.MgkCollagePatternPo;
import com.bilibili.collage.biz.po.MgkCollageWorksPo;
import com.bilibili.collage.biz.render.PatternRender;
import com.bilibili.mgk.platform.common.CollageLayerEnum;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/09/17
 **/
public class PatternServiceImplTest extends BaseMockitoTest {

    @InjectMocks
    private PatternServiceImpl patternService;

    @Mock
    private MgkCollagePatternDao mgkCollagePatternDao;

    @Mock
    private MgkCollageLayerDao mgkCollageLayerDao;

    @Mock
    private IFontLibraryService fontLibraryService;

    @Mock
    private MgkCollageSizeDao mgkCollageSizeDao;

    @Mock
    private QueryCollageLayerDto queryCollageLayerDto;

    @Mock
    private CollageValidService collageValidService;

    @Mock
    private SnowflakeIdWorker snowflakeIdWorker;

    @Mock
    private ICollageService collageService;

    @Mock
    private PatternRender patternRender;

    @Mock
    private ExtMgkCollageLayerDao extMgkCollageLayerDao;

    @Mock
    private IPatternTagService patternTagService;

    @Mock
    private ICollageSizeService collageSizeService;

    @Mock
    private MgkCollageLayerStrokeDao mgkCollageLayerStrokeDao;

    private QueryCollagePatternDto queryCollagePatternDto;

    private MgkCollageLayerPo mgkCollageLayerPo;

    private ArrayList<LayerDto> layerDtos;

    @Before
    public void setUp() throws Exception {
        queryCollagePatternDto = QueryCollagePatternDto.builder()
                .industryIds(Lists.newArrayList(1))
                .collageSizeIds(Lists.newArrayList(1))
                .tagIds(Lists.newArrayList(1))
                .tagId(1)
                .industryId(1)
                .ids(Lists.newArrayList(1))
                .collageSizeId(1)
                .pageInfo(Page.valueOf(1, 15))
                .name("name")
                .status(1)
                .edition(1)
                .build();

        mgkCollageLayerPo = MgkCollageLayerPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .worksId(1)
                .bgColor("rgba(255, 255, 255, 1.00)")
                .seq(1)
                .opacity(1)
                .rotate(1)
                .height(1)
                .width(1)
                .yAxis(1)
                .xAxis(1)
                .type(1)
                .categoryId(1)
                .name("name")
                .creator("SYS_TEST")
                .ctime(new Timestamp(System.currentTimeMillis()))
                .fontFamilyId(1)
                .fontSize(1)
                .fontStyle("1")
                .fontWeight("1")
                .id(1)
                .imageHeight(1)
                .imageLock(1)
                .imageMd5("md5")
                .imageUrl("http://")
                .imageWidth(1)
                .linethrough(1)
                .mtime(new Timestamp(System.currentTimeMillis()))
                .patternId(1)
                .textAlign("text")
                .textColor("rgba(255, 255, 255, 1.00)")
                .textVal("text")
                .underline(1)
                .shadowColor("rgba(0,0,0,1)")
                .shadowWidth(1)
                .shadowBlur(1)
                .shadowY(1)
                .shadowX(1)
                .textOrient(1)
                .textCross(1)
                .build();

        queryCollageLayerDto = QueryCollageLayerDto.builder().build();

        layerDtos = Lists.newArrayList(LayerDto.builder()
                .category(CollageLayerEnum.MAIN_TITLE)
                .opacity(0.6f)
                .fontFamilyId(1)
                .fontFamilyName("family name")
                .fontFamilyUrl("http://www.bilibili.com")
                .fontSize(1)
                .fontStyle("style")
                .fontWeight("weight")
                .height(1)
                .id(1)
                .imageLock(1)
                .imageUrl("http://image/")
                .linethrough(1)
                .name("name")
                .patternId(1)
                .rotate(1)
                .seq(1)
                .text("text")
                .textAlign("test align")
                .underline(1)
                .width(1)
                .x(1)
                .y(1)
                .bgColor(RgbaDto.builder()
                        .alpha(2)
                        .blue(1)
                        .green(1)
                        .opacity(0.6f)
                        .red(1)
                        .build())
                .creator("SYS_TEST")
                .status(1)
                .textColor(RgbaDto.builder()
                        .red(1)
                        .opacity(0.6f)
                        .green(1)
                        .blue(1)
                        .alpha(2)
                        .build())
                .worksId(1)
                .strokeDtos(Lists.newArrayList(StrokeDto.builder()
                        .color(RgbaDto.builder()
                                .red(1)
                                .opacity(0.6f)
                                .green(1)
                                .blue(1)
                                .alpha(2)
                                .build())
                        .id(1)
                        .width(1)
                        .layerId(1)
                        .build()))
                .build());
    }

    @Test
    public void testQueryPatternByPage() {

        when(mgkCollageLayerStrokeDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkCollageLayerStrokePo.builder()
                .layerId(1)
                .color("")
                .build()));

        when(mgkCollagePatternDao.countByExample(any())).thenReturn(0L);
        PageResult<PatternDto> pageResult =
                patternService.queryPatternByPage(queryCollagePatternDto);
        assertTrue(CollectionUtils.isEmpty(pageResult.getRecords()));

        when(mgkCollagePatternDao.countByExample(any())).thenReturn(1L);
        when(mgkCollageLayerDao.selectByExample(any())).thenReturn(Lists.newArrayList(mgkCollageLayerPo));
        pageResult = patternService.queryPatternByPage(queryCollagePatternDto);
        assertFalse(CollectionUtils.isEmpty(pageResult.getRecords()));

    }

    @Test
    public void testQueryPattern() {
        when(mgkCollageLayerDao.selectByExample(any())).thenReturn(Lists.newArrayList(mgkCollageLayerPo));
        when(mgkCollageLayerStrokeDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkCollageLayerStrokePo.builder()
                .layerId(1)
                .color("rgba(0,0,0,1)")
                .build()));
        List<PatternDto> patternDtos = patternService.queryPattern(queryCollagePatternDto);
        assertFalse(CollectionUtils.isEmpty(patternDtos));
    }

    @Test
    public void testQueryPatternBaseInfo() {
        when(mgkCollageLayerDao.selectByExample(any())).thenReturn(Lists.newArrayList(mgkCollageLayerPo));
        List<PatternDto> patternDtos = patternService.queryPatternBaseInfo(queryCollagePatternDto);
        assertFalse(CollectionUtils.isEmpty(patternDtos));
    }

    @Test
    public void testQueryPatternBaseInfoByPage() {
        patternService.queryPatternBaseInfoByPage(queryCollagePatternDto);

        when(mgkCollagePatternDao.countByExample(any())).thenReturn(0L);
        PageResult<PatternDto> pageResult =
                patternService.queryPatternBaseInfoByPage(queryCollagePatternDto);
        assertTrue(CollectionUtils.isEmpty(pageResult.getRecords()));

        when(mgkCollagePatternDao.countByExample(any())).thenReturn(1L);
        when(mgkCollageLayerDao.selectByExample(any())).thenReturn(Lists.newArrayList(mgkCollageLayerPo));
        pageResult = patternService.queryPatternBaseInfoByPage(queryCollagePatternDto);
        assertFalse(CollectionUtils.isEmpty(pageResult.getRecords()));
    }

    @Test
    public void testGetPatternByName() {
        List<PatternDto> patternDtos = patternService.getPatternByName("name");
        assertFalse(CollectionUtils.isEmpty(patternDtos));
    }

    @Test
    public void testGetPatternWithLayerById() {
        when(mgkCollageLayerStrokeDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkCollageLayerStrokePo.builder()
                .layerId(1)
                .color("")
                .build()));
        when(mgkCollageLayerDao.selectByExample(any())).thenReturn(Lists.newArrayList(mgkCollageLayerPo));
        PatternDto patternDto = patternService.getPatternWithLayerById(1);
        assertNotNull(patternDto);
    }

    @Test
    public void testGetPatternBaseInfoById() {
        PatternDto patternDto = patternService.getPatternBaseInfoById(1);
        assertNotNull(patternDto);
    }

    @Test
    public void testGetLayerListByPatternId() {
        when(mgkCollageLayerStrokeDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkCollageLayerStrokePo.builder()
                .layerId(1)
                .color("")
                .build()));
        when(mgkCollageLayerDao.selectByExample(any())).thenReturn(Lists.newArrayList(mgkCollageLayerPo));
        List<LayerDto> layerDtos = patternService.getLayerListByPatternId(1);
        assertFalse(CollectionUtils.isEmpty(layerDtos));
    }

    @Test
    public void testQueryLayerList() {
        when(mgkCollageLayerStrokeDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkCollageLayerStrokePo.builder()
                .layerId(1)
                .color("")
                .build()));
        when(mgkCollageLayerDao.selectByExample(any())).thenReturn(Lists.newArrayList(mgkCollageLayerPo));
        patternService.queryLayerList(queryCollageLayerDto);
    }

    @Test
    public void testGetLayerListByPatternIds() {
        when(mgkCollageLayerStrokeDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkCollageLayerStrokePo.builder()
                .layerId(1)
                .color("")
                .build()));
        when(mgkCollageLayerDao.selectByExample(any())).thenReturn(Lists.newArrayList(mgkCollageLayerPo));
        patternService.getLayerListByPatternIds(Lists.newArrayList(1));
    }

    @Test
    public void testCreatePattern() {
        when(mgkCollageLayerStrokeDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkCollageLayerStrokePo.builder()
                .layerId(1)
                .color("")
                .build()));
        when(mgkCollagePatternDao.insertSelective(any())).thenAnswer(answer -> {
            MgkCollagePatternPo po = answer.getArgumentAt(0, MgkCollagePatternPo.class);
            po.setId(1);
            return 1;
        });
        patternService.createPattern(operator, PatternDto.builder()
                .layerDtos(layerDtos)
                .patternId(1L)
                .build());
        when(patternTagService.getPatternTagByCode(any())).thenReturn(null);
        patternService.createPattern(operator, PatternDto.builder()
                .layerDtos(layerDtos)
                .tagCode("tag")
                .patternId(1L)
                .build());
    }

    @Test
    public void testUpdateRenderUrl() {
        patternService.updateRenderUrl(1, "http://bilibili.com");
    }

    @Test(expected = RuntimeException.class)
    public void testRender() throws Exception {
        patternService.render(PatternDto.builder().build());
        Exception exception = mock(Exception.class);
        when(patternRender.renderAndUploadToBfs(any(PatternDto.class))).thenThrow(exception);
        patternService.render(PatternDto.builder().build());
    }

    @Test(expected = RuntimeException.class)
    public void testTestRender() throws Exception {
        patternService.render(Lists.newArrayList(LayerDto.builder().build()));
        Exception exception = mock(Exception.class);
        when(patternRender.renderAndUploadToBfs(any(List.class))).thenThrow(exception);
        patternService.render(Lists.newArrayList(LayerDto.builder().build()));
    }

    @Test
    public void testRenderAsync() {
        patternService.renderAsync(1);
    }

    @Test
    public void testEditLayer() {
        patternService.editLayer(operator, PatternDto.builder()
                .layerDtos(Lists.newArrayList(LayerDto.builder()
                        .category(CollageLayerEnum.MATE_IMG)
                        .opacity(0.6f)
                        .strokeDtos(Lists.newArrayList(StrokeDto.builder()
                                .color(RgbaDto.builder().alpha(1).red(1).green(1).blue(1).opacity(0.6f).build())
                                .build()))
                        .build()))
                .build());
    }

    @Test
    public void testEditBaseInfoPattern() {
        patternService.editBaseInfoPattern(operator, PatternDto.builder().build());
        when(patternTagService.getPatternTagByCode(any())).thenReturn(null);
        patternService.editBaseInfoPattern(operator, PatternDto.builder()
                .tagCode("tag")
                .build());
    }

    @Test
    public void testDeletePattern() {
        patternService.deletePattern(1);
    }

    @Test
    public void testUpdateStatus() {
        patternService.updateStatus(operator, 1, 1);
    }

    @Test
    public void testDeleteLayerByPatternId() {
        patternService.deleteLayerByPatternId(1);
    }
}