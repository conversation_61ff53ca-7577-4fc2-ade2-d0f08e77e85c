package com.bilibili.mgk.platform.api.user;

import com.bilibili.mgk.platform.api.user.dto.UserDetail;

import java.util.Optional;

public interface IUserInfoService {

    /*
     * 查询用户详细信息
     * @param mid mid
     * @return
     */
    Optional<UserDetail> queryUserDetail(Long mid);

    /*
     * 查询用户敏感信息
     * @param mid mid
     * @return 明文手机号
     *
     * uat环境可以用mid 3289871099691109 测试
     * 注意本地调用这个方法会403,可以在测试、孵化等环境测试
     */
    Optional<String> queryUserSensitiveInfo(Long mid);
}
