package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkCnAdminCodeInfoPo;
import com.bilibili.mgk.platform.biz.po.MgkCnAdminCodeInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkCnAdminCodeInfoDao {
    long countByExample(MgkCnAdminCodeInfoPoExample example);

    int deleteByExample(MgkCnAdminCodeInfoPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkCnAdminCodeInfoPo record);

    int insertBatch(List<MgkCnAdminCodeInfoPo> records);

    int insertUpdateBatch(List<MgkCnAdminCodeInfoPo> records);

    int insert(MgkCnAdminCodeInfoPo record);

    int insertUpdateSelective(MgkCnAdminCodeInfoPo record);

    int insertSelective(MgkCnAdminCodeInfoPo record);

    List<MgkCnAdminCodeInfoPo> selectByExample(MgkCnAdminCodeInfoPoExample example);

    MgkCnAdminCodeInfoPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkCnAdminCodeInfoPo record, @Param("example") MgkCnAdminCodeInfoPoExample example);

    int updateByExample(@Param("record") MgkCnAdminCodeInfoPo record, @Param("example") MgkCnAdminCodeInfoPoExample example);

    int updateByPrimaryKeySelective(MgkCnAdminCodeInfoPo record);

    int updateByPrimaryKey(MgkCnAdminCodeInfoPo record);
}