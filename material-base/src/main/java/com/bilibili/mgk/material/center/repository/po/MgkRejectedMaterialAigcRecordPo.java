package com.bilibili.mgk.material.center.repository.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * mgk_rejected_material_aigc_record
 *
 * <AUTHOR>
@Data
public class MgkRejectedMaterialAigcRecordPo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;
    /**
     * 素材id
     */
    private String materialId;
    /**
     * 素材id类型，如img，video
     */
    private String materialIdType;
    /**
     * 素材类型下的唯一键，一般为md5
     */
    private String materialUk;
    /**
     * 旧素材id
     */
    private Long lauMaterialId;
    /**
     * aigc版本，预留字段
     */
    private Integer version;
    /**
     * 拒审理由
     */
    private String rejectReason;
    /**
     * 拒审key， md5(拒审理由)
     */
    private String rejectKey;
    /**
     * 拒审时间
     */
    private Date rejectTime;
    /**
     * aigc提交时间
     */
    private Date aigcSubmitTime;
    /**
     * aigc完成时间
     */
    private Date aigcCompleteTime;
    /**
     * aigc错误码
     */
    private Integer aigcErrorCode;
    /**
     * aigc错误消息
     */
    private String aigcErrorMsg;
    /**
     * aigc状态
     */
    private String aigcStatus;
    /**
     * aigc请求id
     */
    private String aigcRequestId;
    /**
     * 扩展字段
     */
    private String extra;
    /**
     * 发表时间
     */
    private Date ctime;
    /**
     * 修改时间
     */
    private Date mtime;
    /**
     * 是否删除(0:未删除,1:已删除)
     */
    private Byte deleted;
}