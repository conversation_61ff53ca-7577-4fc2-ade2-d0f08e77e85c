package com.bilibili.mgk.platform.portal.filter;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @ClassName WebApiFilter
 * <AUTHOR>
 * @Date 2022/6/19 3:52 下午
 * @Version 1.0
 **/
public class WebApiFilter implements Filter {
    private static final String CUSTOM_HEADERS = "Content-Type, HTTP-CONSUMER-KEY, HTTP-DEVICE-TYPE, HTTP-ACCESS-TOKEN, image_hash, Location, bid-grey";
    private String ALLOW_ORIGIN ;
    private String ALLOW_CREDENTIALS;

    public String getALLOW_CREDENTIALS() {
        return ALLOW_CREDENTIALS;
    }

    public void setALLOW_CREDENTIALS(String aLLOW_CREDENTIALS) {
        ALLOW_CREDENTIALS = aLLOW_CREDENTIALS;
    }

    public String getALLOW_ORIGIN() {
        return ALLOW_ORIGIN;
    }

    public void setALLOW_ORIGIN(String ALLOW_ORIGIN) {
        this.ALLOW_ORIGIN = ALLOW_ORIGIN;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        this.corsResponse((HttpServletRequest) request, (HttpServletResponse) response);
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {

    }

    public String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    private void corsResponse(HttpServletRequest request, HttpServletResponse response) {
        String origin = request.getHeader("Origin");
        response.setHeader("Access-Control-Allow-Origin", StringUtils.isNotBlank(origin) ? origin : ALLOW_ORIGIN);
        response.setHeader("Access-Control-Allow-Methods", "POST, PUT, GET, OPTIONS, DELETE");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", CUSTOM_HEADERS);
        response.setHeader("Access-Control-Allow-Credentials", ALLOW_CREDENTIALS);
        response.setHeader("Access-Control-Expose-Headers", "X-My-Custom-Header, X-Another-Custom-Header, Date");

    }
}

