package com.bilibili.mgk.material.center.service.asset.vo;

import com.bapis.dynamic.common.ContentType;
import com.bapis.dynamic.common.CreateContent;
import com.bapis.dynamic.common.CreateContentItem;
import com.bapis.dynamic.common.GoodsContent;
import com.bilibili.mgk.material.center.service.asset.model.DynamicDrawPic;
import com.bilibili.mgk.material.center.service.bluelink.dto.RichTextType;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 *
 * 参考
 * {
 * 	"attachCard": {
 * 		"goods": {
 * 			"itemId": [],
 * 			"ext": []
 *                }* 	},
 * 	"content": {
 * 		"paragraphs": [{
 * 			"paraType": "PICTURES",
 * 			"pic": {
 * 				"pics": [{
 * 					"url": "http://i0.hdslb.com/bfs/new_dyn/447e698449bea138cf5d03f37e639738384963039.jpg",
 * 					"width": 878,
 * 					"height": 1536,
 * 					"size": 206.963
 *                }]
 *            }
 *        }, {
 * 			"paraType": "TEXT",
 * 			"text": {
 * 				"nodes": [{
 * 					"nodeType": "WORDS",
 * 					"word": {
 * 						"words": "cehi "
 *                    }
 *                }]
 *            }
 *        }],
 * 		"pubInfo": {
 * 			"scene": 1,
 * 			"timerPubTime": 1718779007
 *        },
 * 		"title": "ceshi "* 	},
 * 	"opusSource": "ALBUM"
 * }
 *
 *  图文动态发布请求
 * <AUTHOR>
 * @desc
 * @date 2024/7/8
 */
@Data
@Accessors(chain = true)
public class MaterialDynamicDrawPublishReq  implements SnakeCaseBody {


    @ApiModelProperty()
    private Long uid;


    @ApiModelProperty("动态图片")
    private List<DynamicDrawPic> pics;


    @ApiModelProperty("动态文本")
    @Deprecated
    private String content;

    @ApiModelProperty("动态标题")
    private String title;


    @ApiModelProperty("支持蓝链的富文本动态正文")
    private List<RichTextDTO> richContent;


    @ApiModelProperty("发布类型， 1，立即发布， 2， 定时发布")
    private Integer publishType;

    @ApiModelProperty("定时并发布时间，类型为2时提供，yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime scheduleTime;


    public MaterialDynamicDrawPublishReq validateAndTransformAsRichTextReq() {

        return this.validate()
                .transformAsRichTextDynamic()
                .assignSeqId();

    }


    private MaterialDynamicDrawPublishReq validate() {

        Assert.notNull(uid, "uid不能为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(pics), "pics不能为空");


        Assert.notNull(publishType, "publishType不能为空");

        if(publishType == PublishType.PUBLISH_BY_SCHEDULE_TIME) {
            Assert.notNull(scheduleTime, "定时发布时间不能为空");
        }

        if (StringUtils.isEmpty(content) && CollectionUtils.isEmpty(richContent)) {
            throw new IllegalArgumentException("动态正文未提供");
        }

        if (!StringUtils.isEmpty(content) && !CollectionUtils.isEmpty(richContent)) {
            throw new IllegalArgumentException("简单正文和富文本正文不能同时存在");
        }

        if (CollectionUtils.isNotEmpty(richContent)) {

            richContent.forEach(RichTextDTO::validate);
        }

        return this;
    }


    public MaterialDynamicDrawPublishReq transformAsRichTextDynamic() {

        if (StringUtils.isEmpty(content) && CollectionUtils.isEmpty(richContent)) {
            throw new IllegalArgumentException("动态正文未提供");
        }

        if (!StringUtils.isEmpty(content) && !CollectionUtils.isEmpty(richContent)) {
            throw new IllegalArgumentException("简单正文和富文本正文不能同时存在");
        }

        if (!StringUtils.isEmpty(content)) {
            this.richContent = Stream.of(content)
                    .map(c -> new RichTextDTO()
                            .setRichTextType(RichTextType.RAW_TEXT)
                            .setRawText(c)
                    ).collect(Collectors.toList());

            // dummy
            this.content = null;
        }

        return this;

    }


    public MaterialDynamicDrawPublishReq assignSeqId() {

        if (CollectionUtils.isEmpty(richContent)) {
            return this;
        }

        AtomicInteger textSeqId = new AtomicInteger(0);

        AtomicInteger bluelinkSeqId = new AtomicInteger(0);

        richContent.stream().forEach(richText -> {

            richText.setTextSegmentId(textSeqId.incrementAndGet());

            if (richText.getRichTextType() == RichTextType.BLUE_LINK) {

                richText.getBluelink().setMid(uid);
                richText.getBluelink().setBluelinkSeqId(bluelinkSeqId.incrementAndGet());
                richText.getBluelink().setTextSegmentId(richText.getTextSegmentId());
                richText.getBluelink().setRawText(
                        Optional.ofNullable(richText.getRawText()).orElse(richText.getBluelink().getRawText())
                );

            }

        });

        return this;
    }


    public CreateContent toGrpcCreateContentWithRichText(Long subType) {

        return CreateContent.newBuilder()
                .setTitle(this.getTitle())
                .addAllContents(richContent
                        .stream()
                        .map(richText -> {

                            switch (richText.getRichTextType()) {

                                case RichTextType.RAW_TEXT: {
                                    return CreateContentItem.newBuilder()
                                            .setRawText(richText.getRawText())
                                            .build();
                                }
                                case RichTextType.BLUE_LINK: {
                                    return CreateContentItem.newBuilder()
                                            .setType(ContentType.GOODS)
                                            .setRawText(richText.getRawText())
                                            .setGoods(GoodsContent.newBuilder()
                                                    .setItemIdStr(richText.getBluelink().getDynamicBizId())
                                                    .setSubType(subType)
                                                    .build())
                                            .build();
                                }

                                default: {

                                    throw new IllegalArgumentException("不支持的富文本类型");
                                }


                            }
                        }).collect(Collectors.toList())
                )
                .build();
    }

//    public List<RichTextDTO> getCopiedRichTextWithDummyBluelink(){
//
//        return richContent.stream().map(richText ->
//                        richText.lightCopy().setBluelink(null))
//                .collect(Collectors.toList());
//    }
//
//
//
//    public MaterialDynamicDrawPublishReq enrichBluelinkRichText(
//            List<DynamicBluelinkResolveContentDTO> scheduleTaskRelatedBluelink) {
//
//
//    }

}
