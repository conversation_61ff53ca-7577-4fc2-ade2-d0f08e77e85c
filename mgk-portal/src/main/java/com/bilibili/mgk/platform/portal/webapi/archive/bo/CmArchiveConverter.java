package com.bilibili.mgk.platform.portal.webapi.archive.bo;

import com.bapis.ad.archive.*;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface CmArchiveConverter {
    CmArchiveConverter MAPPER = Mappers.getMapper(CmArchiveConverter.class);

    @Mapping(target = "value", source = "message")
    @Mapping(target = "key", source = "code")
    AuditBo fromRpcBo(CmArchiveAuditInfo bo);

    CoverBo fromRpcBo(CmArchiveCoverInfo bo);

    VideoBo fromRpcBo(CmArchiveVideoInfo bo);

    @Mapping(target = "catTwo", source = "biliCategoryTwo")
    @Mapping(target = "catOne", source = "biliCategoryOne")
    UgcV2Bo toUgcV2Bo(CmArchiveUgcInfo bo);

    CmArchiveV2Bo toCmArchiveV2Bo(CmArchiveInfo bo);
}
