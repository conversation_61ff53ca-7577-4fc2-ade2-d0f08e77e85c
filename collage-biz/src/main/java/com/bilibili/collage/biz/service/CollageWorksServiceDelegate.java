package com.bilibili.collage.biz.service;

import static com.bilibili.mgk.platform.common.MgkConstants.IMAGE_COMPRESSION_SUFFIX;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.collage.api.dto.CollageCoverDto;
import com.bilibili.collage.api.dto.CollageEditWorksLayerDto;
import com.bilibili.collage.api.dto.CollageEditWorksNoLayersDto;
import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.dto.CollageSizeDto;
import com.bilibili.collage.api.dto.CollageWorksDto;
import com.bilibili.collage.api.dto.LayerDto;
import com.bilibili.collage.api.dto.NewCollageOperationLogDto;
import com.bilibili.collage.api.dto.NewCollageWorksDto;
import com.bilibili.collage.api.dto.QueryCollageCoverDto;
import com.bilibili.collage.api.dto.QueryCollageLayerDto;
import com.bilibili.collage.api.dto.QueryCollageWorksDto;
import com.bilibili.collage.api.dto.StrokeDto;
import com.bilibili.collage.api.service.ICollageCoverService;
import com.bilibili.collage.api.service.ICollageLogService;
import com.bilibili.collage.api.service.ICollageMediaService;
import com.bilibili.collage.api.service.ICollageService;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.collage.api.service.IPatternService;
import com.bilibili.collage.biz.dao.MgkCollageLayerDao;
import com.bilibili.collage.biz.dao.MgkCollageLayerStrokeDao;
import com.bilibili.collage.biz.dao.MgkCollageSizeDao;
import com.bilibili.collage.biz.dao.MgkCollageWorksDao;
import com.bilibili.collage.biz.dao.ext.ExtMgkCollageLayerDao;
import com.bilibili.collage.biz.po.MgkCollageLayerPo;
import com.bilibili.collage.biz.po.MgkCollageLayerPoExample;
import com.bilibili.collage.biz.po.MgkCollageLayerStrokePo;
import com.bilibili.collage.biz.po.MgkCollageLayerStrokePoExample;
import com.bilibili.collage.biz.po.MgkCollageSizePo;
import com.bilibili.collage.biz.po.MgkCollageWorksPo;
import com.bilibili.collage.biz.po.MgkCollageWorksPoExample;
import com.bilibili.mgk.platform.common.CollageCoverTypeEnum;
import com.bilibili.mgk.platform.common.CollageLogObjFlagEnum;
import com.bilibili.mgk.platform.common.CollageMediaOriginEnum;
import com.bilibili.mgk.platform.common.CollageMediaTypeEnum;
import com.bilibili.mgk.platform.common.CollageOperateTypeEnum;
import com.bilibili.mgk.platform.common.CollageWorksOriginEnum;
import com.bilibili.mgk.platform.common.IsSynchro;
import com.bilibili.mgk.platform.common.LayerTypeEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2020/09/11
 **/
@Service
public class CollageWorksServiceDelegate {

    @Autowired
    private CollageValidService collageValidService;

    @Autowired
    private MgkCollageWorksDao collageWorksDao;

    @Autowired
    private ICollageService collageService;

    @Autowired
    private MgkCollageLayerDao mgkCollageLayerDao;

    @Autowired
    private IPatternService patternService;

    @Autowired
    private MgkCollageSizeDao mgkCollageSizeDao;

    @Autowired
    private ExtMgkCollageLayerDao extMgkCollageLayerDao;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private ICollageLogService collageLogService;

    @Autowired
    private ICollageMediaService collageMediaService;

    @Autowired
    private ICollageSizeService collageSizeService;

    @Autowired
    private ICollageCoverService collageCoverService;

    @Autowired
    private MgkCollageLayerStrokeDao collageLayerStrokeDao;

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public Integer create(Operator operator, NewCollageWorksDto worksDto) {
        Integer id = saveWorks(operator, worksDto);
        saveLayers(operator, id, worksDto.getLayerDtos());
//        renderAsync(id);
        return id;
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public Integer createNoLayers(Operator operator, NewCollageWorksDto dto) {
        Integer id = saveWorks(operator, dto);
        CollageCoverDto cover = dto.getCover();
        cover.setCoverId(snowflakeIdWorker.nextId());
        cover.setObjType(CollageCoverTypeEnum.WORKS_COVER.getCode());
        cover.setObjId(id);
        collageCoverService.insert(operator, cover);
        return id;
    }

//    private void renderAsync(Integer id) {
//        CompletableFuture.supplyAsync(() -> patternService.render(getWorksWithLayerById(id).getLayerDtos()))
//                .thenAccept(rendered -> updateRenderUrl(id, rendered));
//    }

    private void updateRenderUrl(Integer id, String rendered) {
        MgkCollageWorksPo po = MgkCollageWorksPo.builder()
                .id(id)
                .isDeleted(IsDeleted.VALID.getCode())
                .renderImage(collageService.replaceHttpsProtocol(rendered))
                .build();
        collageWorksDao.updateByPrimaryKeySelective(po);
    }

//    private CollageWorksDto getWorksWithLayerById(Integer worksId) {
//
//        CollageWorksDto collageWorksDto = getWorksBaseInfoById(worksId);
//        // 获取模板下的图层
//        List<LayerDto> layers = getLayersByWorksId(Lists.newArrayList(collageWorksDto.getId()));
//        layers.sort(Comparator.comparingInt(LayerDto::getSeq));
//
//        // 获取图层下的字体
//        List<Integer> fontIds = layers.stream().map(LayerDto::getFontFamilyId).collect(Collectors.toList());
//        Map<Integer, CollageFontLibraryDto> fontMap = fontLibraryService.getFontLibraryMapByIds(fontIds);
//        layers.forEach(item -> {
//            CollageFontLibraryDto fontDto = fontMap.getOrDefault(item.getFontFamilyId(), CollageFontLibraryDto.builder().build());
//            item.setFontFamilyUrl(fontDto.getUrl());
//        });
//
//        collageWorksDto.setLayerDtos(layers);
//        return collageWorksDto;
//    }

    private List<LayerDto> getLayersByWorksId(List<Integer> worksIds) {

        return patternService.queryLayerList(QueryCollageLayerDto.builder()
                .worksIds(worksIds)
                .isDeleted(IsDeleted.VALID.getCode())
                .build());
    }

    private CollageWorksDto getWorksBaseInfoById(Integer worksId) {

        Assert.isTrue(Utils.isPositive(worksId), "作品Id不合法");

        List<MgkCollageWorksPo> pos = getWorksPoById(Lists.newArrayList(worksId));

        Assert.isTrue(!CollectionUtils.isEmpty(pos), "不存在id为[" + worksId + "]的模板");

        MgkCollageWorksPo po = pos.get(0);

        CollageWorksDto dto = this.convertWorksPo2Dto(po);

        // 获取尺寸信息
        MgkCollageSizePo mgkCollageSizePo = mgkCollageSizeDao.selectByPrimaryKey(po.getCollageSizeId());
        dto.setWidth(mgkCollageSizePo.getWidth());
        dto.setHeight(mgkCollageSizePo.getHeight());
        return dto;

    }

    public List<MgkCollageWorksPo> getWorksPoById(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        MgkCollageWorksPoExample example = new MgkCollageWorksPoExample();
        MgkCollageWorksPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (ids.size() == 1) {
            criteria.andIdEqualTo(ids.get(0));
        } else {
            criteria.andIdIn(ids);
        }
        return collageWorksDao.selectByExample(example);
    }

    private CollageWorksDto convertWorksPo2Dto(MgkCollageWorksPo po) {
        return CollageWorksDto.builder()
                .id(po.getId())
                .name(po.getName())
                .collageSizeId(po.getCollageSizeId())
                .renderImage(po.getRenderImage())
                .mtime(po.getMtime())
                .ctime(po.getCtime())
                .isSynchro(po.getIsSynchro())
                .accountId(po.getAccountId())
                .patternId(po.getPatternId())
                .layerDtos(Collections.emptyList())
                .worksRadio(po.getWorksRadio())
                .worksMd5(po.getWorksMd5())
                .worksSize(po.getWorksSize())
                .worksOrigin(po.getWorksOrigin())
                .totalDuration(po.getTotalDuration())
                .durationPerFrame(po.getDurationPerFrame())
                .frames(po.getFrames())
                .roundsNumber(po.getRoundsNumber())
                .build();
    }


    private void saveLayers(Operator operator, Integer id, List<LayerDto> layerDtos) {

        List<MgkCollageLayerStrokePo> strokePos = new ArrayList<>();
        layerDtos.forEach(layerDto -> {
            MgkCollageLayerPo layerPo = convertLayerDto(layerDto, id);
            mgkCollageLayerDao.insertSelective(layerPo);
            strokePos.addAll(layerDto.getStrokeDtos().stream().map(strokeDto -> convertLayerStrokeDto2Po(strokeDto, layerPo.getId())).collect(Collectors.toList()));
        });

        strokePos.forEach(strokePo -> {
            collageLayerStrokeDao.insertSelective(strokePo);
        });
    }

    private MgkCollageLayerStrokePo convertLayerStrokeDto2Po(StrokeDto strokeDto, Integer id) {
        return MgkCollageLayerStrokePo.builder()
                .width(strokeDto.getWidth())
                .layerId(id)
                .color(strokeDto.getColor() == null ? "" : strokeDto.getColor().toRgbaExp())
                .build();
    }

    private MgkCollageLayerPo convertLayerDto(LayerDto layer, Integer id) {
        MgkCollageLayerPo po = MgkCollageLayerPo.builder()
                .id(layer.getId())
                .name(layer.getName())
                .categoryId(layer.getCategory().getCode())
                .type(layer.getCategory().getType().getCode())
                .xAxis(layer.getX())
                .yAxis(layer.getY())
                .width(layer.getWidth())
                .height(layer.getHeight())
                .rotate(layer.getRotate())
                .opacity(((Float) (layer.getOpacity() * 100)).intValue())
                .seq(layer.getSeq())
                .bgColor(layer.getBgColor() != null ? layer.getBgColor().toRgbaExp() : "")
                .worksId(id)
                .textCross(layer.getTextCross())
                .textOrient(layer.getTextOrient())
                .shadowX(layer.getShadowX())
                .shadowY(layer.getShadowY())
                .shadowBlur(layer.getShadowBlur())
                .shadowWidth(layer.getShadowWidth())
                .shadowColor(layer.getShadowColor() != null ? layer.getShadowColor().toRgbaExp() : "")
                .build();

        if (layer.getCategory().getType().equals(LayerTypeEnum.IMAGE)) {
            po.setImageUrl(collageService.replaceHttpsProtocol(layer.getImageUrl()));
            po.setImageLock(layer.getImageLock());
        }
        if (layer.getCategory().getType().equals(LayerTypeEnum.TEXT)) {
            if (layer.getText() != null) {
                try {
                    po.setTextVal(Base64.encodeBase64String(layer.getText().getBytes("utf-8")));
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }
            po.setFontFamilyId(layer.getFontFamilyId());
            po.setTextColor(layer.getTextColor() != null ? layer.getTextColor().toRgbaExp() : "");
            po.setTextAlign(layer.getTextAlign());
            po.setFontStyle(layer.getFontStyle());
            po.setFontWeight(layer.getFontWeight());
            po.setFontSize(layer.getFontSize());
            po.setUnderline(layer.getUnderline());
            po.setLinethrough(layer.getLinethrough());
        }
        return po;
    }

    private Integer saveWorks(Operator operator, NewCollageWorksDto worksDto) {

        CollageSizeDto collageSizeDto = collageSizeService.getCollageSizeById(worksDto.getCollageSizeId());
        Assert.notNull(collageSizeDto, "贴图尺寸比例为空");

        MgkCollageWorksPo po = MgkCollageWorksPo.builder()
                .isDeleted(IsDeleted.VALID.getCode())
                .name(worksDto.getName())
                .accountId(operator.getOperatorId())
                .collageSizeId(worksDto.getCollageSizeId())
                .isSynchro(IsSynchro.NO_SYNCHRO.getCode())
                .patternId(worksDto.getPatternId())
                .worksOrigin(worksDto.getWorksOrigin())
                .worksId(snowflakeIdWorker.nextId())
                .renderImage(worksDto.getWorksUrl())
                .worksRadio(collageSizeDto.getWidth() * 10000 / collageSizeDto.getHeight())
                .worksMd5(worksDto.getWorksMd5())
                .worksSize(worksDto.getWorksSize())
                .totalDuration(worksDto.getTotalDuration())
                .durationPerFrame(worksDto.getDurationPerFrame())
                .frames(worksDto.getFrames())
                .roundsNumber(worksDto.getRoundsNumber())
                .build();
        int res = collageWorksDao.insertSelective(po);
        Assert.isTrue(Utils.isPositive(res), "插入作品失败");

        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .accountId(operator.getOperatorId())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_WORKS.getCode())
                .operateType(CollageOperateTypeEnum.COLLAGE_WORKS_ADD.getCode())
                .objId(po.getId().longValue())
                .newValue("")
                .oldValue("")
                .operatorType(operator.getOperatorType().getCode())
                .operatorUsername(operator.getOperatorName())
                .build());
        return po.getId();
    }

    public PageResult<CollageWorksDto> query(QueryCollageWorksDto queryParam) {
        MgkCollageWorksPoExample example = getWorksExampleByQueryDto(queryParam);
        Long total = collageWorksDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        List<CollageWorksDto> collageWorksDtos = getCollageWorksBaseInfo(queryParam);
        setWorksCover(collageWorksDtos);
        return PageResult.<CollageWorksDto>builder()
                .records(collageWorksDtos)
                .total(total.intValue())
                .build();
    }

    private CollageWorksDto getWorksWithLayerById(Integer worksId) {
        CollageWorksDto worksDto = getWorksBaseInfoById(worksId);
        // 获取图层
        List<LayerDto> layerDtos = getLayersByWorksId(Lists.newArrayList(worksId));
        layerDtos.sort(Comparator.comparingInt(LayerDto::getSeq));

        worksDto.setLayerDtos(layerDtos);
        return worksDto;
    }

//    private Map<Integer, List<LayerDto>> getLayersMapByWorksIds(List<Integer> worksIds) {
//        List<LayerDto> layerDtos = getLayersByWorksId(worksIds);
//        return layerDtos.stream().collect(Collectors.groupingBy(LayerDto::getWorksId));
//    }

    private List<CollageWorksDto> getCollageWorksBaseInfo(QueryCollageWorksDto queryParam) {
        MgkCollageWorksPoExample example = getWorksExampleByQueryDto(queryParam);
        List<MgkCollageWorksPo> pos = collageWorksDao.selectByExample(example);
        List<CollageWorksDto> worksDtos = convertWorksPos2Dtos(pos);
        List<Integer> collageSizeIds = worksDtos.stream().map(CollageWorksDto::getCollageSizeId).collect(Collectors.toList());
        List<CollageSizeDto> collageSizeByIds = collageSizeService.getCollageSizeByIds(collageSizeIds);
        Map<Integer, CollageSizeDto> sizeMap = collageSizeByIds.stream().collect(Collectors.toMap(CollageSizeDto::getId, Function.identity()));

        worksDtos.forEach(dto -> {
            CollageSizeDto sizeDto = sizeMap.getOrDefault(dto.getCollageSizeId(), CollageSizeDto.builder().height(0).width(0).desc("").build());
            dto.setSizeDesc(sizeDto.getDesc());
            dto.setWidth(sizeDto.getWidth());
            dto.setHeight(sizeDto.getHeight());
        });
        return worksDtos;
    }

    private List<CollageWorksDto> convertWorksPos2Dtos(List<MgkCollageWorksPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertWorksPo2Dto).collect(Collectors.toList());
    }

    public CollageWorksDto queryWithLayerById(Integer id) {
        CollageWorksDto worksDto = getWorksBaseInfoById(id);
        List<LayerDto> layerDtos = getLayersByWorksId(Lists.newArrayList(id));
        layerDtos.sort(Comparator.comparingInt(LayerDto::getSeq));
        worksDto.setLayerDtos(layerDtos);
        return worksDto;
    }

    public CollageWorksDto queryNoLayersById(Integer id) {
        CollageWorksDto worksDto = getWorksBaseInfoById(id);
        // 设置封面
        setWorksCover(Lists.newArrayList(worksDto));
        return worksDto;
    }

    private void setWorksCover(List<CollageWorksDto> worksDtos) {
        List<Integer> worksIds = worksDtos.stream().map(CollageWorksDto::getId).collect(Collectors.toList());

        List<CollageCoverDto> coverDtos = collageCoverService.getCoverDtos(QueryCollageCoverDto.builder()
                .objIds(worksIds)
                .objTypes(Lists.newArrayList(CollageCoverTypeEnum.WORKS_COVER.getCode()))
                .build());

        if (CollectionUtils.isEmpty(coverDtos)) {
            return;
        }
        Map<Integer, CollageCoverDto> dtoMaps = coverDtos.stream().collect(Collectors.toMap(CollageCoverDto::getObjId, Function.identity()));

        worksDtos.forEach(worksDto -> {
            worksDto.setCover(dtoMaps.getOrDefault(worksDto.getId(), CollageCoverDto.builder().build()));
        });
    }

    private MgkCollageWorksPoExample getWorksExampleByQueryDto(QueryCollageWorksDto dto) {
        MgkCollageWorksPoExample example = new MgkCollageWorksPoExample();
        MgkCollageWorksPoExample.Criteria criteria = example.or();

        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        ObjectUtils.setList(dto::getIds, criteria::andIdIn);
        ObjectUtils.setList(dto::getAccountIds, criteria::andAccountIdIn);
        ObjectUtils.setList(dto::getPatternIds, criteria::andPatternIdIn);
        ObjectUtils.setList(dto::getCollageSizeIds, criteria::andCollageSizeIdIn);
        ObjectUtils.setList(dto::getWorksOrigin, criteria::andWorksOriginIn);
        ObjectUtils.setList(dto::getWorksRadios, criteria::andWorksRadioIn);
        ObjectUtils.setObject(dto::getIsSynchro, criteria::andIsSynchroEqualTo);

        if (!Strings.isNullOrEmpty(dto.getName())) {
            criteria.andNameLike("%" + dto.getName() + "%");
        }

        if (dto.getFrom_time() != null && dto.getTo_time() != null) {
            criteria.andCtimeBetween(dto.getFrom_time(), dto.getTo_time());
        }

        Page page = dto.getPage();
        if (page != null) {
            example.setLimit(page.getLimit());
            example.setOffset(page.getOffset());
        }

        example.setOrderByClause("mtime desc, id desc");

        return example;
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void editWorksLayer(Operator operator, CollageEditWorksLayerDto worksDto) {
        updateLayer(operator, worksDto);
//        updateRenderUrl(worksDto.getId(), worksDto.getWorksUrl());
        updateWorks(operator, worksDto);
    }

    private void updateWorks(Operator operator, CollageEditWorksLayerDto worksDto) {
        MgkCollageWorksPo po = MgkCollageWorksPo.builder()
                .id(worksDto.getId())
                .isDeleted(IsDeleted.VALID.getCode())
                .renderImage(collageService.replaceHttpsProtocol(worksDto.getWorksUrl()))
                .worksMd5(worksDto.getWorksMd5())
                .worksSize(worksDto.getWorksSize())
                .build();
        collageWorksDao.updateByPrimaryKeySelective(po);
    }

    private void updateLayer(Operator operator, CollageEditWorksLayerDto worksDto) {
        // 删除此模板下的所有图层
        deleteLayerByWorksId(worksDto.getId());
        // 插入或者更新此模板下的图层
        worksDto.getLayers().forEach(layerDto -> {
            MgkCollageLayerPo layerPo = convertLayerDto(layerDto, worksDto.getId());
            Integer layerId = layerDto.getId();
            if (Utils.isPositive(layerId)) {
                // 更新
                layerPo.setIsDeleted(IsDeleted.VALID.getCode());
                mgkCollageLayerDao.updateByPrimaryKeySelective(layerPo);
                // 更新描边
                updateStrokes(operator, layerId, layerDto.getStrokeDtos());
            } else {
                // 插入图层
                mgkCollageLayerDao.insertSelective(layerPo);
                // 插入描边
                insertStrokes(operator, layerPo.getId(), layerDto.getStrokeDtos());
            }
        });

        // log
        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .accountId(operator.getOperatorId())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_WORKS.getCode())
                .operateType(CollageOperateTypeEnum.COLLAGE_WORKS_LAYER_MODIFY.getCode())
                .objId(worksDto.getId().longValue())
                .newValue("")
                .oldValue("")
                .operatorType(operator.getOperatorType().getCode())
                .operatorUsername(operator.getOperatorName())
                .build());
    }

    private void updateStrokes(Operator operator, Integer layerId, List<StrokeDto> strokeDtos) {
        // 删除此图层下所有的描边
        deleteLayerStrokesByLayerId(layerId);
        strokeDtos.forEach(dto -> {
            Integer strokeId = dto.getId();
            dto.setLayerId(layerId);
            if (Utils.isPositive(strokeId)) {
                // 更新描边
                updateStroke(operator, dto);
            } else {
                // 插入描边
                insertStroke(operator, dto);
            }
        });
    }

    private void updateStroke(Operator operator, StrokeDto dto) {
        MgkCollageLayerStrokePo po = MgkCollageLayerStrokePo.builder().build();
        BeanUtils.copyProperties(dto, po);
        po.setIsDeleted(IsDeleted.VALID.getCode());
        if (dto.getColor() != null) {
            po.setColor(dto.getColor().toRgbaExp());
        }
        int res = collageLayerStrokeDao.updateByPrimaryKeySelective(po);
        Assert.isTrue(Utils.isPositive(res), "更新文字描边失败");
    }

    private void deleteLayerStrokesByLayerId(Integer layerId) {
        MgkCollageLayerStrokePoExample example = new MgkCollageLayerStrokePoExample();
        example.or().andLayerIdEqualTo(layerId);
        MgkCollageLayerStrokePo po = MgkCollageLayerStrokePo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();
        collageLayerStrokeDao.updateByExampleSelective(po, example);
    }

    private void insertStrokes(Operator operator, Integer layerId, List<StrokeDto> strokeDtos) {
        strokeDtos.forEach(dto -> {
            dto.setLayerId(layerId);
            insertStroke(operator, dto);
        });
    }

    private void insertStroke(Operator operator, StrokeDto dto) {
        MgkCollageLayerStrokePo po = MgkCollageLayerStrokePo.builder()
                .color(dto.getColor() == null ? "" : dto.getColor().toRgbaExp())
                .layerId(dto.getLayerId())
                .width(dto.getWidth())
                .build();
        int res = collageLayerStrokeDao.insertSelective(po);
        Assert.isTrue(Utils.isPositive(res), "插入文字描边失败");

    }

    private void deleteLayerByWorksId(Integer id) {
        MgkCollageLayerPoExample example = new MgkCollageLayerPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andWorksIdEqualTo(id);
        MgkCollageLayerPo po = MgkCollageLayerPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();
        mgkCollageLayerDao.updateByExampleSelective(po, example);
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void editWorksName(Operator operator, Integer id, String name) {
        MgkCollageWorksPo po = MgkCollageWorksPo.builder()
                .id(id)
                .name(name)
                .build();
        int res = collageWorksDao.updateByPrimaryKeySelective(po);
        Assert.isTrue(Utils.isPositive(res), "更新失败");

        // log
        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .accountId(operator.getOperatorId())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_WORKS.getCode())
                .operateType(CollageOperateTypeEnum.COLLAGE_WORKS_NAME_MODIFY.getCode())
                .objId(id.longValue())
                .newValue("")
                .oldValue("")
                .operatorType(operator.getOperatorType().getCode())
                .operatorUsername(operator.getOperatorName())
                .build());
    }


    public void synchroWorks(Operator operator, Integer id) {
        CollageWorksDto worksDto = getWorksBaseInfoById(id);
        Assert.isTrue(operator.getOperatorId().equals(worksDto.getAccountId()), "您不能操作不属于您的作品");
        // 插入媒体
        Integer mediaId = insertMedia(operator, worksDto);

        // 插入封面
        insertCover(operator, mediaId, worksDto);

        // 是否渲染
        if (WhetherEnum.NO.getCode().equals(worksDto.getIsSynchro())) {
            updateSynchro(id);
        }

        // 日志
        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .operatorUsername(operator.getOperatorName())
                .operatorType(operator.getOperatorType().getCode())
                .oldValue("")
                .newValue("")
                .objId(id.longValue())
                .operateType(CollageOperateTypeEnum.COLLAGE_WORKS_SYNCHRO.getCode())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_WORKS.getCode())
                .accountId(operator.getOperatorId())
                .build());

        // 异步渲染
//        renderAsync(operator, mediaId, worksDto.getLayerDtos());
    }

    private void insertCover(Operator operator, Integer mediaId, CollageWorksDto worksDto) {
        // 如果作品有封面则新增封面
        List<CollageCoverDto> coverDtos = collageCoverService.getCoverDtos(QueryCollageCoverDto.builder()
                .objIds(Lists.newArrayList(worksDto.getId()))
                .objTypes(Lists.newArrayList(CollageCoverTypeEnum.WORKS_COVER.getCode()))
                .build());
        if (!CollectionUtils.isEmpty(coverDtos)) {
            CollageCoverDto coverDto = coverDtos.get(0);

            Assert.isTrue(coverDto.getCoverSize() <= MgkConstants.COVER_LIMIT_SIZE, "封面大小不能超过150k");

            // FIXME: 在设计中type+obj_id  为唯一键， 此处可以做一下去重，在发生并发时，可能会发生查询的duplicated-key， 可以先在查询侧规避，先不增加插入的复杂度
            Optional<CollageCoverDto> existedCoverMedia = Optional
                    .ofNullable(collageCoverService.getCoverDtos(QueryCollageCoverDto.builder()
                            .objIds(Lists.newArrayList(mediaId))
                            .objTypes(Lists.newArrayList(CollageCoverTypeEnum.MEDIA_COVER.getCode()))
                            .build()))
                    .orElse(new ArrayList<>())
                    .stream()
                    .findFirst();

            if (!existedCoverMedia.isPresent()) {
                CollageCoverDto dto = CollageCoverDto.builder()
                        .coverMd5(coverDto.getCoverMd5())
                        .coverSize(coverDto.getCoverSize())
                        .coverUrl(coverDto.getCoverUrl())
                        .height(coverDto.getHeight())
                        .ratio(coverDto.getRatio())
                        .width(coverDto.getWidth())
                        .coverId(snowflakeIdWorker.nextId())
                        .objId(mediaId)
                        .objType(CollageCoverTypeEnum.MEDIA_COVER.getCode())
                        .build();
                collageCoverService.insert(operator, dto);
            }
        }
    }

    private Integer insertMedia(Operator operator, CollageWorksDto worksDto) {
        Integer mediaType = CollageWorksOriginEnum.TOOLBOX_WORKS.getCode().equals(worksDto.getWorksOrigin()) ?
                CollageMediaTypeEnum.GIF_FILE.getCode() : CollageMediaTypeEnum.PIC_FILE.getCode();
        // 压缩
        String mediaUrl = worksDto.getRenderImage() + String.format(IMAGE_COMPRESSION_SUFFIX, worksDto.getWidth(), worksDto.getHeight());
        byte[] mediaBytes = OkHttpUtils.get(mediaUrl).callForBytes();

        String mediaMd5 = DigestUtils.md5Hex(mediaBytes);

        if (CollageMediaTypeEnum.GIF_FILE.getCode().equals(mediaType)) {
            Assert.isTrue(mediaBytes.length <= MgkConstants.GIF_LIMIT_SIZE, "gif文件大小不能超过1.5M");
        }

        if (CollageMediaTypeEnum.PIC_FILE.getCode().equals(mediaType)) {
            Assert.isTrue(mediaBytes.length <= MgkConstants.COLLAGE_FILE_SIZE_LIMIT, "图片大小不能超过400k");
        }

        // 新增媒体
        Integer mediaId = collageMediaService.insert(operator, CollageMediaDto.builder()
                .mediaRatio(worksDto.getWidth() * 10000 / worksDto.getHeight())
                .width(worksDto.getWidth())
                .height(worksDto.getHeight())
                .mediaType(mediaType)
                .mediaOrigin(CollageWorksOriginEnum.TOOLBOX_WORKS.getCode().equals(worksDto.getWorksOrigin()) ?
                        CollageMediaOriginEnum.TOOLBOX_MEDIA.getCode() : CollageMediaOriginEnum.PATTERN_UPLOAD.getCode())
                .mediaUrl(mediaUrl)
                .mediaName(worksDto.getName())
                .patternId(worksDto.getPatternId())
                .worksId(worksDto.getId())
                .accountId(operator.getOperatorId())
                .mediaId(snowflakeIdWorker.nextId())
                .mediaMd5(mediaMd5)
                .mediaSize((long) mediaBytes.length)
                .totalDuration(worksDto.getTotalDuration())
                .durationPerFrame(worksDto.getDurationPerFrame())
                .frames(worksDto.getFrames())
                .roundsNumber(worksDto.getRoundsNumber())
                .build());
        return mediaId;
    }

    private void renderAsync(Operator operator, Integer mediaId, List<LayerDto> layerDtos) {
        CompletableFuture.supplyAsync(() ->
                patternService.render(layerDtos))
                .thenAccept(rendered -> collageMediaService.updateMediaUrl(operator, mediaId, rendered));
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void updateSynchro(Integer id) {
        int res = collageWorksDao.updateByPrimaryKeySelective(MgkCollageWorksPo.builder()
                .id(id)
                .isSynchro(WhetherEnum.YES.getCode())
                .build());
        Assert.isTrue(Utils.isPositive(res), "更新同步状态失败");
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void delete(Operator operator, List<Integer> ids) {
        MgkCollageWorksPoExample example = new MgkCollageWorksPoExample();
        example.or().andAccountIdEqualTo(operator.getOperatorId()).andIdIn(ids);
        MgkCollageWorksPo po = MgkCollageWorksPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build();
        int res = collageWorksDao.updateByExampleSelective(po, example);
        Assert.isTrue(Utils.isPositive(res), "删除失败");
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    public void editWorks(Operator operator, CollageEditWorksNoLayersDto dto) {
        updateWorks(operator, dto);
        updateWorksCover(operator, dto.getCover());
    }

    private void updateWorksCover(Operator operator, CollageCoverDto cover) {
        collageCoverService.update(operator, cover);
    }

    private void updateWorks(Operator operator, CollageEditWorksNoLayersDto worksDto) {
        MgkCollageWorksPo po = MgkCollageWorksPo.builder()
                .id(worksDto.getId())
                .roundsNumber(worksDto.getRoundsNumber())
                .frames(worksDto.getFrames())
                .durationPerFrame(worksDto.getDurationPerFrame())
                .totalDuration(worksDto.getTotalDuration())
                .worksSize(worksDto.getWorksSize())
                .worksMd5(worksDto.getWorksMd5())
                .renderImage(worksDto.getRenderImage())
                .build();
        collageWorksDao.updateByPrimaryKeySelective(po);

        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .accountId(operator.getOperatorId())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_WORKS.getCode())
                .operateType(CollageOperateTypeEnum.COLLAGE_WORKS_MODIFY.getCode())
                .objId(worksDto.getId().longValue())
                .newValue("")
                .oldValue("")
                .operatorType(operator.getOperatorType().getCode())
                .operatorUsername(operator.getOperatorName())
                .build());
    }

    public void synWorksCompress(Operator operator, Integer id, CollageMediaDto mediaDto) {
        CollageWorksDto worksDto = getWorksBaseInfoById(id);
        Assert.isTrue(operator.getOperatorId().equals(worksDto.getAccountId()), "您不能操作不属于您的作品");
        Integer mediaId = collageMediaService.insert(operator, CollageMediaDto.builder()
                .mediaRatio(mediaDto.getWidth() * 10000 / mediaDto.getHeight())
                .width(mediaDto.getWidth())
                .height(mediaDto.getHeight())
                .mediaType(CollageMediaTypeEnum.PIC_FILE.getCode())
                .mediaOrigin(CollageMediaOriginEnum.PATTERN_UPLOAD.getCode())
                .mediaUrl(mediaDto.getMediaUrl())
                .mediaName(mediaDto.getMediaName())
                .patternId(worksDto.getPatternId())
                .worksId(worksDto.getId())
                .accountId(operator.getOperatorId())
                .mediaId(snowflakeIdWorker.nextId())
                .mediaMd5(mediaDto.getMediaMd5())
                .mediaSize(mediaDto.getMediaSize())
                .build());

        // 是否渲染
        if (WhetherEnum.NO.getCode().equals(worksDto.getIsSynchro())) {
            updateSynchro(id);
        }

        // 日志
        collageLogService.insertLog(NewCollageOperationLogDto.builder()
                .operatorUsername(operator.getOperatorName())
                .operatorType(operator.getOperatorType().getCode())
                .oldValue("")
                .newValue("")
                .objId(id.longValue())
                .operateType(CollageOperateTypeEnum.COLLAGE_WORKS_SYNCHRO.getCode())
                .objFlag(CollageLogObjFlagEnum.COLLAGE_WORKS.getCode())
                .accountId(operator.getOperatorId())
                .build());
    }
}
