package com.bilibili.mgk.material.center.service.course.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.biz.common.doc.tree.model.DocFlattenNode;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */

@Data
@Accessors(chain = true)
public class CourseDirectory implements SnakeCaseBody {


    private Long directoryId;


    private String directoryName;


    /**
     * level = depth -1
     */
    private Integer level;


    private Integer sortPriority;


    private List<CourseDirectory> childDirectory;

    private String nodeType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime mtime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime ctime;

    // 后面新加字段
    private Boolean isShow;


    public static CourseDirectory fromDocFlattenNode(DocFlattenNode node) {

        return new CourseDirectory()
                .setDirectoryId(node.getNodeId())
                .setDirectoryName(node.getNodeName())
                // root=1 level=0; root=2 level=1
                .setLevel(node.getDepth() - 1)
                .setNodeType(node.getNodeType())
                .setMtime(node.getMtime())
                .setCtime(node.getCtime())
                .setSortPriority(node.getSortPriority())
                ;
    }


}
