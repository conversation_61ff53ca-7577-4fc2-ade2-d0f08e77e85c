package com.bilibili.mgk.platform.portal.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class EncryptionUtils {
    public final static String ALGORITHM_CBC = "AES/CBC/PKCS5Padding";
    private static final String key = "h3yf6egdja5yq6dg";
    private static final String ivParam = "kijw7f6sdd5kko08";

    private static Cipher cbcCipher = getCipher();

    private static Cipher getCipher() {
        try {
            final Cipher cipher = Cipher.getInstance(ALGORITHM_CBC);
            byte[] raw = key.getBytes();
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            IvParameterSpec iv = new IvParameterSpec(ivParam.getBytes());//使用CBC模式，需要一个向量iv，可增加加密算法的强度
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            return cipher;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String encrypt(String s) throws Exception {
        if (cbcCipher == null) {
            cbcCipher = Cipher.getInstance(ALGORITHM_CBC);
            byte[] raw = key.getBytes();
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            IvParameterSpec iv = new IvParameterSpec(ivParam.getBytes());//使用CBC模式，需要一个向量iv，可增加加密算法的强度
            cbcCipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
        }

        byte[] encrypted = cbcCipher.doFinal(s.getBytes("utf-8"));
        return com.google.common.io.BaseEncoding.base64Url().encode(encrypted);
    }
}