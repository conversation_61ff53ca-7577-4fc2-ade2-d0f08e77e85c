package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName WechatPackageInfoDto
 * <AUTHOR>
 * @Date 2022/6/17 3:26 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WechatPackageInfoDto implements Serializable {
    /**
     * 微信包id
     */
    private Integer id;

    /**
     * 微信包类型 0-个人号 1-公众号 2-企业微信 3-其他
     */
    private Integer type;

    /**
     * 微信号
     */
    private List<WechatAccountInfoDto> wechatAccounts;
}
