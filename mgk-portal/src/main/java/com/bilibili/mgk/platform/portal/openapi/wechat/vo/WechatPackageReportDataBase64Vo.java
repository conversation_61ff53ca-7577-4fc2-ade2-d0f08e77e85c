package com.bilibili.mgk.platform.portal.openapi.wechat.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @ClassName Base64WechatPackageDataVo
 * <AUTHOR>
 * @Date 2022/6/18 8:31 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WechatPackageReportDataBase64Vo {
    @ApiModelProperty(value = "Base64加密的微信包提交数据")
    @NotBlank
    private String wechat_package_datas;

    @ApiModelProperty(value = "APPKEY")
    private String appkey;
}
