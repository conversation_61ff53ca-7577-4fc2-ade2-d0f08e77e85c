package com.bilibili.mgk.material.center.repository.base;

import com.bilibili.mgk.material.center.repository.po.MgkRejectedMaterialAigcRecordPo;
import com.bilibili.mgk.material.center.repository.po.MgkRejectedMaterialAigcRecordPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MgkRejectedMaterialAigcRecordMapper {

    long countByExample(MgkRejectedMaterialAigcRecordPoExample example);

    int deleteByExample(MgkRejectedMaterialAigcRecordPoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MgkRejectedMaterialAigcRecordPo record);

    int insertSelective(MgkRejectedMaterialAigcRecordPo record);

    List<MgkRejectedMaterialAigcRecordPo> selectByExample(MgkRejectedMaterialAigcRecordPoExample example);

    MgkRejectedMaterialAigcRecordPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkRejectedMaterialAigcRecordPo record,
            @Param("example") MgkRejectedMaterialAigcRecordPoExample example);

    int updateByExample(@Param("record") MgkRejectedMaterialAigcRecordPo record,
            @Param("example") MgkRejectedMaterialAigcRecordPoExample example);

    int updateByPrimaryKeySelective(MgkRejectedMaterialAigcRecordPo record);

    int updateByPrimaryKey(MgkRejectedMaterialAigcRecordPo record);
}