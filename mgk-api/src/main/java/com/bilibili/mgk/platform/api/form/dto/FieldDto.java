package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 类目
 * <AUTHOR>
 * @Date 2023.11.15 12:21
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldDto implements Serializable {

    private static final long serialVersionUID = 3057326338742726192L;

    private String name;

    private String type;

}
