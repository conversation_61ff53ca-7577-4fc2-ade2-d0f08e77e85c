package com.bilibili.mgk.material.center.service.creative.vo;

import io.swagger.annotations.ApiModelProperty;
import io.vavr.Lazy;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/25
 */

@Data
@Accessors(chain = true)
public class RisingFastVideoIdQuery implements SnakeCaseBody {


    @ApiModelProperty(value = "视频id")
    private Long avid;

    @ApiModelProperty(value = "聚合天数，7d、30d、90d、30d_after_creation, 分别代表近7天、30天、90天、发布后30天")
    private DateAggregation dayType;


    @ApiModelProperty("视频发布时间，dayType=30d_after_creation 时参数必填")
    private Lazy<Long> pubtime;


    public void validate() {

        Assert.notNull(avid, "视频id不能为空");
        Assert.notNull(dayType, "dayType不能为空");

        dayType.validateRisingFatVideoDetailQuery();
    }

}
