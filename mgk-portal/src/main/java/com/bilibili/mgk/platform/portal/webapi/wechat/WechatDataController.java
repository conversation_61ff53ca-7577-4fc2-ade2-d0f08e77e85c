package com.bilibili.mgk.platform.portal.webapi.wechat;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.data.dto.QueryWechatReportDataDto;
import com.bilibili.mgk.platform.api.data.dto.WechatPackageReportDataInfoDto;
import com.bilibili.mgk.platform.api.data.service.IMgkWechatPackageDataService;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatPackageDto;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatAccountService;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatPackageService;
import com.bilibili.mgk.platform.common.utils.DataUtils;
import com.bilibili.mgk.platform.portal.annotation.ExcelResources;
import com.bilibili.mgk.platform.portal.common.BasicExportController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Pagination;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.excel.ExcelUtil;
import com.bilibili.mgk.platform.portal.service.WebWechatService;
import com.bilibili.mgk.platform.portal.webapi.form.vo.FormDataVo;
import com.bilibili.mgk.platform.portal.webapi.wechat.vo.WechatReportDataVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName WechatDataController
 * <AUTHOR>
 * @Date 2022/9/21 2:34 下午
 * @Version 1.0
 **/
@RestController
@RequestMapping("/web_api/v1/wechat/data")
@Api(value = "/wechat/data", description = "微信加粉数据相关")
@Slf4j
public class WechatDataController extends BasicExportController<WechatReportDataVo> {

    @Autowired
    private IMgkWechatPackageDataService wechatPackageDataService;
    @Autowired
    private IMgkWechatPackageService mgkWechatPackageService;
    @Autowired
    private IMgkWechatAccountService mgkWechatAccountService;

    @ApiOperation(value = "微信加粉数据列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Pagination<List<WechatReportDataVo>>> getMgkWechatPackageDataList(
            @ApiIgnore Context context,
            @ApiParam("微信组件ID") @RequestParam(value = "wechatComponentIdStr", required = false) Integer wechatComponentId,
            @ApiParam("微信组件ID") @RequestParam(value = "wechatComponentIdStrList", required = false) List<Integer> wechatComponentIdList,
            @ApiParam("微信组件类型 0-微信包 1-微信号") @RequestParam(value = "type") Integer type,
            @ApiParam("开始时间") @RequestParam(value = "begin_time", defaultValue = "") Long beginTime,
            @ApiParam("结束时间") @RequestParam(value = "end_time", defaultValue = "") Long endTime,
            @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(value = "size", defaultValue = "15") Integer size) {

        Assert.isTrue(Utils.isPositive(wechatComponentId) || !CollectionUtils.isEmpty(wechatComponentIdList),
                "微信组件id不能为空");
        QueryWechatReportDataDto paramDto = QueryWechatReportDataDto.builder()
                .accountId(context.getAccountId())
                .beginCtime(beginTime == null ? Utils.getToday() : new Timestamp(beginTime))
                .endCtime(endTime == null ? Utils.getToday() : new Timestamp(endTime))
                .download(false)
                .page(Page.valueOf(page, size))
                .build();
        if (!Utils.isPositive(type)) {
            if(CollectionUtils.isEmpty(wechatComponentIdList)){
                paramDto.setWechatPackageId(wechatComponentId);
            }else {
                wechatComponentId = wechatComponentIdList.get(0);
                paramDto.setWechatPackageIds(wechatComponentIdList);
            }
            WechatPackageDto wechatPackageDto = mgkWechatPackageService.queryValidBaseDtoById(wechatComponentId);
            Assert.isTrue(wechatPackageDto.getAccountId().equals(context.getAccountId()), "您不能查看不属于自己的微信加粉信息");
        } else {
            if(CollectionUtils.isEmpty(wechatComponentIdList)){
                paramDto.setWechatAccountId(wechatComponentId);
            }else {
                wechatComponentId = wechatComponentIdList.get(0);
                paramDto.setWechatAccountIds(wechatComponentIdList);
            }
            WechatAccountDto wechatAccountDto = mgkWechatAccountService.getValidDtoById(wechatComponentId);
            Assert.isTrue(wechatAccountDto.getAccountId().equals(context.getAccountId()), "您不能查看不属于自己的微信加粉信息");
        }

        PageResult<WechatPackageReportDataInfoDto> pageResult = wechatPackageDataService.queryWechatPackageData(paramDto);
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), Collections.emptyList()));
        }

        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), WebWechatService.convertReportDataDtos2Vos(pageResult.getRecords())));
    }

    @ApiOperation(value = "获取微信加粉数据表头")
    @RequestMapping(value = "/header", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<String>> getFormDataHeaders(@ApiIgnore Context context) {
        return Response.SUCCESS(this.buildExcelHeaders(WechatReportDataVo.class));
    }

    @ApiOperation(value = "微信加粉数据列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public
    @ResponseBody
    void exportMgkFormDataList(HttpServletResponse response, @ApiIgnore Context context,
                               @ApiParam("微信组件ID") @RequestParam(value = "wechatComponentIdStr", required = false) Integer wechatComponentId,
                               @ApiParam("微信组件ID") @RequestParam(value = "wechatComponentIdStrList", required = false) List<Integer> wechatComponentIdList,
                               @ApiParam("微信组件类型 0-微信包 1-微信号") @RequestParam(value = "type") Integer type,
                               @ApiParam("开始时间") @RequestParam(value = "begin_time", defaultValue = "") Long beginTime,
                               @ApiParam("结束时间") @RequestParam(value = "end_time", defaultValue = "") Long endTime) throws Exception {

        Assert.isTrue(Utils.isPositive(wechatComponentId) || !CollectionUtils.isEmpty(wechatComponentIdList),
                "微信组件id不能为空");

        QueryWechatReportDataDto paramDto = QueryWechatReportDataDto.builder()
                .accountId(context.getAccountId())
                .beginCtime(beginTime == null ? Utils.getToday() : new Timestamp(beginTime))
                .endCtime(endTime == null ? Utils.getToday() : new Timestamp(endTime))
                .download(true)
                .build();

        String name = "";
        if (!Utils.isPositive(type)) {
            if(CollectionUtils.isEmpty(wechatComponentIdList)){
                paramDto.setWechatPackageId(wechatComponentId);
            }else {
                wechatComponentId = wechatComponentIdList.get(0);
                paramDto.setWechatPackageIds(wechatComponentIdList);
            }
            WechatPackageDto wechatPackageDto = mgkWechatPackageService.queryValidBaseDtoById(wechatComponentId);
            name = wechatPackageDto.getName();
            Assert.isTrue(wechatPackageDto.getAccountId().equals(context.getAccountId()), "您不能查看不属于自己的微信加粉信息");
        } else {
            if(CollectionUtils.isEmpty(wechatComponentIdList)){
                paramDto.setWechatAccountId(wechatComponentId);
            }else {
                wechatComponentId = wechatComponentIdList.get(0);
                paramDto.setWechatAccountIds(wechatComponentIdList);
            }
            WechatAccountDto wechatAccountDto = mgkWechatAccountService.getValidDtoById(wechatComponentId);
            name = wechatAccountDto.getWechatAccount();
            Assert.isTrue(wechatAccountDto.getAccountId().equals(context.getAccountId()), "您不能查看不属于自己的微信加粉信息");
        }

        PageResult<WechatPackageReportDataInfoDto> pageResult = wechatPackageDataService.queryWechatPackageData(paramDto);
        List<WechatReportDataVo> resultVos = WebWechatService.convertReportDataDtos2Vos(pageResult.getRecords());
        List<String> headers = this.buildExcelHeaders(WechatReportDataVo.class);
        List<List<String>> datas = this.getExcelDatas(resultVos);
        try {
            this.exportList2Excel(response, headers, datas,
                    this.processFileName(name + "_" + Utils.getTimestamp2String(Utils.getToday()), wechatComponentId) + ".xls");
        } catch (IOException e) {
            log.error("export wechat package List2Excel failed", e);
            throw new ServiceException("微信加粉数据导出失败");
        }
    }

    private String processFileName(String name, Integer wechatComponentId) {
        try {
            return DataUtils.StringFilter(name);
        } catch (Exception e) {
            log.error("DataUtils.StringFilter failed", e);
            return String.valueOf(wechatComponentId);
        }
    }



}
