package com.bilibili.mgk.platform.common;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @file: MyMgkSortDataEnum
 * @author: x<PERSON><PERSON>an
 * @description: 我的落地页页面的排序字段
 *
 **/
@AllArgsConstructor
public enum MyMgkSortDataEnum {
    MTIME(0, "创建时间"),
    CONSUME(1, "消费"),
    CLICK(2, "点击"),
    CONV_CNT(3, "转化数"),
    CONV_RATE(4,"转化率"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    //可以通过clickHouse排序的
    public final static List<Integer> SUPPORT_SORT_BY_CH = Lists.newArrayList(CONSUME.code, CLICK.code,
            CONV_CNT.code, CONV_RATE.code);

    public static MyMgkSortDataEnum getByCode(Integer code) {
        for (MyMgkSortDataEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MyMgkSortDataEnum " + code);
    }
}
