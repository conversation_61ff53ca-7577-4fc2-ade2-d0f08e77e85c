package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.google.common.base.Splitter;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler("MessageComponentRefreshJob")
public class MessageComponentRefreshJob extends IJobHandler {

    @Autowired
    private IMgkLandingPageService iMgkLandingPageService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("MessageComponentRefreshJob start");
        List<Integer> accountIds =  Splitter.on(",").splitToList(s).stream().map(account->Integer.valueOf(account)).collect(Collectors.toList());
        log.info("accountIds={}",accountIds);
        iMgkLandingPageService.refreshMessageComponent(accountIds);
        log.info("MessageComponentRefreshJob done");
        return SUCCESS;
    }
}
