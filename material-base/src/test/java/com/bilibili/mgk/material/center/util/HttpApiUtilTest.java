package com.bilibili.mgk.material.center.util;

import com.bilibili.mgk.material.center.util.HttpRequestHostBindInterceptor.RequestHostHolder;
import org.junit.BeforeClass;
import org.junit.Test;
import pleiades.component.http.client.response.BiliResultApiResponse;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/19
 */
public class HttpApiUtilTest {

    private static MgkApi api;

    /**
     * -Ddiscovery_zone=sh001 -Ddeploy_env=uat -Dzone=sh001 -Dconf_local=true
     * -Dconf_token=c86b8cd1cd89325e415f46de717d1d0b -Dpaladin_app_id=sycpb.cpm.mgk-portal
     * -Dpleiades.config.enabled=false -Dserver.servlet.context-path=/
     */
    @BeforeClass
    public static void init() {

        setEnv("discovery_zone", "sh001");
        setEnv("deploy_env", "uat");
        setEnv("zone", "sh001");
        setEnv("conf_local", "true");
        setEnv("conf_token", "c86b8cd1cd89325e415f46de717d1d0b");
        setEnv("paladin_app_id", "sycpb.cpm.mgk-portal");
        setEnv("pleiades.config.enabled", "false");
        setEnv("server.servlet.context-path", "/");

        api = HttpApiUtil.buildApiViaDiscovery(
                MgkApi.class,
                "discovery://sycpb.cpm.mgk-portal",
                "sh001",
                false,
                "sock5ProxyHost",
                8080
        );


    }

    public static void setEnv(String key, String value) {
        System.setProperty(key, value);
    }


    @Test
    public void testCall1() {

        BiliResultApiResponse<Object> r = HttpApiUtil.call(
                api.searchReference(""));

        System.out.println(r);
        System.out.println(r.getResult());
        System.out.println(r.getCode());
        System.out.println(r.getMessage());


    }


    @Test
    public void testCallThenFindRealRequestHostAndNextTimeUseSpecificHost() {

        RequestHostHolder requestHostHolder = new RequestHostHolder();

        api = HttpApiUtil.buildApiViaDiscovery(
                MgkApi.class,
                "discovery://sycpb.cpm.mgk-portal",
                "sh001",
                false,
                "sock5ProxyHost",
                8080,
                new HttpRequestHostBindInterceptor(requestHostHolder)
        );

        BiliResultApiResponse<Object> r = HttpApiUtil.call(
                api.searchReference(""));

//        System.out.println(r);
//        System.out.println(r.getResult());
        System.out.println(r.getCode());
//        System.out.println(r.getMessage());

        System.out.println(requestHostHolder.getRealRequestHostAndClear());


    }


    @Test
    public void testCallThenFindRealRequestHostAndNextTimeUseSpecificHostTimes() {

        RequestHostHolder requestHostHolder = new RequestHostHolder();

        api = HttpApiUtil.buildApiViaDiscovery(
                MgkApi.class,
                "discovery://sycpb.cpm.mgk-portal",
                "sh001",
                false,
                "sock5ProxyHost",
                8080,
                new HttpRequestHostBindInterceptor(requestHostHolder)
        );

        for (int i = 0; i < 100; i++) {
            BiliResultApiResponse<Object> r = HttpApiUtil.call(
                    api.searchReference(""));

//        System.out.println(r);
//        System.out.println(r.getResult());
            System.out.println(r.getCode());
//        System.out.println(r.getMessage());

            System.out.println(requestHostHolder.getRealRequestHostAndClear());

        }


    }


    @Test
    public void testCallThenFindRealRequestHostAndNextTimeUseSpecificHostAndStickHost() {

        RequestHostHolder requestHostHolder = new RequestHostHolder();

        api = HttpApiUtil.buildApiViaDiscovery(
                MgkApi.class,
                "discovery://sycpb.cpm.mgk-portal",
                "sh001",
                false,
                "sock5ProxyHost",
                8080,
                new HttpRequestHostBindInterceptor(requestHostHolder)
        );

        BiliResultApiResponse<Object> r0 = HttpApiUtil.call(
                api.searchReference(""));

//        System.out.println(r);
//        System.out.println(r.getResult());
        System.out.println(r0.getCode());
//        System.out.println(r.getMessage());

        String host = requestHostHolder.getRealRequestHostAndClear();

        for (int i = 0; i < 100; i++) {

            requestHostHolder.setSpecificHost(host);

            BiliResultApiResponse<Object> r = HttpApiUtil.call(
                    api.searchReference(""));

//        System.out.println(r);
//        System.out.println(r.getResult());
            System.out.println(r.getCode());
//        System.out.println(r.getMessage());

            System.out.println(requestHostHolder.getRealRequestHostAndClear());

        }


    }


}