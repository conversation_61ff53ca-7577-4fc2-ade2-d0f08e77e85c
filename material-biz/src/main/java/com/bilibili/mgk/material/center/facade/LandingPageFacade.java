package com.bilibili.mgk.material.center.facade;

import com.bapis.datacenter.service.oneservice.AdvFilter;
import com.bapis.datacenter.service.oneservice.OsHeader;
import com.bapis.datacenter.service.oneservice.PageReq;
import com.bapis.datacenter.service.oneservice.PageVo;
import com.bapis.datacenter.service.oneservice.QueryReq;
import com.bapis.datacenter.service.oneservice.QueryReq.Builder;
import com.bapis.datacenter.service.oneservice.QueryResp;
import com.bilibili.mgk.material.center.config.MaterialOneServiceConfig;
import com.bilibili.mgk.material.center.config.MaterialQueryProfileConfig;
import com.bilibili.mgk.material.center.event.LandingPageDataReadyEvent;
import com.bilibili.mgk.material.center.facade.converter.HotLandingPageOnesDTO;
import com.bilibili.mgk.material.center.facade.proxy.OneServiceFlowControlProxy;
import com.bilibili.mgk.material.center.service.creative.model.HotLandingPage;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.HotLandingPageQuery;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * SELECT log_date, page_id, commerce_category_second_id, promotion_purpose_type, page_url,
 * commerce_category_second_name, commerce_category_first_id, commerce_category_first_name, top_creative_id,
 * top_creative_title, click, conv_num, page_arrive, page_load_time, page_slide_num, page_stay_time, cvr, arrive_rate,
 * slide_rate, avg_stay_time, avg_load_time, cvr_rk, arrive_rate_rk, slide_rate_rk, avg_stay_time_rk, avg_load_time_rk,
 * day_type, page_cover, product_name,
 * <p>
 * lower(COALESCE(product_name, '')) as lower_product_name, lower(COALESCE(top_creative_title, '')) as
 * lower_top_creative_title, concat(lower(COALESCE(top_creative_title, '')), lower(COALESCE(product_name, ''))) as
 * lower_search_keyword FROM bi_sycpb.ads_flow_creative_hot_page_analysis_data_i_d
 *
 * <AUTHOR>
 * @desc
 * @date 2024/5/16
 * @since 2024-08-06 支持新行业， 原字段直接映射为新行业字段， 无需变更，只需更新api_id 配置
 */
@Slf4j
@Service
public class LandingPageFacade {


    @Resource
    private OneServiceFlowControlProxy serviceOpenApiManagerBlockingStub;

    @Resource
    private ApiOneServiceBase apiOneServiceBase;

    @Resource
    private MaterialOneServiceConfig oneServiceConfig;

    @Resource
    private MaterialQueryProfileConfig profileConfig;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Value("${material.oneservice.landing-page-v2.app-key:496b1dd505103413f42117ff1d5840ca}")
    private String appKeyV2;

    @Value("${material.oneservice.landing-page-v2.secret:tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=}")
    private String secretV2;

    @Value("${material.oneservice.landing-page-v2.api-id:api_2381}")
    private String apiIdV2;

    @Value("${material.oneservice.landing-page.scan.ps:1000}")
    private Integer scanPs;
//
//    @Value("${material.oneservice.landing-page.app-key:496b1dd505103413f42117ff1d5840ca}")
//    private String appKey;
//
//    @Value("${material.oneservice.landing-page.secret:tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=}")
//    private String secret;
//
//    @Value("${material.oneservice.landing-page.api-id:api_1957}")
//    private String apiId;

    @Value("${material.oneservice.landing-page.ready.date-key:mgk.landing-page.ready.date}")
    private String readyDateKey;

    private DateTimeFormatter latestUpdateTimeFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    @Scheduled(fixedRateString = "${material.oneservice.landing-page.ready.schedule-rate:300000}")
    public void scheduleProbeDataReadyLogDate() {

        // 今天如果是03-09 那么预期就绪的logdate是0308
        LocalDate expectDate = LocalDate.now().minusDays(1);

        String expectDateString = latestUpdateTimeFormat.format(expectDate);

        if (expectDateString.equals(Optional
                .ofNullable(redissonClient.<String>getBucket(readyDateKey).get())
                .orElse(""))) {

            // ready
            log.info("Already finished task scheduleProbeDataReadyLogDate date={}, dataset={}",
                    expectDateString, "landing-page");
            return;
        }

        Tuple2<String, LocalDate> dataReadyDate =
                apiOneServiceBase.findLatestOpenApiLogDateAndDate(apiIdV2);

        if (dataReadyDate._2.equals(expectDate)) {
            // 刚探测到接口数据就绪
            try {
                eventPublisher.publishEvent(new LandingPageDataReadyEvent(this)
                        .setLogdate(dataReadyDate._1)
                        .setLogdateDate(dataReadyDate._2)
                );
            } finally {
                redissonClient.<String>getBucket(readyDateKey).set(expectDateString);
            }

        } else {
            // 还未就绪
            log.info("Task scheduleProbeDataReadyLogDate not ready");
        }

    }



    public Optional<HotLandingPage> detail(
            String pageId, String commerceCategorySecondId, String promotionPurposeType,
            DateAggregation dayType
    ) {

        Pagination<List<HotLandingPage>> page = this.landingPage(new HotLandingPageQuery()
                .setPageId(pageId)
                .setCommerceCategorySecondId(commerceCategorySecondId)
                .setPromotionPurposeType(promotionPurposeType)
                .setDayType(dayType)
                .setSortBy(MaterialSortBy.cvr)
                .setPn(1)
                .setPs(20)
        );

        if (CollectionUtils.isEmpty(page.getData())) {

            return Optional.empty();
        } else {
            return Optional.of(page.getData().get(0));
        }
    }


    public Iterator<List<HotLandingPage>> scan(String logdate, LocalDate logdateDate) {

        Iterator<DateAggregation> dayTypes = Lists.newArrayList(DateAggregation.d3, DateAggregation.d7,
                DateAggregation.d30).iterator();

        return new Iterator<List<HotLandingPage>>() {
            Map<DateAggregation, AtomicInteger> pnMap = new HashMap<>();
            Map<DateAggregation, AtomicBoolean> hasNextMap = new HashMap<>();
            AtomicReference<DateAggregation> current = new AtomicReference<>(dayTypes.next());


            @Override
            public boolean hasNext() {
                return dayTypes.hasNext() ||
                        hasNextMap.computeIfAbsent(current.get(), k -> new AtomicBoolean(true)).get();
            }

            @Override
            public List<HotLandingPage> next() {

                DateAggregation dayType = current.get();

                if (!hasNextMap.computeIfAbsent(dayType, k -> new AtomicBoolean(true)).get()) {
                    // 这一轮dayType 已完结

                    current.set(dayTypes.next());
                    hasNextMap.put(current.get(), new AtomicBoolean(true));
                    dayType = current.get();
                }

                AtomicInteger pn = pnMap.computeIfAbsent(dayType, k -> new AtomicInteger(1));

                HotLandingPageQuery query = emptyScanQuery(pn.getAndIncrement(), dayType);

                Pagination<List<HotLandingPage>> rsp = doPage(query, Tuple.of(logdate, logdateDate));

                if (org.springframework.util.CollectionUtils.isEmpty(rsp.getData())) {
                    hasNextMap.get(dayType).set(false);
                }

                return rsp.getData();
            }

            private HotLandingPageQuery emptyScanQuery(Integer pn, DateAggregation dateType) {
                return new HotLandingPageQuery()
                        .setDayType(dateType)
                        .setPn(pn)
                        .setPs(scanPs)
                        // 注意可能会有间隔页有重复数据的问题，暂时先不用关心
                        .setSortBy(MaterialSortBy.cvr)
                        ;
            }

        };
    }

    public Pagination<List<HotLandingPage>> landingPage(HotLandingPageQuery query) {
        Tuple2<String, LocalDate> logdate = apiOneServiceBase.findLatestOpenApiLogDateAndDate(apiIdV2);

        return this.doPage(query, logdate);
    }


    private Pagination<List<HotLandingPage>> doPage(HotLandingPageQuery query, Tuple2<String, LocalDate> logdate) {


        String latestUpdateTime = dateTimeFormatter.format(logdate._2);

        Builder openApiReq = baseReq()
                .addAllOrders(Lists.newArrayList(query.getSortBy().toLandingPageOrderDesc()))
                .setPageReq(PageReq.newBuilder()
                        .setPage(query.getPn())
                        .setPageSize(query.getPs())
                        .build());

        List<AdvFilter.Builder> rootAdvFilterExpressions = new ArrayList<>();

        this.appendAndParams(openApiReq, "log_date", "=", Lists.newArrayList(logdate._1), rootAdvFilterExpressions);

        this.appendAndParams(openApiReq, "day_type", "=", Lists.newArrayList(query.getDayType().getDayType()),
                rootAdvFilterExpressions);

        // {"422":["435","436"], "440":["441"]}
        //
        if (MapUtils.isNotEmpty(query.getIndustryFilters())) {

            Map<String, Boolean> commerceCategorySecondContainsAll = profileConfig.containsAllCommerceSecondsCategory(
                    query.getIndustryFilters());

            rootAdvFilterExpressions.add(
                    AdvFilter.newBuilder()
                            .setType("or")
                            .addAllExpress(query.getIndustryFilters().entrySet().stream().map(entry -> {
                                AdvFilter.Builder nestedAndExpress = AdvFilter.newBuilder().setType("and")
                                        .addExpress(AdvFilter.newBuilder()
                                                .setField("commerce_category_first_id")
                                                .setOperator("=")
                                                .addAllValues(Lists.newArrayList(entry.getKey())));

                                if (CollectionUtils.isNotEmpty(entry.getValue())
                                        // 如果包含所有，则不添加参数，优化查询效率
                                        && !commerceCategorySecondContainsAll.getOrDefault(entry.getKey(), false)) {

                                    nestedAndExpress.addExpress(AdvFilter.newBuilder()
                                            .setField("commerce_category_second_id")
                                            .setOperator("in")
                                            .addAllValues(entry.getValue()));
                                }

                                return nestedAndExpress.build();
                            }).collect(Collectors.toList()))
            );


        } else {
            // v1版本单行业简单复合
            if (StringUtils.isNotEmpty(query.getCommerceCategoryFirstId())) {
                this.appendAndParams(openApiReq, "commerce_category_first_id", "=",
                        Lists.newArrayList(query.getCommerceCategoryFirstId()), rootAdvFilterExpressions);
            }

            if (StringUtils.isNotEmpty(query.getCommerceCategorySecondId())) {
                this.appendAndParams(openApiReq, "commerce_category_second_id", "=",
                        Lists.newArrayList(query.getCommerceCategorySecondId()), rootAdvFilterExpressions);
            }
        }

        if (StringUtils.isNotEmpty(query.getPromotionPurposeType())) {
            this.appendAndParams(openApiReq,
                    "promotion_purpose_type",
                    "in",
                    Lists.newArrayList(Splitter.on(",").splitToList(query.getPromotionPurposeType())),
                    rootAdvFilterExpressions);

        }

        if (StringUtils.isNotEmpty(query.getPageId())) {
            this.appendAndParams(openApiReq, "page_id", "=", Lists.newArrayList(query.getPageId()),
                    rootAdvFilterExpressions);
        }

        if (StringUtils.isNotEmpty(query.getKeyword()) && query.getKeywordType() != null) {

            //关键字词批量搜索，这个只支持大小写不敏感 兼容全角空格
            List<String> keywords = Splitter
                    .on(" ")
                    .splitToList(query.getKeyword().replaceAll("　", " "));

            List<AdvFilter.Builder> keywordFilters = keywords.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(keyword -> {
                        return AdvFilter.newBuilder()
                                .setField(query.getKeywordType().switchSearchKeywordFieldOfLandingPage())
                                .setOperator("like")
                                .addAllValues(
                                        Lists.newArrayList(keyword.toLowerCase()));

                    }).collect(Collectors.toList());

            rootAdvFilterExpressions.add(AdvFilter.newBuilder()
                    .setType("or")
                    .addAllExpress(keywordFilters.stream()
                            .map(AdvFilter.Builder::build)
                            .collect(Collectors.toList())));


        }

        openApiReq.setAdvFilter(
                AdvFilter.newBuilder()
                        .setType("and")
                        .addAllExpress(rootAdvFilterExpressions
                                .stream()
                                .map(AdvFilter.Builder::build)
                                .collect(Collectors.toList()))
                        .build()
        );

        QueryResp rsp = serviceOpenApiManagerBlockingStub.query(openApiReq.build());

        PageVo page = rsp.getPageVo();
        List<HotLandingPage> result = rsp.getRowsList().stream()
                .map(map -> HotLandingPageOnesDTO.fromMapValue(map.getValueMap())
                        .toBO()
                        .setLatestUpdateTime(latestUpdateTime))
                .collect(Collectors.toList());

        return new Pagination<>(page.getPage(), (int) page.getTotalSize(), result);

    }


    /**
     * @return
     */
    private Builder baseReq() {

        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey(appKeyV2)
                .setSecret(secretV2)
                .setApiId(apiIdV2)
                .build();

        Builder openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader);

        return openApiReq;

    }


    protected void appendAndParams(Builder openApiReq, String key, String op, List<String> values,
            List<AdvFilter.Builder> rootAdvFilterExpressions) {

        AdvFilter.Builder rootAndFilter = AdvFilter.newBuilder()
                .setField(key)
                .setOperator(op)
                .addAllValues(values);

        rootAdvFilterExpressions.add(rootAndFilter);
    }
}
