package com.bilibili.mgk.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MgkPageDownloadComponentHeightPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MgkPageDownloadComponentHeightPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPageIdIsNull() {
            addCriterion("page_id is null");
            return (Criteria) this;
        }

        public Criteria andPageIdIsNotNull() {
            addCriterion("page_id is not null");
            return (Criteria) this;
        }

        public Criteria andPageIdEqualTo(Long value) {
            addCriterion("page_id =", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotEqualTo(Long value) {
            addCriterion("page_id <>", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdGreaterThan(Long value) {
            addCriterion("page_id >", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdGreaterThanOrEqualTo(Long value) {
            addCriterion("page_id >=", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdLessThan(Long value) {
            addCriterion("page_id <", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdLessThanOrEqualTo(Long value) {
            addCriterion("page_id <=", value, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdIn(List<Long> values) {
            addCriterion("page_id in", values, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotIn(List<Long> values) {
            addCriterion("page_id not in", values, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdBetween(Long value1, Long value2) {
            addCriterion("page_id between", value1, value2, "pageId");
            return (Criteria) this;
        }

        public Criteria andPageIdNotBetween(Long value1, Long value2) {
            addCriterion("page_id not between", value1, value2, "pageId");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeIsNull() {
            addCriterion("total_block_size is null");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeIsNotNull() {
            addCriterion("total_block_size is not null");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeEqualTo(Integer value) {
            addCriterion("total_block_size =", value, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeNotEqualTo(Integer value) {
            addCriterion("total_block_size <>", value, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeGreaterThan(Integer value) {
            addCriterion("total_block_size >", value, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_block_size >=", value, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeLessThan(Integer value) {
            addCriterion("total_block_size <", value, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeLessThanOrEqualTo(Integer value) {
            addCriterion("total_block_size <=", value, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeIn(List<Integer> values) {
            addCriterion("total_block_size in", values, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeNotIn(List<Integer> values) {
            addCriterion("total_block_size not in", values, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeBetween(Integer value1, Integer value2) {
            addCriterion("total_block_size between", value1, value2, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalBlockSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("total_block_size not between", value1, value2, "totalBlockSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeIsNull() {
            addCriterion("total_download_component_size is null");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeIsNotNull() {
            addCriterion("total_download_component_size is not null");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeEqualTo(Integer value) {
            addCriterion("total_download_component_size =", value, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeNotEqualTo(Integer value) {
            addCriterion("total_download_component_size <>", value, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeGreaterThan(Integer value) {
            addCriterion("total_download_component_size >", value, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_download_component_size >=", value, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeLessThan(Integer value) {
            addCriterion("total_download_component_size <", value, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeLessThanOrEqualTo(Integer value) {
            addCriterion("total_download_component_size <=", value, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeIn(List<Integer> values) {
            addCriterion("total_download_component_size in", values, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeNotIn(List<Integer> values) {
            addCriterion("total_download_component_size not in", values, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeBetween(Integer value1, Integer value2) {
            addCriterion("total_download_component_size between", value1, value2, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andTotalDownloadComponentSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("total_download_component_size not between", value1, value2, "totalDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeIsNull() {
            addCriterion("max_download_component_size is null");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeIsNotNull() {
            addCriterion("max_download_component_size is not null");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeEqualTo(Integer value) {
            addCriterion("max_download_component_size =", value, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeNotEqualTo(Integer value) {
            addCriterion("max_download_component_size <>", value, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeGreaterThan(Integer value) {
            addCriterion("max_download_component_size >", value, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_download_component_size >=", value, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeLessThan(Integer value) {
            addCriterion("max_download_component_size <", value, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeLessThanOrEqualTo(Integer value) {
            addCriterion("max_download_component_size <=", value, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeIn(List<Integer> values) {
            addCriterion("max_download_component_size in", values, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeNotIn(List<Integer> values) {
            addCriterion("max_download_component_size not in", values, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeBetween(Integer value1, Integer value2) {
            addCriterion("max_download_component_size between", value1, value2, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andMaxDownloadComponentSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("max_download_component_size not between", value1, value2, "maxDownloadComponentSize");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightIsNull() {
            addCriterion("total_first_screen_download_component_height is null");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightIsNotNull() {
            addCriterion("total_first_screen_download_component_height is not null");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightEqualTo(Integer value) {
            addCriterion("total_first_screen_download_component_height =", value, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightNotEqualTo(Integer value) {
            addCriterion("total_first_screen_download_component_height <>", value, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightGreaterThan(Integer value) {
            addCriterion("total_first_screen_download_component_height >", value, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_first_screen_download_component_height >=", value, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightLessThan(Integer value) {
            addCriterion("total_first_screen_download_component_height <", value, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightLessThanOrEqualTo(Integer value) {
            addCriterion("total_first_screen_download_component_height <=", value, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightIn(List<Integer> values) {
            addCriterion("total_first_screen_download_component_height in", values, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightNotIn(List<Integer> values) {
            addCriterion("total_first_screen_download_component_height not in", values, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightBetween(Integer value1, Integer value2) {
            addCriterion("total_first_screen_download_component_height between", value1, value2, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }

        public Criteria andTotalFirstScreenDownloadComponentHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("total_first_screen_download_component_height not between", value1, value2, "totalFirstScreenDownloadComponentHeight");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}