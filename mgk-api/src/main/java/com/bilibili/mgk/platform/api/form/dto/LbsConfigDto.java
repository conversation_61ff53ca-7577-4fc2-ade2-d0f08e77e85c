package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LbsConfigDto implements Serializable{


    private static final long serialVersionUID = -8068356774920672483L;

    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 子级
     */
    private List<LbsOptionDto> children;

    /**
     * 标题
     */
    private String title;


    /**
     * 内容
     */
    private String content;

    /**
     * 类目
     */
    private List<FieldDto> fields;


}
