package com.bilibili.mgk.platform.biz.service.page.dto;

import com.bilibili.mgk.platform.api.download.dto.MgkPageDownloadComponentHeightDto;
import com.bilibili.mgk.platform.common.page_bean.AppPackageBean;
import com.bilibili.mgk.platform.common.page_bean.GameDto;
import com.bilibili.mgk.platform.common.page_bean.LauMiniGameCacheBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName RefreshPageConfigDto
 * <AUTHOR>
 * @Date 2022/11/7 5:54 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefreshPageConfigDto implements Serializable {

    private static final long serialVersionUID = 451912312321358L;

    private Long pageId;

    /**
     * 落地页类型 0-原始落地页 1-模板 2-联投副本 3-图文副本 4-影子副本
     */
    private Integer isModel;
    /**
     * 落地页模板样式
     */
    private Integer templateStyle;
    private List<String> showUrls;
    /**
     * 落地页配置内容
     */
    private String configStr;
    private List<String> awakenWhiteList;
    /**
     * 落地页下载白名单
     */
    private List<AppPackageBean> downloadWhiteList;
    /**
     * 落地页绑定表单id
     */
    private List<Long> formIds;
    /**
     * 落地页绑定微信包id
     */
    private List<Integer> wechatPackageIds;
    /**
     * 落地页帐户id
     */
    private Integer accountId;
    /**
     * 落地页下载组件信息
     */
    private MgkPageDownloadComponentHeightDto downloadComponentDto;

    //微信小游戏IDs
    private List<Integer> miniGameIds;

    private List<LauMiniGameCacheBo> miniGameCacheBos;

    //获客链接id列表
    private List<String> customerAcquisitionLinkIds;

    private List<GameDto> games;
}
