package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.soa.ISoaCreativeService;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.cpt.platform.soa.ISoaCptCreativeService;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.mgk.platform.api.landing_page.dto.*;
import com.bilibili.mgk.platform.api.log.service.IMgkLogService;
import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import com.bilibili.mgk.platform.biz.ad.dao.AccAccountAwakenAppMappingDao;
import com.bilibili.mgk.platform.biz.ad.dao.ResAppPackageApkAuthDao;
import com.bilibili.mgk.platform.biz.ad.dao.ResAppPackageDao;
import com.bilibili.mgk.platform.biz.ad.dao.ResAwakenAppWhitelistDao;
import com.bilibili.mgk.platform.biz.ad.po.AccAccountAwakenAppMappingPo;
import com.bilibili.mgk.platform.biz.ad.po.ResAppPackageApkAuthPo;
import com.bilibili.mgk.platform.biz.ad.po.ResAppPackagePo;
import com.bilibili.mgk.platform.biz.ad.po.ResAwakenAppWhitelistPo;
import com.bilibili.mgk.platform.biz.dao.*;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageAppPackageDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageAvidDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageShowUrlDao;
import com.bilibili.mgk.platform.biz.es.ESMgkModelPo;
import com.bilibili.mgk.platform.biz.po.*;
import com.bilibili.mgk.platform.biz.validator.MgkLandingPageValidator;
import com.bilibili.mgk.platform.common.LandingPageTypeEnum;
import com.bilibili.mgk.platform.common.TemplateStyleEnum;
import com.bilibili.mgk.platform.common.page_bean.AppPackageBean;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class LandingPageServiceDelegateTest extends BaseMockitoTest {
    @Mock
    private SnowflakeIdWorker snowflakeIdWorker;
    @Mock
    private MgkLandingPageValidator landingPageValidator;
    @Mock
    private IMgkLogService mgkLogService;
    @Mock
    private ISoaQueryAccountService soaQueryAccountService;
    @Mock
    private ISoaCptCreativeService soaCptCreativeService;
    @Mock
    private ISoaCreativeService soaCpcCreativeService;
    @Mock
    private ISoaAppPackageService soaAppPackageService;
    @Mock
    private MgkLandingPageDao mgkLandingPageDao;
    @Mock
    private MgkLandingPageConfigDao mgkLandingPageConfigDao;
    @Mock
    private MgkLandingPageShowUrlDao mgkLandingPageShowUrlDao;
    @Mock
    private MgkLandingPageAvidDao mgkLandingPageAvidDao;
    @Mock
    private ExtMgkLandingPageShowUrlDao extMgkLandingPageShowUrlDao;
    @Mock
    private ExtMgkLandingPageAvidDao extMgkLandingPageAvidDao;
    @Mock
    private MgkLandingPageAppPackageDao mgkLandingPageAppPackageDao;
    @Mock
    private ExtMgkLandingPageAppPackageDao extMgkLandingPageAppPackageDao;
    @Mock
    private RedisTemplate<String, String> stringRedisTemplate;
    @Mock
    private MgkPageFormMappingDao mgkPageFormMappingDao;
    @Mock
    private AccAccountAwakenAppMappingDao accAccountAwakenAppMappingDao;
    @Mock
    private ResAwakenAppWhitelistDao resAwakenAppWhitelistDao;
    @Mock
    private ResAppPackageDao resAppPackageDao;
    @Mock
    private Map<Integer, String> apkAuthCodeMap = new HashMap<>();
    @Mock
    private ResAppPackageApkAuthDao resAppPackageApkAuthDao;
    @Mock
    private MgkModelDao mgkModelDao;

    private ValueOperations valueOperations;

    @InjectMocks
    private LandingPageServiceDelegate landingPageServiceDelegate;

    @Before
    public void setUp() throws Exception {
        when(snowflakeIdWorker.nextId()).thenReturn(1L);
        when(mgkLandingPageDao.insertSelective(any(MgkLandingPagePo.class))).thenReturn(1);
        when(mgkPageFormMappingDao.insertBatch(any())).thenReturn(1);
        when(mgkLandingPageConfigDao.insertSelective(any())).thenReturn(1);
        when(extMgkLandingPageShowUrlDao.batchInsert(any())).thenReturn(1);
        when(extMgkLandingPageAvidDao.batchInsert(any())).thenReturn(1);
        MgkLandingPagePo pagePo = new MgkLandingPagePo();
        pagePo.setPageId(1L);
        pagePo.setStatus(1);
        pagePo.setAccountId(1000);
        pagePo.setFormId(1L);
        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(pagePo));
        MgkLandingPageConfigPo pageConfigPo = new MgkLandingPageConfigPo();
        pageConfigPo.setConfig("{}");
        when(mgkLandingPageConfigDao.selectByExampleWithBLOBs(any())).thenReturn(Lists.newArrayList(pageConfigPo));
        when(soaQueryAccountService.getAccountBaseDtoById(1000)).thenReturn(AccountBaseDto.builder().build());
        valueOperations = Mockito.mock(ValueOperations.class);
        when(mgkLandingPageDao.updateByExampleSelective(any(), any())).thenReturn(1);
    }

    @Test
    public void testGetAwakenWhiteListByAccountId() {
        Mockito.when(accAccountAwakenAppMappingDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        List<String> result = landingPageServiceDelegate.getAwakenWhiteListByAccountId(1);
        Assert.assertFalse(CollectionUtils.isEmpty(result));

        Mockito.when(accAccountAwakenAppMappingDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(AccAccountAwakenAppMappingPo.builder()
                        .appId(1)
                        .build()));
        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        result = landingPageServiceDelegate.getAwakenWhiteListByAccountId(1);
        Assert.assertFalse(CollectionUtils.isEmpty(result));

        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(ResAwakenAppWhitelistPo.builder()
                        .build()));
        result = landingPageServiceDelegate.getAwakenWhiteListByAccountId(1);
        assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetAllAwakenWhiteListGroupByAccountId() {
        Mockito.when(accAccountAwakenAppMappingDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        Map<Integer, List<String>> result = landingPageServiceDelegate.getAllAwakenWhiteListGroupByAccountId();
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        Mockito.when(accAccountAwakenAppMappingDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(AccAccountAwakenAppMappingPo.builder()
                        .accountId(1)
                        .appId(1)
                        .build()));
        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        result = landingPageServiceDelegate.getAllAwakenWhiteListGroupByAccountId();
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(ResAwakenAppWhitelistPo.builder()
                        .id(1)
                        .scheme("tbopen")
                        .build()));
        result = landingPageServiceDelegate.getAllAwakenWhiteListGroupByAccountId();
        assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetDownloadWhiteListByPageId() {
        Map<Long, List<AppPackageBean>> result = landingPageServiceDelegate.getDownloadWhiteListByPageId(Collections.emptyList());
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        Mockito.when(mgkLandingPageAppPackageDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        result = landingPageServiceDelegate.getDownloadWhiteListByPageId(Lists.newArrayList(1L));
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        MgkLandingPageAppPackagePo mgkLandingPageAppPackagePo = new MgkLandingPageAppPackagePo();
        mgkLandingPageAppPackagePo.setPageId(1L);
        mgkLandingPageAppPackagePo.setAppPackageId(1);
        Mockito.when(mgkLandingPageAppPackageDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(mgkLandingPageAppPackagePo));
        Mockito.when(resAppPackageDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        result = landingPageServiceDelegate.getDownloadWhiteListByPageId(Lists.newArrayList(1L));
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        Mockito.when(resAppPackageDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(ResAppPackagePo.builder()
                        .id(1)
                        .size(100)
                        .apkUpdateTime(new Timestamp(System.currentTimeMillis()))
                        .build()));
        result = landingPageServiceDelegate.getDownloadWhiteListByPageId(Lists.newArrayList(1L));
        assertFalse(CollectionUtils.isEmpty(result));
    }

//    @Test
//    public void testRefreshLandingPageInRedis() {
//        MgkLandingPagePo nativeLandingPagePo = new MgkLandingPagePo();
//        nativeLandingPagePo.setPageId(1L);
//        nativeLandingPagePo.setAccountId(1);
//        nativeLandingPagePo.setTemplateStyle(TemplateStyleEnum.FULL_SCREEN_VIDEO.getCode());
//        nativeLandingPagePo.setType(LandingPageTypeEnum.NATIVE.getCode());
//
//        MgkLandingPagePo customNativeLandingPagePo = new MgkLandingPagePo();
//        customNativeLandingPagePo.setPageId(2L);
//        customNativeLandingPagePo.setAccountId(1);
//        customNativeLandingPagePo.setTemplateStyle(TemplateStyleEnum.CUSTOM_NATIVE.getCode());
//        customNativeLandingPagePo.setType(LandingPageTypeEnum.CUSTOM_NATIVE.getCode());
//
//        Mockito.when(mgkLandingPageDao.selectByExample(Mockito.any()))
//                .thenReturn(Lists.newArrayList(nativeLandingPagePo, customNativeLandingPagePo));
//
//        MgkLandingPageConfigPo nativeConfigPo = new MgkLandingPageConfigPo();
//        nativeConfigPo.setPageId(1L);
//        nativeConfigPo.setConfig("[{}]");
//
//        MgkLandingPageConfigPo customNativeConfigPo = new MgkLandingPageConfigPo();
//        customNativeConfigPo.setPageId(2L);
//        customNativeConfigPo.setConfig("{}");
//        Mockito.when(mgkLandingPageConfigDao.selectByExampleWithBLOBs(Mockito.any()))
//                .thenReturn(Lists.newArrayList(nativeConfigPo, customNativeConfigPo));
//
//        MgkLandingPageShowUrlPo showUrlPo = new MgkLandingPageShowUrlPo();
//        showUrlPo.setPageId(1L);
//        showUrlPo.setShowUrl("www.bilibili.com");
//        Mockito.when(mgkLandingPageShowUrlDao.selectByExample(Mockito.any()))
//                .thenReturn(Lists.newArrayList(showUrlPo));
//
//        Mockito.when(accAccountAwakenAppMappingDao.selectByExample(Mockito.any()))
//                .thenReturn(Lists.newArrayList(AccAccountAwakenAppMappingPo.builder()
//                        .accountId(1)
//                        .appId(1)
//                        .build()));
//        Mockito.when(resAppPackageDao.selectByExample(Mockito.any()))
//                .thenReturn(Lists.newArrayList(ResAppPackagePo.builder()
//                        .id(1)
//                        .size(100)
//                        .build()));
//
//        MgkLandingPageAppPackagePo mgkLandingPageAppPackagePo = new MgkLandingPageAppPackagePo();
//        mgkLandingPageAppPackagePo.setPageId(1L);
//        mgkLandingPageAppPackagePo.setAppPackageId(1);
//        mgklan.refreshLandingPageInRedis();
//    }

    @Test
    public void testRefreshPageConfigToRedis() {
        MgkLandingPagePo nativeLandingPagePo = new MgkLandingPagePo();
        nativeLandingPagePo.setPageId(1L);
        nativeLandingPagePo.setAccountId(1);
        nativeLandingPagePo.setTemplateStyle(TemplateStyleEnum.FULL_SCREEN_VIDEO.getCode());
        nativeLandingPagePo.setType(LandingPageTypeEnum.NATIVE.getCode());

        MgkLandingPagePo customNativeLandingPagePo = new MgkLandingPagePo();
        customNativeLandingPagePo.setPageId(2L);
        customNativeLandingPagePo.setAccountId(1);
        customNativeLandingPagePo.setTemplateStyle(TemplateStyleEnum.CUSTOM_NATIVE.getCode());
        customNativeLandingPagePo.setType(LandingPageTypeEnum.CUSTOM_NATIVE.getCode());

        Mockito.when(mgkLandingPageDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(nativeLandingPagePo, customNativeLandingPagePo));

        MgkLandingPageConfigPo nativeConfigPo = new MgkLandingPageConfigPo();
        nativeConfigPo.setPageId(1L);
        nativeConfigPo.setConfig("[{}]");

        MgkLandingPageConfigPo customNativeConfigPo = new MgkLandingPageConfigPo();
        customNativeConfigPo.setPageId(2L);
        customNativeConfigPo.setConfig("{}");
        Mockito.when(mgkLandingPageConfigDao.selectByExampleWithBLOBs(Mockito.any()))
                .thenReturn(Lists.newArrayList(nativeConfigPo, customNativeConfigPo));

        MgkLandingPageShowUrlPo showUrlPo = new MgkLandingPageShowUrlPo();
        showUrlPo.setPageId(1L);
        showUrlPo.setShowUrl("www.bilibili.com");
        Mockito.when(mgkLandingPageShowUrlDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(showUrlPo));

        MgkLandingPageAvidPo avidPo = new MgkLandingPageAvidPo();
        avidPo.setAvId(1L);
        Mockito.when(mgkLandingPageAvidDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(avidPo));

        Mockito.when(accAccountAwakenAppMappingDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(AccAccountAwakenAppMappingPo.builder()
                        .appId(1)
                        .build()));
        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(ResAwakenAppWhitelistPo.builder()
                        .build()));

        MgkLandingPageAppPackagePo mgkLandingPageAppPackagePo = new MgkLandingPageAppPackagePo();
        mgkLandingPageAppPackagePo.setPageId(1L);
        mgkLandingPageAppPackagePo.setAppPackageId(1);
        Mockito.when(mgkLandingPageAppPackageDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(mgkLandingPageAppPackagePo));
        Mockito.when(resAppPackageDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(ResAppPackagePo.builder()
                        .id(1)
                        .size(100)
                        .apkUpdateTime(new Timestamp(System.currentTimeMillis()))
                        .build()));

        Mockito.when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);

        landingPageServiceDelegate.refreshPageConfigAndFormItemToRedis(2L);
    }

    @Test
    public void testCreate() {
        long pageId = landingPageServiceDelegate.create(super.operator, NewLandingPageDto.builder()
                .originPageId(0L)
                .accountId(1000)
                .name("")
                .title("")
                .templateStyle(100)
                .effectiveStartTime(Utils.getToday())
                .effectiveEndTime(Utils.getToday())
                .config("")
                .type(1)
                .avIds(Lists.newArrayList(1L))
                .showUrls(Lists.newArrayList(""))
                .appPackageId(1)
                .appPackageIds(Lists.newArrayList(1))
                .formIds(Lists.newArrayList(1L))
                .build());
        Assert.assertEquals(1L, pageId);
    }

    @Test
    public void testUpdate() {
        landingPageServiceDelegate.update(super.operator, UpdateLandingPageDto.builder()
                .pageId(1L)
                .name("name")
                .title("title")
                .effectiveStartTime(Utils.getToday())
                .effectiveEndTime(Utils.getToday())
                .config("{1}")
                .avIds(Lists.newArrayList(1L))
                .showUrls(Lists.newArrayList(""))
                .templateStyle(1)
                .appPackageId(1)
                .formIds(Lists.newArrayList(1L, 2L, 3L))
                .isModel(1)
                .modelId("1")
                .build(), true);

    }

    @Test
    public void testGetAssociateCustomNativePageCountByFormId() {
        when(mgkPageFormMappingDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkPageFormMappingPo.builder().pageId(1L).build()));
        when(mgkLandingPageDao.countByExample(any())).thenReturn(0L);
        long count = landingPageServiceDelegate.getAssociateCustomNativePageCountByFormId(1L);
        Assert.assertEquals(0L, count);

        when(mgkPageFormMappingDao.selectByExample(any())).thenReturn(Collections.emptyList());
        count = landingPageServiceDelegate.getAssociateCustomNativePageCountByFormId(1L);
        Assert.assertEquals(0L, count);
    }

    @Test
    public void testBatchDisable() {
        when(mgkLandingPageDao.updateByExampleSelective(any(), any())).thenReturn(1);
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        landingPageServiceDelegate.batchDisable(super.operator, Lists.newArrayList(1L));
        landingPageServiceDelegate.batchDisable(super.operator, Collections.emptyList());
    }

    @Test
    public void testGetTemplateStyleMapByPageId() {
        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .pageId(1L)
                .status(0)
                .formId(1L)
                .accountId(10005)
                .creator("")
                .templateStyle(1)
                .build()));
        Map<Long, Integer> templateStyleMapByPageId = landingPageServiceDelegate.getTemplateStyleMapByPageId(Lists.newArrayList(1L));
        assertFalse(CollectionUtils.isEmpty(templateStyleMapByPageId));
    }

    @Test
    public void testGetWeChatSign() {
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        ReflectionTestUtils.setField(landingPageServiceDelegate, "appId", "wx86afa182f7d421ed");
        ReflectionTestUtils.setField(landingPageServiceDelegate, "appSecret", "9d26c41d10022346c9ee53e33f2ee583");
        ReflectionTestUtils.setField(landingPageServiceDelegate, "accessTokenUrl", "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s");
        ReflectionTestUtils.setField(landingPageServiceDelegate, "jsapiTicketUrl", "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi");
        try {
            String weChatSign = landingPageServiceDelegate.getWeChatSign();
        } catch (Exception e) {

        }
    }

    @Test
    public void testGetModelUsedDtos() {
        List<Long> modelUsedDtos = landingPageServiceDelegate.getModelUsedDtos(operator);
        assertFalse(CollectionUtils.isEmpty(modelUsedDtos));
    }

    @Test
    public void testGetLandingPageDtoByPageIds() {
        List<MgkLandingPageDto> dtos = landingPageServiceDelegate.getLandingPageDtoByPageIds(Lists.newArrayList(1L));
        assertFalse(CollectionUtils.isEmpty(dtos));
    }


    @Test
    public void testPublish() {

        ReflectionTestUtils.setField(landingPageServiceDelegate, "mgkLandingPageHtmlUrl", "http://cm-mng.bilibili.co/art/brick/m/mgk/page/%s");
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        landingPageServiceDelegate.publish(393374691375722496L);
    }

    @Test
    public void testDownline() {
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .type(1)
                .title("title")
                .reason("reason")
                .pageVersion("0x0x0x")
                .name("name")
                .mtime(new Timestamp(System.currentTimeMillis()))
                .modelId(1L)
                .isModel(1)
                .isDeleted(1)
                .id(1)
                .effectiveStartTime(new Timestamp(System.currentTimeMillis()))
                .effectiveEndTime(new Timestamp(System.currentTimeMillis()))
                .ctime(new Timestamp(System.currentTimeMillis()))
                .pageId(1L)
                .status(2)
                .formId(1L)
                .accountId(1)
                .creator("creator")
                .templateStyle(1)
                .build()));
        landingPageServiceDelegate.downline(operator, 393139068358373376L);
    }

    @Test
    public void testGetLandingPageConfigForMobByPageId() {
        when(mgkLandingPageConfigDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPageConfigPo.builder()
                .config("")
                .ctime(new Timestamp(System.currentTimeMillis()))
                .id(1)
                .isDeleted(1)
                .mtime(new Timestamp(System.currentTimeMillis()))
                .pageId(393374691375722496L)
                .build()));
        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .templateStyle(1)
                .creator("")
                .accountId(1)
                .formId(1L)
                .status(1)
                .pageId(393374691375722496L)
                .ctime(new Timestamp(System.currentTimeMillis()))
                .effectiveEndTime(new Timestamp(System.currentTimeMillis()))
                .effectiveStartTime(new Timestamp(System.currentTimeMillis()))
                .id(1)
                .isDeleted(1)
                .isModel(1)
                .modelId(1L)
                .mtime(new Timestamp(System.currentTimeMillis()))
                .name("name")
                .pageVersion("0x0x0x")
                .reason("why")
                .title("title")
                .type(1)
                .build()));
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        landingPageServiceDelegate.getLandingPageConfigForMobByPageId(393374691375722496L);
    }

    @Test
    public void testGetLandingPageDtos() {
        landingPageServiceDelegate.getLandingPageDtos(QueryLandingPageParamDto.builder()
                .accountIdList(Lists.newArrayList(1))
                .modelIdList(Lists.newArrayList(1L))
                .pageIdList(Lists.newArrayList(1L))
                .formIdList(Lists.newArrayList(1L))
                .nameLike("name")
                .orderBy("desc")
                .statusList(Lists.newArrayList(1))
                .templateStyleList(Lists.newArrayList(1))
                .typeList(Lists.newArrayList(1))
                .withAvid(true)
                .creatorLike("creator")
                .effectiveEndTime(new Timestamp(System.currentTimeMillis()))
                .effectiveStartTime(new Timestamp(System.currentTimeMillis()))
                .formIdLtZero(true)
                .idList(Lists.newArrayList(1))
                .titleLike("title")
                .build());
    }

    @Test
    public void testTestGetLandingPageDtos() {
        when(mgkLandingPageDao.countByExample(any())).thenReturn(1L);

        landingPageServiceDelegate.getLandingPageDtos(QueryLandingPageParamDto.builder()
                .accountIdList(Lists.newArrayList(1))
                .modelIdList(Lists.newArrayList(1L))
                .pageIdList(Lists.newArrayList(1L))
                .formIdList(Lists.newArrayList(1L))
                .nameLike("name")
                .orderBy("desc")
                .statusList(Lists.newArrayList(1))
                .templateStyleList(Lists.newArrayList(1))
                .typeList(Lists.newArrayList(1))
                .withAvid(true)
                .creatorLike("creator")
                .effectiveEndTime(new Timestamp(System.currentTimeMillis()))
                .effectiveStartTime(new Timestamp(System.currentTimeMillis()))
                .formIdLtZero(true)
                .idList(Lists.newArrayList(1))
                .titleLike("title")
                .build(), Page.valueOf(1, 15));
    }

    @Test
    public void testGetAdVersionControlIdByPageId() {
        ReflectionTestUtils.setField(landingPageServiceDelegate, "mgkAdVersionControl", "0x010100,3;");
        Timestamp ctime = new Timestamp(System.currentTimeMillis());
        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .templateStyle(1)
                .creator("诡术妖姬")
                .accountId(10005)
                .formId(1L)
                .status(0)
                .pageId(1L)
                .ctime(ctime)
                .effectiveEndTime(ctime)
                .effectiveStartTime(ctime)
                .id(1)
                .isDeleted(0)
                .isModel(0)
                .modelId(1L)
                .mtime(ctime)
                .name("name")
                .pageVersion("0x010100")
                .reason("reason")
                .title("title")
                .type(1)
                .build()));
        Integer adVersionControlId = landingPageServiceDelegate.getAdVersionControlIdByPageId(1L);
        assertEquals(0, (int) adVersionControlId);

        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .templateStyle(201)
                .creator("诡术妖姬")
                .accountId(10005)
                .formId(1L)
                .status(0)
                .pageId(1L)
                .ctime(ctime)
                .effectiveEndTime(ctime)
                .effectiveStartTime(ctime)
                .id(1)
                .isDeleted(0)
                .isModel(0)
                .modelId(1L)
                .mtime(ctime)
                .name("name")
                .pageVersion("")
                .reason("reason")
                .title("title")
                .type(1)
                .build()));
        adVersionControlId = landingPageServiceDelegate.getAdVersionControlIdByPageId(1L);
        assertEquals(2, (int) adVersionControlId);

        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .templateStyle(203)
                .creator("诡术妖姬")
                .accountId(10005)
                .formId(1L)
                .status(0)
                .pageId(1L)
                .ctime(ctime)
                .effectiveEndTime(ctime)
                .effectiveStartTime(ctime)
                .id(1)
                .isDeleted(0)
                .isModel(0)
                .modelId(1L)
                .mtime(ctime)
                .name("name")
                .pageVersion("")
                .reason("reason")
                .title("title")
                .type(1)
                .build()));
        adVersionControlId = landingPageServiceDelegate.getAdVersionControlIdByPageId(1L);
        assertEquals(1, (int) adVersionControlId);

        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .templateStyle(1)
                .creator("诡术妖姬")
                .accountId(10005)
                .formId(1L)
                .status(0)
                .pageId(1L)
                .ctime(ctime)
                .effectiveEndTime(ctime)
                .effectiveStartTime(ctime)
                .id(1)
                .isDeleted(0)
                .isModel(0)
                .modelId(1L)
                .mtime(ctime)
                .name("name")
                .pageVersion("")
                .reason("reason")
                .title("title")
                .type(1)
                .build()));
        adVersionControlId = landingPageServiceDelegate.getAdVersionControlIdByPageId(1L);
        assertEquals(0, (int) adVersionControlId);
    }

    @Test
    public void testGetNativeLaunchUrlByPageId() {
        Timestamp ctime = new Timestamp(System.currentTimeMillis());
        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .templateStyle(1)
                .creator("诡术妖姬")
                .accountId(10005)
                .formId(1L)
                .status(0)
                .pageId(1L)
                .ctime(ctime)
                .effectiveEndTime(ctime)
                .effectiveStartTime(ctime)
                .id(1)
                .isDeleted(0)
                .isModel(0)
                .modelId(1L)
                .mtime(ctime)
                .name("name")
                .pageVersion("")
                .reason("reason")
                .title("title")
                .type(1)
                .build()));
        String nativeLaunchUrl = landingPageServiceDelegate.getNativeLaunchUrlByPageId(1L);
        assertTrue(Strings.isNullOrEmpty(nativeLaunchUrl));

        when(mgkLandingPageDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkLandingPagePo.builder()
                .templateStyle(301)
                .creator("诡术妖姬")
                .accountId(10005)
                .formId(1L)
                .status(0)
                .pageId(1L)
                .ctime(ctime)
                .effectiveEndTime(ctime)
                .effectiveStartTime(ctime)
                .id(1)
                .isDeleted(0)
                .isModel(0)
                .modelId(1L)
                .mtime(ctime)
                .name("name")
                .pageVersion("")
                .reason("reason")
                .title("title")
                .type(1)
                .build()));
        nativeLaunchUrl = landingPageServiceDelegate.getNativeLaunchUrlByPageId(1L);
        assertFalse(Strings.isNullOrEmpty(nativeLaunchUrl));
    }

    @Test
    public void testGetAppInfo() {

        Timestamp ctime = new Timestamp(System.currentTimeMillis());
        when(resAppPackageDao.selectByExample(any())).thenReturn(Lists.newArrayList(ResAppPackagePo.builder()
                .id(1)
                .size(1)
                .accountId(1)
                .appName("appName")
                .authCodeList("65,69,12,14,80,82,20,22,88,94,97,101,37,39,47,48,114,51,115,116,118")
                .authorityUrl("http")
                .developerName("无锡白兔网络科技有限公司")
                .ctime(ctime)
                .iconUrl("http")
                .internalUrl("http")
                .isDeleted(0)
                .md5("")
                .mtime(ctime)
                .name("name")
                .packageName("com.qingshu520.chat")
                .platform(2)
                .platformStatus(1)
                .status(0)
                .url("http")
                .version("1")
                .build()));
        List<AppInfoDto> appInfo = landingPageServiceDelegate.getAppInfo(Lists.newArrayList(1));
        System.out.println(appInfo.get(0));
        assertFalse(CollectionUtils.isEmpty(appInfo));
    }

    @Test
    public void testInit() {
        when(resAppPackageApkAuthDao.selectByExample(any())).thenReturn(Lists.newArrayList(ResAppPackageApkAuthPo.builder()
                .id(1)
                .authCode("auth")
                .authName("authname")
                .build()));
        landingPageServiceDelegate.init();

        when(resAppPackageApkAuthDao.selectByExample(any())).thenReturn(Collections.emptyList());
        landingPageServiceDelegate.init();
    }

    @Test
    public void testTransPage() throws ServiceException {
        Object s = landingPageServiceDelegate.transPage(1000, "493137750280732672", 0);
        assertNotNull(s);
    }
}
