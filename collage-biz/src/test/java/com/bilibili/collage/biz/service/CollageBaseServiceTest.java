package com.bilibili.collage.biz.service;

import com.bilibili.collage.biz.BaseMockitoTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.redisson.RedissonLock;
import org.redisson.api.RedissonClient;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/09/16
 **/
public class CollageBaseServiceTest extends BaseMockitoTest {

    @InjectMocks
    private CollageBaseService collageBaseService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RedissonLock redissonLock;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testGetLock() throws InterruptedException {
        when(redissonClient.getLock(any())).thenReturn(redissonLock);
        collageBaseService.getLock(1, "1");
    }
}