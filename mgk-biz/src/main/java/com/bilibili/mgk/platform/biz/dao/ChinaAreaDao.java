package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.ChinaAreaPo;
import com.bilibili.mgk.platform.biz.po.ChinaAreaPoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChinaAreaDao {
    long countByExample(ChinaAreaPoExample example);

    int deleteByExample(ChinaAreaPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(ChinaAreaPo record);

    int insertBatch(List<ChinaAreaPo> records);

    int insertUpdateBatch(List<ChinaAreaPo> records);

    int insert(ChinaAreaPo record);

    int insertUpdateSelective(ChinaAreaPo record);

    int insertSelective(ChinaAreaPo record);

    List<ChinaAreaPo> selectByExample(ChinaAreaPoExample example);

    ChinaAreaPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ChinaAreaPo record, @Param("example") ChinaAreaPoExample example);

    int updateByExample(@Param("record") ChinaAreaPo record, @Param("example") ChinaAreaPoExample example);

    int updateByPrimaryKeySelective(ChinaAreaPo record);

    int updateByPrimaryKey(ChinaAreaPo record);
}