package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/09/09
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CollageMediaVo {

    @ApiModelProperty(value = "自增id")
    private Integer id;

    @ApiModelProperty(value = "媒体Id")
    private String media_id;

    @ApiModelProperty(value = "媒体名称")
    private String media_name;

    @ApiModelProperty(value = "媒体地址")
    private String media_url;

    @ApiModelProperty(value = "媒体类型 1-图片类型 2-gif类型")
    private Integer media_type;

    @ApiModelProperty(value = "媒体来源")
    private Integer media_origin;

    @ApiModelProperty(value = "媒体比例")
    private Integer media_ratio;

    @ApiModelProperty(value = "宽度")
    private Integer width;

    @ApiModelProperty(value = "高度")
    private Integer height;

    @ApiModelProperty(value = "模板Id")
    private Integer pattern_id;

    @ApiModelProperty(value = "作品Id")
    private Integer works_id;

    @ApiModelProperty(value = "创建时间")
    private Timestamp ctime;

    @ApiModelProperty(value = "修改时间")
    private Timestamp mtime;

    @ApiModelProperty(value = "媒体Md5")
    private String media_md5;

    @ApiModelProperty(value = "媒体大小")
    private Long media_size;

    @ApiModelProperty(value = "总时长")
    private Integer total_duration;

    @ApiModelProperty(value = "每帧时长")
    private Integer duration_per_frame;

    @ApiModelProperty(value = "帧数")
    private Integer frames;

    @ApiModelProperty(value = "轮播次数")
    private Integer rounds_number;

    @ApiModelProperty(value = "封面")
    private CollageCoverVo coverVo;


    @ApiModelProperty(value = "素材Id")
    private String material_id;


    /**
     * 数据来源类型: 1-本账号上传 2-其他账号推送
     */
    @ApiModelProperty(value = "数据来源类型: 1-本账号上传 2-其他账号推送")
    private Integer data_source_type;


}