package com.bilibili.mgk.platform.biz.databus.pub;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.databus.base.Message;
import com.bilibili.databus.base.SendResult;
import com.bilibili.databus.core.DataBusClient;
import com.bilibili.mgk.platform.api.landing_page.dto.BusinessToolStatusChangeDto;
import com.bilibili.mgk.platform.api.personal_mgk.dto.AutoCreatePersonalFlyDto;
import com.bilibili.mgk.platform.biz.dao.MgkAutoCreateAccInfoDao;
import com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPo;
import com.bilibili.mgk.platform.biz.po.MgkAutoCreateAccInfoPoExample;
import com.bilibili.mgk.platform.biz.service.MgkBaseService;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.enums.databus.MsgStatusEnum;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import com.bilibili.mgk.platform.common.utils.RandomUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PubBusinessToolDisable {

    private final DataBusClient businessToolDelPubClient;
    private final MgkBaseService baseService;

    public void businessToolDisablePub(BusinessToolStatusChangeDto changeDto) {
        RLock lock = baseService.getLock(changeDto.getBusinessToolType() + "_" + changeDto.getId(),
                MgkConstants.BUSINESS_TOOL_STATUS_CHANGE_LOCK_SUFFIX);
        try {
            Message msg = new Message(String.valueOf(RandomUtils.getHundredBoundRandom()),
                    JSON.toJSONBytes(changeDto),
                    Collections.emptyMap());

            log.info("businessToolDisablePub pub msg:{}", changeDto);
            SendResult sendResult = businessToolDelPubClient.pub(msg);
            if (!sendResult.isSuccess()) {
                log.error("businessToolDisablePub pub err [{}]", ExceptionUtils.getSubStringMsg(sendResult.getException()));
            }
        } catch (Exception e) {
            log.error("businessToolDisablePub error" + ExceptionUtils.getSubStringMsg(e));
        } finally {
            lock.unlock();
        }
    }
}
