package com.bilibili.mgk.platform.portal.excel;

import lombok.EqualsAndHashCode;

/**
 * @ClassName ExcelHeader
 * <AUTHOR>
 * @Date 2022/6/19 4:33 下午
 * @Version 1.0
 **/
@EqualsAndHashCode
public class ExcelHeader implements Comparable<ExcelHeader> {
    /**
     * excel的标题名称
     */
    private String title;
    /**
     * 每一个标题的顺序
     */
    private int order;

    /**
     * 当前列为空时是否跳过
     */
    private boolean skipIfEmpty;

    /**
     * 说对应方法名称
     */
    private String methodName;


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public boolean isSkipIfEmpty() {
        return skipIfEmpty;
    }

    public void setSkipIfEmpty(boolean skipIfEmpty) {
        this.skipIfEmpty = skipIfEmpty;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    @Override
    public int compareTo(ExcelHeader o) {
        return order == o.order ? 0 : (order > o.order ? 1 : -1);
    }

    public ExcelHeader(String title, int order, boolean skipIfEmpty, String methodName) {
        super();
        this.title = title;
        this.order = order;
        this.skipIfEmpty = skipIfEmpty;
        this.methodName = methodName;
    }

    @Override
    public String toString() {
        return "ExcelHeader [title=" + title + ", order=" + order
                + ", methodName=" + methodName + "]";
    }

}
