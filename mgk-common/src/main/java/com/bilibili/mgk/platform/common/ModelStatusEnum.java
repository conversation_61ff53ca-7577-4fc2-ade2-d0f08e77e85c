package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/08
 **/
@AllArgsConstructor
public enum ModelStatusEnum {
    UNPUBLISHED(1, "未发布") {
        @Override
        public boolean isModifiable() {
            return true;
        }

        @Override
        public boolean validateToStatus(ModelStatusEnum toStatus) {
            return EnumSet.of(PUBLISHED, DELETED).contains(toStatus);
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return null;
        }
    },
    PUBLISHED(2, "已发布") {
        @Override
        public boolean isModifiable() {
            return false;
        }

        @Override
        public boolean validateToStatus(ModelStatusEnum toStatus) {
            return EnumSet.of(DOWNLINE, DELETED, ADMIN_REJECT).contains(toStatus);
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.MODEL_PUBLISHED;
        }
    },
    DOWNLINE(3, "已下线") {
        @Override
        public boolean isModifiable() {
            return true;
        }

        @Override
        public boolean validateToStatus(ModelStatusEnum toStatus) {
            return EnumSet.of(DELETED, PUBLISHED).contains(toStatus);
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.MODEL_DOWNLINE;
        }
    },
    ADMIN_REJECT(4, "管理员驳回") {
        @Override
        public boolean isModifiable() {
            return false;
        }

        @Override
        public boolean validateToStatus(ModelStatusEnum toStatus) {
            return false;
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.MODEL_REJECT;
        }
    },
    DELETED(5, "已删除") {
        @Override
        public boolean isModifiable() {
            return false;
        }

        @Override
        public boolean validateToStatus(ModelStatusEnum toStatus) {
            return false;
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.MODEL_DELETED;
        }
    };

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static ModelStatusEnum getByCode(int code) {
        for (ModelStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code mgkModelStatusEnum " + code);
    }

    public final static List<Integer> CAN_BE_PREVIEW_STATUS_LIST = Arrays.asList(UNPUBLISHED.getCode(), PUBLISHED.getCode());

    public final static List<Integer> NOT_DELETED_STATUS_LIST = Arrays.asList(UNPUBLISHED.getCode(), PUBLISHED.getCode(), DOWNLINE.getCode(), ADMIN_REJECT.getCode());

    public abstract boolean isModifiable();

    public abstract boolean validateToStatus(ModelStatusEnum toStatus);

    public abstract LogOperateTypeEnum getLogOperateType();
}
