package com.bilibili.mgk.platform.portal.webapi.archive.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/9
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UposVideoVo {
    @ApiModelProperty(value = "视频业务ID")
    private Integer biz_id;
    @ApiModelProperty(value = "视频业务ID")
    private String upos_url;
    @ApiModelProperty(value = "视频文件名")
    private String file_name;
    @ApiModelProperty(value = "转码后视频URL")
    private String xcode_upos_url;
    @ApiModelProperty(value = "转码后视频MD5")
    private String xcode_md5;
    @ApiModelProperty(value = "转码后视频宽度")
    private Integer xcode_width;
    @ApiModelProperty(value = "转码后视频高度")
    private Integer xcode_height;
    @ApiModelProperty(value = "视频状态")
    private Integer status;
    @ApiModelProperty(value = "视频状态描述")
    private String status_desc;
    @ApiModelProperty(value = "转码后的视频大小(单位：M)")
    private BigDecimal xcode_size;
    @ApiModelProperty(value = "转码后的视频时长(单位：秒)")
    private BigDecimal xcode_duration;
    @ApiModelProperty(value = "视频封面图片")
    private String video_cover_url;
    @ApiModelProperty(value = "视频封面图片url列表")
    private List<String> video_cover_urls;
}