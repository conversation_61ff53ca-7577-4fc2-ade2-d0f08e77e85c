package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.launch.api.soa.ISoaCreativeService;
import com.bilibili.adp.passport.api.service.IConvReturnService;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.mgk.platform.api.data.dto.*;
import com.bilibili.mgk.platform.api.form.dto.MgkFormItemDto;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.biz.dao.MgkFormDataDao;
import com.bilibili.mgk.platform.biz.dao.MgkFormDataDuplicateRecordDao;
import com.bilibili.mgk.platform.biz.dao.MgkFormDataExtraDao;
import com.bilibili.mgk.platform.biz.dao.MgkMidPhoneDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkFormDataDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkFormDataExtraDao;
import com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPo;
import com.bilibili.mgk.platform.biz.po.MgkFormDataPo;
import com.bilibili.mgk.platform.biz.po.MgkMidPhonePo;
import com.bilibili.mgk.platform.biz.service.data.impl.MgkDataServiceImpl;
import com.bilibili.mgk.platform.common.FormItemTypeEnum;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Timestamp;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年09月20日
 */
public class MgkDataServiceImplTest extends MockitoDefaultTest {
    @Mock
    private MgkFormDataDao formDataDao;
    @Mock
    private ExtMgkFormDataDao extMgkFormDataDao;
    @Mock
    private MgkFormDataExtraDao formDataExtraDao;
    @Mock
    private ExtMgkFormDataExtraDao extMgkFormDataExtraDao;
    @Mock
    private IMgkLandingPageService landingPageService;
    @Mock
    private IMgkFormService mgkFormService;
    @Mock
    private MgkFormDataDuplicateRecordDao formDataDuplicateRecordDao;
    @Mock
    private RedisTemplate<String, String> stringRedisTemplate;
    @Mock
    private IConvReturnService convReturnService;
    @Mock
    private ISoaCreativeService soaCreativeService;
    @Mock
    private MgkMidPhoneDao mgkMidPhoneDao;

    private ThreadPoolTaskExecutor taskExecutor;

    @InjectMocks
    MgkDataServiceImpl mgkDataService;

    @Before
    public void before() {
        if (taskExecutor == null) {
            taskExecutor = new ThreadPoolTaskExecutor();
            taskExecutor.setCorePoolSize(1);
            taskExecutor.initialize();
            ReflectionTestUtils.setField(mgkDataService, "taskExecutor", taskExecutor);
        }
    }

    @Test
    public void insertFormData() {
        when(formDataDao.insertSelective(any()))
                .then(arg -> BeanTestUtils.initSimpleFields(arg.getArgumentAt(0, MgkFormDataPo.class)).getId());
        mgkDataService.insertFormData(BeanTestUtils.initSimpleFields(ReportDataDto.builder()
                .salesType(SalesType.CPM.getCode())
                .build()));
    }

    @Test
    public void getReportDataByPage() {
        PageResult<ReportDataDto> data = mgkDataService.getReportDataByPage(BeanTestUtils
                .initSimpleFields(QueryReportDataParamDto.builder()
                        .salesType(SalesType.CPM.getCode())
                        .build()));
        Assert.assertNotNull(data);
        Assert.assertNotNull(data.getRecords());
        Assert.assertFalse(data.getRecords().isEmpty());
    }

    @Test
    public void validateTelephone() {
        mgkDataService.validateTelephone(BeanTestUtils.initSimpleFields(ReportDataDto.builder().build()),
                BeanTestUtils.initSimpleFields(FormItemDataDto.builder()
                        .build()),
                BeanTestUtils.initSimpleFields(MgkFormItemDto.builder()
                        .type(FormItemTypeEnum.TELEPHONE.getId())
                        .isSubmitValidate(WhetherEnum.NO.getCode())
                        .isUnique(WhetherEnum.NO.getCode())
                        .build()));
        ;
    }

    @Test
    public void validateIdCard() {
        mgkDataService.validateIdCard(BeanTestUtils.initSimpleFields(FormItemDataDto.builder()
                        .build()),
                BeanTestUtils.initSimpleFields(MgkFormItemDto.builder()
                        .type(FormItemTypeEnum.TELEPHONE.getId())
                        .isSubmitValidate(WhetherEnum.NO.getCode())
                        .isUnique(WhetherEnum.NO.getCode())
                        .build()));
        mgkDataService.convertFormDataExtraPosToFormDataId2ItemDataDtoMap(Lists.newArrayList(BeanTestUtils
                .initSimpleFields(MgkFormDataExtraPo.builder().build())));
        ;
    }

    @Test
    public void validateAndConvertAddress() {
        try {
            mgkDataService.validateAndConvertAddress(BeanTestUtils.initSimpleFields(FormItemDataDto.builder()
                            .value(GsonUtils.toJson(BeanTestUtils.initSimpleFields(AddressDataDto.builder().build())))
                            .build()),
                    BeanTestUtils.initSimpleFields(MgkFormItemDto.builder()
                            .type(FormItemTypeEnum.ADDRESS.getId())
                            .isSubmitValidate(WhetherEnum.NO.getCode())
                            .isUnique(WhetherEnum.NO.getCode())
                            .build()));
        } catch (IllegalArgumentException e) {

        }

    }

    @Test
    public void testGetPhone() {
        MgkPhoneDto phoneDto;
        when(mgkMidPhoneDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkMidPhonePo.builder()
                .mid(1L)
                .prefix("+86")
                .phoneNum("18888888888")
                .buvid("123")
                .build()));
        phoneDto = MgkPhoneDto.builder()
                .mid(1L)
                .buvid("123")
                .build();
//        Assert.assertNotNull(mgkDataService.getPhone(phoneDto));

        when(mgkMidPhoneDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkMidPhonePo.builder()
                .mid(12345678L)
                .prefix("+86")
                .phoneNum("18888888888")
                .buvid("123")
                .build()));
        phoneDto = MgkPhoneDto.builder()
                .mid(12345678L)
                .buvid("123")
                .build();
//        Assert.assertTrue(mgkDataService.getPhone(phoneDto).getMid() == 12345678L);

        when(mgkMidPhoneDao.selectByExample(any())).thenReturn(Lists.newArrayList(MgkMidPhonePo.builder()
                .buvid("ANDROID")
                .prefix("+86")
                .phoneNum("18888888888")
                .build()));
        phoneDto = MgkPhoneDto.builder()
                .mid(1L)
                .buvid("ANDROID")
                .build();
//        Assert.assertEquals(mgkDataService.getPhone(phoneDto).getBuvid(), "ANDROID");
    }

    @Test
    public void testInsertMidPhone() {
        MgkPhoneDto dto = MgkPhoneDto.builder()
                .mid(4L)
                .buvid("")
                .deviceId("")
                .imei("")
                .prefix("+86")
                .phoneNum("123")
                .mtime(new Timestamp(System.currentTimeMillis()))
                .build();
//        mgkDataService.insertMidPhone(dto);
    }

    @Test
    public void testUpdateMidPhone() {
//        mgkDataService.updateMidPhone(MgkPhoneDto.builder()
//                .mtime(new Timestamp(System.currentTimeMillis()))
//                .phoneNum("123")
//                .prefix("+86")
//                .imei("")
//                .deviceId("")
//                .buvid("")
//                .mid(1L)
//                .build());
    }

    @Test
    public void getFormData() {
        mgkDataService.getFormData(1L);
    }

    @Test
    public void batchInsertFormData() {
        mgkDataService.batchInsertFormData(BeanTestUtils.initSimpleFields(NewReportDataDto.builder()
                .reportDataDtoList(Lists.newArrayList(BeanTestUtils
                        .initSimpleFields(ReportDataDto.builder().salesType(31).build()))).build()));
    }


}