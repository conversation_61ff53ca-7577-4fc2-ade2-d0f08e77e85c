package com.bilibili.collage.biz.service;

import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.redisson.RedissonLock;
import org.redisson.api.RedissonClient;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/09/17
 **/
public class CollageWorksServiceImplTest extends BaseMockitoTest {

    @InjectMocks
    private CollageWorksServiceImpl collageWorksService;

    @Mock
    private CollageWorksServiceDelegate collageWorksServiceDelegate;

    @Mock
    private CollageBaseService collageBaseService;

    @Mock
    private RedissonLock redissonLock;

    @Mock
    private CollageValidService collageValidService;

    @Mock
    private RedissonClient redissonClient;

    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void testCreate() {
        collageWorksService.create(operator, NewCollageWorksDto.builder().build());
    }

    @Test
    public void testQuery() {
        collageWorksService.query(QueryCollageWorksDto.builder().build());
    }

    @Test
    public void testQueryWithLayerById() {
        collageWorksService.queryWithLayerById(1);
    }

    @Test
    public void testEditWorksLayer() {
        when(collageBaseService.getLock(any(), any())).thenReturn(redissonLock);
        collageWorksService.editWorksLayer(operator, CollageEditWorksLayerDto.builder().build());
    }

    @Test
    public void testEditWorksName() {
        when(collageBaseService.getLock(any(), any())).thenReturn(redissonLock);
        collageWorksService.editWorksName(operator, 1, "name");
    }

    @Test
    public void testSynchroWorks() {
        collageWorksService.synchroWorks(operator, 1);
    }

    @Test
    public void testDelete() {
        when(collageBaseService.getLock(any(), any())).thenReturn(redissonLock);
        collageWorksService.delete(operator, Lists.newArrayList(1));
    }

    @Test
    public void testCreateNoLayers() {
        collageWorksService.createNoLayers(operator, NewCollageWorksDto.builder()
                .collageSizeId(1)
                .name("name")
                .patternId(1)
                .worksOrigin(1)
                .worksUrl("http://www.bilibili.com")
                .worksMd5("md5")
                .worksSize(1L)
                .cover(CollageCoverDto.builder()
                        .coverId(1L)
                        .objId(1)
                        .objType(1)
                        .id(1)
                        .width(1)
                        .height(1)
                        .ratio(1)
                        .coverUrl("http://www.bilibili.com")
                        .coverSize(1)
                        .coverMd5("md5")
                        .build())
                .totalDuration(1)
                .durationPerFrame(1)
                .frames(1)
                .roundsNumber(1)
                .build());
    }

    @Test
    public void testEditWorks() {
        when(collageBaseService.getLock(any(),any())).thenReturn(redissonLock);
        collageWorksService.editWorks(operator, CollageEditWorksNoLayersDto.builder()
                .cover(CollageCoverDto.builder()
                        .coverMd5("md5")
                        .coverSize(1)
                        .coverUrl("http://www.bilibili.com")
                        .ratio(1)
                        .height(1)
                        .width(1)
                        .id(1)
                        .objType(1)
                        .objId(1)
                        .coverId(1L)
                        .build())
                .durationPerFrame(1)
                .frames(1)
                .id(1)
                .renderImage("http://www.bilibili.com")
                .roundsNumber(1)
                .totalDuration(1)
                .worksMd5("md5")
                .worksSize(1L)
                .build());
    }

    @Test
    public void testQueryNoLayersById() {
        collageWorksService.queryNoLayersById(1);
    }
}