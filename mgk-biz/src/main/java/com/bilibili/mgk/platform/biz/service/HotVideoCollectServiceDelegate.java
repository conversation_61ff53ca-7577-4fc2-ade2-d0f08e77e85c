package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDto;
import com.bilibili.mgk.platform.api.hot_ads.service.IHotAdsService;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoCollectDto;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoDto;
import com.bilibili.mgk.platform.api.hot_video.dto.QueryHotVideoCollectDto;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoService;
import com.bilibili.mgk.platform.biz.dao.MgkHotVideoCollectDao;
import com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPo;
import com.bilibili.mgk.platform.biz.po.MgkHotVideoCollectPoExample;
import com.bilibili.mgk.platform.common.MgkHotVideoDataTypeEnum;
import com.bilibili.mgk.platform.common.MgkHotVideoIsCollectEnum;
import com.bilibili.mgk.platform.common.utils.ExampleUtils;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.mysema.commons.lang.Pair;
import edu.emory.mathcs.backport.java.util.Collections;
import io.vavr.Tuple2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @file: HotVideoCollectServiceDelegate
 * @author: gaoming
 * @date: 2020/11/12
 * @version: 1.0
 * @description:
 **/
@Service
public class HotVideoCollectServiceDelegate {
    @Autowired
    private MgkHotVideoCollectDao hotVideoCollectDao;

    @Autowired
    private IHotVideoService hotVideoService;

    @Autowired
    private IHotAdsService hotAdsService;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    public List<HotVideoCollectDto> getCollects(QueryHotVideoCollectDto queryDto) {
        MgkHotVideoCollectPoExample example = this.getHotVideoExample(queryDto);
        List<MgkHotVideoCollectPo> pos = hotVideoCollectDao.selectByExample(example);
        return convertPos2Dtos(pos);
    }

    private List<HotVideoCollectDto> convertPos2Dtos(List<MgkHotVideoCollectPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertPo2Dto).collect(Collectors.toList());
    }

    private HotVideoCollectDto convertPo2Dto(MgkHotVideoCollectPo po) {
        HotVideoCollectDto dto = HotVideoCollectDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private MgkHotVideoCollectPoExample getHotVideoExample(QueryHotVideoCollectDto queryDto) {
        MgkHotVideoCollectPoExample example = new MgkHotVideoCollectPoExample();

        if (CollectionUtils.isEmpty(queryDto.getTitles())) {
            MgkHotVideoCollectPoExample.Criteria criteria = example.or();
            ObjectUtils.setList(queryDto::getAccountIds, criteria::andAccountIdIn);
            ObjectUtils.setList(queryDto::getCollectTypes, criteria::andCollectTypeIn);
            ObjectUtils.setList(queryDto::getBvids, criteria::andBvidIn);
            ObjectUtils.setList(queryDto::getAdTypeCreativeIds, criteria::andAdTypeCreativeIdIn);
            ObjectUtils.setList(queryDto::getCollectIds, criteria::andCollectIdIn);
            ObjectUtils.setObject(queryDto::getIsDeleted, criteria::andIsDeletedEqualTo);
            ObjectUtils.setList(queryDto::getBlackList, criteria::andBvidNotIn);
            ObjectUtils.setList(queryDto::getAdTypeCreativeIdBlacks, criteria::andAdTypeCreativeIdNotIn);
            ExampleUtils.notNull(queryDto.getBeginDate(), criteria::andLogDateGreaterThanOrEqualTo);

        } else {
            for (String title : queryDto.getTitles()) {
                MgkHotVideoCollectPoExample.Criteria criteria = example.or();
                ObjectUtils.setList(queryDto::getAccountIds, criteria::andAccountIdIn);
                ObjectUtils.setList(queryDto::getCollectTypes, criteria::andCollectTypeIn);
                ObjectUtils.setList(queryDto::getBvids, criteria::andBvidIn);
                ObjectUtils.setList(queryDto::getAdTypeCreativeIds, criteria::andAdTypeCreativeIdIn);
                ObjectUtils.setList(queryDto::getCollectIds, criteria::andCollectIdIn);
                ObjectUtils.setObject(queryDto::getIsDeleted, criteria::andIsDeletedEqualTo);
                ObjectUtils.setList(queryDto::getBlackList, criteria::andBvidNotIn);
                ObjectUtils.setList(queryDto::getAdTypeCreativeIdBlacks, criteria::andAdTypeCreativeIdNotIn);
                criteria.andTitleLike("%" + title + "%");
                ExampleUtils.notNull(queryDto.getBeginDate(), criteria::andLogDateGreaterThanOrEqualTo);
            }
        }

        example.setOrderByClause("mtime desc");

        if (queryDto.getPage() != null) {
            example.setOffset(queryDto.getPage().getOffset());
            example.setLimit(queryDto.getPage().getLimit());
        }
        return example;
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void updateIsDeleted(Operator operator, String bvid, Integer collectType, IsDeleted valid) {
        MgkHotVideoCollectPoExample example = new MgkHotVideoCollectPoExample();
        example.or().andBvidEqualTo(bvid).andAccountIdEqualTo(operator.getOperatorId()).andCollectTypeEqualTo(collectType);
        MgkHotVideoCollectPo po = MgkHotVideoCollectPo.builder()
                .isDeleted(valid.getCode())
                .build();
        hotVideoCollectDao.updateByExampleSelective(po, example);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void insert(HotVideoCollectDto dto) {
        MgkHotVideoCollectPo po = MgkHotVideoCollectPo.builder()
                .isDeleted(dto.getIsDeleted())
                .accountId(dto.getAccountId())
                .bvid(dto.getBvid())
                .adTypeCreativeId(dto.getAdTypeCreativeId())
                .title(dto.getTitle())
                .collectId(snowflakeIdWorker.nextId())
                .collectType(dto.getCollectType())
                .logDate(dto.getLogDate())
                .build();

        // title可能存在非法字符，导致insert失败
        int res = 0;
        try {
            res = hotVideoCollectDao.insertSelective(po);
        } catch (Exception e) {
            po.setTitle("");
            res = hotVideoCollectDao.insertSelective(po);
        }
        Assert.isTrue(Utils.isPositive(res), "插入失败");
    }

    public PageResult<HotVideoDto> getCollectList(QueryHotVideoCollectDto queryDto) {
        // 获取page信息
        Page page = queryDto.getPage();
        queryDto.setPage(null);
        MgkHotVideoCollectPoExample example = this.getHotVideoExample(queryDto);

        List<MgkHotVideoCollectPo> pos = hotVideoCollectDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        List<String> bvids = pos.stream().map(MgkHotVideoCollectPo::getBvid).collect(Collectors.toList());
        // 获取收藏列表的视频详情
        List<HotVideoDto> collects = getHotVideoDtosByBvids(bvids);

        List<HotVideoDto> result = collects.stream().skip(page.getPageSize() * (page.getPage() - 1)).limit(page.getPageSize()).collect(Collectors.toList());

        return PageResult.<HotVideoDto>builder()
                .total(collects.size())
                .records(result)
                .build();
    }

    private List<HotVideoDto> getHotVideoDtosByBvids(List<String> bvids) {
        if (CollectionUtils.isEmpty(bvids)) {
            return Collections.emptyList();
        }
        List<HotVideoDto> hotVideoDtos = hotVideoService.getHotVideoDtoByBvids(bvids, MgkHotVideoDataTypeEnum.LAST_MOUTH.getCode());
        hotVideoDtos.forEach(hotVideoDto -> hotVideoDto.setIsCollect(MgkHotVideoIsCollectEnum.COLLECT_ENUM.getCode()));
        return hotVideoDtos.stream().filter(hotVideoDto -> !Strings.isNullOrEmpty(hotVideoDto.getTitle())).collect(Collectors.toList());
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void updateCreativeStatus(Operator operator, Integer id, int code, String logDate, Integer dayType) {
        MgkHotVideoCollectPo po = MgkHotVideoCollectPo.builder()
                .id(id)
                .isDeleted(code)
                .logDate(logDate)
                .dayType(dayType)
                .build();
        hotVideoCollectDao.updateByPrimaryKeySelective(po);
    }

    public PageResult<HotAdsDto> getCollectAdsList(QueryHotVideoCollectDto queryDto) {
        Page page = queryDto.getPage();
        queryDto.setPage(null);
        MgkHotVideoCollectPoExample example = this.getHotVideoExample(queryDto);

        List<MgkHotVideoCollectPo> pos = hotVideoCollectDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        List<String> adTypeCreativeIds = pos.stream().map(MgkHotVideoCollectPo::getAdTypeCreativeId).distinct().collect(Collectors.toList());
        List<HotAdsDto> hotAdsDtos = hotAdsService.getHotAdsDtosByAdTypeCreativeIds(adTypeCreativeIds);
        hotAdsDtos = hotAdsDtos.stream().sorted(Comparator.comparing(HotAdsDto::getLogDate).reversed())
                .filter(distinctByKey(HotAdsDto::getAdTypeCreativeId)).collect(Collectors.toList());
        Map<String, HotAdsDto> hotAdsDtoMap = hotAdsDtos.stream().collect(Collectors.toMap(HotAdsDto::getAdTypeCreativeId, Function.identity()));

        hotAdsDtos = adTypeCreativeIds.stream().map(creativeId -> hotAdsDtoMap.getOrDefault(creativeId, HotAdsDto.builder().adTypeCreativeId("").build())).collect(Collectors.toList());
        hotAdsDtos = hotAdsDtos.stream().filter(dto -> !Strings.isNullOrEmpty(dto.getAdTypeCreativeId())).collect(Collectors.toList());
        hotAdsDtos.forEach(dto -> dto.setIsCollect(MgkHotVideoIsCollectEnum.COLLECT_ENUM.getCode()));

        List<HotAdsDto> result = hotAdsDtos.stream().skip(page.getPageSize() * (page.getPage() - 1)).limit(page.getPageSize()).collect(Collectors.toList());

        return PageResult.<HotAdsDto>builder()
                .total(hotAdsDtos.size())
                .records(result)
                .build();
    }

    public PageResult<Pair<String, String>> getCollectAdsByPage(QueryHotVideoCollectDto queryDto){
        MgkHotVideoCollectPoExample example = this.getHotVideoExample(queryDto);

        long count = hotVideoCollectDao.countByExample(example);
        if(count == 0){
            return PageResult.EMPTY_PAGE_RESULT;
        }
        List<MgkHotVideoCollectPo> pos = hotVideoCollectDao.selectByExample(example);

        List<Pair<String, String>> adTypeCreativeIds2LogDate = pos.stream()
                .map(po-> Pair.of(po.getAdTypeCreativeId(),
                        po.getLogDate())).collect(Collectors.toList());
        return PageResult.<Pair<String, String>>builder().total(pos.size()).records(adTypeCreativeIds2LogDate).build();
    }


    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

}
