package com.bilibili.mgk.platform.biz.service.archive.impl;

import com.bilibili.mgk.platform.api.archive.service.ILauArchiveInfoService;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.pojos.LauMaterialBilibiliVideoInfoPo;
import com.bilibili.mgk.platform.biz.service.archive.delegate.LauArchiveInfoServiceDelegate;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName MgkMgkArchiveInfoServiceImpl
 * <AUTHOR>
 * @Date 2021/12/22 2:36 下午
 * @Version 1.0
 **/
@Service
public class LauArchiveInfoServiceImpl implements ILauArchiveInfoService {

    @Autowired
    private LauArchiveInfoServiceDelegate lauArchiveInfoServiceDelegate;

    @Override
    public void refreshLauBilibiliVideoWithCoverArchiveInfo() {
        long startId = 0;
        List<LauArchiveInfoServiceDelegate.ArchiveRefreshInfoDto> refreshInfoDtos =
                lauArchiveInfoServiceDelegate.getLauBilibiliVideoWithCoverRefreshInfoList(startId);
        while (!CollectionUtils.isEmpty(refreshInfoDtos)) {
            List<Long> avidList = refreshInfoDtos.stream()
                    .map(LauArchiveInfoServiceDelegate.ArchiveRefreshInfoDto::getAvid)
                    .collect(Collectors.toList());
            List<LauMaterialBilibiliVideoInfoPo> archivePoList = lauArchiveInfoServiceDelegate.getArchiveInfoListByGrpc(avidList);
            lauArchiveInfoServiceDelegate.saveCmArchiveInfoList(archivePoList);
            startId = refreshInfoDtos.get(refreshInfoDtos.size() - 1).getLongId();
            refreshInfoDtos = lauArchiveInfoServiceDelegate.getLauBilibiliVideoWithCoverRefreshInfoList(startId);
        }
    }

    @Override
    public void refreshLauBilibiliVideoArchiveInfo() {
        long startAvid = 0;
        List<Long> avidList = lauArchiveInfoServiceDelegate.getLauBilibiliVideoAvidList(startAvid);
        while (!CollectionUtils.isEmpty(avidList)) {
            List<LauMaterialBilibiliVideoInfoPo> archivePoList = lauArchiveInfoServiceDelegate.getArchiveInfoListByGrpc(avidList);
            lauArchiveInfoServiceDelegate.saveCmArchiveInfoList(archivePoList);
            startAvid = avidList.get(avidList.size() - 1);
            avidList = lauArchiveInfoServiceDelegate.getLauBilibiliVideoAvidList(startAvid);
        }
    }

    @Override
    public void refreshLauBilibiliVideoInfo() {
        long startAvid = 0;
        List<Long> avidList = lauArchiveInfoServiceDelegate.getLauBilibiliVideoInfoAvidList(startAvid);
        while (!CollectionUtils.isEmpty(avidList)) {
            List<LauMaterialBilibiliVideoInfoPo> archivePoList = lauArchiveInfoServiceDelegate.getArchiveInfoListByGrpc(avidList);
            lauArchiveInfoServiceDelegate.saveCmArchiveInfoList(archivePoList);
            startAvid = avidList.get(avidList.size() - 1);
            avidList = lauArchiveInfoServiceDelegate.getLauBilibiliVideoInfoAvidList(startAvid);
        }
    }

    @Override
    public void refreshMgkArcInfo() {
        int startId = 0;
        List<LauArchiveInfoServiceDelegate.ArchiveRefreshInfoDto> refreshInfoDtos =
                lauArchiveInfoServiceDelegate.getMgkLandingPageAvidList(startId);
        while (!CollectionUtils.isEmpty(refreshInfoDtos)) {
            List<Long> avidList = refreshInfoDtos.stream()
                    .map(LauArchiveInfoServiceDelegate.ArchiveRefreshInfoDto::getAvid)
                    .collect(Collectors.toList());
            List<LauMaterialBilibiliVideoInfoPo> archivePoList = lauArchiveInfoServiceDelegate.getArchiveInfoListByGrpc(avidList);
            lauArchiveInfoServiceDelegate.saveCmArchiveInfoList(archivePoList);
            startId = refreshInfoDtos.get(refreshInfoDtos.size() - 1).getIntId();
            refreshInfoDtos = lauArchiveInfoServiceDelegate.getMgkLandingPageAvidList(startId);
        }
    }

    @Override
    public int saveInfoByAvids(List<Long> avids) {
        List<LauMaterialBilibiliVideoInfoPo> archivePoList = lauArchiveInfoServiceDelegate.getArchiveInfoListByGrpc(avids);
        lauArchiveInfoServiceDelegate.saveCmArchiveInfoList(archivePoList);
        return archivePoList.size();
    }


}
