package com.bilibili.mgk.platform.portal.openapi.wechat.vo;

import com.bilibili.mgk.platform.portal.openapi.form.vo.FormItemDataVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName WechatPackageReportDataVo
 * <AUTHOR>
 * @Date 2022/6/18 8:44 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WechatPackageReportDataVo {
    @ApiModelProperty(value = "微信包ID")
    @NotNull
    private String wechat_package_id;
    @ApiModelProperty(value = "微信号ID")
    @NotNull
    private String wechat_account_id;
    @ApiModelProperty(value = "操作类型 0-复制 1-跳转")
    @NotNull
    private String data_type;
}
