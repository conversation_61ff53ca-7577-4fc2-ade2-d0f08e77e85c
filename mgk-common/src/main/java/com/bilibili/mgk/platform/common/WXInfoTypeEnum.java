package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 */

@AllArgsConstructor
public enum WXInfoTypeEnum {

    CREATE_AUTH("create_auth", "授权成功通知"),

    CHANGE_AUTH("change_auth", "变更授权通知"),

    CANCEL_AUTH("cancel_auth", "取消授权通知"),

    SUITE_TICKET("suite_ticket", "获取ticket"),

    CUSTOMER_ACQUISITION("customer_acquisition", "获客链接"),

    CHANGE_EXTERNAL_CONTACT("change_external_contact", "添加企业客户事件"),

    APPROVE_SPECIAL_AUTH("approve_special_auth", "获客助手权限确认事件"),
    CANCEL_SPECIAL_AUTH("cancel_special_auth", "获客助手权限取消事件"),
    ;

    @Getter
    private final String code;
    @Getter
    private final String desc;

    public static WXInfoTypeEnum getByCode(String code) {
        for (WXInfoTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code WXInfoTypeEnum " + code);
    }

}
