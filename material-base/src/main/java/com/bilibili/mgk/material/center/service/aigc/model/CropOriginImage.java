package com.bilibili.mgk.material.center.service.aigc.model;

import com.bilibili.mgk.material.center.exception.MaterialValidationException;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.vavr.Lazy;
import io.vavr.control.Try;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.imageio.ImageIO;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/6
 */
@Data
@Accessors(chain = true)
public class CropOriginImage implements SnakeCaseBody {


    private String originImgMd5;

    private String originImgUrl;

    @Nullable
    private Integer originImgHeight;

    @Nullable
    private Integer originImgWidth;

    @Nullable
    private ImageRatioType originImgRatioType;

    @Nullable
    private Long originImgMaterialId;

    @JsonIgnore
    private Lazy<BufferedImage> originImgLazy;


    private Boolean usePersistCache = true;


    private Boolean isForceOverwriteCroppedImg = false;


    private Map<Integer, Long> ratioTypeToCroppedImagePrimaryKey = new HashMap<>();


    public static CropOriginImage fromForceOverwriteCroppedImage(
            List<CropResultImage> images) throws MaterialValidationException {

        if (CollectionUtils.isEmpty(images) || images.size() != 3) {
            throw new MaterialValidationException("裁切图数量不正确", false);
        }

        CropResultImage origin = images.stream().filter(img -> img.getIsOrigin())
                .findFirst()
                .orElseThrow(() -> {
                    return new MaterialValidationException("原图不存在", false);
                });

        return new CropOriginImage()
                .setOriginImgMd5(origin.getOriginImgMd5())
                .setOriginImgUrl(origin.getOriginImgUrl())
                .setOriginImgHeight(origin.getOriginImgHeight())
                .setOriginImgWidth(origin.getOriginImgWidth())
                .setOriginImgRatioType(ImageRatioType.fromCode(origin.getOriginImgRatioType()))
                .setOriginImgMaterialId(origin.getOriginImgMaterialId())
                .setUsePersistCache(false)
                .setIsForceOverwriteCroppedImg(true)
                .setRatioTypeToCroppedImagePrimaryKey(images.stream().collect(
                        Collectors.toMap(
                                CropResultImage::getCropImgRatioType,
                                CropResultImage::getId
                        )
                ));

    }



    public void validate() {

        Assert.isTrue(StringUtils.isNotEmpty(originImgMd5), "originImgMd5 is empty");
        Assert.isTrue(StringUtils.isNotEmpty(originImgUrl), "originImgUrl is empty");

        this.originImgLazy = Lazy.of(() -> {
            return Try.of(() -> ImageIO.read(new URL(originImgUrl)))
                    .getOrElseThrow(t -> {

                        throw new IllegalArgumentException("无法下载原图", t);
                    });
        });

        if (originImgHeight == null || originImgWidth == null || originImgWidth <= 0 || originImgHeight <= 0) {

            this.originImgHeight = originImgLazy.get().getHeight();
            this.originImgWidth = originImgLazy.get().getWidth();

        }

        if (originImgRatioType == null) {
            this.originImgRatioType = ImageRatioType.fromResolution(originImgWidth, originImgHeight);
        }

        // FIXME: 在新版本中，要求允许任意比例的图片进行裁切

        if (ImageRatioType.fromResolution(this.originImgWidth, this.originImgHeight) != originImgRatioType) {
            // 检查参数是否本身自洽
            throw new MaterialValidationException("参数宽高比与图片的宽高比不匹配", false);
        }

        if (this.originImgMaterialId == null) {
            this.originImgMaterialId = 0L;
        }

    }






}
