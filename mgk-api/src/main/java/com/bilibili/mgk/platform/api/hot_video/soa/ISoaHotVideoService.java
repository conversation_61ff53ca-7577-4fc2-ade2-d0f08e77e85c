package com.bilibili.mgk.platform.api.hot_video.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoBussInterestDto;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoDto;
import com.bilibili.mgk.platform.api.hot_video.dto.QueryHotVideoDto;

import java.util.List;

/**
 * @file: ISoaHotVideoService
 * @author: gaoming
 * @date: 2020/12/23
 * @version: 1.0
 * @description:
 **/
public interface ISoaHotVideoService {
    PageResult<HotVideoDto> getHotVideoDtos(Operator operator, QueryHotVideoDto queryHotVideoDto);

    List<HotVideoBussInterestDto> getBussInterest();
}
