package com.bilibili.mgk.material.center.service.aigc.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/25
 */
@Data
@Accessors(chain = true)
public class AIDerivationImageBlacklist implements SnakeCaseBody {

    private Long id;

    // uk account_id, img_md5
    private Long accountId;

    private Long creativeId;


    private Long unitId;

    private Long campaignId;

    private String imgMd5;

    private Long version;

    private String imgUrl;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

}
