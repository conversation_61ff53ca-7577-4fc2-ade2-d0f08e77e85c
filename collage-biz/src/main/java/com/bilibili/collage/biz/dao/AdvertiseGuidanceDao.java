package com.bilibili.collage.biz.dao;

import com.bilibili.collage.biz.po.AdvertiseGuidancePo;
import com.bilibili.collage.biz.po.AdvertiseGuidancePoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AdvertiseGuidanceDao {
    long countByExample(AdvertiseGuidancePoExample example);

    int deleteByExample(AdvertiseGuidancePoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AdvertiseGuidancePo record);

    int insertSelective(AdvertiseGuidancePo record);

    List<AdvertiseGuidancePo> selectByExample(AdvertiseGuidancePoExample example);

    AdvertiseGuidancePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AdvertiseGuidancePo record, @Param("example") AdvertiseGuidancePoExample example);

    int updateByExample(@Param("record") AdvertiseGuidancePo record, @Param("example") AdvertiseGuidancePoExample example);

    int updateByPrimaryKeySelective(AdvertiseGuidancePo record);

    int updateByPrimaryKey(AdvertiseGuidancePo record);
}