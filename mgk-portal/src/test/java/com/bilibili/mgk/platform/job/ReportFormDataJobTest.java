package com.bilibili.mgk.platform.job;

import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.mgk.platform.api.data.service.IMgkDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年09月20日
 */
public class ReportFormDataJobTest extends MockitoDefaultTest {

    @Mock
    private IMgkDataService mgkDataService;

    @InjectMocks
    ReportFormDataJob reportFormDataJob;

    @Test
    public void execute() throws Exception {
        ReflectionTestUtils.setField(reportFormDataJob, "reportData", true);
        ReturnT<String> execute = reportFormDataJob.execute("1");
        assertEquals(IJobHandler.SUCCESS, execute);
    }
}