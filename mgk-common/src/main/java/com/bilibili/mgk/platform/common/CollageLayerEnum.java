package com.bilibili.mgk.platform.common;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2018/11/12
 * 拼贴艺术-图层
 **/
@NoArgsConstructor
public enum CollageLayerEnum {

    MAIN_TITLE(1, "主标题", 1, 1, LayerTypeEnum.TEXT),
    SUB_TITLE(2, "副标题", 0, 1, LayerTypeEnum.TEXT),
    OTHER_TEXT(3, "其他文字", 0, 1, LayerTypeEnum.TEXT),
    MATE_IMG(4, "素材图", 0, 1, LayerTypeEnum.IMAGE),
    LOGO_IMG(5, "logo", 1, 1, LayerTypeEnum.IMAGE),
    DECORATE_IMG(6, "装饰图", 0, 0, LayerTypeEnum.IMAGE),
    BACKGROUND_IMG(7, "背景图", 1, 0, LayerTypeEnum.IMAGE);

    @Getter
    private Integer code;

    @Getter
    private String name;

    @Getter
    private Integer unique;

    @Getter
    private Integer isCustomer;

    @Getter
    private LayerTypeEnum type;

    CollageLayerEnum(Integer code, String name, Integer unique, Integer isCustomer, LayerTypeEnum type) {
        this.code = code;
        this.name = name;
        this.unique = unique;
        this.isCustomer = isCustomer;
        this.type = type;
    }

    public static CollageLayerEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("图层类型不可为空");
        }
        for(CollageLayerEnum lrt: values()) {
            if(lrt.getCode().equals(code)) {
                return lrt;
            }
        }

        throw new IllegalArgumentException("未知的图层类型");
    }
}
