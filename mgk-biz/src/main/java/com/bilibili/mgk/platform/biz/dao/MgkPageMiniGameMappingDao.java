package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkPageMiniGameMappingPo;
import com.bilibili.mgk.platform.biz.po.MgkPageMiniGameMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkPageMiniGameMappingDao {
    long countByExample(MgkPageMiniGameMappingPoExample example);

    int deleteByExample(MgkPageMiniGameMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkPageMiniGameMappingPo record);

    int insertBatch(List<MgkPageMiniGameMappingPo> records);

    int insertUpdateBatch(List<MgkPageMiniGameMappingPo> records);

    int insert(MgkPageMiniGameMappingPo record);

    int insertUpdateSelective(MgkPageMiniGameMappingPo record);

    int insertSelective(MgkPageMiniGameMappingPo record);

    List<MgkPageMiniGameMappingPo> selectByExample(MgkPageMiniGameMappingPoExample example);

    MgkPageMiniGameMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkPageMiniGameMappingPo record, @Param("example") MgkPageMiniGameMappingPoExample example);

    int updateByExample(@Param("record") MgkPageMiniGameMappingPo record, @Param("example") MgkPageMiniGameMappingPoExample example);

    int updateByPrimaryKeySelective(MgkPageMiniGameMappingPo record);

    int updateByPrimaryKey(MgkPageMiniGameMappingPo record);
}