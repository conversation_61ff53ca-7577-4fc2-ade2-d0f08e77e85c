package com.bilibili.mgk.material.center.service.course.vo;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */
@Data
@Accessors(chain = true)
public class CourseIdReq implements SnakeCaseBody {


    @ApiModelProperty("访问者账号id，无需前端填写，如有登录态会自动填充")
    private Long accountId;

    private List<Long> courseIds;


    private Boolean isShow;

    public void validate() {

        // TODO 是否要检查first second 是否跨树？
    }


}
