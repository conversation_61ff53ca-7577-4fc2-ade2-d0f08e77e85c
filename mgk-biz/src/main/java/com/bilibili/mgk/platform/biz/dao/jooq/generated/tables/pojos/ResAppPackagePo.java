/*
 * This file is generated by jOOQ.
 */
package com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;

import org.jooq.types.UByte;
import org.jooq.types.UInteger;


/**
 * app应用包信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResAppPackagePo implements Serializable {

    private static final long serialVersionUID = **********;

    private UInteger      id;
    private UInteger      accountId;
    private String        name;
    private String        url;
    private String        packageName;
    private String        appName;
    private UByte         platform;
    private String        version;
    private UInteger      size;
    private String        md5;
    private String        iconUrl;
    private LocalDateTime ctime;
    private LocalDateTime mtime;
    private UByte         isDeleted;
    private String        internalUrl;
    private UByte         status;
    private UByte         platformStatus;
    private String        developerName;
    private String        authorityUrl;
    private String        authCodeList;
    private LocalDateTime apkUpdateTime;
    private String        privacyPolicy;
    private Integer       dmpAppId;
    private UByte         isNewFly;
    private String        description;
    private String        subTitle;

    public ResAppPackagePo() {}

    public ResAppPackagePo(ResAppPackagePo value) {
        this.id = value.id;
        this.accountId = value.accountId;
        this.name = value.name;
        this.url = value.url;
        this.packageName = value.packageName;
        this.appName = value.appName;
        this.platform = value.platform;
        this.version = value.version;
        this.size = value.size;
        this.md5 = value.md5;
        this.iconUrl = value.iconUrl;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.isDeleted = value.isDeleted;
        this.internalUrl = value.internalUrl;
        this.status = value.status;
        this.platformStatus = value.platformStatus;
        this.developerName = value.developerName;
        this.authorityUrl = value.authorityUrl;
        this.authCodeList = value.authCodeList;
        this.apkUpdateTime = value.apkUpdateTime;
        this.privacyPolicy = value.privacyPolicy;
        this.dmpAppId = value.dmpAppId;
        this.isNewFly = value.isNewFly;
        this.description = value.description;
        this.subTitle = value.subTitle;
    }

    public ResAppPackagePo(
        UInteger      id,
        UInteger      accountId,
        String        name,
        String        url,
        String        packageName,
        String        appName,
        UByte         platform,
        String        version,
        UInteger      size,
        String        md5,
        String        iconUrl,
        LocalDateTime ctime,
        LocalDateTime mtime,
        UByte         isDeleted,
        String        internalUrl,
        UByte         status,
        UByte         platformStatus,
        String        developerName,
        String        authorityUrl,
        String        authCodeList,
        LocalDateTime apkUpdateTime,
        String        privacyPolicy,
        Integer       dmpAppId,
        UByte         isNewFly,
        String        description,
        String        subTitle
    ) {
        this.id = id;
        this.accountId = accountId;
        this.name = name;
        this.url = url;
        this.packageName = packageName;
        this.appName = appName;
        this.platform = platform;
        this.version = version;
        this.size = size;
        this.md5 = md5;
        this.iconUrl = iconUrl;
        this.ctime = ctime;
        this.mtime = mtime;
        this.isDeleted = isDeleted;
        this.internalUrl = internalUrl;
        this.status = status;
        this.platformStatus = platformStatus;
        this.developerName = developerName;
        this.authorityUrl = authorityUrl;
        this.authCodeList = authCodeList;
        this.apkUpdateTime = apkUpdateTime;
        this.privacyPolicy = privacyPolicy;
        this.dmpAppId = dmpAppId;
        this.isNewFly = isNewFly;
        this.description = description;
        this.subTitle = subTitle;
    }

    public UInteger getId() {
        return this.id;
    }

    public void setId(UInteger id) {
        this.id = id;
    }

    public UInteger getAccountId() {
        return this.accountId;
    }

    public void setAccountId(UInteger accountId) {
        this.accountId = accountId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPackageName() {
        return this.packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getAppName() {
        return this.appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public UByte getPlatform() {
        return this.platform;
    }

    public void setPlatform(UByte platform) {
        this.platform = platform;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public UInteger getSize() {
        return this.size;
    }

    public void setSize(UInteger size) {
        this.size = size;
    }

    public String getMd5() {
        return this.md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getIconUrl() {
        return this.iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public LocalDateTime getCtime() {
        return this.ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    public LocalDateTime getMtime() {
        return this.mtime;
    }

    public void setMtime(LocalDateTime mtime) {
        this.mtime = mtime;
    }

    public UByte getIsDeleted() {
        return this.isDeleted;
    }

    public void setIsDeleted(UByte isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getInternalUrl() {
        return this.internalUrl;
    }

    public void setInternalUrl(String internalUrl) {
        this.internalUrl = internalUrl;
    }

    public UByte getStatus() {
        return this.status;
    }

    public void setStatus(UByte status) {
        this.status = status;
    }

    public UByte getPlatformStatus() {
        return this.platformStatus;
    }

    public void setPlatformStatus(UByte platformStatus) {
        this.platformStatus = platformStatus;
    }

    public String getDeveloperName() {
        return this.developerName;
    }

    public void setDeveloperName(String developerName) {
        this.developerName = developerName;
    }

    public String getAuthorityUrl() {
        return this.authorityUrl;
    }

    public void setAuthorityUrl(String authorityUrl) {
        this.authorityUrl = authorityUrl;
    }

    public String getAuthCodeList() {
        return this.authCodeList;
    }

    public void setAuthCodeList(String authCodeList) {
        this.authCodeList = authCodeList;
    }

    public LocalDateTime getApkUpdateTime() {
        return this.apkUpdateTime;
    }

    public void setApkUpdateTime(LocalDateTime apkUpdateTime) {
        this.apkUpdateTime = apkUpdateTime;
    }

    public String getPrivacyPolicy() {
        return this.privacyPolicy;
    }

    public void setPrivacyPolicy(String privacyPolicy) {
        this.privacyPolicy = privacyPolicy;
    }

    public Integer getDmpAppId() {
        return this.dmpAppId;
    }

    public void setDmpAppId(Integer dmpAppId) {
        this.dmpAppId = dmpAppId;
    }

    public UByte getIsNewFly() {
        return this.isNewFly;
    }

    public void setIsNewFly(UByte isNewFly) {
        this.isNewFly = isNewFly;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSubTitle() {
        return this.subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResAppPackagePo (");

        sb.append(id);
        sb.append(", ").append(accountId);
        sb.append(", ").append(name);
        sb.append(", ").append(url);
        sb.append(", ").append(packageName);
        sb.append(", ").append(appName);
        sb.append(", ").append(platform);
        sb.append(", ").append(version);
        sb.append(", ").append(size);
        sb.append(", ").append(md5);
        sb.append(", ").append(iconUrl);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(internalUrl);
        sb.append(", ").append(status);
        sb.append(", ").append(platformStatus);
        sb.append(", ").append(developerName);
        sb.append(", ").append(authorityUrl);
        sb.append(", ").append(authCodeList);
        sb.append(", ").append(apkUpdateTime);
        sb.append(", ").append(privacyPolicy);
        sb.append(", ").append(dmpAppId);
        sb.append(", ").append(isNewFly);
        sb.append(", ").append(description);
        sb.append(", ").append(subTitle);

        sb.append(")");
        return sb.toString();
    }
}
