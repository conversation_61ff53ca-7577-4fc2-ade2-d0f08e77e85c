package com.bilibili.mgk.platform.portal.webapi.material.vo;

import com.bilibili.mgk.material.center.service.asset.model.DynamicDrawPic;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicInfo;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicInfo.ShadowInfo;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 相比{@link MaterialDynamicInfo} 额外增加ios包信息， android包信息，游戏信息， 资质信息
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/8
 */
@Data
@Accessors(chain = true)
public class MaterialDynamicDetailInfo implements SnakeCaseBody {


    @ApiModelProperty(value = "动态id")
    private String dynId;

    @ApiModelProperty(value = "动态类型")
    private String dynRid;

    @ApiModelProperty(value = "动态类型")
    private String dynType;


    // owner
    @ApiModelProperty(value = "用户id")
    private Long uid;

    @ApiModelProperty(value = "用户名称")
    private String upName;

    @ApiModelProperty(value = "用户头像")
    private String upFace;


    @ApiModelProperty(value = "动态标题")
    private String title;

    @ApiModelProperty(value = "动态图片")
    private List<DynamicDrawPic> pics;

    @ApiModelProperty(value = "预约id")
    private Long reserveId;

    // 预约id
    @ApiModelProperty(value = "调度任务id")
    private Long scheduleId;


    @ApiModelProperty(value = "发布时间,单位秒")
    private Long pubTime;

    @ApiModelProperty(value = "动态内容")
    private String content;

    @ApiModelProperty(value = "动态内容v2，支持富文本")
    private List<RichTextDetailDTO> richContent;

    // 动态是否支持暗投

    @ApiModelProperty(value = "是否支持暗投")
    private Boolean sneakingVisible;

    // 联合投稿动态信息
    @ApiModelProperty(value = "联合投稿信息")
    private ShadowInfo shadowInfo;


    @ApiModelProperty(value = "定时发布调度时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime scheduleTime;


    @ApiModelProperty(value = "动态发布状态")
    private Integer publishStatus;


    @ApiModelProperty(value = "错误码，只有定时发布失败才存在")
    private Integer errCode;


    @ApiModelProperty(value = "错误信息，只有定时发布失败才存在")
    private String errMsg;

    @ApiModelProperty("商业侧审核原因")
    private String commerceAuditReason;

    @ApiModelProperty("商务审核状态, 0:未审核, 1:审核通过, 2:审核不通过")
    private Integer commerceAuditStatus;

    @ApiModelProperty("商务审核状态描述, 商业审核中, 商业审核驳回")
    private String commerceAuditStatusDesc;


}
