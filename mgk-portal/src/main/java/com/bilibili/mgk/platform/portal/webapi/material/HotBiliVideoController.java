package com.bilibili.mgk.platform.portal.webapi.material;

import com.bilibili.mgk.material.center.event.HotBiliVideoDataReadyEvent;
import com.bilibili.mgk.material.center.service.creative.impl.HotBiliVideoServiceImpl;
import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideo;
import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideoDetail;
import com.bilibili.mgk.material.center.service.creative.model.UpAuthType;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.DurationSection;
import com.bilibili.mgk.material.center.service.creative.vo.HotBiliVideoId;
import com.bilibili.mgk.material.center.service.creative.vo.HotBiliVideoQuery;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortOrder;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.QueryKeywordType;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.vavr.control.Try;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/11
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/hot_bili_video")
@Api(value = "/material/hot_bili_video", tags = "material-center")
public class HotBiliVideoController extends BasicController {

    @Resource
    private HotBiliVideoServiceImpl hotBiliVideoService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @ApiOperation("展示b站热门视频数据任务完成webhook")
    @RequestMapping(value = "/ready", method = RequestMethod.GET)
    @FreeLogin
    public Response<HotBiliVideoDetail> ready(
            @RequestParam(value = "logdate", required = false) String logdate
    ) {

        eventPublisher.publishEvent(new HotBiliVideoDataReadyEvent(this)
                .setEventRecvDate(LocalDate.now()) // 0308
                .setLogdateReady(logdate) // 0307
                .setLogdateFrom(Optional.ofNullable(logdate)
                        .map(date -> {
                            return Try.of(() -> DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.parse(date,
                                            DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                                    .minusDays(1))).getOrNull();
                        }).orElse(null) // 0306

                ));

        return Response.SUCCESS();
    }


    @ApiOperation("展示b站热门视频素材")
    // FIXME 为了兼容{@link WebAPISecurityFilter}的注解扫描，其实更应该是那个扫描过滤器本身的实现问题
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @FreeLogin
    public Response<HotBiliVideoDetail> detail(
            @ApiIgnore Context context,
            @ApiParam("avid") @RequestParam(value = "avid", required = false) Long avid,
            @ApiParam("时间聚合窗口 1d 3d 7d 30d") @RequestParam(value = "day_type", defaultValue = "1d") String dateAggregation,
            @ApiParam("bvid") @RequestParam(value = "bvid", required = false) String bvid) {

        return Response.SUCCESS(hotBiliVideoService.detail(
                        Optional.ofNullable(context).map(Context::getAccountId).map(Integer::longValue).orElse(null),
                        new HotBiliVideoId()
                                .setAvid(avid)
                                .setBvid(bvid)
                                .setDayType(DateAggregation.fromParam(dateAggregation))
                )
        );
    }


    @ApiOperation("展示b站热门视频素材")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @FreeLogin
    public Response<Pagination<List<HotBiliVideo>>> analysis(
            @ApiIgnore Context context,
            @ApiParam("关键词类型，composite video_tag video_title")
            @RequestParam(value = "keyword_type", defaultValue = "composite") QueryKeywordType keywordType,
            @ApiParam("关键词") @RequestParam(value = "keyword", required = false) String keyword,
            @ApiParam("商业兴趣") @RequestParam(value = "business_interest", required = false) String businessInterest,
            @ApiParam("视频一级类目") @RequestParam(value = "video_first_category", required = false) String videoFirstCategory,
            @ApiParam("是否竖屏") @RequestParam(value = "is_vertical_screen", required = false) Integer isVerticalScreen,
            @ApiParam("up类型") @RequestParam(value = "up_type", required = false) UpAuthType upType,
            @ApiParam("时间区间below15s \n\nbetween15_30s\nbetween30_60s\nabove60s")
            @RequestParam(value = "duration_section", required = false) DurationSection durationSection,
            @ApiParam("排序方式 play, likes, coin, share, reply, fav, danmu")
            @RequestParam(value = "sort_by", defaultValue = "play") MaterialSortBy sortBy,
            @ApiParam("") @RequestParam(value = "order", defaultValue = "desc") MaterialSortOrder order,
            @ApiParam("时间聚合窗口 1d 3d 7d 30d") @RequestParam(value = "day_type", defaultValue = "1d") String dateAggregation,
            @ApiParam("页号") @RequestParam(value = "pn", defaultValue = "1") Integer pn,
            @ApiParam("页大小") @RequestParam(value = "ps", defaultValue = "20") Integer ps) {
        return Response.SUCCESS(hotBiliVideoService.page(new HotBiliVideoQuery()
                .setAccountId(
                        Optional.ofNullable(context).map(Context::getAccountId).map(Integer::longValue).orElse(null))
                .setKeywordType(keywordType)
                .setKeyword(keyword)
                .setBusinessInterest(businessInterest)
                .setVideoFirstCategory(videoFirstCategory)
                .setIsVerticalScreen(isVerticalScreen)
                .setUpType(upType)
                .setDurationSection(durationSection)
                .setSortBy(sortBy)
                .setOrder(order)
                .setDayType(DateAggregation.fromParam(dateAggregation))
                .setPn(pn)
                .setPs(ps)));
    }










}
