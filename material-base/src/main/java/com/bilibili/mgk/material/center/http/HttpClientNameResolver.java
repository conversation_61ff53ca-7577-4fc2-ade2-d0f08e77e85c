package com.bilibili.mgk.material.center.http;

import com.google.common.collect.Lists;
import java.net.InetSocketAddress;
import java.util.List;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import pleiades.component.env.Environment;
import pleiades.component.env.EnvironmentKeys;
import pleiades.venus.naming.client.event.CacheRefreshedEvent;
import pleiades.venus.naming.client.listener.NamingEventListener;
import pleiades.venus.naming.client.model.Address;
import pleiades.venus.naming.client.model.AppInstance;
import pleiades.venus.naming.client.resolve.NamingResolver;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/26
 */
@Slf4j
public class HttpClientNameResolver {

    public static void registerRefreshListener(String zone, NamingResolver resolver,
            Consumer<List<InetSocketAddress>> addressChangeListener) {
        NamingEventListener namingEventListener = event -> {
            if (event instanceof CacheRefreshedEvent) {
                log.info("Receive polls event {}", event);
                try {
                    List<InetSocketAddress> addrs = addresses(zone, resolver);
                    if (!addrs.isEmpty()) {
                        addressChangeListener.accept(addrs);
                    }
                } catch (Exception e) {
                    log.warn("Error http client submitting update task to executor, skipping one round of updates", e);
                }
            }
        };
        resolver.registerEventListener(namingEventListener);
    }

    public static List<InetSocketAddress> addresses(String zone, NamingResolver resolver) {
        List<InetSocketAddress> addressList = Lists.newArrayList();
        String deployColor = Environment.ofNullable(EnvironmentKeys.DEPLOY_COLOR)
                .orElse("");
        List<AppInstance> appInstances = resolver.fetch(zone, null, deployColor);
        if (appInstances != null && !appInstances.isEmpty()) {
            for (AppInstance appInstance : appInstances) {
                Address address = appInstance.getAddress();
                addressList.add(new InetSocketAddress(address.getHost(), address.getPort()));
            }
        }
        if (addressList.isEmpty()) {
            log.error("Empty http client address, resolver:{}, zone:{}, color:{}", resolver.getAppId(), zone,
                    deployColor);
        } else {
            log.info("Http client({}) address updated {}", resolver.getAppId(), addressList);
        }
        return addressList;
    }

}