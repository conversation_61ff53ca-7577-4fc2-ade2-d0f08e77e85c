package com.bilibili.mgk.platform.api.auto.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MgkAutoPageConfigBo {

    private Integer accountId;

    private Integer androidGameId;

    private String androidMonitorParam;

    private String iosFallbackH5Url;

    private Long id;

    private String iosAppStoreUrl;

    private Integer platformId;

    private String title;

    public static MgkAutoPageConfigBo fromAndroid(MgkAutoAndroidPageConfigBo androidBo) {
        if (Objects.isNull(androidBo)) return null;
        final MgkAutoPageConfigBo bo = new MgkAutoPageConfigBo();
        BeanUtils.copyProperties(androidBo, bo);
        return bo;
    }

    public static MgkAutoPageConfigBo fromIOS(MgkAutoIOSPageConfigBo iosBo) {
        if (Objects.isNull(iosBo)) return null;
        final MgkAutoPageConfigBo bo = new MgkAutoPageConfigBo();
        BeanUtils.copyProperties(iosBo, bo);
        return bo;
    }

}

