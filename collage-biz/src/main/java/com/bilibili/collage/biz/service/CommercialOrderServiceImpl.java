package com.bilibili.collage.biz.service;

import com.bapis.ad.commercialorder.adauth.Adauth4AdpServiceGrpc;
import com.bapis.ad.commercialorder.adauth.GetPageAdAuthGroupByRs;
import com.bapis.ad.commercialorder.adauth.GetPageAdAuthReq;
import com.bapis.archive.service.Arc;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoDto;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoQueryDto;
import com.bilibili.collage.api.service.ICommercialOrderService;
import com.bilibili.collage.biz.utils.ArchiveUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CommercialOrderServiceImpl implements ICommercialOrderService {

    @Resource(name = "adAuth4AdpServiceBlockingStub")
    private Adauth4AdpServiceGrpc.Adauth4AdpServiceBlockingStub adAuth4AdpServiceBlockingStub;

    @Autowired
    private CollageArchiveService collageArchiveService;

    private static final int CM_ORDER_AUTH_CONFIRM_AND_ACTIVE = 4;
    private static final int CM_ORDER_SOURCE_TYPE_ARCHIVE = 2;
    private static final String AVID = "avid";
    private static final String ASC = "asc";
    private static final String DESC = "desc";

    @Override
    public PageResult<CollageEnterpriseVideoDto> getCommercialOrderVideos(CollageEnterpriseVideoQueryDto queryDto) {

        GetPageAdAuthReq.Builder reqBuilder = GetPageAdAuthReq.newBuilder();
        reqBuilder.setAdvertisersAccountId(queryDto.getAccountId());
        if (Utils.isPositive(queryDto.getAvid())) {
            reqBuilder.setAvid(queryDto.getAvid());
        }
        reqBuilder.setStateExt(CM_ORDER_AUTH_CONFIRM_AND_ACTIVE);
        reqBuilder.setSourceType(CM_ORDER_SOURCE_TYPE_ARCHIVE);
        reqBuilder.setGroupBy(AVID);
        reqBuilder.setPage(queryDto.getPage());
        reqBuilder.setSize(queryDto.getPageSize());
        GetPageAdAuthReq req = reqBuilder.build();
        GetPageAdAuthGroupByRs resp = adAuth4AdpServiceBlockingStub.withWaitForReady()
                .withDeadlineAfter(5000L, TimeUnit.MILLISECONDS)
                .getPageAdAuthGroupBy(req);

        long total = resp.getTotal();
        List<Long> avidList = resp.getListList()
                .stream()
                .map(GetPageAdAuthGroupByRs.GetPageAdAuthGroupByRsItem::getAvid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(avidList)) {
            return PageResult.<CollageEnterpriseVideoDto>builder()
                    .total((int)total)
                    .records(Collections.emptyList())
                    .build();
        }

        Map<Long, Arc> arcMap = collageArchiveService.getArcMap(avidList);
        List<CollageEnterpriseVideoDto> result = arcMap.values().stream()
                .map(ArchiveUtils::fromArc)
                .collect(Collectors.toList());

        List<CollageEnterpriseVideoDto> sortedResult = getSortedResult(result, queryDto.getOrderBy());
        return PageResult.<CollageEnterpriseVideoDto>builder()
                .total((int)total)
                .records(sortedResult)
                .build();
    }

    private List<CollageEnterpriseVideoDto> getSortedResult(List<CollageEnterpriseVideoDto> records, String orderBy) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        // 目前order by 仅支持 pubDate
        if (orderBy.toLowerCase().contains(DESC)) {
            return records.stream()
                    .sorted(Comparator.comparing(CollageEnterpriseVideoDto::getPubTime).reversed())
                    .collect(Collectors.toList());
        } else {
            return records.stream()
                    .sorted(Comparator.comparing(CollageEnterpriseVideoDto::getPubTime))
                    .collect(Collectors.toList());
        }
    }
}
