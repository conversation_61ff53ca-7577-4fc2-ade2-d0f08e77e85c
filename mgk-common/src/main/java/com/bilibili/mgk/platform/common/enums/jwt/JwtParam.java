package com.bilibili.mgk.platform.common.enums.jwt;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum JwtParam {

    ACCOUNT_ID("acc_id", "账户id"),
    ACCOUNT_NAME("acc_name", "账户名称"),
    CREATE_TIME("ctime", "创建时间"),
    EXPIRE_TIME("ext", "过期时间"),
    MID("mid", "uid"),

    STATE("state", "state"),
    DATA("data", "data"),
    ;

    @Getter
    private final String code;

    @Getter
    private final String desc;
}
