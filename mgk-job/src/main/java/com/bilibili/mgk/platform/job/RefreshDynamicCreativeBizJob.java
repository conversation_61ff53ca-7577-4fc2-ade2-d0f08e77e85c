package com.bilibili.mgk.platform.job;

import com.bilibili.adp.launch.api.service.IDynamicAdService;
import com.bilibili.mgk.platform.api.dynamic.service.IMgkDynamicService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @file: RefreshDynamicCreativeBizJob
 * @author: gaoming
 * @date: 2021/05/22
 * @version: 1.0
 * @description:
 **/
@Slf4j
@Component
@JobHandler("DynamicCreativeBizJob")
public class RefreshDynamicCreativeBizJob extends IJobHandler {

    @Autowired
    private IMgkDynamicService mgkDynamicService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            mgkDynamicService.refreshDynamicBizMappingInRedis();
        } catch (Exception e) {
            log.info("RefreshDynamicCreativeBizJob Failed e {}", e.getMessage());
        }
        log.info("RefreshDynamicCreativeBizJob finished");
        return SUCCESS;
    }
}
