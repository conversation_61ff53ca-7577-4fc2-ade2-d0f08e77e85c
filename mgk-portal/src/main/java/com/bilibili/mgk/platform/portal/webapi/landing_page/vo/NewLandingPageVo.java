package com.bilibili.mgk.platform.portal.webapi.landing_page.vo;

import com.bilibili.mgk.platform.portal.webapi.game.vo.GameVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/1/22
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NewLandingPageVo {

    @ApiModelProperty(value = "原始落地页页面ID， 复制时必传")
    private String origin_page_id;

    @ApiModelProperty(value = "落地页版本")
    private String page_version;

    @ApiModelProperty(value = "落地页名称")
    @NotNull(message = "落地页名称不可为空")
    private String name;

    @ApiModelProperty(value = "页面标题")
    @NotNull(message = "页面标题不可为空")
    private String title;

    @ApiModelProperty(value = "模板样式：1-浮层样式 2-图文样式 3-橱窗样式 4-视频样式 100-自定义")
    @NotNull(message = "模板样式不可为空")
    private Integer template_style;

    @ApiModelProperty(value = "生效开始时间")
    private Long effective_start_time;

    @ApiModelProperty(value = "生效结束时间")
    private Long effective_end_time;

    @ApiModelProperty(value = "页面配置")
    @NotNull(message = "页面配置不可为空")
    private String config;

    @ApiModelProperty(value = "落地页类型 1:H5 2:原生 ")
    @NotNull(message = "落地页类型")
    private Integer page_type;

    @ApiModelProperty(value = "稿件IDs")
    private List<Long> av_ids;

    @ApiModelProperty(value = "曝光监控链接")
    private List<String> show_urls;

    @ApiModelProperty(value = "表单ID")
    private String form_id;

    @ApiModelProperty(value = "原生自定义表单IDs")
    private List<String> form_ids;

    @ApiModelProperty(value = "微信小游戏IDs")
    private List<Integer> mini_game_ids;

    @ApiModelProperty(value = "微信加粉微信包id")
    private Integer wechat_package_id;

    @ApiModelProperty(value = "APP ID")
    private Integer app_package_id;

    @ApiModelProperty(value = "APP IDs")
    private List<Integer> app_package_ids;

    @ApiModelProperty(value = "biz")
    private Integer biz_id;

    @ApiModelProperty(value = "bizIds")
    private List<Integer> biz_ids;

    @ApiModelProperty(value = "是否模板落地页")
    private Integer is_model;

    @ApiModelProperty(value = "模板id")
    private Long model_id;

    @ApiModelProperty(value = "是否支持PC")
    private Integer is_pc;

    @ApiModelProperty(value = "页面是否含有header 0-没有 1-有")
    private Integer header;

    @ApiModelProperty(value = "是否需要转场 0-不需要 1-需要")
    private Integer has_transition;

    @ApiModelProperty(value = "转场信息")
    private String transition;

    @ApiModelProperty(value = "封面地址")
    private String page_cover;

    @ApiModelProperty(value = "落地页是否包含滚动信息 0-不包含 1-包含")
    private Integer is_form_scroll;

    @ApiModelProperty(value = "是否包含动态商品组件 0-不包含 1-包含")
    private Integer has_dpa_goods;

    @ApiModelProperty(value = "总模块高度")
    private String total_block_size;

    @ApiModelProperty(value = "总下载组件高度")
    private String total_download_component_size;

    @ApiModelProperty(value = "首屏下载组件高度")
    private String total_first_screen_download_component_size;

    @ApiModelProperty(value = "最大的下载组件高度")
    private String max_download_component_size;

    @ApiModelProperty(value = "获客链接id列表")
    private List<String> customer_acquisition_link_ids;

    @ApiModelProperty(value = "是否是表单预约落地页 0-否 1-是")
    private Integer is_game_form_reserve;

    @ApiModelProperty(value = "游戏列表")
    private List<GameVo> games;

    @ApiModelProperty(value = "推送源落地页ids")
    private List<Long> copy_source_page_ids;

    @ApiModelProperty(value = "要复制的账号ids")
    private List<Integer> to_acc_ids;

    @ApiModelProperty(value = "是否有欢迎词, 0-否 1-是")
    private Integer has_welcome_words;

    @ApiModelProperty(value = "欢迎词")
    private String welcome_words;

    @ApiModelProperty(value = "是否有留资组件, 0-否 1-是")
    private Integer is_leave_data;

    @ApiModelProperty(value = "是否有faq, 0-否 1-是")
    private Integer has_consult_faq;

    @ApiModelProperty(value = "faq")
    private String consult_faq;

    @ApiModelProperty(value = "三方客服code 1-商务通")
    private Integer customer_service_type;

    @ApiModelProperty(value = "客服头像")
    private String profile;

    @ApiModelProperty(value = "连接类型：onPage 进页建连， onMessage 开口建连")
    private String connect_type;

}
