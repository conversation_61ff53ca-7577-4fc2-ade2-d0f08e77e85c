package com.biz.common.doc.tree.service.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import com.biz.common.doc.tree.model.DocFlattenNode;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class NodeSearchResult extends DocFlattenNode implements SnakeCaseBody {


    /**
     * 搜索结果的高亮，
     */
    private List<String> docContentHighlight;

    private List<String> docTitleHighlight;

    private List<String> docSummaryHighlight;


}
