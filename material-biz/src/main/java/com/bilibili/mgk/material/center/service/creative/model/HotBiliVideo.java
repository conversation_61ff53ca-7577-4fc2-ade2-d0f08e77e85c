package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 热门b站视频
 *
 * <AUTHOR>
 * @desc
 * @date 2024/3/11
 */
@Data
@Accessors(chain = true)
public class HotBiliVideo implements SnakeCaseBody {


    /**
     * 时间窗 1d 7d 30d
     */
    private String dataType;


    private String logDate;


    private MaterialType materialType = MaterialType.bili_video;


    /**
     * 行业兴趣
     */
    private String bussInterest;


    private List<String> businessInterestNames;

    /**
     * avid
     */
    private Long avid;

    /**
     * bvid
     */
    private String bvid;

    /**
     * up主昵称
     */
    private String upName;

    private String upAvatar;


    private UpAuthType upType;

    /**
     * up主id
     */
    private Long upMid;

//    /***
//     * avatar
//     */
//    private String upPhoto;

    /**
     * 视频一级类目
     */
    private String tname;


    /**
     * 视频子类目
     */
    private String subTname;

    /**
     * {@link VerifyType}
     */
    private Integer verifyType;


    /**
     * 是否竖屏
     */
    private Integer isVerticalScreen;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pubtime;


    /**
     * 标题
     */
    private String title;


    /**
     * 封面
     */
    private String cover;


    /**
     * 标签
     */
    private String tag;


    /**
     * 时长
     */
    private Long duration;


    /**
     * 播放
     */
    private Long play;


    /**
     * 点赞
     */
    private Long likes;


    /**
     * 硬币
     */
    private Long coin;


    /**
     * 分享
     */
    private Long share;


    /**
     * 收藏
     */
    private Long fav;


    /**
     * 回复
     */
    private Long reply;


    /**
     * 弹幕
     */
    private Long danmu;


    /**
     * 播放增长
     */
    private Long playDaily;


    /**
     * 回复增长
     */
    private Long replyDaily;


    /**
     * 收藏增长
     */
    private Long favDaily;


    /**
     * 硬币增长
     */
    private Long coinDaily;


    /**
     * 弹幕增长
     */
    private Long danmuDaily;


    /**
     * 分享增长
     */
    private Long shareDaily;

    /**
     * 点赞增长
     */
    private Long likesDaily;

    /**
     * 是否收藏
     */
    private Boolean favorite;

    private String materialId;

    private String favoriteKey;

    private String latestUpdateTime;


    public UpAuthType getUpType() {
        if (upType == null) {
            upType = UpAuthType.fromVerifyType(verifyType);
        }
        return upType;
    }


}
