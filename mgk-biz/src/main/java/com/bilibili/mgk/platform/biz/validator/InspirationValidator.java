package com.bilibili.mgk.platform.biz.validator;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.inspiration.dto.NewArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.QueryArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.UpdateArticleDto;
import com.bilibili.mgk.platform.common.CollageIndustryEnum;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;
import reactor.core.support.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @file: MgkInspirationValidator
 * @author: gaoming
 * @date: 2021/03/23
 * @version: 1.0
 * @description:
 **/
@Service
public class InspirationValidator {
    public void validNewArticleDto(NewArticleDto newArticleDto) {
        Assert.isTrue(!Strings.isNullOrEmpty(newArticleDto.getTitle()), "标题不可为空");
        Assert.isTrue(!Strings.isNullOrEmpty(newArticleDto.getCover()), "封面不可为空");
        Assert.isTrue(!Strings.isNullOrEmpty(newArticleDto.getContent()), "内容不可为空");
        Assert.isTrue(!Strings.isNullOrEmpty(newArticleDto.getIndustry()), "行业不可为空");
        Arrays.stream(newArticleDto.getIndustry().split(",")).forEach(code -> CollageIndustryEnum.getByCode(Integer.valueOf(code)));
    }

    public void validUpdateArticleDto(UpdateArticleDto updateArticleDto) {
        Assert.isTrue(Utils.isPositive(updateArticleDto.getArticleId()), "文章Id不合法");
        if (!Strings.isNullOrEmpty(updateArticleDto.getIndustry())) {
            Arrays.stream(updateArticleDto.getIndustry().split(",")).forEach(code -> CollageIndustryEnum.getByCode(Integer.valueOf(code)));
        }
    }

    public void validQueryArticleDto(QueryArticleDto queryArticleDto) {
    }
}
