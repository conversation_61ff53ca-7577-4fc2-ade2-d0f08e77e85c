package com.bilibili.mgk.platform.api.log.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName MgkPageOperateLogExportDto
 * <AUTHOR>
 * @Date 2022/12/20 3:04 上午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkPageAuditLogExportDto implements Serializable {

    private Long id;

    private Integer accountId;

    private Long pageId;

    private String jumpUrl;

    private String jumpUrlSecondary;

    private String auditorName;

    private String operateTypeDesc;

    private String reason;

    private String auditTime;

    private String sendAuditTime;

    private Long workOrderId;

    private List<Integer> appPackageIds;

    private Integer wechatPackageId;

}
