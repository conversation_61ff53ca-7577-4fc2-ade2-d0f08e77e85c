package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/07/14
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccessTokenDto implements Serializable {
    private static final long serialVersionUID = 2046730979883435729L;
    private String access_token;
    private Long expires_in;
}
