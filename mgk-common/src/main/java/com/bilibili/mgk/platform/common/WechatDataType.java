package com.bilibili.mgk.platform.common;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName WechatDataSubmitType
 * <AUTHOR>
 * @Date 2022/6/18 3:06 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
public enum WechatDataType {
    COPY(0, "账号复制", MgkConstants.WECHAT_PACKAGE_COPY_LOCK_REASON, OcpcConvType.WECHAT_COPY.getValue()),

    JUMP(1, "应用唤起", MgkConstants.WECHAT_PACKAGE_JUMP_LOCK_REASON, null),

    //微信加粉还需要上报一次表单提交 因为O目标是表单提交
    ADD_FANS(2, "微信加粉", MgkConstants.WECHAT_PACKAGE_JUMP_LOCK_REASON, OcpcConvType.WX_ADD_FANS.getValue()),

    CHAT(3, "开口聊天", MgkConstants.WECHAT_PACKAGE_JUMP_LOCK_REASON, OcpcConvType.WX_CHAT.getValue()),

    FORM_SUBMIT(4, "表单提交", MgkConstants.WECHAT_PACKAGE_JUMP_LOCK_REASON, OcpcConvType.FORM_SUBMIT.getValue()),
    ;

    @Getter
    private Integer code;
    @Getter
    private String desc;
    @Getter
    private String lockReason;

    @Getter
    private String convType;

    public static final List<Integer> NEED_REPORT = Lists.newArrayList(COPY.code, ADD_FANS.code, CHAT.code, FORM_SUBMIT.code);

    public static final List<Integer> WORK_WX_REPORT = Lists.newArrayList(ADD_FANS.code, CHAT.code, FORM_SUBMIT.code);

    public static final List<Integer> WORK_WX_SHOW = Lists.newArrayList(ADD_FANS.code, CHAT.code);

    public static WechatDataType getByCode(int code) {
        for (WechatDataType bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code WechatDataSubmitType " + code);
    }
}
