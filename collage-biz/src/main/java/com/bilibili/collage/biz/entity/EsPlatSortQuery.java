package com.bilibili.collage.biz.entity;

import java.io.Serializable;

public class EsPlatSortQuery implements Serializable {
    private String field;
    private EsPlatSortQuery.Type type;
    private static final long serialVersionUID = 1L;

    public String getField() {
        return this.field;
    }

    public EsPlatSortQuery.Type getType() {
        return this.type;
    }

    public void setField(String field) {
        this.field = field;
    }

    public void setType(EsPlatSortQuery.Type type) {
        this.type = type;
    }

    public EsPlatSortQuery() {
    }

    public EsPlatSortQuery(String field, EsPlatSortQuery.Type type) {
        this.field = field;
        this.type = type;
    }

    public static enum Type {
        ASC,
        DESC;

        private Type() {
        }
    }
}