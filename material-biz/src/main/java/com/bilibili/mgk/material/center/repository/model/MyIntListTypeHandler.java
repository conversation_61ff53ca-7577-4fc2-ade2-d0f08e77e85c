package com.bilibili.mgk.material.center.repository.model;

import com.bilibili.mgk.material.center.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import io.vavr.control.Try;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/27
 */
public class MyIntListTypeHandler extends BaseTypeHandler<List<Integer>> {

    public MyIntListTypeHandler() {
    }

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<Integer> list, JdbcType jdbcType)
            throws SQLException {

        preparedStatement.setString(i,
                JsonUtil.writeValueAsString(list)
        );

    }

    @Override
    public List<Integer> getNullableResult(ResultSet resultSet, String s) throws SQLException {

        return string2List(resultSet.getString(s));
    }

    @Override
    public List<Integer> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return string2List(resultSet.getString(i));
    }

    @Override
    public List<Integer> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return string2List(callableStatement.getString(i));
    }


    private List<Integer> string2List(String s) {
        return Try.of(() -> JsonUtil.readValue(s, new TypeReference<List<Integer>>() {
        })).getOrElse(new ArrayList<>());
    }

}