package com.bilibili.mgk.material.center.service.creative.impl;

import com.bilibili.mgk.material.center.facade.UpRankOneserviceFacade;
import com.bilibili.mgk.material.center.facade.UpRankVideoInfoOneserviceFacade;
import com.bilibili.mgk.material.center.service.creative.UpRankService;
import com.bilibili.mgk.material.center.service.creative.model.FansGenderTag;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdMandatory;
import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.bilibili.mgk.material.center.service.creative.model.UpRankInfo;
import com.bilibili.mgk.material.center.service.creative.model.UpRankVideoInfo;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankMidQuery;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankQuery;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankVideoQuery;
import com.bilibili.mgk.material.center.service.favorite.MaterialFavoriteKeyUtils;
import com.bilibili.mgk.material.center.service.favorite.MaterialFavoriteService;
import com.bilibili.mgk.material.center.service.mainsite.BiliAccountService;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */
@Slf4j
@Service
public class UpRankServiceImpl implements UpRankService {

    @Resource
    private UpRankOneserviceFacade upRankFacade;


    @Resource
    private UpRankVideoInfoOneserviceFacade upRankVideoInfoFacade;

    @Resource
    private MaterialFavoriteService materialFavoriteService;

    @Resource
    private BiliAccountService biliAccountService;


    @Value("${material.oneservice.up-rank.male-major-percent:0.55}")
    private String maleMajorPercent;

    @Value("${material.oneservice.up-rank.female-major-percent:0.45}")
    private String femaleMajorPercent;


    @Override
    public Pagination<List<UpRankInfo>> page(UpRankQuery pageQuery) {

        pageQuery.validate();

        return upRankFacade.page(pageQuery)
                .map(list -> {
                    list = this.fillingUpFace(list);
                    this.fillingFavoriteStatus(pageQuery.getAccountId(), list);
                    this.fillingFansGenderTag(list);
                    return list;
                });
    }


    private void fillingFavoriteStatus(Long accountId, List<UpRankInfo> upRankInfos) {

        upRankInfos.forEach(up -> {

            up.setFavoriteKey(MaterialFavoriteKeyUtils.generateId(
                    new MaterialIdMandatory()
                            .setMaterialType(MaterialType.up_rank)
                            .setMid(up.getUpMid())
            ));
        });

        materialFavoriteService.fillingFavoriteStatus(
                accountId,
                upRankInfos,
                UpRankInfo::getFavoriteKey,
                UpRankInfo::setFavorite
        );

    }


    private List<UpRankInfo> fillingUpFace(List<UpRankInfo> upRankInfos) {

        return Try.of(() -> biliAccountService.fillingUpFaceAndFilterOutInvalidMidOfUpRankInfo(upRankInfos))
                .getOrElse(upRankInfos);
    }


    private void fillingUpFaceForRankVideo(List<UpRankVideoInfo> upRankInfos) {

        Try.run(() -> biliAccountService.fillingUpFaceOfUpRankVideoInfo(upRankInfos));
    }


    private void fillingFansGenderTag(List<UpRankInfo> upRankInfos) {

        Try.run(() -> upRankInfos.forEach(up -> {

            up.setFansGenderTag(FansGenderTag.from(
                    up.getMaleFansRatio(),
                    maleMajorPercent,
                    femaleMajorPercent
            ));
        }));

    }



    @Override
    public List<UpRankInfo> detail(UpRankMidQuery midQuery) {

        midQuery.validate();

        Map<Long, UpRankInfo> upId2RankInfo = Optional.ofNullable(
                        upRankFacade.page(midQuery.toRankQuery()).getData())
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(
                        up -> up.getUpMid(),
                        up -> up,
                        (a, b) -> b
                ));

        List<UpRankInfo> result = midQuery.getMids()
                .stream()
                .map(mid -> {
                    return Try.of(() -> upId2RankInfo.get(Long.valueOf(mid))).getOrNull();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        result = this.fillingUpFace(result);
        this.fillingFavoriteStatus(midQuery.getAccountId(), result);
        this.fillingFansGenderTag(result);

        return result;
    }

    @Override
    public List<UpRankVideoInfo> listUpVideos(UpRankVideoQuery query) {

        query.validate();

        List<UpRankVideoInfo> result = upRankVideoInfoFacade.list(query);

        this.fillingUpFaceForRankVideo(result);

        return result;
    }
}
