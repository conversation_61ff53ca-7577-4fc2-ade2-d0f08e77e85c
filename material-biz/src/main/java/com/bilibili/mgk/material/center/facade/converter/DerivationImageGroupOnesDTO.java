package com.bilibili.mgk.material.center.facade.converter;

import com.bilibili.mgk.material.center.service.asset.model.DerivationImageGroup;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/28
 */
@Data
public class DerivationImageGroupOnesDTO {

    private Long accountId;

//    @JsonProperty("creative_id")
//    private Long originCreativeId;

    @JsonProperty("ori_img")
    private String originImgUrl;

    @JsonProperty("ori_md5")
    private String originImgMd5;

    private String cost;

    private String logDate;

    private Long version;

    private String derivatedImgs;


    public static DerivationImageGroupOnesDTO fromMapValue(Map<String, String> mapValue) {
        return JsonUtil.fromJson(mapValue, DerivationImageGroupOnesDTO.class);
    }

    public DerivationImageGroup toBO() {
        return OneServiceDTOConverter.converter.toMaterialBO(this);
    }


}
