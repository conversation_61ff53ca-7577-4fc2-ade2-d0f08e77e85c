package com.bilibili.mgk.material.center.service.mainsite.model;

import com.bilibili.mgk.material.center.service.asset.model.DynamicPublishStatus;
import java.util.Optional;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/11
 */
@RequiredArgsConstructor
@Getter
public enum DynamicFilterType {


    ALL(0, "全部"),

    AUDIT(1, "审核中"),

    PASS(2, "已通过"),

    REJECT(3, "未通过"),

    ;


    private final int code;

    private final String desc;


    public static Integer publishStatus2FilterType(Integer publishStatus) {

        return Optional.ofNullable(publishStatus).map(status -> {
            if (status == DynamicPublishStatus.audit.getCode()) {
                return AUDIT.getCode();
            } else if (status == DynamicPublishStatus.audit_pass.getCode()) {
                return PASS.getCode();
            } else if (status == DynamicPublishStatus.audit_deny.getCode()) {
                return REJECT.getCode();
            } else {
                return ALL.getCode();
            }
        }).orElse(ALL.getCode());

    }


    public static Integer filterType2PublishStatus(Integer filterType) {

        if (filterType == null) {
            return DynamicPublishStatus.reserved.getCode();
        } else if (filterType == AUDIT.getCode()) {
            return DynamicPublishStatus.audit.getCode();
        } else if (filterType == PASS.getCode()) {
            return DynamicPublishStatus.audit_pass.getCode();
        } else if (filterType == REJECT.getCode()) {
            return DynamicPublishStatus.audit_deny.getCode();
        } else {
            return DynamicPublishStatus.reserved.getCode();
        }

    }


}
