package com.bilibili.mgk.material.center.service.favorite;

import com.bilibili.mgk.material.center.service.creative.model.CreativeMaterial;
import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideo;
import com.bilibili.mgk.material.center.service.creative.model.MaterialFavorite;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdMandatory;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialFavoriteQuery;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import io.vavr.control.Try;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/12
 */
public interface MaterialFavoriteService {

    /**
     * 添加收藏
     *
     * @param accountId
     * @param material  top1CreativeId top1CreativeTitle materialType imgType auditStatus coverImgUrl avid bvid;
     */
    MaterialIdMandatory addFavorite(Long accountId, MaterialFavorite material);

    /**
     * 移除收藏
     *
     * @param accountId
     * @param materialIdMandatory
     */
    MaterialIdMandatory removeFavorite(Long accountId, MaterialIdMandatory materialIdMandatory);


    /**
     * 分页列表展示收藏
     *
     * @param query
     * @return
     */
    Pagination<List<MaterialFavorite>> page(MaterialFavoriteQuery query);


    /**
     * 判断是否被收藏
     *
     * @param accountId
     * @param materialIds
     * @return
     */
    Map<String, Boolean> isFavorite(Long accountId, List<String> materialIds);


    /**
     * 工具方法
     *
     * @param accountId
     * @param materials
     * @param favoriteKeyGetter
     * @param favoriteSetter
     * @param <T>
     */
    default <T> void fillingFavoriteStatus(Long accountId, List<T> materials,
            Function<T, String> favoriteKeyGetter,
            BiConsumer<T, Boolean> favoriteSetter) {

        Map<String, Boolean> favorites = this.isFavorite(accountId,
                materials.stream().map(favoriteKeyGetter).collect(Collectors.toList()));

        materials.forEach(material -> {
            favoriteSetter.accept(material, favorites.getOrDefault(favoriteKeyGetter.apply(material), false));
        });
    }


    String generateFavoriteKey(MaterialIdMandatory idMandatoryParams);


    default String generateFavoriteKey(CreativeMaterial material) {

        return Try.of(() -> generateFavoriteKey(new MaterialIdMandatory()
                        .setMaterialUk(
                                material.fetchMaterialUk()
                        )
                        .setMaterialType(material.getMaterialType())
                        .setImageMd5(material.getImageMd5())
                        .setBvid(material.getBvid())
                        .setAvid(material.getAvid())))
                .getOrNull();
    }


    default String generateFavoriteKey(HotBiliVideo material) {
        return Try.of(() -> generateFavoriteKey(new MaterialIdMandatory()
                        .setMaterialUk("")
                        .setMaterialType(material.getMaterialType())
                        .setBvid(material.getBvid())
                        .setAvid(material.getAvid())))
                .getOrNull();
    }


}
