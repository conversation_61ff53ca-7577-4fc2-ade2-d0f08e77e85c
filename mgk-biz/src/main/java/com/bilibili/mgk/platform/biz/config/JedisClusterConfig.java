package com.bilibili.mgk.platform.biz.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/22.
 */
@Configuration
@ComponentScan
public class JedisClusterConfig {
    @Value("${redis.cluster.nodes}")
    private String redisNodes;

    @Value("${redis.timeout}")
    private Integer timeout;
    @Value("${redis.maxIdle}")
    private Integer redisMaxIdle;

    @Value("${redis.minIdle}")
    private Integer redisMinIdle;

    @Bean(destroyMethod = "shutdown")
    RedissonClient redisson() throws IOException {
        Config config = new Config();
        config.useClusterServers()
                .setMasterConnectionPoolSize(redisMaxIdle)
                .setMasterConnectionMinimumIdleSize(redisMinIdle)
                .setConnectTimeout(timeout)
                .addNodeAddress(redisNodes.split(","));
        return Redisson.create(config);
    }
}
