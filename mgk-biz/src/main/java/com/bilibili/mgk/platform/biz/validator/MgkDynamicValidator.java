package com.bilibili.mgk.platform.biz.validator;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.passport.api.enums.ThumbUpLikeActionEnum;
import com.bilibili.mgk.platform.api.dynamic.dto.MgkDynamicLikeReqDto;
import com.bilibili.mgk.platform.api.dynamic.dto.MgkDynamicLikeStatsReqDto;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;
import reactor.core.support.Assert;

/**
 * @file: MgkDynamicValidator
 * @author: gaoming
 * @date: 2021/05/17
 * @version: 1.0
 * @description:
 **/
@Service
public class MgkDynamicValidator {
    public void validDynamicLikeReqDto(MgkDynamicLikeReqDto reqDto) {
        Assert.isTrue(Utils.isPositive(reqDto.getMid()) || !Strings.isNullOrEmpty(reqDto.getBuvid()), "mid和buvid不可为空");
        Assert.isTrue(Utils.isPositive(reqDto.getBizId()), "视频id为空或非法");
        Assert.isTrue(Utils.isPositive(reqDto.getCreativeId()), "创意id为空或非法");
        Assert.notNull(reqDto.getType(), "点赞类型不可为空");
        ThumbUpLikeActionEnum.getByCode(reqDto.getType());
    }

    public void validDynamicLikeStatsReqDto(MgkDynamicLikeStatsReqDto reqDto) {
        Assert.isTrue(Utils.isPositive(reqDto.getBizId()), "对象id不可为空");
    }
}
