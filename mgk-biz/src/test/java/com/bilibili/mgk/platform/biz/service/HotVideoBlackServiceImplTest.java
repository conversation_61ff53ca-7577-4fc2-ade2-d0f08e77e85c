package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.api.hot_video.dto.NewHotVideoBlackDto;
import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.redisson.RedissonLock;
import org.redisson.api.RedissonClient;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * @file: HotVideoBlackServiceImplTest
 * @author: gaoming
 * @date: 2020/12/26
 * @version: 1.0
 * @description:
 **/
public class HotVideoBlackServiceImplTest extends BaseMockitoTest {

    @InjectMocks
    private HotVideoBlackServiceImpl hotVideoBlackService;

    @Mock
    private HotVideoBlackServiceDelegate hotVideoBlackServiceDelegate;

    @Mock
    private RedissonClient redissonClient;
    @Mock
    private RedissonLock redissonLock;

    @Mock
    private MgkBaseService mgkBaseService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testGetBlackList() {
        hotVideoBlackService.getBlackList(1);
    }

    @Test
    public void testInsert() {
        hotVideoBlackService.insert(operator, NewHotVideoBlackDto.builder()
                .blackId(1L)
                .blackType(1)
                .bvid("1")
                .build());
    }

    @Test
    public void testEnable() {
        when(mgkBaseService.getLock(any(), any())).thenReturn(redissonLock);
        when(redissonClient.getLock(any())).thenReturn(redissonLock);
        hotVideoBlackService.enable(operator, "1", 1);
    }

    @Test
    public void testDisable() {
        when(mgkBaseService.getLock(any(), any())).thenReturn(redissonLock);
        when(redissonClient.getLock(any())).thenReturn(redissonLock);
        hotVideoBlackService.disable(operator, "1", 1);
    }

    @Test
    public void testUpdateStatusById() {
        when(mgkBaseService.getLock(any(), any())).thenReturn(redissonLock);
        when(redissonClient.getLock(any())).thenReturn(redissonLock);
        hotVideoBlackService.updateStatusById(operator, 1, 1);
    }
}