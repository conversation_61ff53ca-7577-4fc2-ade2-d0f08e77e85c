package com.bilibili.mgk.material.center.facade;

import com.bapis.datacenter.service.oneservice.OperatorVo;
import com.bapis.datacenter.service.oneservice.OsHeader;
import com.bapis.datacenter.service.oneservice.QueryReq;
import com.bapis.datacenter.service.oneservice.QueryReq.Builder;
import com.bapis.datacenter.service.oneservice.QueryResp;
import com.bilibili.mgk.material.center.facade.converter.CreativeInsightOnesDTO;
import com.bilibili.mgk.material.center.facade.proxy.OneServiceFlowControlProxy;
import com.bilibili.mgk.material.center.service.creative.model.CreativeInsight;
import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.google.common.collect.Lists;
import io.vavr.Tuple2;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 用户的创意洞察汇总 /insight/summary
 */
@Slf4j
@Service
public class CreativeInsightFacade {

    @Resource
    private OneServiceFlowControlProxy serviceOpenApiManagerBlockingStub;

    @Resource
    private ApiOneServiceBase oneServiceBase;

    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Value("${material.oneservice.insight.app-key:496b1dd505103413f42117ff1d5840ca}")
    private String appKey;

    @Value("${material.oneservice.insight.secret:tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=}")
    private String secret;

    @Value("${material.oneservice.insight.api-id:api_1647}")
    private String apiId;


    public List<CreativeInsight> query(DateAggregation time, Integer accountId, @Nullable MaterialType materialType) {

        log.info("Start to query insight time={}, account={}, material={}", time, accountId, materialType);

        String dayType = time.getDayType();

        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey(appKey)
                .setSecret(secret)
                .setApiId(apiId)
                .build();

        Tuple2<String, LocalDate> tuple = oneServiceBase.findLatestOpenApiLogDateAndDate(apiId);

        String logdate = tuple._1;
        String latestUpdateTime = dateTimeFormatter.format(tuple._2);
        Builder openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader)
                .addReqs(OperatorVo.newBuilder().setField("account_id").setOperator("=")
                        .addAllValues(Lists.newArrayList(accountId.toString())))
                .addReqs(OperatorVo.newBuilder().setField("log_date").setOperator("=")
                        .addAllValues(Lists.newArrayList(logdate)))
                .addReqs(OperatorVo.newBuilder().setField("day_type").setOperator("=")
                        .addAllValues(Lists.newArrayList(dayType)))

                .addAllOrders(Lists.newArrayList("day_type desc"));

        if (materialType != null) {
            openApiReq.addReqs(OperatorVo.newBuilder().setField("material_type").setOperator("=")
                    .addAllValues(Lists.newArrayList(materialType.isImage() ? "image" : "video")));
        }

        QueryResp rsp = serviceOpenApiManagerBlockingStub.query(openApiReq.build());

        List<CreativeInsight> result = rsp.getRowsList().stream()
                .map(map -> CreativeInsightOnesDTO.fromMapValue(map.getValueMap())
                        .toBO()
                        .setLatestUpdateTime(latestUpdateTime))
                .collect(Collectors.toList());

        return result;
    }


}
