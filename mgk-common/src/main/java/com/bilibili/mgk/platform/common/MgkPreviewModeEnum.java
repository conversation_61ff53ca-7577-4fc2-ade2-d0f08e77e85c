package com.bilibili.mgk.platform.common;

import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName MgkPreviewModeEnum
 * <AUTHOR>
 * @Date 2022/12/7 6:00 下午
 * @Version 1.0
 **/
@NoArgsConstructor
public enum MgkPreviewModeEnum {

    VIEW(0, "正常浏览"),
    MGK_PREVIEW(1, "建站预览"),
    AUDIT_PREVIEW(2, "审核预览"),
    ;
    @Getter
    private Integer code;

    @Getter
    private String name;

    MgkPreviewModeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
