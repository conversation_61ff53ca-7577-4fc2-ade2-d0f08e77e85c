package com.bilibili.mgk.platform.api.abyss;

import com.bilibili.mgk.platform.api.abyss.bo.PageConfigQueryBO;
import com.bilibili.mgk.platform.api.abyss.bo.PageConfigResBO;

public interface IMgkAbyssService {

    PageConfigResBO queryPageConfig(PageConfigQueryBO queryBO);

    /*
    *
     * @Param accountId账户id
     * @Return 是否需要添加置底按钮 0-否 1-需要添加
     */
    int needAddBottomDownloadButton(int accountId, int customer);
}
