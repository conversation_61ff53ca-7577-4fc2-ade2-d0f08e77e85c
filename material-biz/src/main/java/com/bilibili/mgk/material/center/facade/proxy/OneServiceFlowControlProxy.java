package com.bilibili.mgk.material.center.facade.proxy;

import com.bapis.datacenter.service.oneservice.OneServiceOpenApiManagerGrpc;
import com.bapis.datacenter.service.oneservice.QueryResp;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 目前的主站基建限流不支持并发数，只是非常简单的限制间隔，同时又不准备处理这个恶心bug，只能先临时的做sleep处理
 * <p>
 * TODO 可以根据api 按照访问按需sleep，这里先粗暴处理
 *
 * <AUTHOR>
 * @desc
 * @date 2024/4/3
 */
@Service
public class OneServiceFlowControlProxy {


    @Value("${material.oneservice.force-sleep-millis:20}")
    private Long forceSleepMillis;


    @Resource
    private OneServiceOpenApiManagerGrpc.OneServiceOpenApiManagerBlockingStub serviceOpenApiManagerBlockingStub;


    public QueryResp query(com.bapis.datacenter.service.oneservice.QueryReq request) {

        QueryResp r = serviceOpenApiManagerBlockingStub.query(request);

        if (forceSleepMillis > 0) {
            try {
                TimeUnit.MILLISECONDS.sleep(forceSleepMillis);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        return r;

    }

}
