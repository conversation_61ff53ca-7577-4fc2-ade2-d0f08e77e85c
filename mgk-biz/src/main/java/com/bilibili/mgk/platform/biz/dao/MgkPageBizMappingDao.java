package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkPageBizMappingPo;
import com.bilibili.mgk.platform.biz.po.MgkPageBizMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkPageBizMappingDao {
    long countByExample(MgkPageBizMappingPoExample example);

    int deleteByExample(MgkPageBizMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkPageBizMappingPo record);

    int insertBatch(List<MgkPageBizMappingPo> records);

    int insertUpdateBatch(List<MgkPageBizMappingPo> records);

    int insert(MgkPageBizMappingPo record);

    int insertUpdateSelective(MgkPageBizMappingPo record);

    int insertSelective(MgkPageBizMappingPo record);

    List<MgkPageBizMappingPo> selectByExample(MgkPageBizMappingPoExample example);

    MgkPageBizMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkPageBizMappingPo record, @Param("example") MgkPageBizMappingPoExample example);

    int updateByExample(@Param("record") MgkPageBizMappingPo record, @Param("example") MgkPageBizMappingPoExample example);

    int updateByPrimaryKeySelective(MgkPageBizMappingPo record);

    int updateByPrimaryKey(MgkPageBizMappingPo record);
}