package com.bilibili.mgk.material.center.service.course.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CourseUpdateReq extends CourseAddReq implements SnakeCaseBody {


    private Long courseId;


    public void validate() {

        Assert.notNull(courseId, "courseId is null");
        super.validate();

    }

}
