package com.bilibili.mgk.platform.common.utils;

import com.google.common.base.Throwables;
import org.springframework.util.StringUtils;

public class ExceptionUtils {

    private static final int MAX_LENGTH = 1300;

    public static String getSubStringMsg(String origin){
        if(StringUtils.isEmpty(origin) || origin.length() <= MAX_LENGTH){
           return origin;
        }
        return origin.substring(0, MAX_LENGTH - 2);
    }

    public static String getSubStringMsg(Exception e){
        if(e == null){
            return "";
        }
        String origin = Throwables.getStackTraceAsString(e);
        if(StringUtils.isEmpty(origin) || origin.length() <= MAX_LENGTH){
            return origin;
        }
        return origin.substring(0, MAX_LENGTH - 2);
    }
}
