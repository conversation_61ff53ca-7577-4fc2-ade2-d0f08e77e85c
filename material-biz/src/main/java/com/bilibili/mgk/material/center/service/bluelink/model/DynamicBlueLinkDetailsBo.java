package com.bilibili.mgk.material.center.service.bluelink.model;

import com.bilibili.mgk.material.center.service.asset.model.DynamicDrawPic;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicBlueLinkDetailsBo {


    // TODO nice-to-have pojo最好统一用装箱类
    private long id;
    private long mtime;
    private long ctime;
    private long mid;
    private int accountId;
    private int customerId;
    private int agentId;
    private int isAndroidAppDirect;
    private Integer componentType; // 一个动态
    private Integer appSubType;

    /**
     * 最终是否可见
     */
    private Integer status;

    /**
     * {@link BluelinkAuditStatus}
     */
    private Integer auditStatus;

    private List<JumpUrl> urls;

    private List<DynamicContent> dynamicContents;

    private int gameBaseId;

    private Integer gamePlatformType;

    private int iosAppPackageId;

    private int androidAppPackageId;

    private String reason;

    private List<Integer> qualificationIds;

    private int campaignId;

    private int unitId;

    private int creativeId;

    private int subPkg;

    private int clueType;

    private String clueData;

    private String customizedImpUrl;

    private String customizedClickUrl;

    private long productId;

    private List<Long> productIdSet;

    private long commentId;

    private int bizCode;

    private int contactType;

    private String dynamicId;

    private String upNickname;

    private String operatorName;


    private String dynamicTitle;

    /**
     * 保存在extra上
     */
    private List<DynamicDrawPic> dynamicPics;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JumpUrl {

        private String conversionUrlText;

        private int conversionUrlType;

        private String conversionUrlPageId;

        private String conversionUrl;

        private int iosUrlType;

        private String iosUrlPageId;

        private String iosUrl;

        private int androidUrlType;

        private String androidUrlPageId;

        private String androidUrl;

        private String iosSchemaUrl;

        private String androidSchemaUrl;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicContent {

        private String text;

        private Integer type;

        private String link;

    }
}