package com.bilibili.mgk.platform.api.landing_page.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/20 17:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatStartRespDto  implements Serializable {

    private static final long serialVersionUID = 6530901027480443469L;

    // 三方会话ID
    private String thirdChatId;
}
