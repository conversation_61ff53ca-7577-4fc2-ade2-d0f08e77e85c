package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkMaterialIdGeneratorQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkMaterialIdGenerator is a Querydsl query type for MgkMaterialIdGeneratorQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkMaterialIdGenerator extends com.querydsl.sql.RelationalPathBase<MgkMaterialIdGeneratorQueryDSLPo> {

    private static final long serialVersionUID = -158639701;

    public static final QMgkMaterialIdGenerator mgkMaterialIdGenerator = new QMgkMaterialIdGenerator("mgk_material_id_generator");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath md5 = createString("md5");

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final com.querydsl.sql.PrimaryKey<MgkMaterialIdGeneratorQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkMaterialIdGenerator(String variable) {
        super(MgkMaterialIdGeneratorQueryDSLPo.class, forVariable(variable), "null", "mgk_material_id_generator");
        addMetadata();
    }

    public QMgkMaterialIdGenerator(String variable, String schema, String table) {
        super(MgkMaterialIdGeneratorQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkMaterialIdGenerator(String variable, String schema) {
        super(MgkMaterialIdGeneratorQueryDSLPo.class, forVariable(variable), schema, "mgk_material_id_generator");
        addMetadata();
    }

    public QMgkMaterialIdGenerator(Path<? extends MgkMaterialIdGeneratorQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_material_id_generator");
        addMetadata();
    }

    public QMgkMaterialIdGenerator(PathMetadata metadata) {
        super(MgkMaterialIdGeneratorQueryDSLPo.class, metadata, "null", "mgk_material_id_generator");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(3).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(md5, ColumnMetadata.named("md5").withIndex(2).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(4).ofType(Types.TIMESTAMP).withSize(19).notNull());
    }

}

