<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="mgkSsrBfsClient" class="com.bilibili.bjcom.bfs.BFSClient" destroy-method="close">
        <constructor-arg name="host" value="${mgk.ssr.bfs.host}"/>
        <constructor-arg name="port" value="${mgk.ssr.bfs.port}"/>
        <constructor-arg name="accessKey" value="${mgk.ssr.bfs.accessKey}"/>
        <constructor-arg name="accessSecret" value="${mgk.ssr.bfs.accessSecret}"/>
        <constructor-arg name="bucketname" value="${mgk.ssr.bfs.bucketname}"/>
    </bean>

</beans>


