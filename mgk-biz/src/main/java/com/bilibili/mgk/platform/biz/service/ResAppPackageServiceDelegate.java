package com.bilibili.mgk.platform.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.app_package.dto.QueryAppPackageDto;
import com.bilibili.mgk.platform.api.app_package.dto.*;
import com.bilibili.mgk.platform.biz.ad.dao.ResAppIosPackageScreenshotDao;
import com.bilibili.mgk.platform.biz.ad.dao.ResAppPackageDao;
import com.bilibili.mgk.platform.biz.ad.po.ResAppIosPackageScreenshotPo;
import com.bilibili.mgk.platform.biz.ad.po.ResAppIosPackageScreenshotPoExample;
import com.bilibili.mgk.platform.biz.ad.po.ResAppPackagePo;
import com.bilibili.mgk.platform.biz.ad.po.ResAppPackagePoExample;
import com.bilibili.mgk.platform.biz.dao.jooq.ResAppPackageDaoService;
import com.bilibili.mgk.platform.biz.utils.FileUtil;
import com.bilibili.mgk.platform.biz.utils.HdfsFileSystemUtil;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.hadoop.fs.FileSystem;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @file: ResAppPackageServiceDelegate
 * @author: gaoming
 * @date: 2021/11/25
 * @version: 1.0
 * @description:
 **/
@Service
@Slf4j
public class ResAppPackageServiceDelegate {

    private static final int filteredPlatform = 2;

    public static final String SRC_APP_PACKAGE_ID_UPLOADING_REDIS_KEY = "mgk:appPackageId:uploading";

    public static final String JOB_RUNNING = "running";

    public static final String uploadSrcAppPackageIdEvent = "uploadSrcAppPackageId";

    @Value("${srcAppPackageIdFileName:srcAppPackageOuterId.txt}")
    private String srcAppPackageIdFileName;

    @Value("${srcAppPackageIdMetaFileName:srcAppPackageOuterId.meta}")
    private String srcAppPackageIdMetaFileName;

    @Value("${hdfsSrcAppPackageIdPath:/department/buss_product/srcAppPackage/}")
    private String hdfsSrcAppPackageIdPath;

    @Autowired
    private RedisTemplate<String, String> stringRedisTemplate;

    @Autowired
    private ResAppPackageDao resAppPackageDao;

    @Autowired
    private ResAppPackageDaoService resAppPackageDaoService;

    @Autowired
    private ResAppIosPackageScreenshotDao resAppIosPackageScreenshotDao;
    @Autowired
    private LandingPageServiceDelegate landingPageServiceDelegate;

    public ResIosAppPackageDto getIosAppPackageInfo(Integer packageId) {


        String iosAppPackageStringKey = MgkConstants.RES_APP_PACKAGE_IOS_INFO_REDIS_KEY_PREFIX + packageId;

        String iosAppPackageStringValue = stringRedisTemplate.opsForValue().get(iosAppPackageStringKey);

        if (!Strings.isNullOrEmpty(iosAppPackageStringValue)) {
            return JSONObject.parseObject(iosAppPackageStringValue, ResIosAppPackageDto.class);
        }

        ResIosAppPackageDto resIosAppPackageDto = getIosAppPackageInfoFromDB(packageId);

        if (resIosAppPackageDto != null) {
            stringRedisTemplate.opsForValue().set(iosAppPackageStringKey, JSONObject.toJSONString(resIosAppPackageDto), MgkConstants.RES_APP_PACKAGE_IOS_REDIS_EXPIRE, TimeUnit.MINUTES);
        }
        return resIosAppPackageDto;
    }

    private ResIosAppPackageDto getIosAppPackageInfoFromDB(Integer packageId) {

        List<ResAppPackagePo> resAppPackagePos = getResAppPackagePosByPackageIds(Lists.newArrayList(packageId));
        if (CollectionUtils.isEmpty(resAppPackagePos)) {
            return null;
        }
        List<ResAppIosPackageScreenshotPo> resAppIosPackageScreenshotPos = getResAppIosPackageScreenshotsByPackageIds(Lists.newArrayList(packageId));
        List<String> screenshotUrls = resAppIosPackageScreenshotPos.stream().map(ResAppIosPackageScreenshotPo::getBfsScreenshotUrl).collect(Collectors.toList());
        ResAppPackagePo resAppPackagePo = resAppPackagePos.get(0);
        return ResIosAppPackageDto.builder()
                .id(resAppPackagePo.getId())
                .appName(resAppPackagePo.getAppName())
                .iconUrl(resAppPackagePo.getIconUrl())
                .subTitle(resAppPackagePo.getSubTitle())
                .description(resAppPackagePo.getDescription())
                .screenshotUrls(screenshotUrls)
                .url(resAppPackagePo.getUrl())
                .build();
    }

    private List<ResAppIosPackageScreenshotPo> getResAppIosPackageScreenshotsByPackageIds(List<Integer> packageIds) {

        if (CollectionUtils.isEmpty(packageIds)) {
            return Collections.emptyList();
        }

        ResAppIosPackageScreenshotPoExample example = new ResAppIosPackageScreenshotPoExample();
        ResAppIosPackageScreenshotPoExample.Criteria or = example.or();
        if (packageIds.size() == 1) {
            or.andAppPackageIdEqualTo(packageIds.get(0));
        } else {
            or.andAppPackageIdIn(packageIds);
        }
        or.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return resAppIosPackageScreenshotDao.selectByExample(example);
    }

    private List<ResAppPackagePo> getResAppPackagePosByPackageIds(List<Integer> packageIds) {

        if (CollectionUtils.isEmpty(packageIds)) {
            return Collections.emptyList();
        }
        ResAppPackagePoExample example = new ResAppPackagePoExample();
        ResAppPackagePoExample.Criteria or = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (packageIds.size() == 1) {
            or.andIdEqualTo(packageIds.get(0));
        } else {
            or.andIdIn(packageIds);
        }

        List<ResAppPackagePo> pos = resAppPackageDao.selectByExampleWithBLOBs(example);
        pos.forEach(po -> po.setUrl(po.getUrl().trim()));
        return pos;
    }

    /**
     * 根据packageId将数据缓存到redis中
     *
     * @param packageId
     */
    public void refreshResIosAppPackageToRedis(Integer packageId) {
        ResIosAppPackageDto resIosAppPackageDto = getIosAppPackageInfoFromDB(packageId);

        if (resIosAppPackageDto == null) {
            return;
        }
        String iosAppPackageStringKey = MgkConstants.RES_APP_PACKAGE_IOS_INFO_REDIS_KEY_PREFIX + packageId;
        stringRedisTemplate.opsForValue().set(iosAppPackageStringKey, JSONObject.toJSONString(resIosAppPackageDto), MgkConstants.RES_APP_PACKAGE_IOS_REDIS_EXPIRE, TimeUnit.MINUTES);
    }

    public List<ResAppPackageDto> getAppPackageDtos(List<Integer> packageIds) {
        if (CollectionUtils.isEmpty(packageIds)) {
            return Collections.emptyList();
        }

        List<ResAppPackagePo> pos = getResAppPackagePosByPackageIds(packageIds);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos.stream().map(this::convertPackagePo2Dto).collect(Collectors.toList());
    }

    private List<ResAppPackageDto> convertPackagePos2Dtos(List<ResAppPackagePo> pos) {
        return pos.stream().map(this::convertPackagePo2Dto).collect(Collectors.toList());
    }

    private ResAppPackageDto convertPackagePo2Dto(ResAppPackagePo po) {
        ResAppPackageDto dto = ResAppPackageDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(po.getAuthCodeList())) {
            dto.setAuthName(Arrays.stream(po.getAuthCodeList().split(","))
                    .map(code -> landingPageServiceDelegate.getApkAuthCodeMap().get(Integer.valueOf(code)))
                    .collect(Collectors.joining(",")));
        }else {
            dto.setAuthName("");
        }
        return dto;
    }

    public List<ResAppPackageDto> queryList(QueryResAppPackageDto queryDto) {
        ResAppPackagePoExample example = this.queryToExample(queryDto);
        List<ResAppPackagePo> pos = this.resAppPackageDao.selectByExample(example);
        return this.convertPackagePos2Dtos(pos);
    }

    private ResAppPackagePoExample queryToExample(QueryResAppPackageDto queryDto) {
        ResAppPackagePoExample example = new ResAppPackagePoExample();

        ResAppPackagePoExample.Criteria c = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        ObjectUtils.setObject(queryDto::getStatus, c::andStatusEqualTo);
        ObjectUtils.setObject(queryDto::getPlatformStatus, c::andPlatformStatusEqualTo);

        ObjectUtils.setList(queryDto::getAccountIds, c::andAccountIdIn);
        ObjectUtils.setList(queryDto::getIds, c::andIdIn);
        ObjectUtils.setList(queryDto::getPlatforms, c::andPlatformIn);

        ObjectUtils.setString(queryDto::getName, c::andNameEqualTo);
        ObjectUtils.setString(queryDto::getPackageName, c::andPackageNameEqualTo);
        ObjectUtils.setString(queryDto::getUrl, c::andUrlEqualTo);
        ObjectUtils.setString(queryDto::getVersion, c::andVersionEqualTo);
        ObjectUtils.setString(queryDto::getMd5, c::andMd5EqualTo);
        ObjectUtils.setString(queryDto::getIconUrl, c::andIconUrlEqualTo);

        ObjectUtils.setString(queryDto::getOrderBy, example::setOrderByClause);
        ObjectUtils.setPage(queryDto::getPage, example::setLimit, example::setOffset);

        if (Integer.valueOf(1).equals(queryDto.getIsNewFly())) {
            c.andIsNewFlyEqualTo(1);
        } else {
            c.andIsNewFlyEqualTo(0);
        }
        // 应用游戏名，模糊查询
        if (!StringUtils.isEmpty(queryDto.getAppName())) {
            c.andAppNameLike("%" +queryDto.getAppName() + "%");
        }
        return example;
    }

    public void uploadResAppPackageId2Hdfs() {
        // check running status
        String uploadingStatus = stringRedisTemplate.opsForValue().get(SRC_APP_PACKAGE_ID_UPLOADING_REDIS_KEY);
        if (!StringUtils.isEmpty(uploadingStatus)) {
            log.error("uploadResAppPackageId is uploading");
            return;
        }
        // write running status
        stringRedisTemplate.opsForValue().set(SRC_APP_PACKAGE_ID_UPLOADING_REDIS_KEY, JOB_RUNNING, 3, TimeUnit.HOURS);

        // write file
        writeSrcAppPackageId2File(srcAppPackageIdFileName);
        // 生成meta文件
        try {
            writeHdfsMetaFile(srcAppPackageIdFileName, srcAppPackageIdMetaFileName);
        } catch (Exception e) {
            log.error("writeHdfsMetaFile error fileName {}", srcAppPackageIdFileName, e);
            Cat.logEvent(uploadSrcAppPackageIdEvent, "WriteMd5Error");
            // rm running status
            stringRedisTemplate.delete(SRC_APP_PACKAGE_ID_UPLOADING_REDIS_KEY);
            return;
        }

        // 上传hdfs
        FileSystem hdfs = HdfsFileSystemUtil.getFileSystem();
        if (hdfs == null) {
            Cat.logEvent("HDFS", "getFileSystem_Error");
            log.error("getFileSystem error");
            Cat.logEvent(uploadSrcAppPackageIdEvent, "GetFileSystemError");
            // rm running status
            stringRedisTemplate.delete(SRC_APP_PACKAGE_ID_UPLOADING_REDIS_KEY);
            return;
        }

        HdfsFileSystemUtil.writeToHDFS(hdfs, srcAppPackageIdFileName,
                hdfsSrcAppPackageIdPath + srcAppPackageIdFileName);
        HdfsFileSystemUtil.writeToHDFS(hdfs, srcAppPackageIdMetaFileName,
                hdfsSrcAppPackageIdPath + srcAppPackageIdMetaFileName);


        HdfsFileSystemUtil.closeFileSystem(hdfs);

        // rm running status
        stringRedisTemplate.delete(SRC_APP_PACKAGE_ID_UPLOADING_REDIS_KEY);
    }

    private void writeSrcAppPackageId2File(String fileName) {
        QueryRefreshInfoDto queryDto = QueryRefreshInfoDto.builder()
                .filteredDescription("")
                .filteredPlatform(filteredPlatform)
                .page(new Page())
                .build();
        FileWriter writer = null;
        BufferedWriter bufferedWriter = null;
        try {
            writer = new FileWriter(fileName, false);
            bufferedWriter = new BufferedWriter(writer);
            List<Integer> idList = getResAppPackageIdListByPage(queryDto);
            String overwriteString = idList.stream().map(String::valueOf).collect(Collectors.joining("\n"));
            bufferedWriter.write(overwriteString);
            while (!CollectionUtils.isEmpty(idList)) {
                bufferedWriter.append("\n");
                queryDto.getPage().setOffset(idList.get(idList.size() - 1));
                idList = getResAppPackageIdListByPage(queryDto);
                String appendString = idList.stream().map(String::valueOf).collect(Collectors.joining("\n"));
                bufferedWriter.append(appendString);
            }
        } catch (Exception e) {
            log.error("uploadSrcAppPackageId error");
            Cat.logEvent("UploadSrcAppPackage", "QueryError");
        } finally {
            try {
                if (bufferedWriter != null) {
                    bufferedWriter.flush();
                    bufferedWriter.close();
                }
                if (writer != null) {
                    writer.close();
                }
            } catch (Exception e) {
                log.error("close io exception:{}", e.getMessage());
                Cat.logEvent("UploadSrcAppPackage close io exception", "QueryError");
            }
        }
    }

    private List<Integer> getResAppPackageIdListByPage(QueryRefreshInfoDto queryDto) {
        return resAppPackageDaoService.getResAppPackageIdListByPage(queryDto);
    }

    private void writeHdfsMetaFile(String originFileName, String metaFileName) throws IOException {
        String md5 = FileUtil.getFileMd5(originFileName);
        Assert.notNull(md5, originFileName + " 文件md5生成失败");

        // write meta file
        FileMetaDto meta = FileMetaDto.builder()
                .checksum(md5)
                .encryptType("MD5")
                .filePath(originFileName)
                .build();
        FileUtil.writeFile(metaFileName, JSON.toJSONString(meta));
    }

    /**
     * 根据路径获取hdfs文件
     *
     * @param hdfsFilePath hdfs文件路径
     * @return {@link File}
     */
    private File getHdfsFileByPath(String hdfsFilePath) {
        Assert.notNull(hdfsFilePath, "文件路径不可为空");

        String fileName = FilenameUtils.getName(hdfsFilePath);
        FileSystem fs = null;
        try {
            fs = HdfsFileSystemUtil.getFileSystem();
            Assert.notNull(fs, "fileSystem获取失败");

            boolean result = HdfsFileSystemUtil.readFromHDFS(fs, hdfsFilePath, fileName);
            if (!result) {
                log.error("getHdfsFileByPath error filePath {}", hdfsFilePath);
                return null;
            }
        } finally {
            if (fs != null) {
                HdfsFileSystemUtil.closeFileSystem(fs);
            }
        }

        return new File(fileName);
    }

    private String getFileContentSizeAndDataExample() throws IOException {
        String hdfsPath = hdfsSrcAppPackageIdPath + srcAppPackageIdFileName;
        File file = getHdfsFileByPath(hdfsPath);
        Assert.notNull(file, "文件不存在");
        FileReader fileReader = new FileReader(file);
        BufferedReader br = new BufferedReader(fileReader);
        StringBuilder str = new StringBuilder();
        str.append(br.readLine());
        String line = null;
        int count = 1;
        while((line = br.readLine()) != null){
            count ++;
            str.append(",");
            str.append(line);
        }
        return str.toString() + "\n" + "total num:" + count;
    }
}
