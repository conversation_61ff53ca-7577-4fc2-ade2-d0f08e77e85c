package com.bilibili.mgk.platform.portal.openapi.ad_complain;

import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.mgk.platform.api.ad_complain.service.IAdComplainService;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebAdComplainService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.mock.web.MockMultipartFile;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年10月11日
 */
public class OpenAdComplainControllerTest extends MockitoDefaultTest {

    @Mock
    private WebAdComplainService webAdComplainService;

    @Mock
    private IAdComplainService adComplainService;

    @InjectMocks
    private OpenAdComplainController openAdComplainController;

    @Test
    public void upload() throws Exception {
        Response<String> response = openAdComplainController.upload(
                new MockMultipartFile("name", "123.jpg", "image/jpg", new byte[0]));
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getResult());
    }

}