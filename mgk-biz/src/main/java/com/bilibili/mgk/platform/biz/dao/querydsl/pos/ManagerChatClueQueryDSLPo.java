package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * ManagerChatClueQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class ManagerChatClueQueryDSLPo {

    private Long avid;

    private java.sql.Timestamp clueCtime;

    private String cover;

    private java.sql.Timestamp ctime;

    private Long customerServiceUid;

    private String extra;

    private Long id;

    private String ip;

    private Integer isDeleted;

    private Integer isPushed;

    private Long mid;

    private String midName;

    private String msgKey;

    private java.sql.Timestamp mtime;

    private String phoneNo;

    private String title;

    private Integer toolType;

    private Long uid;

    public Long getAvid() {
        return avid;
    }

    public void setAvid(Long avid) {
        this.avid = avid;
    }

    public java.sql.Timestamp getClueCtime() {
        return clueCtime;
    }

    public void setClueCtime(java.sql.Timestamp clueCtime) {
        this.clueCtime = clueCtime;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getCustomerServiceUid() {
        return customerServiceUid;
    }

    public void setCustomerServiceUid(Long customerServiceUid) {
        this.customerServiceUid = customerServiceUid;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsPushed() {
        return isPushed;
    }

    public void setIsPushed(Integer isPushed) {
        this.isPushed = isPushed;
    }

    public Long getMid() {
        return mid;
    }

    public void setMid(Long mid) {
        this.mid = mid;
    }

    public String getMidName() {
        return midName;
    }

    public void setMidName(String midName) {
        this.midName = midName;
    }

    public String getMsgKey() {
        return msgKey;
    }

    public void setMsgKey(String msgKey) {
        this.msgKey = msgKey;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getToolType() {
        return toolType;
    }

    public void setToolType(Integer toolType) {
        this.toolType = toolType;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    @Override
    public String toString() {
         return "avid = " + avid + ", clueCtime = " + clueCtime + ", cover = " + cover + ", ctime = " + ctime + ", customerServiceUid = " + customerServiceUid + ", extra = " + extra + ", id = " + id + ", ip = " + ip + ", isDeleted = " + isDeleted + ", isPushed = " + isPushed + ", mid = " + mid + ", midName = " + midName + ", msgKey = " + msgKey + ", mtime = " + mtime + ", phoneNo = " + phoneNo + ", title = " + title + ", toolType = " + toolType + ", uid = " + uid;
    }

}

