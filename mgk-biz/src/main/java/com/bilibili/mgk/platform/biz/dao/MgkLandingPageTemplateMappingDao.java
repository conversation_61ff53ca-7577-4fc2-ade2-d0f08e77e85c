package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkLandingPageTemplateMappingPo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageTemplateMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkLandingPageTemplateMappingDao {
    long countByExample(MgkLandingPageTemplateMappingPoExample example);

    int deleteByExample(MgkLandingPageTemplateMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkLandingPageTemplateMappingPo record);

    int insertBatch(List<MgkLandingPageTemplateMappingPo> records);

    int insertUpdateBatch(List<MgkLandingPageTemplateMappingPo> records);

    int insert(MgkLandingPageTemplateMappingPo record);

    int insertUpdateSelective(MgkLandingPageTemplateMappingPo record);

    int insertSelective(MgkLandingPageTemplateMappingPo record);

    List<MgkLandingPageTemplateMappingPo> selectByExample(MgkLandingPageTemplateMappingPoExample example);

    MgkLandingPageTemplateMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkLandingPageTemplateMappingPo record, @Param("example") MgkLandingPageTemplateMappingPoExample example);

    int updateByExample(@Param("record") MgkLandingPageTemplateMappingPo record, @Param("example") MgkLandingPageTemplateMappingPoExample example);

    int updateByPrimaryKeySelective(MgkLandingPageTemplateMappingPo record);

    int updateByPrimaryKey(MgkLandingPageTemplateMappingPo record);
}