package com.bilibili.mgk.platform.biz.utils;

import com.bilibili.mgk.platform.common.MgkEnvironmentEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class EnvironmentUtil {

    @Value("${mgk.environment:1}")
    private Integer mgkEnvironment;

    public boolean isProd(){
        return !MgkEnvironmentEnum.TEST.getCode().equals(mgkEnvironment)
                && !MgkEnvironmentEnum.UAT.getCode().equals(mgkEnvironment);
    }
}
