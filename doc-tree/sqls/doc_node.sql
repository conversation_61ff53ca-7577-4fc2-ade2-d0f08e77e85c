CREATE TABLE `mgk_doc_node`
(
    `node_id`       BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',

    -- Node原生属性
    `node_name`     VARCHAR(255) NOT NULL DEFAULT '' COMMENT '当前节点名',
    `node_type` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '节点类型，一版用于保存是目录文档类型等',
    `parent_id`     BIGINT(20)   NOT NULL DEFAULT 0 COMMENT '父节点ID，根节点时为0',
    `parent_name`   VARCHAR(255)          DEFAULT '' COMMENT '父节点名，根节点时为空',
    `root_id`       BIGINT(20)   NOT NULL COMMENT '根节点ID',
    `root_name`     VARCHAR(255) NOT NULL DEFAULT '' COMMENT '根节点名称',
    `path`          VARCHAR(500) NOT NULL DEFAULT '' COMMENT '从根到该节点的路径',
    `depth`         INT(11)      NOT NULL DEFAULT 0 COMMENT '节点深度',
    `has_child`     TINYINT(4)   NOT NULL DEFAULT 0 COMMENT '是否有子节点，0表示无，1表示有',

    -- Doc附带属性
    `has_doc`       TINYINT(4)   NOT NULL DEFAULT 0 COMMENT '是否挂载文档，0表示无，1表示有',
    `doc_type`  VARCHAR(100)          DEFAULT '' COMMENT '文档类型',
    `doc_title`     VARCHAR(255)          DEFAULT '' COMMENT '文档标题（目录和文档共用）',
    `doc_summary`   VARCHAR(5000)         DEFAULT '' COMMENT '文档摘要',

    -- 业务附带属性
    `author`        VARCHAR(100)          DEFAULT '' COMMENT '作者名称',
    `author_id`     VARCHAR(100)          DEFAULT '' COMMENT '作者ID',
    `ctime`         DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，排序键',
    `mtime`         DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间，排序键',
    `sort_priority` INT(11)      NOT NULL DEFAULT 0 COMMENT '排序优先级，越小越靠前',
    `is_deleted`    TINYINT(4)   NOT NULL DEFAULT 0 COMMENT '是否删除，0表示未删除，1表示已删除',
    `is_show`       TINYINT(4)   NOT NULL DEFAULT 1 COMMENT '是否展示，0表示不展示，1表示展示',

    -- 其他业务字段
    `biz_extra`     TEXT COMMENT '业务额外字段，例如节点logo或封面',

    PRIMARY KEY (`node_id`),
    KEY `idx_root_id_depth` (`root_id`, `depth`) USING BTREE,
    KEY `idx_parent_id` (`parent_id`) USING BTREE,
    KEY `idx_path` (`path`) USING BTREE,
    KEY `idx_priority` (`sort_priority`) USING BTREE,
    KEY `idx_mtime` (`mtime`) USING BTREE,
    KEY `idx_ctime` (`ctime`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '文档树平铺节点表';



ALTER table `mgk_doc_node`
    ADD COLUMN `node_type` VARCHAR(20) DEFAULT '' COMMENT '节点类型，一版用于保存是目录文档类型等';