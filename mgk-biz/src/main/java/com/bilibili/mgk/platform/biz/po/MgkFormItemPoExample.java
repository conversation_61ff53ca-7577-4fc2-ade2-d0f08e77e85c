package com.bilibili.mgk.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MgkFormItemPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MgkFormItemPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdIsNull() {
            addCriterion("form_item_id is null");
            return (Criteria) this;
        }

        public Criteria andFormItemIdIsNotNull() {
            addCriterion("form_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormItemIdEqualTo(Long value) {
            addCriterion("form_item_id =", value, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdNotEqualTo(Long value) {
            addCriterion("form_item_id <>", value, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdGreaterThan(Long value) {
            addCriterion("form_item_id >", value, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_item_id >=", value, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdLessThan(Long value) {
            addCriterion("form_item_id <", value, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdLessThanOrEqualTo(Long value) {
            addCriterion("form_item_id <=", value, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdIn(List<Long> values) {
            addCriterion("form_item_id in", values, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdNotIn(List<Long> values) {
            addCriterion("form_item_id not in", values, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdBetween(Long value1, Long value2) {
            addCriterion("form_item_id between", value1, value2, "formItemId");
            return (Criteria) this;
        }

        public Criteria andFormItemIdNotBetween(Long value1, Long value2) {
            addCriterion("form_item_id not between", value1, value2, "formItemId");
            return (Criteria) this;
        }

        public Criteria andLabelIsNull() {
            addCriterion("label is null");
            return (Criteria) this;
        }

        public Criteria andLabelIsNotNull() {
            addCriterion("label is not null");
            return (Criteria) this;
        }

        public Criteria andLabelEqualTo(String value) {
            addCriterion("label =", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotEqualTo(String value) {
            addCriterion("label <>", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThan(String value) {
            addCriterion("label >", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThanOrEqualTo(String value) {
            addCriterion("label >=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThan(String value) {
            addCriterion("label <", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThanOrEqualTo(String value) {
            addCriterion("label <=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLike(String value) {
            addCriterion("label like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotLike(String value) {
            addCriterion("label not like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelIn(List<String> values) {
            addCriterion("label in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotIn(List<String> values) {
            addCriterion("label not in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelBetween(String value1, String value2) {
            addCriterion("label between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotBetween(String value1, String value2) {
            addCriterion("label not between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andSortNumberIsNull() {
            addCriterion("sort_number is null");
            return (Criteria) this;
        }

        public Criteria andSortNumberIsNotNull() {
            addCriterion("sort_number is not null");
            return (Criteria) this;
        }

        public Criteria andSortNumberEqualTo(Integer value) {
            addCriterion("sort_number =", value, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberNotEqualTo(Integer value) {
            addCriterion("sort_number <>", value, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberGreaterThan(Integer value) {
            addCriterion("sort_number >", value, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort_number >=", value, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberLessThan(Integer value) {
            addCriterion("sort_number <", value, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberLessThanOrEqualTo(Integer value) {
            addCriterion("sort_number <=", value, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberIn(List<Integer> values) {
            addCriterion("sort_number in", values, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberNotIn(List<Integer> values) {
            addCriterion("sort_number not in", values, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberBetween(Integer value1, Integer value2) {
            addCriterion("sort_number between", value1, value2, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andSortNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("sort_number not between", value1, value2, "sortNumber");
            return (Criteria) this;
        }

        public Criteria andOptionsValIsNull() {
            addCriterion("options_val is null");
            return (Criteria) this;
        }

        public Criteria andOptionsValIsNotNull() {
            addCriterion("options_val is not null");
            return (Criteria) this;
        }

        public Criteria andOptionsValEqualTo(String value) {
            addCriterion("options_val =", value, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValNotEqualTo(String value) {
            addCriterion("options_val <>", value, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValGreaterThan(String value) {
            addCriterion("options_val >", value, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValGreaterThanOrEqualTo(String value) {
            addCriterion("options_val >=", value, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValLessThan(String value) {
            addCriterion("options_val <", value, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValLessThanOrEqualTo(String value) {
            addCriterion("options_val <=", value, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValLike(String value) {
            addCriterion("options_val like", value, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValNotLike(String value) {
            addCriterion("options_val not like", value, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValIn(List<String> values) {
            addCriterion("options_val in", values, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValNotIn(List<String> values) {
            addCriterion("options_val not in", values, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValBetween(String value1, String value2) {
            addCriterion("options_val between", value1, value2, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andOptionsValNotBetween(String value1, String value2) {
            addCriterion("options_val not between", value1, value2, "optionsVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValIsNull() {
            addCriterion("radio_checkbox_val is null");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValIsNotNull() {
            addCriterion("radio_checkbox_val is not null");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValEqualTo(String value) {
            addCriterion("radio_checkbox_val =", value, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValNotEqualTo(String value) {
            addCriterion("radio_checkbox_val <>", value, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValGreaterThan(String value) {
            addCriterion("radio_checkbox_val >", value, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValGreaterThanOrEqualTo(String value) {
            addCriterion("radio_checkbox_val >=", value, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValLessThan(String value) {
            addCriterion("radio_checkbox_val <", value, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValLessThanOrEqualTo(String value) {
            addCriterion("radio_checkbox_val <=", value, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValLike(String value) {
            addCriterion("radio_checkbox_val like", value, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValNotLike(String value) {
            addCriterion("radio_checkbox_val not like", value, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValIn(List<String> values) {
            addCriterion("radio_checkbox_val in", values, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValNotIn(List<String> values) {
            addCriterion("radio_checkbox_val not in", values, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValBetween(String value1, String value2) {
            addCriterion("radio_checkbox_val between", value1, value2, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andRadioCheckboxValNotBetween(String value1, String value2) {
            addCriterion("radio_checkbox_val not between", value1, value2, "radioCheckboxVal");
            return (Criteria) this;
        }

        public Criteria andMinAddressIsNull() {
            addCriterion("min_address is null");
            return (Criteria) this;
        }

        public Criteria andMinAddressIsNotNull() {
            addCriterion("min_address is not null");
            return (Criteria) this;
        }

        public Criteria andMinAddressEqualTo(Integer value) {
            addCriterion("min_address =", value, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressNotEqualTo(Integer value) {
            addCriterion("min_address <>", value, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressGreaterThan(Integer value) {
            addCriterion("min_address >", value, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressGreaterThanOrEqualTo(Integer value) {
            addCriterion("min_address >=", value, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressLessThan(Integer value) {
            addCriterion("min_address <", value, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressLessThanOrEqualTo(Integer value) {
            addCriterion("min_address <=", value, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressIn(List<Integer> values) {
            addCriterion("min_address in", values, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressNotIn(List<Integer> values) {
            addCriterion("min_address not in", values, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressBetween(Integer value1, Integer value2) {
            addCriterion("min_address between", value1, value2, "minAddress");
            return (Criteria) this;
        }

        public Criteria andMinAddressNotBetween(Integer value1, Integer value2) {
            addCriterion("min_address not between", value1, value2, "minAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressIsNull() {
            addCriterion("is_detail_address is null");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressIsNotNull() {
            addCriterion("is_detail_address is not null");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressEqualTo(Integer value) {
            addCriterion("is_detail_address =", value, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressNotEqualTo(Integer value) {
            addCriterion("is_detail_address <>", value, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressGreaterThan(Integer value) {
            addCriterion("is_detail_address >", value, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_detail_address >=", value, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressLessThan(Integer value) {
            addCriterion("is_detail_address <", value, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressLessThanOrEqualTo(Integer value) {
            addCriterion("is_detail_address <=", value, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressIn(List<Integer> values) {
            addCriterion("is_detail_address in", values, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressNotIn(List<Integer> values) {
            addCriterion("is_detail_address not in", values, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressBetween(Integer value1, Integer value2) {
            addCriterion("is_detail_address between", value1, value2, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsDetailAddressNotBetween(Integer value1, Integer value2) {
            addCriterion("is_detail_address not between", value1, value2, "isDetailAddress");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyIsNull() {
            addCriterion("is_allow_empty is null");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyIsNotNull() {
            addCriterion("is_allow_empty is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyEqualTo(Integer value) {
            addCriterion("is_allow_empty =", value, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyNotEqualTo(Integer value) {
            addCriterion("is_allow_empty <>", value, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyGreaterThan(Integer value) {
            addCriterion("is_allow_empty >", value, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_allow_empty >=", value, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyLessThan(Integer value) {
            addCriterion("is_allow_empty <", value, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyLessThanOrEqualTo(Integer value) {
            addCriterion("is_allow_empty <=", value, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyIn(List<Integer> values) {
            addCriterion("is_allow_empty in", values, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyNotIn(List<Integer> values) {
            addCriterion("is_allow_empty not in", values, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyBetween(Integer value1, Integer value2) {
            addCriterion("is_allow_empty between", value1, value2, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsAllowEmptyNotBetween(Integer value1, Integer value2) {
            addCriterion("is_allow_empty not between", value1, value2, "isAllowEmpty");
            return (Criteria) this;
        }

        public Criteria andIsUniqueIsNull() {
            addCriterion("is_unique is null");
            return (Criteria) this;
        }

        public Criteria andIsUniqueIsNotNull() {
            addCriterion("is_unique is not null");
            return (Criteria) this;
        }

        public Criteria andIsUniqueEqualTo(Integer value) {
            addCriterion("is_unique =", value, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueNotEqualTo(Integer value) {
            addCriterion("is_unique <>", value, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueGreaterThan(Integer value) {
            addCriterion("is_unique >", value, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_unique >=", value, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueLessThan(Integer value) {
            addCriterion("is_unique <", value, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueLessThanOrEqualTo(Integer value) {
            addCriterion("is_unique <=", value, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueIn(List<Integer> values) {
            addCriterion("is_unique in", values, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueNotIn(List<Integer> values) {
            addCriterion("is_unique not in", values, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueBetween(Integer value1, Integer value2) {
            addCriterion("is_unique between", value1, value2, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsUniqueNotBetween(Integer value1, Integer value2) {
            addCriterion("is_unique not between", value1, value2, "isUnique");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateIsNull() {
            addCriterion("is_submit_validate is null");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateIsNotNull() {
            addCriterion("is_submit_validate is not null");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateEqualTo(Integer value) {
            addCriterion("is_submit_validate =", value, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateNotEqualTo(Integer value) {
            addCriterion("is_submit_validate <>", value, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateGreaterThan(Integer value) {
            addCriterion("is_submit_validate >", value, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_submit_validate >=", value, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateLessThan(Integer value) {
            addCriterion("is_submit_validate <", value, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateLessThanOrEqualTo(Integer value) {
            addCriterion("is_submit_validate <=", value, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateIn(List<Integer> values) {
            addCriterion("is_submit_validate in", values, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateNotIn(List<Integer> values) {
            addCriterion("is_submit_validate not in", values, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateBetween(Integer value1, Integer value2) {
            addCriterion("is_submit_validate between", value1, value2, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andIsSubmitValidateNotBetween(Integer value1, Integer value2) {
            addCriterion("is_submit_validate not between", value1, value2, "isSubmitValidate");
            return (Criteria) this;
        }

        public Criteria andRequiredIsNull() {
            addCriterion("required is null");
            return (Criteria) this;
        }

        public Criteria andRequiredIsNotNull() {
            addCriterion("required is not null");
            return (Criteria) this;
        }

        public Criteria andRequiredEqualTo(Integer value) {
            addCriterion("required =", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotEqualTo(Integer value) {
            addCriterion("required <>", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredGreaterThan(Integer value) {
            addCriterion("required >", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredGreaterThanOrEqualTo(Integer value) {
            addCriterion("required >=", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredLessThan(Integer value) {
            addCriterion("required <", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredLessThanOrEqualTo(Integer value) {
            addCriterion("required <=", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredIn(List<Integer> values) {
            addCriterion("required in", values, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotIn(List<Integer> values) {
            addCriterion("required not in", values, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredBetween(Integer value1, Integer value2) {
            addCriterion("required between", value1, value2, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotBetween(Integer value1, Integer value2) {
            addCriterion("required not between", value1, value2, "required");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIsNull() {
            addCriterion("placeholder is null");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIsNotNull() {
            addCriterion("placeholder is not null");
            return (Criteria) this;
        }

        public Criteria andPlaceholderEqualTo(String value) {
            addCriterion("placeholder =", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotEqualTo(String value) {
            addCriterion("placeholder <>", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderGreaterThan(String value) {
            addCriterion("placeholder >", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderGreaterThanOrEqualTo(String value) {
            addCriterion("placeholder >=", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLessThan(String value) {
            addCriterion("placeholder <", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLessThanOrEqualTo(String value) {
            addCriterion("placeholder <=", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLike(String value) {
            addCriterion("placeholder like", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotLike(String value) {
            addCriterion("placeholder not like", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIn(List<String> values) {
            addCriterion("placeholder in", values, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotIn(List<String> values) {
            addCriterion("placeholder not in", values, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderBetween(String value1, String value2) {
            addCriterion("placeholder between", value1, value2, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotBetween(String value1, String value2) {
            addCriterion("placeholder not between", value1, value2, "placeholder");
            return (Criteria) this;
        }

        public Criteria andItemDefaultIsNull() {
            addCriterion("item_default is null");
            return (Criteria) this;
        }

        public Criteria andItemDefaultIsNotNull() {
            addCriterion("item_default is not null");
            return (Criteria) this;
        }

        public Criteria andItemDefaultEqualTo(String value) {
            addCriterion("item_default =", value, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultNotEqualTo(String value) {
            addCriterion("item_default <>", value, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultGreaterThan(String value) {
            addCriterion("item_default >", value, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultGreaterThanOrEqualTo(String value) {
            addCriterion("item_default >=", value, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultLessThan(String value) {
            addCriterion("item_default <", value, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultLessThanOrEqualTo(String value) {
            addCriterion("item_default <=", value, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultLike(String value) {
            addCriterion("item_default like", value, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultNotLike(String value) {
            addCriterion("item_default not like", value, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultIn(List<String> values) {
            addCriterion("item_default in", values, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultNotIn(List<String> values) {
            addCriterion("item_default not in", values, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultBetween(String value1, String value2) {
            addCriterion("item_default between", value1, value2, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andItemDefaultNotBetween(String value1, String value2) {
            addCriterion("item_default not between", value1, value2, "itemDefault");
            return (Criteria) this;
        }

        public Criteria andMaxLengthIsNull() {
            addCriterion("max_length is null");
            return (Criteria) this;
        }

        public Criteria andMaxLengthIsNotNull() {
            addCriterion("max_length is not null");
            return (Criteria) this;
        }

        public Criteria andMaxLengthEqualTo(Integer value) {
            addCriterion("max_length =", value, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthNotEqualTo(Integer value) {
            addCriterion("max_length <>", value, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthGreaterThan(Integer value) {
            addCriterion("max_length >", value, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_length >=", value, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthLessThan(Integer value) {
            addCriterion("max_length <", value, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthLessThanOrEqualTo(Integer value) {
            addCriterion("max_length <=", value, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthIn(List<Integer> values) {
            addCriterion("max_length in", values, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthNotIn(List<Integer> values) {
            addCriterion("max_length not in", values, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthBetween(Integer value1, Integer value2) {
            addCriterion("max_length between", value1, value2, "maxLength");
            return (Criteria) this;
        }

        public Criteria andMaxLengthNotBetween(Integer value1, Integer value2) {
            addCriterion("max_length not between", value1, value2, "maxLength");
            return (Criteria) this;
        }

        public Criteria andPrefixIsNull() {
            addCriterion("prefix is null");
            return (Criteria) this;
        }

        public Criteria andPrefixIsNotNull() {
            addCriterion("prefix is not null");
            return (Criteria) this;
        }

        public Criteria andPrefixEqualTo(String value) {
            addCriterion("prefix =", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixNotEqualTo(String value) {
            addCriterion("prefix <>", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixGreaterThan(String value) {
            addCriterion("prefix >", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixGreaterThanOrEqualTo(String value) {
            addCriterion("prefix >=", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixLessThan(String value) {
            addCriterion("prefix <", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixLessThanOrEqualTo(String value) {
            addCriterion("prefix <=", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixLike(String value) {
            addCriterion("prefix like", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixNotLike(String value) {
            addCriterion("prefix not like", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixIn(List<String> values) {
            addCriterion("prefix in", values, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixNotIn(List<String> values) {
            addCriterion("prefix not in", values, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixBetween(String value1, String value2) {
            addCriterion("prefix between", value1, value2, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixNotBetween(String value1, String value2) {
            addCriterion("prefix not between", value1, value2, "prefix");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkIsNull() {
            addCriterion("auto_fill_link is null");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkIsNotNull() {
            addCriterion("auto_fill_link is not null");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkEqualTo(String value) {
            addCriterion("auto_fill_link =", value, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkNotEqualTo(String value) {
            addCriterion("auto_fill_link <>", value, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkGreaterThan(String value) {
            addCriterion("auto_fill_link >", value, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkGreaterThanOrEqualTo(String value) {
            addCriterion("auto_fill_link >=", value, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkLessThan(String value) {
            addCriterion("auto_fill_link <", value, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkLessThanOrEqualTo(String value) {
            addCriterion("auto_fill_link <=", value, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkLike(String value) {
            addCriterion("auto_fill_link like", value, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkNotLike(String value) {
            addCriterion("auto_fill_link not like", value, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkIn(List<String> values) {
            addCriterion("auto_fill_link in", values, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkNotIn(List<String> values) {
            addCriterion("auto_fill_link not in", values, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkBetween(String value1, String value2) {
            addCriterion("auto_fill_link between", value1, value2, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillLinkNotBetween(String value1, String value2) {
            addCriterion("auto_fill_link not between", value1, value2, "autoFillLink");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextIsNull() {
            addCriterion("auto_fill_text is null");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextIsNotNull() {
            addCriterion("auto_fill_text is not null");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextEqualTo(String value) {
            addCriterion("auto_fill_text =", value, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextNotEqualTo(String value) {
            addCriterion("auto_fill_text <>", value, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextGreaterThan(String value) {
            addCriterion("auto_fill_text >", value, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextGreaterThanOrEqualTo(String value) {
            addCriterion("auto_fill_text >=", value, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextLessThan(String value) {
            addCriterion("auto_fill_text <", value, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextLessThanOrEqualTo(String value) {
            addCriterion("auto_fill_text <=", value, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextLike(String value) {
            addCriterion("auto_fill_text like", value, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextNotLike(String value) {
            addCriterion("auto_fill_text not like", value, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextIn(List<String> values) {
            addCriterion("auto_fill_text in", values, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextNotIn(List<String> values) {
            addCriterion("auto_fill_text not in", values, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextBetween(String value1, String value2) {
            addCriterion("auto_fill_text between", value1, value2, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andAutoFillTextNotBetween(String value1, String value2) {
            addCriterion("auto_fill_text not between", value1, value2, "autoFillText");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillIsNull() {
            addCriterion("show_auto_fill is null");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillIsNotNull() {
            addCriterion("show_auto_fill is not null");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillEqualTo(Integer value) {
            addCriterion("show_auto_fill =", value, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillNotEqualTo(Integer value) {
            addCriterion("show_auto_fill <>", value, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillGreaterThan(Integer value) {
            addCriterion("show_auto_fill >", value, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillGreaterThanOrEqualTo(Integer value) {
            addCriterion("show_auto_fill >=", value, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillLessThan(Integer value) {
            addCriterion("show_auto_fill <", value, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillLessThanOrEqualTo(Integer value) {
            addCriterion("show_auto_fill <=", value, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillIn(List<Integer> values) {
            addCriterion("show_auto_fill in", values, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillNotIn(List<Integer> values) {
            addCriterion("show_auto_fill not in", values, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillBetween(Integer value1, Integer value2) {
            addCriterion("show_auto_fill between", value1, value2, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andShowAutoFillNotBetween(Integer value1, Integer value2) {
            addCriterion("show_auto_fill not between", value1, value2, "showAutoFill");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeIsNull() {
            addCriterion("keyboard_type is null");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeIsNotNull() {
            addCriterion("keyboard_type is not null");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeEqualTo(Integer value) {
            addCriterion("keyboard_type =", value, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeNotEqualTo(Integer value) {
            addCriterion("keyboard_type <>", value, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeGreaterThan(Integer value) {
            addCriterion("keyboard_type >", value, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("keyboard_type >=", value, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeLessThan(Integer value) {
            addCriterion("keyboard_type <", value, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeLessThanOrEqualTo(Integer value) {
            addCriterion("keyboard_type <=", value, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeIn(List<Integer> values) {
            addCriterion("keyboard_type in", values, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeNotIn(List<Integer> values) {
            addCriterion("keyboard_type not in", values, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeBetween(Integer value1, Integer value2) {
            addCriterion("keyboard_type between", value1, value2, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andKeyboardTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("keyboard_type not between", value1, value2, "keyboardType");
            return (Criteria) this;
        }

        public Criteria andSubTypeIsNull() {
            addCriterion("sub_type is null");
            return (Criteria) this;
        }

        public Criteria andSubTypeIsNotNull() {
            addCriterion("sub_type is not null");
            return (Criteria) this;
        }

        public Criteria andSubTypeEqualTo(Integer value) {
            addCriterion("sub_type =", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotEqualTo(Integer value) {
            addCriterion("sub_type <>", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeGreaterThan(Integer value) {
            addCriterion("sub_type >", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sub_type >=", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeLessThan(Integer value) {
            addCriterion("sub_type <", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sub_type <=", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeIn(List<Integer> values) {
            addCriterion("sub_type in", values, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotIn(List<Integer> values) {
            addCriterion("sub_type not in", values, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeBetween(Integer value1, Integer value2) {
            addCriterion("sub_type between", value1, value2, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sub_type not between", value1, value2, "subType");
            return (Criteria) this;
        }

        public Criteria andShowRuleIsNull() {
            addCriterion("show_rule is null");
            return (Criteria) this;
        }

        public Criteria andShowRuleIsNotNull() {
            addCriterion("show_rule is not null");
            return (Criteria) this;
        }

        public Criteria andShowRuleEqualTo(Integer value) {
            addCriterion("show_rule =", value, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleNotEqualTo(Integer value) {
            addCriterion("show_rule <>", value, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleGreaterThan(Integer value) {
            addCriterion("show_rule >", value, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleGreaterThanOrEqualTo(Integer value) {
            addCriterion("show_rule >=", value, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleLessThan(Integer value) {
            addCriterion("show_rule <", value, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleLessThanOrEqualTo(Integer value) {
            addCriterion("show_rule <=", value, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleIn(List<Integer> values) {
            addCriterion("show_rule in", values, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleNotIn(List<Integer> values) {
            addCriterion("show_rule not in", values, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleBetween(Integer value1, Integer value2) {
            addCriterion("show_rule between", value1, value2, "showRule");
            return (Criteria) this;
        }

        public Criteria andShowRuleNotBetween(Integer value1, Integer value2) {
            addCriterion("show_rule not between", value1, value2, "showRule");
            return (Criteria) this;
        }

        public Criteria andRangeLeftIsNull() {
            addCriterion("range_left is null");
            return (Criteria) this;
        }

        public Criteria andRangeLeftIsNotNull() {
            addCriterion("range_left is not null");
            return (Criteria) this;
        }

        public Criteria andRangeLeftEqualTo(Integer value) {
            addCriterion("range_left =", value, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftNotEqualTo(Integer value) {
            addCriterion("range_left <>", value, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftGreaterThan(Integer value) {
            addCriterion("range_left >", value, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftGreaterThanOrEqualTo(Integer value) {
            addCriterion("range_left >=", value, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftLessThan(Integer value) {
            addCriterion("range_left <", value, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftLessThanOrEqualTo(Integer value) {
            addCriterion("range_left <=", value, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftIn(List<Integer> values) {
            addCriterion("range_left in", values, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftNotIn(List<Integer> values) {
            addCriterion("range_left not in", values, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftBetween(Integer value1, Integer value2) {
            addCriterion("range_left between", value1, value2, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeLeftNotBetween(Integer value1, Integer value2) {
            addCriterion("range_left not between", value1, value2, "rangeLeft");
            return (Criteria) this;
        }

        public Criteria andRangeRightIsNull() {
            addCriterion("range_right is null");
            return (Criteria) this;
        }

        public Criteria andRangeRightIsNotNull() {
            addCriterion("range_right is not null");
            return (Criteria) this;
        }

        public Criteria andRangeRightEqualTo(Integer value) {
            addCriterion("range_right =", value, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightNotEqualTo(Integer value) {
            addCriterion("range_right <>", value, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightGreaterThan(Integer value) {
            addCriterion("range_right >", value, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightGreaterThanOrEqualTo(Integer value) {
            addCriterion("range_right >=", value, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightLessThan(Integer value) {
            addCriterion("range_right <", value, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightLessThanOrEqualTo(Integer value) {
            addCriterion("range_right <=", value, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightIn(List<Integer> values) {
            addCriterion("range_right in", values, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightNotIn(List<Integer> values) {
            addCriterion("range_right not in", values, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightBetween(Integer value1, Integer value2) {
            addCriterion("range_right between", value1, value2, "rangeRight");
            return (Criteria) this;
        }

        public Criteria andRangeRightNotBetween(Integer value1, Integer value2) {
            addCriterion("range_right not between", value1, value2, "rangeRight");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}