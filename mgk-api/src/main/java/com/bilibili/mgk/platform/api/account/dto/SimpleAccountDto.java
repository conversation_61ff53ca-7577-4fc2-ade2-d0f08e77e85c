package com.bilibili.mgk.platform.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/09/09
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimpleAccountDto implements Serializable {

    private Integer accountId;

    private String username;

    private static final long serialVersionUID = 1L;
}
