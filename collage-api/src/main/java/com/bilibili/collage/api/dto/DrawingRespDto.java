package com.bilibili.collage.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/12/12
 * 智能作图返回结果对象
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DrawingRespDto implements Serializable {

    private static final long serialVersionUID = -2602229549202661055L;

    // 本次作图的唯一标识 该标记可用来获取结果集（下个结果集或者轮询）
    private String requestId;

    // 结果集总数
    private Integer totalCount;

    // 页码
    private Integer page;

    // 图片URL 带上id
    private List<DrawingRenderResultDto> pics;
}
