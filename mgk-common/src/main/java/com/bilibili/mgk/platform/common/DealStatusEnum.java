package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 */

@AllArgsConstructor
public enum DealStatusEnum {

    UNTREATED(0, "未处理"),

    FINISHED(1, "已完成"),

    PROCESSING(2, "处理中"),

    FAILED(3, "处理失败"),
    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static DealStatusEnum getByCode(int code) {
        for (DealStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code DealStatusEnum " + code);
    }

}
