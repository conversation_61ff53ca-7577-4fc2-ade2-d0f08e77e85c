package com.bilibili.mgk.platform.api.landing_page_group.dto;

import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingLogDto;
import com.bilibili.mgk.platform.common.MgkDatabaseColumnName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName LandingPageGroupLogDto
 * <AUTHOR>
 * @Date 2023/5/31 3:41 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupLogDiffDto {

    @MgkDatabaseColumnName("落地页组名称")
    private String name;

    @MgkDatabaseColumnName("落地页组")
    private List<LandingPageGroupMappingLogDto> mappingList;

    @MgkDatabaseColumnName("状态")
    private String status;

}
