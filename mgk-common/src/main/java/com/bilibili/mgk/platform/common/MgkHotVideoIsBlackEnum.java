package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotVideoIsBlack
 * @author: gaoming
 * @date: 2020/12/23
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
public enum MgkHotVideoIsBlackEnum {
    NORMAL(0, "正常"),
    BLACK(1, "黑名单");

    @Getter
    private Integer code;

    private String desc;

    public static MgkHotVideoIsBlackEnum getByCode(int code) {
        for (MgkHotVideoIsBlackEnum bean : values()) {
            if (bean.getCode() == code) return bean;
        }
        throw new IllegalArgumentException("unknown code MgkHotVideoIsBlackEnum " + code);
    }

}
