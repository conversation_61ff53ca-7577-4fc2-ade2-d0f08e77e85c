package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkCvrDealFailPo;
import com.bilibili.mgk.platform.biz.po.MgkCvrDealFailPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkCvrDealFailDao {
    long countByExample(MgkCvrDealFailPoExample example);

    int deleteByExample(MgkCvrDealFailPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkCvrDealFailPo record);

    int insertBatch(List<MgkCvrDealFailPo> records);

    int insertUpdateBatch(List<MgkCvrDealFailPo> records);

    int insert(MgkCvrDealFailPo record);

    int insertUpdateSelective(MgkCvrDealFailPo record);

    int insertSelective(MgkCvrDealFailPo record);

    List<MgkCvrDealFailPo> selectByExample(MgkCvrDealFailPoExample example);

    MgkCvrDealFailPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkCvrDealFailPo record, @Param("example") MgkCvrDealFailPoExample example);

    int updateByExample(@Param("record") MgkCvrDealFailPo record, @Param("example") MgkCvrDealFailPoExample example);

    int updateByPrimaryKeySelective(MgkCvrDealFailPo record);

    int updateByPrimaryKey(MgkCvrDealFailPo record);
}