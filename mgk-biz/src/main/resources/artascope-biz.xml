<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <context:component-scan base-package="com.bilibili"/>

    <bean id="snowflakeIdWorker" scope="singleton" class="com.bilibili.adp.common.util.SnowflakeIdWorker">
        <constructor-arg index="0" value="1"/>
    </bean>

    <aop:aspectj-autoproxy proxy-target-class="true" expose-proxy="true"/>

    <import resource="classpath:bfs.xml"/>
    <import resource="classpath:httpinvoker.xml"/>
    <import resource="classpath:artascope-database-config.xml"/>
    <import resource="classpath:mgk-redis-config.xml"/>
    <import resource="classpath:collage-biz.xml"/>
    <import resource="ad-database-config.xml"/>
    <import resource="account-database-config.xml"/>
    <import resource="dmp-database-config.xml"/>
</beans>


