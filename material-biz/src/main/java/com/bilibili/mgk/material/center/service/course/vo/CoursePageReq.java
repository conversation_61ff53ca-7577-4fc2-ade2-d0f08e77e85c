package com.bilibili.mgk.material.center.service.course.vo;

import com.bilibili.mgk.material.center.service.course.model.CourseDocType;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.biz.common.doc.tree.service.vo.NodeSortType;
import io.swagger.annotations.ApiModelProperty;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.util.Optional;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */
@Data
public class CoursePageReq implements SnakeCaseBody {

    @ApiModelProperty("访问者账号id，无需前端填写，如有登录态会自动填充")
    private Long accountId;

    /**
     * 如果为空表示所有的课程，
     */
    private Long firstDirectoryId;

    /**
     * 当二级目录指定时，其实以及目录id已经不重要了， 当二级目标为空是， 表示查询一级目录下的所有课程
     */
    private Long secondDirectoryId;


    @ApiModelProperty("排序方式枚举， pubtime (降序)或priority (升序) . 默认pubtime")
    private MaterialSortBy sortBy;


    private Integer pn;


    private Integer ps;


    @ApiModelProperty("课程类型, 不传表示所有类型， video表示视频课程， doc表示文档课程")
    private CourseDocType courseType;


    @ApiModelProperty("是否回表查询文档内存，如果文档存在时 可选，默认false")
    private Boolean retrieveDoc;


    @ApiModelProperty("是否显示，如果不传表示所有，true表示显示，false表示不显示， 针对B端则只允许查看可见的课程")
    private Boolean isShow;


    // 明确表示不支持多选查询，不然不符合目录设计
    public Tuple2<Long, Integer> fetchTargetIdAndDepth(Long rootId) {

        if (secondDirectoryId != null) {
            return Tuple.of(secondDirectoryId, 1);
        }

        if (firstDirectoryId != null) {
            return Tuple.of(firstDirectoryId, 2);
        } else {
            return Tuple.of(rootId, 3);
        }
    }


    public NodeSortType fetchDocTreeSortType() {

        return Optional.ofNullable(this.getSortBy())
                .map(sortBy -> Try.of(() -> {
                    switch (sortBy) {
                        case pubtime: {
                            return NodeSortType.ctime_desc;
                        }
                        case priority: {
                            return NodeSortType.priority_asc;
                        }
                        default: {
                            return NodeSortType.ctime_desc;
                        }
                    }
                }).getOrNull())
                .orElse(NodeSortType.ctime_desc);
    }


    public void validate() {

        // TODO 是否要检查first second 是否跨树？
    }


}
