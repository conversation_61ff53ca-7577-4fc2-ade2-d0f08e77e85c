package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/09/11
 **/
@AllArgsConstructor
public enum IsSynchro {
    NO_SYNCHRO(0, "未同步"),
    TES_SYNCHRO(1, "同步");
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static IsSynchro getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("未知发送状态");
        }
        for (IsSynchro bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code IsSynchro " + code);
    }
}
