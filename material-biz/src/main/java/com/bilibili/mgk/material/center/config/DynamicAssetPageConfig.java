package com.bilibili.mgk.material.center.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/25
 */
@Data
@Configuration
public class DynamicAssetPageConfig {


    // 1. 为filing  2.为filterOut，默认filling
    @Value("${material.dynamic.commerce.audit.status.render-type:false}")
    private Boolean filterOutAuditingDynamicInListV1;


    /**
     * true 表示不足页时就认为没有更多了， false 要求则是返回为空才认为没有更多了
     */
    @Value("${material.dynamic.waterfall-page.not-full-as-no-more:true}")
    private Boolean notFullAsNoMore;

    @Value("${material.dynamic.waterfall-page.combine-next-dataset:true}")
    private Boolean combineNextDataset;

    @Value("${material.dynamic.waterfall-page.default-page-size:20}")
    private Integer defaultWaterfallPageSize;


}
