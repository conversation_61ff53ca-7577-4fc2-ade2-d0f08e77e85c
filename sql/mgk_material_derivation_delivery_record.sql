CREATE TABLE `mgk_material_ai_derivation_delivery_record`
(
    `id`                   bigint(20)          NOT NULL AUTO_INCREMENT COMMENT 'ID',

    `account_id`           bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '账户Id',

    `creative_id`          bigint(20)          NOT NULL DEFAULT '0' COMMENT '创意Id',

    `unit_id`              bigint(20)          NOT NULL DEFAULT '0' COMMENT '单元Id',
    `campaign_id`          bigint(20)          NOT NULL DEFAULT '0' COMMENT '计划Id',
    `version`              bigint(20)          NOT NULL DEFAULT '0' COMMENT '用户Id',
    `creative_name`        varchar(150)                 default '' not null comment '图片md5',
    `unit_name`            varchar(150)                 default '' not null comment '图片md5',
    `campaign_name`        varchar(150)                 default '' not null comment '图片md5',

    `origin_img_md5`       varchar(100)                 default '' not null comment '图片md5',
    `origin_img_url`       varchar(300)                 default '' not null comment '图片url',

# 衍生信息
    `derivated_imgs`       varchar(2000)                default '' not null comment '衍生图json map结构{md5:url} ',
    `derivated_extra_info` varchar(2000)                default '' not null comment 'map接口',

# 投放信息

    `deliver_creative_id`  bigint(20)          NOT NULL DEFAULT '0' COMMENT '创意Id',
    `deliver_unit_id`      bigint(20)          NOT NULL DEFAULT '0' COMMENT '单元Id',
    `deliver_campaign_id`  bigint(20)          NOT NULL DEFAULT '0' COMMENT '计划Id',
    `deliver_img_md5s`     varchar(1000)       NOT NULL DEFAULT '' COMMENT '投放图片信息',
    `deliver_img_urls`     varchar(2000)       NOT NULL DEFAULT '' COMMENT '投放图片信息',

    `deliver_time`         datetime            NOT NULL DEFAULT current_timestamp() COMMENT '投放时间',


# meta

    `ctime`                datetime            NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`                datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`              tinyint(4)          NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',


    PRIMARY KEY (`id`),
    KEY `idx_account` (`account_id`) USING BTREE,
    KEY `idx_creative` (`creative_id`) USING BTREE,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='AI衍生图投放记录';



ALTER table `mgk_material_ai_derivation_delivery_record`
    ADD COLUMN `ad_title` varchar(150) default '' not null comment '广告标题';