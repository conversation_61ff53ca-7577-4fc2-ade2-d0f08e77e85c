package com.bilibili.mgk.platform.portal.webapi.doctree;

import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.core.Response;
import com.biz.common.doc.tree.common.Pagination;
import com.biz.common.doc.tree.model.DocFlattenNode;
import com.biz.common.doc.tree.model.DocNodeFavorite;
import com.biz.common.doc.tree.service.DocNodeFavoriteService;
import com.biz.common.doc.tree.service.vo.FavoriteNodePageReq;
import com.biz.common.doc.tree.service.vo.NodeFavoriteAddReq;
import com.biz.common.doc.tree.service.vo.NodeFavoriteRemoveReq;
import io.swagger.annotations.Api;
import java.util.List;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/13
 */
@RestController
@FreeLogin
@Api(value = "/doc/tree", tags = "文档树")
@RequestMapping("/web_api/v1/doc/tree/favorite")
@RequiredArgsConstructor
public class DocTreeFavoriteController extends BasicController {

    @Resource
    private DocNodeFavoriteService docNodeFavoriteService;


    @FreeLogin
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Response<DocNodeFavorite> add(
            @RequestBody NodeFavoriteAddReq req) {
        return Response.SUCCESS(docNodeFavoriteService.add(req));
    }


    @FreeLogin
    @RequestMapping(value = "/remove", method = RequestMethod.POST)
    public Response<DocNodeFavorite> remove(
            @RequestBody NodeFavoriteRemoveReq req) {
        return Response.SUCCESS(docNodeFavoriteService.remove(req));
    }


    @FreeLogin
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public Response<Pagination<List<DocFlattenNode>>> pageFavoriteNodes(
            @RequestBody FavoriteNodePageReq req) {
        return Response.SUCCESS(docNodeFavoriteService.pageFavoriteNodes(req));
    }

}
