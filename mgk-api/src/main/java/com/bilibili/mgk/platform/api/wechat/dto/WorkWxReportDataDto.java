package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WorkWxReportDataDto implements Serializable {

    private static final long serialVersionUID = -4514281360115216618L;

    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * requestId
     */
    private String requestId;

    /**
     * trackId
     */
    private String trackId;

    /**
     * 转化时间,单位毫秒
     */
    private Long ts;

    private String convType;


}
