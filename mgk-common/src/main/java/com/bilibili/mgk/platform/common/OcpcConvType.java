
package com.bilibili.mgk.platform.common;

import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 转换类型枚举。
 *
 * <AUTHOR>
 * @date 2019年1月11日
 */
@Getter
public enum OcpcConvType {
    APP_FIRST_ACTIVE("APP_FIRST_ACTIVE"),
    APP_ACTIVE("APP_ACTIVE"),
    USER_REGISTER("USER_REGISTER"),
    ORDER_PLACE("ORDER_PLACE"),
    USER_COST("USER_COST"),
    DOWNLOAD_SUCCESS("DOWNLOAD_SUCCESS"),
    INSTALL_SUCCESS("INSTALL_SUCCESS"),
    FORM_SUBMIT("FORM_SUBMIT"),
    PAID_IN_24H_ROI("PAID_IN_24H_ROI"),
    PAID_IN_7D_COST("PAID_IN_7D_COST"),
    WECHAT_COPY("WX_COPY"),
    //微信开口
    WX_CHAT("WX_CHAT"),
    //微信涨粉
    WX_ADD_FANS("WX_ADD_FANS"),
    //号经营直播讲解卡表单提交
    LIVE_FORM_SUBMIT("LIVE_FORM_SUBMIT")

    ;

    public static final List<String> ALL_VALUES = Stream.of(OcpcConvType.values())
            .map(OcpcConvType::getValue).collect(Collectors.toList());

    private String value;

    OcpcConvType(String value) {
        this.value = value;
    }
}
