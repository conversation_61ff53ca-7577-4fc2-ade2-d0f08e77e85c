<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.ad.dao.AccAccountWalletDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="cash" jdbcType="BIGINT" property="cash" />
    <result column="red_packet" jdbcType="BIGINT" property="redPacket" />
    <result column="special_red_packet" jdbcType="BIGINT" property="specialRedPacket" />
    <result column="total_cash_recharge" jdbcType="BIGINT" property="totalCashRecharge" />
    <result column="total_cash_consume" jdbcType="BIGINT" property="totalCashConsume" />
    <result column="total_red_packet_recharge" jdbcType="BIGINT" property="totalRedPacketRecharge" />
    <result column="total_red_packet_consume" jdbcType="BIGINT" property="totalRedPacketConsume" />
    <result column="total_special_red_packet_recharge" jdbcType="BIGINT" property="totalSpecialRedPacketRecharge" />
    <result column="total_special_red_packet_consume" jdbcType="BIGINT" property="totalSpecialRedPacketConsume" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="trust_fund" jdbcType="BIGINT" property="trustFund" />
    <result column="withhold_fund" jdbcType="BIGINT" property="withholdFund" />
    <result column="trust_cash" jdbcType="BIGINT" property="trustCash" />
    <result column="trust_incentive" jdbcType="BIGINT" property="trustIncentive" />
    <result column="trust_fly_coin" jdbcType="BIGINT" property="trustFlyCoin" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPo">
    <id column="acc_account_wallet_id" jdbcType="INTEGER" property="id" />
    <result column="acc_account_wallet_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="acc_account_wallet_cash" jdbcType="BIGINT" property="cash" />
    <result column="acc_account_wallet_red_packet" jdbcType="BIGINT" property="redPacket" />
    <result column="acc_account_wallet_special_red_packet" jdbcType="BIGINT" property="specialRedPacket" />
    <result column="acc_account_wallet_total_cash_recharge" jdbcType="BIGINT" property="totalCashRecharge" />
    <result column="acc_account_wallet_total_cash_consume" jdbcType="BIGINT" property="totalCashConsume" />
    <result column="acc_account_wallet_total_red_packet_recharge" jdbcType="BIGINT" property="totalRedPacketRecharge" />
    <result column="acc_account_wallet_total_red_packet_consume" jdbcType="BIGINT" property="totalRedPacketConsume" />
    <result column="acc_account_wallet_total_special_red_packet_recharge" jdbcType="BIGINT" property="totalSpecialRedPacketRecharge" />
    <result column="acc_account_wallet_total_special_red_packet_consume" jdbcType="BIGINT" property="totalSpecialRedPacketConsume" />
    <result column="acc_account_wallet_version" jdbcType="INTEGER" property="version" />
    <result column="acc_account_wallet_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="acc_account_wallet_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="acc_account_wallet_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="acc_account_wallet_trust_fund" jdbcType="BIGINT" property="trustFund" />
    <result column="acc_account_wallet_withhold_fund" jdbcType="BIGINT" property="withholdFund" />
    <result column="acc_account_wallet_trust_cash" jdbcType="BIGINT" property="trustCash" />
    <result column="acc_account_wallet_trust_incentive" jdbcType="BIGINT" property="trustIncentive" />
    <result column="acc_account_wallet_trust_fly_coin" jdbcType="BIGINT" property="trustFlyCoin" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as acc_account_wallet_id, ${alias}.account_id as acc_account_wallet_account_id, 
    ${alias}.cash as acc_account_wallet_cash, ${alias}.red_packet as acc_account_wallet_red_packet, 
    ${alias}.special_red_packet as acc_account_wallet_special_red_packet, ${alias}.total_cash_recharge as acc_account_wallet_total_cash_recharge, 
    ${alias}.total_cash_consume as acc_account_wallet_total_cash_consume, ${alias}.total_red_packet_recharge as acc_account_wallet_total_red_packet_recharge, 
    ${alias}.total_red_packet_consume as acc_account_wallet_total_red_packet_consume, 
    ${alias}.total_special_red_packet_recharge as acc_account_wallet_total_special_red_packet_recharge, 
    ${alias}.total_special_red_packet_consume as acc_account_wallet_total_special_red_packet_consume, 
    ${alias}.version as acc_account_wallet_version, ${alias}.ctime as acc_account_wallet_ctime, 
    ${alias}.mtime as acc_account_wallet_mtime, ${alias}.is_deleted as acc_account_wallet_is_deleted, 
    ${alias}.trust_fund as acc_account_wallet_trust_fund, ${alias}.withhold_fund as acc_account_wallet_withhold_fund, 
    ${alias}.trust_cash as acc_account_wallet_trust_cash, ${alias}.trust_incentive as acc_account_wallet_trust_incentive, 
    ${alias}.trust_fly_coin as acc_account_wallet_trust_fly_coin
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, cash, red_packet, special_red_packet, total_cash_recharge, total_cash_consume, 
    total_red_packet_recharge, total_red_packet_consume, total_special_red_packet_recharge, 
    total_special_red_packet_consume, version, ctime, mtime, is_deleted, trust_fund, 
    withhold_fund, trust_cash, trust_incentive, trust_fly_coin
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from acc_account_wallet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from acc_account_wallet
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from acc_account_wallet
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPoExample">
    delete from acc_account_wallet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_account_wallet (account_id, cash, red_packet, 
      special_red_packet, total_cash_recharge, total_cash_consume, 
      total_red_packet_recharge, total_red_packet_consume, 
      total_special_red_packet_recharge, total_special_red_packet_consume, 
      version, ctime, mtime, 
      is_deleted, trust_fund, withhold_fund, 
      trust_cash, trust_incentive, trust_fly_coin
      )
    values (#{accountId,jdbcType=INTEGER}, #{cash,jdbcType=BIGINT}, #{redPacket,jdbcType=BIGINT}, 
      #{specialRedPacket,jdbcType=BIGINT}, #{totalCashRecharge,jdbcType=BIGINT}, #{totalCashConsume,jdbcType=BIGINT}, 
      #{totalRedPacketRecharge,jdbcType=BIGINT}, #{totalRedPacketConsume,jdbcType=BIGINT}, 
      #{totalSpecialRedPacketRecharge,jdbcType=BIGINT}, #{totalSpecialRedPacketConsume,jdbcType=BIGINT}, 
      #{version,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{trustFund,jdbcType=BIGINT}, #{withholdFund,jdbcType=BIGINT}, 
      #{trustCash,jdbcType=BIGINT}, #{trustIncentive,jdbcType=BIGINT}, #{trustFlyCoin,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_account_wallet
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="cash != null">
        cash,
      </if>
      <if test="redPacket != null">
        red_packet,
      </if>
      <if test="specialRedPacket != null">
        special_red_packet,
      </if>
      <if test="totalCashRecharge != null">
        total_cash_recharge,
      </if>
      <if test="totalCashConsume != null">
        total_cash_consume,
      </if>
      <if test="totalRedPacketRecharge != null">
        total_red_packet_recharge,
      </if>
      <if test="totalRedPacketConsume != null">
        total_red_packet_consume,
      </if>
      <if test="totalSpecialRedPacketRecharge != null">
        total_special_red_packet_recharge,
      </if>
      <if test="totalSpecialRedPacketConsume != null">
        total_special_red_packet_consume,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="trustFund != null">
        trust_fund,
      </if>
      <if test="withholdFund != null">
        withhold_fund,
      </if>
      <if test="trustCash != null">
        trust_cash,
      </if>
      <if test="trustIncentive != null">
        trust_incentive,
      </if>
      <if test="trustFlyCoin != null">
        trust_fly_coin,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="cash != null">
        #{cash,jdbcType=BIGINT},
      </if>
      <if test="redPacket != null">
        #{redPacket,jdbcType=BIGINT},
      </if>
      <if test="specialRedPacket != null">
        #{specialRedPacket,jdbcType=BIGINT},
      </if>
      <if test="totalCashRecharge != null">
        #{totalCashRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalCashConsume != null">
        #{totalCashConsume,jdbcType=BIGINT},
      </if>
      <if test="totalRedPacketRecharge != null">
        #{totalRedPacketRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalRedPacketConsume != null">
        #{totalRedPacketConsume,jdbcType=BIGINT},
      </if>
      <if test="totalSpecialRedPacketRecharge != null">
        #{totalSpecialRedPacketRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalSpecialRedPacketConsume != null">
        #{totalSpecialRedPacketConsume,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="trustFund != null">
        #{trustFund,jdbcType=BIGINT},
      </if>
      <if test="withholdFund != null">
        #{withholdFund,jdbcType=BIGINT},
      </if>
      <if test="trustCash != null">
        #{trustCash,jdbcType=BIGINT},
      </if>
      <if test="trustIncentive != null">
        #{trustIncentive,jdbcType=BIGINT},
      </if>
      <if test="trustFlyCoin != null">
        #{trustFlyCoin,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPoExample" resultType="java.lang.Long">
    select count(*) from acc_account_wallet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update acc_account_wallet
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.cash != null">
        cash = #{record.cash,jdbcType=BIGINT},
      </if>
      <if test="record.redPacket != null">
        red_packet = #{record.redPacket,jdbcType=BIGINT},
      </if>
      <if test="record.specialRedPacket != null">
        special_red_packet = #{record.specialRedPacket,jdbcType=BIGINT},
      </if>
      <if test="record.totalCashRecharge != null">
        total_cash_recharge = #{record.totalCashRecharge,jdbcType=BIGINT},
      </if>
      <if test="record.totalCashConsume != null">
        total_cash_consume = #{record.totalCashConsume,jdbcType=BIGINT},
      </if>
      <if test="record.totalRedPacketRecharge != null">
        total_red_packet_recharge = #{record.totalRedPacketRecharge,jdbcType=BIGINT},
      </if>
      <if test="record.totalRedPacketConsume != null">
        total_red_packet_consume = #{record.totalRedPacketConsume,jdbcType=BIGINT},
      </if>
      <if test="record.totalSpecialRedPacketRecharge != null">
        total_special_red_packet_recharge = #{record.totalSpecialRedPacketRecharge,jdbcType=BIGINT},
      </if>
      <if test="record.totalSpecialRedPacketConsume != null">
        total_special_red_packet_consume = #{record.totalSpecialRedPacketConsume,jdbcType=BIGINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.trustFund != null">
        trust_fund = #{record.trustFund,jdbcType=BIGINT},
      </if>
      <if test="record.withholdFund != null">
        withhold_fund = #{record.withholdFund,jdbcType=BIGINT},
      </if>
      <if test="record.trustCash != null">
        trust_cash = #{record.trustCash,jdbcType=BIGINT},
      </if>
      <if test="record.trustIncentive != null">
        trust_incentive = #{record.trustIncentive,jdbcType=BIGINT},
      </if>
      <if test="record.trustFlyCoin != null">
        trust_fly_coin = #{record.trustFlyCoin,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update acc_account_wallet
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      cash = #{record.cash,jdbcType=BIGINT},
      red_packet = #{record.redPacket,jdbcType=BIGINT},
      special_red_packet = #{record.specialRedPacket,jdbcType=BIGINT},
      total_cash_recharge = #{record.totalCashRecharge,jdbcType=BIGINT},
      total_cash_consume = #{record.totalCashConsume,jdbcType=BIGINT},
      total_red_packet_recharge = #{record.totalRedPacketRecharge,jdbcType=BIGINT},
      total_red_packet_consume = #{record.totalRedPacketConsume,jdbcType=BIGINT},
      total_special_red_packet_recharge = #{record.totalSpecialRedPacketRecharge,jdbcType=BIGINT},
      total_special_red_packet_consume = #{record.totalSpecialRedPacketConsume,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      trust_fund = #{record.trustFund,jdbcType=BIGINT},
      withhold_fund = #{record.withholdFund,jdbcType=BIGINT},
      trust_cash = #{record.trustCash,jdbcType=BIGINT},
      trust_incentive = #{record.trustIncentive,jdbcType=BIGINT},
      trust_fly_coin = #{record.trustFlyCoin,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPo">
    update acc_account_wallet
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="cash != null">
        cash = #{cash,jdbcType=BIGINT},
      </if>
      <if test="redPacket != null">
        red_packet = #{redPacket,jdbcType=BIGINT},
      </if>
      <if test="specialRedPacket != null">
        special_red_packet = #{specialRedPacket,jdbcType=BIGINT},
      </if>
      <if test="totalCashRecharge != null">
        total_cash_recharge = #{totalCashRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalCashConsume != null">
        total_cash_consume = #{totalCashConsume,jdbcType=BIGINT},
      </if>
      <if test="totalRedPacketRecharge != null">
        total_red_packet_recharge = #{totalRedPacketRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalRedPacketConsume != null">
        total_red_packet_consume = #{totalRedPacketConsume,jdbcType=BIGINT},
      </if>
      <if test="totalSpecialRedPacketRecharge != null">
        total_special_red_packet_recharge = #{totalSpecialRedPacketRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalSpecialRedPacketConsume != null">
        total_special_red_packet_consume = #{totalSpecialRedPacketConsume,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="trustFund != null">
        trust_fund = #{trustFund,jdbcType=BIGINT},
      </if>
      <if test="withholdFund != null">
        withhold_fund = #{withholdFund,jdbcType=BIGINT},
      </if>
      <if test="trustCash != null">
        trust_cash = #{trustCash,jdbcType=BIGINT},
      </if>
      <if test="trustIncentive != null">
        trust_incentive = #{trustIncentive,jdbcType=BIGINT},
      </if>
      <if test="trustFlyCoin != null">
        trust_fly_coin = #{trustFlyCoin,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPo">
    update acc_account_wallet
    set account_id = #{accountId,jdbcType=INTEGER},
      cash = #{cash,jdbcType=BIGINT},
      red_packet = #{redPacket,jdbcType=BIGINT},
      special_red_packet = #{specialRedPacket,jdbcType=BIGINT},
      total_cash_recharge = #{totalCashRecharge,jdbcType=BIGINT},
      total_cash_consume = #{totalCashConsume,jdbcType=BIGINT},
      total_red_packet_recharge = #{totalRedPacketRecharge,jdbcType=BIGINT},
      total_red_packet_consume = #{totalRedPacketConsume,jdbcType=BIGINT},
      total_special_red_packet_recharge = #{totalSpecialRedPacketRecharge,jdbcType=BIGINT},
      total_special_red_packet_consume = #{totalSpecialRedPacketConsume,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      trust_fund = #{trustFund,jdbcType=BIGINT},
      withhold_fund = #{withholdFund,jdbcType=BIGINT},
      trust_cash = #{trustCash,jdbcType=BIGINT},
      trust_incentive = #{trustIncentive,jdbcType=BIGINT},
      trust_fly_coin = #{trustFlyCoin,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_account_wallet (account_id, cash, red_packet, 
      special_red_packet, total_cash_recharge, total_cash_consume, 
      total_red_packet_recharge, total_red_packet_consume, 
      total_special_red_packet_recharge, total_special_red_packet_consume, 
      version, ctime, mtime, 
      is_deleted, trust_fund, withhold_fund, 
      trust_cash, trust_incentive, trust_fly_coin
      )
    values (#{accountId,jdbcType=INTEGER}, #{cash,jdbcType=BIGINT}, #{redPacket,jdbcType=BIGINT}, 
      #{specialRedPacket,jdbcType=BIGINT}, #{totalCashRecharge,jdbcType=BIGINT}, #{totalCashConsume,jdbcType=BIGINT}, 
      #{totalRedPacketRecharge,jdbcType=BIGINT}, #{totalRedPacketConsume,jdbcType=BIGINT}, 
      #{totalSpecialRedPacketRecharge,jdbcType=BIGINT}, #{totalSpecialRedPacketConsume,jdbcType=BIGINT}, 
      #{version,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{trustFund,jdbcType=BIGINT}, #{withholdFund,jdbcType=BIGINT}, 
      #{trustCash,jdbcType=BIGINT}, #{trustIncentive,jdbcType=BIGINT}, #{trustFlyCoin,jdbcType=BIGINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      cash = values(cash),
      red_packet = values(red_packet),
      special_red_packet = values(special_red_packet),
      total_cash_recharge = values(total_cash_recharge),
      total_cash_consume = values(total_cash_consume),
      total_red_packet_recharge = values(total_red_packet_recharge),
      total_red_packet_consume = values(total_red_packet_consume),
      total_special_red_packet_recharge = values(total_special_red_packet_recharge),
      total_special_red_packet_consume = values(total_special_red_packet_consume),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      trust_fund = values(trust_fund),
      withhold_fund = values(withhold_fund),
      trust_cash = values(trust_cash),
      trust_incentive = values(trust_incentive),
      trust_fly_coin = values(trust_fly_coin),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      acc_account_wallet
      (account_id,cash,red_packet,special_red_packet,total_cash_recharge,total_cash_consume,total_red_packet_recharge,total_red_packet_consume,total_special_red_packet_recharge,total_special_red_packet_consume,version,ctime,mtime,is_deleted,trust_fund,withhold_fund,trust_cash,trust_incentive,trust_fly_coin)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.cash,jdbcType=BIGINT},
        #{item.redPacket,jdbcType=BIGINT},
        #{item.specialRedPacket,jdbcType=BIGINT},
        #{item.totalCashRecharge,jdbcType=BIGINT},
        #{item.totalCashConsume,jdbcType=BIGINT},
        #{item.totalRedPacketRecharge,jdbcType=BIGINT},
        #{item.totalRedPacketConsume,jdbcType=BIGINT},
        #{item.totalSpecialRedPacketRecharge,jdbcType=BIGINT},
        #{item.totalSpecialRedPacketConsume,jdbcType=BIGINT},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.trustFund,jdbcType=BIGINT},
        #{item.withholdFund,jdbcType=BIGINT},
        #{item.trustCash,jdbcType=BIGINT},
        #{item.trustIncentive,jdbcType=BIGINT},
        #{item.trustFlyCoin,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      acc_account_wallet
      (account_id,cash,red_packet,special_red_packet,total_cash_recharge,total_cash_consume,total_red_packet_recharge,total_red_packet_consume,total_special_red_packet_recharge,total_special_red_packet_consume,version,ctime,mtime,is_deleted,trust_fund,withhold_fund,trust_cash,trust_incentive,trust_fly_coin)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.cash,jdbcType=BIGINT},
        #{item.redPacket,jdbcType=BIGINT},
        #{item.specialRedPacket,jdbcType=BIGINT},
        #{item.totalCashRecharge,jdbcType=BIGINT},
        #{item.totalCashConsume,jdbcType=BIGINT},
        #{item.totalRedPacketRecharge,jdbcType=BIGINT},
        #{item.totalRedPacketConsume,jdbcType=BIGINT},
        #{item.totalSpecialRedPacketRecharge,jdbcType=BIGINT},
        #{item.totalSpecialRedPacketConsume,jdbcType=BIGINT},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.trustFund,jdbcType=BIGINT},
        #{item.withholdFund,jdbcType=BIGINT},
        #{item.trustCash,jdbcType=BIGINT},
        #{item.trustIncentive,jdbcType=BIGINT},
        #{item.trustFlyCoin,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      cash = values(cash),
      red_packet = values(red_packet),
      special_red_packet = values(special_red_packet),
      total_cash_recharge = values(total_cash_recharge),
      total_cash_consume = values(total_cash_consume),
      total_red_packet_recharge = values(total_red_packet_recharge),
      total_red_packet_consume = values(total_red_packet_consume),
      total_special_red_packet_recharge = values(total_special_red_packet_recharge),
      total_special_red_packet_consume = values(total_special_red_packet_consume),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      trust_fund = values(trust_fund),
      withhold_fund = values(withhold_fund),
      trust_cash = values(trust_cash),
      trust_incentive = values(trust_incentive),
      trust_fly_coin = values(trust_fly_coin),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.AccAccountWalletPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_account_wallet
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="cash != null">
        cash,
      </if>
      <if test="redPacket != null">
        red_packet,
      </if>
      <if test="specialRedPacket != null">
        special_red_packet,
      </if>
      <if test="totalCashRecharge != null">
        total_cash_recharge,
      </if>
      <if test="totalCashConsume != null">
        total_cash_consume,
      </if>
      <if test="totalRedPacketRecharge != null">
        total_red_packet_recharge,
      </if>
      <if test="totalRedPacketConsume != null">
        total_red_packet_consume,
      </if>
      <if test="totalSpecialRedPacketRecharge != null">
        total_special_red_packet_recharge,
      </if>
      <if test="totalSpecialRedPacketConsume != null">
        total_special_red_packet_consume,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="trustFund != null">
        trust_fund,
      </if>
      <if test="withholdFund != null">
        withhold_fund,
      </if>
      <if test="trustCash != null">
        trust_cash,
      </if>
      <if test="trustIncentive != null">
        trust_incentive,
      </if>
      <if test="trustFlyCoin != null">
        trust_fly_coin,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="cash != null">
        #{cash,jdbcType=BIGINT},
      </if>
      <if test="redPacket != null">
        #{redPacket,jdbcType=BIGINT},
      </if>
      <if test="specialRedPacket != null">
        #{specialRedPacket,jdbcType=BIGINT},
      </if>
      <if test="totalCashRecharge != null">
        #{totalCashRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalCashConsume != null">
        #{totalCashConsume,jdbcType=BIGINT},
      </if>
      <if test="totalRedPacketRecharge != null">
        #{totalRedPacketRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalRedPacketConsume != null">
        #{totalRedPacketConsume,jdbcType=BIGINT},
      </if>
      <if test="totalSpecialRedPacketRecharge != null">
        #{totalSpecialRedPacketRecharge,jdbcType=BIGINT},
      </if>
      <if test="totalSpecialRedPacketConsume != null">
        #{totalSpecialRedPacketConsume,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="trustFund != null">
        #{trustFund,jdbcType=BIGINT},
      </if>
      <if test="withholdFund != null">
        #{withholdFund,jdbcType=BIGINT},
      </if>
      <if test="trustCash != null">
        #{trustCash,jdbcType=BIGINT},
      </if>
      <if test="trustIncentive != null">
        #{trustIncentive,jdbcType=BIGINT},
      </if>
      <if test="trustFlyCoin != null">
        #{trustFlyCoin,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="cash != null">
        cash = values(cash),
      </if>
      <if test="redPacket != null">
        red_packet = values(red_packet),
      </if>
      <if test="specialRedPacket != null">
        special_red_packet = values(special_red_packet),
      </if>
      <if test="totalCashRecharge != null">
        total_cash_recharge = values(total_cash_recharge),
      </if>
      <if test="totalCashConsume != null">
        total_cash_consume = values(total_cash_consume),
      </if>
      <if test="totalRedPacketRecharge != null">
        total_red_packet_recharge = values(total_red_packet_recharge),
      </if>
      <if test="totalRedPacketConsume != null">
        total_red_packet_consume = values(total_red_packet_consume),
      </if>
      <if test="totalSpecialRedPacketRecharge != null">
        total_special_red_packet_recharge = values(total_special_red_packet_recharge),
      </if>
      <if test="totalSpecialRedPacketConsume != null">
        total_special_red_packet_consume = values(total_special_red_packet_consume),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="trustFund != null">
        trust_fund = values(trust_fund),
      </if>
      <if test="withholdFund != null">
        withhold_fund = values(withhold_fund),
      </if>
      <if test="trustCash != null">
        trust_cash = values(trust_cash),
      </if>
      <if test="trustIncentive != null">
        trust_incentive = values(trust_incentive),
      </if>
      <if test="trustFlyCoin != null">
        trust_fly_coin = values(trust_fly_coin),
      </if>
    </trim>
  </insert>
</mapper>