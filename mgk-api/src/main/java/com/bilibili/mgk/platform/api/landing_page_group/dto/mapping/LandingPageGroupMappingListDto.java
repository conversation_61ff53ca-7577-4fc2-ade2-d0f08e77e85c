package com.bilibili.mgk.platform.api.landing_page_group.dto.mapping;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName LandingPageGroupMappingListDto
 * <AUTHOR>
 * @Date 2023/5/22 2:44 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupMappingListDto {

    // 对于三方落地页来说 可能为空
    private Long pageId;

    // 三方落地页名称 三方专用
    private String name;

    // 原始落地页url
    private String pageUrl;

    // 联投落地页url 仅建站落地页支持
    private String containerPageUrl;

    // 联投落地页id 仅建站落地页支持
    private Long containerPageId;

    private Integer isEnable;

}
