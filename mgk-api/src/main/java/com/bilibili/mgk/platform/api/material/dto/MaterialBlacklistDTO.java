package com.bilibili.mgk.platform.api.material.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/15
 */
@Data
@Accessors(chain = true)
public class MaterialBlacklistDTO implements Serializable {

    private Long id;

    /**
     * 黑名单id
     */
    private String targetId;

    /**
     * 黑名单id类型，如customer,
     */
    private String targetType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 黑明单类型, 如forbid，protect
     */
    private String blacklistType;

    /**
     * 额外参数
     */
    private String extra;

    /**
     * 发表时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 只做预留， 目前是硬删除 是否删除(0:未删除,1:已删除)
     */
    private Boolean deleted;
}
