package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotVideoCollectTypeEnum
 * @author: gaoming
 * @date: 2020/11/16
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
public enum MgkHotVideoCollectTypeEnum {

    HOT_VIDEO(0, "热门视频"),
    HOT_ADS(1, "热门广告"),
    DAIHUO_HOT_ADS(2, "带货热门广告"),
    ;
    @Getter
    private final Integer code;
    private final String desc;

    public static MgkHotVideoCollectTypeEnum getByCode(Integer code) {
        for (MgkHotVideoCollectTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotVideoCollectTypeEnum " + code);
    }
}
