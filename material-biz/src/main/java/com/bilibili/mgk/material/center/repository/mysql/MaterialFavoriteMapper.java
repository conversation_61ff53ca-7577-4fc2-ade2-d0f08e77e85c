package com.bilibili.mgk.material.center.repository.mysql;

import com.bilibili.mgk.material.center.service.creative.model.MaterialFavorite;
import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/11
 */
public interface MaterialFavoriteMapper {


    int insertSelective(MaterialFavorite record);

    int updateByPrimaryKeySelective(MaterialFavorite record);


    int updateDeletedByAccountIdAndFavoriteKey(
            @Param("accountId") Long accountId,
            @Param("materialId") String materialId,
            @Param("deleted") Boolean deleted
    );


    /**
     * 收藏
     *
     * @return
     */
    MaterialFavorite selectByAccountIdAndFavoriteKey(
            @Param("accountId") Long accountId,
            @Param("materialId") String materialId
    );


    List<MaterialFavorite> selectByAccountIdAndFavoriteKeys(
            @Param("accountId") Long accountId,
            @Param("materialIds") List<String> materialId,
            @Param("deleted") Boolean deleted
    );


    List<MaterialFavorite> selectByAccountIdAndMaterialType(
            @Param("accountId") Long accountId,
            @Param("materialTypes") List<MaterialType> materialTypes,
            @Param("deleted") Boolean deleted
    );


}
