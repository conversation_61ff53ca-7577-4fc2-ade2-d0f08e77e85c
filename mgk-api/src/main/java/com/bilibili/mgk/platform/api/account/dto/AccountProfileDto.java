package com.bilibili.mgk.platform.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountProfileDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 是否允许代理商获取表单数据
     */
    private Integer allowAgentGetFormData;

}
