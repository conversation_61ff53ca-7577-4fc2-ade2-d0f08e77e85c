package com.bilibili.mgk.platform.biz.service.third_party.page.impl;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingListDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.MgkThirdPartyPageCreateDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.MgkThirdPartyPageDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.MgkThirdPartyPageUpdateDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.QueryMgkThirdPartyPageDto;
import com.bilibili.mgk.platform.api.third_party.page.service.IMgkThirdPartyPageService;
import com.bilibili.mgk.platform.biz.service.third_party.page.delegate.MgkThirdPartyPageServiceDelegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName MgkThirdPartyPageServiceImpl
 * <AUTHOR>
 * @Date 2023/5/19 3:49 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkThirdPartyPageServiceImpl implements IMgkThirdPartyPageService {

    @Autowired
    private MgkThirdPartyPageServiceDelegate mgkThirdPartyPageServiceDelegate;

    @Override
    public List<LandingPageGroupMappingListDto> saveThirdPartyPage(List<LandingPageGroupMappingListDto> mappingList, Operator operator) {
        return mgkThirdPartyPageServiceDelegate.saveThirdPartyPage(mappingList, operator);
    }

    @Override
    public List<MgkThirdPartyPageDto> queryMgkThirdPartyPageList(QueryMgkThirdPartyPageDto queryDto) {
        return mgkThirdPartyPageServiceDelegate.queryMgkThirdPartyPageList(queryDto);
    }

    @Override
    public int countMgkThirdPartyPage(QueryMgkThirdPartyPageDto queryDto) {
        return mgkThirdPartyPageServiceDelegate.countMgkThirdPartyPage(queryDto);
    }
}
