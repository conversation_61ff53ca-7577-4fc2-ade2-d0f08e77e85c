package com.bilibili.mgk.platform.api.shadow;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.mgk.platform.api.landing_page.dto.NewLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.UpdateLandingPageDto;

import java.util.List;
import java.util.Map;

/**
 * @ClassName IMgkShadowLandingPageService
 * <AUTHOR>
 * @Date 2022/11/8 7:56 下午
 * @Version 1.0
 **/
public interface IMgkShadowLandingPageService {

    Long getShadowPageIdByPageId(Long pageId);

    Long getPageIdByShadowPageId(Long shadowPageId);

    Map<Long, Long> getShadowPageIdMap(List<Long> pageIds);

    /**
     * 创建影子页面
     *
     * @param operator                操作符
     * @param pageId                  页面id
     * @param newShadowLandingPageDto 新的影子着陆页dto
     * @return {@link Long}
     */
    Long createShadowPage(Operator operator, Long pageId, NewLandingPageDto newShadowLandingPageDto);

    Long updateShadowPage(Operator operator, UpdateLandingPageDto updateShadowLandingPageDto);
}
