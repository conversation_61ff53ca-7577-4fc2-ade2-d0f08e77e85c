<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageAppPackageDao">
  <insert id="batchSave" parameterType="list">

    insert into
    mgk_landing_page_app_package
    (
    page_id,
    app_package_id,
    status
    )
    values
    <foreach item="entity" index="index" collection="records" open="" separator="," close="">
      (
      #{entity.pageId,jdbcType=BIGINT},
      #{entity.appPackageId,jdbcType=INTEGER},
      #{entity.status,jdbcType=TINYINT})
    </foreach>
    ON DUPLICATE KEY UPDATE
    status=VALUES (status),
    is_deleted=0;
  </insert>
</mapper>