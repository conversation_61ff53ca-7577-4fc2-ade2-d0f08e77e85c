package com.bilibili.mgk.material.center.service.bluelink.model;

import com.bilibili.mgk.material.center.service.bluelink.dto.DynamicReleaseStateDTO;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/29
 */
public class DynamicStatus {

    /**
     * 0 正常，可见状态，默认为正常
     */
    public static final int VISIBLE = 0;


    /**
     * 不正常，不可见
     */
    public static final int INVISIBLE = 1;


    public static Integer isPublicView(DynamicReleaseStateDTO dynamicState) {

        if (!Objects.isNull(dynamicState)) {
            if (!integer2Boolean(Optional.ofNullable(dynamicState.getVisible()).orElse(1L).intValue())) {
                // 0：不可见
                return INVISIBLE;
            }
            if (integer2Boolean(Optional.ofNullable(dynamicState.getInAudit()).orElse(0L).intValue())) {
                // 1：审核中，不可见
                return INVISIBLE;
            }
            if (integer2Boolean(Optional.ofNullable(dynamicState.getIsDeleted()).orElse(0L).intValue())) {
                // 1：已删除，不可见
                return INVISIBLE;
            }
            if (integer2Boolean(Optional.ofNullable(dynamicState.getUserRemove()).orElse(0L).intValue())) {
                // 1：用户删除，不可见
                return INVISIBLE;
            }
            if (integer2Boolean(Optional.ofNullable(dynamicState.getOwnerVisibleOnly()).orElse(0L).intValue())) {
                // 1：仅自己可见，不可见
                return INVISIBLE;
            }
            if (integer2Boolean(Optional.ofNullable(dynamicState.getIndexVisibleOnly()).orElse(0L).intValue())) {
                // 1：仅详情可见，不可见
                return INVISIBLE;
            }
            if (!integer2Boolean(Optional.ofNullable(dynamicState.getPegasusVisible()).orElse(1L).intValue())) {
                // 0：天马禁止分发，不可见
                return INVISIBLE;
            }
            if (integer2Boolean(Optional.ofNullable(dynamicState.getSpaceVisibleOnly()).orElse(0L).intValue())) {
                // 1：动态仅空间页可见，不可见
                return INVISIBLE;
            }
        }
        return VISIBLE;
    }

    private static Boolean integer2Boolean(Integer i) {
        if (Objects.isNull(i)) {
            return false;
        } else if (i == 1) {
            return true;
        } else if (i == 0) {
            return false;
        } else {
            throw new IllegalArgumentException("参数仅限0,1");
        }
    }

}
