/*
 * This file is generated by jOOQ.
 */
package com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.records;


import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.TResAppPackage;

import java.time.LocalDateTime;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;
import org.jooq.types.UByte;
import org.jooq.types.UInteger;


/**
 * app应用包信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResAppPackageRecord extends UpdatableRecordImpl<ResAppPackageRecord> {

    private static final long serialVersionUID = *********;

    /**
     * Setter for <code>res_app_package.id</code>. 主键ID
     */
    public void setId(UInteger value) {
        set(0, value);
    }

    /**
     * Getter for <code>res_app_package.id</code>. 主键ID
     */
    public UInteger getId() {
        return (UInteger) get(0);
    }

    /**
     * Setter for <code>res_app_package.account_id</code>. 账号id
     */
    public void setAccountId(UInteger value) {
        set(1, value);
    }

    /**
     * Getter for <code>res_app_package.account_id</code>. 账号id
     */
    public UInteger getAccountId() {
        return (UInteger) get(1);
    }

    /**
     * Setter for <code>res_app_package.name</code>. 应用包名称
     */
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>res_app_package.name</code>. 应用包名称
     */
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>res_app_package.url</code>. 应用包原始下载链接
     */
    public void setUrl(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>res_app_package.url</code>. 应用包原始下载链接
     */
    public String getUrl() {
        return (String) get(3);
    }

    /**
     * Setter for <code>res_app_package.package_name</code>. 应用包的包名称
     */
    public void setPackageName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>res_app_package.package_name</code>. 应用包的包名称
     */
    public String getPackageName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>res_app_package.app_name</code>. 应用名称
     */
    public void setAppName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>res_app_package.app_name</code>. 应用名称
     */
    public String getAppName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>res_app_package.platform</code>. 适应系统 1-IOS, 2-Android,3-iphone, 4-ipad
     */
    public void setPlatform(UByte value) {
        set(6, value);
    }

    /**
     * Getter for <code>res_app_package.platform</code>. 适应系统 1-IOS, 2-Android,3-iphone, 4-ipad
     */
    public UByte getPlatform() {
        return (UByte) get(6);
    }

    /**
     * Setter for <code>res_app_package.version</code>. 版本号
     */
    public void setVersion(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>res_app_package.version</code>. 版本号
     */
    public String getVersion() {
        return (String) get(7);
    }

    /**
     * Setter for <code>res_app_package.size</code>. 应用包大小（单位字节）
     */
    public void setSize(UInteger value) {
        set(8, value);
    }

    /**
     * Getter for <code>res_app_package.size</code>. 应用包大小（单位字节）
     */
    public UInteger getSize() {
        return (UInteger) get(8);
    }

    /**
     * Setter for <code>res_app_package.md5</code>. 应用包的MD5
     */
    public void setMd5(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>res_app_package.md5</code>. 应用包的MD5
     */
    public String getMd5() {
        return (String) get(9);
    }

    /**
     * Setter for <code>res_app_package.icon_url</code>. 图标url
     */
    public void setIconUrl(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>res_app_package.icon_url</code>. 图标url
     */
    public String getIconUrl() {
        return (String) get(10);
    }

    /**
     * Setter for <code>res_app_package.ctime</code>. 创建时间
     */
    public void setCtime(LocalDateTime value) {
        set(11, value);
    }

    /**
     * Getter for <code>res_app_package.ctime</code>. 创建时间
     */
    public LocalDateTime getCtime() {
        return (LocalDateTime) get(11);
    }

    /**
     * Setter for <code>res_app_package.mtime</code>. 修改时间
     */
    public void setMtime(LocalDateTime value) {
        set(12, value);
    }

    /**
     * Getter for <code>res_app_package.mtime</code>. 修改时间
     */
    public LocalDateTime getMtime() {
        return (LocalDateTime) get(12);
    }

    /**
     * Setter for <code>res_app_package.is_deleted</code>. 软删除 0-有效, 1-删除
     */
    public void setIsDeleted(UByte value) {
        set(13, value);
    }

    /**
     * Getter for <code>res_app_package.is_deleted</code>. 软删除 0-有效, 1-删除
     */
    public UByte getIsDeleted() {
        return (UByte) get(13);
    }

    /**
     * Setter for <code>res_app_package.internal_url</code>. 应用包内部下载链接
     */
    public void setInternalUrl(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>res_app_package.internal_url</code>. 应用包内部下载链接
     */
    public String getInternalUrl() {
        return (String) get(14);
    }

    /**
     * Setter for <code>res_app_package.status</code>. 应用包状态 0-有效，1-无效
     */
    public void setStatus(UByte value) {
        set(15, value);
    }

    /**
     * Getter for <code>res_app_package.status</code>. 应用包状态 0-有效，1-无效
     */
    public UByte getStatus() {
        return (UByte) get(15);
    }

    /**
     * Setter for <code>res_app_package.platform_status</code>. 平台状态 0-有效, 1-禁用
     */
    public void setPlatformStatus(UByte value) {
        set(16, value);
    }

    /**
     * Getter for <code>res_app_package.platform_status</code>. 平台状态 0-有效, 1-禁用
     */
    public UByte getPlatformStatus() {
        return (UByte) get(16);
    }

    /**
     * Setter for <code>res_app_package.developer_name</code>. 开发商名称
     */
    public void setDeveloperName(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>res_app_package.developer_name</code>. 开发商名称
     */
    public String getDeveloperName() {
        return (String) get(17);
    }

    /**
     * Setter for <code>res_app_package.authority_url</code>. 权限地址
     */
    public void setAuthorityUrl(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>res_app_package.authority_url</code>. 权限地址
     */
    public String getAuthorityUrl() {
        return (String) get(18);
    }

    /**
     * Setter for <code>res_app_package.auth_code_list</code>. 权限code
     */
    public void setAuthCodeList(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>res_app_package.auth_code_list</code>. 权限code
     */
    public String getAuthCodeList() {
        return (String) get(19);
    }

    /**
     * Setter for <code>res_app_package.apk_update_time</code>. 安装包更新时间
     */
    public void setApkUpdateTime(LocalDateTime value) {
        set(20, value);
    }

    /**
     * Getter for <code>res_app_package.apk_update_time</code>. 安装包更新时间
     */
    public LocalDateTime getApkUpdateTime() {
        return (LocalDateTime) get(20);
    }

    /**
     * Setter for <code>res_app_package.privacy_policy</code>. 隐私政策地址
     */
    public void setPrivacyPolicy(String value) {
        set(21, value);
    }

    /**
     * Getter for <code>res_app_package.privacy_policy</code>. 隐私政策地址
     */
    public String getPrivacyPolicy() {
        return (String) get(21);
    }

    /**
     * Setter for <code>res_app_package.dmp_app_id</code>. dmp_app_id
     */
    public void setDmpAppId(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>res_app_package.dmp_app_id</code>. dmp_app_id
     */
    public Integer getDmpAppId() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>res_app_package.is_new_fly</code>. 是否新起飞：0-否 1-是
     */
    public void setIsNewFly(UByte value) {
        set(23, value);
    }

    /**
     * Getter for <code>res_app_package.is_new_fly</code>. 是否新起飞：0-否 1-是
     */
    public UByte getIsNewFly() {
        return (UByte) get(23);
    }

    /**
     * Setter for <code>res_app_package.description</code>. 描述信息
     */
    public void setDescription(String value) {
        set(24, value);
    }

    /**
     * Getter for <code>res_app_package.description</code>. 描述信息
     */
    public String getDescription() {
        return (String) get(24);
    }

    /**
     * Setter for <code>res_app_package.sub_title</code>. 简介
     */
    public void setSubTitle(String value) {
        set(25, value);
    }

    /**
     * Getter for <code>res_app_package.sub_title</code>. 简介
     */
    public String getSubTitle() {
        return (String) get(25);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UInteger> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ResAppPackageRecord
     */
    public ResAppPackageRecord() {
        super(TResAppPackage.RES_APP_PACKAGE);
    }

    /**
     * Create a detached, initialised ResAppPackageRecord
     */
    public ResAppPackageRecord(UInteger id, UInteger accountId, String name, String url, String packageName, String appName, UByte platform, String version, UInteger size, String md5, String iconUrl, LocalDateTime ctime, LocalDateTime mtime, UByte isDeleted, String internalUrl, UByte status, UByte platformStatus, String developerName, String authorityUrl, String authCodeList, LocalDateTime apkUpdateTime, String privacyPolicy, Integer dmpAppId, UByte isNewFly, String description, String subTitle) {
        super(TResAppPackage.RES_APP_PACKAGE);

        set(0, id);
        set(1, accountId);
        set(2, name);
        set(3, url);
        set(4, packageName);
        set(5, appName);
        set(6, platform);
        set(7, version);
        set(8, size);
        set(9, md5);
        set(10, iconUrl);
        set(11, ctime);
        set(12, mtime);
        set(13, isDeleted);
        set(14, internalUrl);
        set(15, status);
        set(16, platformStatus);
        set(17, developerName);
        set(18, authorityUrl);
        set(19, authCodeList);
        set(20, apkUpdateTime);
        set(21, privacyPolicy);
        set(22, dmpAppId);
        set(23, isNewFly);
        set(24, description);
        set(25, subTitle);
    }
}
