package com.bilibili.mgk.platform.portal.webapi.form.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/9/26
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormItemTypeVo {
    @ApiModelProperty(value = "ID")
    private Integer id;
    @ApiModelProperty(value = "类型码")
    private String code;
    @ApiModelProperty(value = "描述")
    private String desc;
}
