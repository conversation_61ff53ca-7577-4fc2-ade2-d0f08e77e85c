<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkTradeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkTradePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_trade_id" jdbcType="BIGINT" property="parentTradeId" />
    <result column="trade_id" jdbcType="BIGINT" property="tradeId" />
    <result column="trade_level" jdbcType="TINYINT" property="tradeLevel" />
    <result column="trade_name" jdbcType="VARCHAR" property="tradeName" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, parent_trade_id, trade_id, trade_level, trade_name, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_trade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_trade
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_trade
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePoExample">
    delete from mgk_trade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePo">
    insert into mgk_trade (id, parent_trade_id, trade_id, 
      trade_level, trade_name, is_deleted, 
      ctime, mtime)
    values (#{id,jdbcType=INTEGER}, #{parentTradeId,jdbcType=BIGINT}, #{tradeId,jdbcType=BIGINT}, 
      #{tradeLevel,jdbcType=TINYINT}, #{tradeName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePo">
    insert into mgk_trade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="parentTradeId != null">
        parent_trade_id,
      </if>
      <if test="tradeId != null">
        trade_id,
      </if>
      <if test="tradeLevel != null">
        trade_level,
      </if>
      <if test="tradeName != null">
        trade_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="parentTradeId != null">
        #{parentTradeId,jdbcType=BIGINT},
      </if>
      <if test="tradeId != null">
        #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="tradeLevel != null">
        #{tradeLevel,jdbcType=TINYINT},
      </if>
      <if test="tradeName != null">
        #{tradeName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePoExample" resultType="java.lang.Long">
    select count(*) from mgk_trade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_trade
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.parentTradeId != null">
        parent_trade_id = #{record.parentTradeId,jdbcType=BIGINT},
      </if>
      <if test="record.tradeId != null">
        trade_id = #{record.tradeId,jdbcType=BIGINT},
      </if>
      <if test="record.tradeLevel != null">
        trade_level = #{record.tradeLevel,jdbcType=TINYINT},
      </if>
      <if test="record.tradeName != null">
        trade_name = #{record.tradeName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_trade
    set id = #{record.id,jdbcType=INTEGER},
      parent_trade_id = #{record.parentTradeId,jdbcType=BIGINT},
      trade_id = #{record.tradeId,jdbcType=BIGINT},
      trade_level = #{record.tradeLevel,jdbcType=TINYINT},
      trade_name = #{record.tradeName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePo">
    update mgk_trade
    <set>
      <if test="parentTradeId != null">
        parent_trade_id = #{parentTradeId,jdbcType=BIGINT},
      </if>
      <if test="tradeId != null">
        trade_id = #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="tradeLevel != null">
        trade_level = #{tradeLevel,jdbcType=TINYINT},
      </if>
      <if test="tradeName != null">
        trade_name = #{tradeName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePo">
    update mgk_trade
    set parent_trade_id = #{parentTradeId,jdbcType=BIGINT},
      trade_id = #{tradeId,jdbcType=BIGINT},
      trade_level = #{tradeLevel,jdbcType=TINYINT},
      trade_name = #{tradeName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePo">
    insert into mgk_trade (id, parent_trade_id, trade_id, 
      trade_level, trade_name, is_deleted, 
      ctime, mtime)
    values (#{id,jdbcType=INTEGER}, #{parentTradeId,jdbcType=BIGINT}, #{tradeId,jdbcType=BIGINT}, 
      #{tradeLevel,jdbcType=TINYINT}, #{tradeName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      parent_trade_id = values(parent_trade_id),
      trade_id = values(trade_id),
      trade_level = values(trade_level),
      trade_name = values(trade_name),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_trade
      (parent_trade_id,trade_id,trade_level,trade_name,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.parentTradeId,jdbcType=BIGINT},
        #{item.tradeId,jdbcType=BIGINT},
        #{item.tradeLevel,jdbcType=TINYINT},
        #{item.tradeName,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_trade
      (parent_trade_id,trade_id,trade_level,trade_name,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.parentTradeId,jdbcType=BIGINT},
        #{item.tradeId,jdbcType=BIGINT},
        #{item.tradeLevel,jdbcType=TINYINT},
        #{item.tradeName,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      parent_trade_id = values(parent_trade_id),
      trade_id = values(trade_id),
      trade_level = values(trade_level),
      trade_name = values(trade_name),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkTradePo">
    insert into mgk_trade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="parentTradeId != null">
        parent_trade_id,
      </if>
      <if test="tradeId != null">
        trade_id,
      </if>
      <if test="tradeLevel != null">
        trade_level,
      </if>
      <if test="tradeName != null">
        trade_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="parentTradeId != null">
        #{parentTradeId,jdbcType=BIGINT},
      </if>
      <if test="tradeId != null">
        #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="tradeLevel != null">
        #{tradeLevel,jdbcType=TINYINT},
      </if>
      <if test="tradeName != null">
        #{tradeName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="parentTradeId != null">
        parent_trade_id = values(parent_trade_id),
      </if>
      <if test="tradeId != null">
        trade_id = values(trade_id),
      </if>
      <if test="tradeLevel != null">
        trade_level = values(trade_level),
      </if>
      <if test="tradeName != null">
        trade_name = values(trade_name),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>