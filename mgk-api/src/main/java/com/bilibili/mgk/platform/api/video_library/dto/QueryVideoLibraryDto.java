package com.bilibili.mgk.platform.api.video_library.dto;

import com.bilibili.adp.common.util.Page;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/3/12
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryVideoLibraryDto implements Serializable {

    private static final long serialVersionUID = 7849187811580519976L;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 账号id
     */
    private List<Integer> accountIds;

    private String name;

    private Integer sizeType;

    private Integer definitionType;

    private Integer durationBegin;

    private Integer durationEnd;

    private Integer status;

    private Page pageInfo;

    /**
     * 审核状态 0全部 1待审核 2审核通过 3审核驳回
     */
    private Integer auditStatus;

    /**
     * 视频修改时间起
     */
    private Long mtimeFrom;

    /**
     * 视频修改时间止
     */
    private Long mtimeTo;

    /**
     * 视频id
     */
    private Integer mgkVideoId;

    /**
     * 视频状态 不传全部 1正常 2已删除
     */
    private Integer mgkVideoStatus;

    private Integer bizId;

    private Integer width;

    private Integer height;


    /**
     * 0 desc , 1 asc
     */
    private Integer order;


    private String searchWord;

}
