package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkLandingPageRefreshLogPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 页面id
     */
    private Long pageId;

    /**
     * 模板类型
     */
    private Integer templateStyle;

    /**
     * 页面类型
     */
    private Integer pageType;

    /**
     * 是否删除 0-正常 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 页面配置
     */
    private String pageConfig;

    private static final long serialVersionUID = 1L;
}