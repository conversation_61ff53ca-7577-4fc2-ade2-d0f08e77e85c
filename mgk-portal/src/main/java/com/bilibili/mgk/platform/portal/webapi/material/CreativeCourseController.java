package com.bilibili.mgk.platform.portal.webapi.material;

import com.bilibili.mgk.material.center.service.course.CreativeCourseService;
import com.bilibili.mgk.material.center.service.course.model.CourseDirectory;
import com.bilibili.mgk.material.center.service.course.model.CreativeCourse;
import com.bilibili.mgk.material.center.service.course.vo.CourseFavoriteAddReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseFavoritePageReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseFavoriteRemoveReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseFavoriteResp;
import com.bilibili.mgk.material.center.service.course.vo.CourseIdReq;
import com.bilibili.mgk.material.center.service.course.vo.CoursePageReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseRelatedReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseSearchReq;
import com.bilibili.mgk.material.center.service.course.vo.CreativeCourseSearchResult;
import com.bilibili.mgk.material.center.service.course.vo.DirectoryListReq;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.biz.common.doc.tree.common.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/18
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/course")
@Api(value = "/material/course", tags = "material-center")
@FreeLogin
public class CreativeCourseController extends BasicController {

    @Resource
    private CreativeCourseService creativeCourseService;


    /**
     * 查询所有二级目录
     *
     * @param req
     * @return
     */
    @ApiOperation("展示目录")
    @RequestMapping(value = "/directory/list", method = RequestMethod.POST)
    @FreeLogin
    public Response<List<CourseDirectory>> listAllTwoLevelDirectory(
            @ApiIgnore Context context,
            @RequestBody(required = false) DirectoryListReq req) {

        req = Optional.ofNullable(req).orElse(new DirectoryListReq());

        // 用户侧视角只能查询可见的目录
        req.setIsShow(true);


        return Response.SUCCESS(creativeCourseService.listAllTwoLevelDirectory(req));
    }


    @ApiOperation("查询相关课程")
    @RequestMapping(value = "/related", method = RequestMethod.POST)
    @FreeLogin
    public Response<List<CreativeCourse>> relatedCourse(
            @ApiIgnore Context context,
            @RequestBody CourseRelatedReq req) {

        req.setAccountId(fetchAccountIdFromContext(context));

        return Response.SUCCESS(creativeCourseService.relatedCourse(req));
    }



    /**
     * 根据课程id查询课程
     *
     * @param req
     * @return
     */
    @ApiOperation("根据课程id查询课程")
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    @FreeLogin
    public Response<List<CreativeCourse>> listById(
            @ApiIgnore Context context,
            @RequestBody CourseIdReq req) {

        req.setIsShow(true);
        req.setAccountId(fetchAccountIdFromContext(context));

        return Response.SUCCESS(creativeCourseService.listById(req));
    }


    /**
     * 根据课程id查询课程
     *
     * @param courseId
     * @return
     */
    @ApiOperation("搜索课程")
    @RequestMapping(value = "/{course_id}", method = RequestMethod.GET)
    @FreeLogin
    public Response<CreativeCourse> getById(
            @ApiIgnore Context context,
            @PathVariable(value = "course_id") Long courseId) {

        return Response.SUCCESS(creativeCourseService.getById(fetchAccountIdFromContext(context), courseId, true)
                .orElseThrow(() -> {
                    return new IllegalArgumentException("没有匹配的课程");
                }));


    }

    /**
     * 分页查询课程
     *
     * @param coursePageReq
     * @return
     */
    @ApiOperation("分页查询课程")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @FreeLogin
    public Response<Pagination<List<CreativeCourse>>> pageCourse(
            @ApiIgnore Context context,
            @RequestBody CoursePageReq coursePageReq) {

        // 对于B端面向客户，只允许查询可见的课程
        coursePageReq.setIsShow(true);
        coursePageReq.setAccountId(fetchAccountIdFromContext(context));

        return Response.SUCCESS(creativeCourseService.pageCourse(coursePageReq));
    }

    /**
     * 搜索课程
     *
     * @param req
     * @return
     */
    @ApiOperation("搜索课程")
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    @FreeLogin
    public Response<Pagination<List<CreativeCourseSearchResult>>> searchCourse(
            @ApiIgnore Context context,
            @RequestBody CourseSearchReq req) {

        req.setAccountId(fetchAccountIdFromContext(context));

        return Response.SUCCESS(creativeCourseService.searchCourse(req));
    }


    /**
     * 添加收藏
     *
     * @param req
     * @return
     */
    @ApiOperation("添加收藏")
    @RequestMapping(value = "/favorite/add", method = RequestMethod.POST)
    public Response<CourseFavoriteResp> addFavorite(
            @ApiIgnore Context context,
            @RequestBody CourseFavoriteAddReq req) {

        req.setAccountId(Optional
                .ofNullable(fetchAccountIdFromContext(context))
                .map(String::valueOf).orElse(null));

        return Response.SUCCESS(creativeCourseService.addFavorite(req));
    }


    /**
     * 取消收藏
     *
     * @param req
     * @return
     */
    @ApiOperation("取消收藏")
    @RequestMapping(value = "/favorite/remove", method = RequestMethod.POST)
    public Response<CourseFavoriteResp> removeFavorite(
            @ApiIgnore Context context,
            @RequestBody CourseFavoriteRemoveReq req) {

        req.setAccountId(Optional
                .ofNullable(fetchAccountIdFromContext(context))
                .map(String::valueOf)
                .orElse(null));

        return Response.SUCCESS(creativeCourseService.removeFavorite(req));
    }


    /**
     * 分页查询收藏课程
     *
     * @param coursePageReq
     * @return
     */
    @ApiOperation("分页查询收藏课程")
    @RequestMapping(value = "/favorite/page", method = RequestMethod.POST)
    public Response<Pagination<List<CreativeCourse>>> pageFavoriteCourse(
            @ApiIgnore Context context,
            @RequestBody CourseFavoritePageReq coursePageReq) {

        coursePageReq.setAccountId(Optional
                .ofNullable(fetchAccountIdFromContext(context))
                .map(String::valueOf)
                .orElse(null));

        return Response.SUCCESS(creativeCourseService.pageFavoriteCourse(coursePageReq));
    }



    private Long fetchAccountIdFromContext(Context context) {
        return Optional.ofNullable(context).map(Context::getAccountId).map(Integer::longValue).orElse(null);
    }
}
