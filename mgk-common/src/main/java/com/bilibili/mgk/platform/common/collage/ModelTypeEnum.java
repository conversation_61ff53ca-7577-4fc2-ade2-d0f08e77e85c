package com.bilibili.mgk.platform.common.collage;

import com.bilibili.mgk.platform.common.MgkModelStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  智能作图功能分类
 */
@AllArgsConstructor
public enum ModelTypeEnum {

    MODEL_INDUSTRY_AD(1, "制作行业广告"),

    MODEL_GOODS_AD(2, "制作商品广告");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static List<Integer> MODEL_TYPE_CODE = Arrays.stream(values()).map(ModelTypeEnum::getCode).collect(Collectors.toList());

    public static ModelTypeEnum getByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("智能作图功能分类ID不可为空");
        }
        for(ModelTypeEnum m: values()) {
            if(m.getCode().equals(code)) {
                return m;
            }
        }
        throw new IllegalArgumentException("未知的智能作图功能分类 "+code);
    }
}
