<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.ad.dao.LauUnitCreativeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePo">
    <id column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="creative_type" jdbcType="TINYINT" property="creativeType" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_name" jdbcType="VARCHAR" property="creativeName" />
    <result column="promotion_purpose_content" jdbcType="VARCHAR" property="promotionPurposeContent" />
    <result column="customized_imp_url" jdbcType="VARCHAR" property="customizedImpUrl" />
    <result column="customized_click_url" jdbcType="VARCHAR" property="customizedClickUrl" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="ext_description" jdbcType="VARCHAR" property="extDescription" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="image_md5" jdbcType="VARCHAR" property="imageMd5" />
    <result column="video_id" jdbcType="BIGINT" property="videoId" />
    <result column="video_url" jdbcType="VARCHAR" property="videoUrl" />
    <result column="ext_image_url" jdbcType="VARCHAR" property="extImageUrl" />
    <result column="ext_image_md5" jdbcType="VARCHAR" property="extImageMd5" />
    <result column="creative_json" jdbcType="VARCHAR" property="creativeJson" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="template_id" jdbcType="INTEGER" property="templateId" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="sales_type" jdbcType="TINYINT" property="salesType" />
    <result column="cm_mark" jdbcType="SMALLINT" property="cmMark" />
    <result column="button_copy" jdbcType="VARCHAR" property="buttonCopy" />
    <result column="category_first_id" jdbcType="INTEGER" property="categoryFirstId" />
    <result column="category_second_id" jdbcType="INTEGER" property="categorySecondId" />
    <result column="is_history" jdbcType="TINYINT" property="isHistory" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="begin_time" jdbcType="DATE" property="beginTime" />
    <result column="end_time" jdbcType="DATE" property="endTime" />
    <result column="creative_status" jdbcType="TINYINT" property="creativeStatus" />
    <result column="is_mark" jdbcType="TINYINT" property="isMark" />
    <result column="is_tag" jdbcType="TINYINT" property="isTag" />
    <result column="scheme_url" jdbcType="VARCHAR" property="schemeUrl" />
    <result column="jump_type" jdbcType="TINYINT" property="jumpType" />
    <result column="bilibili_user_id" jdbcType="INTEGER" property="bilibiliUserId" />
    <result column="ad_version_controll_id" jdbcType="INTEGER" property="adVersionControllId" />
    <result column="mgk_page_id" jdbcType="BIGINT" property="mgkPageId" />
    <result column="ad_mark" jdbcType="VARCHAR" property="adMark" />
    <result column="modify_offline_creative_id" jdbcType="INTEGER" property="modifyOfflineCreativeId" />
    <result column="flow_weight_state" jdbcType="TINYINT" property="flowWeightState" />
    <result column="bus_mark_id" jdbcType="INTEGER" property="busMarkId" />
    <result column="style_ability" jdbcType="TINYINT" property="styleAbility" />
    <result column="adp_version" jdbcType="TINYINT" property="adpVersion" />
    <result column="template_group_id" jdbcType="INTEGER" property="templateGroupId" />
    <result column="prefer_scene" jdbcType="TINYINT" property="preferScene" />
    <result column="is_programmatic" jdbcType="TINYINT" property="isProgrammatic" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="title_id" jdbcType="BIGINT" property="titleId" />
    <result column="auto_audit_flag" jdbcType="TINYINT" property="autoAuditFlag" />
    <result column="is_new_fly" jdbcType="TINYINT" property="isNewFly" />
    <result column="prog_audit_status" jdbcType="TINYINT" property="progAuditStatus" />
    <result column="prog_misc_elem_audit_status" jdbcType="TINYINT" property="progMiscElemAuditStatus" />
    <result column="is_recheck" jdbcType="TINYINT" property="isRecheck" />
    <result column="flag" jdbcType="INTEGER" property="flag" />
    <result column="material_video_id" jdbcType="BIGINT" property="materialVideoId" />
    <result column="under_frame_audit_flag" jdbcType="TINYINT" property="underFrameAuditFlag" />
    <result column="is_auto_fill" jdbcType="TINYINT" property="isAutoFill" />
    <result column="promotion_purpose_content_secondary" jdbcType="VARCHAR" property="promotionPurposeContentSecondary" />
    <result column="is_managed" jdbcType="TINYINT" property="isManaged" />
    <result column="is_gd_plus" jdbcType="TINYINT" property="isGdPlus" />
    <result column="advertising_mode" jdbcType="TINYINT" property="advertisingMode" />
    <result column="is_middle_ad" jdbcType="TINYINT" property="isMiddleAd" />
    <result column="is_video_bind" jdbcType="TINYINT" property="isVideoBind" />
    <result column="trackadf" jdbcType="VARCHAR" property="trackadf" />
    <result column="is_page_group" jdbcType="TINYINT" property="isPageGroup" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    creative_id, account_id, campaign_id, creative_type, unit_id, creative_name, promotion_purpose_content, 
    customized_imp_url, customized_click_url, title, description, ext_description, image_url, 
    image_md5, video_id, video_url, ext_image_url, ext_image_md5, creative_json, reason, 
    template_id, audit_status, status, version, is_deleted, ctime, mtime, order_id, sales_type, 
    cm_mark, button_copy, category_first_id, category_second_id, is_history, tags, begin_time, 
    end_time, creative_status, is_mark, is_tag, scheme_url, jump_type, bilibili_user_id, 
    ad_version_controll_id, mgk_page_id, ad_mark, modify_offline_creative_id, flow_weight_state, 
    bus_mark_id, style_ability, adp_version, template_group_id, prefer_scene, is_programmatic, 
    material_id, title_id, auto_audit_flag, is_new_fly, prog_audit_status, prog_misc_elem_audit_status, 
    is_recheck, flag, material_video_id, under_frame_audit_flag, is_auto_fill, promotion_purpose_content_secondary, 
    is_managed, is_gd_plus, advertising_mode, is_middle_ad, is_video_bind, trackadf, 
    is_page_group
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_unit_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_unit_creative
    where creative_id = #{creativeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lau_unit_creative
    where creative_id = #{creativeId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePoExample">
    delete from lau_unit_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePo">
    <selectKey keyProperty="creativeId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_creative (account_id, campaign_id, creative_type, 
      unit_id, creative_name, promotion_purpose_content, 
      customized_imp_url, customized_click_url, title, 
      description, ext_description, image_url, 
      image_md5, video_id, video_url, 
      ext_image_url, ext_image_md5, creative_json, 
      reason, template_id, audit_status, 
      status, version, is_deleted, 
      ctime, mtime, order_id, 
      sales_type, cm_mark, button_copy, 
      category_first_id, category_second_id, is_history, 
      tags, begin_time, end_time, 
      creative_status, is_mark, is_tag, 
      scheme_url, jump_type, bilibili_user_id, 
      ad_version_controll_id, mgk_page_id, ad_mark, 
      modify_offline_creative_id, flow_weight_state, 
      bus_mark_id, style_ability, adp_version, 
      template_group_id, prefer_scene, is_programmatic, 
      material_id, title_id, auto_audit_flag, 
      is_new_fly, prog_audit_status, prog_misc_elem_audit_status, 
      is_recheck, flag, material_video_id, 
      under_frame_audit_flag, is_auto_fill, promotion_purpose_content_secondary, 
      is_managed, is_gd_plus, advertising_mode, 
      is_middle_ad, is_video_bind, trackadf, 
      is_page_group)
    values (#{accountId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{creativeType,jdbcType=TINYINT}, 
      #{unitId,jdbcType=INTEGER}, #{creativeName,jdbcType=VARCHAR}, #{promotionPurposeContent,jdbcType=VARCHAR}, 
      #{customizedImpUrl,jdbcType=VARCHAR}, #{customizedClickUrl,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{extDescription,jdbcType=VARCHAR}, #{imageUrl,jdbcType=VARCHAR}, 
      #{imageMd5,jdbcType=VARCHAR}, #{videoId,jdbcType=BIGINT}, #{videoUrl,jdbcType=VARCHAR}, 
      #{extImageUrl,jdbcType=VARCHAR}, #{extImageMd5,jdbcType=VARCHAR}, #{creativeJson,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{templateId,jdbcType=INTEGER}, #{auditStatus,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{version,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{orderId,jdbcType=INTEGER}, 
      #{salesType,jdbcType=TINYINT}, #{cmMark,jdbcType=SMALLINT}, #{buttonCopy,jdbcType=VARCHAR}, 
      #{categoryFirstId,jdbcType=INTEGER}, #{categorySecondId,jdbcType=INTEGER}, #{isHistory,jdbcType=TINYINT}, 
      #{tags,jdbcType=VARCHAR}, #{beginTime,jdbcType=DATE}, #{endTime,jdbcType=DATE}, 
      #{creativeStatus,jdbcType=TINYINT}, #{isMark,jdbcType=TINYINT}, #{isTag,jdbcType=TINYINT}, 
      #{schemeUrl,jdbcType=VARCHAR}, #{jumpType,jdbcType=TINYINT}, #{bilibiliUserId,jdbcType=INTEGER}, 
      #{adVersionControllId,jdbcType=INTEGER}, #{mgkPageId,jdbcType=BIGINT}, #{adMark,jdbcType=VARCHAR}, 
      #{modifyOfflineCreativeId,jdbcType=INTEGER}, #{flowWeightState,jdbcType=TINYINT}, 
      #{busMarkId,jdbcType=INTEGER}, #{styleAbility,jdbcType=TINYINT}, #{adpVersion,jdbcType=TINYINT}, 
      #{templateGroupId,jdbcType=INTEGER}, #{preferScene,jdbcType=TINYINT}, #{isProgrammatic,jdbcType=TINYINT}, 
      #{materialId,jdbcType=BIGINT}, #{titleId,jdbcType=BIGINT}, #{autoAuditFlag,jdbcType=TINYINT}, 
      #{isNewFly,jdbcType=TINYINT}, #{progAuditStatus,jdbcType=TINYINT}, #{progMiscElemAuditStatus,jdbcType=TINYINT}, 
      #{isRecheck,jdbcType=TINYINT}, #{flag,jdbcType=INTEGER}, #{materialVideoId,jdbcType=BIGINT}, 
      #{underFrameAuditFlag,jdbcType=TINYINT}, #{isAutoFill,jdbcType=TINYINT}, #{promotionPurposeContentSecondary,jdbcType=VARCHAR}, 
      #{isManaged,jdbcType=TINYINT}, #{isGdPlus,jdbcType=TINYINT}, #{advertisingMode,jdbcType=TINYINT}, 
      #{isMiddleAd,jdbcType=TINYINT}, #{isVideoBind,jdbcType=TINYINT}, #{trackadf,jdbcType=VARCHAR}, 
      #{isPageGroup,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePo">
    <selectKey keyProperty="creativeId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_creative
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="creativeType != null">
        creative_type,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeName != null">
        creative_name,
      </if>
      <if test="promotionPurposeContent != null">
        promotion_purpose_content,
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url,
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="extDescription != null">
        ext_description,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="videoUrl != null">
        video_url,
      </if>
      <if test="extImageUrl != null">
        ext_image_url,
      </if>
      <if test="extImageMd5 != null">
        ext_image_md5,
      </if>
      <if test="creativeJson != null">
        creative_json,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="cmMark != null">
        cm_mark,
      </if>
      <if test="buttonCopy != null">
        button_copy,
      </if>
      <if test="categoryFirstId != null">
        category_first_id,
      </if>
      <if test="categorySecondId != null">
        category_second_id,
      </if>
      <if test="isHistory != null">
        is_history,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="creativeStatus != null">
        creative_status,
      </if>
      <if test="isMark != null">
        is_mark,
      </if>
      <if test="isTag != null">
        is_tag,
      </if>
      <if test="schemeUrl != null">
        scheme_url,
      </if>
      <if test="jumpType != null">
        jump_type,
      </if>
      <if test="bilibiliUserId != null">
        bilibili_user_id,
      </if>
      <if test="adVersionControllId != null">
        ad_version_controll_id,
      </if>
      <if test="mgkPageId != null">
        mgk_page_id,
      </if>
      <if test="adMark != null">
        ad_mark,
      </if>
      <if test="modifyOfflineCreativeId != null">
        modify_offline_creative_id,
      </if>
      <if test="flowWeightState != null">
        flow_weight_state,
      </if>
      <if test="busMarkId != null">
        bus_mark_id,
      </if>
      <if test="styleAbility != null">
        style_ability,
      </if>
      <if test="adpVersion != null">
        adp_version,
      </if>
      <if test="templateGroupId != null">
        template_group_id,
      </if>
      <if test="preferScene != null">
        prefer_scene,
      </if>
      <if test="isProgrammatic != null">
        is_programmatic,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="titleId != null">
        title_id,
      </if>
      <if test="autoAuditFlag != null">
        auto_audit_flag,
      </if>
      <if test="isNewFly != null">
        is_new_fly,
      </if>
      <if test="progAuditStatus != null">
        prog_audit_status,
      </if>
      <if test="progMiscElemAuditStatus != null">
        prog_misc_elem_audit_status,
      </if>
      <if test="isRecheck != null">
        is_recheck,
      </if>
      <if test="flag != null">
        flag,
      </if>
      <if test="materialVideoId != null">
        material_video_id,
      </if>
      <if test="underFrameAuditFlag != null">
        under_frame_audit_flag,
      </if>
      <if test="isAutoFill != null">
        is_auto_fill,
      </if>
      <if test="promotionPurposeContentSecondary != null">
        promotion_purpose_content_secondary,
      </if>
      <if test="isManaged != null">
        is_managed,
      </if>
      <if test="isGdPlus != null">
        is_gd_plus,
      </if>
      <if test="advertisingMode != null">
        advertising_mode,
      </if>
      <if test="isMiddleAd != null">
        is_middle_ad,
      </if>
      <if test="isVideoBind != null">
        is_video_bind,
      </if>
      <if test="trackadf != null">
        trackadf,
      </if>
      <if test="isPageGroup != null">
        is_page_group,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="creativeType != null">
        #{creativeType,jdbcType=TINYINT},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeName != null">
        #{creativeName,jdbcType=VARCHAR},
      </if>
      <if test="promotionPurposeContent != null">
        #{promotionPurposeContent,jdbcType=VARCHAR},
      </if>
      <if test="customizedImpUrl != null">
        #{customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedClickUrl != null">
        #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="extDescription != null">
        #{extDescription,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=BIGINT},
      </if>
      <if test="videoUrl != null">
        #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="extImageUrl != null">
        #{extImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="extImageMd5 != null">
        #{extImageMd5,jdbcType=VARCHAR},
      </if>
      <if test="creativeJson != null">
        #{creativeJson,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=TINYINT},
      </if>
      <if test="cmMark != null">
        #{cmMark,jdbcType=SMALLINT},
      </if>
      <if test="buttonCopy != null">
        #{buttonCopy,jdbcType=VARCHAR},
      </if>
      <if test="categoryFirstId != null">
        #{categoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="categorySecondId != null">
        #{categorySecondId,jdbcType=INTEGER},
      </if>
      <if test="isHistory != null">
        #{isHistory,jdbcType=TINYINT},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=DATE},
      </if>
      <if test="creativeStatus != null">
        #{creativeStatus,jdbcType=TINYINT},
      </if>
      <if test="isMark != null">
        #{isMark,jdbcType=TINYINT},
      </if>
      <if test="isTag != null">
        #{isTag,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="bilibiliUserId != null">
        #{bilibiliUserId,jdbcType=INTEGER},
      </if>
      <if test="adVersionControllId != null">
        #{adVersionControllId,jdbcType=INTEGER},
      </if>
      <if test="mgkPageId != null">
        #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="adMark != null">
        #{adMark,jdbcType=VARCHAR},
      </if>
      <if test="modifyOfflineCreativeId != null">
        #{modifyOfflineCreativeId,jdbcType=INTEGER},
      </if>
      <if test="flowWeightState != null">
        #{flowWeightState,jdbcType=TINYINT},
      </if>
      <if test="busMarkId != null">
        #{busMarkId,jdbcType=INTEGER},
      </if>
      <if test="styleAbility != null">
        #{styleAbility,jdbcType=TINYINT},
      </if>
      <if test="adpVersion != null">
        #{adpVersion,jdbcType=TINYINT},
      </if>
      <if test="templateGroupId != null">
        #{templateGroupId,jdbcType=INTEGER},
      </if>
      <if test="preferScene != null">
        #{preferScene,jdbcType=TINYINT},
      </if>
      <if test="isProgrammatic != null">
        #{isProgrammatic,jdbcType=TINYINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="titleId != null">
        #{titleId,jdbcType=BIGINT},
      </if>
      <if test="autoAuditFlag != null">
        #{autoAuditFlag,jdbcType=TINYINT},
      </if>
      <if test="isNewFly != null">
        #{isNewFly,jdbcType=TINYINT},
      </if>
      <if test="progAuditStatus != null">
        #{progAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="progMiscElemAuditStatus != null">
        #{progMiscElemAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="isRecheck != null">
        #{isRecheck,jdbcType=TINYINT},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=INTEGER},
      </if>
      <if test="materialVideoId != null">
        #{materialVideoId,jdbcType=BIGINT},
      </if>
      <if test="underFrameAuditFlag != null">
        #{underFrameAuditFlag,jdbcType=TINYINT},
      </if>
      <if test="isAutoFill != null">
        #{isAutoFill,jdbcType=TINYINT},
      </if>
      <if test="promotionPurposeContentSecondary != null">
        #{promotionPurposeContentSecondary,jdbcType=VARCHAR},
      </if>
      <if test="isManaged != null">
        #{isManaged,jdbcType=TINYINT},
      </if>
      <if test="isGdPlus != null">
        #{isGdPlus,jdbcType=TINYINT},
      </if>
      <if test="advertisingMode != null">
        #{advertisingMode,jdbcType=TINYINT},
      </if>
      <if test="isMiddleAd != null">
        #{isMiddleAd,jdbcType=TINYINT},
      </if>
      <if test="isVideoBind != null">
        #{isVideoBind,jdbcType=TINYINT},
      </if>
      <if test="trackadf != null">
        #{trackadf,jdbcType=VARCHAR},
      </if>
      <if test="isPageGroup != null">
        #{isPageGroup,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePoExample" resultType="java.lang.Long">
    select count(*) from lau_unit_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_unit_creative
    <set>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeType != null">
        creative_type = #{record.creativeType,jdbcType=TINYINT},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeName != null">
        creative_name = #{record.creativeName,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionPurposeContent != null">
        promotion_purpose_content = #{record.promotionPurposeContent,jdbcType=VARCHAR},
      </if>
      <if test="record.customizedImpUrl != null">
        customized_imp_url = #{record.customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.customizedClickUrl != null">
        customized_click_url = #{record.customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.extDescription != null">
        ext_description = #{record.extDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.imageUrl != null">
        image_url = #{record.imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.imageMd5 != null">
        image_md5 = #{record.imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.videoId != null">
        video_id = #{record.videoId,jdbcType=BIGINT},
      </if>
      <if test="record.videoUrl != null">
        video_url = #{record.videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.extImageUrl != null">
        ext_image_url = #{record.extImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.extImageMd5 != null">
        ext_image_md5 = #{record.extImageMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.creativeJson != null">
        creative_json = #{record.creativeJson,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.salesType != null">
        sales_type = #{record.salesType,jdbcType=TINYINT},
      </if>
      <if test="record.cmMark != null">
        cm_mark = #{record.cmMark,jdbcType=SMALLINT},
      </if>
      <if test="record.buttonCopy != null">
        button_copy = #{record.buttonCopy,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryFirstId != null">
        category_first_id = #{record.categoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="record.categorySecondId != null">
        category_second_id = #{record.categorySecondId,jdbcType=INTEGER},
      </if>
      <if test="record.isHistory != null">
        is_history = #{record.isHistory,jdbcType=TINYINT},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.beginTime != null">
        begin_time = #{record.beginTime,jdbcType=DATE},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=DATE},
      </if>
      <if test="record.creativeStatus != null">
        creative_status = #{record.creativeStatus,jdbcType=TINYINT},
      </if>
      <if test="record.isMark != null">
        is_mark = #{record.isMark,jdbcType=TINYINT},
      </if>
      <if test="record.isTag != null">
        is_tag = #{record.isTag,jdbcType=TINYINT},
      </if>
      <if test="record.schemeUrl != null">
        scheme_url = #{record.schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.jumpType != null">
        jump_type = #{record.jumpType,jdbcType=TINYINT},
      </if>
      <if test="record.bilibiliUserId != null">
        bilibili_user_id = #{record.bilibiliUserId,jdbcType=INTEGER},
      </if>
      <if test="record.adVersionControllId != null">
        ad_version_controll_id = #{record.adVersionControllId,jdbcType=INTEGER},
      </if>
      <if test="record.mgkPageId != null">
        mgk_page_id = #{record.mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="record.adMark != null">
        ad_mark = #{record.adMark,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyOfflineCreativeId != null">
        modify_offline_creative_id = #{record.modifyOfflineCreativeId,jdbcType=INTEGER},
      </if>
      <if test="record.flowWeightState != null">
        flow_weight_state = #{record.flowWeightState,jdbcType=TINYINT},
      </if>
      <if test="record.busMarkId != null">
        bus_mark_id = #{record.busMarkId,jdbcType=INTEGER},
      </if>
      <if test="record.styleAbility != null">
        style_ability = #{record.styleAbility,jdbcType=TINYINT},
      </if>
      <if test="record.adpVersion != null">
        adp_version = #{record.adpVersion,jdbcType=TINYINT},
      </if>
      <if test="record.templateGroupId != null">
        template_group_id = #{record.templateGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.preferScene != null">
        prefer_scene = #{record.preferScene,jdbcType=TINYINT},
      </if>
      <if test="record.isProgrammatic != null">
        is_programmatic = #{record.isProgrammatic,jdbcType=TINYINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.titleId != null">
        title_id = #{record.titleId,jdbcType=BIGINT},
      </if>
      <if test="record.autoAuditFlag != null">
        auto_audit_flag = #{record.autoAuditFlag,jdbcType=TINYINT},
      </if>
      <if test="record.isNewFly != null">
        is_new_fly = #{record.isNewFly,jdbcType=TINYINT},
      </if>
      <if test="record.progAuditStatus != null">
        prog_audit_status = #{record.progAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.progMiscElemAuditStatus != null">
        prog_misc_elem_audit_status = #{record.progMiscElemAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.isRecheck != null">
        is_recheck = #{record.isRecheck,jdbcType=TINYINT},
      </if>
      <if test="record.flag != null">
        flag = #{record.flag,jdbcType=INTEGER},
      </if>
      <if test="record.materialVideoId != null">
        material_video_id = #{record.materialVideoId,jdbcType=BIGINT},
      </if>
      <if test="record.underFrameAuditFlag != null">
        under_frame_audit_flag = #{record.underFrameAuditFlag,jdbcType=TINYINT},
      </if>
      <if test="record.isAutoFill != null">
        is_auto_fill = #{record.isAutoFill,jdbcType=TINYINT},
      </if>
      <if test="record.promotionPurposeContentSecondary != null">
        promotion_purpose_content_secondary = #{record.promotionPurposeContentSecondary,jdbcType=VARCHAR},
      </if>
      <if test="record.isManaged != null">
        is_managed = #{record.isManaged,jdbcType=TINYINT},
      </if>
      <if test="record.isGdPlus != null">
        is_gd_plus = #{record.isGdPlus,jdbcType=TINYINT},
      </if>
      <if test="record.advertisingMode != null">
        advertising_mode = #{record.advertisingMode,jdbcType=TINYINT},
      </if>
      <if test="record.isMiddleAd != null">
        is_middle_ad = #{record.isMiddleAd,jdbcType=TINYINT},
      </if>
      <if test="record.isVideoBind != null">
        is_video_bind = #{record.isVideoBind,jdbcType=TINYINT},
      </if>
      <if test="record.trackadf != null">
        trackadf = #{record.trackadf,jdbcType=VARCHAR},
      </if>
      <if test="record.isPageGroup != null">
        is_page_group = #{record.isPageGroup,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_unit_creative
    set creative_id = #{record.creativeId,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      creative_type = #{record.creativeType,jdbcType=TINYINT},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_name = #{record.creativeName,jdbcType=VARCHAR},
      promotion_purpose_content = #{record.promotionPurposeContent,jdbcType=VARCHAR},
      customized_imp_url = #{record.customizedImpUrl,jdbcType=VARCHAR},
      customized_click_url = #{record.customizedClickUrl,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      ext_description = #{record.extDescription,jdbcType=VARCHAR},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      image_md5 = #{record.imageMd5,jdbcType=VARCHAR},
      video_id = #{record.videoId,jdbcType=BIGINT},
      video_url = #{record.videoUrl,jdbcType=VARCHAR},
      ext_image_url = #{record.extImageUrl,jdbcType=VARCHAR},
      ext_image_md5 = #{record.extImageMd5,jdbcType=VARCHAR},
      creative_json = #{record.creativeJson,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=INTEGER},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      version = #{record.version,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      order_id = #{record.orderId,jdbcType=INTEGER},
      sales_type = #{record.salesType,jdbcType=TINYINT},
      cm_mark = #{record.cmMark,jdbcType=SMALLINT},
      button_copy = #{record.buttonCopy,jdbcType=VARCHAR},
      category_first_id = #{record.categoryFirstId,jdbcType=INTEGER},
      category_second_id = #{record.categorySecondId,jdbcType=INTEGER},
      is_history = #{record.isHistory,jdbcType=TINYINT},
      tags = #{record.tags,jdbcType=VARCHAR},
      begin_time = #{record.beginTime,jdbcType=DATE},
      end_time = #{record.endTime,jdbcType=DATE},
      creative_status = #{record.creativeStatus,jdbcType=TINYINT},
      is_mark = #{record.isMark,jdbcType=TINYINT},
      is_tag = #{record.isTag,jdbcType=TINYINT},
      scheme_url = #{record.schemeUrl,jdbcType=VARCHAR},
      jump_type = #{record.jumpType,jdbcType=TINYINT},
      bilibili_user_id = #{record.bilibiliUserId,jdbcType=INTEGER},
      ad_version_controll_id = #{record.adVersionControllId,jdbcType=INTEGER},
      mgk_page_id = #{record.mgkPageId,jdbcType=BIGINT},
      ad_mark = #{record.adMark,jdbcType=VARCHAR},
      modify_offline_creative_id = #{record.modifyOfflineCreativeId,jdbcType=INTEGER},
      flow_weight_state = #{record.flowWeightState,jdbcType=TINYINT},
      bus_mark_id = #{record.busMarkId,jdbcType=INTEGER},
      style_ability = #{record.styleAbility,jdbcType=TINYINT},
      adp_version = #{record.adpVersion,jdbcType=TINYINT},
      template_group_id = #{record.templateGroupId,jdbcType=INTEGER},
      prefer_scene = #{record.preferScene,jdbcType=TINYINT},
      is_programmatic = #{record.isProgrammatic,jdbcType=TINYINT},
      material_id = #{record.materialId,jdbcType=BIGINT},
      title_id = #{record.titleId,jdbcType=BIGINT},
      auto_audit_flag = #{record.autoAuditFlag,jdbcType=TINYINT},
      is_new_fly = #{record.isNewFly,jdbcType=TINYINT},
      prog_audit_status = #{record.progAuditStatus,jdbcType=TINYINT},
      prog_misc_elem_audit_status = #{record.progMiscElemAuditStatus,jdbcType=TINYINT},
      is_recheck = #{record.isRecheck,jdbcType=TINYINT},
      flag = #{record.flag,jdbcType=INTEGER},
      material_video_id = #{record.materialVideoId,jdbcType=BIGINT},
      under_frame_audit_flag = #{record.underFrameAuditFlag,jdbcType=TINYINT},
      is_auto_fill = #{record.isAutoFill,jdbcType=TINYINT},
      promotion_purpose_content_secondary = #{record.promotionPurposeContentSecondary,jdbcType=VARCHAR},
      is_managed = #{record.isManaged,jdbcType=TINYINT},
      is_gd_plus = #{record.isGdPlus,jdbcType=TINYINT},
      advertising_mode = #{record.advertisingMode,jdbcType=TINYINT},
      is_middle_ad = #{record.isMiddleAd,jdbcType=TINYINT},
      is_video_bind = #{record.isVideoBind,jdbcType=TINYINT},
      trackadf = #{record.trackadf,jdbcType=VARCHAR},
      is_page_group = #{record.isPageGroup,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePo">
    update lau_unit_creative
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="creativeType != null">
        creative_type = #{creativeType,jdbcType=TINYINT},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeName != null">
        creative_name = #{creativeName,jdbcType=VARCHAR},
      </if>
      <if test="promotionPurposeContent != null">
        promotion_purpose_content = #{promotionPurposeContent,jdbcType=VARCHAR},
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url = #{customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url = #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="extDescription != null">
        ext_description = #{extDescription,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        image_md5 = #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        video_id = #{videoId,jdbcType=BIGINT},
      </if>
      <if test="videoUrl != null">
        video_url = #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="extImageUrl != null">
        ext_image_url = #{extImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="extImageMd5 != null">
        ext_image_md5 = #{extImageMd5,jdbcType=VARCHAR},
      </if>
      <if test="creativeJson != null">
        creative_json = #{creativeJson,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="salesType != null">
        sales_type = #{salesType,jdbcType=TINYINT},
      </if>
      <if test="cmMark != null">
        cm_mark = #{cmMark,jdbcType=SMALLINT},
      </if>
      <if test="buttonCopy != null">
        button_copy = #{buttonCopy,jdbcType=VARCHAR},
      </if>
      <if test="categoryFirstId != null">
        category_first_id = #{categoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="categorySecondId != null">
        category_second_id = #{categorySecondId,jdbcType=INTEGER},
      </if>
      <if test="isHistory != null">
        is_history = #{isHistory,jdbcType=TINYINT},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=DATE},
      </if>
      <if test="creativeStatus != null">
        creative_status = #{creativeStatus,jdbcType=TINYINT},
      </if>
      <if test="isMark != null">
        is_mark = #{isMark,jdbcType=TINYINT},
      </if>
      <if test="isTag != null">
        is_tag = #{isTag,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        scheme_url = #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="jumpType != null">
        jump_type = #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="bilibiliUserId != null">
        bilibili_user_id = #{bilibiliUserId,jdbcType=INTEGER},
      </if>
      <if test="adVersionControllId != null">
        ad_version_controll_id = #{adVersionControllId,jdbcType=INTEGER},
      </if>
      <if test="mgkPageId != null">
        mgk_page_id = #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="adMark != null">
        ad_mark = #{adMark,jdbcType=VARCHAR},
      </if>
      <if test="modifyOfflineCreativeId != null">
        modify_offline_creative_id = #{modifyOfflineCreativeId,jdbcType=INTEGER},
      </if>
      <if test="flowWeightState != null">
        flow_weight_state = #{flowWeightState,jdbcType=TINYINT},
      </if>
      <if test="busMarkId != null">
        bus_mark_id = #{busMarkId,jdbcType=INTEGER},
      </if>
      <if test="styleAbility != null">
        style_ability = #{styleAbility,jdbcType=TINYINT},
      </if>
      <if test="adpVersion != null">
        adp_version = #{adpVersion,jdbcType=TINYINT},
      </if>
      <if test="templateGroupId != null">
        template_group_id = #{templateGroupId,jdbcType=INTEGER},
      </if>
      <if test="preferScene != null">
        prefer_scene = #{preferScene,jdbcType=TINYINT},
      </if>
      <if test="isProgrammatic != null">
        is_programmatic = #{isProgrammatic,jdbcType=TINYINT},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="titleId != null">
        title_id = #{titleId,jdbcType=BIGINT},
      </if>
      <if test="autoAuditFlag != null">
        auto_audit_flag = #{autoAuditFlag,jdbcType=TINYINT},
      </if>
      <if test="isNewFly != null">
        is_new_fly = #{isNewFly,jdbcType=TINYINT},
      </if>
      <if test="progAuditStatus != null">
        prog_audit_status = #{progAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="progMiscElemAuditStatus != null">
        prog_misc_elem_audit_status = #{progMiscElemAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="isRecheck != null">
        is_recheck = #{isRecheck,jdbcType=TINYINT},
      </if>
      <if test="flag != null">
        flag = #{flag,jdbcType=INTEGER},
      </if>
      <if test="materialVideoId != null">
        material_video_id = #{materialVideoId,jdbcType=BIGINT},
      </if>
      <if test="underFrameAuditFlag != null">
        under_frame_audit_flag = #{underFrameAuditFlag,jdbcType=TINYINT},
      </if>
      <if test="isAutoFill != null">
        is_auto_fill = #{isAutoFill,jdbcType=TINYINT},
      </if>
      <if test="promotionPurposeContentSecondary != null">
        promotion_purpose_content_secondary = #{promotionPurposeContentSecondary,jdbcType=VARCHAR},
      </if>
      <if test="isManaged != null">
        is_managed = #{isManaged,jdbcType=TINYINT},
      </if>
      <if test="isGdPlus != null">
        is_gd_plus = #{isGdPlus,jdbcType=TINYINT},
      </if>
      <if test="advertisingMode != null">
        advertising_mode = #{advertisingMode,jdbcType=TINYINT},
      </if>
      <if test="isMiddleAd != null">
        is_middle_ad = #{isMiddleAd,jdbcType=TINYINT},
      </if>
      <if test="isVideoBind != null">
        is_video_bind = #{isVideoBind,jdbcType=TINYINT},
      </if>
      <if test="trackadf != null">
        trackadf = #{trackadf,jdbcType=VARCHAR},
      </if>
      <if test="isPageGroup != null">
        is_page_group = #{isPageGroup,jdbcType=TINYINT},
      </if>
    </set>
    where creative_id = #{creativeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePo">
    update lau_unit_creative
    set account_id = #{accountId,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      creative_type = #{creativeType,jdbcType=TINYINT},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_name = #{creativeName,jdbcType=VARCHAR},
      promotion_purpose_content = #{promotionPurposeContent,jdbcType=VARCHAR},
      customized_imp_url = #{customizedImpUrl,jdbcType=VARCHAR},
      customized_click_url = #{customizedClickUrl,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      ext_description = #{extDescription,jdbcType=VARCHAR},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      image_md5 = #{imageMd5,jdbcType=VARCHAR},
      video_id = #{videoId,jdbcType=BIGINT},
      video_url = #{videoUrl,jdbcType=VARCHAR},
      ext_image_url = #{extImageUrl,jdbcType=VARCHAR},
      ext_image_md5 = #{extImageMd5,jdbcType=VARCHAR},
      creative_json = #{creativeJson,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      version = #{version,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      order_id = #{orderId,jdbcType=INTEGER},
      sales_type = #{salesType,jdbcType=TINYINT},
      cm_mark = #{cmMark,jdbcType=SMALLINT},
      button_copy = #{buttonCopy,jdbcType=VARCHAR},
      category_first_id = #{categoryFirstId,jdbcType=INTEGER},
      category_second_id = #{categorySecondId,jdbcType=INTEGER},
      is_history = #{isHistory,jdbcType=TINYINT},
      tags = #{tags,jdbcType=VARCHAR},
      begin_time = #{beginTime,jdbcType=DATE},
      end_time = #{endTime,jdbcType=DATE},
      creative_status = #{creativeStatus,jdbcType=TINYINT},
      is_mark = #{isMark,jdbcType=TINYINT},
      is_tag = #{isTag,jdbcType=TINYINT},
      scheme_url = #{schemeUrl,jdbcType=VARCHAR},
      jump_type = #{jumpType,jdbcType=TINYINT},
      bilibili_user_id = #{bilibiliUserId,jdbcType=INTEGER},
      ad_version_controll_id = #{adVersionControllId,jdbcType=INTEGER},
      mgk_page_id = #{mgkPageId,jdbcType=BIGINT},
      ad_mark = #{adMark,jdbcType=VARCHAR},
      modify_offline_creative_id = #{modifyOfflineCreativeId,jdbcType=INTEGER},
      flow_weight_state = #{flowWeightState,jdbcType=TINYINT},
      bus_mark_id = #{busMarkId,jdbcType=INTEGER},
      style_ability = #{styleAbility,jdbcType=TINYINT},
      adp_version = #{adpVersion,jdbcType=TINYINT},
      template_group_id = #{templateGroupId,jdbcType=INTEGER},
      prefer_scene = #{preferScene,jdbcType=TINYINT},
      is_programmatic = #{isProgrammatic,jdbcType=TINYINT},
      material_id = #{materialId,jdbcType=BIGINT},
      title_id = #{titleId,jdbcType=BIGINT},
      auto_audit_flag = #{autoAuditFlag,jdbcType=TINYINT},
      is_new_fly = #{isNewFly,jdbcType=TINYINT},
      prog_audit_status = #{progAuditStatus,jdbcType=TINYINT},
      prog_misc_elem_audit_status = #{progMiscElemAuditStatus,jdbcType=TINYINT},
      is_recheck = #{isRecheck,jdbcType=TINYINT},
      flag = #{flag,jdbcType=INTEGER},
      material_video_id = #{materialVideoId,jdbcType=BIGINT},
      under_frame_audit_flag = #{underFrameAuditFlag,jdbcType=TINYINT},
      is_auto_fill = #{isAutoFill,jdbcType=TINYINT},
      promotion_purpose_content_secondary = #{promotionPurposeContentSecondary,jdbcType=VARCHAR},
      is_managed = #{isManaged,jdbcType=TINYINT},
      is_gd_plus = #{isGdPlus,jdbcType=TINYINT},
      advertising_mode = #{advertisingMode,jdbcType=TINYINT},
      is_middle_ad = #{isMiddleAd,jdbcType=TINYINT},
      is_video_bind = #{isVideoBind,jdbcType=TINYINT},
      trackadf = #{trackadf,jdbcType=VARCHAR},
      is_page_group = #{isPageGroup,jdbcType=TINYINT}
    where creative_id = #{creativeId,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePo">
    <selectKey keyProperty="creativeId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_creative (account_id, campaign_id, creative_type, 
      unit_id, creative_name, promotion_purpose_content, 
      customized_imp_url, customized_click_url, title, 
      description, ext_description, image_url, 
      image_md5, video_id, video_url, 
      ext_image_url, ext_image_md5, creative_json, 
      reason, template_id, audit_status, 
      status, version, is_deleted, 
      ctime, mtime, order_id, 
      sales_type, cm_mark, button_copy, 
      category_first_id, category_second_id, is_history, 
      tags, begin_time, end_time, 
      creative_status, is_mark, is_tag, 
      scheme_url, jump_type, bilibili_user_id, 
      ad_version_controll_id, mgk_page_id, ad_mark, 
      modify_offline_creative_id, flow_weight_state, 
      bus_mark_id, style_ability, adp_version, 
      template_group_id, prefer_scene, is_programmatic, 
      material_id, title_id, auto_audit_flag, 
      is_new_fly, prog_audit_status, prog_misc_elem_audit_status, 
      is_recheck, flag, material_video_id, 
      under_frame_audit_flag, is_auto_fill, promotion_purpose_content_secondary, 
      is_managed, is_gd_plus, advertising_mode, 
      is_middle_ad, is_video_bind, trackadf, 
      is_page_group)
    values (#{accountId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{creativeType,jdbcType=TINYINT}, 
      #{unitId,jdbcType=INTEGER}, #{creativeName,jdbcType=VARCHAR}, #{promotionPurposeContent,jdbcType=VARCHAR}, 
      #{customizedImpUrl,jdbcType=VARCHAR}, #{customizedClickUrl,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{extDescription,jdbcType=VARCHAR}, #{imageUrl,jdbcType=VARCHAR}, 
      #{imageMd5,jdbcType=VARCHAR}, #{videoId,jdbcType=BIGINT}, #{videoUrl,jdbcType=VARCHAR}, 
      #{extImageUrl,jdbcType=VARCHAR}, #{extImageMd5,jdbcType=VARCHAR}, #{creativeJson,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{templateId,jdbcType=INTEGER}, #{auditStatus,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{version,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{orderId,jdbcType=INTEGER}, 
      #{salesType,jdbcType=TINYINT}, #{cmMark,jdbcType=SMALLINT}, #{buttonCopy,jdbcType=VARCHAR}, 
      #{categoryFirstId,jdbcType=INTEGER}, #{categorySecondId,jdbcType=INTEGER}, #{isHistory,jdbcType=TINYINT}, 
      #{tags,jdbcType=VARCHAR}, #{beginTime,jdbcType=DATE}, #{endTime,jdbcType=DATE}, 
      #{creativeStatus,jdbcType=TINYINT}, #{isMark,jdbcType=TINYINT}, #{isTag,jdbcType=TINYINT}, 
      #{schemeUrl,jdbcType=VARCHAR}, #{jumpType,jdbcType=TINYINT}, #{bilibiliUserId,jdbcType=INTEGER}, 
      #{adVersionControllId,jdbcType=INTEGER}, #{mgkPageId,jdbcType=BIGINT}, #{adMark,jdbcType=VARCHAR}, 
      #{modifyOfflineCreativeId,jdbcType=INTEGER}, #{flowWeightState,jdbcType=TINYINT}, 
      #{busMarkId,jdbcType=INTEGER}, #{styleAbility,jdbcType=TINYINT}, #{adpVersion,jdbcType=TINYINT}, 
      #{templateGroupId,jdbcType=INTEGER}, #{preferScene,jdbcType=TINYINT}, #{isProgrammatic,jdbcType=TINYINT}, 
      #{materialId,jdbcType=BIGINT}, #{titleId,jdbcType=BIGINT}, #{autoAuditFlag,jdbcType=TINYINT}, 
      #{isNewFly,jdbcType=TINYINT}, #{progAuditStatus,jdbcType=TINYINT}, #{progMiscElemAuditStatus,jdbcType=TINYINT}, 
      #{isRecheck,jdbcType=TINYINT}, #{flag,jdbcType=INTEGER}, #{materialVideoId,jdbcType=BIGINT}, 
      #{underFrameAuditFlag,jdbcType=TINYINT}, #{isAutoFill,jdbcType=TINYINT}, #{promotionPurposeContentSecondary,jdbcType=VARCHAR}, 
      #{isManaged,jdbcType=TINYINT}, #{isGdPlus,jdbcType=TINYINT}, #{advertisingMode,jdbcType=TINYINT}, 
      #{isMiddleAd,jdbcType=TINYINT}, #{isVideoBind,jdbcType=TINYINT}, #{trackadf,jdbcType=VARCHAR}, 
      #{isPageGroup,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      creative_type = values(creative_type),
      unit_id = values(unit_id),
      creative_name = values(creative_name),
      promotion_purpose_content = values(promotion_purpose_content),
      customized_imp_url = values(customized_imp_url),
      customized_click_url = values(customized_click_url),
      title = values(title),
      description = values(description),
      ext_description = values(ext_description),
      image_url = values(image_url),
      image_md5 = values(image_md5),
      video_id = values(video_id),
      video_url = values(video_url),
      ext_image_url = values(ext_image_url),
      ext_image_md5 = values(ext_image_md5),
      creative_json = values(creative_json),
      reason = values(reason),
      template_id = values(template_id),
      audit_status = values(audit_status),
      status = values(status),
      version = values(version),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      order_id = values(order_id),
      sales_type = values(sales_type),
      cm_mark = values(cm_mark),
      button_copy = values(button_copy),
      category_first_id = values(category_first_id),
      category_second_id = values(category_second_id),
      is_history = values(is_history),
      tags = values(tags),
      begin_time = values(begin_time),
      end_time = values(end_time),
      creative_status = values(creative_status),
      is_mark = values(is_mark),
      is_tag = values(is_tag),
      scheme_url = values(scheme_url),
      jump_type = values(jump_type),
      bilibili_user_id = values(bilibili_user_id),
      ad_version_controll_id = values(ad_version_controll_id),
      mgk_page_id = values(mgk_page_id),
      ad_mark = values(ad_mark),
      modify_offline_creative_id = values(modify_offline_creative_id),
      flow_weight_state = values(flow_weight_state),
      bus_mark_id = values(bus_mark_id),
      style_ability = values(style_ability),
      adp_version = values(adp_version),
      template_group_id = values(template_group_id),
      prefer_scene = values(prefer_scene),
      is_programmatic = values(is_programmatic),
      material_id = values(material_id),
      title_id = values(title_id),
      auto_audit_flag = values(auto_audit_flag),
      is_new_fly = values(is_new_fly),
      prog_audit_status = values(prog_audit_status),
      prog_misc_elem_audit_status = values(prog_misc_elem_audit_status),
      is_recheck = values(is_recheck),
      flag = values(flag),
      material_video_id = values(material_video_id),
      under_frame_audit_flag = values(under_frame_audit_flag),
      is_auto_fill = values(is_auto_fill),
      promotion_purpose_content_secondary = values(promotion_purpose_content_secondary),
      is_managed = values(is_managed),
      is_gd_plus = values(is_gd_plus),
      advertising_mode = values(advertising_mode),
      is_middle_ad = values(is_middle_ad),
      is_video_bind = values(is_video_bind),
      trackadf = values(trackadf),
      is_page_group = values(is_page_group),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_unit_creative
      (account_id,campaign_id,creative_type,unit_id,creative_name,promotion_purpose_content,customized_imp_url,customized_click_url,title,description,ext_description,image_url,image_md5,video_id,video_url,ext_image_url,ext_image_md5,creative_json,reason,template_id,audit_status,status,version,is_deleted,ctime,mtime,order_id,sales_type,cm_mark,button_copy,category_first_id,category_second_id,is_history,tags,begin_time,end_time,creative_status,is_mark,is_tag,scheme_url,jump_type,bilibili_user_id,ad_version_controll_id,mgk_page_id,ad_mark,modify_offline_creative_id,flow_weight_state,bus_mark_id,style_ability,adp_version,template_group_id,prefer_scene,is_programmatic,material_id,title_id,auto_audit_flag,is_new_fly,prog_audit_status,prog_misc_elem_audit_status,is_recheck,flag,material_video_id,under_frame_audit_flag,is_auto_fill,promotion_purpose_content_secondary,is_managed,is_gd_plus,advertising_mode,is_middle_ad,is_video_bind,trackadf,is_page_group)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.creativeType,jdbcType=TINYINT},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeName,jdbcType=VARCHAR},
        #{item.promotionPurposeContent,jdbcType=VARCHAR},
        #{item.customizedImpUrl,jdbcType=VARCHAR},
        #{item.customizedClickUrl,jdbcType=VARCHAR},
        #{item.title,jdbcType=VARCHAR},
        #{item.description,jdbcType=VARCHAR},
        #{item.extDescription,jdbcType=VARCHAR},
        #{item.imageUrl,jdbcType=VARCHAR},
        #{item.imageMd5,jdbcType=VARCHAR},
        #{item.videoId,jdbcType=BIGINT},
        #{item.videoUrl,jdbcType=VARCHAR},
        #{item.extImageUrl,jdbcType=VARCHAR},
        #{item.extImageMd5,jdbcType=VARCHAR},
        #{item.creativeJson,jdbcType=VARCHAR},
        #{item.reason,jdbcType=VARCHAR},
        #{item.templateId,jdbcType=INTEGER},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.version,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.orderId,jdbcType=INTEGER},
        #{item.salesType,jdbcType=TINYINT},
        #{item.cmMark,jdbcType=SMALLINT},
        #{item.buttonCopy,jdbcType=VARCHAR},
        #{item.categoryFirstId,jdbcType=INTEGER},
        #{item.categorySecondId,jdbcType=INTEGER},
        #{item.isHistory,jdbcType=TINYINT},
        #{item.tags,jdbcType=VARCHAR},
        #{item.beginTime,jdbcType=DATE},
        #{item.endTime,jdbcType=DATE},
        #{item.creativeStatus,jdbcType=TINYINT},
        #{item.isMark,jdbcType=TINYINT},
        #{item.isTag,jdbcType=TINYINT},
        #{item.schemeUrl,jdbcType=VARCHAR},
        #{item.jumpType,jdbcType=TINYINT},
        #{item.bilibiliUserId,jdbcType=INTEGER},
        #{item.adVersionControllId,jdbcType=INTEGER},
        #{item.mgkPageId,jdbcType=BIGINT},
        #{item.adMark,jdbcType=VARCHAR},
        #{item.modifyOfflineCreativeId,jdbcType=INTEGER},
        #{item.flowWeightState,jdbcType=TINYINT},
        #{item.busMarkId,jdbcType=INTEGER},
        #{item.styleAbility,jdbcType=TINYINT},
        #{item.adpVersion,jdbcType=TINYINT},
        #{item.templateGroupId,jdbcType=INTEGER},
        #{item.preferScene,jdbcType=TINYINT},
        #{item.isProgrammatic,jdbcType=TINYINT},
        #{item.materialId,jdbcType=BIGINT},
        #{item.titleId,jdbcType=BIGINT},
        #{item.autoAuditFlag,jdbcType=TINYINT},
        #{item.isNewFly,jdbcType=TINYINT},
        #{item.progAuditStatus,jdbcType=TINYINT},
        #{item.progMiscElemAuditStatus,jdbcType=TINYINT},
        #{item.isRecheck,jdbcType=TINYINT},
        #{item.flag,jdbcType=INTEGER},
        #{item.materialVideoId,jdbcType=BIGINT},
        #{item.underFrameAuditFlag,jdbcType=TINYINT},
        #{item.isAutoFill,jdbcType=TINYINT},
        #{item.promotionPurposeContentSecondary,jdbcType=VARCHAR},
        #{item.isManaged,jdbcType=TINYINT},
        #{item.isGdPlus,jdbcType=TINYINT},
        #{item.advertisingMode,jdbcType=TINYINT},
        #{item.isMiddleAd,jdbcType=TINYINT},
        #{item.isVideoBind,jdbcType=TINYINT},
        #{item.trackadf,jdbcType=VARCHAR},
        #{item.isPageGroup,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_unit_creative
      (account_id,campaign_id,creative_type,unit_id,creative_name,promotion_purpose_content,customized_imp_url,customized_click_url,title,description,ext_description,image_url,image_md5,video_id,video_url,ext_image_url,ext_image_md5,creative_json,reason,template_id,audit_status,status,version,is_deleted,ctime,mtime,order_id,sales_type,cm_mark,button_copy,category_first_id,category_second_id,is_history,tags,begin_time,end_time,creative_status,is_mark,is_tag,scheme_url,jump_type,bilibili_user_id,ad_version_controll_id,mgk_page_id,ad_mark,modify_offline_creative_id,flow_weight_state,bus_mark_id,style_ability,adp_version,template_group_id,prefer_scene,is_programmatic,material_id,title_id,auto_audit_flag,is_new_fly,prog_audit_status,prog_misc_elem_audit_status,is_recheck,flag,material_video_id,under_frame_audit_flag,is_auto_fill,promotion_purpose_content_secondary,is_managed,is_gd_plus,advertising_mode,is_middle_ad,is_video_bind,trackadf,is_page_group)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.creativeType,jdbcType=TINYINT},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeName,jdbcType=VARCHAR},
        #{item.promotionPurposeContent,jdbcType=VARCHAR},
        #{item.customizedImpUrl,jdbcType=VARCHAR},
        #{item.customizedClickUrl,jdbcType=VARCHAR},
        #{item.title,jdbcType=VARCHAR},
        #{item.description,jdbcType=VARCHAR},
        #{item.extDescription,jdbcType=VARCHAR},
        #{item.imageUrl,jdbcType=VARCHAR},
        #{item.imageMd5,jdbcType=VARCHAR},
        #{item.videoId,jdbcType=BIGINT},
        #{item.videoUrl,jdbcType=VARCHAR},
        #{item.extImageUrl,jdbcType=VARCHAR},
        #{item.extImageMd5,jdbcType=VARCHAR},
        #{item.creativeJson,jdbcType=VARCHAR},
        #{item.reason,jdbcType=VARCHAR},
        #{item.templateId,jdbcType=INTEGER},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.version,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.orderId,jdbcType=INTEGER},
        #{item.salesType,jdbcType=TINYINT},
        #{item.cmMark,jdbcType=SMALLINT},
        #{item.buttonCopy,jdbcType=VARCHAR},
        #{item.categoryFirstId,jdbcType=INTEGER},
        #{item.categorySecondId,jdbcType=INTEGER},
        #{item.isHistory,jdbcType=TINYINT},
        #{item.tags,jdbcType=VARCHAR},
        #{item.beginTime,jdbcType=DATE},
        #{item.endTime,jdbcType=DATE},
        #{item.creativeStatus,jdbcType=TINYINT},
        #{item.isMark,jdbcType=TINYINT},
        #{item.isTag,jdbcType=TINYINT},
        #{item.schemeUrl,jdbcType=VARCHAR},
        #{item.jumpType,jdbcType=TINYINT},
        #{item.bilibiliUserId,jdbcType=INTEGER},
        #{item.adVersionControllId,jdbcType=INTEGER},
        #{item.mgkPageId,jdbcType=BIGINT},
        #{item.adMark,jdbcType=VARCHAR},
        #{item.modifyOfflineCreativeId,jdbcType=INTEGER},
        #{item.flowWeightState,jdbcType=TINYINT},
        #{item.busMarkId,jdbcType=INTEGER},
        #{item.styleAbility,jdbcType=TINYINT},
        #{item.adpVersion,jdbcType=TINYINT},
        #{item.templateGroupId,jdbcType=INTEGER},
        #{item.preferScene,jdbcType=TINYINT},
        #{item.isProgrammatic,jdbcType=TINYINT},
        #{item.materialId,jdbcType=BIGINT},
        #{item.titleId,jdbcType=BIGINT},
        #{item.autoAuditFlag,jdbcType=TINYINT},
        #{item.isNewFly,jdbcType=TINYINT},
        #{item.progAuditStatus,jdbcType=TINYINT},
        #{item.progMiscElemAuditStatus,jdbcType=TINYINT},
        #{item.isRecheck,jdbcType=TINYINT},
        #{item.flag,jdbcType=INTEGER},
        #{item.materialVideoId,jdbcType=BIGINT},
        #{item.underFrameAuditFlag,jdbcType=TINYINT},
        #{item.isAutoFill,jdbcType=TINYINT},
        #{item.promotionPurposeContentSecondary,jdbcType=VARCHAR},
        #{item.isManaged,jdbcType=TINYINT},
        #{item.isGdPlus,jdbcType=TINYINT},
        #{item.advertisingMode,jdbcType=TINYINT},
        #{item.isMiddleAd,jdbcType=TINYINT},
        #{item.isVideoBind,jdbcType=TINYINT},
        #{item.trackadf,jdbcType=VARCHAR},
        #{item.isPageGroup,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      creative_type = values(creative_type),
      unit_id = values(unit_id),
      creative_name = values(creative_name),
      promotion_purpose_content = values(promotion_purpose_content),
      customized_imp_url = values(customized_imp_url),
      customized_click_url = values(customized_click_url),
      title = values(title),
      description = values(description),
      ext_description = values(ext_description),
      image_url = values(image_url),
      image_md5 = values(image_md5),
      video_id = values(video_id),
      video_url = values(video_url),
      ext_image_url = values(ext_image_url),
      ext_image_md5 = values(ext_image_md5),
      creative_json = values(creative_json),
      reason = values(reason),
      template_id = values(template_id),
      audit_status = values(audit_status),
      status = values(status),
      version = values(version),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      order_id = values(order_id),
      sales_type = values(sales_type),
      cm_mark = values(cm_mark),
      button_copy = values(button_copy),
      category_first_id = values(category_first_id),
      category_second_id = values(category_second_id),
      is_history = values(is_history),
      tags = values(tags),
      begin_time = values(begin_time),
      end_time = values(end_time),
      creative_status = values(creative_status),
      is_mark = values(is_mark),
      is_tag = values(is_tag),
      scheme_url = values(scheme_url),
      jump_type = values(jump_type),
      bilibili_user_id = values(bilibili_user_id),
      ad_version_controll_id = values(ad_version_controll_id),
      mgk_page_id = values(mgk_page_id),
      ad_mark = values(ad_mark),
      modify_offline_creative_id = values(modify_offline_creative_id),
      flow_weight_state = values(flow_weight_state),
      bus_mark_id = values(bus_mark_id),
      style_ability = values(style_ability),
      adp_version = values(adp_version),
      template_group_id = values(template_group_id),
      prefer_scene = values(prefer_scene),
      is_programmatic = values(is_programmatic),
      material_id = values(material_id),
      title_id = values(title_id),
      auto_audit_flag = values(auto_audit_flag),
      is_new_fly = values(is_new_fly),
      prog_audit_status = values(prog_audit_status),
      prog_misc_elem_audit_status = values(prog_misc_elem_audit_status),
      is_recheck = values(is_recheck),
      flag = values(flag),
      material_video_id = values(material_video_id),
      under_frame_audit_flag = values(under_frame_audit_flag),
      is_auto_fill = values(is_auto_fill),
      promotion_purpose_content_secondary = values(promotion_purpose_content_secondary),
      is_managed = values(is_managed),
      is_gd_plus = values(is_gd_plus),
      advertising_mode = values(advertising_mode),
      is_middle_ad = values(is_middle_ad),
      is_video_bind = values(is_video_bind),
      trackadf = values(trackadf),
      is_page_group = values(is_page_group),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.LauUnitCreativePo">
    <selectKey keyProperty="creativeId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_creative
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="creativeType != null">
        creative_type,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeName != null">
        creative_name,
      </if>
      <if test="promotionPurposeContent != null">
        promotion_purpose_content,
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url,
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="extDescription != null">
        ext_description,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="videoUrl != null">
        video_url,
      </if>
      <if test="extImageUrl != null">
        ext_image_url,
      </if>
      <if test="extImageMd5 != null">
        ext_image_md5,
      </if>
      <if test="creativeJson != null">
        creative_json,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="cmMark != null">
        cm_mark,
      </if>
      <if test="buttonCopy != null">
        button_copy,
      </if>
      <if test="categoryFirstId != null">
        category_first_id,
      </if>
      <if test="categorySecondId != null">
        category_second_id,
      </if>
      <if test="isHistory != null">
        is_history,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="creativeStatus != null">
        creative_status,
      </if>
      <if test="isMark != null">
        is_mark,
      </if>
      <if test="isTag != null">
        is_tag,
      </if>
      <if test="schemeUrl != null">
        scheme_url,
      </if>
      <if test="jumpType != null">
        jump_type,
      </if>
      <if test="bilibiliUserId != null">
        bilibili_user_id,
      </if>
      <if test="adVersionControllId != null">
        ad_version_controll_id,
      </if>
      <if test="mgkPageId != null">
        mgk_page_id,
      </if>
      <if test="adMark != null">
        ad_mark,
      </if>
      <if test="modifyOfflineCreativeId != null">
        modify_offline_creative_id,
      </if>
      <if test="flowWeightState != null">
        flow_weight_state,
      </if>
      <if test="busMarkId != null">
        bus_mark_id,
      </if>
      <if test="styleAbility != null">
        style_ability,
      </if>
      <if test="adpVersion != null">
        adp_version,
      </if>
      <if test="templateGroupId != null">
        template_group_id,
      </if>
      <if test="preferScene != null">
        prefer_scene,
      </if>
      <if test="isProgrammatic != null">
        is_programmatic,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="titleId != null">
        title_id,
      </if>
      <if test="autoAuditFlag != null">
        auto_audit_flag,
      </if>
      <if test="isNewFly != null">
        is_new_fly,
      </if>
      <if test="progAuditStatus != null">
        prog_audit_status,
      </if>
      <if test="progMiscElemAuditStatus != null">
        prog_misc_elem_audit_status,
      </if>
      <if test="isRecheck != null">
        is_recheck,
      </if>
      <if test="flag != null">
        flag,
      </if>
      <if test="materialVideoId != null">
        material_video_id,
      </if>
      <if test="underFrameAuditFlag != null">
        under_frame_audit_flag,
      </if>
      <if test="isAutoFill != null">
        is_auto_fill,
      </if>
      <if test="promotionPurposeContentSecondary != null">
        promotion_purpose_content_secondary,
      </if>
      <if test="isManaged != null">
        is_managed,
      </if>
      <if test="isGdPlus != null">
        is_gd_plus,
      </if>
      <if test="advertisingMode != null">
        advertising_mode,
      </if>
      <if test="isMiddleAd != null">
        is_middle_ad,
      </if>
      <if test="isVideoBind != null">
        is_video_bind,
      </if>
      <if test="trackadf != null">
        trackadf,
      </if>
      <if test="isPageGroup != null">
        is_page_group,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="creativeType != null">
        #{creativeType,jdbcType=TINYINT},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeName != null">
        #{creativeName,jdbcType=VARCHAR},
      </if>
      <if test="promotionPurposeContent != null">
        #{promotionPurposeContent,jdbcType=VARCHAR},
      </if>
      <if test="customizedImpUrl != null">
        #{customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedClickUrl != null">
        #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="extDescription != null">
        #{extDescription,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=BIGINT},
      </if>
      <if test="videoUrl != null">
        #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="extImageUrl != null">
        #{extImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="extImageMd5 != null">
        #{extImageMd5,jdbcType=VARCHAR},
      </if>
      <if test="creativeJson != null">
        #{creativeJson,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=TINYINT},
      </if>
      <if test="cmMark != null">
        #{cmMark,jdbcType=SMALLINT},
      </if>
      <if test="buttonCopy != null">
        #{buttonCopy,jdbcType=VARCHAR},
      </if>
      <if test="categoryFirstId != null">
        #{categoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="categorySecondId != null">
        #{categorySecondId,jdbcType=INTEGER},
      </if>
      <if test="isHistory != null">
        #{isHistory,jdbcType=TINYINT},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=DATE},
      </if>
      <if test="creativeStatus != null">
        #{creativeStatus,jdbcType=TINYINT},
      </if>
      <if test="isMark != null">
        #{isMark,jdbcType=TINYINT},
      </if>
      <if test="isTag != null">
        #{isTag,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="bilibiliUserId != null">
        #{bilibiliUserId,jdbcType=INTEGER},
      </if>
      <if test="adVersionControllId != null">
        #{adVersionControllId,jdbcType=INTEGER},
      </if>
      <if test="mgkPageId != null">
        #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="adMark != null">
        #{adMark,jdbcType=VARCHAR},
      </if>
      <if test="modifyOfflineCreativeId != null">
        #{modifyOfflineCreativeId,jdbcType=INTEGER},
      </if>
      <if test="flowWeightState != null">
        #{flowWeightState,jdbcType=TINYINT},
      </if>
      <if test="busMarkId != null">
        #{busMarkId,jdbcType=INTEGER},
      </if>
      <if test="styleAbility != null">
        #{styleAbility,jdbcType=TINYINT},
      </if>
      <if test="adpVersion != null">
        #{adpVersion,jdbcType=TINYINT},
      </if>
      <if test="templateGroupId != null">
        #{templateGroupId,jdbcType=INTEGER},
      </if>
      <if test="preferScene != null">
        #{preferScene,jdbcType=TINYINT},
      </if>
      <if test="isProgrammatic != null">
        #{isProgrammatic,jdbcType=TINYINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="titleId != null">
        #{titleId,jdbcType=BIGINT},
      </if>
      <if test="autoAuditFlag != null">
        #{autoAuditFlag,jdbcType=TINYINT},
      </if>
      <if test="isNewFly != null">
        #{isNewFly,jdbcType=TINYINT},
      </if>
      <if test="progAuditStatus != null">
        #{progAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="progMiscElemAuditStatus != null">
        #{progMiscElemAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="isRecheck != null">
        #{isRecheck,jdbcType=TINYINT},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=INTEGER},
      </if>
      <if test="materialVideoId != null">
        #{materialVideoId,jdbcType=BIGINT},
      </if>
      <if test="underFrameAuditFlag != null">
        #{underFrameAuditFlag,jdbcType=TINYINT},
      </if>
      <if test="isAutoFill != null">
        #{isAutoFill,jdbcType=TINYINT},
      </if>
      <if test="promotionPurposeContentSecondary != null">
        #{promotionPurposeContentSecondary,jdbcType=VARCHAR},
      </if>
      <if test="isManaged != null">
        #{isManaged,jdbcType=TINYINT},
      </if>
      <if test="isGdPlus != null">
        #{isGdPlus,jdbcType=TINYINT},
      </if>
      <if test="advertisingMode != null">
        #{advertisingMode,jdbcType=TINYINT},
      </if>
      <if test="isMiddleAd != null">
        #{isMiddleAd,jdbcType=TINYINT},
      </if>
      <if test="isVideoBind != null">
        #{isVideoBind,jdbcType=TINYINT},
      </if>
      <if test="trackadf != null">
        #{trackadf,jdbcType=VARCHAR},
      </if>
      <if test="isPageGroup != null">
        #{isPageGroup,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="creativeType != null">
        creative_type = values(creative_type),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="creativeName != null">
        creative_name = values(creative_name),
      </if>
      <if test="promotionPurposeContent != null">
        promotion_purpose_content = values(promotion_purpose_content),
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url = values(customized_imp_url),
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url = values(customized_click_url),
      </if>
      <if test="title != null">
        title = values(title),
      </if>
      <if test="description != null">
        description = values(description),
      </if>
      <if test="extDescription != null">
        ext_description = values(ext_description),
      </if>
      <if test="imageUrl != null">
        image_url = values(image_url),
      </if>
      <if test="imageMd5 != null">
        image_md5 = values(image_md5),
      </if>
      <if test="videoId != null">
        video_id = values(video_id),
      </if>
      <if test="videoUrl != null">
        video_url = values(video_url),
      </if>
      <if test="extImageUrl != null">
        ext_image_url = values(ext_image_url),
      </if>
      <if test="extImageMd5 != null">
        ext_image_md5 = values(ext_image_md5),
      </if>
      <if test="creativeJson != null">
        creative_json = values(creative_json),
      </if>
      <if test="reason != null">
        reason = values(reason),
      </if>
      <if test="templateId != null">
        template_id = values(template_id),
      </if>
      <if test="auditStatus != null">
        audit_status = values(audit_status),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="orderId != null">
        order_id = values(order_id),
      </if>
      <if test="salesType != null">
        sales_type = values(sales_type),
      </if>
      <if test="cmMark != null">
        cm_mark = values(cm_mark),
      </if>
      <if test="buttonCopy != null">
        button_copy = values(button_copy),
      </if>
      <if test="categoryFirstId != null">
        category_first_id = values(category_first_id),
      </if>
      <if test="categorySecondId != null">
        category_second_id = values(category_second_id),
      </if>
      <if test="isHistory != null">
        is_history = values(is_history),
      </if>
      <if test="tags != null">
        tags = values(tags),
      </if>
      <if test="beginTime != null">
        begin_time = values(begin_time),
      </if>
      <if test="endTime != null">
        end_time = values(end_time),
      </if>
      <if test="creativeStatus != null">
        creative_status = values(creative_status),
      </if>
      <if test="isMark != null">
        is_mark = values(is_mark),
      </if>
      <if test="isTag != null">
        is_tag = values(is_tag),
      </if>
      <if test="schemeUrl != null">
        scheme_url = values(scheme_url),
      </if>
      <if test="jumpType != null">
        jump_type = values(jump_type),
      </if>
      <if test="bilibiliUserId != null">
        bilibili_user_id = values(bilibili_user_id),
      </if>
      <if test="adVersionControllId != null">
        ad_version_controll_id = values(ad_version_controll_id),
      </if>
      <if test="mgkPageId != null">
        mgk_page_id = values(mgk_page_id),
      </if>
      <if test="adMark != null">
        ad_mark = values(ad_mark),
      </if>
      <if test="modifyOfflineCreativeId != null">
        modify_offline_creative_id = values(modify_offline_creative_id),
      </if>
      <if test="flowWeightState != null">
        flow_weight_state = values(flow_weight_state),
      </if>
      <if test="busMarkId != null">
        bus_mark_id = values(bus_mark_id),
      </if>
      <if test="styleAbility != null">
        style_ability = values(style_ability),
      </if>
      <if test="adpVersion != null">
        adp_version = values(adp_version),
      </if>
      <if test="templateGroupId != null">
        template_group_id = values(template_group_id),
      </if>
      <if test="preferScene != null">
        prefer_scene = values(prefer_scene),
      </if>
      <if test="isProgrammatic != null">
        is_programmatic = values(is_programmatic),
      </if>
      <if test="materialId != null">
        material_id = values(material_id),
      </if>
      <if test="titleId != null">
        title_id = values(title_id),
      </if>
      <if test="autoAuditFlag != null">
        auto_audit_flag = values(auto_audit_flag),
      </if>
      <if test="isNewFly != null">
        is_new_fly = values(is_new_fly),
      </if>
      <if test="progAuditStatus != null">
        prog_audit_status = values(prog_audit_status),
      </if>
      <if test="progMiscElemAuditStatus != null">
        prog_misc_elem_audit_status = values(prog_misc_elem_audit_status),
      </if>
      <if test="isRecheck != null">
        is_recheck = values(is_recheck),
      </if>
      <if test="flag != null">
        flag = values(flag),
      </if>
      <if test="materialVideoId != null">
        material_video_id = values(material_video_id),
      </if>
      <if test="underFrameAuditFlag != null">
        under_frame_audit_flag = values(under_frame_audit_flag),
      </if>
      <if test="isAutoFill != null">
        is_auto_fill = values(is_auto_fill),
      </if>
      <if test="promotionPurposeContentSecondary != null">
        promotion_purpose_content_secondary = values(promotion_purpose_content_secondary),
      </if>
      <if test="isManaged != null">
        is_managed = values(is_managed),
      </if>
      <if test="isGdPlus != null">
        is_gd_plus = values(is_gd_plus),
      </if>
      <if test="advertisingMode != null">
        advertising_mode = values(advertising_mode),
      </if>
      <if test="isMiddleAd != null">
        is_middle_ad = values(is_middle_ad),
      </if>
      <if test="isVideoBind != null">
        is_video_bind = values(is_video_bind),
      </if>
      <if test="trackadf != null">
        trackadf = values(trackadf),
      </if>
      <if test="isPageGroup != null">
        is_page_group = values(is_page_group),
      </if>
    </trim>
  </insert>
</mapper>