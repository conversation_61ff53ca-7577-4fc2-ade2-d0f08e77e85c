package com.biz.common.doc.tree.service.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import reactor.core.support.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/2
 */
@Data
@Accessors(chain = true)
public class FavoriteNodePageReq implements SnakeCaseBody {


    @ApiModelProperty(value = "页码")
    private Integer pn;

    @ApiModelProperty(value = "每页大小")
    private Integer ps;

    @ApiModelProperty(value = "账户id")
    private String accountId;

    @ApiModelProperty(value = "可选， 根节点id，数组，即允许跨树查询")
    private List<Long> rootId;
    
    @ApiModelProperty(value = "可选，父节点id，要和depth一起使用")
    private Long parentId;

    @ApiModelProperty(value = "可选，深度，要和parentId一起使用")
    private Integer depth;

    @ApiModelProperty(value = "可选，节点类型")
    private List<String> nodeType;

    @ApiModelProperty(value = "可选，文档类型")
    private List<String> docType;

    @ApiModelProperty(value = "可选，是否显示")
    private Boolean isShow = true;

    @ApiModelProperty(value = "可选，是否删除")
    private Boolean isDeleted = false;

    @ApiModelProperty(value = "可选，是否获取文档")
    private Boolean retrieveDoc = false;


    public void validate() {

        Assert.notNull(pn, "pn is null");
        Assert.notNull(ps, "ps is null");
        Assert.notNull(accountId, "accountId is null");


    }

}
