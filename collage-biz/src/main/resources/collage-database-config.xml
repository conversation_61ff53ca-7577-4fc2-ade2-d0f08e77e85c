<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
  		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
  		http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <bean id="mgkCatExecutorMybatisPlugin" class="com.bilibili.bjcom.cat.mybatis.CatExecutorMybatisPlugin"/>

    <bean id="collagePlatformDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${mgk.jdbc.driver}"></property>
        <property name="jdbcUrl" value="${mgk.jdbc.url}"></property>
        <property name="user" value="${mgk.jdbc.username}"></property>
        <property name="password" value="${mgk.jdbc.password}"></property>
        <!--<property name="minPoolSize" value="10"></property>-->
        <property name="maxPoolSize" value="10"></property>
        <property name="maxIdleTime" value="7200"></property>
        <property name="testConnectionOnCheckin" value="true"></property>
        <property name="idleConnectionTestPeriod" value="5"></property>
        <property name="preferredTestQuery" value="SELECT 1"></property>
        <property name="checkoutTimeout" value="1800000"></property>
    </bean>

    <!-- Spring 和 MyBatis -->
    <bean id="collagePlatformSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="collagePlatformDataSource"/>
        <property name="mapperLocations" value="classpath:mapper/collage/*.xml"/>
        <property name="plugins">
            <array>
                <ref bean="mgkCatExecutorMybatisPlugin"/>
            </array>
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.bilibili.collage.biz.dao"/>
        <property name="sqlSessionFactoryBeanName" value="collagePlatformSqlSessionFactory"/>
    </bean>
    <tx:annotation-driven transaction-manager="collagePlatformTransactionManager"/>
    <!-- 配置事务管理器 -->
    <bean id="collagePlatformTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="collagePlatformDataSource"/>
    </bean>
</beans>
