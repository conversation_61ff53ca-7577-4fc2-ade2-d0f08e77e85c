package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageArchiveRefreshLogDto;
import com.bilibili.mgk.platform.biz.dao.MgkLandingPageArchiveRefreshLogDao;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageArchiveRefreshLogPo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageArchiveRefreshLogPoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @file: MgkLandingPageArchiveRefreshLogServiceDelegate
 * @author: gaoming
 * @date: 2021/12/13
 * @version: 1.0
 * @description:
 **/
@Service
public class MgkLandingPageArchiveRefreshLogServiceDelegate {

    @Autowired
    private MgkLandingPageArchiveRefreshLogDao mgkLandingPageArchiveRefreshLogDao;

    public List<MgkLandingPageArchiveRefreshLogDto> getDtosByPageIds(List<Long> pageIds) {
        List<MgkLandingPageArchiveRefreshLogPo> pos = getPosByPageIds(pageIds);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos.stream().map(this::convertPo2Dto).collect(Collectors.toList());
    }

    private MgkLandingPageArchiveRefreshLogDto convertPo2Dto(MgkLandingPageArchiveRefreshLogPo po) {
        MgkLandingPageArchiveRefreshLogDto dto = MgkLandingPageArchiveRefreshLogDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private List<MgkLandingPageArchiveRefreshLogPo> getPosByPageIds(List<Long> pageIds) {
        MgkLandingPageArchiveRefreshLogPoExample example = new MgkLandingPageArchiveRefreshLogPoExample();
        MgkLandingPageArchiveRefreshLogPoExample.Criteria or = example.or();
        or.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        if (pageIds.size() == 1) {
            or.andPageIdEqualTo(pageIds.get(0));
        } else {
            or.andPageIdIn(pageIds);
        }

        return mgkLandingPageArchiveRefreshLogDao.selectByExampleWithBLOBs(example);
    }

    /**
     * 删除数据
     *
     * @param pageId
     */
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void deletedByPageId(Long pageId) {
        MgkLandingPageArchiveRefreshLogPoExample example = new MgkLandingPageArchiveRefreshLogPoExample();
        MgkLandingPageArchiveRefreshLogPoExample.Criteria or = example.or();
        or.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        or.andPageIdEqualTo(pageId);
        mgkLandingPageArchiveRefreshLogDao.updateByExampleSelective(MgkLandingPageArchiveRefreshLogPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build(), example);

    }
}
