package com.bilibili.mgk.platform.portal.webapi.inspiration.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @file: UpdateArticleVo
 * @author: gaoming
 * @date: 2021/03/23
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class UpdateArticleVo {

    /**
     * 标题
     */
    private String title;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 行业 1-电商 2-游戏 3-网服 4-教育 5-其他
     */
    private String industry;

    /**
     * 内容
     */
    private String content;
}
