<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.ad.dao.CrmSecondaryAgentRoleDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mid" jdbcType="BIGINT" property="mid" />
    <result column="bid" jdbcType="BIGINT" property="bid" />
    <result column="agent_id" jdbcType="INTEGER" property="agentId" />
    <result column="role_type" jdbcType="TINYINT" property="roleType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="old_agent_id" jdbcType="INTEGER" property="oldAgentId" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePo">
    <id column="crm_secondary_agent_role_id" jdbcType="INTEGER" property="id" />
    <result column="crm_secondary_agent_role_mid" jdbcType="BIGINT" property="mid" />
    <result column="crm_secondary_agent_role_bid" jdbcType="BIGINT" property="bid" />
    <result column="crm_secondary_agent_role_agent_id" jdbcType="INTEGER" property="agentId" />
    <result column="crm_secondary_agent_role_role_type" jdbcType="TINYINT" property="roleType" />
    <result column="crm_secondary_agent_role_remark" jdbcType="VARCHAR" property="remark" />
    <result column="crm_secondary_agent_role_status" jdbcType="TINYINT" property="status" />
    <result column="crm_secondary_agent_role_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="crm_secondary_agent_role_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="crm_secondary_agent_role_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="crm_secondary_agent_role_old_agent_id" jdbcType="INTEGER" property="oldAgentId" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as crm_secondary_agent_role_id, ${alias}.mid as crm_secondary_agent_role_mid, 
    ${alias}.bid as crm_secondary_agent_role_bid, ${alias}.agent_id as crm_secondary_agent_role_agent_id, 
    ${alias}.role_type as crm_secondary_agent_role_role_type, ${alias}.remark as crm_secondary_agent_role_remark, 
    ${alias}.status as crm_secondary_agent_role_status, ${alias}.is_deleted as crm_secondary_agent_role_is_deleted, 
    ${alias}.ctime as crm_secondary_agent_role_ctime, ${alias}.mtime as crm_secondary_agent_role_mtime, 
    ${alias}.old_agent_id as crm_secondary_agent_role_old_agent_id
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mid, bid, agent_id, role_type, remark, status, is_deleted, ctime, mtime, old_agent_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_secondary_agent_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_secondary_agent_role
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_secondary_agent_role
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePoExample">
    delete from crm_secondary_agent_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_secondary_agent_role (mid, bid, agent_id, 
      role_type, remark, status, 
      is_deleted, ctime, mtime, 
      old_agent_id)
    values (#{mid,jdbcType=BIGINT}, #{bid,jdbcType=BIGINT}, #{agentId,jdbcType=INTEGER}, 
      #{roleType,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{oldAgentId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_secondary_agent_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mid != null">
        mid,
      </if>
      <if test="bid != null">
        bid,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="oldAgentId != null">
        old_agent_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="bid != null">
        #{bid,jdbcType=BIGINT},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=INTEGER},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldAgentId != null">
        #{oldAgentId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePoExample" resultType="java.lang.Long">
    select count(*) from crm_secondary_agent_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_secondary_agent_role
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.bid != null">
        bid = #{record.bid,jdbcType=BIGINT},
      </if>
      <if test="record.agentId != null">
        agent_id = #{record.agentId,jdbcType=INTEGER},
      </if>
      <if test="record.roleType != null">
        role_type = #{record.roleType,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.oldAgentId != null">
        old_agent_id = #{record.oldAgentId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_secondary_agent_role
    set id = #{record.id,jdbcType=INTEGER},
      mid = #{record.mid,jdbcType=BIGINT},
      bid = #{record.bid,jdbcType=BIGINT},
      agent_id = #{record.agentId,jdbcType=INTEGER},
      role_type = #{record.roleType,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      old_agent_id = #{record.oldAgentId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePo">
    update crm_secondary_agent_role
    <set>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="bid != null">
        bid = #{bid,jdbcType=BIGINT},
      </if>
      <if test="agentId != null">
        agent_id = #{agentId,jdbcType=INTEGER},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldAgentId != null">
        old_agent_id = #{oldAgentId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePo">
    update crm_secondary_agent_role
    set mid = #{mid,jdbcType=BIGINT},
      bid = #{bid,jdbcType=BIGINT},
      agent_id = #{agentId,jdbcType=INTEGER},
      role_type = #{roleType,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      old_agent_id = #{oldAgentId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_secondary_agent_role (mid, bid, agent_id, 
      role_type, remark, status, 
      is_deleted, ctime, mtime, 
      old_agent_id)
    values (#{mid,jdbcType=BIGINT}, #{bid,jdbcType=BIGINT}, #{agentId,jdbcType=INTEGER}, 
      #{roleType,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{oldAgentId,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      mid = values(mid),
      bid = values(bid),
      agent_id = values(agent_id),
      role_type = values(role_type),
      remark = values(remark),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      old_agent_id = values(old_agent_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_secondary_agent_role
      (mid,bid,agent_id,role_type,remark,status,is_deleted,ctime,mtime,old_agent_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.mid,jdbcType=BIGINT},
        #{item.bid,jdbcType=BIGINT},
        #{item.agentId,jdbcType=INTEGER},
        #{item.roleType,jdbcType=TINYINT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.oldAgentId,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_secondary_agent_role
      (mid,bid,agent_id,role_type,remark,status,is_deleted,ctime,mtime,old_agent_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.mid,jdbcType=BIGINT},
        #{item.bid,jdbcType=BIGINT},
        #{item.agentId,jdbcType=INTEGER},
        #{item.roleType,jdbcType=TINYINT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.oldAgentId,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      mid = values(mid),
      bid = values(bid),
      agent_id = values(agent_id),
      role_type = values(role_type),
      remark = values(remark),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      old_agent_id = values(old_agent_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.ad.po.CrmSecondaryAgentRolePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_secondary_agent_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mid != null">
        mid,
      </if>
      <if test="bid != null">
        bid,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="oldAgentId != null">
        old_agent_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="bid != null">
        #{bid,jdbcType=BIGINT},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=INTEGER},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldAgentId != null">
        #{oldAgentId,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="mid != null">
        mid = values(mid),
      </if>
      <if test="bid != null">
        bid = values(bid),
      </if>
      <if test="agentId != null">
        agent_id = values(agent_id),
      </if>
      <if test="roleType != null">
        role_type = values(role_type),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="oldAgentId != null">
        old_agent_id = values(old_agent_id),
      </if>
    </trim>
  </insert>
</mapper>