package com.bilibili.mgk.material.center.service.creative.vo;

import com.bilibili.mgk.material.center.service.creative.model.UpRankVideoQueryGroupType;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/2
 */
@Data
@Accessors(chain = true)
public class UpRankVideoQuery implements SnakeCaseBody {


    @ApiModelProperty()
    private Long upMid;


    @ApiModelProperty()
    private UpRankVideoQueryGroupType type;


    @ApiModelProperty("视频分区")
    private List<String> avidTidNames;


    @ApiModelProperty
    private MaterialSortBy sortBy = MaterialSortBy.play;


    public void validate() {

        Assert.notNull(sortBy, "sortBy不能为空");

        sortBy.validateUpRankVideoQuery();


    }
}
