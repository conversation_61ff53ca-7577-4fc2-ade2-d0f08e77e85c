package com.bilibili.collage.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MgkCollageLayerPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MgkCollageLayerPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andXAxisIsNull() {
            addCriterion("x_axis is null");
            return (Criteria) this;
        }

        public Criteria andXAxisIsNotNull() {
            addCriterion("x_axis is not null");
            return (Criteria) this;
        }

        public Criteria andXAxisEqualTo(Integer value) {
            addCriterion("x_axis =", value, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisNotEqualTo(Integer value) {
            addCriterion("x_axis <>", value, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisGreaterThan(Integer value) {
            addCriterion("x_axis >", value, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisGreaterThanOrEqualTo(Integer value) {
            addCriterion("x_axis >=", value, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisLessThan(Integer value) {
            addCriterion("x_axis <", value, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisLessThanOrEqualTo(Integer value) {
            addCriterion("x_axis <=", value, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisIn(List<Integer> values) {
            addCriterion("x_axis in", values, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisNotIn(List<Integer> values) {
            addCriterion("x_axis not in", values, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisBetween(Integer value1, Integer value2) {
            addCriterion("x_axis between", value1, value2, "xAxis");
            return (Criteria) this;
        }

        public Criteria andXAxisNotBetween(Integer value1, Integer value2) {
            addCriterion("x_axis not between", value1, value2, "xAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisIsNull() {
            addCriterion("y_axis is null");
            return (Criteria) this;
        }

        public Criteria andYAxisIsNotNull() {
            addCriterion("y_axis is not null");
            return (Criteria) this;
        }

        public Criteria andYAxisEqualTo(Integer value) {
            addCriterion("y_axis =", value, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisNotEqualTo(Integer value) {
            addCriterion("y_axis <>", value, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisGreaterThan(Integer value) {
            addCriterion("y_axis >", value, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisGreaterThanOrEqualTo(Integer value) {
            addCriterion("y_axis >=", value, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisLessThan(Integer value) {
            addCriterion("y_axis <", value, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisLessThanOrEqualTo(Integer value) {
            addCriterion("y_axis <=", value, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisIn(List<Integer> values) {
            addCriterion("y_axis in", values, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisNotIn(List<Integer> values) {
            addCriterion("y_axis not in", values, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisBetween(Integer value1, Integer value2) {
            addCriterion("y_axis between", value1, value2, "yAxis");
            return (Criteria) this;
        }

        public Criteria andYAxisNotBetween(Integer value1, Integer value2) {
            addCriterion("y_axis not between", value1, value2, "yAxis");
            return (Criteria) this;
        }

        public Criteria andWidthIsNull() {
            addCriterion("width is null");
            return (Criteria) this;
        }

        public Criteria andWidthIsNotNull() {
            addCriterion("width is not null");
            return (Criteria) this;
        }

        public Criteria andWidthEqualTo(Integer value) {
            addCriterion("width =", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotEqualTo(Integer value) {
            addCriterion("width <>", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThan(Integer value) {
            addCriterion("width >", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("width >=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThan(Integer value) {
            addCriterion("width <", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThanOrEqualTo(Integer value) {
            addCriterion("width <=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthIn(List<Integer> values) {
            addCriterion("width in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotIn(List<Integer> values) {
            addCriterion("width not in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthBetween(Integer value1, Integer value2) {
            addCriterion("width between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("width not between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andHeightIsNull() {
            addCriterion("height is null");
            return (Criteria) this;
        }

        public Criteria andHeightIsNotNull() {
            addCriterion("height is not null");
            return (Criteria) this;
        }

        public Criteria andHeightEqualTo(Integer value) {
            addCriterion("height =", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotEqualTo(Integer value) {
            addCriterion("height <>", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThan(Integer value) {
            addCriterion("height >", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("height >=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThan(Integer value) {
            addCriterion("height <", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThanOrEqualTo(Integer value) {
            addCriterion("height <=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightIn(List<Integer> values) {
            addCriterion("height in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotIn(List<Integer> values) {
            addCriterion("height not in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightBetween(Integer value1, Integer value2) {
            addCriterion("height between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("height not between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andRotateIsNull() {
            addCriterion("rotate is null");
            return (Criteria) this;
        }

        public Criteria andRotateIsNotNull() {
            addCriterion("rotate is not null");
            return (Criteria) this;
        }

        public Criteria andRotateEqualTo(Integer value) {
            addCriterion("rotate =", value, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateNotEqualTo(Integer value) {
            addCriterion("rotate <>", value, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateGreaterThan(Integer value) {
            addCriterion("rotate >", value, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateGreaterThanOrEqualTo(Integer value) {
            addCriterion("rotate >=", value, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateLessThan(Integer value) {
            addCriterion("rotate <", value, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateLessThanOrEqualTo(Integer value) {
            addCriterion("rotate <=", value, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateIn(List<Integer> values) {
            addCriterion("rotate in", values, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateNotIn(List<Integer> values) {
            addCriterion("rotate not in", values, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateBetween(Integer value1, Integer value2) {
            addCriterion("rotate between", value1, value2, "rotate");
            return (Criteria) this;
        }

        public Criteria andRotateNotBetween(Integer value1, Integer value2) {
            addCriterion("rotate not between", value1, value2, "rotate");
            return (Criteria) this;
        }

        public Criteria andOpacityIsNull() {
            addCriterion("opacity is null");
            return (Criteria) this;
        }

        public Criteria andOpacityIsNotNull() {
            addCriterion("opacity is not null");
            return (Criteria) this;
        }

        public Criteria andOpacityEqualTo(Integer value) {
            addCriterion("opacity =", value, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityNotEqualTo(Integer value) {
            addCriterion("opacity <>", value, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityGreaterThan(Integer value) {
            addCriterion("opacity >", value, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityGreaterThanOrEqualTo(Integer value) {
            addCriterion("opacity >=", value, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityLessThan(Integer value) {
            addCriterion("opacity <", value, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityLessThanOrEqualTo(Integer value) {
            addCriterion("opacity <=", value, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityIn(List<Integer> values) {
            addCriterion("opacity in", values, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityNotIn(List<Integer> values) {
            addCriterion("opacity not in", values, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityBetween(Integer value1, Integer value2) {
            addCriterion("opacity between", value1, value2, "opacity");
            return (Criteria) this;
        }

        public Criteria andOpacityNotBetween(Integer value1, Integer value2) {
            addCriterion("opacity not between", value1, value2, "opacity");
            return (Criteria) this;
        }

        public Criteria andSeqIsNull() {
            addCriterion("seq is null");
            return (Criteria) this;
        }

        public Criteria andSeqIsNotNull() {
            addCriterion("seq is not null");
            return (Criteria) this;
        }

        public Criteria andSeqEqualTo(Integer value) {
            addCriterion("seq =", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotEqualTo(Integer value) {
            addCriterion("seq <>", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThan(Integer value) {
            addCriterion("seq >", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("seq >=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThan(Integer value) {
            addCriterion("seq <", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThanOrEqualTo(Integer value) {
            addCriterion("seq <=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqIn(List<Integer> values) {
            addCriterion("seq in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotIn(List<Integer> values) {
            addCriterion("seq not in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqBetween(Integer value1, Integer value2) {
            addCriterion("seq between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("seq not between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNull() {
            addCriterion("image_url is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNotNull() {
            addCriterion("image_url is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlEqualTo(String value) {
            addCriterion("image_url =", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotEqualTo(String value) {
            addCriterion("image_url <>", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThan(String value) {
            addCriterion("image_url >", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("image_url >=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThan(String value) {
            addCriterion("image_url <", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThanOrEqualTo(String value) {
            addCriterion("image_url <=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLike(String value) {
            addCriterion("image_url like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotLike(String value) {
            addCriterion("image_url not like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlIn(List<String> values) {
            addCriterion("image_url in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotIn(List<String> values) {
            addCriterion("image_url not in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlBetween(String value1, String value2) {
            addCriterion("image_url between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotBetween(String value1, String value2) {
            addCriterion("image_url not between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNull() {
            addCriterion("image_md5 is null");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNotNull() {
            addCriterion("image_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andImageMd5EqualTo(String value) {
            addCriterion("image_md5 =", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotEqualTo(String value) {
            addCriterion("image_md5 <>", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThan(String value) {
            addCriterion("image_md5 >", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThanOrEqualTo(String value) {
            addCriterion("image_md5 >=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThan(String value) {
            addCriterion("image_md5 <", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThanOrEqualTo(String value) {
            addCriterion("image_md5 <=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Like(String value) {
            addCriterion("image_md5 like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotLike(String value) {
            addCriterion("image_md5 not like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5In(List<String> values) {
            addCriterion("image_md5 in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotIn(List<String> values) {
            addCriterion("image_md5 not in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Between(String value1, String value2) {
            addCriterion("image_md5 between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotBetween(String value1, String value2) {
            addCriterion("image_md5 not between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageWidthIsNull() {
            addCriterion("image_width is null");
            return (Criteria) this;
        }

        public Criteria andImageWidthIsNotNull() {
            addCriterion("image_width is not null");
            return (Criteria) this;
        }

        public Criteria andImageWidthEqualTo(Integer value) {
            addCriterion("image_width =", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthNotEqualTo(Integer value) {
            addCriterion("image_width <>", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthGreaterThan(Integer value) {
            addCriterion("image_width >", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_width >=", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthLessThan(Integer value) {
            addCriterion("image_width <", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthLessThanOrEqualTo(Integer value) {
            addCriterion("image_width <=", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthIn(List<Integer> values) {
            addCriterion("image_width in", values, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthNotIn(List<Integer> values) {
            addCriterion("image_width not in", values, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthBetween(Integer value1, Integer value2) {
            addCriterion("image_width between", value1, value2, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("image_width not between", value1, value2, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageHeightIsNull() {
            addCriterion("image_height is null");
            return (Criteria) this;
        }

        public Criteria andImageHeightIsNotNull() {
            addCriterion("image_height is not null");
            return (Criteria) this;
        }

        public Criteria andImageHeightEqualTo(Integer value) {
            addCriterion("image_height =", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightNotEqualTo(Integer value) {
            addCriterion("image_height <>", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightGreaterThan(Integer value) {
            addCriterion("image_height >", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_height >=", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightLessThan(Integer value) {
            addCriterion("image_height <", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightLessThanOrEqualTo(Integer value) {
            addCriterion("image_height <=", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightIn(List<Integer> values) {
            addCriterion("image_height in", values, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightNotIn(List<Integer> values) {
            addCriterion("image_height not in", values, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightBetween(Integer value1, Integer value2) {
            addCriterion("image_height between", value1, value2, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("image_height not between", value1, value2, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageLockIsNull() {
            addCriterion("image_lock is null");
            return (Criteria) this;
        }

        public Criteria andImageLockIsNotNull() {
            addCriterion("image_lock is not null");
            return (Criteria) this;
        }

        public Criteria andImageLockEqualTo(Integer value) {
            addCriterion("image_lock =", value, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockNotEqualTo(Integer value) {
            addCriterion("image_lock <>", value, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockGreaterThan(Integer value) {
            addCriterion("image_lock >", value, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_lock >=", value, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockLessThan(Integer value) {
            addCriterion("image_lock <", value, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockLessThanOrEqualTo(Integer value) {
            addCriterion("image_lock <=", value, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockIn(List<Integer> values) {
            addCriterion("image_lock in", values, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockNotIn(List<Integer> values) {
            addCriterion("image_lock not in", values, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockBetween(Integer value1, Integer value2) {
            addCriterion("image_lock between", value1, value2, "imageLock");
            return (Criteria) this;
        }

        public Criteria andImageLockNotBetween(Integer value1, Integer value2) {
            addCriterion("image_lock not between", value1, value2, "imageLock");
            return (Criteria) this;
        }

        public Criteria andBgColorIsNull() {
            addCriterion("bg_color is null");
            return (Criteria) this;
        }

        public Criteria andBgColorIsNotNull() {
            addCriterion("bg_color is not null");
            return (Criteria) this;
        }

        public Criteria andBgColorEqualTo(String value) {
            addCriterion("bg_color =", value, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorNotEqualTo(String value) {
            addCriterion("bg_color <>", value, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorGreaterThan(String value) {
            addCriterion("bg_color >", value, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorGreaterThanOrEqualTo(String value) {
            addCriterion("bg_color >=", value, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorLessThan(String value) {
            addCriterion("bg_color <", value, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorLessThanOrEqualTo(String value) {
            addCriterion("bg_color <=", value, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorLike(String value) {
            addCriterion("bg_color like", value, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorNotLike(String value) {
            addCriterion("bg_color not like", value, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorIn(List<String> values) {
            addCriterion("bg_color in", values, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorNotIn(List<String> values) {
            addCriterion("bg_color not in", values, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorBetween(String value1, String value2) {
            addCriterion("bg_color between", value1, value2, "bgColor");
            return (Criteria) this;
        }

        public Criteria andBgColorNotBetween(String value1, String value2) {
            addCriterion("bg_color not between", value1, value2, "bgColor");
            return (Criteria) this;
        }

        public Criteria andTextValIsNull() {
            addCriterion("text_val is null");
            return (Criteria) this;
        }

        public Criteria andTextValIsNotNull() {
            addCriterion("text_val is not null");
            return (Criteria) this;
        }

        public Criteria andTextValEqualTo(String value) {
            addCriterion("text_val =", value, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValNotEqualTo(String value) {
            addCriterion("text_val <>", value, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValGreaterThan(String value) {
            addCriterion("text_val >", value, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValGreaterThanOrEqualTo(String value) {
            addCriterion("text_val >=", value, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValLessThan(String value) {
            addCriterion("text_val <", value, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValLessThanOrEqualTo(String value) {
            addCriterion("text_val <=", value, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValLike(String value) {
            addCriterion("text_val like", value, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValNotLike(String value) {
            addCriterion("text_val not like", value, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValIn(List<String> values) {
            addCriterion("text_val in", values, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValNotIn(List<String> values) {
            addCriterion("text_val not in", values, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValBetween(String value1, String value2) {
            addCriterion("text_val between", value1, value2, "textVal");
            return (Criteria) this;
        }

        public Criteria andTextValNotBetween(String value1, String value2) {
            addCriterion("text_val not between", value1, value2, "textVal");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdIsNull() {
            addCriterion("font_family_id is null");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdIsNotNull() {
            addCriterion("font_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdEqualTo(Integer value) {
            addCriterion("font_family_id =", value, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdNotEqualTo(Integer value) {
            addCriterion("font_family_id <>", value, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdGreaterThan(Integer value) {
            addCriterion("font_family_id >", value, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("font_family_id >=", value, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdLessThan(Integer value) {
            addCriterion("font_family_id <", value, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdLessThanOrEqualTo(Integer value) {
            addCriterion("font_family_id <=", value, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdIn(List<Integer> values) {
            addCriterion("font_family_id in", values, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdNotIn(List<Integer> values) {
            addCriterion("font_family_id not in", values, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdBetween(Integer value1, Integer value2) {
            addCriterion("font_family_id between", value1, value2, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andFontFamilyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("font_family_id not between", value1, value2, "fontFamilyId");
            return (Criteria) this;
        }

        public Criteria andTextColorIsNull() {
            addCriterion("text_color is null");
            return (Criteria) this;
        }

        public Criteria andTextColorIsNotNull() {
            addCriterion("text_color is not null");
            return (Criteria) this;
        }

        public Criteria andTextColorEqualTo(String value) {
            addCriterion("text_color =", value, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorNotEqualTo(String value) {
            addCriterion("text_color <>", value, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorGreaterThan(String value) {
            addCriterion("text_color >", value, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorGreaterThanOrEqualTo(String value) {
            addCriterion("text_color >=", value, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorLessThan(String value) {
            addCriterion("text_color <", value, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorLessThanOrEqualTo(String value) {
            addCriterion("text_color <=", value, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorLike(String value) {
            addCriterion("text_color like", value, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorNotLike(String value) {
            addCriterion("text_color not like", value, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorIn(List<String> values) {
            addCriterion("text_color in", values, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorNotIn(List<String> values) {
            addCriterion("text_color not in", values, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorBetween(String value1, String value2) {
            addCriterion("text_color between", value1, value2, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextColorNotBetween(String value1, String value2) {
            addCriterion("text_color not between", value1, value2, "textColor");
            return (Criteria) this;
        }

        public Criteria andTextAlignIsNull() {
            addCriterion("text_align is null");
            return (Criteria) this;
        }

        public Criteria andTextAlignIsNotNull() {
            addCriterion("text_align is not null");
            return (Criteria) this;
        }

        public Criteria andTextAlignEqualTo(String value) {
            addCriterion("text_align =", value, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignNotEqualTo(String value) {
            addCriterion("text_align <>", value, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignGreaterThan(String value) {
            addCriterion("text_align >", value, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignGreaterThanOrEqualTo(String value) {
            addCriterion("text_align >=", value, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignLessThan(String value) {
            addCriterion("text_align <", value, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignLessThanOrEqualTo(String value) {
            addCriterion("text_align <=", value, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignLike(String value) {
            addCriterion("text_align like", value, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignNotLike(String value) {
            addCriterion("text_align not like", value, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignIn(List<String> values) {
            addCriterion("text_align in", values, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignNotIn(List<String> values) {
            addCriterion("text_align not in", values, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignBetween(String value1, String value2) {
            addCriterion("text_align between", value1, value2, "textAlign");
            return (Criteria) this;
        }

        public Criteria andTextAlignNotBetween(String value1, String value2) {
            addCriterion("text_align not between", value1, value2, "textAlign");
            return (Criteria) this;
        }

        public Criteria andFontStyleIsNull() {
            addCriterion("font_style is null");
            return (Criteria) this;
        }

        public Criteria andFontStyleIsNotNull() {
            addCriterion("font_style is not null");
            return (Criteria) this;
        }

        public Criteria andFontStyleEqualTo(String value) {
            addCriterion("font_style =", value, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleNotEqualTo(String value) {
            addCriterion("font_style <>", value, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleGreaterThan(String value) {
            addCriterion("font_style >", value, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleGreaterThanOrEqualTo(String value) {
            addCriterion("font_style >=", value, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleLessThan(String value) {
            addCriterion("font_style <", value, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleLessThanOrEqualTo(String value) {
            addCriterion("font_style <=", value, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleLike(String value) {
            addCriterion("font_style like", value, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleNotLike(String value) {
            addCriterion("font_style not like", value, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleIn(List<String> values) {
            addCriterion("font_style in", values, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleNotIn(List<String> values) {
            addCriterion("font_style not in", values, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleBetween(String value1, String value2) {
            addCriterion("font_style between", value1, value2, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontStyleNotBetween(String value1, String value2) {
            addCriterion("font_style not between", value1, value2, "fontStyle");
            return (Criteria) this;
        }

        public Criteria andFontSizeIsNull() {
            addCriterion("font_size is null");
            return (Criteria) this;
        }

        public Criteria andFontSizeIsNotNull() {
            addCriterion("font_size is not null");
            return (Criteria) this;
        }

        public Criteria andFontSizeEqualTo(Integer value) {
            addCriterion("font_size =", value, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeNotEqualTo(Integer value) {
            addCriterion("font_size <>", value, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeGreaterThan(Integer value) {
            addCriterion("font_size >", value, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("font_size >=", value, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeLessThan(Integer value) {
            addCriterion("font_size <", value, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeLessThanOrEqualTo(Integer value) {
            addCriterion("font_size <=", value, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeIn(List<Integer> values) {
            addCriterion("font_size in", values, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeNotIn(List<Integer> values) {
            addCriterion("font_size not in", values, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeBetween(Integer value1, Integer value2) {
            addCriterion("font_size between", value1, value2, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("font_size not between", value1, value2, "fontSize");
            return (Criteria) this;
        }

        public Criteria andFontWeightIsNull() {
            addCriterion("font_weight is null");
            return (Criteria) this;
        }

        public Criteria andFontWeightIsNotNull() {
            addCriterion("font_weight is not null");
            return (Criteria) this;
        }

        public Criteria andFontWeightEqualTo(String value) {
            addCriterion("font_weight =", value, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightNotEqualTo(String value) {
            addCriterion("font_weight <>", value, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightGreaterThan(String value) {
            addCriterion("font_weight >", value, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightGreaterThanOrEqualTo(String value) {
            addCriterion("font_weight >=", value, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightLessThan(String value) {
            addCriterion("font_weight <", value, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightLessThanOrEqualTo(String value) {
            addCriterion("font_weight <=", value, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightLike(String value) {
            addCriterion("font_weight like", value, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightNotLike(String value) {
            addCriterion("font_weight not like", value, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightIn(List<String> values) {
            addCriterion("font_weight in", values, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightNotIn(List<String> values) {
            addCriterion("font_weight not in", values, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightBetween(String value1, String value2) {
            addCriterion("font_weight between", value1, value2, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andFontWeightNotBetween(String value1, String value2) {
            addCriterion("font_weight not between", value1, value2, "fontWeight");
            return (Criteria) this;
        }

        public Criteria andUnderlineIsNull() {
            addCriterion("underline is null");
            return (Criteria) this;
        }

        public Criteria andUnderlineIsNotNull() {
            addCriterion("underline is not null");
            return (Criteria) this;
        }

        public Criteria andUnderlineEqualTo(Integer value) {
            addCriterion("underline =", value, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineNotEqualTo(Integer value) {
            addCriterion("underline <>", value, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineGreaterThan(Integer value) {
            addCriterion("underline >", value, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineGreaterThanOrEqualTo(Integer value) {
            addCriterion("underline >=", value, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineLessThan(Integer value) {
            addCriterion("underline <", value, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineLessThanOrEqualTo(Integer value) {
            addCriterion("underline <=", value, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineIn(List<Integer> values) {
            addCriterion("underline in", values, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineNotIn(List<Integer> values) {
            addCriterion("underline not in", values, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineBetween(Integer value1, Integer value2) {
            addCriterion("underline between", value1, value2, "underline");
            return (Criteria) this;
        }

        public Criteria andUnderlineNotBetween(Integer value1, Integer value2) {
            addCriterion("underline not between", value1, value2, "underline");
            return (Criteria) this;
        }

        public Criteria andLinethroughIsNull() {
            addCriterion("linethrough is null");
            return (Criteria) this;
        }

        public Criteria andLinethroughIsNotNull() {
            addCriterion("linethrough is not null");
            return (Criteria) this;
        }

        public Criteria andLinethroughEqualTo(Integer value) {
            addCriterion("linethrough =", value, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughNotEqualTo(Integer value) {
            addCriterion("linethrough <>", value, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughGreaterThan(Integer value) {
            addCriterion("linethrough >", value, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughGreaterThanOrEqualTo(Integer value) {
            addCriterion("linethrough >=", value, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughLessThan(Integer value) {
            addCriterion("linethrough <", value, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughLessThanOrEqualTo(Integer value) {
            addCriterion("linethrough <=", value, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughIn(List<Integer> values) {
            addCriterion("linethrough in", values, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughNotIn(List<Integer> values) {
            addCriterion("linethrough not in", values, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughBetween(Integer value1, Integer value2) {
            addCriterion("linethrough between", value1, value2, "linethrough");
            return (Criteria) this;
        }

        public Criteria andLinethroughNotBetween(Integer value1, Integer value2) {
            addCriterion("linethrough not between", value1, value2, "linethrough");
            return (Criteria) this;
        }

        public Criteria andPatternIdIsNull() {
            addCriterion("pattern_id is null");
            return (Criteria) this;
        }

        public Criteria andPatternIdIsNotNull() {
            addCriterion("pattern_id is not null");
            return (Criteria) this;
        }

        public Criteria andPatternIdEqualTo(Integer value) {
            addCriterion("pattern_id =", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdNotEqualTo(Integer value) {
            addCriterion("pattern_id <>", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdGreaterThan(Integer value) {
            addCriterion("pattern_id >", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("pattern_id >=", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdLessThan(Integer value) {
            addCriterion("pattern_id <", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdLessThanOrEqualTo(Integer value) {
            addCriterion("pattern_id <=", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdIn(List<Integer> values) {
            addCriterion("pattern_id in", values, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdNotIn(List<Integer> values) {
            addCriterion("pattern_id not in", values, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdBetween(Integer value1, Integer value2) {
            addCriterion("pattern_id between", value1, value2, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdNotBetween(Integer value1, Integer value2) {
            addCriterion("pattern_id not between", value1, value2, "patternId");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andWorksIdIsNull() {
            addCriterion("works_id is null");
            return (Criteria) this;
        }

        public Criteria andWorksIdIsNotNull() {
            addCriterion("works_id is not null");
            return (Criteria) this;
        }

        public Criteria andWorksIdEqualTo(Integer value) {
            addCriterion("works_id =", value, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdNotEqualTo(Integer value) {
            addCriterion("works_id <>", value, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdGreaterThan(Integer value) {
            addCriterion("works_id >", value, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("works_id >=", value, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdLessThan(Integer value) {
            addCriterion("works_id <", value, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdLessThanOrEqualTo(Integer value) {
            addCriterion("works_id <=", value, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdIn(List<Integer> values) {
            addCriterion("works_id in", values, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdNotIn(List<Integer> values) {
            addCriterion("works_id not in", values, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdBetween(Integer value1, Integer value2) {
            addCriterion("works_id between", value1, value2, "worksId");
            return (Criteria) this;
        }

        public Criteria andWorksIdNotBetween(Integer value1, Integer value2) {
            addCriterion("works_id not between", value1, value2, "worksId");
            return (Criteria) this;
        }

        public Criteria andTextCrossIsNull() {
            addCriterion("text_cross is null");
            return (Criteria) this;
        }

        public Criteria andTextCrossIsNotNull() {
            addCriterion("text_cross is not null");
            return (Criteria) this;
        }

        public Criteria andTextCrossEqualTo(Integer value) {
            addCriterion("text_cross =", value, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossNotEqualTo(Integer value) {
            addCriterion("text_cross <>", value, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossGreaterThan(Integer value) {
            addCriterion("text_cross >", value, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossGreaterThanOrEqualTo(Integer value) {
            addCriterion("text_cross >=", value, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossLessThan(Integer value) {
            addCriterion("text_cross <", value, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossLessThanOrEqualTo(Integer value) {
            addCriterion("text_cross <=", value, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossIn(List<Integer> values) {
            addCriterion("text_cross in", values, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossNotIn(List<Integer> values) {
            addCriterion("text_cross not in", values, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossBetween(Integer value1, Integer value2) {
            addCriterion("text_cross between", value1, value2, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextCrossNotBetween(Integer value1, Integer value2) {
            addCriterion("text_cross not between", value1, value2, "textCross");
            return (Criteria) this;
        }

        public Criteria andTextOrientIsNull() {
            addCriterion("text_orient is null");
            return (Criteria) this;
        }

        public Criteria andTextOrientIsNotNull() {
            addCriterion("text_orient is not null");
            return (Criteria) this;
        }

        public Criteria andTextOrientEqualTo(Integer value) {
            addCriterion("text_orient =", value, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientNotEqualTo(Integer value) {
            addCriterion("text_orient <>", value, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientGreaterThan(Integer value) {
            addCriterion("text_orient >", value, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientGreaterThanOrEqualTo(Integer value) {
            addCriterion("text_orient >=", value, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientLessThan(Integer value) {
            addCriterion("text_orient <", value, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientLessThanOrEqualTo(Integer value) {
            addCriterion("text_orient <=", value, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientIn(List<Integer> values) {
            addCriterion("text_orient in", values, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientNotIn(List<Integer> values) {
            addCriterion("text_orient not in", values, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientBetween(Integer value1, Integer value2) {
            addCriterion("text_orient between", value1, value2, "textOrient");
            return (Criteria) this;
        }

        public Criteria andTextOrientNotBetween(Integer value1, Integer value2) {
            addCriterion("text_orient not between", value1, value2, "textOrient");
            return (Criteria) this;
        }

        public Criteria andShadowXIsNull() {
            addCriterion("shadow_x is null");
            return (Criteria) this;
        }

        public Criteria andShadowXIsNotNull() {
            addCriterion("shadow_x is not null");
            return (Criteria) this;
        }

        public Criteria andShadowXEqualTo(Integer value) {
            addCriterion("shadow_x =", value, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXNotEqualTo(Integer value) {
            addCriterion("shadow_x <>", value, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXGreaterThan(Integer value) {
            addCriterion("shadow_x >", value, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXGreaterThanOrEqualTo(Integer value) {
            addCriterion("shadow_x >=", value, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXLessThan(Integer value) {
            addCriterion("shadow_x <", value, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXLessThanOrEqualTo(Integer value) {
            addCriterion("shadow_x <=", value, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXIn(List<Integer> values) {
            addCriterion("shadow_x in", values, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXNotIn(List<Integer> values) {
            addCriterion("shadow_x not in", values, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXBetween(Integer value1, Integer value2) {
            addCriterion("shadow_x between", value1, value2, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowXNotBetween(Integer value1, Integer value2) {
            addCriterion("shadow_x not between", value1, value2, "shadowX");
            return (Criteria) this;
        }

        public Criteria andShadowYIsNull() {
            addCriterion("shadow_y is null");
            return (Criteria) this;
        }

        public Criteria andShadowYIsNotNull() {
            addCriterion("shadow_y is not null");
            return (Criteria) this;
        }

        public Criteria andShadowYEqualTo(Integer value) {
            addCriterion("shadow_y =", value, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYNotEqualTo(Integer value) {
            addCriterion("shadow_y <>", value, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYGreaterThan(Integer value) {
            addCriterion("shadow_y >", value, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYGreaterThanOrEqualTo(Integer value) {
            addCriterion("shadow_y >=", value, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYLessThan(Integer value) {
            addCriterion("shadow_y <", value, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYLessThanOrEqualTo(Integer value) {
            addCriterion("shadow_y <=", value, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYIn(List<Integer> values) {
            addCriterion("shadow_y in", values, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYNotIn(List<Integer> values) {
            addCriterion("shadow_y not in", values, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYBetween(Integer value1, Integer value2) {
            addCriterion("shadow_y between", value1, value2, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowYNotBetween(Integer value1, Integer value2) {
            addCriterion("shadow_y not between", value1, value2, "shadowY");
            return (Criteria) this;
        }

        public Criteria andShadowBlurIsNull() {
            addCriterion("shadow_blur is null");
            return (Criteria) this;
        }

        public Criteria andShadowBlurIsNotNull() {
            addCriterion("shadow_blur is not null");
            return (Criteria) this;
        }

        public Criteria andShadowBlurEqualTo(Integer value) {
            addCriterion("shadow_blur =", value, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurNotEqualTo(Integer value) {
            addCriterion("shadow_blur <>", value, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurGreaterThan(Integer value) {
            addCriterion("shadow_blur >", value, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurGreaterThanOrEqualTo(Integer value) {
            addCriterion("shadow_blur >=", value, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurLessThan(Integer value) {
            addCriterion("shadow_blur <", value, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurLessThanOrEqualTo(Integer value) {
            addCriterion("shadow_blur <=", value, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurIn(List<Integer> values) {
            addCriterion("shadow_blur in", values, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurNotIn(List<Integer> values) {
            addCriterion("shadow_blur not in", values, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurBetween(Integer value1, Integer value2) {
            addCriterion("shadow_blur between", value1, value2, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowBlurNotBetween(Integer value1, Integer value2) {
            addCriterion("shadow_blur not between", value1, value2, "shadowBlur");
            return (Criteria) this;
        }

        public Criteria andShadowWidthIsNull() {
            addCriterion("shadow_width is null");
            return (Criteria) this;
        }

        public Criteria andShadowWidthIsNotNull() {
            addCriterion("shadow_width is not null");
            return (Criteria) this;
        }

        public Criteria andShadowWidthEqualTo(Integer value) {
            addCriterion("shadow_width =", value, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthNotEqualTo(Integer value) {
            addCriterion("shadow_width <>", value, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthGreaterThan(Integer value) {
            addCriterion("shadow_width >", value, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("shadow_width >=", value, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthLessThan(Integer value) {
            addCriterion("shadow_width <", value, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthLessThanOrEqualTo(Integer value) {
            addCriterion("shadow_width <=", value, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthIn(List<Integer> values) {
            addCriterion("shadow_width in", values, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthNotIn(List<Integer> values) {
            addCriterion("shadow_width not in", values, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthBetween(Integer value1, Integer value2) {
            addCriterion("shadow_width between", value1, value2, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("shadow_width not between", value1, value2, "shadowWidth");
            return (Criteria) this;
        }

        public Criteria andShadowColorIsNull() {
            addCriterion("shadow_color is null");
            return (Criteria) this;
        }

        public Criteria andShadowColorIsNotNull() {
            addCriterion("shadow_color is not null");
            return (Criteria) this;
        }

        public Criteria andShadowColorEqualTo(String value) {
            addCriterion("shadow_color =", value, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorNotEqualTo(String value) {
            addCriterion("shadow_color <>", value, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorGreaterThan(String value) {
            addCriterion("shadow_color >", value, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorGreaterThanOrEqualTo(String value) {
            addCriterion("shadow_color >=", value, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorLessThan(String value) {
            addCriterion("shadow_color <", value, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorLessThanOrEqualTo(String value) {
            addCriterion("shadow_color <=", value, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorLike(String value) {
            addCriterion("shadow_color like", value, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorNotLike(String value) {
            addCriterion("shadow_color not like", value, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorIn(List<String> values) {
            addCriterion("shadow_color in", values, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorNotIn(List<String> values) {
            addCriterion("shadow_color not in", values, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorBetween(String value1, String value2) {
            addCriterion("shadow_color between", value1, value2, "shadowColor");
            return (Criteria) this;
        }

        public Criteria andShadowColorNotBetween(String value1, String value2) {
            addCriterion("shadow_color not between", value1, value2, "shadowColor");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}