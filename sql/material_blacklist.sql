CREATE TABLE `mgk_material_blacklist`
(
    `id`             bigint(20)    NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `target_id`      varchar(100)  NOT NULL DEFAULT '' COMMENT '黑名单id',
    `target_type`    varchar(50)   NOT NULL DEFAULT '' COMMENT '黑名单id类型，如customer, ',
    `operator`       varchar(50)   NOT NULL DEFAULT '' COMMENT '操作人',
    `blacklist_type` varchar(50)   NOT NULL DEFAULT '' COMMENT '黑明单类型, 如forbid，protect',
    `extra`          varchar(1500) NOT NULL DEFAULT '' COMMENT '额外参数',
    `ctime`          datetime      NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`          datetime      NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`        tinyint(4)    NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_blacklist_type` (`blacklist_type`) USING BTREE,
    KEY `idx_target_type` (`target_type`) USING BTREE,
    KEY `idx_target_id` (`target_id`) USING BTREE,
    UNIQUE KEY `idx_black_target_type_id` (`blacklist_type`, `target_type`, `target_id`),
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='创意素材中心黑名单';



