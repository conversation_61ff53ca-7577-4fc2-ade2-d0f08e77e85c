package com.bilibili.mgk.platform.common.utils;

import com.bilibili.adp.common.util.Utils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/9/29 下午7:45
 */
public class NumberUtils {

    public static boolean isPositive(Number number) {
        return goe(number, 1);
    }

    public static boolean isNonNegative(Number number) {
        return goe(number, 0);
    }

    public static boolean goe(Number number, Number anchor) {
        if (Objects.isNull(number) || Objects.isNull(anchor)) return false;

        return number.longValue() >= anchor.longValue();
    }

    /**
     * 最大公约数
     *
     * @param a
     * @param b
     * @return
     */
    public static int gcd(Integer a, Integer b) {
        return BigInteger.valueOf(a).gcd(BigInteger.valueOf(b)).intValue();
    }

    public static boolean integer2Boolean(Integer a) {
        return isPositive(a);
    }

    public static int boolean2Integer(Boolean a) {
        if (Objects.isNull(a) || !a) return 0;

        return 1;
    }

    public static Timestamp zeroValueTimestamp() {
        return new Timestamp(1);
    }

    public static boolean isZeroValueTimestamp(Timestamp timestamp) {
        return timestamp.getTime() < 3600L;
    }

    public static Double longDivideLong(Long orig, Long divisor) {
        if(!Utils.isPositive(orig) || !Utils.isPositive(divisor)){
            return 0.0;
        }
        return new BigDecimal(orig).divide(new BigDecimal(divisor),
                4, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}


