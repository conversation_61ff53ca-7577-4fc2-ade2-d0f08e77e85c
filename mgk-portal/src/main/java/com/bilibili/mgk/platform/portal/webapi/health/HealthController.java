package com.bilibili.mgk.platform.portal.webapi.health;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName HealthController
 * <AUTHOR>
 * @Date 2022/6/28 5:12 下午
 * @Version 1.0
 **/
@RestController
@RequestMapping(value = "/")
public class HealthController {

    @ResponseBody
    @RequestMapping(value = "/checkhealth.do", method = RequestMethod.GET, produces = "text/plain")
    public String checkHealth() {
        return  "server is health";
    }
}
