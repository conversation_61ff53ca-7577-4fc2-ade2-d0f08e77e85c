package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * MgkThirdPartyLandingPageQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class MgkThirdPartyLandingPageQueryDSLPo {

    private Integer accountId;

    private String creator;

    private java.sql.Timestamp ctime;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Long pageId;

    private String pageName;

    private String pageUrl;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public String getPageName() {
        return pageName;
    }

    public void setPageName(String pageName) {
        this.pageName = pageName;
    }

    public String getPageUrl() {
        return pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", creator = " + creator + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", pageId = " + pageId + ", pageName = " + pageName + ", pageUrl = " + pageUrl;
    }

}

