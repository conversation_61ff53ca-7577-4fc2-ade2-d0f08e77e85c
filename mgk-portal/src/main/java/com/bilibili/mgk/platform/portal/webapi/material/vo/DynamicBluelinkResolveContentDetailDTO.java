package com.bilibili.mgk.platform.portal.webapi.material.vo;

import com.bilibili.mgk.material.center.service.bluelink.dto.DynamicBluelinkResolveContentDTO;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DynamicBluelinkResolveContentDetailDTO extends DynamicBluelinkResolveContentDTO implements SnakeCaseBody {


    @ApiModelProperty("安卓应用包")
    private AppPackageForLinkDTO androidAppPackage;


    @ApiModelProperty("ios应用包")
    private AppPackageForLinkDTO iosAppPackage;


    @ApiModelProperty("游戏信息")
    private GameInfoForLinkDTO game;


    @ApiModelProperty("资质列表，注意因为系统耦合的缘故本期不提供，使用额外的接口获取")
    private List<CreativeQualificationVo> qualifications;


    @Data
    @Accessors(chain = true)
    public static class AppPackageForLinkDTO implements SnakeCaseBody {


        /**
         * 应用包名称
         */
        @ApiModelProperty("应用包名称")
        private String name;

        /**
         * 应用包原始下载链接
         */
        @ApiModelProperty("应用包原始下载链接")
        private String url;

        /**
         * 应用包的包名称
         */
        @ApiModelProperty("应用包的包名称")
        private String packageName;

        /**
         * 应用名称
         */
        @ApiModelProperty("应用名称")
        private String appName;


    }


    @Data
    @Accessors(chain = true)
    public static class GameInfoForLinkDTO implements SnakeCaseBody {

        @ApiModelProperty("游戏id")
        private Integer gameBaseId;

        @ApiModelProperty("游戏名称")
        private String gameName;


        @ApiModelProperty("游戏icon")
        private String gameIcon;

    }
}
