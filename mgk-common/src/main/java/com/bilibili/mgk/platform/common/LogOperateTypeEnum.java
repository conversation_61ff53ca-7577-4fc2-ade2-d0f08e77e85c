package com.bilibili.mgk.platform.common;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum LogOperateTypeEnum {

    LANDING_PAGE_ADD(1, "新建落地页"),
    LANDING_PAGE_DELETED(2, "删除落地页"),
    LANDING_PAGE_MODIFY(3, "修改落地页"),
    LANDING_PAGE_COPY(4, "复制落地页"),
    LANDING_PAGE_PUBLISHED(5, "发布落地页"),
    LANDING_PAGE_DOWNLINE(6, "下线落地页"),
    LANDING_PAGE_REJECT(7, "管理员驳回落地页"),

    FORM_ADD(8, "新建表单"),
    FORM_DELETED(9, "删除表单"),
    FORM_MODIFY(10, "修改表单"),
    FORM_COPY(11, "复制表单"),

    FORM_DATA_ALLOW(20, "更新授权"),

    MODEL_ADD(31, "新建模型"),
    MODEL_DELETED(32, "删除模板"),
    MODEL_MODIFY(33, "修改模板"),
    MODEL_COPY(34, "复制模板"),
    MODEL_PUBLISHED(35, "发布模板"),
    MODEL_DOWNLINE(36, "下线模板"),
    MODEL_REJECT(37, "管理员驳回模板"),

    LANDING_PAGE_AUDIT_COMMIT(40, "落地页送审"),
    LANDING_PAGE_AUDIT_REJECT(41, "落地页审核拒绝"),
    LANDING_PAGE_AUDIT_PASS(42, "落地页审核通过");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static LogOperateTypeEnum getByCode(int code) {
        for (LogOperateTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code LogOperateTypeEnum " + code);
    }

}


