package com.bilibili.mgk.platform.api.landing_page_group.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName LandingPageGroupLogDto
 * <AUTHOR>
 * @Date 2023/5/31 5:16 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupLogDto {
    private Long groupId;
    private Integer operateType;
    private String operatorName;
    private Integer operatorId;
    private Integer operatorType;
    private String operateValue;
}
