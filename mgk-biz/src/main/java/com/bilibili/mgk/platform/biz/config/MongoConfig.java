package com.bilibili.mgk.platform.biz.config;

import com.mongodb.Mongo;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoCredential;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.mongodb.core.MongoClientFactoryBean;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * @file: MongoConfig
 * @author: gaoming
 * @date: 2021/03/02
 * @version: 1.0
 * @description:
 **/
@Configuration
@PropertySource(value = "classpath:mongo.properties")
@EnableMongoRepositories(basePackages = "com.bilibili.mgk.platform.biz.mongo.repositories")
public class MongoConfig {

    @Value("${mongo.host}")
    private String host;
    @Value("${mongo.port}")
    private int port;
    @Value("${mongo.dbname}")
    private String dbname;
    @Value("${mongo.username}")
    private String username;
    @Value("${mongo.password}")
    private String password;
    @Value("${mongo.connectionsPerHost}")
    private int connectionsPerHost;
    @Value("${mongo.threadsAllowedToBlockForConnectionMultiplier}")
    private int multiplier;
    @Value("${mongo.connectTimeout}")
    private int connectTimeout;
    @Value("${mongo.maxWaitTime}")
    private int maxWaitTime;
    @Value("${mongo.socketKeepAlive}")
    private boolean socketKeepAlive;
    @Value("${mongo.socketTimeout}")
    private int socketTimeout;

    @Bean
    public MongoClientFactoryBean mongo() {

        MongoClientOptions options = MongoClientOptions.builder()
                .threadsAllowedToBlockForConnectionMultiplier(multiplier)
                .connectionsPerHost(connectionsPerHost)
                .connectTimeout(connectTimeout)
                .maxWaitTime(maxWaitTime)
                .socketTimeout(socketTimeout)
                .build();

        MongoClientFactoryBean mongo = new MongoClientFactoryBean();
        MongoCredential[] mongoCredentials = new MongoCredential[1];
        mongoCredentials[0] = MongoCredential.createCredential(username, dbname, password.toCharArray());
        mongo.setHost(host);
        mongo.setPort(port);
        mongo.setCredentials(mongoCredentials);
        mongo.setMongoClientOptions(options);
        return mongo;
    }

    @Bean(value = "mongoTemplate")
    public MongoOperations mongoTemplate(Mongo mongo) {
        return new MongoTemplate((MongoClient) mongo, dbname);
    }
}
