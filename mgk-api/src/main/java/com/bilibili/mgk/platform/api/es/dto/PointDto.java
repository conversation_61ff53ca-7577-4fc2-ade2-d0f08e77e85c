package com.bilibili.mgk.platform.api.es.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: PointDto
 * @author: gaoming
 * @date: 2021/07/06
 * @version: 1.0
 * @description:
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PointDto implements Serializable {

    private static final long serialVersionUID = -8471371755319296736L;
    /**
     * 时间
     */
    private String time;
    /**
     * 值
     */
    private Long value;
    /**
     * 格式化
     */
    private String valueFormat;
}
