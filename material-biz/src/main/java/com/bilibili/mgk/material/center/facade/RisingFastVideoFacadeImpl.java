package com.bilibili.mgk.material.center.facade;

import com.bapis.datacenter.service.oneservice.AdvFilter;
import com.bapis.datacenter.service.oneservice.OperatorVo;
import com.bapis.datacenter.service.oneservice.OsHeader;
import com.bapis.datacenter.service.oneservice.PageReq;
import com.bapis.datacenter.service.oneservice.PageVo;
import com.bapis.datacenter.service.oneservice.QueryReq;
import com.bapis.datacenter.service.oneservice.QueryReq.Builder;
import com.bapis.datacenter.service.oneservice.QueryResp;
import com.bilibili.mgk.material.center.facade.converter.RisingFastVideoOnesGeneralDTO;
import com.bilibili.mgk.material.center.facade.proxy.OneServiceFlowControlProxy;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideo;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideoCurve;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery.DaihuoFilterType;
import com.bilibili.mgk.material.center.service.creative.vo.DaihuoChannel;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.PubTimeSection;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoCurveQuery;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoIdQuery;
import com.bilibili.mgk.material.center.service.creative.vo.RisingFastVideoQuery;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * rising fast video oneservice/metric(指标服务) facade
 * <p>
 * 该服务相较于其他的服务区别在 （1） 数据组提供hive数据日数据，指标服务每天增量同步ck (2） 指标服务查询层提供数据聚合能力；
 * <p>
 * 根据业务的需要，聚合成日、周、月的播放量增量倒序排序（榜单），累积倒序排序， 粉丝数倒序排序，发布时间倒序排序



 * ---经过和数据平台的讨论后，支持同步sql+sql查询模板的方式进行接入， 以下为支持动态聚合的查询模板 根据group_by_avid, group_by_log_date 决定聚合方式


 ------数据同步sql-------
 !!!一定一定要注意检查和修改新增字段的类型

 SELECT
 log_date,
 avid,
 avid_title,
 avid_cover,
 avid_content,
 avid_tag,
 avid_tid_name,
 is_vertical_screen,
 avid_duration,
 avid_pubtime,
 up_mid,
 up_name,
 verify_type,
 verify_type_desc,
 fans_daily,
 fans,
 play_daily,
 likes_daily,
 reply_daily,
 danmu_daily,
 coin_daily,
 fav_daily,
 share_daily,
 (likes_daily+ reply_daily + danmu_daily+  coin_daily + share_daily) as hudong_daily,
 play,
 likes,
 reply,
 danmu,
 coin,
 fav,
 share,
 (likes + reply + danmu + coin + share ) as hudong,
 commerce_category_first_name,
 product_name,
 item_id,
 item_image_url,
 item_name,
 item_source,
 first_category,
 place_type,
 avid_app_id,
 avid_app_url,
 avid_app_name,
 avid_ad_type,
 item_title,
 item_brand_name,
 original_price,
 current_price,
 item_jump_url,
 lower(COALESCE(up_name,'')) as lower_up_name,
 lower(COALESCE(item_name,'')) as lower_item_name,
 lower(COALESCE(item_title,'')) as lower_item_title,
 lower(COALESCE(item_brand_name,'')) as lower_item_brand_name,
 concat(lower(COALESCE(avid_title,'')) , lower(COALESCE(avid_content,'')) , lower(COALESCE(avid_tag,'')) )  as lower_video_search_word,
 concat(lower(COALESCE(up_name,'')) ,  lower(COALESCE(item_name,'')) , lower(COALESCE(item_brand_name,'')) ,  lower(COALESCE(avid_title,'')) , lower(COALESCE(avid_content,'')) , lower(COALESCE(avid_tag,'')) ) as lower_search_word
 FROM
 bi_sycpb.dws_ctnt_crea_center_ad_avid_analysis_data_i_d


 ------查询模板---- 9377795 数据任务

 SELECT
 <if test="group_by_avid !=null ">
 ${group_by_avid,type=string} as group_by_avid,
 </if>

 <if test="filter_by_having !=null ">
 ${filter_by_having,type=string} as filter_by_having,
 </if>

 <if test="curve_type !=null ">
 ${curve_type,type=string} as curve_type,
 </if>

 <if test="keyword_type !=null ">
 ${keyword_type,type=string} as keyword_type,
 </if>

 <if test="is_non_item_source !=null ">
 ${is_non_item_source,type=string} as is_non_item_source,
 </if>


 max(log_date) as max_log_date,

 min(log_date) as min_log_date,

 <if test="group_by_avid == 'true' ">
 any(avid) as max_avid,
 any(avid_title) as max_avid_title,
 any(avid_cover) as max_avid_cover,
 any(avid_content) as max_avid_content,
 any(avid_tag) as max_avid_tag,
 any(avid_tid_name) as max_avid_tid_name,
 any(is_vertical_screen) as max_is_vertical_screen ,
 any(avid_duration) as max_avid_duration,
 any(avid_pubtime) as max_avid_pubtime,
 any(up_mid) as max_up_mid,
 any(up_name) as max_up_name,
 any(verify_type) as max_verify_type,
 any(verify_type_desc) as max_verify_type_desc,
 </if>


 <if test="curve_type == null or curve_type == '' ">

 <if test=" group_by_avid != 'true'">
 count(distinct(up_mid)) as up_count_incr,
 count(distinct(avid)) as video_count_incr,
 </if>

 sum(fans_daily) as fans_incr,
 sum(play_daily) as play_incr,
 sum(likes_daily) as likes_incr,
 sum(reply_daily) as reply_incr,
 sum(danmu_daily) as danmu_incr,
 sum(coin_daily) as coin_incr,
 sum(fav_daily) as fav_incr,
 sum(share_daily) as share_incr,
 sum(hudong_daily) as hudong_incr,

 argMax(fans, log_date) as fans_acc,
 argMax(play, log_date) as play_acc,
 argMax(likes, log_date) as likes_acc,
 argMax(reply, log_date) as reply_acc,
 argMax(danmu, log_date) as danmu_acc,
 argMax(coin, log_date) as coin_acc,
 argMax(fav, log_date) as fav_acc,
 argMax(share, log_date) as share_acc,
 argMax(hudong, log_date)as hudong_acc,
 </if>


 <if test="curve_type == 'play' or curve_type == 'hudong_rate'  or curve_type == 'play_fans_rate'">
 sum(play_daily) as play_incr,
 argMax(play, log_date) as play_acc,
 </if>

 <if test="curve_type == 'likes' ">
 sum(likes_daily) as likes_incr,
 argMax(likes, log_date) as likes_acc,
 </if>


 <if test="curve_type == 'reply' ">
 sum(reply_daily) as reply_incr,
 argMax(reply, log_date) as reply_acc,
 </if>

 <if test="curve_type == 'danmu' ">
 sum(danmu_daily) as danmu_incr,
 argMax(danmu, log_date) as danmu_acc,
 </if>

 <if test="curve_type == 'coin' ">
 sum(coin_daily) as coin_incr,
 argMax(coin, log_date) as coin_acc,
 </if>

 <if test="curve_type == 'fav' ">
 sum(fav_daily) as fav_incr,
 argMax(fav, log_date) as fav_acc,
 </if>

 <if test="curve_type == 'share' ">
 sum(share_daily) as share_incr,
 argMax(share, log_date) as share_acc,
 </if>

 <if test="curve_type == 'hudong_rate' ">
 sum(hudong_daily) as hudong_incr,
 argMax(hudong, log_date) as hudong_acc,
 </if>


 <if test="curve_type == 'play_fans_rate' ">
 sum(fans_daily) as fans_incr,
 argMax(fans, log_date) as fans_acc,
 </if>


 <if test="curve_type == 'up_count' ">
 count(distinct(up_mid)) as up_count_incr,
 </if>

 <if test="curve_type == 'video_count' ">
 count(distinct(avid)) as video_count_incr,
 </if>

 any(commerce_category_first_name) as max_commerce_category_first_name,
 any(product_name) as max_product_name,
 any(item_id) as max_item_id,
 any(item_image_url) as  max_item_image_url,
 any(item_name) as max_item_name,
 any(item_source) as  max_item_source,
 any(item_title) as max_item_title,
 any(item_brand_name) as max_item_brand_name,
 any(original_price) as max_original_price,
 any(current_price) as max_current_price,
 any(item_jump_url) as max_item_jump_url,

 any(first_category) as max_first_category,
 any(place_type) as max_place_type,
 any(avid_app_id) as max_avid_app_id,
 any(avid_app_url) as max_avid_app_url,
 any(avid_app_name) as max_avid_app_name,
 any(avid_ad_type) as max_avid_ad_type

 FROM

 bili_oneservice.ads_oneservice_physics_build_2066_2 as t

 WHERE
 log_date &gt;= ${log_date_from,type=string} and log_date &lt;= ${log_date_to,type=string}
 <if test="keyword != null and keyword != '' and keyword_type != '' ">
 <if test="keyword_type == 'composite' ">
 and t.lower_search_word like '%${keyword,type=number}%'
 </if>
 <if test="keyword_type == 'up_name' ">
 and t.lower_up_name like '%${keyword,type=number}%'
 </if>
 <if test="keyword_type == 'item_name' ">
 and t.lower_item_name like '%${keyword,type=number}%'
 </if>
 <if test="keyword_type == 'brand_name' ">
 and t.lower_item_brand_name like '%${keyword,type=number}%'
 </if>
 <if test="keyword_type == 'video_composite' ">
 and t.lower_video_search_word like '%${keyword,type=number}%'
 </if>
 </if>

 <if test="avid_tid_names != null ">
 and t.avid_tid_name in ${avid_tid_names,type=array(string)}
 </if>


 <if test="item_source_eq != null ">
 and t.item_source = ${item_source_eq,type=string}
 </if>

 <if test="item_source_nin != null ">
 and avid_ad_type = 1  and t.item_source not in ${item_source_nin,type=array(string)}
 </if>

 <if test="avid_ad_type_eq != null ">
 and avid_ad_type = ${avid_ad_type_eq,type=number}
 </if>



 <if test="is_non_item_source != null ">
 and avid_ad_type != 1
 </if>


 <if test="daihuo_first_categories != null">
 and t.first_category in ${daihuo_first_categories,type=array(string)}
 </if>

 <if test="place_type != null">
 and t.place_type = ${place_type,type=string}
 </if>

 <if test="verify_type_desc != null">
 and t.verify_type_desc = ${verify_type_desc,type=string}
 </if>

 <if test='duration_from != null'>
 and t.avid_duration &gt; ${duration_from,type=number}
 </if>

 <if test='duration_to != null'>
 and t.avid_duration &lt;= ${duration_to,type=number}
 </if>

 <if test='is_vertical_screen != null'>
 and t.is_vertical_screen = ${is_vertical_screen,type=number}
 </if>

 <if test='avid_pubtime_gte != null'>
 and t.avid_pubtime &gt;= ${avid_pubtime_gte,type=string}
 </if>

 <if test='avid != null'>
 and t.avid = ${avid,type=string}
 </if>

 <if test='item_id != null'>
 and t.item_id = ${item_id,type=string}
 </if>


 group by
 <if test=" group_by_avid == 'true'"> avid </if>  <if test=" group_by_avid != 'true'"> item_id </if>
 <if test="curve_type != null and curve_type != '' "> , log_date</if>


 <if test="group_by_avid == 'true' and filter_by_having != null  ">
having 1=1
 <if test="play_from != null  ">
 and play_acc &gt;= ${play_from,type=number}
 </if>
 <if test="play_to != null  ">
 and play_acc &lt;= ${play_to,type=number}
 </if>

 <if test="likes_from != null  ">
 and likes_acc &gt;= ${likes_from,type=number}
 </if>
 <if test="likes_to != null  ">
 and likes_acc &lt;= ${likes_to,type=number}
 </if>


 <if test="reply_from != null  ">
 and reply_acc &gt;= ${reply_from,type=number}
 </if>
 <if test="reply_to != null  ">
 and reply_acc &lt;= ${reply_to,type=number}
 </if>

 </if>



 *
 * <AUTHOR>
 * @desc
 * @date 2024/6/24
 * @see www.tapd.cn/67874887/prong/stories/view/1167874887004288679
 */
@Service
public class RisingFastVideoFacadeImpl implements RisingFastVideoFacade {


    @Resource
    private OneServiceFlowControlProxy serviceOpenApiManagerBlockingStub;

    @Resource
    private ApiOneServiceBase apiOneServiceBase;

    private final DateTimeFormatter lastestUpdateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter logdateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    private final DateTimeFormatter pubTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    @Value("${material.oneservice.rising-fast-video.app-key:496b1dd505103413f42117ff1d5840ca}")
    private String appKey;

    @Value("${material.oneservice.rising-fast-video.secret:tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=}")
    private String secret;

    @Value("${material.oneservice.rising-fast-video.api-id:api_2204}")
    private String apiId;


    @Override
    public LocalDate apiDataReadyDate(){
        Tuple2<String, LocalDate> readyDate = apiOneServiceBase.findLatestOpenApiLogDateAndDate(apiId);

        return readyDate._2;
    }

    @Override
    public Optional<RisingFastVideoCurve> videoCurve(RisingFastVideoCurveQuery query) {

        Tuple2<Pagination<List<RisingFastVideo>>, LocalDate> page = this.videoPage(
                new RisingFastVideoQuery()
                        .setPn(1)
                        .setPs(1000)
                        // FIXME：查询的曲线字段可能不包含sort字段，避免sql报错允许比使用sort字段
                        .setSortBy(null)
                        .setAvid(query.getAvid())
                        .setDayType(query.getDayType())
                        .setPubtime(query.getPubtime())
                        .setQueryCurve(true)
                        .setCurveType(query.getCurveType())
        );

        return RisingFastVideoCurve.from(
                page._1.getData(),
                query.getCurveType(),
                lastestUpdateTimeFormatter.format(page._2)
        );

    }


    @Override
    public Optional<RisingFastVideo> videoGroupDetail(RisingFastVideoIdQuery query) {

        Tuple2<Pagination<List<RisingFastVideo>>, LocalDate> page = this.videoPage(
                new RisingFastVideoQuery()
                        .setPn(1)
                        .setPs(20)
                        .setSortBy(MaterialSortBy.play)
                        .setAvid(query.getAvid())
                        .setDayType(query.getDayType())
                        .setPubtime(query.getPubtime())

        );

        if (CollectionUtils.isEmpty(page._1.getData())) {
            return Optional.empty();
        }

        return Optional.of(page._1.getData().get(0));

    }


    /**
     * 分页查询飙升视频数据
     *
     * @param query
     * @return _1 result, _2 date ready log date
     */
    @Override
    public Tuple2<Pagination<List<RisingFastVideo>>, LocalDate> videoPage(RisingFastVideoQuery query) {

        Builder req = baseReq();

        List<AdvFilter.Builder> rootAdvFilterExpressions = new ArrayList<>();

        Tuple2<String, LocalDate> readyDate = apiOneServiceBase.findLatestOpenApiLogDateAndDate(apiId);

        this.selectAndGroupByFieldsOfVideoPage(req, query);

        Tuple2<LocalDate, LocalDate> logdateBetween =
                this.whereLogdateIn(req, rootAdvFilterExpressions, query, readyDate._2);

        this.whereOtherVideoFilterConditionMatch(req, rootAdvFilterExpressions, query);

        this.havingPlaysLikesReplyCntMatchCondition(req, query);

        this.orderByAndPage(req,query);

        QueryResp rsp = serviceOpenApiManagerBlockingStub.query(req.build());

        String dataReadyTime = lastestUpdateTimeFormatter.format(readyDate._2);
        String logdateGroupingFrom = lastestUpdateTimeFormatter.format(logdateBetween._1);
        String logdateGroupingTo = lastestUpdateTimeFormatter.format(logdateBetween._2);

        PageVo page = rsp.getPageVo();
        List<RisingFastVideo> result = rsp.getRowsList().stream()
                .map(map -> {
                    RisingFastVideo r = RisingFastVideoOnesGeneralDTO.fromMapValue(map.getValueMap())
                            .toVideoGroupInfo()
                            .toBO()
                            .setDataReadyTime(dataReadyTime)
                            .setLogdateGroupingFrom(logdateGroupingFrom)
                            .setLogdateGroupingTo(logdateGroupingTo);

                    r.setParsedLogDate(Try.of(() -> LocalDate.parse(r.getLogDate(), logdateFormatter))
                            .getOrNull());

                    return r;
                })
                .collect(Collectors.toList());

        return Tuple.of(new Pagination<>(page.getPage(), (int) page.getTotalSize(), result), readyDate._2);


    }


    private void orderByAndPage(Builder req, RisingFastVideoQuery query) {
        req.setPageReq(PageReq.newBuilder()
                .setPage(query.getPn())
                .setPageSize(query.getPs())
                .build());

        if (query.getSortBy() != null) {
            req.addAllOrders(Lists.newArrayList(
                    query.getSortBy().toRisingFastVideoOrderDesc()
            ));
        }


    }


    private void selectAndGroupByFieldsOfVideoPage(Builder req, RisingFastVideoQuery query) {

        req.addReqs(OperatorVo.newBuilder()
                .setField("group_by_avid")
                .setOperator("=")
                .addValues("true")
                .build());
        if(query.isQueryCurve()) {
            req.addReqs(OperatorVo.newBuilder()
                    .setField("curve_type")
                    .setOperator("=")
                    .addValues(query.getCurveType().name())
                    .build());
        }


    }




    private Tuple2<LocalDate, LocalDate> whereLogdateIn(Builder req,
            List<AdvFilter.Builder> advFilters,
            RisingFastVideoQuery query,
            LocalDate readyLogdate) {

        Tuple2<LocalDate, LocalDate> logdateBetween = query.fetchDayTimeRange(readyLogdate, query.getPubtime());

        this.appendAndParams(req,"log_date_from", "=", logdateFormatter.format(logdateBetween._1), advFilters);
        this.appendAndParams(req, "log_date_to", "=", logdateFormatter.format(logdateBetween._2), advFilters);

        return logdateBetween;
    }




    private void whereOtherVideoFilterConditionMatch(Builder req, List<AdvFilter.Builder> advFilters,
            RisingFastVideoQuery query) {

        if (query.getKeywordType() != null && StringUtils.isNotEmpty(query.getKeyword())) {


            this.appendAndParams(req, "keyword_type", "=", query.getKeywordType().name(), advFilters);
            this.appendAndParams(req, "keyword", "=", query.getKeyword().toLowerCase(), advFilters);


        }

        if (StringUtils.isNotEmpty(query.getVideoFirstCategory())) {
            //  视频分区：稿件一级分区，默认全部，支持多选
            List<String> videoCategories = Splitter.on(",").splitToList(query.getVideoFirstCategory());
            this.appendAndParams(req, "avid_tid_names", "=", videoCategories, advFilters);
        }

        if (query.getDaihuoType() != null && query.getDaihuoType() != DaihuoFilterType.all) {

            if (query.getDaihuoType() == DaihuoFilterType.daihuo) {

                // 带货渠道： 全部、淘宝、天猫、京东，其他、无带货。默认全部
                DaihuoChannel channel = DaihuoChannel.fromDesc(query.getDaihuoChannelV2());

                if (channel == null || channel == DaihuoChannel.daihuo) {
                    this.appendAndParams(req, "avid_ad_type_eq", "=", "1", advFilters);
                } else {
                    switch (channel) {

                        case jd:
                        case pdd:
                        case taobao: {
                            this.appendAndParams(req, "item_source_eq", "=", channel.getCode().toString(), advFilters);
                            break;
                        }
                        case other: {
                            this.appendAndParams(req, "item_source_nin", "=",
                                    Stream.of(DaihuoChannel.jd, DaihuoChannel.pdd, DaihuoChannel.taobao)
                                            .map(item -> item.getCode().toString())
                                            .collect(Collectors.toList()), advFilters);
                            break;
                        }
                        case non_daihuo:
                        default: {
                            // 全部
                            throw new IllegalArgumentException(
                                    "选择带货的情况下，不支持筛选该渠道:" + query.getDaihuoChannelV2());
                        }
                    }
                }
            } else if (query.getDaihuoType() == DaihuoFilterType.non_daihuo) {
                this.appendAndParams(req, "is_non_item_source", "=", "true", advFilters);

            }
        }


        if (StringUtils.isNotEmpty(query.getDaihuoChannel())) {
            // 带货渠道： 全部、淘宝、天猫、京东，其他、无带货。默认全部
            DaihuoChannel channel = DaihuoChannel.fromDesc(query.getDaihuoChannel());

            if(channel != null){

                switch (channel) {

                    case jd:
                    case pdd:
                    case taobao: {
                        this.appendAndParams(req, "item_source_eq", "=", channel.getCode().toString(), advFilters);
                        break;
                    }
                    case other:{
                        this.appendAndParams(req, "item_source_nin", "=",
                                Stream.of(DaihuoChannel.jd, DaihuoChannel.pdd, DaihuoChannel.taobao)
                                        .map(item-> item.getCode().toString())
                                        .collect(Collectors.toList()), advFilters);
                        break;
                    }
                    case daihuo: {
                        this.appendAndParams(req, "avid_ad_type_eq", "=", "1", advFilters);
                        break;
                    }
                    case non_daihuo:{
                        this.appendAndParams(req, "is_non_item_source", "=", "true", advFilters);
                        break;
                    }
                    default: {
                        // 全部
                        break;
                    }
                }

            }

        }

        if (CollectionUtils.isNotEmpty(query.getCommerceFirstCategoryNames())) {
            // TODO 查询非带货行业

            this.appendAndParams(req,
                    "commerce_category_first_names", "=", query.getCommerceFirstCategoryNames(), advFilters);
        }






        if (StringUtils.isNotEmpty(query.getDaihuoFirstCategory())) {
            //食品饮料, 3C数码家电
            //确认下是否多选

            this.appendAndParams(req,"daihuo_first_categories", "=",
                    Splitter.on(",").splitToList(query.getDaihuoFirstCategory()), advFilters);

        }

        if (query.getDaihuoMethod() != null) {

            this.appendAndParams(req,"place_type", "=", query.getDaihuoMethod().getDesc(), advFilters);

        }

        if (query.getUpType() != null) {

            // verifyType
            this.appendAndParams(req,"verify_type_desc", "=", query.getUpType().getDesc(), advFilters);

        }

        if (query.getDurationSection() != null) {


            // 时长
            if(query.getDurationSection().getMinExclusive()!= null){
                this.appendAndParams(req, "duration_from", "=",
                        query.getDurationSection().getMinExclusive().toString(), advFilters);
            }

            if (query.getDurationSection().getMaxInclusive() != null) {
                this.appendAndParams(req, "duration_to", "=",
                        query.getDurationSection().getMaxInclusive().toString(), advFilters);

            }


        }

        if (query.getIsVerticalScreen() != null) {
            this.appendAndParams(req,"is_vertical_screen", "=", query.getIsVerticalScreen().toString(), advFilters);
        }

        if (query.getPublishTimeSection() != null && query.getPublishTimeSection() != PubTimeSection.all) {
            // 这里直接用格式化时间 精确到秒

            String pubtimeStartFrom = pubTimeFormatter.format(LocalDateTime.now().minusDays(
                    query.getPublishTimeSection().getOffset()
            ));
            this.appendAndParams(req, "avid_pubtime_gte", "=", pubtimeStartFrom, advFilters);
        }

        if (query.getAvid() != null) {
            this.appendAndParams(req,"avid", "=", query.getAvid().toString(), advFilters);
        }

        if (StringUtils.isNotEmpty(query.getItemId())) {
            this.appendAndParams(req,"item_id", "=", query.getItemId(), advFilters);
        }


    }


    /**
     * 播放量、点赞数、评论数，三者均支持区间筛选，默认不限
     * <p>
     * 注意这是对聚合后数据的筛选，而不是对原始数据筛选
     *
     * @param req
     * @param query
     */
    private void havingPlaysLikesReplyCntMatchCondition(Builder req, RisingFastVideoQuery query) {

        List<OperatorVo> having = new ArrayList<>();

        if (query.getPlayFrom() != null) {

            having.add(OperatorVo.newBuilder()
                    .setField("play_from")
                    .setOperator("=")
                    .addValues(query.getPlayFrom().toString())
                    .build());


        }

        if (query.getPlayTo() != null) {
            having.add(OperatorVo.newBuilder()
                    .setField("play_to")
                    .setOperator("=")
                    .addValues(query.getPlayTo().toString())
                    .build());
        }

        if (query.getLikeFrom() != null) {
            having.add(OperatorVo.newBuilder()
                    .setField("likes_from")
                    .setOperator("=")
                    .addValues(query.getLikeFrom().toString())
                    .build());
        }

        if (query.getLikeTo() != null) {
            having.add(OperatorVo.newBuilder()
                    .setField("likes_to")
                    .setOperator("=")
                    .addValues(query.getLikeTo().toString())
                    .build());
        }

        if (query.getReplyFrom() != null) {
            having.add(OperatorVo.newBuilder()
                    .setField("reply_from")
                    .setOperator("=")
                    .addValues(query.getReplyFrom().toString())
                    .build());
        }

        if (query.getReplyTo() != null) {
            having.add(OperatorVo.newBuilder()
                    .setField("reply_to")
                    .setOperator("=")
                    .addValues(query.getReplyTo().toString())
                    .build());
        }

        if (having.size() > 0) {
            having.add(OperatorVo.newBuilder()
                    .setField("filter_by_having")
                    .setOperator("=")
                    .addValues("true")
                    .build());

            req.addAllReqs(having);
        }



    }


    /**
     * @return
     */
    private Builder baseReq() {

        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey(appKey)
                .setSecret(secret)
                .setApiId(apiId)
                .build();

        Builder openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader);

        return openApiReq;

    }


    protected void appendAndParams(Builder req, String key, String op, String values,
            List<AdvFilter.Builder> rootAdvFilterExpressions) {

        this.appendAndParams(req,key, op, Lists.newArrayList(values), rootAdvFilterExpressions);
    }


    protected void appendAndParams(Builder req,String key, String op, List<String> values,
            List<AdvFilter.Builder> rootAdvFilterExpressions) {

        req.addReqs(OperatorVo.newBuilder()
                .setField(key)
                .setOperator(op)
                .addAllValues(values));
    }


}
