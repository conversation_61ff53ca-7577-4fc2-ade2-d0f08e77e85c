package com.bilibili.mgk.platform.api.account;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.account.dto.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年12月25日
 */
public interface IMgkAccountService {

    AccountProfileDto getAccountProfile(Integer accountId);

    void saveAccountProfile(AccountProfileDto accountProfileDto, Operator operator);

    /**
     * 刷新mgk账户相关缓存 不允许自动过期
     *
     */
    void refreshMgkAccountCustomerInfoData();

    /**
     * 根据accountId获取客户名称 隐私协议中使用
     *
     * @param accountId 帐户id
     * @return {@link AccountCustomerNameInfoDto}
     */
    AccountCustomerNameInfoDto getAccountCustomerNameInfo(Integer accountId);

    /**
     * 根据accountId获取客户id&产品id 反作弊中使用
     *
     * @param accountId 帐户id
     * @return {@link AccountCustomerInfoDto}
     */
    AccountCustomerInfoDto getAccountCustomerInfo(Integer accountId);

    /**
     * 根据accountId检查客户是否打上了此标签
     *
     * @param accountId 帐户id
     * @param accountLabelCode 帐户标签枚举id @See com.bilibili.mgk.platform.common.MgkAccountLabelEnum
     * @return 是否打上了此标签 0-无标签 1-有标签
     */
    Integer checkAccountLabel(Integer accountId, Integer accountLabelCode);

    /**
     * 通过DB查询根据accountId检查客户是否打上了此标签
     *
     * @param accountId 帐户id
     * @param accountLabelCode 帐户标签枚举id @See com.bilibili.mgk.platform.common.MgkAccountLabelEnum
     * @return 是否打上了此标签 true-有标签
     */
    Boolean checkAccountLabelFromDB(Integer accountId, Integer accountLabelCode);

    Map<Integer, String> getAccountNameMapByAccountIds(List<Integer> accountIds);

    /**
     * 检查帐户是否在下载组件限制白名单
     */
    Boolean checkAccountHasDownloadComponentLimitWhiteList(Integer accountId);

    CardDto getCard(String url);

    /**
     * 判断用户是否是内广用户
     *
     * @param accountId
     * @return
     */
    Boolean isInnerAccount(Integer accountId);

    /**
     * 更新用户信息
     *
     * @param operator
     * @param accountDto
     */
    void updateAccountInfo(Operator operator, AccAccountDto accountDto);

    long count(QueryAccountParam param);

    /**
     * 登录 查询账户 adp同方法
     */
    AccountDto getAccount(Integer accountId, List<Integer> userTypes);

    AccountDto getAccount(Integer accountId);

    List<AccountDto> getAccountDtosByMid(Long mid, List<Integer> userTypes) throws ServiceException;

    PageResult<AccountDto> getAccountDtosByPage(QueryAccAccountDto queryDto) throws ServiceException;

    Map<Integer, WalletDto> getWalletDtoMap(List<Integer> accountIds);

    List<AccountDto> list(QueryAccountParam param, int page, int pageSize);

    Optional<AccountDto> getAccountDtoById(Integer accountId);

    /**
     * 获取同集团同品牌的广告主账户列表
     *
     * @param queryDto
     * @return
     */
    List<SimpleAccountDto> querySameGroupAndBrandAccounts(AccountQueryDto queryDto);


    String getCustomerNameWithSecret(Integer customerId, String secret);

    String generateCustomerSecret(Integer customerId);

    String getCustomerNamePoById(Integer customerId);

    /**
     * 根据accountId查询唤醒app映射
     * @param accountId
     * @return
     */
    List<Integer> queryAwakenAppMapping(Integer accountId);
}
