package com.bilibili.mgk.platform.portal.webapi.account;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.bjcom.sso.SSOUserInfo;
import com.bilibili.bjcom.sso.SSOUtils;
import com.bilibili.commercialorder.api.session.enums.DomainLoginType;
import com.bilibili.commercialorder.api.session.service.dto.LoginInfoDto;
import com.bilibili.commercialorder.exception.CmOrderErrorCode;
import com.bilibili.commercialorder.soa.bid.ISoaPickupBidSessionService;
import com.bilibili.commercialorder.soa.session.service.ISoaPickupSessionInfoService;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.mgk.platform.api.account.IMgkAccountService;
import com.bilibili.mgk.platform.api.account.dto.AccountDto;
import com.bilibili.mgk.platform.api.session.IMgkSessionService;
import com.bilibili.mgk.platform.api.session.dto.MgkLoginInfoDto;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.bilibili.mgk.platform.common.enums.login.BusinessSourceEnum;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.exception.WebApiExceptionCode;
import com.bilibili.mgk.platform.portal.service.WebSessionService;
import com.bilibili.mgk.platform.portal.webapi.account.vo.MgkLoginInfo;
import com.bilibili.rbac.api.service.IPermissionService;
import com.dianping.cat.Cat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.internal.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * @ClassName SessionController
 * <AUTHOR>
 * @Date 2022/6/19 8:46 下午
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping(value = "/web_api/v1/session")
@Api(value = "/session", description = "登录相关")
public class SessionController extends BasicController {
    @Value("${mgk.cookie.max.age:86400}")
    private int cookieMaxAge;
    @Value("${mgk-platform.passport.refer.url}")
    private String passportReferUrl;

    @Autowired
    private IMgkSessionService sessionService;
    @Autowired
    private SystemType systemType;
    @Autowired
    private IPermissionService permissionService;
    @Autowired
    private Integer tenantId;

    @Autowired
    private ISoaPickupBidSessionService soaPickupBidSessionService;

    @Autowired
    private WebSessionService webSessionService;
    @Autowired
    private IMgkAccountService mgkAccountService;

    @Autowired
    private ISoaAccountLabelService accountLabelService;

    private static final String INTRANET_DOMAIN = ".bilibili.co";

    private final static Integer FORBID_LOGIN_MGK_LABEL_ID = 690;

    @ResponseBody
    @PostMapping(value = "/logout")
    public Response<Object> logout(@ApiIgnore Context context,
                                   HttpServletRequest request,
                                   HttpServletResponse response) {
        log.info("logout context: [{}], cookie:[{}]", JSONObject.toJSONString(context), JSONObject.toJSONString(request.getCookies()));
        if (context != null && OperatorType.ADVERTISERS.getCode() == context.getType()) {
            removeMasterStationCookie(request, response);
        }
        // 防止旧cookie无法登出
        this.removeAccessTokenCookie(response);
        webSessionService.clearRequiredInternalCookies(request, response);

        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "检查用户是否登录")
    @RequestMapping(value = "/login/check", method = RequestMethod.GET)
    public Response<Integer> checkLoginStatus(HttpServletRequest request){
        boolean validateState = false;
        try {
            validateState = webSessionService.validateSession(request);
        } catch (Exception e) {
            log.error("validateCookie.error:{}, cookie:{}", e, request.getHeader("Cookie"));
            Cat.logEvent("MGK_COOKIE_ERROR", "validateCookie.error");
        }
        return validateState ? Response.SUCCESS(1) : Response.SUCCESS(0);
    }

    @ResponseBody
    @ApiOperation(value = "检查号经营用户是否登录")
    @RequestMapping(value = "/login/business/tool/check", method = RequestMethod.GET)
    public Response<Integer> checkBusinessToolLoginStatus(HttpServletRequest request){
        boolean validateState = false;
        try {
            validateState = webSessionService.validateBusinessToolSession(request);
        } catch (Exception e) {
            log.error("checkBusinessToolLoginStatus.error:{}, cookie:{}", e, request.getHeader("Cookie"));
            Cat.logEvent("MGK_COOKIE_ERROR", "validateCookie.error");
        }
        return validateState ? Response.SUCCESS(1) : Response.SUCCESS(0);
    }

    @ApiOperation(value = "内网用户选择账户登录建站工具")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<MgkLoginInfo> loginMgkInternal(HttpServletRequest request, HttpServletResponse response, @RequestParam("account_id") Integer accountId) throws ServiceException {
        SSOUserInfo userInfo = SSOUtils.getUserInfo(request);
        if (null == userInfo) {
            return Response.FAIL(WebApiExceptionCode.UNAUTHORIZED);
        }
        AccountDto accountDto = mgkAccountService.getAccountDtoById(accountId)
                .orElseThrow(() -> new IllegalArgumentException("账户信息不存在"));

        Boolean forbidLogin = accountLabelService.checkAccountWithLabel(accountId,
                FORBID_LOGIN_MGK_LABEL_ID);
        if(forbidLogin){
            return Response.FAIL(WebApiExceptionCode.BLACK_ACCOUNT);
        }

        // 花火账户内网登录
        if (WhetherEnum.YES.getCode().equals(accountDto.getIsSupportPickup())) {
            DomainLoginType loginType = accountDto.getIsAgent() ? DomainLoginType.AGENT : DomainLoginType.ACCOUNT;
            String pickupToken = soaPickupBidSessionService.domainLogin(loginType, userInfo.getUserName(), null, accountDto.getAccountId().longValue());
            this.removeAccessTokenCookie(response);
            addCookie(pickupToken, response, MgkConstants.PICKUP_TOKEN, INTRANET_DOMAIN);
            return Response.SUCCESS(
                    MgkLoginInfo.builder()
                            .access_token(pickupToken)
                            .build());
        }

        boolean isAdmin = false;
        if (permissionService.checkPermission(tenantId, userInfo.getUserName(), "cpm_view_all_accountlist")) {
            isAdmin = true;
        }
        MgkLoginInfoDto loginInfoDto = sessionService.biliUserLogin(accountId, userInfo.getUserName(), isAdmin, systemType);
        String key = accountDto.getIsSupportCluePass().equals(WhetherEnum.YES.getCode()) ?
                MgkConstants.CLUE_PASS_TOKEN : MgkConstants.HTTP_TOKEN;
        this.removeAccessTokenCookie(response);
        webSessionService.clearRequiredInternalCookies(request, response);
        addCookie(loginInfoDto.getAccessToken(), response, key, INTRANET_DOMAIN);
        return Response.SUCCESS(MgkLoginInfo.builder().access_token(loginInfoDto.getAccessToken()).build());
    }

    @ApiOperation(value = "bili用户sso登录")
    @RequestMapping(value = "/login/sso", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<MgkLoginInfo> loginSso(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        MgkLoginInfoDto loginInfoDto = sessionService.ssoLogin(request.getHeader("Cookie"),
                BusinessSourceEnum.OTHER.getCode());
        AccountDto accountDto = mgkAccountService.getAccountDtoById(loginInfoDto.getAccountId())
                .orElseThrow(() -> new IllegalArgumentException("账户信息不存在"));

        Boolean forbidLogin = accountLabelService.checkAccountWithLabel(accountDto.getAccountId(),
                FORBID_LOGIN_MGK_LABEL_ID);
        if(forbidLogin){
            return Response.FAIL(WebApiExceptionCode.BLACK_ACCOUNT);
        }

        String key = accountDto.getIsSupportCluePass().equals(WhetherEnum.YES.getCode()) ?
                MgkConstants.CLUE_PASS_TOKEN : MgkConstants.HTTP_TOKEN;
        webSessionService.clearRequiredMasterStationCookies(request, response);
        this.removeAccessTokenCookie(response);
        webSessionService.clearRequiredInternalCookies(request, response);
        addAccessTokenCookie(loginInfoDto.getAccessToken(), key, response);
        return Response.SUCCESS(MgkLoginInfo.builder().access_token(loginInfoDto.getAccessToken()).build());
    }

    @ApiOperation(value = "号经营用户登录")
    @RequestMapping(value = "/business/tool/login", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<MgkLoginInfo> businessToolLoginSso(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        MgkLoginInfoDto loginInfoDto = sessionService.ssoLogin(request.getHeader("Cookie"),
                BusinessSourceEnum.BUSINESS_TOOL.getCode());
        AccountDto accountDto = mgkAccountService.getAccountDtoById(loginInfoDto.getAccountId())
                .orElseThrow(() -> new IllegalArgumentException("账户信息不存在"));

        Boolean forbidLogin = accountLabelService.checkAccountWithLabel(accountDto.getAccountId(),
                FORBID_LOGIN_MGK_LABEL_ID);
        if(forbidLogin){
            return Response.FAIL(WebApiExceptionCode.BLACK_ACCOUNT);
        }

        webSessionService.clearRequiredMasterStationCookies(request, response);
        this.removeAccessTokenCookie(response);
        webSessionService.clearRequiredInternalCookies(request, response);
        addAccessTokenCookie(loginInfoDto.getAccessToken(), MgkConstants.BUSINESS_TOOL_TOKEN, response);
        return Response.SUCCESS(MgkLoginInfo.builder().access_token(loginInfoDto.getAccessToken()).build());
    }

    private void addAccessTokenCookie(String accessToken, String key, HttpServletResponse response) {
        Cookie cookie = new Cookie(key, accessToken);
        cookie.setPath("/");
        cookie.setMaxAge(cookieMaxAge);
        cookie.setHttpOnly(true);
        response.addCookie(cookie);
    }

    private void addCookie(String accessToken, HttpServletResponse response, String key, String domain) {
        Cookie cookie = new Cookie(key, accessToken);
        cookie.setPath("/");
        cookie.setMaxAge(cookieMaxAge);
        cookie.setHttpOnly(true);
        cookie.setDomain(domain);
        response.addCookie(cookie);
    }

    private void removeMasterStationCookie(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            cookie.setPath("/");
            cookie.setMaxAge(0);
            cookie.setValue(null);
            cookie.setDomain(".bilibili.com");
            response.addCookie(cookie);
        }
        response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
        response.setHeader("Location", passportReferUrl);
    }

    private void removeAccessTokenCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie(MgkConstants.HTTP_TOKEN, null);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setHttpOnly(true);
        response.addCookie(cookie);
        Cookie cookieEffectAd = new Cookie(MgkConstants.EFFECT_AD_TOKEN, null);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setHttpOnly(true);
        response.addCookie(cookieEffectAd);
        Cookie cookieCluePass = new Cookie(MgkConstants.CLUE_PASS_TOKEN, null);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setHttpOnly(true);
        response.addCookie(cookieCluePass);
        Cookie cookiePickup = new Cookie(MgkConstants.PICKUP_TOKEN, null);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setHttpOnly(true);
        response.addCookie(cookiePickup);
    }
}
