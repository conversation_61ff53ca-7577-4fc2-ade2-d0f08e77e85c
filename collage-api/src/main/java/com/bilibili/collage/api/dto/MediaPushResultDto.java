package com.bilibili.collage.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/09/09
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MediaPushResultDto implements Serializable {

    /**
     * 成功数
     */
    private Integer successCount;

    /**
     * 已经存在数
     */
    private Integer existCount;

    /**
     * 总数
     */
    private Integer totalCount;

    public static MediaPushResultDto init() {
        return MediaPushResultDto.builder()
                .successCount(0)
                .existCount(0)
                .totalCount(0)
                .build();
    }

    private static final long serialVersionUID = 1L;
}
