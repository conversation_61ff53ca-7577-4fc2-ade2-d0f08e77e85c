package com.bilibili.mgk.platform.portal.webapi.form.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MgkFormLbsProcessVo {

    /**
     * 表单项ID
     */
    private String formItemId;

    /**
     * 处理状态 1-已完成 2-处理中 3-处理失败
     */
    private Integer dealStatus;

    /**
     * 处理进度
     */
    private Integer processingProgress;

    /**
     * 失败原因
     */
    private String failMsg;


}
