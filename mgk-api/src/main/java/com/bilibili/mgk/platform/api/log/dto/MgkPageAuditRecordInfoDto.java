package com.bilibili.mgk.platform.api.log.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 在记录用户操作的同时 在审核通过/驳回的时候又要知道当前的内容
 * 没有es的任务概念 又想要创意审核那一套导出功能
 * 难受
 *
 * @ClassName MgkPageLogOperateValueDto
 * <AUTHOR>
 * @Date 2022/12/19 10:14 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkPageAuditRecordInfoDto implements Serializable {

    private Integer accountId;

    private String jumpUrl;

    private String jumpUrlSecondary;

    private List<Integer> appPackageIds;

    private Integer wechatPackageId;
}
