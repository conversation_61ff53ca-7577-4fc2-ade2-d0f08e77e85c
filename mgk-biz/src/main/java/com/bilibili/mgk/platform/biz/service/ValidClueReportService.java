package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.enums.ConvReturnMsgCode;
import com.bilibili.adp.passport.api.dto.AdConvDto;
import com.bilibili.adp.passport.api.service.IConvReturnService;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.mgk.platform.common.MgkReportStatusEnum;
import io.vavr.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkClue.mgkClue;

@Service
@Slf4j
public class ValidClueReportService {
    @Autowired
    private IConvReturnService cvtReturnService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private BaseQueryFactory bqf;

    public void report(String trackId, String cvtType, String cvtTime) {
        final AdConvDto adCvtDto = AdConvDto.builder()
                .track_id(trackId)
                .conv_type(cvtType)
                .conv_time(cvtTime)
                .client_ip("0.0.0.0")
                .scid("mgk")
                .build();
        final int resCode = cvtReturnService.adTrackReturn(adCvtDto);
        if (ConvReturnMsgCode.SUCCESS.getCode().equals(resCode)) {
            log.info(String.format("reported trackId [%s]: ok", adCvtDto.getTrack_id()));
            updateReportStatus(trackId, MgkReportStatusEnum.REPORTED.getCode());
        } else if (ConvReturnMsgCode.INVALID_TRACKID.getCode().equals(resCode)) {
            log.error(String.format("reported trackId [%s]: invalid", adCvtDto.getTrack_id()));
            updateReportStatus(trackId, MgkReportStatusEnum.INVALID_TRACKID.getCode());
        } else {
            log.error(String.format("reported trackId [%s]: failed", adCvtDto.getTrack_id()));
        }
    }

    private void updateReportStatus(String trackId, Integer status) {
        bqf.update(mgkClue)
                .where(mgkClue.trackId.eq(trackId))
                .set(mgkClue.reportStatus, status)
                .execute();
    }

    public void reportAsync(String trackId, String cvtType, String cvtTime) {
        taskExecutor.execute(() -> report(trackId, cvtType, cvtTime));
    }

    public void reportListAsync(List<Tuple3<String, String, String>> list) {
        taskExecutor.execute(() -> list.forEach(x -> report(x._1, x._2, x._3)));
    }
}
