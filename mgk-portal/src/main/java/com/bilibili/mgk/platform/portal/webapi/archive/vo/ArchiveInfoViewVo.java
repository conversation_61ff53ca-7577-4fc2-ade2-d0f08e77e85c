package com.bilibili.mgk.platform.portal.webapi.archive.vo;

import com.bilibili.adp.passport.biz.manager.bean.ArchiveVideo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/9
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArchiveInfoViewVo {

    @ApiModelProperty(value = "视频ID")
    private String aid;

    @ApiModelProperty(value = "分区ID")
    private Integer tid;

    @ApiModelProperty(value = "分区名称")
    private String tname;

    @ApiModelProperty(value = "投稿类型")
    private String copyright;

    @ApiModelProperty(value = "封面")
    private String pic;

    @ApiModelProperty(value = "标题")
    private String title;

    private Long pubdate;

    @ApiModelProperty(value = "创建时间")
    private Long ctime;

    @ApiModelProperty(value = "描述")
    private String desc;

    @ApiModelProperty(value = "状态")
    private Integer state;

    private List<ArchiveVideo> archiveVideos;

}