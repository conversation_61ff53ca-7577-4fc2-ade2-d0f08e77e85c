package com.bilibili.mgk.platform.api.abyss.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class PageConfigResBO {

    /**
     * 实验 0-基线 1-实验1 2-实验2 3-实验3 4-实验4
     *
     * @return
     */
    private Integer exp_hit;

    /**
     * 预加载实验 同上
     */
    private Integer preload_hit;
}
