package com.bilibili.mgk.platform.biz.ad.dao;

import com.bilibili.mgk.platform.biz.ad.po.LauBizCreativeMappingPo;
import com.bilibili.mgk.platform.biz.ad.po.LauBizCreativeMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauBizCreativeMappingDao {
    long countByExample(LauBizCreativeMappingPoExample example);

    int deleteByExample(LauBizCreativeMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(LauBizCreativeMappingPo record);

    int insertBatch(List<LauBizCreativeMappingPo> records);

    int insertUpdateBatch(List<LauBizCreativeMappingPo> records);

    int insert(LauBizCreativeMappingPo record);

    int insertUpdateSelective(LauBizCreativeMappingPo record);

    int insertSelective(LauBizCreativeMappingPo record);

    List<LauBizCreativeMappingPo> selectByExample(LauBizCreativeMappingPoExample example);

    LauBizCreativeMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") LauBizCreativeMappingPo record, @Param("example") LauBizCreativeMappingPoExample example);

    int updateByExample(@Param("record") LauBizCreativeMappingPo record, @Param("example") LauBizCreativeMappingPoExample example);

    int updateByPrimaryKeySelective(LauBizCreativeMappingPo record);

    int updateByPrimaryKey(LauBizCreativeMappingPo record);
}