package com.bilibili.mgk.platform.biz.service.message;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.adp.passport.biz.manager.PassportBaseRequestFactory;
import com.bilibili.adp.passport.biz.manager.SignUtil;
import com.bilibili.adp.passport.biz.manager.bean.PassportBaseRequest;
import com.bilibili.mgk.platform.api.message.IMessageService;
import com.bilibili.mgk.platform.api.message.SendMessageResponse;
import com.bilibili.mgk.platform.common.AbstractMessageBuilder;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 站内信服务。
 *
 * <AUTHOR>
 * @since 2018年06月06日
 */
@Slf4j
@Service
public class MessageServiceImpl implements IMessageService {

    @Value("${message.url:http://message.bilibili.co/api/notify/send.user.notify.do}")
    private String messageUrl;

    @Autowired
    private SignUtil signUtil;
    @Autowired
    private PassportBaseRequestFactory passportBaseRequestFactory;

    private PassportBaseRequest getSignedBaseRequest() {
        PassportBaseRequest baseRequest = passportBaseRequestFactory.createPassportBaseRequestWithOutSign();
        try {
            String sign = signUtil.getSign(baseRequest);
            baseRequest.setSign(sign);
            return baseRequest;
        } catch (ServiceException e) {
            log.error("获取主站签名失败！", e);
            throw new RuntimeException("获取主站签名失败：" + e.getMessage());
        }
    }

    private OkHttpUtils.FormPoster buildBaseMessagePoster() {
        PassportBaseRequest request = getSignedBaseRequest();
        return OkHttpUtils.formPost(messageUrl)
                .bean(request)
                .param("type", "json")
                .param("source", 999);
    }

    @Override
    public void sendMessage(AbstractMessageBuilder messageBuilder) {
        log.info("发送消息参数：messageBuilder={}", messageBuilder);
        try {
            SendMessageResponse response = buildBaseMessagePoster()
                    .bean(messageBuilder.build())
                    .callForObject(SendMessageResponse.class);
            Cat.logEvent("MessageServiceImpl", messageBuilder.toString());
            log.info("发送消息结果：{}", response);
        } catch (Exception e) {
            Cat.logEvent("MessageServiceImpl", messageBuilder.toString()+e);
            log.error("发送消息失败！参数：massage=" + messageBuilder, e);
        }
    }

    @Override
    public void sendMessageAsync(AbstractMessageBuilder messageBuilder) {
        CompletableFuture.runAsync(()-> this.sendMessage(messageBuilder));
    }
}
