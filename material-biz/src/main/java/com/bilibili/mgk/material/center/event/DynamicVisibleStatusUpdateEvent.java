package com.bilibili.mgk.material.center.event;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/29
 */
@Setter
@Getter
@Accessors(chain = true)
public class DynamicVisibleStatusUpdateEvent extends ApplicationEvent {

    private String dynamicId;

    private Integer dynamicStatus;


    public DynamicVisibleStatusUpdateEvent(Object source) {
        super(source);
    }
}
