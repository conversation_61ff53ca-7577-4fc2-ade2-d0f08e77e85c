package com.bilibili.collage.api.dto;

import com.bilibili.adp.common.util.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/16
 **/
@Data
@Builder
@AllArgsConstructor
public class QueryCollageFontLibraryDto implements Serializable {

    private static final long serialVersionUID = 6803185958490736155L;

    private Integer id;

    private List<Integer> ids;

    private String name;

    private Integer status;

    private Page pageInfo;

    private Integer edition;
}
