package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.archive.service.ILauArchiveInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName RefreshArchiveJob
 * <AUTHOR>
 * @Date 2021/12/22 2:13 下午
 * @Version 1.0
 **/
@Slf4j
@Component
@JobHandler("RefreshLauMaterialBilibiliVideoWithCoverJob")
public class RefreshLauMaterialBilibiliVideoWithCoverJob extends IJobHandler {

    @Autowired
    private ILauArchiveInfoService mgkArchiveInfoService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("start execute refresh LauMaterialBilibiliVideoWithCover job");
        try {
            mgkArchiveInfoService.refreshLauBilibiliVideoWithCoverArchiveInfo();
        } catch (Exception ex) {
            log.error("execute refresh LauMaterialBilibiliVideoWithCover job failed, exception :{}", ex.getMessage());
        }
        log.info("end execute refresh LauMaterialBilibiliVideoWithCover job");
        return SUCCESS;
    }
}
