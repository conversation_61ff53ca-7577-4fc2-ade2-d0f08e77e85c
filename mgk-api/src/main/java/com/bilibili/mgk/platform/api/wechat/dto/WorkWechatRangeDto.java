package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WorkWechatRangeDto implements Serializable {


    private static final long serialVersionUID = -2887294013545779785L;
    /**
     * 用户列表
     */
    private List<String> user_list;

    /**
     * 部门列表
     */
    private List<String> department_list;

}
