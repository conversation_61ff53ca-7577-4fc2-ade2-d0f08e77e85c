<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.collage.biz.dao.MgkCollageLayerDao">
  <resultMap id="BaseResultMap" type="com.bilibili.collage.biz.po.MgkCollageLayerPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="x_axis" jdbcType="INTEGER" property="xAxis" />
    <result column="y_axis" jdbcType="INTEGER" property="yAxis" />
    <result column="width" jdbcType="INTEGER" property="width" />
    <result column="height" jdbcType="INTEGER" property="height" />
    <result column="rotate" jdbcType="INTEGER" property="rotate" />
    <result column="opacity" jdbcType="TINYINT" property="opacity" />
    <result column="seq" jdbcType="TINYINT" property="seq" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="image_md5" jdbcType="VARCHAR" property="imageMd5" />
    <result column="image_width" jdbcType="INTEGER" property="imageWidth" />
    <result column="image_height" jdbcType="INTEGER" property="imageHeight" />
    <result column="image_lock" jdbcType="TINYINT" property="imageLock" />
    <result column="bg_color" jdbcType="VARCHAR" property="bgColor" />
    <result column="text_val" jdbcType="VARCHAR" property="textVal" />
    <result column="font_family_id" jdbcType="INTEGER" property="fontFamilyId" />
    <result column="text_color" jdbcType="VARCHAR" property="textColor" />
    <result column="text_align" jdbcType="VARCHAR" property="textAlign" />
    <result column="font_style" jdbcType="VARCHAR" property="fontStyle" />
    <result column="font_size" jdbcType="TINYINT" property="fontSize" />
    <result column="font_weight" jdbcType="VARCHAR" property="fontWeight" />
    <result column="underline" jdbcType="TINYINT" property="underline" />
    <result column="linethrough" jdbcType="TINYINT" property="linethrough" />
    <result column="pattern_id" jdbcType="INTEGER" property="patternId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="works_id" jdbcType="INTEGER" property="worksId" />
    <result column="text_cross" jdbcType="INTEGER" property="textCross" />
    <result column="text_orient" jdbcType="INTEGER" property="textOrient" />
    <result column="shadow_x" jdbcType="INTEGER" property="shadowX" />
    <result column="shadow_y" jdbcType="INTEGER" property="shadowY" />
    <result column="shadow_blur" jdbcType="INTEGER" property="shadowBlur" />
    <result column="shadow_width" jdbcType="INTEGER" property="shadowWidth" />
    <result column="shadow_color" jdbcType="VARCHAR" property="shadowColor" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, category_id, type, x_axis, y_axis, width, height, rotate, opacity, seq, 
    image_url, image_md5, image_width, image_height, image_lock, bg_color, text_val, 
    font_family_id, text_color, text_align, font_style, font_size, font_weight, underline, 
    linethrough, pattern_id, creator, is_deleted, ctime, mtime, works_id, text_cross, 
    text_orient, shadow_x, shadow_y, shadow_blur, shadow_width, shadow_color
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_collage_layer
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_collage_layer
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_collage_layer
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPoExample">
    delete from mgk_collage_layer
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_layer (name, category_id, type, 
      x_axis, y_axis, width, 
      height, rotate, opacity, 
      seq, image_url, image_md5, 
      image_width, image_height, image_lock, 
      bg_color, text_val, font_family_id, 
      text_color, text_align, font_style, 
      font_size, font_weight, underline, 
      linethrough, pattern_id, creator, 
      is_deleted, ctime, mtime, 
      works_id, text_cross, text_orient, 
      shadow_x, shadow_y, shadow_blur, 
      shadow_width, shadow_color)
    values (#{name,jdbcType=VARCHAR}, #{categoryId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{xAxis,jdbcType=INTEGER}, #{yAxis,jdbcType=INTEGER}, #{width,jdbcType=INTEGER}, 
      #{height,jdbcType=INTEGER}, #{rotate,jdbcType=INTEGER}, #{opacity,jdbcType=TINYINT}, 
      #{seq,jdbcType=TINYINT}, #{imageUrl,jdbcType=VARCHAR}, #{imageMd5,jdbcType=VARCHAR}, 
      #{imageWidth,jdbcType=INTEGER}, #{imageHeight,jdbcType=INTEGER}, #{imageLock,jdbcType=TINYINT}, 
      #{bgColor,jdbcType=VARCHAR}, #{textVal,jdbcType=VARCHAR}, #{fontFamilyId,jdbcType=INTEGER}, 
      #{textColor,jdbcType=VARCHAR}, #{textAlign,jdbcType=VARCHAR}, #{fontStyle,jdbcType=VARCHAR}, 
      #{fontSize,jdbcType=TINYINT}, #{fontWeight,jdbcType=VARCHAR}, #{underline,jdbcType=TINYINT}, 
      #{linethrough,jdbcType=TINYINT}, #{patternId,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{worksId,jdbcType=INTEGER}, #{textCross,jdbcType=INTEGER}, #{textOrient,jdbcType=INTEGER}, 
      #{shadowX,jdbcType=INTEGER}, #{shadowY,jdbcType=INTEGER}, #{shadowBlur,jdbcType=INTEGER}, 
      #{shadowWidth,jdbcType=INTEGER}, #{shadowColor,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_layer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="xAxis != null">
        x_axis,
      </if>
      <if test="yAxis != null">
        y_axis,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="rotate != null">
        rotate,
      </if>
      <if test="opacity != null">
        opacity,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="imageWidth != null">
        image_width,
      </if>
      <if test="imageHeight != null">
        image_height,
      </if>
      <if test="imageLock != null">
        image_lock,
      </if>
      <if test="bgColor != null">
        bg_color,
      </if>
      <if test="textVal != null">
        text_val,
      </if>
      <if test="fontFamilyId != null">
        font_family_id,
      </if>
      <if test="textColor != null">
        text_color,
      </if>
      <if test="textAlign != null">
        text_align,
      </if>
      <if test="fontStyle != null">
        font_style,
      </if>
      <if test="fontSize != null">
        font_size,
      </if>
      <if test="fontWeight != null">
        font_weight,
      </if>
      <if test="underline != null">
        underline,
      </if>
      <if test="linethrough != null">
        linethrough,
      </if>
      <if test="patternId != null">
        pattern_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="worksId != null">
        works_id,
      </if>
      <if test="textCross != null">
        text_cross,
      </if>
      <if test="textOrient != null">
        text_orient,
      </if>
      <if test="shadowX != null">
        shadow_x,
      </if>
      <if test="shadowY != null">
        shadow_y,
      </if>
      <if test="shadowBlur != null">
        shadow_blur,
      </if>
      <if test="shadowWidth != null">
        shadow_width,
      </if>
      <if test="shadowColor != null">
        shadow_color,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="xAxis != null">
        #{xAxis,jdbcType=INTEGER},
      </if>
      <if test="yAxis != null">
        #{yAxis,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="rotate != null">
        #{rotate,jdbcType=INTEGER},
      </if>
      <if test="opacity != null">
        #{opacity,jdbcType=TINYINT},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=TINYINT},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="imageWidth != null">
        #{imageWidth,jdbcType=INTEGER},
      </if>
      <if test="imageHeight != null">
        #{imageHeight,jdbcType=INTEGER},
      </if>
      <if test="imageLock != null">
        #{imageLock,jdbcType=TINYINT},
      </if>
      <if test="bgColor != null">
        #{bgColor,jdbcType=VARCHAR},
      </if>
      <if test="textVal != null">
        #{textVal,jdbcType=VARCHAR},
      </if>
      <if test="fontFamilyId != null">
        #{fontFamilyId,jdbcType=INTEGER},
      </if>
      <if test="textColor != null">
        #{textColor,jdbcType=VARCHAR},
      </if>
      <if test="textAlign != null">
        #{textAlign,jdbcType=VARCHAR},
      </if>
      <if test="fontStyle != null">
        #{fontStyle,jdbcType=VARCHAR},
      </if>
      <if test="fontSize != null">
        #{fontSize,jdbcType=TINYINT},
      </if>
      <if test="fontWeight != null">
        #{fontWeight,jdbcType=VARCHAR},
      </if>
      <if test="underline != null">
        #{underline,jdbcType=TINYINT},
      </if>
      <if test="linethrough != null">
        #{linethrough,jdbcType=TINYINT},
      </if>
      <if test="patternId != null">
        #{patternId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="worksId != null">
        #{worksId,jdbcType=INTEGER},
      </if>
      <if test="textCross != null">
        #{textCross,jdbcType=INTEGER},
      </if>
      <if test="textOrient != null">
        #{textOrient,jdbcType=INTEGER},
      </if>
      <if test="shadowX != null">
        #{shadowX,jdbcType=INTEGER},
      </if>
      <if test="shadowY != null">
        #{shadowY,jdbcType=INTEGER},
      </if>
      <if test="shadowBlur != null">
        #{shadowBlur,jdbcType=INTEGER},
      </if>
      <if test="shadowWidth != null">
        #{shadowWidth,jdbcType=INTEGER},
      </if>
      <if test="shadowColor != null">
        #{shadowColor,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPoExample" resultType="java.lang.Long">
    select count(*) from mgk_collage_layer
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_collage_layer
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.xAxis != null">
        x_axis = #{record.xAxis,jdbcType=INTEGER},
      </if>
      <if test="record.yAxis != null">
        y_axis = #{record.yAxis,jdbcType=INTEGER},
      </if>
      <if test="record.width != null">
        width = #{record.width,jdbcType=INTEGER},
      </if>
      <if test="record.height != null">
        height = #{record.height,jdbcType=INTEGER},
      </if>
      <if test="record.rotate != null">
        rotate = #{record.rotate,jdbcType=INTEGER},
      </if>
      <if test="record.opacity != null">
        opacity = #{record.opacity,jdbcType=TINYINT},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=TINYINT},
      </if>
      <if test="record.imageUrl != null">
        image_url = #{record.imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.imageMd5 != null">
        image_md5 = #{record.imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.imageWidth != null">
        image_width = #{record.imageWidth,jdbcType=INTEGER},
      </if>
      <if test="record.imageHeight != null">
        image_height = #{record.imageHeight,jdbcType=INTEGER},
      </if>
      <if test="record.imageLock != null">
        image_lock = #{record.imageLock,jdbcType=TINYINT},
      </if>
      <if test="record.bgColor != null">
        bg_color = #{record.bgColor,jdbcType=VARCHAR},
      </if>
      <if test="record.textVal != null">
        text_val = #{record.textVal,jdbcType=VARCHAR},
      </if>
      <if test="record.fontFamilyId != null">
        font_family_id = #{record.fontFamilyId,jdbcType=INTEGER},
      </if>
      <if test="record.textColor != null">
        text_color = #{record.textColor,jdbcType=VARCHAR},
      </if>
      <if test="record.textAlign != null">
        text_align = #{record.textAlign,jdbcType=VARCHAR},
      </if>
      <if test="record.fontStyle != null">
        font_style = #{record.fontStyle,jdbcType=VARCHAR},
      </if>
      <if test="record.fontSize != null">
        font_size = #{record.fontSize,jdbcType=TINYINT},
      </if>
      <if test="record.fontWeight != null">
        font_weight = #{record.fontWeight,jdbcType=VARCHAR},
      </if>
      <if test="record.underline != null">
        underline = #{record.underline,jdbcType=TINYINT},
      </if>
      <if test="record.linethrough != null">
        linethrough = #{record.linethrough,jdbcType=TINYINT},
      </if>
      <if test="record.patternId != null">
        pattern_id = #{record.patternId,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.worksId != null">
        works_id = #{record.worksId,jdbcType=INTEGER},
      </if>
      <if test="record.textCross != null">
        text_cross = #{record.textCross,jdbcType=INTEGER},
      </if>
      <if test="record.textOrient != null">
        text_orient = #{record.textOrient,jdbcType=INTEGER},
      </if>
      <if test="record.shadowX != null">
        shadow_x = #{record.shadowX,jdbcType=INTEGER},
      </if>
      <if test="record.shadowY != null">
        shadow_y = #{record.shadowY,jdbcType=INTEGER},
      </if>
      <if test="record.shadowBlur != null">
        shadow_blur = #{record.shadowBlur,jdbcType=INTEGER},
      </if>
      <if test="record.shadowWidth != null">
        shadow_width = #{record.shadowWidth,jdbcType=INTEGER},
      </if>
      <if test="record.shadowColor != null">
        shadow_color = #{record.shadowColor,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_collage_layer
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      category_id = #{record.categoryId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      x_axis = #{record.xAxis,jdbcType=INTEGER},
      y_axis = #{record.yAxis,jdbcType=INTEGER},
      width = #{record.width,jdbcType=INTEGER},
      height = #{record.height,jdbcType=INTEGER},
      rotate = #{record.rotate,jdbcType=INTEGER},
      opacity = #{record.opacity,jdbcType=TINYINT},
      seq = #{record.seq,jdbcType=TINYINT},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      image_md5 = #{record.imageMd5,jdbcType=VARCHAR},
      image_width = #{record.imageWidth,jdbcType=INTEGER},
      image_height = #{record.imageHeight,jdbcType=INTEGER},
      image_lock = #{record.imageLock,jdbcType=TINYINT},
      bg_color = #{record.bgColor,jdbcType=VARCHAR},
      text_val = #{record.textVal,jdbcType=VARCHAR},
      font_family_id = #{record.fontFamilyId,jdbcType=INTEGER},
      text_color = #{record.textColor,jdbcType=VARCHAR},
      text_align = #{record.textAlign,jdbcType=VARCHAR},
      font_style = #{record.fontStyle,jdbcType=VARCHAR},
      font_size = #{record.fontSize,jdbcType=TINYINT},
      font_weight = #{record.fontWeight,jdbcType=VARCHAR},
      underline = #{record.underline,jdbcType=TINYINT},
      linethrough = #{record.linethrough,jdbcType=TINYINT},
      pattern_id = #{record.patternId,jdbcType=INTEGER},
      creator = #{record.creator,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      works_id = #{record.worksId,jdbcType=INTEGER},
      text_cross = #{record.textCross,jdbcType=INTEGER},
      text_orient = #{record.textOrient,jdbcType=INTEGER},
      shadow_x = #{record.shadowX,jdbcType=INTEGER},
      shadow_y = #{record.shadowY,jdbcType=INTEGER},
      shadow_blur = #{record.shadowBlur,jdbcType=INTEGER},
      shadow_width = #{record.shadowWidth,jdbcType=INTEGER},
      shadow_color = #{record.shadowColor,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPo">
    update mgk_collage_layer
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="xAxis != null">
        x_axis = #{xAxis,jdbcType=INTEGER},
      </if>
      <if test="yAxis != null">
        y_axis = #{yAxis,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=INTEGER},
      </if>
      <if test="rotate != null">
        rotate = #{rotate,jdbcType=INTEGER},
      </if>
      <if test="opacity != null">
        opacity = #{opacity,jdbcType=TINYINT},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=TINYINT},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        image_md5 = #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="imageWidth != null">
        image_width = #{imageWidth,jdbcType=INTEGER},
      </if>
      <if test="imageHeight != null">
        image_height = #{imageHeight,jdbcType=INTEGER},
      </if>
      <if test="imageLock != null">
        image_lock = #{imageLock,jdbcType=TINYINT},
      </if>
      <if test="bgColor != null">
        bg_color = #{bgColor,jdbcType=VARCHAR},
      </if>
      <if test="textVal != null">
        text_val = #{textVal,jdbcType=VARCHAR},
      </if>
      <if test="fontFamilyId != null">
        font_family_id = #{fontFamilyId,jdbcType=INTEGER},
      </if>
      <if test="textColor != null">
        text_color = #{textColor,jdbcType=VARCHAR},
      </if>
      <if test="textAlign != null">
        text_align = #{textAlign,jdbcType=VARCHAR},
      </if>
      <if test="fontStyle != null">
        font_style = #{fontStyle,jdbcType=VARCHAR},
      </if>
      <if test="fontSize != null">
        font_size = #{fontSize,jdbcType=TINYINT},
      </if>
      <if test="fontWeight != null">
        font_weight = #{fontWeight,jdbcType=VARCHAR},
      </if>
      <if test="underline != null">
        underline = #{underline,jdbcType=TINYINT},
      </if>
      <if test="linethrough != null">
        linethrough = #{linethrough,jdbcType=TINYINT},
      </if>
      <if test="patternId != null">
        pattern_id = #{patternId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="worksId != null">
        works_id = #{worksId,jdbcType=INTEGER},
      </if>
      <if test="textCross != null">
        text_cross = #{textCross,jdbcType=INTEGER},
      </if>
      <if test="textOrient != null">
        text_orient = #{textOrient,jdbcType=INTEGER},
      </if>
      <if test="shadowX != null">
        shadow_x = #{shadowX,jdbcType=INTEGER},
      </if>
      <if test="shadowY != null">
        shadow_y = #{shadowY,jdbcType=INTEGER},
      </if>
      <if test="shadowBlur != null">
        shadow_blur = #{shadowBlur,jdbcType=INTEGER},
      </if>
      <if test="shadowWidth != null">
        shadow_width = #{shadowWidth,jdbcType=INTEGER},
      </if>
      <if test="shadowColor != null">
        shadow_color = #{shadowColor,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPo">
    update mgk_collage_layer
    set name = #{name,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      x_axis = #{xAxis,jdbcType=INTEGER},
      y_axis = #{yAxis,jdbcType=INTEGER},
      width = #{width,jdbcType=INTEGER},
      height = #{height,jdbcType=INTEGER},
      rotate = #{rotate,jdbcType=INTEGER},
      opacity = #{opacity,jdbcType=TINYINT},
      seq = #{seq,jdbcType=TINYINT},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      image_md5 = #{imageMd5,jdbcType=VARCHAR},
      image_width = #{imageWidth,jdbcType=INTEGER},
      image_height = #{imageHeight,jdbcType=INTEGER},
      image_lock = #{imageLock,jdbcType=TINYINT},
      bg_color = #{bgColor,jdbcType=VARCHAR},
      text_val = #{textVal,jdbcType=VARCHAR},
      font_family_id = #{fontFamilyId,jdbcType=INTEGER},
      text_color = #{textColor,jdbcType=VARCHAR},
      text_align = #{textAlign,jdbcType=VARCHAR},
      font_style = #{fontStyle,jdbcType=VARCHAR},
      font_size = #{fontSize,jdbcType=TINYINT},
      font_weight = #{fontWeight,jdbcType=VARCHAR},
      underline = #{underline,jdbcType=TINYINT},
      linethrough = #{linethrough,jdbcType=TINYINT},
      pattern_id = #{patternId,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      works_id = #{worksId,jdbcType=INTEGER},
      text_cross = #{textCross,jdbcType=INTEGER},
      text_orient = #{textOrient,jdbcType=INTEGER},
      shadow_x = #{shadowX,jdbcType=INTEGER},
      shadow_y = #{shadowY,jdbcType=INTEGER},
      shadow_blur = #{shadowBlur,jdbcType=INTEGER},
      shadow_width = #{shadowWidth,jdbcType=INTEGER},
      shadow_color = #{shadowColor,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_layer (name, category_id, type, 
      x_axis, y_axis, width, 
      height, rotate, opacity, 
      seq, image_url, image_md5, 
      image_width, image_height, image_lock, 
      bg_color, text_val, font_family_id, 
      text_color, text_align, font_style, 
      font_size, font_weight, underline, 
      linethrough, pattern_id, creator, 
      is_deleted, ctime, mtime, 
      works_id, text_cross, text_orient, 
      shadow_x, shadow_y, shadow_blur, 
      shadow_width, shadow_color)
    values (#{name,jdbcType=VARCHAR}, #{categoryId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{xAxis,jdbcType=INTEGER}, #{yAxis,jdbcType=INTEGER}, #{width,jdbcType=INTEGER}, 
      #{height,jdbcType=INTEGER}, #{rotate,jdbcType=INTEGER}, #{opacity,jdbcType=TINYINT}, 
      #{seq,jdbcType=TINYINT}, #{imageUrl,jdbcType=VARCHAR}, #{imageMd5,jdbcType=VARCHAR}, 
      #{imageWidth,jdbcType=INTEGER}, #{imageHeight,jdbcType=INTEGER}, #{imageLock,jdbcType=TINYINT}, 
      #{bgColor,jdbcType=VARCHAR}, #{textVal,jdbcType=VARCHAR}, #{fontFamilyId,jdbcType=INTEGER}, 
      #{textColor,jdbcType=VARCHAR}, #{textAlign,jdbcType=VARCHAR}, #{fontStyle,jdbcType=VARCHAR}, 
      #{fontSize,jdbcType=TINYINT}, #{fontWeight,jdbcType=VARCHAR}, #{underline,jdbcType=TINYINT}, 
      #{linethrough,jdbcType=TINYINT}, #{patternId,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{worksId,jdbcType=INTEGER}, #{textCross,jdbcType=INTEGER}, #{textOrient,jdbcType=INTEGER}, 
      #{shadowX,jdbcType=INTEGER}, #{shadowY,jdbcType=INTEGER}, #{shadowBlur,jdbcType=INTEGER}, 
      #{shadowWidth,jdbcType=INTEGER}, #{shadowColor,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      name = values(name),
      category_id = values(category_id),
      type = values(type),
      x_axis = values(x_axis),
      y_axis = values(y_axis),
      width = values(width),
      height = values(height),
      rotate = values(rotate),
      opacity = values(opacity),
      seq = values(seq),
      image_url = values(image_url),
      image_md5 = values(image_md5),
      image_width = values(image_width),
      image_height = values(image_height),
      image_lock = values(image_lock),
      bg_color = values(bg_color),
      text_val = values(text_val),
      font_family_id = values(font_family_id),
      text_color = values(text_color),
      text_align = values(text_align),
      font_style = values(font_style),
      font_size = values(font_size),
      font_weight = values(font_weight),
      underline = values(underline),
      linethrough = values(linethrough),
      pattern_id = values(pattern_id),
      creator = values(creator),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      works_id = values(works_id),
      text_cross = values(text_cross),
      text_orient = values(text_orient),
      shadow_x = values(shadow_x),
      shadow_y = values(shadow_y),
      shadow_blur = values(shadow_blur),
      shadow_width = values(shadow_width),
      shadow_color = values(shadow_color),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_collage_layer
      (name,category_id,type,x_axis,y_axis,width,height,rotate,opacity,seq,image_url,image_md5,image_width,image_height,image_lock,bg_color,text_val,font_family_id,text_color,text_align,font_style,font_size,font_weight,underline,linethrough,pattern_id,creator,is_deleted,ctime,mtime,works_id,text_cross,text_orient,shadow_x,shadow_y,shadow_blur,shadow_width,shadow_color)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.name,jdbcType=VARCHAR},
        #{item.categoryId,jdbcType=INTEGER},
        #{item.type,jdbcType=INTEGER},
        #{item.xAxis,jdbcType=INTEGER},
        #{item.yAxis,jdbcType=INTEGER},
        #{item.width,jdbcType=INTEGER},
        #{item.height,jdbcType=INTEGER},
        #{item.rotate,jdbcType=INTEGER},
        #{item.opacity,jdbcType=TINYINT},
        #{item.seq,jdbcType=TINYINT},
        #{item.imageUrl,jdbcType=VARCHAR},
        #{item.imageMd5,jdbcType=VARCHAR},
        #{item.imageWidth,jdbcType=INTEGER},
        #{item.imageHeight,jdbcType=INTEGER},
        #{item.imageLock,jdbcType=TINYINT},
        #{item.bgColor,jdbcType=VARCHAR},
        #{item.textVal,jdbcType=VARCHAR},
        #{item.fontFamilyId,jdbcType=INTEGER},
        #{item.textColor,jdbcType=VARCHAR},
        #{item.textAlign,jdbcType=VARCHAR},
        #{item.fontStyle,jdbcType=VARCHAR},
        #{item.fontSize,jdbcType=TINYINT},
        #{item.fontWeight,jdbcType=VARCHAR},
        #{item.underline,jdbcType=TINYINT},
        #{item.linethrough,jdbcType=TINYINT},
        #{item.patternId,jdbcType=INTEGER},
        #{item.creator,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.worksId,jdbcType=INTEGER},
        #{item.textCross,jdbcType=INTEGER},
        #{item.textOrient,jdbcType=INTEGER},
        #{item.shadowX,jdbcType=INTEGER},
        #{item.shadowY,jdbcType=INTEGER},
        #{item.shadowBlur,jdbcType=INTEGER},
        #{item.shadowWidth,jdbcType=INTEGER},
        #{item.shadowColor,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_collage_layer
      (name,category_id,type,x_axis,y_axis,width,height,rotate,opacity,seq,image_url,image_md5,image_width,image_height,image_lock,bg_color,text_val,font_family_id,text_color,text_align,font_style,font_size,font_weight,underline,linethrough,pattern_id,creator,is_deleted,ctime,mtime,works_id,text_cross,text_orient,shadow_x,shadow_y,shadow_blur,shadow_width,shadow_color)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.name,jdbcType=VARCHAR},
        #{item.categoryId,jdbcType=INTEGER},
        #{item.type,jdbcType=INTEGER},
        #{item.xAxis,jdbcType=INTEGER},
        #{item.yAxis,jdbcType=INTEGER},
        #{item.width,jdbcType=INTEGER},
        #{item.height,jdbcType=INTEGER},
        #{item.rotate,jdbcType=INTEGER},
        #{item.opacity,jdbcType=TINYINT},
        #{item.seq,jdbcType=TINYINT},
        #{item.imageUrl,jdbcType=VARCHAR},
        #{item.imageMd5,jdbcType=VARCHAR},
        #{item.imageWidth,jdbcType=INTEGER},
        #{item.imageHeight,jdbcType=INTEGER},
        #{item.imageLock,jdbcType=TINYINT},
        #{item.bgColor,jdbcType=VARCHAR},
        #{item.textVal,jdbcType=VARCHAR},
        #{item.fontFamilyId,jdbcType=INTEGER},
        #{item.textColor,jdbcType=VARCHAR},
        #{item.textAlign,jdbcType=VARCHAR},
        #{item.fontStyle,jdbcType=VARCHAR},
        #{item.fontSize,jdbcType=TINYINT},
        #{item.fontWeight,jdbcType=VARCHAR},
        #{item.underline,jdbcType=TINYINT},
        #{item.linethrough,jdbcType=TINYINT},
        #{item.patternId,jdbcType=INTEGER},
        #{item.creator,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.worksId,jdbcType=INTEGER},
        #{item.textCross,jdbcType=INTEGER},
        #{item.textOrient,jdbcType=INTEGER},
        #{item.shadowX,jdbcType=INTEGER},
        #{item.shadowY,jdbcType=INTEGER},
        #{item.shadowBlur,jdbcType=INTEGER},
        #{item.shadowWidth,jdbcType=INTEGER},
        #{item.shadowColor,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      name = values(name),
      category_id = values(category_id),
      type = values(type),
      x_axis = values(x_axis),
      y_axis = values(y_axis),
      width = values(width),
      height = values(height),
      rotate = values(rotate),
      opacity = values(opacity),
      seq = values(seq),
      image_url = values(image_url),
      image_md5 = values(image_md5),
      image_width = values(image_width),
      image_height = values(image_height),
      image_lock = values(image_lock),
      bg_color = values(bg_color),
      text_val = values(text_val),
      font_family_id = values(font_family_id),
      text_color = values(text_color),
      text_align = values(text_align),
      font_style = values(font_style),
      font_size = values(font_size),
      font_weight = values(font_weight),
      underline = values(underline),
      linethrough = values(linethrough),
      pattern_id = values(pattern_id),
      creator = values(creator),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      works_id = values(works_id),
      text_cross = values(text_cross),
      text_orient = values(text_orient),
      shadow_x = values(shadow_x),
      shadow_y = values(shadow_y),
      shadow_blur = values(shadow_blur),
      shadow_width = values(shadow_width),
      shadow_color = values(shadow_color),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_collage_layer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="xAxis != null">
        x_axis,
      </if>
      <if test="yAxis != null">
        y_axis,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="rotate != null">
        rotate,
      </if>
      <if test="opacity != null">
        opacity,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="imageWidth != null">
        image_width,
      </if>
      <if test="imageHeight != null">
        image_height,
      </if>
      <if test="imageLock != null">
        image_lock,
      </if>
      <if test="bgColor != null">
        bg_color,
      </if>
      <if test="textVal != null">
        text_val,
      </if>
      <if test="fontFamilyId != null">
        font_family_id,
      </if>
      <if test="textColor != null">
        text_color,
      </if>
      <if test="textAlign != null">
        text_align,
      </if>
      <if test="fontStyle != null">
        font_style,
      </if>
      <if test="fontSize != null">
        font_size,
      </if>
      <if test="fontWeight != null">
        font_weight,
      </if>
      <if test="underline != null">
        underline,
      </if>
      <if test="linethrough != null">
        linethrough,
      </if>
      <if test="patternId != null">
        pattern_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="worksId != null">
        works_id,
      </if>
      <if test="textCross != null">
        text_cross,
      </if>
      <if test="textOrient != null">
        text_orient,
      </if>
      <if test="shadowX != null">
        shadow_x,
      </if>
      <if test="shadowY != null">
        shadow_y,
      </if>
      <if test="shadowBlur != null">
        shadow_blur,
      </if>
      <if test="shadowWidth != null">
        shadow_width,
      </if>
      <if test="shadowColor != null">
        shadow_color,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="xAxis != null">
        #{xAxis,jdbcType=INTEGER},
      </if>
      <if test="yAxis != null">
        #{yAxis,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="rotate != null">
        #{rotate,jdbcType=INTEGER},
      </if>
      <if test="opacity != null">
        #{opacity,jdbcType=TINYINT},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=TINYINT},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="imageWidth != null">
        #{imageWidth,jdbcType=INTEGER},
      </if>
      <if test="imageHeight != null">
        #{imageHeight,jdbcType=INTEGER},
      </if>
      <if test="imageLock != null">
        #{imageLock,jdbcType=TINYINT},
      </if>
      <if test="bgColor != null">
        #{bgColor,jdbcType=VARCHAR},
      </if>
      <if test="textVal != null">
        #{textVal,jdbcType=VARCHAR},
      </if>
      <if test="fontFamilyId != null">
        #{fontFamilyId,jdbcType=INTEGER},
      </if>
      <if test="textColor != null">
        #{textColor,jdbcType=VARCHAR},
      </if>
      <if test="textAlign != null">
        #{textAlign,jdbcType=VARCHAR},
      </if>
      <if test="fontStyle != null">
        #{fontStyle,jdbcType=VARCHAR},
      </if>
      <if test="fontSize != null">
        #{fontSize,jdbcType=TINYINT},
      </if>
      <if test="fontWeight != null">
        #{fontWeight,jdbcType=VARCHAR},
      </if>
      <if test="underline != null">
        #{underline,jdbcType=TINYINT},
      </if>
      <if test="linethrough != null">
        #{linethrough,jdbcType=TINYINT},
      </if>
      <if test="patternId != null">
        #{patternId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="worksId != null">
        #{worksId,jdbcType=INTEGER},
      </if>
      <if test="textCross != null">
        #{textCross,jdbcType=INTEGER},
      </if>
      <if test="textOrient != null">
        #{textOrient,jdbcType=INTEGER},
      </if>
      <if test="shadowX != null">
        #{shadowX,jdbcType=INTEGER},
      </if>
      <if test="shadowY != null">
        #{shadowY,jdbcType=INTEGER},
      </if>
      <if test="shadowBlur != null">
        #{shadowBlur,jdbcType=INTEGER},
      </if>
      <if test="shadowWidth != null">
        #{shadowWidth,jdbcType=INTEGER},
      </if>
      <if test="shadowColor != null">
        #{shadowColor,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="name != null">
        name = values(name),
      </if>
      <if test="categoryId != null">
        category_id = values(category_id),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="xAxis != null">
        x_axis = values(x_axis),
      </if>
      <if test="yAxis != null">
        y_axis = values(y_axis),
      </if>
      <if test="width != null">
        width = values(width),
      </if>
      <if test="height != null">
        height = values(height),
      </if>
      <if test="rotate != null">
        rotate = values(rotate),
      </if>
      <if test="opacity != null">
        opacity = values(opacity),
      </if>
      <if test="seq != null">
        seq = values(seq),
      </if>
      <if test="imageUrl != null">
        image_url = values(image_url),
      </if>
      <if test="imageMd5 != null">
        image_md5 = values(image_md5),
      </if>
      <if test="imageWidth != null">
        image_width = values(image_width),
      </if>
      <if test="imageHeight != null">
        image_height = values(image_height),
      </if>
      <if test="imageLock != null">
        image_lock = values(image_lock),
      </if>
      <if test="bgColor != null">
        bg_color = values(bg_color),
      </if>
      <if test="textVal != null">
        text_val = values(text_val),
      </if>
      <if test="fontFamilyId != null">
        font_family_id = values(font_family_id),
      </if>
      <if test="textColor != null">
        text_color = values(text_color),
      </if>
      <if test="textAlign != null">
        text_align = values(text_align),
      </if>
      <if test="fontStyle != null">
        font_style = values(font_style),
      </if>
      <if test="fontSize != null">
        font_size = values(font_size),
      </if>
      <if test="fontWeight != null">
        font_weight = values(font_weight),
      </if>
      <if test="underline != null">
        underline = values(underline),
      </if>
      <if test="linethrough != null">
        linethrough = values(linethrough),
      </if>
      <if test="patternId != null">
        pattern_id = values(pattern_id),
      </if>
      <if test="creator != null">
        creator = values(creator),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="worksId != null">
        works_id = values(works_id),
      </if>
      <if test="textCross != null">
        text_cross = values(text_cross),
      </if>
      <if test="textOrient != null">
        text_orient = values(text_orient),
      </if>
      <if test="shadowX != null">
        shadow_x = values(shadow_x),
      </if>
      <if test="shadowY != null">
        shadow_y = values(shadow_y),
      </if>
      <if test="shadowBlur != null">
        shadow_blur = values(shadow_blur),
      </if>
      <if test="shadowWidth != null">
        shadow_width = values(shadow_width),
      </if>
      <if test="shadowColor != null">
        shadow_color = values(shadow_color),
      </if>
    </trim>
  </insert>
</mapper>