package com.bilibili.mgk.platform.api.data.dto.soa;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.data.dto.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ISoaMgkDataService {

    /**
     * 根据请求参数分页查询表单数据
     *
     * @param paramDto 请求参数
     * @return 数据信息
     */
    PageResult<FormDataDto> getFormDataByPage(FormDataQueryDto paramDto);

    /**
     * 根据请求参数查询表单数据
     *
     * @param paramDto 请求参数
     * @return 数据信息
     */
    List<ReportDataDto> getReportDatas(QueryReportDataParamDto paramDto);


    /**
     * 根据请求参数查询微信涨粉数据
     *
     * @param queryDataDto 请求参数
     * @return 数据信息
     */
    PageResult<WechatPackageReportDataInfoDto> queryWechatPackageData(QueryWechatReportDataDto queryDataDto);


    /**
     * 根据请求参数查询表单数据不过滤反作弊逻辑
     *
     * @param formIds 表单id
     * @param removeCheat 是否去除反作弊逻辑
     * @return 数据信息
     */
    Map<Long, ExtFormDataDto> getFormId2ExtFormDataMap(List<Long> formIds, Boolean removeCheat);



    /**
     * 根据微信账户id查询微信涨粉数据
     *
     * @param wechatAccountIds 微信账户id
     * @param removeCheat 是否去除反作弊逻辑
     * @return 数据信息
     */
    Map<Integer, WechatPackageDataCountDto> queryWechatAccountDataMap(List<Integer> wechatAccountIds, Boolean removeCheat);

}
