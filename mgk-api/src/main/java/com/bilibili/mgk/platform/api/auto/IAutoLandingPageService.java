package com.bilibili.mgk.platform.api.auto;

import com.bilibili.mgk.platform.api.auto.bos.*;
import com.bilibili.mgk.platform.api.video_library.dto.AuditResultBo;

import java.util.Collection;
import java.util.List;

public interface IAutoLandingPageService {
    String downloadAndGetMd5(String url);
    void handleStatusChange(AuditResultBo auditResult);
    void batchHandleStatusChange(Collection<AuditResultBo> auditResults);
    List<MgkAutoIosGameLandingPageBo> queryIosLandingPage();
    List<MgkAutoAndroidGameLandingPageBo> queryAndroidLandingPage();
    void saveIosLandingPages(Collection<MgkAutoIosGameLandingPageBo> bos);
    void saveAndroidLandingPages(Collection<MgkAutoAndroidGameLandingPageBo> bos);
    void deleteIosLandingPages(Collection<Integer> accountIds);
    void deleteAndroidLandingPages(Collection<Integer> accountIds);
}
