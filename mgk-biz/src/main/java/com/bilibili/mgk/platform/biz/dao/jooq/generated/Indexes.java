/*
 * This file is generated by jOOQ.
 */
package com.bilibili.mgk.platform.biz.dao.jooq.generated;


import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.TLauMaterialBilibiliVideoInfo;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.TLauMaterialBilibiliVideoWithCover;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.TResAppPackage;

import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.Internal;


/**
 * A class modelling indexes of tables of the <code></code> schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_CTIME_IDX = Indexes0.LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_CTIME_IDX;
    public static final Index LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_MTIME_IDX = Indexes0.LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_MTIME_IDX;
    public static final Index LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER_IX_MTIME = Indexes0.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER_IX_MTIME;
    public static final Index RES_APP_PACKAGE_IX_ACCOUNT_ID = Indexes0.RES_APP_PACKAGE_IX_ACCOUNT_ID;
    public static final Index RES_APP_PACKAGE_IX_ID_ACCOUNT_ID = Indexes0.RES_APP_PACKAGE_IX_ID_ACCOUNT_ID;
    public static final Index RES_APP_PACKAGE_IX_MD5 = Indexes0.RES_APP_PACKAGE_IX_MD5;
    public static final Index RES_APP_PACKAGE_IX_MTIME = Indexes0.RES_APP_PACKAGE_IX_MTIME;
    public static final Index RES_APP_PACKAGE_IX_NAME = Indexes0.RES_APP_PACKAGE_IX_NAME;
    public static final Index RES_APP_PACKAGE_IX_PACKAGE_NAME = Indexes0.RES_APP_PACKAGE_IX_PACKAGE_NAME;

    // -------------------------------------------------------------------------
    // [#1459] distribute members to avoid static initialisers > 64kb
    // -------------------------------------------------------------------------

    private static class Indexes0 {
        public static Index LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_CTIME_IDX = Internal.createIndex("lau_material_bilibili_video_info_ctime_IDX", TLauMaterialBilibiliVideoInfo.LAU_MATERIAL_BILIBILI_VIDEO_INFO, new OrderField[] { TLauMaterialBilibiliVideoInfo.LAU_MATERIAL_BILIBILI_VIDEO_INFO.CTIME }, false);
        public static Index LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_MTIME_IDX = Internal.createIndex("lau_material_bilibili_video_info_mtime_IDX", TLauMaterialBilibiliVideoInfo.LAU_MATERIAL_BILIBILI_VIDEO_INFO, new OrderField[] { TLauMaterialBilibiliVideoInfo.LAU_MATERIAL_BILIBILI_VIDEO_INFO.MTIME }, false);
        public static Index LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER_IX_MTIME = Internal.createIndex("ix_mtime", TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER, new OrderField[] { TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER.MTIME }, false);
        public static Index RES_APP_PACKAGE_IX_ACCOUNT_ID = Internal.createIndex("ix_account_id", TResAppPackage.RES_APP_PACKAGE, new OrderField[] { TResAppPackage.RES_APP_PACKAGE.ACCOUNT_ID }, false);
        public static Index RES_APP_PACKAGE_IX_ID_ACCOUNT_ID = Internal.createIndex("ix_id_account_id", TResAppPackage.RES_APP_PACKAGE, new OrderField[] { TResAppPackage.RES_APP_PACKAGE.ID, TResAppPackage.RES_APP_PACKAGE.ACCOUNT_ID }, false);
        public static Index RES_APP_PACKAGE_IX_MD5 = Internal.createIndex("ix_md5", TResAppPackage.RES_APP_PACKAGE, new OrderField[] { TResAppPackage.RES_APP_PACKAGE.MD5 }, false);
        public static Index RES_APP_PACKAGE_IX_MTIME = Internal.createIndex("ix_mtime", TResAppPackage.RES_APP_PACKAGE, new OrderField[] { TResAppPackage.RES_APP_PACKAGE.MTIME }, false);
        public static Index RES_APP_PACKAGE_IX_NAME = Internal.createIndex("ix_name", TResAppPackage.RES_APP_PACKAGE, new OrderField[] { TResAppPackage.RES_APP_PACKAGE.NAME }, false);
        public static Index RES_APP_PACKAGE_IX_PACKAGE_NAME = Internal.createIndex("ix_package_name", TResAppPackage.RES_APP_PACKAGE, new OrderField[] { TResAppPackage.RES_APP_PACKAGE.PACKAGE_NAME }, false);
    }
}
