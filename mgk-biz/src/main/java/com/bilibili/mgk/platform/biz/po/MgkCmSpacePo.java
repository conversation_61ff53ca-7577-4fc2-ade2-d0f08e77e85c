package com.bilibili.mgk.platform.biz.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkCmSpacePo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 商业账号
     */
    private Integer accountId;

    /**
     * 商业虚拟空间mid
     */
    private Long mid;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 商业虚拟空间密码
     */
    private String password;

    private static final long serialVersionUID = 1L;
}