package com.bilibili.mgk.platform.api.data.dto;

import com.bilibili.adp.common.util.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName WechatPackageDataQueryDto
 * <AUTHOR>
 * @Date 2022/6/18 3:09 下午
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatPackageDataQueryDto {
    private static final long serialVersionUID = 6889855684658452L;
    private Integer accountId;
    private List<Integer> accountIds;
    private Long pageId;
    private List<Long> pageIds;
    private Integer wechatAccountId;
    private List<Integer> wechatAccountIds;
    private Integer wechatPackageId;
    private List<Integer> wechatPackageIds;
    private Long creativeId;
    private List<Long> creativeIds;
    private Integer salesType;
    private List<Integer> salesTypes;
    private String buvid;
    private List<String> buvids;
    private Integer os;
    private List<Integer> osList;
    private Timestamp beginCtime;
    private Timestamp endCtime;
    private boolean download;
    private Integer dataType;

    private Page page;
}
