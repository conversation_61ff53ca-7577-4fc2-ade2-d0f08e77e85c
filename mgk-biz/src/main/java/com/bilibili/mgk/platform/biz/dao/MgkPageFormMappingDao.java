package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkPageFormMappingPo;
import com.bilibili.mgk.platform.biz.po.MgkPageFormMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkPageFormMappingDao {
    long countByExample(MgkPageFormMappingPoExample example);

    int deleteByExample(MgkPageFormMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkPageFormMappingPo record);

    int insertBatch(List<MgkPageFormMappingPo> records);

    int insertUpdateBatch(List<MgkPageFormMappingPo> records);

    int insert(MgkPageFormMappingPo record);

    int insertUpdateSelective(MgkPageFormMappingPo record);

    int insertSelective(MgkPageFormMappingPo record);

    List<MgkPageFormMappingPo> selectByExample(MgkPageFormMappingPoExample example);

    MgkPageFormMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkPageFormMappingPo record, @Param("example") MgkPageFormMappingPoExample example);

    int updateByExample(@Param("record") MgkPageFormMappingPo record, @Param("example") MgkPageFormMappingPoExample example);

    int updateByPrimaryKeySelective(MgkPageFormMappingPo record);

    int updateByPrimaryKey(MgkPageFormMappingPo record);
}