package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkCmArchivePo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 是否删除0否 1是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * ssa视频物料id
     */
    private Integer bizId;

    /**
     * 投稿使用的主站mid
     */
    private Long mid;

    /**
     * 投稿得到的主站稿件id
     */
    private Long avid;

    /**
     * ugc视频物料id
     */
    private Long cid;

    /**
     * 用于ugc投稿的filename
     */
    private String ugcFilename;

    /**
     * 稿件封面url
     */
    private String coverUrl;

    /**
     * 稿件封面md5
     */
    private String coverMd5;

    /**
     * 视频物料宽
     */
    private Integer width;

    /**
     * 视频物料高
     */
    private Integer height;

    /**
     * 视频物料时长(毫秒)
     */
    private Integer durationInMs;

    /**
     * 视频大小(KB)
     */
    private Integer sizeKb;

    /**
     * 视频云转码后得到的视频宽高比分类：0-其他 1-16:9 2-9:16 3-3:4 4-4:3
     */
    private Integer sizeType;

    /**
     * 主站审核状态: 大于等于0代表审核通过
     */
    private Integer auditStatus;

    /**
     * 主站审核拒绝原因
     */
    private String auditReason;

    /**
     * 主站审核通过时间
     */
    private Timestamp auditPassTime;

    /**
     * 视频宽高比(宽)
     */
    private Integer ratioWidth;

    /**
     * 视频宽高比(高)
     */
    private Integer ratioHeight;

    /**
     * 用于ssa投稿的filename
     */
    private String ssaFilename;

    /**
     * 原始文件md5值, 用来关联视频表
     */
    private String videoMd5;

    private static final long serialVersionUID = 1L;
}