package com.bilibili.mgk.platform.portal.openapi.form.vo;

import com.bilibili.mgk.platform.api.form.dto.MgkClueDataEntryBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhoneInfoVo {

    //手机号
    @NotNull
    private String phone_no;

    //校验码
    @NotNull
    private String check_no;
}
