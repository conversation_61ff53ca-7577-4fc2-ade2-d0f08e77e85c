package com.bilibili.mgk.platform.biz.repo;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.mgk.platform.biz.dao.MgkCmArchiveDao;
import com.bilibili.mgk.platform.biz.po.MgkCmArchivePo;
import com.bilibili.mgk.platform.biz.po.MgkCmArchivePoExample;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import reactor.core.support.Assert;

/**
 * <AUTHOR>
 * @date 2022/10/20 下午12:19
 */
@Repository
public class MgkCmArchiveRepo {

    @Autowired
    private MgkCmArchiveDao mgkCmArchiveDao;

    /**
     *
     * @param mid
     * @param needSyncAvids
     * @return
     */
    public List<MgkCmArchivePo> queryAuditedListByAvids(Long mid, List<Long> needSyncAvids) {
        Assert.notNull(mid, "mid 不能为空");

        if (CollectionUtils.isEmpty(needSyncAvids)) {
            return Collections.EMPTY_LIST;
        }

        MgkCmArchivePoExample example = new MgkCmArchivePoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAuditStatusGreaterThanOrEqualTo(0)
                .andAvidIn(needSyncAvids);
        List<MgkCmArchivePo> mgkCmArchivePos = mgkCmArchiveDao.selectByExample(example);
        return mgkCmArchivePos;
    }

    public Integer insertBatch(List<MgkCmArchivePo> partition) {
        return mgkCmArchiveDao.insertBatch(partition);
    }

    public Integer insert(MgkCmArchivePo archivePo) {
        return mgkCmArchiveDao.insertSelective(archivePo);
    }

    public List<MgkCmArchivePo> queryListByVideoMd5s(List<String> needSyncMediaMd5s) {

        if (CollectionUtils.isEmpty(needSyncMediaMd5s)) {
            return Collections.EMPTY_LIST;
        }

        MgkCmArchivePoExample example = new MgkCmArchivePoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andVideoMd5In(needSyncMediaMd5s);
        List<MgkCmArchivePo> mgkCmArchivePos = mgkCmArchiveDao.selectByExample(example);
        return mgkCmArchivePos;
    }
}
