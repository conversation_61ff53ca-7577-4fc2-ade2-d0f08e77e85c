package com.bilibili.mgk.platform.portal.service;

import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoBussInterestDto;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoDto;
import com.bilibili.mgk.platform.portal.util.UnitConversionUtils;
import com.bilibili.mgk.platform.portal.webapi.hot_video.vo.HotVideoBussInterestVo;
import com.bilibili.mgk.platform.portal.webapi.hot_video.vo.HotVideoListVo;
import com.bilibili.mgk.platform.portal.webapi.hot_video.vo.HotVideoVo;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @file: WebHotVideoService
 * @author: gaoming
 * @date: 2020/11/04
 * @version: 1.0
 * @description:
 **/

@Service
@Slf4j
public class WebHotVideoService {
    public List<HotVideoListVo> convertListDtos2Vos(List<HotVideoDto> records) {
        return records.stream().map(this::convertListDto2Vo).collect(Collectors.toList());
    }

    private HotVideoListVo convertListDto2Vo(HotVideoDto hotVideoDto) {
        return HotVideoListVo.builder()
                .avid(hotVideoDto.getAvid() == null ? 0L : hotVideoDto.getAvid())
                .bvid(hotVideoDto.getBvid() == null ? "0" : hotVideoDto.getBvid())
                .title(hotVideoDto.getTitle() == null ? "--" : hotVideoDto.getTitle())
                .cover(hotVideoDto.getCover() == null ? "" : hotVideoDto.getCover())
                .play(UnitConversionUtils.playConversion(hotVideoDto.getPlayDaily()))
                .likes(UnitConversionUtils.likesConversion(hotVideoDto.getLikesDaily()))
                .is_collect(hotVideoDto.getIsCollect())
                .status(hotVideoDto.getBlackStatus() == null ? 0 : hotVideoDto.getBlackStatus())
                .buss_interest(hotVideoDto.getBussInterest() == null ? "--" : hotVideoDto.getBussInterest())
                .up_name(hotVideoDto.getUpName() == null ? "--" : hotVideoDto.getUpName())
                .buss_interest_desc(hotVideoDto.getBussInterestDesc())
                .tname(hotVideoDto.getTname())
                .is_vertical_screen(hotVideoDto.getIsVerticalScreen())
                .build();
    }


    public HotVideoVo convertDto2Vo(HotVideoDto hotVideoDto) {
        return HotVideoVo.builder()
                .avid(hotVideoDto.getAvid() == null ? 0L : hotVideoDto.getAvid())
                .bvid(hotVideoDto.getBvid() == null ? "0" : hotVideoDto.getBvid())
                .title(hotVideoDto.getTitle() == null ? "--" : hotVideoDto.getTitle())
                .up_mid(hotVideoDto.getUpMid() == null ? 0L : hotVideoDto.getUpMid())
                .up_name(hotVideoDto.getUpName() == null ? "--" : hotVideoDto.getUpName())
                .tname(hotVideoDto.getTname() == null ? "--" : hotVideoDto.getTname())
                .sub_name(hotVideoDto.getSubName() == null ? "--" : hotVideoDto.getSubName())
                .cover(hotVideoDto.getCover() == null ? "" : hotVideoDto.getCover())
                .buss_interest(hotVideoDto.getBussInterest() == null ? "--" : hotVideoDto.getBussInterest())
                .play(UnitConversionUtils.playConversion(hotVideoDto.getPlay()))
                .reply(UnitConversionUtils.replyConversion(hotVideoDto.getReplay()))
                .fav(UnitConversionUtils.favConversion(hotVideoDto.getFav()))
                .coin(UnitConversionUtils.coinConversion(hotVideoDto.getCoin()))
                .danmu(UnitConversionUtils.danmuConversion(hotVideoDto.getDanmu()))
                .share(UnitConversionUtils.shareConversion(hotVideoDto.getShare()))
                .likes(UnitConversionUtils.likesConversion(hotVideoDto.getLikes()))
                .play_daily(UnitConversionUtils.playConversion(hotVideoDto.getPlayDaily()))
                .reply_daily(UnitConversionUtils.replyConversion(hotVideoDto.getReplyDaily()))
                .fav_daily(UnitConversionUtils.favConversion(hotVideoDto.getFavDaily()))
                .coin_daily(UnitConversionUtils.coinConversion(hotVideoDto.getCoinDaily()))
                .danmu_daily(UnitConversionUtils.danmuConversion(hotVideoDto.getDanmuDaily()))
                .share_daily(UnitConversionUtils.shareConversion(hotVideoDto.getShareDaily()))
                .likes_daily(UnitConversionUtils.likesConversion(hotVideoDto.getLikesDaily()))
                .build();
    }

    public List<HotVideoBussInterestVo> convertVideoBussInterestVos2Dtos(List<HotVideoBussInterestDto> bussInterests) {

        if (CollectionUtils.isEmpty(bussInterests)) {
            return Collections.emptyList();
        }
        return bussInterests.stream().map(this::convertVideoBussInterestVosDto).collect(Collectors.toList());
    }

    private HotVideoBussInterestVo convertVideoBussInterestVosDto(HotVideoBussInterestDto dto) {
        return HotVideoBussInterestVo.builder()
                .id(dto.getId())
                .name(dto.getName())
                .build();
    }
}
