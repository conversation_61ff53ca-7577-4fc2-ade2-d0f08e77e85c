package com.bilibili.mgk.material.center.config;

import com.bilibili.databus.base.Message;
import com.bilibili.databus.config.DataBusConfig;
import com.bilibili.databus.core.DataBusClient;
import com.bilibili.databus.core.DataBusImpl;
import com.bilibili.discovery.DiscoveryClient;
import com.bilibili.mgk.material.center.config.model.AdBroadcastEventMsg;
import com.bilibili.mgk.material.center.service.aigc.SmartCropFailureRetryQueue;
import com.bilibili.mgk.material.center.service.aigc.SmartCropServiceFlowControlAndFailRetryImpl;
import com.bilibili.mgk.material.center.service.aigc.impl.RejectedMaterialAiSuggestServiceImpl;
import com.bilibili.mgk.material.center.util.EnvUtils;
import com.bilibili.mgk.material.center.util.JsonUtil;
import io.opentelemetry.api.OpenTelemetry;
import io.vavr.control.Try;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/14
 */
@Slf4j
@Configuration
public class DatabusBaseConfigure {

    private static final AtomicInteger DEFAULT_CLIENT_ID_GENERATOR = new AtomicInteger(1);

    @Resource
    private DatabusBaseProperties databusBaseProperties;

    @Resource
    private OpenTelemetry discoveryNoopOpenTelemetry2;

    @Resource
    private DiscoveryClient mgkDiscoveryClient;

    @Lazy
    @Resource
    private SmartCropFailureRetryQueue smartCropFailureRetryQueue;

    @Lazy
    @Resource
    private SmartCropServiceFlowControlAndFailRetryImpl smartCropService;

    @Lazy
    @Resource
    private RejectedMaterialAiSuggestServiceImpl rejectedMaterialAiSuggestService;



    @Bean
    public DataBusClient smartCropFailRetryPub() {

        return createDatabusPubV3(
                databusBaseProperties.getSmartCropFailRetryTopic(),
                databusBaseProperties.getSmartCropFailRetryPubGroup(),
                databusBaseProperties.getSmartCropFailRetryPubAppId(),
                databusBaseProperties.getSmartCropFailRetryPubZone(),
                databusBaseProperties.getSmartCropFailRetryPubToken()
        );
    }


    @Bean
    public List<DataBusClient> smartCropFailRetySub() {

        return createDatabusSubV3AndStartSub(
                databusBaseProperties.getSmartCropFailRetryTopic(),
                databusBaseProperties.getSmartCropFailRetrySubGroup(),
                databusBaseProperties.getSmartCropFailRetrySubAppId(),
                databusBaseProperties.getSmartCropFailRetrySubZone(),
                databusBaseProperties.getSmartCropFailRetrySubToken(),
                databusBaseProperties.getSmartCropFailRetrySubConsumer(),
                msg -> smartCropFailureRetryQueue.consume(msg)
        );
    }

    @Bean
    public List<DataBusClient> smartCropAsyncTaskSub() {

        return createDatabusSubV3AndStartSub(
                databusBaseProperties.getSmartCropAsyncTaskTopic(),
                databusBaseProperties.getSmartCropAsyncTaskSubGroup(),
                databusBaseProperties.getSmartCropAsyncTaskSubAppId(),
                databusBaseProperties.getSmartCropAsyncTaskSubZone(),
                databusBaseProperties.getSmartCropAsyncTaskSubToken(),
                databusBaseProperties.getSmartCropAsyncTaskSubConsumer(),
                msg -> {
                    AdBroadcastEventMsg event = JsonUtil.readValue(msg, AdBroadcastEventMsg.class);

                    if (event == null || CollectionUtils.isEmpty(event.getSmartCropImage())) {
                        return;
                    }

                    log.info("Start to handle smart crop event={}", event);

                    event.getSmartCropImage().forEach(img -> {
                        try {
                            smartCropService.smartCrop(img, false);
                        } catch (Throwable t) {
                            log.error("Fail to crop img of mq, img={} ", img, t);
                        }
                    });
                });

    }

//    @Bean
//    public List<DataBusClient> rejectedMaterialSub() {
//
//        return createDatabusSubV3AndStartSub(
//                databusBaseProperties.getRejectedMaterialAiSuggestionTopic(),
//                databusBaseProperties.getRejectedMaterialAiSuggestionSubGroup(),
//                databusBaseProperties.getRejectedMaterialAiSuggestionSubAppId(),
//                databusBaseProperties.getRejectedMaterialAiSuggestionSubZone(),
//                databusBaseProperties.getRejectedMaterialAiSuggestionSubToken(),
//                databusBaseProperties.getRejectedMaterialAiSuggestionSubConsumer(),
//                msg -> {
//                    rejectedMaterialAiSuggestService.acceptRejectedMaterial(
//                            JsonUtil.readValue(msg, AuditRejectedCreativeMsg.class)
//                    );
//                });
//
//    }




    @Bean
    public OpenTelemetry discoveryNoopOpenTelemetry2() {
        return OpenTelemetry.noop();
    }


    private DataBusClient createDatabusPubV3(
            String topic, String group, String appId, String zone, String token
    ) {

        DataBusConfig databusV3Config = new DataBusConfig(
                EnvUtils.getDatabusEnv(databusBaseProperties.getEnv()),
                EnvUtils.getDatabusZone(zone),
                topic, group,
                appId,
                EnvUtils.getDatabusToken(token)
        );

        return new DataBusImpl(databusV3Config, mgkDiscoveryClient, discoveryNoopOpenTelemetry2);
    }


    private List<DataBusClient> createDatabusSubV3AndStartSub(
            String topic, String group, String appId, String zone, String token, Integer consumers,
            Consumer<String> consumer
    ) {

        DataBusConfig databusV3Config = new DataBusConfig(
                EnvUtils.getDatabusEnv(databusBaseProperties.getEnv()),
                zone,
                topic,
                group,
                appId,
                token
        );
        log.info("Start to create databus v3 client: {}", databusV3Config);

        ExecutorService executor = Executors.newFixedThreadPool(consumers);

        List<DataBusClient> subClients = new ArrayList<>();

        for (int i = 0; i < consumers; i++) {

            executor.submit(() -> {

                DataBusClient subClient = new DataBusImpl(databusV3Config, mgkDiscoveryClient,
                        discoveryNoopOpenTelemetry2);

                subClients.add(subClient);

                while (true) {
                    Try.run(() -> {

                        Message msg = subClient.sub();

                        if (msg == null) {
                            return;
                        }

                        Try.run(() -> {
                            consumer.accept(new String(msg.getValue(), StandardCharsets.UTF_8));

                        }).onFailure(t -> {
                            log.error("Fail to handle databus message: {}, topic={}", msg, topic, t);
                        });

                        subClient.ack(msg);

                    }).onFailure(t -> {
                        log.error("Receive databus message error: {}, topic={}", t.getMessage(), topic, t);
                    });

                }

            });
        }

        return subClients;


    }


}
