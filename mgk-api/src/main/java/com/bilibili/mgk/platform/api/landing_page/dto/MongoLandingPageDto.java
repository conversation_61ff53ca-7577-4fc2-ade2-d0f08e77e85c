package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @file: MongoLandingPageDto
 * @author: gaoming
 * @date: 2021/03/03
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MongoLandingPageDto implements Serializable {

    private static final long serialVersionUID = -4569767965179960573L;
    /**
     * 落地页Id
     */
    private Long pageId;

    /**
     * 配置
     */
    private Object config;

    /**
     * 账号ID
     */
    private Integer accountId;
}
