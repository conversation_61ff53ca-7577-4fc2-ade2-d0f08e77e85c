package com.bilibili.mgk.platform.biz.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

import java.io.*;

/**
 * @ClassName FileUtil
 * <AUTHOR>
 * @Date 2021/12/28 2:40 下午
 * @Version 1.0
 **/
public class FileUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtil.class);

    public static String getFileMd5(String fileName) {
        try (InputStream inputStream = new FileInputStream(fileName)) {
            return DigestUtils.md5DigestAsHex(inputStream);
        } catch (Exception e) {
            LOGGER.error("getFileMd5 error", e);
        }

        return null;
    }

    public static String getFileMd5(File file) {
        try (InputStream inputStream = new FileInputStream(file)) {
            return DigestUtils.md5DigestAsHex(inputStream);
        } catch (Exception e) {
            LOGGER.error("getFileMd5 error", e);
        }

        return null;
    }

    public static void writeFile(String fileName, String content) throws IOException {
        Writer fileWriter = new FileWriter(fileName);
        fileWriter.write(content);
        fileWriter.close();
    }
}
