package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPageRefreshLogPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkLandingPageRefreshLogDao {
    long countByExample(MgkLandingPageRefreshLogPoExample example);

    int deleteByExample(MgkLandingPageRefreshLogPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkLandingPageRefreshLogPo record);

    int insertBatch(List<MgkLandingPageRefreshLogPo> records);

    int insertUpdateBatch(List<MgkLandingPageRefreshLogPo> records);

    int insert(MgkLandingPageRefreshLogPo record);

    int insertUpdateSelective(MgkLandingPageRefreshLogPo record);

    int insertSelective(MgkLandingPageRefreshLogPo record);

    List<MgkLandingPageRefreshLogPo> selectByExampleWithBLOBs(MgkLandingPageRefreshLogPoExample example);

    List<MgkLandingPageRefreshLogPo> selectByExample(MgkLandingPageRefreshLogPoExample example);

    MgkLandingPageRefreshLogPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkLandingPageRefreshLogPo record, @Param("example") MgkLandingPageRefreshLogPoExample example);

    int updateByExampleWithBLOBs(@Param("record") MgkLandingPageRefreshLogPo record, @Param("example") MgkLandingPageRefreshLogPoExample example);

    int updateByExample(@Param("record") MgkLandingPageRefreshLogPo record, @Param("example") MgkLandingPageRefreshLogPoExample example);

    int updateByPrimaryKeySelective(MgkLandingPageRefreshLogPo record);

    int updateByPrimaryKeyWithBLOBs(MgkLandingPageRefreshLogPo record);

    int updateByPrimaryKey(MgkLandingPageRefreshLogPo record);
}