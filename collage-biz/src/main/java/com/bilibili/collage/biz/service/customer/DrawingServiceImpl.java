package com.bilibili.collage.biz.service.customer;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.api.service.ICollageService;
import com.bilibili.collage.api.service.customer.ICustomerPatternService;
import com.bilibili.collage.api.service.customer.IDownloadLogService;
import com.bilibili.collage.api.service.customer.IDrawingService;
import com.bilibili.collage.biz.render.RenderHelper;
import com.bilibili.collage.biz.utils.ZipUtils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/12/11
 **/
@Slf4j
@Service
public class DrawingServiceImpl implements IDrawingService {

    @Autowired
    private ICustomerPatternService customerPatternService;

    @Autowired
    private DrawingCacheService drawingCacheService;

    @Autowired
    private CustomerRenderService customerRenderService;

    @Autowired
    private ZipUtils zipUtils;

    @Autowired
    private IDownloadLogService downloadLogService;

    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;

    @Autowired
    private ICollageService collageService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 根据requestId查询是否存在cache
     * 有: 直接返回
     * 无: 1.匹配->2.缓存->3.渲染->4.生成response
     * step1.匹配
     *   根据request条件(素材数，主标，副标，尺寸，行业)匹配模版集合
     *   1.按模版id的纬度聚合出所有图层数据
     *   2.再根据素材数，主标，副标的值的情况筛选出符合的图层
     *   3.最后得到筛选后的模版id
     *   4.根据模版id，尺寸，行业 查询出匹配的模版
     *   5.返回匹配结果（分页）
     *
     * step2.缓存
     *   缓存请求和匹配结果
     *
     * step3.渲染
     *   异步绘图，更新缓存
     *
     * step4.生成response
     *   包括requestId,分页信息
     *
     * @param reqDto
     */
    @Override
    public DrawingRespDto draw(DrawingReqDto reqDto) {

        // 查询cache
        DrawingCacheDto cached = drawingCacheService.get(reqDto.getRequestId());
        if (!Objects.isNull(cached)) {
            log.info("matching requestId: {}", reqDto.getRequestId());
            // 延长时间
            drawingCacheService.renew(reqDto.getRequestId());
            return this.genResponse(cached);
        }

        // 匹配
        PageResult<PatternDto> matching = this.matching(reqDto);
        if (CollectionUtils.isEmpty(matching.getRecords())) {
            return DrawingRespDto.builder().build();
        }

        // 保存cache
        DrawingCacheDto drawingCache = drawingCacheService.saveDrawingCache(reqDto, matching);
        // 渲染
        this.doDrawTask(drawingCache.getRequestId(), matching.getRecords());

        // 生成response
        return this.genResponse(drawingCache);
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void download(Integer accountId, String downloadKey, OutputStream out) throws IOException {

        Assert.notNull(accountId, "帐号id不能为空");
        Assert.hasText(downloadKey, "下载的key不能为空");

        // 查询cache，查出ksUrl
        List<String> ksUrls = drawingCacheService.getImageUrlsByDownloadKey(downloadKey);
        Assert.isTrue(!CollectionUtils.isEmpty(ksUrls), "本次下载的时效已过期");

        zipUtils.compress(ksUrls, out);

        // 异步上传至bfs并入库
        saveMaterialUrlAsync(accountId, ksUrls);
    }

    private void saveMaterialUrlAsync (Integer accountId, List<String> ksUrls) {

        taskExecutor.execute(() -> {
            // 上传至bfs
            List<File> files = getFiles(ksUrls);
            List<String> bfsUrls = collageService.upload(files);
            files.forEach(File::delete);

            AccountBaseDto account = soaQueryAccountService.getAccountBaseDtoById(accountId);

            bfsUrls.forEach(url -> {
                url = collageService.replaceHttpsProtocol(url);
                CollageDownloadDto download = CollageDownloadDto.builder().url(url).mid(account.getLongMid()).build();
                downloadLogService.insertMaterial(download);
            });
        });
    }

    private List<File> getFiles (List<String> urls)  {

        if (CollectionUtils.isEmpty(urls)) {
            return Collections.emptyList();
        }
        return urls.stream().map(url -> {
            try {
                File file = File.createTempFile(RenderHelper.TEMP_PREFIX, "." + RenderHelper.OUTPUT_FORMAT);
                FileUtils.copyURLToFile(new URL(url), file);
                return file;
            } catch (IOException e) {
                log.error("getFiles fail ", e.getMessage());
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());
    }

    @Override
    public String getDownloadKey(DrawingDownloadDto downloadDto) {

        return drawingCacheService.downloadKeyGen(downloadDto.getImageUrls());
    }

    /**
     * 渲染
     * 1.初始化图片缓存
     * 2.异步渲染
     * 3.更新图片缓存
     *
     * @param matching
     */
    private void doDrawTask (String requestId, List<PatternDto> matching) {

        try {
            customerRenderService.doRenderTaskAsync(requestId, matching);
        } catch (TaskRejectedException e) { // 当任务被拒绝时，即渲染没有执行，则删除本次缓存
            log.error("requestId: {} task has be rejected, delete cache!", requestId, e);
            drawingCacheService.deleteDrawingCache(requestId);
        }
    }

    /**
     *匹配
     * @param reqDto
     * @return
     */
    private PageResult<PatternDto> matching(DrawingReqDto reqDto) {

        return customerPatternService.matchingPattern(MatchingPatternDto.builder()
                .mateCount(reqDto.getMateCount())
                .mainTitle(reqDto.getMainTitle())
                .subTitle(reqDto.getSubTitle())
                .adSizeId(reqDto.getAdSizeId())
                .industryId(reqDto.getIndustryId())
                .tagId(reqDto.getTagId())
                .page(reqDto.getPage())
                .size(reqDto.getSize())
                .build());
    }

    // 根据请求对象，匹配对象，缓存id返回响应
    private DrawingRespDto genResponse(DrawingCacheDto cache) {
        return DrawingRespDto.builder()
                .requestId(cache.getRequestId())
                .totalCount(cache.getRenderInfo().getTotal())
                .page(cache.getRenderInfo().getRequest().getPage())
                .pics(cache.getRenderResult())
                .build();
    }
}
