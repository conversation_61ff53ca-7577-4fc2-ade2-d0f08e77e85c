package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkInspirationArticlePo implements Serializable {
    /**
     * 自增Id
     */
    private Integer id;

    /**
     * 文章id
     */
    private Long articleId;

    /**
     * 用户Id
     */
    private Integer accountId;

    /**
     * 用户名称
     */
    private String creator;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 行业 1-电商 2-游戏 3-网服 4-教育 5-其他
     */
    private String industry;

    /**
     * 阅读数
     */
    private Long articleRead;

    /**
     * 点赞数
     */
    private Long articleLike;

    /**
     * 状态 0-启用 1-禁用
     */
    private Integer articleStatus;

    /**
     * 是否删除 0-正常 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 内容
     */
    private String content;

    private static final long serialVersionUID = 1L;
}