package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.api.hot_ads.service.IHotAdsIndustryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @file: HotAdsIndustryServiceImpl
 * @author: gaoming
 * @date: 2021/01/09
 * @version: 1.0
 * @description:
 **/
@Service
@Slf4j
public class HotAdsIndustryServiceImpl implements IHotAdsIndustryService {

    @Autowired
    private HotAdsIndustryServiceDelegate hotAdsIndustryServiceDelegate;

    @Override
    public List<String> getIndustry() {
        return hotAdsIndustryServiceDelegate.getIndustry();
    }
}
