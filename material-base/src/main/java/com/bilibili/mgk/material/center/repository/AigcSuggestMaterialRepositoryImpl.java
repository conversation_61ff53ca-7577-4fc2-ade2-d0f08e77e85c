package com.bilibili.mgk.material.center.repository;

import com.bilibili.mgk.material.center.repository.base.MgkAigcSuggestionMaterialMapper;
import com.bilibili.mgk.material.center.repository.base.MgkRejectedMaterialAigcRecordMapper;
import com.bilibili.mgk.material.center.repository.po.MgkAigcSuggestionMaterialPoExample;
import com.bilibili.mgk.material.center.repository.po.MgkRejectedMaterialAigcRecordPoExample;
import com.bilibili.mgk.material.center.repository.po.PersistObjectMapping;
import com.bilibili.mgk.material.center.service.aigc.model.AigcSuggestMaterial;
import com.bilibili.mgk.material.center.service.aigc.model.RejectedMaterialAigcRecord;
import com.bilibili.mgk.material.center.service.aigc.model.RejectedRecordWithAigcMaterial;
import com.bilibili.mgk.material.center.service.creative.vo.RingPage;
import com.bilibili.mgk.material.center.service.creative.vo.cursor.Cursor;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/7
 */
@Repository
public class AigcSuggestMaterialRepositoryImpl implements AigcSuggestMaterialRepository {

    @Resource
    private MgkRejectedMaterialAigcRecordMapper mgkRejectedMaterialAigcRecordMapper;

    @Resource
    private MgkAigcSuggestionMaterialMapper mgkAigcSuggestionMaterialMapper;


    @Override
    public List<RejectedMaterialAigcRecord> selectAllByTypeReasonKeyAndUk(String type, String reason,
            List<String> uks) {

        MgkRejectedMaterialAigcRecordPoExample example = new MgkRejectedMaterialAigcRecordPoExample();

        example.createCriteria()
                .andMaterialIdTypeEqualTo(type)
                .andRejectKeyEqualTo(reason)
                .andMaterialUkIn(uks);

        return mgkRejectedMaterialAigcRecordMapper.selectByExample(example)
                .stream()
                .map(PersistObjectMapping.instance::toModel)
                .collect(Collectors.toList());

    }


    @Override
    public List<RejectedRecordWithAigcMaterial> selectAllByTypeReasonKeyAndUkWithAigcMaterial(String type,
            String reason, Map<String, Cursor<AigcSuggestMaterial>> uks) {

        if (MapUtils.isEmpty(uks)) {
            return new ArrayList<>();
        }


        MgkRejectedMaterialAigcRecordPoExample example = new MgkRejectedMaterialAigcRecordPoExample();
        example.createCriteria()
                .andMaterialIdTypeEqualTo(type)
                .andRejectKeyEqualTo(reason)
                .andMaterialUkIn(uks.keySet().stream().distinct().collect(Collectors.toList()));

        List<RejectedMaterialAigcRecord> records = mgkRejectedMaterialAigcRecordMapper.selectByExample(example)
                .stream()
                .map(PersistObjectMapping.instance::toModel)
                .collect(Collectors.toList());

        Map<String, RejectedMaterialAigcRecord> uk2Records = records.stream()
                .collect(Collectors.toMap(
                        r -> r.getMaterialUk(),
                        r -> r,
                        (a, b) -> b
                ));

        return withRejectedMaterialAigcRecord(records,
                uks.entrySet().stream().filter(entry -> uk2Records.containsKey(entry.getKey()))
                        .collect(Collectors.toMap(
                                entry -> uk2Records.get(entry.getKey()),
                                entry -> entry.getValue(),
                                (a, b) -> b
                        ))
        );

    }


    @Override
    public List<RejectedRecordWithAigcMaterial> selectAllByReasonAndMaterialIdWithAigcMaterial(String reason,
            Map<String, Cursor<AigcSuggestMaterial>> materialIds) {

        if (MapUtils.isEmpty(materialIds)) {
            return new ArrayList<>();
        }


        MgkRejectedMaterialAigcRecordPoExample example = new MgkRejectedMaterialAigcRecordPoExample();
        example.createCriteria()
                .andRejectKeyEqualTo(reason)
                .andMaterialIdIn(materialIds.keySet().stream().collect(Collectors.toList()));

        List<RejectedMaterialAigcRecord> records = mgkRejectedMaterialAigcRecordMapper.selectByExample(example)
                .stream().map(PersistObjectMapping.instance::toModel).collect(Collectors.toList());

        Map<String, RejectedMaterialAigcRecord> materialId2Record = records.stream().collect(Collectors.toMap(
                r -> r.getMaterialId(),
                r -> r,
                (existing, replacement) -> existing
        ));

        return withRejectedMaterialAigcRecord(records,

                materialIds.entrySet().stream()
                        .filter(entry -> materialId2Record.containsKey(entry.getKey()))
                        .collect(Collectors.toMap(
                                entry -> materialId2Record.get(entry.getKey()),
                                entry -> entry.getValue(),
                                (a, b) -> b
                        ))
        );


    }

//
//    private RejectedRecordWithAigcMaterial withRejectedMaterialAigcRecord(RejectedMaterialAigcRecord record,
//            int offset, int limit) {
//
//        if (record == null) {
//            return null;
//        }
//
//        List<RejectedRecordWithAigcMaterial> r = withRejectedMaterialAigcRecord(Lists.newArrayList(record), offset,
//                limit);
//
//        if (CollectionUtils.isEmpty(r)) {
//            return PersistObjectMapping.instance.toRecordWithAigc(record,
//                    new OffsetLimitCursor(0, 0).serialize(), new ArrayList<>());
//
//        }
//
//        return r.get(0);
//
//    }


    private List<RejectedRecordWithAigcMaterial> withRejectedMaterialAigcRecord(
            List<RejectedMaterialAigcRecord> records,
            Map<RejectedMaterialAigcRecord, Cursor<AigcSuggestMaterial>> cursors) {

        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }

        // 为了简单所有的请求都是一律内存分页。实际上最大请求是来自于批量。批量的不方便数据库分页。
        Map<Long, List<AigcSuggestMaterial>> recordId2AigcMaterials = this.selectAiSuggestMaterialByRejectRecordIds(
                records.stream().map(r -> r.getId()).collect(Collectors.toList())
        ).stream().collect(Collectors.groupingBy(
                aigc -> aigc.getRejectRecordId(),
                Collectors.toList()
        ));

        return records.stream().map(r -> {

            List<AigcSuggestMaterial> aigcMaterialsSortedByScore = Optional.ofNullable(
                            recordId2AigcMaterials.get(r.getId())).orElse(new ArrayList<>())
                    .stream()
                    .sorted((o1, o2) -> Double.compare(o2.getAigcMaterialScore(), o1.getAigcMaterialScore()))
                    .collect(Collectors.toList());

            Cursor<AigcSuggestMaterial> cursor = cursors.get(r);


            if (CollectionUtils.isEmpty(aigcMaterialsSortedByScore)) {
                return PersistObjectMapping.instance.toRecordWithAigc(r,
                        cursor.dummy().serialize(), new ArrayList<>());
            }

            RingPage<AigcSuggestMaterial> page = cursor.toPage(aigcMaterialsSortedByScore);

            return PersistObjectMapping.instance.toRecordWithAigc(r, page.getNextCursor(), page.getData());

        }).collect(Collectors.toList());


    }


    @Override
    public List<AigcSuggestMaterial> selectAiSuggestMaterialByRejectRecordIds(List<Long> rejectRecordIds) {

        MgkAigcSuggestionMaterialPoExample example = new MgkAigcSuggestionMaterialPoExample();

        example.createCriteria()
                .andRejectRecordIdIn(rejectRecordIds)
                .andDeletedEqualTo((byte) 0)
        ;

        return mgkAigcSuggestionMaterialMapper.selectByExample(example)
                .stream()
                .map(PersistObjectMapping.instance::toModel)
                .collect(Collectors.toList());

    }


    @Override
    public int updateBatchBySelective(RejectedMaterialAigcRecord rejectedMaterialAigcRecord, List<Long> ids) {

        MgkRejectedMaterialAigcRecordPoExample example = new MgkRejectedMaterialAigcRecordPoExample();
        example.createCriteria()
                .andIdIn(ids);

        return mgkRejectedMaterialAigcRecordMapper.updateByExampleSelective(
                PersistObjectMapping.instance.toPo(rejectedMaterialAigcRecord), example);
    }


    @Override
    public int insertSelective(RejectedMaterialAigcRecord record) {

        return mgkRejectedMaterialAigcRecordMapper.insertSelective(PersistObjectMapping.instance.toPo(record));

    }


    @Override
    public void insertAiSuggestMaterialSelective(AigcSuggestMaterial aiSuggestMaterial) {

        mgkAigcSuggestionMaterialMapper.insertSelective(PersistObjectMapping.instance.toPo(aiSuggestMaterial));
    }

    @Override
    public void updateSelective(RejectedMaterialAigcRecord form) {

        mgkRejectedMaterialAigcRecordMapper.updateByPrimaryKeySelective(PersistObjectMapping.instance.toPo(form));

    }


}
