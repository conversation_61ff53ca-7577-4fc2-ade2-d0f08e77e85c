package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.ws.rs.DefaultValue;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/11
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorksVo {

    @ApiModelProperty(value = "ids")
    private List<Integer> ids;

    @ApiModelProperty(value = "作品名称")
    private String name;

    @ApiModelProperty(value = "尺寸Id")
    private List<Integer> collage_size_ids;

    @ApiModelProperty(value = "比例类型")
    private List<Integer> image_ratio_type_list;

    @ApiModelProperty(value = "模板Id")
    private List<Integer> pattern_ids;

    @ApiModelProperty(value = "作品来源")
    private List<Integer> works_origins;

    @ApiModelProperty(value = "是否同步")
    private Integer is_synchro;

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "页面大小")
    private Integer size;

    @ApiModelProperty(value = "开始时间")
    private Long from_time;

    @ApiModelProperty(value = "结束时间")
    private Long to_time;

    @ApiModelProperty(value = "作品比例")
    private List<Integer> works_radios;
}
