package com.bilibili.mgk.platform.common;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.awt.*;

/**
 * <AUTHOR>
 * @date 2018/12/3
 * 字体粗细
 **/
@NoArgsConstructor
public enum CollageFontWeightEnum {

    NORMAL("normal", Font.PLAIN),
    BOLDER("bolder", Font.BOLD);

    @Getter
    private String code;

    @Getter
    private Integer fontStyle;

    CollageFontWeightEnum(String code, Integer fontStyle) {

        this.code = code;
        this.fontStyle = fontStyle;
    }

    public static CollageFontWeightEnum getByCode(String code) {
        if (code == null) {
            throw new IllegalArgumentException("字体粗细不可为空");
        }

        for(CollageFontWeightEnum lrt: values()) {
            if(lrt.getCode().equals(code)) {
                return lrt;
            }
        }

        throw new IllegalArgumentException("未知的字体粗细");
    }
}
