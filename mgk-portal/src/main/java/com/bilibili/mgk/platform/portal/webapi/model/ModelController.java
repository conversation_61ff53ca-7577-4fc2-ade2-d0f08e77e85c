package com.bilibili.mgk.platform.portal.webapi.model;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.mgk.platform.api.model.dto.MgkModelDto;
import com.bilibili.mgk.platform.api.model.dto.MgkModelUsedDto;
import com.bilibili.mgk.platform.api.model.dto.QueryModelParamDto;
import com.bilibili.mgk.platform.api.model.dto.QueryTradeParamDto;
import com.bilibili.mgk.platform.api.model.service.IMgkModelService;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.MgkRightLabelEnum;
import com.bilibili.mgk.platform.common.ModelStatusEnum;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Pagination;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebModelService;
import com.bilibili.mgk.platform.portal.webapi.model.vo.*;
import com.google.common.collect.Lists;
import com.mysema.commons.lang.Assert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/02
 **/
@Controller
@RequestMapping("web_api/v1/model")
@Api(value = "/model", tags = "模板相关")
public class ModelController extends BasicController {

    @Autowired
    private IMgkModelService mgkModelService;
    @Autowired
    private WebModelService webModelService;

    @ApiOperation(value = "模板列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Pagination<List<MgkModelVo>>> getModelList(@ApiIgnore Context context,
                                                        @ApiParam("模板ID") @RequestParam(value = "model_ids", defaultValue = "") List<String> modelIds,
                                                        @ApiParam("名称") @RequestParam(value = "name_like", defaultValue = "") String nameLike,
                                                        @ApiParam("模板样式") @RequestParam(value = "model_styles", defaultValue = "") List<Integer> modelStyles,
                                                        @ApiParam("模板类型") @RequestParam(value = "model_type", defaultValue = "") List<Integer> modelTypes,
                                                        @ApiParam("状态") @RequestParam(value = "status", defaultValue = "") List<Integer> statusList,
                                                        @ApiParam("行业ID") @RequestParam(value = "trade_ids", defaultValue = "") List<String> tradeIds,
                                                        @ApiParam("组件类型 0-模版 1-模块") @RequestParam(value = "type", defaultValue = "0") Integer type,
                                                        @ApiParam("模块样式id") @RequestParam(value = "module_style_ids", defaultValue = "") List<Integer> moduleStyleIds,
                                                        @ApiParam("模块内容id") @RequestParam(value = "module_content_ids", defaultValue = "") List<Integer> moduleContentIds,
                                                        @ApiParam("管理员操作") @RequestParam(value = "is_admin", defaultValue = "0") Integer isAdmin,
                                                        @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                        @ApiParam("每页大小") @RequestParam(value = "size", defaultValue = "15") Integer size,
                                                        @ApiParam("一级类目") @RequestParam(value = "parent_trade_ids", defaultValue = "") List<String> parentTradeIds,
                                                        @ApiParam("排序") @RequestParam(value = "order_by", defaultValue = "ctime desc") String orderBy) throws ServiceException {
        QueryModelParamDto modelParamDto = QueryModelParamDto.builder()
                .accountIds(Lists.newArrayList(context.getAccountId()))
                .modelIds(CollectionUtils.isEmpty(modelIds) ? Collections.emptyList() : modelIds.stream().map(Long::parseLong).collect(Collectors.toList()))
                .nameLike(nameLike)
                .modelStyles(CollectionUtils.isEmpty(modelStyles) ? Collections.emptyList() : modelStyles)
                .modelTypes(CollectionUtils.isEmpty(modelTypes) ? Collections.emptyList() : modelTypes)
                .statusList(CollectionUtils.isEmpty(statusList) ? ModelStatusEnum.NOT_DELETED_STATUS_LIST : statusList)
                .tradeIds(CollectionUtils.isEmpty(tradeIds) ? Collections.emptyList() : tradeIds.stream().map(Long::parseLong).collect(Collectors.toList()))
                .parentTradeIds(CollectionUtils.isEmpty(parentTradeIds) ? Collections.emptyList() : parentTradeIds.stream().map(Long::parseLong).collect(Collectors.toList()))
                .isDeleted(IsDeleted.VALID.getCode())
                .orderBy(MgkConstants.ORDER_BY_MTIME_DESC)
                .moduleContentIds(CollectionUtils.isEmpty(moduleContentIds) ? Collections.emptyList() : moduleContentIds)
                .moduleStyleIds(CollectionUtils.isEmpty(moduleStyleIds) ? Collections.emptyList() : moduleStyleIds)
                .type(type)
                .isAdmin(isAdmin)
                .orderBy(orderBy)
                .build();
        PageResult<MgkModelDto> pageResult = mgkModelService.getModelDtos(modelParamDto, Page.valueOf(page, size));
        if (pageResult.getTotal() == 0 || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), Collections.emptyList()));
        }
        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(),
                webModelService.convertModelDtos2Vos(pageResult.getRecords())));
    }

    @ApiOperation(value = "查询模板信息")
    @RequestMapping(value = "/{model_id}", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<MgkModelVo> getModel(@ApiIgnore Context context,
                                  @ApiParam("模板ID") @PathVariable("model_id") String modelId) throws ServiceException {
        long modelIdLong = Long.parseLong(modelId);
        Assert.isTrue(modelIdLong > 0L, "模板id不能为0");
        List<MgkModelDto> modelDtos = mgkModelService.getModelsByModelIds(Lists.newArrayList(modelIdLong));
        if (CollectionUtils.isEmpty(modelDtos)) {
            return Response.SUCCESS(null);
        }
        return Response.SUCCESS(webModelService.convertModelDtos2Vos(modelDtos).get(0));
    }

    @ApiOperation(value = "编辑模板-设置")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> updateModelConfig(@ApiIgnore Context context,
                                       @ApiParam("模板属性") @Valid @RequestBody UpdateModelVo updateModelVo) {
        webModelService.validatePermission(context, Lists.newArrayList(Long.parseLong(updateModelVo.getModel_id())));
        mgkModelService.update(this.getOperator(context), webModelService.convertUpdateModelVo2Dto(updateModelVo));
        return Response.SUCCESS(null);
    }


    @ApiOperation(value = "新建模板")
    @RequestMapping(value = "", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<String> create(@ApiIgnore Context context,
                            @ApiParam("落地页信息") @Valid @RequestBody NewModelVo newModelVo) throws ServiceException {
        webModelService.validatePermission(context, newModelVo.getType(), newModelVo.getIs_admin());
        long modelId = mgkModelService.create(this.getOperator(context), webModelService.convertNewModelVo2Dto(context.getAccountId(), newModelVo));
        return Response.SUCCESS(String.valueOf(modelId));
    }

    @ApiOperation(value = "发布模板")
    @RequestMapping(value = "{model_id}/publish", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> publish(@ApiIgnore Context context,
                             @ApiParam("模板Id") @PathVariable("model_id") String modelId) throws ServiceException {
        long modelIdLong = Long.parseLong(modelId);
        webModelService.validatePermission(context, Lists.newArrayList(modelIdLong));
        mgkModelService.publish(this.getOperator(context), modelIdLong);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "下线")
    @RequestMapping(value = "{model_id}/downline", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> downline(@ApiIgnore Context context,
                              @ApiParam("模板Id") @PathVariable("model_id") String modelId) throws ServiceException {
        long modelIdLong = Long.parseLong(modelId);
        webModelService.validatePermission(context, Lists.newArrayList(modelIdLong));
        mgkModelService.downline(this.getOperator(context), modelIdLong);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "删除")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    public
    @ResponseBody
    Response<Object> batchDelete(@ApiIgnore Context context,
                                 @ApiParam("模板id") @RequestParam("model_id") List<String> modelIds) throws ServiceException {
        List<Long> modelIdsLong = modelIds.stream().map(Long::parseLong).collect(Collectors.toList());
        webModelService.validatePermission(context, modelIdsLong);
        mgkModelService.batchDisable(this.getOperator(context), modelIdsLong);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "查询行业")
    @RequestMapping(value = "/trade", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<MgkTradeVo>> getTrade(@ApiIgnore Context context,
                                        @ApiParam("父级行业IDs") @RequestParam(value = "parent_trade_ids", defaultValue = "") List<String> parentTradeIds,
                                        @ApiParam("行业IDs") @RequestParam(value = "trade_ids", defaultValue = "") List<String> tradeIds,
                                        @ApiParam("级别") @RequestParam(value = "level", defaultValue = "") List<Integer> level,
                                        @ApiParam("名称") @RequestParam(value = "name_like", defaultValue = "") String nameLike) {

        QueryTradeParamDto queryTradeParamDto = QueryTradeParamDto.builder()
                .parentTradeIds(CollectionUtils.isEmpty(parentTradeIds) ? Collections.emptyList() : parentTradeIds.stream().map(Long::parseLong).collect(Collectors.toList()))
                .tradeIds(CollectionUtils.isEmpty(tradeIds) ? Collections.emptyList() : tradeIds.stream().map(Long::parseLong).collect(Collectors.toList()))
                .level(CollectionUtils.isEmpty(level) ? Collections.emptyList() : level)
                .nameLike(nameLike)
                .isDeleted(IsDeleted.VALID.getCode())
                .build();
        List<MgkTradeVo> mgkTradeVos = webModelService.convertTradeDtos2Vos(mgkModelService.getTradeDtos(queryTradeParamDto));
        return Response.SUCCESS(mgkTradeVos);
    }

    @ApiOperation(value = "新建行业")
    @RequestMapping(value = "/trade", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<String> createTrade(@ApiIgnore Context context,
                                 @ApiParam("行业信息") @Valid @RequestBody NewTradeVo newTradeVo) throws ServiceException {
        webModelService.validatePermission(context, 0, 0);
        long tradeId = mgkModelService.createTrade(this.getOperator(context), webModelService.convertNewTradeVo2Dto(newTradeVo));
        return Response.SUCCESS(String.valueOf(tradeId));
    }

    @ApiOperation(value = "用户模板权限接口")
    @RequestMapping(value = "/rights", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<String>> hasRights(@ApiIgnore Context context) throws ServiceException {
        List<MgkRightLabelEnum> hasRights = mgkModelService.hasRights(this.getOperator(context));
        return Response.SUCCESS(hasRights.stream().map(MgkRightLabelEnum::getCode).collect(Collectors.toList()));
    }

    @ApiOperation(value = "查询用户已经使用的所有模板")
    @RequestMapping(value = "/used")
    public
    @ResponseBody
    Response<List<MgkModelUsedVo>> getModelUsed(@ApiIgnore Context context) {
        List<MgkModelUsedDto> modelUsedDtos = mgkModelService.getModelUsed(this.getOperator(context));
        return Response.SUCCESS(webModelService.convertModelUsedDtos2Vos(modelUsedDtos));
    }

    @ApiOperation(value = "热词")
    @RequestMapping(value = "/hotWords", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<String>> hotWords(@ApiIgnore Context context) throws ServiceException {
        return Response.SUCCESS(mgkModelService.getHotWords());
    }

}
