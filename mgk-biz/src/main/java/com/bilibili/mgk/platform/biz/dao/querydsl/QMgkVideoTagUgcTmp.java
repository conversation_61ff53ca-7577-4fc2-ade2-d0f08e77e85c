package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkVideoTagUgcTmpQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkVideoTagUgcTmp is a Querydsl query type for MgkVideoTagUgcTmpQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkVideoTagUgcTmp extends com.querydsl.sql.RelationalPathBase<MgkVideoTagUgcTmpQueryDSLPo> {

    private static final long serialVersionUID = -1184029101;

    public static final QMgkVideoTagUgcTmp mgkVideoTagUgcTmp = new QMgkVideoTagUgcTmp("mgk_video_tag_ugc_tmp");

    public final NumberPath<Long> avid = createNumber("avid", Long.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final com.querydsl.sql.PrimaryKey<MgkVideoTagUgcTmpQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkVideoTagUgcTmp(String variable) {
        super(MgkVideoTagUgcTmpQueryDSLPo.class, forVariable(variable), "null", "mgk_video_tag_ugc_tmp");
        addMetadata();
    }

    public QMgkVideoTagUgcTmp(String variable, String schema, String table) {
        super(MgkVideoTagUgcTmpQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkVideoTagUgcTmp(String variable, String schema) {
        super(MgkVideoTagUgcTmpQueryDSLPo.class, forVariable(variable), schema, "mgk_video_tag_ugc_tmp");
        addMetadata();
    }

    public QMgkVideoTagUgcTmp(Path<? extends MgkVideoTagUgcTmpQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_video_tag_ugc_tmp");
        addMetadata();
    }

    public QMgkVideoTagUgcTmp(PathMetadata metadata) {
        super(MgkVideoTagUgcTmpQueryDSLPo.class, metadata, "null", "mgk_video_tag_ugc_tmp");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(avid, ColumnMetadata.named("avid").withIndex(4).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(2).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(3).ofType(Types.TIMESTAMP).withSize(19).notNull());
    }

}

