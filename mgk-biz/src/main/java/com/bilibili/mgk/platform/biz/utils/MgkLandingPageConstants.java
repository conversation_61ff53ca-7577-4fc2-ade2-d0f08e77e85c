package com.bilibili.mgk.platform.biz.utils;

import com.bilibili.adp.common.enums.SalesType;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 上午10:43
 */
public class MgkLandingPageConstants {

    /**
     * 线索通 sales types
     *
     */
    public static List<Integer> CLUE_PASS_SALES_TYPES = Arrays.asList(SalesType.XST_CPC.getCode(),
            SalesType.XST_CPA.getCode());

    /**
     * 锚点 sales types
     *
     */
    public static List<Integer> ANCHOR_SALES_TYPES = Arrays.asList(SalesType.NATIVE_ANCHOR.getCode());

}
