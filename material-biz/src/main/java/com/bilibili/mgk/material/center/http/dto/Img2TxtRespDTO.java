package com.bilibili.mgk.material.center.http.dto;

import com.bilibili.mgk.material.center.service.aigc.dto.ImageToTextResp;
import com.bilibili.mgk.material.center.service.converter.BusinessPojoConverter;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/19
 */
@Data
@Accessors(chain = true)
public class Img2TxtRespDTO {


    private String caption;

    private Integer code;

    private String msg;

    private String detail;


    public ImageToTextResp toResp() {

        if (StringUtils.isEmpty(caption)) {

            throw new RuntimeException("图片文本分析失败:" + Optional.ofNullable(detail).orElse(""));
        }

        return BusinessPojoConverter.converter.toResp(this)
                .setPrompt(caption);
    }

}
