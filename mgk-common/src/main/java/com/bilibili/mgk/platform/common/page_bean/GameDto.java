package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/26 11:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GameDto implements Serializable {
    private static final long serialVersionUID = -2652479342344618016L;
    //游戏唯一标识ID
    private Integer gameBaseId;
    //游戏名
    private String gameName;
    //游戏icon
    private String gameIcon;
    //开放平台游戏状态
    private Integer gameState;
    //开放平台游戏状态描述
    private String gameStateDesc;
    //游戏中心游戏状态
    private Integer gameStatus;
    //游戏中心游戏状态描述
    private String gameStatusDesc;
    //是否上下架，1：上架，0：下架
    private Integer onLine;
    //是否上下架描述，1：上架，0：下架
    private String onLineDesc;
    //1=广告包，0：联运包
    private Integer channelId;
}
