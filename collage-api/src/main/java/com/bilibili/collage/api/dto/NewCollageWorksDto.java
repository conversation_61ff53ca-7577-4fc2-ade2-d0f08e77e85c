package com.bilibili.collage.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/11
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewCollageWorksDto implements Serializable {
    private static final long serialVersionUID = 8267745360991039111L;

    /**
     * 作品名称
     */
    private String name;

    /**
     * 尺寸Id
     */
    private Integer collageSizeId;


    /**
     * 模板Id
     */
    private Integer patternId;

    /**
     * 作品来源
     */
    private Integer worksOrigin;

    /**
     * 图层
     */
    private List<LayerDto> layerDtos;

    /**
     * 作品渲染图片
     */
    private String worksUrl;

    /**
     * 作品md5
     */
    private String worksMd5;

    /**
     * 作品大小
     */
    private Long worksSize;

    /**
     * 总时长
     */
    private Integer totalDuration;

    /**
     * 每帧时长
     */
    private Integer durationPerFrame;

    /**
     * 帧数
     */
    private Integer frames;

    /**
     * 轮播次数
     */
    private Integer roundsNumber;

    /**
     * 作品封面
     */
    private CollageCoverDto cover;

}
