package com.bilibili.mgk.material.center.service.creative.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */
@Getter
@RequiredArgsConstructor
public enum UpRewardType {


    all(0, "全部"),


    huahuo(1, "花火"),

    rewarded(2, "悬赏带货");


    private final int code;

    private final String desc;

    public static UpRewardType getByCode(Integer rewardType) {
        if (rewardType == null) {
            return UpRewardType.all;
        }

        for (UpRewardType upRewardType : UpRewardType.values()) {
            if (upRewardType.getCode() == rewardType) {
                return upRewardType;
            }
        }
        return UpRewardType.all;
    }

}
