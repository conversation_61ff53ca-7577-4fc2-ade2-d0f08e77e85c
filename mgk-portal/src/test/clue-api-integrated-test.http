### 查询内部建站页的线索数据
GET http://localhost:80/mgk/api/open_api/v1/clues/get?advertiser_id=10031&sign=9f3f1a29948d73dc6dab644016ea5a33&ts=1587553062481&start_time=2018-01-01&end_time=2021-01-01

### 签名失败的情况
GET http://localhost:80/mgk/api/open_api/v1/clues/get?advertiser_id=8&sign=虚假的签名&ts=1587553062481&start_time=2020-01-01

### 参数错误的情况
GET http://localhost:80/mgk/api/open_api/v1/clues/get?advertiser_id=8&sign=418a67d07db25a201d954e8825de6afb&ts=1587553062481&start_time=2020-01-01&end_time=2018-01-01

### 上报外链状态变更(重复上报同一个状态会被拒绝)
POST http://localhost:80/mgk/api/open_api/v1/clues/update?track_id=LFgsZQhNTkvQ4K41F4uyT2SAyMZ1vrqHar8grhE5JCiuLzJU3mX0HLnn5HJWnCo6Azf_sqx1jVG8LDZ-ORVCyvoAZ2_d3pJd1haKVm3hsSg=&clue_state=1
Content-Type: application/json

### 上报建站页状态变更同时上报外链数据的情况(请求被拒绝)
POST http://localhost:80/mgk/api/open_api/v1/clues/update?track_id=Dp-v_0s9a65nw7mtfWBja8Kj65n5-HIcrj0aKHHQjyifw6kCxTEDdd3NPzx0ltxW1PZU5i9l1aHDX_k46-9-ypr0rk45NBLNPeL_Fc5jQIKyO1zX_lN21RO_iMjl4w_-&clue_state=2
Content-Type: application/json

{
  "list": [
    {
      "label": "您的手机",
      "value": "18888888888",
      "type": "telephone"
    },
    {
      "label": "小孩子才选一个",
      "value": "C",
      "type": "radio"
    },
    {
      "label": "成年人全部都要",
      "value": "A;B",
      "type": "checkbox"
    }
  ]
}
