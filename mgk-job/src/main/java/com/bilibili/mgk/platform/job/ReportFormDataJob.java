package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.data.service.IMgkDataService;
import com.bilibili.mgk.platform.api.form.service.IMgkClueService;
import com.bilibili.mgk.platform.biz.service.ChinaMobileFormPaidReportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@JobHandler("ReportFormDataJob")
public class ReportFormDataJob extends IJobHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IMgkDataService mgkDataService;
    @Autowired
    private IMgkClueService mgkClueService;
    @Autowired
    private ChinaMobileFormPaidReportService chinaMobileFormPaidReportService;

    @Value("${mgk.report.form.data.job:false}")
    private boolean reportData;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        LOGGER.info(" ReportFormDataJob start.......");

        try {
            if (reportData) {
                mgkDataService.reportFormDataJob();
                mgkClueService.reportClueJob();
                chinaMobileFormPaidReportService.reportJob();
            }
        } catch (Exception e) {
            LOGGER.info("ReportFormDataJob encounter an exception:{}", e);
        }

        LOGGER.info("ReportFormDataJob finish.......");
        return SUCCESS;
    }
}
