package com.bilibili.collage.api.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/12/23 上午11:01
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommercialGeneralVideoQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer accountId;
    private Long avid;

    private Integer page;
    private Integer pageSize;

    private String orderBy;


}
