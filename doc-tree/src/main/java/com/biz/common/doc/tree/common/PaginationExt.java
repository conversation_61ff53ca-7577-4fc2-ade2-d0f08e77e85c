package com.biz.common.doc.tree.common;

import java.util.Optional;
import java.util.function.Function;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/24
 */
@Getter
public class PaginationExt<T, E> extends Pagination<T> {

    private E extra;

    public PaginationExt(Integer page, Integer total_count, T data, E extra) {

        super(page, total_count, data);
        this.extra = extra;
    }

    public static <T, E> PaginationExt<T, E> extent(Pagination<T> pagination, E extra) {
        return new PaginationExt<>(pagination.getPage(), pagination.getTotalCount(), pagination.getData(), extra);
    }


    public <F> PaginationExt<F, E> map(Function<T, F> mapping) {
        return new PaginationExt<>(this.getPage(), this.getTotalCount(),

                Optional.ofNullable(this.getData())
                        .map(mapping)
                        .orElse(null), this.extra
        );
    }
}
