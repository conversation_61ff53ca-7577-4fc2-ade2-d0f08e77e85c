package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 */

@AllArgsConstructor
public enum H5DownloadStrategyEnum {

    ENABLE(0, "必加下载按钮账户id名单", "mgk_h5_down_acc_black",
            1, "LandingPageDownloadAccountBlackList"),

    DISABLE_BY_ACCOUNT(1, "去除下载按钮客户id名单", "mgk_h5_down_acc_white",
            0, "LandingPageDownloadAccountList"),

    DISABLE_BY_CUSTOMER(2, "去除下载按钮客户id名单", "mgk_h5_down_cus_white",
            0, "LandingPageDownloadCustomerList"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    @Getter
    private final String redisKey;

    @Getter
    //小程序置底下载按钮 0-不出 1-出
    private final Integer h5DownloadHit;

    @Getter
    private final String abyssKey;

    public static H5DownloadStrategyEnum getByCode(int code) {
        for (H5DownloadStrategyEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code H5DownloadStrategyEnum " + code);
    }

}
