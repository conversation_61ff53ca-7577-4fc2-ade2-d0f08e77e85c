package com.bilibili.mgk.platform.biz.service.wechat.delegate;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.data.dto.WechatPackageDataCountDto;
import com.bilibili.mgk.platform.api.data.service.IMgkWechatPackageDataService;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountCreateDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountListDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountQueryDto;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatAccountService;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatPackageService;
import com.bilibili.mgk.platform.common.IsModelEnum;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.mgk.platform.common.WechatTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName WechatAccountValidateService
 * <AUTHOR>
 * @Date 2022/5/31 12:32 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkWechatAccountValidateService {

    private static final Integer PAGE_SIZE_LIMIT = 100;

    @Autowired
    private IMgkWechatAccountService mgkWechatAccountService;
    @Autowired
    private IMgkWechatPackageService mgkWechatPackageService;
    @Autowired
    private IMgkWechatPackageDataService mgkWechatPackageDataService;
    @Autowired
    private IMgkLandingPageService mgkLandingPageService;

    public void validCreateDto(WechatAccountCreateDto createDto) {
        Assert.notNull(createDto, "创建对象不可为空");
        Assert.isTrue(!StringUtils.isEmpty(createDto.getWechatAccount()), "请输入添加的微信账号名称");
        Assert.notNull(createDto.getType(), "微信账号类型不可为空");
        Assert.isTrue(Utils.isPositive(createDto.getAccountId()), "账户id不可为空");
        WechatTypeEnum.getByCode(createDto.getType());
        List<WechatAccountListDto> existListDtos = queryListByWechatAccountEqual(createDto.getWechatAccount(), createDto.getAccountId());
        Assert.isTrue(CollectionUtils.isEmpty(existListDtos), "微信号已存在");
        if (!WechatTypeEnum.OTHER.getCode().equals(createDto.getType())) {
            Assert.isTrue(!StringUtils.isEmpty(createDto.getWechatName()), "原始微信号不可为空");
        }
    }

    public void validUpdateDto(WechatAccountCreateDto updateDto, Operator operator) {
        Assert.notNull(updateDto, "更新对象不可为空");
        Assert.isTrue(Utils.isPositive(updateDto.getId()), "更新id不可为空");
        WechatTypeEnum.getByCode(updateDto.getType());
        if (!WechatTypeEnum.OTHER.getCode().equals(updateDto.getType())) {
            Assert.isTrue(!StringUtils.isEmpty(updateDto.getWechatName()), "原始微信号不可为空");
        }
        validPermission(updateDto.getId(), operator);
        List<WechatAccountListDto> existListDtos = queryListByWechatAccountEqual(updateDto.getWechatAccount(), updateDto.getAccountId());
        Assert.isTrue(CollectionUtils.isEmpty(existListDtos), "微信号已存在");
        List<Integer> packageIds = mgkWechatAccountService.getAccountWechatPackageIds(updateDto.getId());
        if (!CollectionUtils.isEmpty(packageIds)) {
            List<Long> pageIds = mgkWechatPackageService.getPageIdsByPackageIds(packageIds);
            List<MgkLandingPageDto> pagePos = mgkLandingPageService.getLandingPageDtoByPageIds(pageIds);
            List<Long> publishedPageIds = pagePos.stream()
                    .filter(pagePo -> LandingPageStatusEnum.PUBLISHED.getCode().equals(pagePo.getStatus()))
                    .filter(pagePo -> IsModelEnum.NOT_LAUNCH_TEMPLATE_LIST.contains(pagePo.getIsModel()))
                    .map(MgkLandingPageDto::getPageId)
                    .collect(Collectors.toList());
            publishedPageIds = publishedPageIds.stream().limit(3).collect(Collectors.toList());
            Assert.isTrue(CollectionUtils.isEmpty(publishedPageIds), "该微信号关联微信包已关联已发布落地页" + publishedPageIds + ",更新失败");
            List<Long> waitAuditPageIds = pagePos.stream()
                    .filter(pagePo -> LandingPageStatusEnum.WAIT_AUDIT.getCode().equals(pagePo.getStatus()))
                    .map(MgkLandingPageDto::getPageId)
                    .collect(Collectors.toList());
            Assert.isTrue(CollectionUtils.isEmpty(waitAuditPageIds), "该微信号关联微信包已有关联落地页推审,更新失败");
        }
        Map<Integer, WechatPackageDataCountDto> existDataMap = mgkWechatPackageDataService.queryWechatAccountDataMap(Lists.newArrayList(updateDto.getId()), false);
        Assert.isTrue(CollectionUtils.isEmpty(existDataMap), "该微信号已有提交数据,更新失败");
    }

    public void validDeleteInfo(Integer id, Operator operator) {
        validPermission(id, operator);
        List<Integer> packageIds = mgkWechatAccountService.getAccountWechatPackageIds(id);
        if (!CollectionUtils.isEmpty(packageIds)) {
            List<Long> pageIds = mgkWechatPackageService.getPageIdsByPackageIds(packageIds);
            List<MgkLandingPageDto> pagePos = mgkLandingPageService.getLandingPageDtoByPageIds(pageIds);
            List<Long> publishedPageIds = pagePos.stream()
                    .filter(pagePo -> LandingPageStatusEnum.PUBLISHED.getCode().equals(pagePo.getStatus()))
                    .filter(pagePo -> IsModelEnum.NOT_LAUNCH_TEMPLATE_LIST.contains(pagePo.getIsModel()))
                    .map(MgkLandingPageDto::getPageId)
                    .collect(Collectors.toList());
            publishedPageIds = publishedPageIds.stream().limit(3).collect(Collectors.toList());
            Assert.isTrue(CollectionUtils.isEmpty(publishedPageIds), "该微信号关联微信包已关联已发布落地页" + publishedPageIds + ",删除失败");
            List<Long> waitAuditPageIds = pagePos.stream()
                    .filter(pagePo -> LandingPageStatusEnum.WAIT_AUDIT.getCode().equals(pagePo.getStatus()))
                    .map(MgkLandingPageDto::getPageId)
                    .collect(Collectors.toList());
            Assert.isTrue(CollectionUtils.isEmpty(waitAuditPageIds), "该微信号关联微信包已有关联落地页推审,删除失败");
        }
        Map<Integer, WechatPackageDataCountDto> existDataMap = mgkWechatPackageDataService.queryWechatAccountDataMap(Lists.newArrayList(id), false);
        Assert.isTrue(CollectionUtils.isEmpty(existDataMap), "该微信号已有提交数据,删除失败");
    }

    private void validPermission(Integer id, Operator operator) {
        WechatAccountDto dto = getValidBaseDtoById(id);
        Assert.isTrue(operator.getOperatorId().equals(dto.getAccountId()), "您不能操作不属于您的微信号");
    }

    private WechatAccountDto getValidBaseDtoById(Integer id) {
        Assert.isTrue(Utils.isPositive(id), "删除微信账户id不可为空");
        WechatAccountDto baseDto = mgkWechatAccountService.getValidDtoById(id);
        Assert.notNull(baseDto, "微信号不存在");
        return baseDto;
    }

    private List<WechatAccountListDto> queryListByWechatAccountEqual(String wechatAccount, Integer accountId) {
        if (StringUtils.isEmpty(wechatAccount)) {
            return Collections.emptyList();
        }
        WechatAccountQueryDto queryDto = WechatAccountQueryDto.builder()
                .wechatAccountEqual(wechatAccount)
                .accountId(accountId)
                .build();
        return mgkWechatAccountService.queryList(queryDto);
    }

    public void validQueryByPageDto(WechatAccountQueryDto queryDto) {
        Assert.notNull(queryDto.getPage(), "分页参数不可为空");
        Assert.isTrue(Utils.isPositive(queryDto.getPage().getPage()), "分页信息错误");
        Assert.isTrue(queryDto.getPage().getPageSize() <= PAGE_SIZE_LIMIT, "分页过大");
    }

}
