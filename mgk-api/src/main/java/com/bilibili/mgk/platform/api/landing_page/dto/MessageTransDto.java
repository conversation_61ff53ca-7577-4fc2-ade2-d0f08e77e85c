package com.bilibili.mgk.platform.api.landing_page.dto;


import com.bilibili.mgk.platform.common.enums.chatmessage.MessageStyleEnum;
import com.bilibili.mgk.platform.common.enums.chatmessage.MessageTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/3/10 17:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageTransDto  implements Serializable {

    private static final long serialVersionUID = 6530901027480443469L;


    private String msgId;

    private String msgContent;

    private Integer msgStyle;

    private Integer msgType;

    private Long timeStamp;

    private Map<String , String> messageExtra;

}
