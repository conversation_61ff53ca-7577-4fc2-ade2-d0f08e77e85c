package com.bilibili.mgk.platform.portal.httplog;

import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.openapi.bean.OpenApiResponse;

/**
 * @ClassName ResponseParse
 * <AUTHOR>
 * @Date 2023/2/10 2:51 下午
 * @Version 1.0
 **/
public class ResponseParse {
    private Integer code;
    private String msg;

    ResponseParse(Response<?> response) {
        if (response == null) {
            return;
        }
        this.code = response.getError_code();
        this.msg = response.getError_msg();
    }

    ResponseParse(OpenApiResponse<?> response) {
        if (response == null) {
            return;
        }
        this.code = response.getCode();
        this.msg = response.getMessage();
    }

    public static ResponseParse Parse(Object obj) {
        if (obj instanceof Response) {
            return new ResponseParse((Response<?>) obj);
        }
        if (obj instanceof OpenApiResponse) {
            return new ResponseParse((OpenApiResponse<?>) obj);
        }
        return null;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getCodeDefault() {
        if (this.code == null) {
            return 0;
        }
        return this.code;
    }

    public String getMsgDefault() {
        if (this.msg == null) {
            return "";
        }
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

