package com.bilibili.mgk.platform.api.data.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataDto implements Serializable{
    private static final long serialVersionUID = -6447578334214316781L;
    private Long reportDatId;
    private Long pageId;
    private Long formId;

    private String formName;
    private Integer formType;
    private String formTypeDesc;
    private Long creativeId;
    private String trackId;
    private Integer salesType;
    private String adType;
    private String requestId;
    private Integer sourceId;
    private Long mid;
    private String imei;
    private String buvid;
    private String deviceId;
    private Integer os;
    private Timestamp ctime;
    private Integer allowHistory;
    /**
     * 授权lbs
     */
    private Integer allowLbs;
    /**
     * 是否为作弊 0-不是 1-是
     */
    private Integer isCheat;

    /**
     * 帐户id
     */
    private Integer accountId;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 新版行业id
     */
    private Integer cmCategoryId;

    /**
     * 定制表单名称
     */
    private String customize;

    /**
     * up主mid
     */
    private Long upMid;

    private List<FormItemDataDto> formData;

    /**
     * 上报事件来源
     * @See com.bilibili.mgk.platform.common.EventSourceType
     */
    private Integer eventSourceType;

    /**
     * 上报表单来源
     */
    private Integer reportSource;

    /**
     * avid
     */
    private Long avid;

    //表单创建时间
    private Timestamp formCreateTime;

    //0: 不是直播组件卡广告， 1:是直播组件卡广告
    private Integer isLiveAssemblyCard;

    private String assemblyTrackId;

    private Long sharerUid;

}
