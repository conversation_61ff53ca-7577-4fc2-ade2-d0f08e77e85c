<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
               http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
               http://www.springframework.org/schema/context
               http://www.springframework.org/schema/context/spring-context-3.0.xsd
               http://www.springframework.org/schema/tx
	           http://www.springframework.org/schema/tx/spring-tx.xsd
               http://www.springframework.org/schema/mvc
               http://www.springframework.org/schema/mvc/spring-mvc.xsd">
    <context:component-scan base-package="com.bilibili.adp">
        <context:exclude-filter type="assignable" expression="com.bilibili.adp.passport.biz.config.GrpcChannelConfig"/>
    </context:component-scan>

    <context:component-scan base-package="com.biz.common.doc.tree"/>


    <bean id="dnsUpdater" class="com.bilibili.adp.common.util.DNSUpdater"/>


    <tx:annotation-driven/>
    <mvc:default-servlet-handler/>
    <mvc:annotation-driven>
        <mvc:argument-resolvers>

            <bean class="com.bilibili.mgk.platform.portal.common.ContextWebArgumentResolver"/>

        </mvc:argument-resolvers>
    </mvc:annotation-driven>


    <mvc:interceptors>

        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.bilibili.bjcom.disconf.cat.spring.CatInterceptor"/>
        </mvc:interceptor>

        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="pleiades.component.http.server.interceptor.HttpContextInterceptor"/>
        </mvc:interceptor>

        <mvc:interceptor>
            <mvc:mapping path="/web_api/**"/>
            <bean
                    class="com.bilibili.mgk.platform.portal.filter.PassportInterceptor">
                <property name="NOT_FILTER_URL"
                          value="${mgk-platform.interceptor.not.filter.url}"/>
                <property name="REFER_URL" value="${mgk-platform.passport.refer.url}"/>
            </bean>
        </mvc:interceptor>

        <!-- 没有接入trace 暂时不使用 会大量报错 且与现有filter冲突 -->
<!--        <mvc:interceptor>-->
<!--            <mvc:mapping path="/web_api/**"/>-->
<!--            <mvc:mapping path="/open_api/**"/>-->
<!--            <bean-->
<!--                    class="pleiades.component.http.server.interceptor.HttpContextInterceptor">-->
<!--            </bean>-->
<!--        </mvc:interceptor>-->

        <mvc:interceptor>
            <mvc:mapping path="/web_api/v1/sale/**"/>
            <mvc:mapping path="/web_api/v1/session/login"/>
            <bean class="com.bilibili.rbac.filter.interceptor.RbacInterceptor">
                <property name="tenantId" value="${rbac.tenantId}"/>
            </bean>
        </mvc:interceptor>

        <mvc:interceptor>
            <mvc:mapping path="/mapi/**"/>
            <bean class="com.bilibili.mgk.platform.portal.filter.MapiInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <bean id="sso" class="com.bilibili.mgk.platform.portal.filter.WebLdapFilter"
          destroy-method="destroy">
        <property name="NOT_FILTER_URL" value="${mgk-platform.ldap.not.filter.url}"></property>
        <property name="ssoClient">
            <bean class="com.bilibili.bjcom.sso.SSOClient" destroy-method="close">
                <constructor-arg name="host"
                                 value="${mgk-platform.sso.host}"/>
                <constructor-arg name="port"
                                 value="${mgk-platform.sso.port}"/>
                <constructor-arg name="caller"
                                 value="${mgk-platform.sso.caller}"/>
                <constructor-arg name="apiKey"
                                 value="${mgk-platform.sso.apiKey}"/>
            </bean>
        </property>
        <property name="logoutPath" value="${mgk-platform.sso.logoutPath}"/>
        <property name="enableHeaderToken" value="${mgk-platform.sso.enableHeaderToken}"/>
        <property name="cacheTimeout" value="${mgk-platform.sso.cacheTimeout}"/>
        <property name="cacheSecret" value="${mgk-platform.sso.cacheSecret}"/>
        <property name="authFailHandler">
            <bean class="com.bilibili.bjcom.sso.StdSSOAuthFailHandler"/>
        </property>
    </bean>

    <bean id="webapi_security"
          class="com.bilibili.mgk.platform.portal.filter.WebAPISecurityFilter">
        <property name="salesType" value="CPM"/>
        <property name="passportService" ref="passportService"/>
        <property name="soaPickupSessionService" ref="soaPickupSessionService"/>
    </bean>

    <bean id="base_filter" class="com.bilibili.mgk.platform.portal.filter.WebApiFilter">
        <property name="ALLOW_ORIGIN" value="${access.control.allow.rigin}"/>
        <property name="ALLOW_CREDENTIALS" value="${access.control.allow.credentials}"/>
    </bean>

    <bean id="httpInvokerSecurityFilter" class="com.bilibili.mgk.platform.portal.filter.HttpInvokerSecurityFilter">
        <property name="HTTP_INVOKER_DOMAIN" value="#{'${http.invoker.domain:@}'.split(',')}"/>
    </bean>

    <bean id="documentationConfig" class="com.bilibili.mgk.platform.portal.config.SwaggerConfig"/>
    <bean id="multipartResolver"
          class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="${bfs.file.max.upload.size:1048576000}"/>
        <property name="maxInMemorySize" value="${bfs.file.max.in.memory.size:2048000}"/>
    </bean>

    <bean id="operatorType"
          class="org.springframework.beans.factory.config.FieldRetrievingFactoryBean">
        <property name="staticField"
                  value="com.bilibili.adp.common.enums.OperatorType.ADVERTISERS"/>
    </bean>
    <bean id="systemType"
          class="org.springframework.beans.factory.config.FieldRetrievingFactoryBean">
        <property name="staticField" value="com.bilibili.adp.common.enums.SystemType.MGK"/>
    </bean>

    <bean id="tenantId" class="java.lang.Integer">
        <constructor-arg type="int" value="${rbac.tenantId}"/>
    </bean>

    <bean id="cpmMngTenantId" class="java.lang.Integer">
        <constructor-arg type="int" value="${cpm_mng.tenantId}"/>
    </bean>

    <bean id="httpStatusLogFilter" class="com.bilibili.mgk.platform.portal.filter.HttpStatusLogFilter"/>

    <bean id="open_api_filter" class="com.bilibili.mgk.platform.portal.filter.GatewayFilter">
        <property name="USE" value="${gateway.use:false}"/>
    </bean>

    <bean id="open_api_form_filter" class="com.bilibili.mgk.platform.portal.filter.FormDataFilter">
        <property name="USE" value="${formdata.filter.use:true}"/>
    </bean>

    <bean id="mapiFilter" class="com.bilibili.mgk.platform.portal.filter.MapiFilter"/>

    <import resource="classpath:rbac-biz.xml"/>
    <import resource="classpath:artascope-biz.xml"/>
    <import resource="classpath:collage-biz.xml"/>
    <import resource="classpath:mgk-httpinvoker-exporter.xml"/>
    <import resource="classpath:mgk-bfs.xml"/>
    <import resource="classpath:material-database-config.xml"/>
    <import resource="classpath:material-base-database-config.xml"/>
    <import resource="classpath:doc-database-config.xml"/>
</beans>