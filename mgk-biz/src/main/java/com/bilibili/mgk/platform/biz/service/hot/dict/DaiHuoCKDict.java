package com.bilibili.mgk.platform.biz.service.hot.dict;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.jooq.Field;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.*;

@Getter
public class DaiHuoCKDict {
    //开启窗口函数
    public static  final String ALLOW_EXPERIMENTAL_WINDOW_FUNCTIONS = "settings allow_experimental_window_functions = 1";

    // 数量
    public static  final String COL_COUNT = "creative_id_count";
    public static  final String COL_RANK= "rank";
    public static  final Integer TOP_RANK = 1;
    public static  final String LEFT_PARENTHESIS = "(";
    public static  final String RIGHT_PARENTHESIS = ")";

    // 创意ID
    public static final String COL_CREATIVE_ID = "creative_id";
    // 创意标题
    public static final String COL_CREATIVE_TITLE = "creative_title";

    // 图片gif类型为图片链接，视频类型为封面图
    public static final String COL_IMAGE_URL = "image_url";
    // avid
    public static final String COL_AV_ID = "av_id";
    // 账户ID
    public static final String COL_ACCOUNT_ID = "account_id";
    // bvid
    public static final String COL_BVID = "bv_id";
    //商品id
    public static final String COL_ITEM_ID = "item_id";
    //商品名称
    public static final String COL_ITEM_NAME = "item_name";
    //商品来源
    public static final String COL_ITEM_SOURCE= "item_source";

    //带货一级类目
    public static final String COL_FIRST_CATEGORY = "first_category";
    //带货二级类目
    public static final String COL_SECOND_CATEGORY= "second_category";
    //商品主图
    public static final String COL_MAIN_IMAGE_URL = "main_image_url";

    // 曝光
    public static final String COL_PV = "pv";
    // 点击
    public static final String COL_CLICK = "click";
    // 转化数
    public static final String COL_CONV_NUM = "conv_num";
    //ctr点击率
    public static final String COL_CTR = "ctr";
    //cvr 曝光转化率
    public static final String COL_CVR = "cvr";
    //cvr 点击转化率
    public static final String COL_PCRCVR = "ctcvr";
    // 曝光等级 S A B C
    public static final String COL_PV_RANK = "pv_rank";
    // ctr等级
    public static final String COL_CTR_RANK = "ctr_rank";
    // cvr等级
    public static final String COL_CVR_RANK = "cvr_rank";
    // pctcvr_rank等级
    public static final String COL_PCTCVR_RANK = "ctcvr_rank";

    // 同步日期
    public static final String COL_LOG_DATE = "date";
    // 统计日期类型,7d:最近7天，30d:最近30天
    public static final String COL_DAY_TYPE = "day_type";


    public static final ArrayList<? extends Field<? extends Serializable>> fields = Lists.newArrayList(
            field(COL_CREATIVE_ID).coerce(String.class).as(COL_CREATIVE_ID),
            field(COL_CREATIVE_TITLE).coerce(String.class).as(COL_CREATIVE_TITLE),
            field(COL_AV_ID).coerce(Long.class).as(COL_AV_ID),
            field(COL_BVID).coerce(String.class).as(COL_BVID),
            field(COL_IMAGE_URL).coerce(String.class).as(COL_IMAGE_URL),
            field(COL_ACCOUNT_ID).coerce(Long.class).as(COL_ACCOUNT_ID),

            field(COL_ITEM_ID).coerce(String.class).as(COL_ITEM_ID),
            field(COL_ITEM_NAME).coerce(String.class).as(COL_ITEM_NAME),
            field(COL_ITEM_SOURCE).coerce(String.class).as(COL_ITEM_SOURCE),
            field(COL_FIRST_CATEGORY).coerce(String.class).as(COL_FIRST_CATEGORY),
            field(COL_SECOND_CATEGORY).coerce(String.class).as(COL_SECOND_CATEGORY),
            field(COL_MAIN_IMAGE_URL).coerce(String.class).as(COL_MAIN_IMAGE_URL),

            field(COL_PV).coerce(Long.class).as(COL_PV),
            field(COL_CLICK).coerce(Long.class).as(COL_CLICK),
            field(COL_CONV_NUM).coerce(Long.class).as(COL_CONV_NUM),
            field(COL_CTR).coerce(Double.class).as(COL_CTR),
            field(COL_CVR).coerce(Double.class).as(COL_CVR),
            field(COL_PCRCVR).coerce(Double.class).as(COL_PCRCVR),
            field(COL_PV_RANK).coerce(String.class).as(COL_PV_RANK),
            field(COL_CTR_RANK).coerce(String.class).as(COL_CTR_RANK),
            field(COL_CVR_RANK).coerce(String.class).as(COL_CVR_RANK),
            field(COL_PCTCVR_RANK).coerce(String.class).as(COL_PCTCVR_RANK),

            field(COL_LOG_DATE).coerce(String.class).as(COL_LOG_DATE),
            field(COL_DAY_TYPE).coerce(String.class).as(COL_DAY_TYPE)
    );

    public static final ArrayList<String> fieldKeys = Lists.newArrayList(
            COL_CREATIVE_ID,
            COL_CREATIVE_TITLE,
            COL_AV_ID,
            COL_BVID,
            COL_IMAGE_URL,
            COL_ACCOUNT_ID,
            COL_ITEM_ID,
            COL_ITEM_NAME,
            COL_ITEM_SOURCE,
            COL_FIRST_CATEGORY,
            COL_SECOND_CATEGORY,
            COL_MAIN_IMAGE_URL,
            COL_PV,
            COL_CLICK,
            COL_CONV_NUM,
            COL_CTR,
            COL_CVR,
            COL_PCRCVR,
            COL_PV_RANK,
            COL_CTR_RANK,
            COL_CVR_RANK,
            COL_PCTCVR_RANK,
            COL_LOG_DATE,
            COL_DAY_TYPE
    );

}
