package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年8月2日
 * @desc 稿件状态
 */
@AllArgsConstructor
public enum ArchiveStateEnum {
    OPEN_BROWSE(0, "开放浏览") {
        @Override
        public boolean isValid() {
            return true;
        }
    },
    ORANGE_THROUGH(1, "橙色通过") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    VIP_ACCESS(10000, "会员可见") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    TO_BE_AUDIT(-1, "待审") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    REJECT(-2, "打回") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    NETWORK_LOCK(-3, "网警锁定") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    LOCK(-4, "锁定") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    REPAIR_PENDING(-6, "修复待审") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    SUSPENSION_AUDIT(-7, "暂缓审核") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    TRANSCODING(-9, "转码中") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    DELAY_RELEASE(-10, "延迟发布") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    VIDEO_SOURCE_TO_BE_REPAIR(-11, "视频源待修") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    ALLOW_COMMENT_TO_BE_PENDING(-13, "允许评论待审") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    DISTRIBUTION(-15, "分发中") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    TRANSCODING_FAILED(-16, "转码失败") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    CREATE_SUBMIT(-30, "创建提交") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    UP_TIMED_RELEASE(-40, "UP主定时发布") {
        @Override
        public boolean isValid() {
            return false;
        }
    },
    UP_DELETED(-100, "UP主删除") {
        @Override
        public boolean isValid() {
            return false;
        }
    };

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public final static List<Integer> validVideoStatus = Arrays.asList(OPEN_BROWSE.getCode(),
            ORANGE_THROUGH.getCode(),
            VIP_ACCESS.getCode(),
            REPAIR_PENDING.getCode());

    public static ArchiveStateEnum getByCode(int code) {
        for (ArchiveStateEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        return null;
    }

    public abstract boolean isValid();
}
