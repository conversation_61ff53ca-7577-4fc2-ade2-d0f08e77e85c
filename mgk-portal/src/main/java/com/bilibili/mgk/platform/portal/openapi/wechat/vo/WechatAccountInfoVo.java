package com.bilibili.mgk.platform.portal.openapi.wechat.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName WechatAccountInfoDto
 * <AUTHOR>
 * @Date 2022/6/17 9:20 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WechatAccountInfoVo {

    @ApiModelProperty(value = " 微信号id")
    private Integer wechat_account_id;

    @ApiModelProperty(value = " 微信号")
    private String wechat_account;

}
