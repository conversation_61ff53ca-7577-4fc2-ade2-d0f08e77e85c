package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: MgkTemplatePageMappingDto
 * @author: gaoming
 * @date: 2021/12/02
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MgkTemplatePageMappingDto implements Serializable {

    private static final long serialVersionUID = 4960301600747627787L;
    /**
     * hash值 page_id game_id package_id url
     */
    private String hashKey;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 游戏中心游戏id
     */
    private Integer gameBaseId;

    /**
     * 应用包应用id
     */
    private Integer packageId;

    /**
     * 外链地址
     */
    private String url;

    /**
     * 模板page_id
     */
    private Long templatePageId;
}
