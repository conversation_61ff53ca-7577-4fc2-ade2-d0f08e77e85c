/**
 * <AUTHOR>
 * @date 2018年5月18日
 */

package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NativeVideoConfig implements Serializable {

    private static final long serialVersionUID = 7574537160887109889L;

    private String title;
    private String icon;
    //一期只支持视频
    //private List<Tag> tags;
    private String cover;
    private String jump_url;
    private String callup_url;
    private List<String> report_urls;
    private String desc;
    private Button button;
    private Video video;
    private Long report_time;
    private String weburl;
    private String _use_weburl;

    // 半屏视频ios
    private String store_url; // appStore链接
    private Integer auto_store; // 进入页面是否自动唤起app商店地址

    private String version; // 版本

    // 半屏视频 android
    private Integer game_id; // 游戏id
    private String game_monitor_param; // 游戏监控

}
