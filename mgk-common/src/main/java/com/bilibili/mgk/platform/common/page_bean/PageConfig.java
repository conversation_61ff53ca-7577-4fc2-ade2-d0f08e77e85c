/**
 * <AUTHOR>
 * @date 2018年5月18日
 */

package com.bilibili.mgk.platform.common.page_bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageConfig<T> implements Serializable {

    private static final long serialVersionUID = 8535028649361344013L;

    private Integer template_style;
    private List<String> show_urls;
    private List<T> config;
    private List<String> open_whitelist;
    private List<AppPackageBean> download_whitelist;
    private List<String> form_ids;
    private Integer wechat_package_id;
    private List<Object> form_dtos;
    //    private LikeDto like;
    private LikeDto archive_like;

    /**
     * 落地页类型 0-原始落地页 1-模板 2-联投副本 3-图文副本 4-影子副本
     */
    private Integer isModel;
    /**
     * 客户名称 隐私协议中使用
     */
    private String privacy_name;

    /**
     * 隐私协议页面使用的url
     */
    private String privacy_url;

    /**
     * 下载按钮高度超限
     */
    private Integer is_download_button_over_limit;

    /**
     * 下载按钮高度超限需要提示
     */
    private Integer download_button_over_limit_hint;

    /**
     * 实验 0-基线 1-实验1 2-实验2 3-实验3 4-实验4
     *
     * @return
     */
    private Integer exp_hit;

    /**
     * 预加载实验 同上
     */
    private Integer preload_hit;

    /**
     * 表单底部按钮实验 同上
     */
    private Integer form_button_hit;

    /**
     * 小程序置底下载按钮 0-不出 1-出
     */
    private Integer h5_download_hit;

    /**
     * 微信加粉手动样式开关
     */
    private Integer wechat_is_manual;

    /**
     * 判断表单缓存更新字段 1是 0-否
     */
    private Integer form_cache_updated;

    /**
     * 环境 方便测试判断环境用
     */
    private Integer env;

    /**
     * 账户id
     */
    private Integer account_id;

    /**
     * 账户标签配置
     */
    private AccountLabelConfig account_label_config;

    //微信小游戏IDs
    private List<Integer> mini_game_ids;

    private List<LauMiniGameCacheBo> mini_game_cache_bos;

    //获客链接id列表
    private List<String> customer_acq_link_ids;

    //落地页封面亮度
    private Double lum;

    //落地页封面灰度
    private Double grey;

    //落地页封面色度
    private Double color;

    /**
     * 商业行业一级分类id(新版)
     */
    private Integer commerce_category_first_id;

    /**
     * 商业行业二级分类id（新版）
     */
    private Integer commerce_category_second_id;

    /**
     * 落地页是否是专为营销助手自动开户设计的落地页
     */
    private Boolean auto_create_acc_page;

    private List<GameCache> games;

    public static PageConfig nullPageConfig() {
        return PageConfig
                .builder()
                .build();
    }
}
