package com.bilibili.collage.biz.service;

import com.bapis.ad.cmc.goods.*;
import com.bilibili.collage.biz.service.model.CmcGoodsBo;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 带货稿件查询器
 *
 * <AUTHOR>
 * @date 2022/12/9 下午2:20
 */
@Component
public class GoodsArchiveQuerier {

    @Value("${archiveGoods.batchSize:100}")
    private Integer archiveGoodsBatchSize;

    @Resource
    private GoodsGrpc.GoodsBlockingStub goodsBlockingStub;

    public Map<Long, CmcGoodsBo> queryArchiveGoods(List<Long> avidList) {
        if (CollectionUtils.isEmpty(avidList)) {
            return Collections.emptyMap();
        }

        Cat.logEvent("查询带货稿件信息", "queryArchiveGoods start", Event.SUCCESS, "avidSize=" + avidList.size());
        try {
            // 分批查询
            List<List<Long>> partitions = Lists.partition(avidList, archiveGoodsBatchSize);
            Map<Long, CmcGoodsBo> map = new HashMap<>(avidList.size());
            // todo parallelStream问题
            partitions.parallelStream().forEach(avidPartition -> {
                CmcGoodsResponseV2 responseV2 = goodsBlockingStub.withDeadlineAfter(2000, TimeUnit.MILLISECONDS)
                        .queryCmcGoodsV2(CmcGoodsQueryReqV2.newBuilder()
                                .addAllContentIds(avidPartition)
                                .setContentType(ContentType.ARCHIVE)
                                .build());
                List<CmcGoodsResult> goodsResults = responseV2.getResultList();
                Map<Long, CmcGoodsBo> partitionMap = goodsResults.stream().collect(Collectors.toMap(CmcGoodsResult::getContentId,
                        r -> CmcGoodsBo.builder()
                                .isGoodsArchive(r.getCommentHasGoods()
                                        || r.getBarrageHasGoods()
                                        || r.getRecommendHasGoods()
                                        || r.getSupernatantHasGoods())
                                .isSupportCommentClick(r.getCommentHasGoods())
                                .build()));
                if (!CollectionUtils.isEmpty(partitionMap)) {
                    map.putAll(partitionMap);
                }
            });

            return map;
        } catch (Exception e) {
            Cat.logError("查询带货稿件信息超时", e);
            return Collections.emptyMap();
        }
    }
}
