package com.bilibili.mgk.material.center.service.aigc.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * uk = accountId + taskId + genMd5
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/19
 */
@Data
@Accessors(chain = true)
public class AIGeneratedImage implements SnakeCaseBody {

    @ApiModelProperty(value = "自增主键")
    @JsonIgnore
    private Long id;

    @ApiModelProperty(value = "账户id")
    private Long accountId;


    @ApiModelProperty(value = "生成记录id，外键关联progressRecord")
    private Long recordId;


    @ApiModelProperty(value = "原图md5")
    private String originImgMd5;

    @ApiModelProperty(value = "原图ulr")
    private String originImgUrl;

    @ApiModelProperty(value = "图生图任务id")
    private String taskId;


    @ApiModelProperty(value = "生成图MD5")
    private String genImgMd5;

    @ApiModelProperty(value = "生成图url")
    private String genImgUrl;

    @ApiModelProperty(value = "是否点赞")
    private Boolean isLike;

    @ApiModelProperty(value = "是否点踩")
    private Boolean isDislike;

    @ApiModelProperty(value = "点踩的具体理由")
    private String dislikeReasons;


    @ApiModelProperty(value = "是否删除")
    @JsonIgnore
    private Boolean deleted;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime ctime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime mtime;

    @ApiModelProperty(value = "图片宽度")
    private Integer imgWidth;

    @ApiModelProperty(value = "图片高度")
    private Integer imgHeight;


    @ApiModelProperty(value = "图片透传字段以备后续")
    private String imgExtra;

    @ApiModelProperty(value = "审核状态，是否拒审； false 通过没被拒； true拒审； 默认false")
    private Boolean auditDenied;

//    public Integer getAuditStatus() {
//
//        return getAuditStatus(null);
//    }


    public Integer fetchAuditStatusInterceptAuditError(Boolean errorSeenAsDeny) {

        if (auditStatus == null) {

            this.auditStatus = Optional
                    .ofNullable(this.getNestedImgExtra())
                    .map(NestedImgExtra::getAuditStatus)
                    .orElse(Img2ImgAuditStatus.undefined.getCode());

            if (errorSeenAsDeny != null) {

                Img2ImgAuditStatus interceptAudit = Img2ImgAuditStatus
                        .fromCode(this.auditStatus)
                        .interceptError(errorSeenAsDeny);

                this.auditStatus = interceptAudit.getCode();

            }

        }

        return auditStatus;
    }

    @ApiModelProperty(value = "机审状态，0未机审，1机审通过，2机审拒审，3机审报错现在也认识为拒审, 4审核进行中不展示图片")
    private Integer auditStatus;


    private NestedImgExtra nestedImgExtra;

//    public Boolean getAuditDenied() {
//
//        if (auditDenied == null) {
//            this.auditDenied = Img2ImgAuditStatus.auditStatusToDenied(getAuditStatus());
//        }
//        return auditDenied;
//
//    }


    public Boolean fetchAuditDeniedAndInterceptAuditError(Boolean errorSeenAsDeny) {

        if (auditDenied == null) {
            this.auditDenied = Img2ImgAuditStatus.auditStatusToDenied(
                    this.fetchAuditStatusInterceptAuditError(errorSeenAsDeny));
        }
        return auditDenied;

    }

    public AIGeneratedImage setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
        NestedImgExtra extra = this.getNestedImgExtra()
                .setAuditStatus(auditStatus);
        this.imgExtra = JsonUtil.writeValueAsString(extra);

        return this;
    }

    public NestedImgExtra getNestedImgExtra() {

        if (nestedImgExtra == null) {

            this.nestedImgExtra = Optional
                    .ofNullable(JsonUtil.readValue(imgExtra, NestedImgExtra.class))
                    .orElse(new NestedImgExtra()
                            .setAuditStatus(Img2ImgAuditStatus.undefined.getCode())
                    );

        }
        return nestedImgExtra;
    }


    @Data
    @Accessors(chain = true)
    public static class NestedImgExtra implements SnakeCaseBody {

        private Integer auditStatus;

    }

    public AIGeneratedImage() {
    }
}
