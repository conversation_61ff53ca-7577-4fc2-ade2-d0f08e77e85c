package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.bjcom.util.common.TreeUtils;
import com.bilibili.mgk.platform.api.data.dto.ExtFormDataDto;
import com.bilibili.mgk.platform.api.form.dto.*;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.bilibili.mgk.platform.biz.dao.ChinaAreaDao;
import com.bilibili.mgk.platform.biz.dao.MgkPageFormMappingDao;
import com.bilibili.mgk.platform.biz.po.ChinaAreaPo;
import com.bilibili.mgk.platform.biz.po.ChinaAreaPoExample;
import com.bilibili.mgk.platform.biz.po.MgkPageFormMappingPo;
import com.bilibili.mgk.platform.biz.po.MgkPageFormMappingPoExample;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/1/19
 **/
@Service
@Slf4j
public class MgkFormServiceImpl implements IMgkFormService {

    @Autowired
    private MgkBaseService mgkBaseService;
    @Autowired
    private FormServiceDelegate formServiceDelegate;
    @Autowired
    private ChinaAreaDao chinaAreaDao;
    @Autowired
    private MgkPageFormMappingDao mgkPageFormMappingDao;

    @Override
    public long create(Operator operator, NewMgkFormDto newMgkFormDto) {
        return formServiceDelegate.create(operator, newMgkFormDto);
    }

    @Override
    public void update(Operator operator, UpdateMgkFormDto updateMgkFormDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(updateMgkFormDto.getFormId(), "表单ID不可为空");
        RLock lock = mgkBaseService.getLock(updateMgkFormDto.getFormId(), MgkConstants.FORM_LOCK_SUFFIX);
        try {
            formServiceDelegate.update(operator, updateMgkFormDto);
        } finally {
            log.info(System.currentTimeMillis() + "---updateForm: [{}] unLock----", updateMgkFormDto.getFormId());
            lock.unlock();
        }
    }

    @Override
    public void delete(Operator operator, Long formId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(formId, "表单ID不可为空");
        RLock lock = mgkBaseService.getLock(formId, MgkConstants.FORM_LOCK_SUFFIX);
        try {
            formServiceDelegate.delete(operator, formId);
        } finally {
            log.info(System.currentTimeMillis() + "---deleteForm: [{}] unLock----", formId);
            lock.unlock();
        }
    }

    @Override
    public boolean canUpdateItem(Long formId) {
        return formServiceDelegate.canUpdateItem(formId, false, false);
    }

    @Override
    public MgkFormDto getFormDtoByFormIdWithCache(Long formId, boolean needRefreshFormSubmitCache) {
        return formServiceDelegate.getFormDtoByFormIdWithCache(formId, needRefreshFormSubmitCache);
    }

    @Override
    public MgkFormDto getFormDtoByFormId(Long formId) {
        return formServiceDelegate.getFormDtoByFormId(formId);
    }

    @Override
    public List<MgkFormDto> getFormDropboxByAccountId(Integer accountId, Integer formType, Set<Integer> formTypeSet) {
        // 兼容老版调用方式（新版使用formTypeSet忽略formType参数）
        if (formTypeSet == null) {
            formTypeSet = Collections.singleton(formType);
        }
        return formServiceDelegate.getFormDropboxByAccountId(accountId, new ArrayList<>(formTypeSet));
    }

    @Override
    public List<MgkFormDto> getFormDtosInFormIds(List<Long> formIds) {
        return formServiceDelegate.getFormDtosInFormIds(formIds);
    }

    @Override
    public Map<Long, MgkFormDto> getFormMapInFormIds(List<Long> formIds) {
        return formServiceDelegate.getFormMapInFormIds(formIds);
    }

    @Override
    public List<Long> getFormIdsByPageId(Long pageId) {
        MgkPageFormMappingPoExample mgkPageFormMappingPoExample = new MgkPageFormMappingPoExample();
        mgkPageFormMappingPoExample.or().andPageIdEqualTo(pageId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        var mappings = mgkPageFormMappingDao.selectByExample(mgkPageFormMappingPoExample);
        if(CollectionUtils.isEmpty(mappings)){
            return new ArrayList<>();
        }
        return mappings.stream().map(MgkPageFormMappingPo::getFormId).collect(Collectors.toList());
    }

    @Override
    public List<MgkFormDto> getFormDtos(QueryFormParamDto queryFormParamDto) {
        return formServiceDelegate.getFormDtos(queryFormParamDto);
    }

    @Override
    public MgkFormDto getBaseFormDtoByFormId(Long formId) {
        return formServiceDelegate.getBaseFormDtoByFormId(formId);
    }

    @Override
    public List<MgkFormDto> getBaseFormDtosInFormIds(List<Long> formIds) {
        return formServiceDelegate.getBaseFormDtosInFormIds(formIds);
    }

    @Override
    public PageResult<MgkFormDto> getPageFormDtos(QueryFormParamDto queryFormParamDto) {
        return formServiceDelegate.getPageFormDtos(queryFormParamDto);
    }

    @Override
    public Map<Long, List<Long>> getFormId2SortedItemIdMapInformIds(List<Long> formIds) {
        return formServiceDelegate.getFormId2SortedItemIdMapInformIds(formIds);
    }

    @Override
    public MgkFormItemDto getFormItemDtoByFormIdAndItemId(Long formId, Long formItemId) {
        return formServiceDelegate.getFormItemDtoByFormIdAndItemId(formId, formItemId);
    }

    @Override
    public List<MgkFormItemDto> getFormItemDtosInItemIds(List<Long> formItemIds) {
        return formServiceDelegate.getFormItemDtosInItemIds(formItemIds);
    }

    @Override
    public List<MgkFormItemDto> getFormItemDtosByFormId(Long formId) {
        return formServiceDelegate.getFormItemDtosByFormId(formId);
    }

    @Override
    public List<MgkFormItemDto> getFormItemDtosByFormIdFromCache(Long formId) {
        return formServiceDelegate.getFormItemDtosByFormIdFromRedisCache(formId);
    }

    @Override
    public void refreshFormItemCache() {
        formServiceDelegate.refreshFormItemCache();
    }

    @Override
    public void refreshFormItemCacheByFormIds(List<Long> formIds) {
        formIds.forEach(formId -> {
            formServiceDelegate.refreshFormItemCacheByFormId(formId);
        });
    }

    @Override
    public Integer getFormTypeByFormId(Long formId) {
        return formServiceDelegate.getFormTypeByFormId(formId);
    }

    @Override
    public Map<Long, Integer> getFormTypeMapByFormIds(List<Long> formIds) {
        return formServiceDelegate.getFormTypeMapByFormIds(formIds);
    }

    private final LoadingCache<Object, List<AreaTreeDto>> areasCache = CacheBuilder.newBuilder()
            .maximumSize(1)
            .expireAfterAccess(1L, TimeUnit.DAYS)
            .build(new CacheLoader<Object, List<AreaTreeDto>>() {
                @Override
                public List<AreaTreeDto> load(Object cacheKey) {
                    log.info("LoadingAreasTree:" + cacheKey);
                    List<ChinaAreaPo> chinaAreaPos = chinaAreaDao == null ?
                            Collections.emptyList() : chinaAreaDao.selectByExample(new ChinaAreaPoExample());

                    List<AreaTreeDto> areaTreeDtos = CollectionUtils.isEmpty(chinaAreaPos) ? Collections.emptyList() :
                            chinaAreaPos.stream().map(po -> AreaTreeDto.builder()
                                    .id(po.getAreaCode())
                                    .parentId(po.getParentCode())
                                    .name(po.getName())
                                    .build()).collect(Collectors.toList());

                    return TreeUtils.toTree(areaTreeDtos, ArrayList::new,
                            AreaTreeDto::getId, AreaTreeDto::getParentId,
                            AreaTreeDto::getChildren, AreaTreeDto::setChildren);
                }
            });

    @Override
    public List<AreaTreeDto> getAreasTreeFromCache() {
        return areasCache.getUnchecked("all");
    }

    @Override
    public Map<Long, ExtFormDataDto> getFormCount(List<Long> formIds) {
        return formServiceDelegate.getFormCount(formIds);
    }

    @Override
    public List<MgkFormSubmitInfoDto> getSubmitInfo(Long formId) {
        return formServiceDelegate.getSubmitInfo(formId);
    }

    @Override
    public Long refreshFormCache() {
        return formServiceDelegate.refreshFormInfo();
    }

    @Override
    public void refreshFormSubmitInfo() {
        formServiceDelegate.refreshFormSubmitInfo();
    }

    @Override
    public void refreshFormSubmitInfo(List<Long> formIds) {
        formServiceDelegate.refreshFormSubmitInfo(formIds);
    }

    @Override
    public Integer updateOptions(String options, Long formId, Long formItemId) {
        return formServiceDelegate.updateOptions(options, formId, formItemId);
    }

    @Override
    public Long copyForm(Long originFormId, Integer accountId, String formName) {
        return formServiceDelegate.copyForm(originFormId, accountId, formName);
    }
}
