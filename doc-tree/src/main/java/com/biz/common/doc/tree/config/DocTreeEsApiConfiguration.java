package com.biz.common.doc.tree.config;

import com.biz.common.doc.tree.http.EsSimpleHttpApi;
import com.biz.common.doc.tree.util.DocHttpMetricInterceptor;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.Retrofit.Builder;
import retrofit2.converter.jackson.JacksonConverterFactory;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/11
 */
@Configuration
public class DocTreeEsApiConfiguration {

    @Value("${doc-tree.es.base-url:http://uat-olap-search.bilibili.co}")
    private String esProxyBaseUrl;


    @Bean
    public EsSimpleHttpApi esSimpleHttpApi() {

        ObjectMapper om = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                .setSerializationInclusion(Include.NON_NULL);

        OkHttpClient.Builder client = new OkHttpClient.Builder();

        client.addInterceptor(new DocHttpMetricInterceptor());

        Retrofit retrofit = new Builder()
                .baseUrl(esProxyBaseUrl)
                .client(client.build())
                .addConverterFactory(
                        JacksonConverterFactory.create(om)
                )
                .build();

        return retrofit.create(EsSimpleHttpApi.class);

    }

}
