package com.bilibili.mgk.material.center.service.creative.model;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/13
 */
@Getter
public enum UpAuthType {

    normal("普通"),


    official_blue("蓝V"),

    ;

    private String desc;

    UpAuthType(String desc) {
        this.desc = desc;
    }

    public static UpAuthType fromVerifyType(Integer verifyType) {
        Boolean isOfficialBlue = Optional.ofNullable(verifyType)
                .map(type -> Arrays
                        .stream(VerifyType.values())
                        .filter(verifyType1 -> verifyType1.getCode() == type)
                        .findAny()
                        .map(VerifyType::isOfficialBlue)
                        .orElse(false)
                )
                .orElse(false);

        UpAuthType upType = isOfficialBlue ? UpAuthType.official_blue : UpAuthType.normal;
        return upType;
    }


    public List<String> getVerifyTypes() {

        return Arrays.stream(VerifyType.values())
                .filter(type -> {
                    if (this == UpAuthType.official_blue) {
                        return type.isOfficialBlue();
                    } else {
                        return !type.isOfficialBlue();
                    }
                }).map(type -> String.valueOf(type.getCode()))
                .collect(Collectors.toList());
    }


}
