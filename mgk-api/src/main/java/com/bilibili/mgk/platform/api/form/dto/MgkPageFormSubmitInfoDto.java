package com.bilibili.mgk.platform.api.form.dto;

import com.bilibili.mgk.platform.common.utils.MgkDateUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName MgkPageFormSubmitInfoDto
 * <AUTHOR>
 * @Date 2022/8/24 8:54 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MgkPageFormSubmitInfoDto implements Serializable {

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 时间
     */
    private String time_desc;

    public static List<MgkPageFormSubmitInfoDto> convertFormSubmitInfoDto2PageFormSubmitInfoDto(List<MgkFormSubmitInfoDto> infoDtos) {
        return infoDtos.stream()
                .map(MgkPageFormSubmitInfoDto::convertFormSubmitInfoDto2PageFormSubmitInfoDto)
                .collect(Collectors.toList());
    }

    public static MgkPageFormSubmitInfoDto convertFormSubmitInfoDto2PageFormSubmitInfoDto(MgkFormSubmitInfoDto infoDto) {
        return MgkPageFormSubmitInfoDto.builder()
                .name(convertName(infoDto.getName()))
                .phone(convertPhone(infoDto.getPhone()))
                .time_desc(MgkDateUtils.scrollTimeShow(infoDto.getTime()))
                .build();
    }

    public static String convertName(String name) {
        if (Strings.isNullOrEmpty(name)) {
            return "***";
        }
        return name.charAt(0) + "**";
    }

    public static String convertPhone(String phone) {
        if (Strings.isNullOrEmpty(phone) || phone.length() != 11) {
            return "";
        }
        String prefix = phone.substring(0, 3);
        String suffix = phone.substring(7, 11);

        return prefix + "****" + suffix;
    }

}
