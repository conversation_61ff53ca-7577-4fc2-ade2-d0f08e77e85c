package com.bilibili.mgk.platform.biz.ad.dao;

import com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardInfoPo;
import com.bilibili.mgk.platform.biz.ad.po.LauLiveGameCardInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauLiveGameCardInfoDao {
    long countByExample(LauLiveGameCardInfoPoExample example);

    int deleteByExample(LauLiveGameCardInfoPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauLiveGameCardInfoPo record);

    int insertBatch(List<LauLiveGameCardInfoPo> records);

    int insertUpdateBatch(List<LauLiveGameCardInfoPo> records);

    int insert(LauLiveGameCardInfoPo record);

    int insertUpdateSelective(LauLiveGameCardInfoPo record);

    int insertSelective(LauLiveGameCardInfoPo record);

    List<LauLiveGameCardInfoPo> selectByExample(LauLiveGameCardInfoPoExample example);

    LauLiveGameCardInfoPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauLiveGameCardInfoPo record, @Param("example") LauLiveGameCardInfoPoExample example);

    int updateByExample(@Param("record") LauLiveGameCardInfoPo record, @Param("example") LauLiveGameCardInfoPoExample example);

    int updateByPrimaryKeySelective(LauLiveGameCardInfoPo record);

    int updateByPrimaryKey(LauLiveGameCardInfoPo record);
}