package com.bilibili.mgk.material.center.service.aigc;

import com.bilibili.mgk.material.center.util.ImageUtil;
import java.io.IOException;
import java.net.URL;
import javax.imageio.ImageIO;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Ignore;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/19
 */
@Ignore
public class SmartCropServiceSimpleHttpImplTest {


    @Test
    public void testUploadBfs() throws IOException {

        String url = "https://boss.hdslb.com/cover-url-for-cover-deriavation-outer/cover_seg/tmp/94b7fa91814159d6bf4f8554ebd7b5a5.png";

        byte[] bytes = ImageUtil.img2data(ImageIO.read(new URL(url)), "png");
        byte[] bytes2 = ImageUtil.url2data(url);

        System.out.println(DigestUtils.md5Hex(bytes));

        System.out.println(DigestUtils.md5Hex(bytes2));


    }


    @Test
    public void testUploadBfs2() throws IOException {

        String url = "https://boss.hdslb.com/cover-url-for-cover-deriavation-outer/cover_seg/tmp/d7f8d52a8a17c5b4ed3ec4b5823a8f35.png";

        byte[] bytes = ImageUtil.img2data(ImageIO.read(new URL(url)), "png");
        byte[] bytes2 = ImageUtil.url2data(url);

        System.out.println(DigestUtils.md5Hex(bytes));

        System.out.println(DigestUtils.md5Hex(bytes2));


    }
}