<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
  		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
  		http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <bean id="adCatExecutorMybatisPlugin" class="com.bilibili.bjcom.cat.mybatis.CatExecutorMybatisPlugin"/>

    <bean id="adDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${mgk.ad.jdbc.driver}"></property>
        <property name="jdbcUrl" value="${mgk.ad.jdbc.url}"></property>
        <property name="user" value="${mgk.ad.jdbc.username}"></property>
        <property name="password" value="${mgk.ad.jdbc.password}"></property>
        <property name="maxPoolSize" value="10"></property>
        <property name="maxIdleTime" value="7200"></property>
        <property name="testConnectionOnCheckin" value="true"></property>
        <property name="idleConnectionTestPeriod" value="5"></property>
        <property name="preferredTestQuery" value="SELECT 1"></property>
        <property name="checkoutTimeout" value="1800000"></property>
    </bean>

    <!-- Spring 和 MyBatis -->
    <bean id="adSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adDataSource"/>
        <property name="mapperLocations" value="classpath:mapper/ad/*.xml"/>
        <property name="plugins">
            <array>
                <ref bean="adCatExecutorMybatisPlugin"/>
            </array>
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.bilibili.mgk.platform.biz.ad.dao"/>
        <property name="sqlSessionFactoryBeanName" value="adSqlSessionFactory"/>
    </bean>

    <tx:annotation-driven transaction-manager="adTransactionManager"/>
    <!-- 配置事务管理器 -->
    <bean id="adTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="adDataSource"/>
    </bean>

    <!-- jooq -->
    <bean id="jooqTransactionAwareDataSource"
          class="org.springframework.jdbc.datasource.TransactionAwareDataSourceProxy">
        <constructor-arg ref="adDataSource" />
    </bean>
    <bean class="org.jooq.impl.DataSourceConnectionProvider" name="jooqConnectionProvider">
        <constructor-arg ref="jooqTransactionAwareDataSource" />
    </bean>
    <bean id="jooqDsl" class="org.jooq.impl.DefaultDSLContext">
        <constructor-arg ref="jooqConfig" />
    </bean>

    <!-- Invoking an internal, package-private constructor for the example
         Implement your own Configuration for more reliable behaviour -->
    <bean class="org.jooq.impl.DefaultConfiguration" name="jooqConfig">
        <property name="SQLDialect"><value type="org.jooq.SQLDialect">MYSQL</value></property>
        <property name="connectionProvider" ref="jooqConnectionProvider" />
    </bean>
</beans>
