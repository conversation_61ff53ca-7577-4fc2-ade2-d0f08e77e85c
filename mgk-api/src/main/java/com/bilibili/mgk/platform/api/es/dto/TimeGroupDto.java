package com.bilibili.mgk.platform.api.es.dto;

import com.bilibili.adp.common.util.Utils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * @file: TimeGroupDto
 * @author: gaoming
 * @date: 2021/07/06
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimeGroupDto implements Serializable {
    private static final long serialVersionUID = 1702812072449554123L;

    /**
     * 昨日
     */
    private TimeRangeDto yesterday;

    /**
     * 前天
     */
    private TimeRangeDto beforeYesterday;

    /**
     * 前一周
     */
    private TimeRangeDto week;

    /**
     * 前一周的前一周
     */
    private TimeRangeDto beforeWeek;

    public static TimeGroupDto valueOf(Timestamp logTime) {
        if (logTime == null) {
            throw new IllegalArgumentException("The logTime can not be null");
        }

        String startFormat = Utils.getTimestamp2String(logTime, "yyyyMMdd");

        return valueOf(Utils.getTimestamp(startFormat, "yyyyMMdd").getTime());
    }

    public static TimeGroupDto valueOf(Long startTime)  {
        if (startTime == null) {
            throw new IllegalArgumentException("The startTime or endTime can not be null");
        }
        // 昨天
        TimeRangeDto yesterday = TimeRangeDto.builder()
                .startTime(startTime)
                .endTime(startTime)
                .build();

        // 前天
        Long timeBeforeYesterday = getTimeBefore(startTime, Calendar.DAY_OF_MONTH, -1);
        TimeRangeDto beforeYesterday = TimeRangeDto.builder()
                .startTime(timeBeforeYesterday)
                .endTime(timeBeforeYesterday)
                .build();


        // 7日
        Long weekStartTime = getTimeBefore(startTime, Calendar.DAY_OF_MONTH, -6);
        TimeRangeDto weekTime = TimeRangeDto.builder()
                .startTime(weekStartTime)
                .endTime(startTime)
                .build();

        // 7日前7日
        Long beforeWeekStartTime = getTimeBefore(weekStartTime, Calendar.DAY_OF_MONTH, -7);
        Long beforeWeekEndTime = getTimeBefore(weekStartTime, Calendar.DAY_OF_MONTH, -1);
        TimeRangeDto beforeWeekTime = TimeRangeDto.builder()
                .startTime(beforeWeekStartTime)
                .endTime(beforeWeekEndTime)
                .build();

        return new TimeGroupDto(yesterday, beforeYesterday, weekTime, beforeWeekTime);
    }

    public static Long getTimeBefore(Long nowTime, int field, int diff)  {
        Date nowDate = new Date(nowTime);
        Calendar cld = Calendar.getInstance();
        cld.setTime(nowDate);
        cld.add(field, diff);
        Date newDate = cld.getTime();
        return newDate.getTime();
    }

    @Override
    public String toString() {
        return "TimeGroupDto{" +
                "yesterday=" + yesterday.toString() +
                ", beforeYesterday=" + beforeYesterday.toString() +
                ", week=" + week.toString() +
                ", beforeWeek=" + beforeWeek.toString() +
                '}';
    }

    public static void main(String[] args) throws ParseException {
        TimeGroupDto timeGroupDto = TimeGroupDto.valueOf(new Timestamp(System.currentTimeMillis()));
        System.out.println(timeGroupDto.getYesterday());
        System.out.println(timeGroupDto.getBeforeYesterday());
        System.out.println(timeGroupDto.getWeek());
        System.out.println(timeGroupDto.getBeforeWeek());
    }

}
