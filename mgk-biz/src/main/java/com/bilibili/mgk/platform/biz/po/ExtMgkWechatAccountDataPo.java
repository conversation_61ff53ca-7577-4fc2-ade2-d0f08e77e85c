package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @ClassName ExtMgkWechatAccountDataPo
 * <AUTHOR>
 * @Date 2022/6/18 4:32 下午
 * @Version 1.0
 **/
public class ExtMgkWechatAccountDataPo implements Serializable {
    private static final long serialVersionUID = 8241533333962L;

    /**
     * 微信号id
     */
    private Integer wechatAccountId;

    /**
     * 计数
     */
    private Integer dataCount;

    /**
     * 提交类型 0-复制 1-跳转
     */
    private Integer dataType;

    /**
     * 最近提交数据的时间
     */
    private Timestamp recentSubmitTime;

    public Integer getWechatAccountId() {
        return wechatAccountId;
    }

    public void setWechatAccountId(Integer wechatAccountId) {
        this.wechatAccountId = wechatAccountId;
    }

    public Integer getDataCount() {
        return dataCount;
    }

    public void setDataCount(Integer dataCount) {
        this.dataCount = dataCount;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Timestamp getRecentSubmitTime() {
        return recentSubmitTime;
    }

    public void setRecentSubmitTime(Timestamp recentSubmitTime) {
        this.recentSubmitTime = recentSubmitTime;
    }
}
