package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkFormPersonInfoOnOffEnum
 * @author: gaoming
 * @date: 2021/10/21
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@Getter
public enum MgkFormPersonInfoOnOffEnum {

    /**
     * 关闭
     */
    CLOSE(0, "关闭"),

    /**
     * 开启
     */
    OPEN(1, "开启");
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static MgkFormPersonInfoOnOffEnum getByCode(int code) {
        for (MgkFormPersonInfoOnOffEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkFormPersonInfoOnOffEnum " + code);
    }
}
