package com.bilibili.mgk.platform.portal.webapi.hot_video.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @file: HotVideoListVo
 * @author: gaoming
 * @date: 2020/11/11
 * @version: 1.0
 * @description:
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HotVideoListVo {
    @ApiModelProperty(value = "avid")
    private Long avid;

    @ApiModelProperty(value = "bvid")
    private String bvid;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "封面")
    private String cover;

    @ApiModelProperty(value = "总播放数量")
    private String play;

    @ApiModelProperty(value = "总点赞数")
    private String likes;

    @ApiModelProperty(value = "是否收藏")
    private Integer is_collect;

    @ApiModelProperty(value = "状态 0-正常 1-黑名单")
    private Integer status;

    @ApiModelProperty(value = "商业兴趣")
    private String buss_interest;

    @ApiModelProperty(value = "up主名称")
    private String up_name;

    @ApiModelProperty(value = "商业兴趣偏好")
    private String buss_interest_desc;

    @ApiModelProperty(value = "分区")
    private String tname;

    @ApiModelProperty(value = "是否是竖屏，1:是 0:否")
    private Integer is_vertical_screen;

}
