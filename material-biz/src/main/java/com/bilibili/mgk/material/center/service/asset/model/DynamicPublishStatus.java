package com.bilibili.mgk.material.center.service.asset.model;

import com.bilibili.mgk.material.center.service.bluelink.model.BluelinkAuditStatus;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/16
 */
@Getter
@RequiredArgsConstructor
public enum DynamicPublishStatus {


    /**
     *
     */
    reserved(0 , "保留状态",false),




    audit(1, "审核中", false),


    audit_pass(2, "审核通过", false),


    audit_deny(3,"审核不通过",false),

    scheduling(4, "定时发布中", true),

    schedule_fail(5, "定时发布失败", true),

    ;



    private final  int code;


    private final String desc;

    private final boolean scheduleState;


    public static DynamicPublishStatus fromCode(Integer code) {

        for (DynamicPublishStatus value : DynamicPublishStatus.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return reserved;

    }


    public static DynamicPublishStatus fromCommerceAuditStatus(Integer commerceAuditStatus) {

        if (commerceAuditStatus == null) {
            return reserved;
        }
        switch (BluelinkAuditStatus.fromCode(commerceAuditStatus)) {
            case AUDITING:
                return audit;
            case AUDIT_PASSED:
                return audit_pass;
            case AUDIT_REJECTED:
                return audit_deny;
            default:
                return reserved;
        }


    }


}
