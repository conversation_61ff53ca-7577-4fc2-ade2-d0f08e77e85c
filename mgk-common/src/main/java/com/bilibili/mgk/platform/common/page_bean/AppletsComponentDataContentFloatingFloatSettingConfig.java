package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataContentFloatingFloatSettingConfig
 * @author: gaoming
 * @date: 2021/12/03
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataContentFloatingFloatSettingConfig implements Serializable {
    private static final long serialVersionUID = 8748079046916732401L;
    private String logo;

    private String title;

    private String buttonText;

    private String imageUrl;
}
