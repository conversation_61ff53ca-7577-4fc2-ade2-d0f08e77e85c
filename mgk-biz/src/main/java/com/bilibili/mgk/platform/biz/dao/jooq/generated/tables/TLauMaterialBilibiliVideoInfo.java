/*
 * This file is generated by jOOQ.
 */
package com.bilibili.mgk.platform.biz.dao.jooq.generated.tables;


import com.bilibili.mgk.platform.biz.dao.jooq.generated.DefaultSchema;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.Indexes;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.Keys;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.records.LauMaterialBilibiliVideoInfoRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.jooq.types.UInteger;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauMaterialBilibiliVideoInfo extends TableImpl<LauMaterialBilibiliVideoInfoRecord> {

    private static final long serialVersionUID = -1603797989;

    /**
     * The reference instance of <code>lau_material_bilibili_video_info</code>
     */
    public static final TLauMaterialBilibiliVideoInfo LAU_MATERIAL_BILIBILI_VIDEO_INFO = new TLauMaterialBilibiliVideoInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauMaterialBilibiliVideoInfoRecord> getRecordType() {
        return LauMaterialBilibiliVideoInfoRecord.class;
    }

    /**
     * The column <code>lau_material_bilibili_video_info.id</code>. 自增主键
     */
    public final TableField<LauMaterialBilibiliVideoInfoRecord, UInteger> ID = createField(DSL.name("id"), org.jooq.impl.SQLDataType.INTEGERUNSIGNED.nullable(false).identity(true), this, "自增主键");

    /**
     * The column <code>lau_material_bilibili_video_info.is_deleted</code>. 是否被删除 0-正常 1-已删除
     */
    public final TableField<LauMaterialBilibiliVideoInfoRecord, Byte> IS_DELETED = createField(DSL.name("is_deleted"), org.jooq.impl.SQLDataType.TINYINT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.TINYINT)), this, "是否被删除 0-正常 1-已删除");

    /**
     * The column <code>lau_material_bilibili_video_info.ctime</code>. 创建时间
     */
    public final TableField<LauMaterialBilibiliVideoInfoRecord, LocalDateTime> CTIME = createField(DSL.name("ctime"), org.jooq.impl.SQLDataType.LOCALDATETIME.nullable(false).defaultValue(org.jooq.impl.DSL.field("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>lau_material_bilibili_video_info.mtime</code>. 更新时间
     */
    public final TableField<LauMaterialBilibiliVideoInfoRecord, LocalDateTime> MTIME = createField(DSL.name("mtime"), org.jooq.impl.SQLDataType.LOCALDATETIME.nullable(false).defaultValue(org.jooq.impl.DSL.field("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.LOCALDATETIME)), this, "更新时间");

    /**
     * The column <code>lau_material_bilibili_video_info.avid</code>. 主站视频avid
     */
    public final TableField<LauMaterialBilibiliVideoInfoRecord, Long> AVID = createField(DSL.name("avid"), org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.BIGINT)), this, "主站视频avid");

    /**
     * The column <code>lau_material_bilibili_video_info.play</code>. 播放量
     */
    public final TableField<LauMaterialBilibiliVideoInfoRecord, UInteger> PLAY = createField(DSL.name("play"), org.jooq.impl.SQLDataType.INTEGERUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGERUNSIGNED)), this, "播放量");

    /**
     * The column <code>lau_material_bilibili_video_info.duration</code>. 时长（秒）
     */
    public final TableField<LauMaterialBilibiliVideoInfoRecord, Long> DURATION = createField(DSL.name("duration"), org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.BIGINT)), this, "时长（秒）");

    /**
     * Create a <code>lau_material_bilibili_video_info</code> table reference
     */
    public TLauMaterialBilibiliVideoInfo() {
        this(DSL.name("lau_material_bilibili_video_info"), null);
    }

    /**
     * Create an aliased <code>lau_material_bilibili_video_info</code> table reference
     */
    public TLauMaterialBilibiliVideoInfo(String alias) {
        this(DSL.name(alias), LAU_MATERIAL_BILIBILI_VIDEO_INFO);
    }

    /**
     * Create an aliased <code>lau_material_bilibili_video_info</code> table reference
     */
    public TLauMaterialBilibiliVideoInfo(Name alias) {
        this(alias, LAU_MATERIAL_BILIBILI_VIDEO_INFO);
    }

    private TLauMaterialBilibiliVideoInfo(Name alias, Table<LauMaterialBilibiliVideoInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauMaterialBilibiliVideoInfo(Name alias, Table<LauMaterialBilibiliVideoInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    public <O extends Record> TLauMaterialBilibiliVideoInfo(Table<O> child, ForeignKey<O, LauMaterialBilibiliVideoInfoRecord> key) {
        super(child, key, LAU_MATERIAL_BILIBILI_VIDEO_INFO);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_CTIME_IDX, Indexes.LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_MTIME_IDX);
    }

    @Override
    public Identity<LauMaterialBilibiliVideoInfoRecord, UInteger> getIdentity() {
        return Keys.IDENTITY_LAU_MATERIAL_BILIBILI_VIDEO_INFO;
    }

    @Override
    public UniqueKey<LauMaterialBilibiliVideoInfoRecord> getPrimaryKey() {
        return Keys.KEY_LAU_MATERIAL_BILIBILI_VIDEO_INFO_PRIMARY;
    }

    @Override
    public List<UniqueKey<LauMaterialBilibiliVideoInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<LauMaterialBilibiliVideoInfoRecord>>asList(Keys.KEY_LAU_MATERIAL_BILIBILI_VIDEO_INFO_PRIMARY, Keys.KEY_LAU_MATERIAL_BILIBILI_VIDEO_INFO_LAU_MATERIAL_BILIBILI_VIDEO_INFO_UN);
    }

    @Override
    public TLauMaterialBilibiliVideoInfo as(String alias) {
        return new TLauMaterialBilibiliVideoInfo(DSL.name(alias), this);
    }

    @Override
    public TLauMaterialBilibiliVideoInfo as(Name alias) {
        return new TLauMaterialBilibiliVideoInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauMaterialBilibiliVideoInfo rename(String name) {
        return new TLauMaterialBilibiliVideoInfo(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauMaterialBilibiliVideoInfo rename(Name name) {
        return new TLauMaterialBilibiliVideoInfo(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UInteger, Byte, LocalDateTime, LocalDateTime, Long, UInteger, Long> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
