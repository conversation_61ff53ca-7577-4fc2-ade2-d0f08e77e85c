package com.bilibili.collage.biz.utils;

import com.bapis.ad.mgk.material.GrpcNodeDeleteReq;
import com.bapis.ad.mgk.material.GrpcNodeDeleteReq.Builder;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/17
 */
public class ProtobufValueWrapUtilsTest {


    @Test
    public void test() {

        Builder req = GrpcNodeDeleteReq.newBuilder();
        ProtobufValueWrapUtils.toBoolValue(null).ifPresent(req::setForce);
        req.build();

        System.out.println(req);

        Boolean extract = ProtobufValueWrapUtils.fromBoolValue(req.hasForce(), req.getForce());

        System.out.println(extract);


    }


    @Test
    public void test2() {
        GrpcNodeDeleteReq req = GrpcNodeDeleteReq.newBuilder()
                .build();

        System.out.println(req);

        Boolean extract = ProtobufValueWrapUtils.fromBoolValue(req.hasForce(), req.getForce());

        System.out.println(extract);
    }
}