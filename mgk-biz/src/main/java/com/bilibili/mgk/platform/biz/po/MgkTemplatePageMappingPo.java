package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkTemplatePageMappingPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * hash值 page_id game_id package_id url
     */
    private String hashKey;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 游戏中心游戏id
     */
    private Integer gameBaseId;

    /**
     * 应用包应用id
     */
    private Integer packageId;

    /**
     * 外链地址
     */
    private String url;

    /**
     * 模板page_id
     */
    private Long templatePageId;

    /**
     * 是否删除 0-正常 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}