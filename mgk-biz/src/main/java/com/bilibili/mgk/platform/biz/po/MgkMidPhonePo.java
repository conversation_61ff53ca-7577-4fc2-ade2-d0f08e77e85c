package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkMidPhonePo implements Serializable {
    /**
     * 自增ID（主键）
     */
    private Integer id;

    /**
     * 主站mid
     */
    private Long mid;

    /**
     * B站用户唯一识别号
     */
    private String buvid;

    /**
     * 用户终端IMEI
     */
    private String imei;

    /**
     * 设备ID: os=0存AndroidID os=1 IDFA os=2 DUID
     */
    private String deviceId;

    /**
     * 手机号前缀（默认"+86"）
     */
    private String prefix;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 姓名
     */
    private String name;

    /**
     * 用户是否允许输入历史信息 0-未设置 1-允许 2-不允许
     */
    private Integer allowHistory;

    /**
     * 用户不允许输入历史信息提示 0-未显示 1-显示
     */
    private Integer allowHistoryToast;

    /**
     * 手机号是否经过验证 0-未验证 1-已验证
     */
    private Integer isPhoneValidate;

    /**
     * 拒绝使用登陆信息的操作时间
     */
    private Timestamp rejectLoginInfoTime;

    /**
     * 是否弹过允许使用登陆信息的窗口 0-未弹过窗 1-已弹过窗
     */
    private Integer allowLoginInfoToast;

    /**
     * 加密手机号
     */
    private String encryptPhoneNum;

    private static final long serialVersionUID = 1L;
}