package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkVideoLogOperationPo;
import com.bilibili.mgk.platform.biz.po.MgkVideoLogOperationPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkVideoLogOperationDao {
    long countByExample(MgkVideoLogOperationPoExample example);

    int deleteByExample(MgkVideoLogOperationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkVideoLogOperationPo record);

    int insertBatch(List<MgkVideoLogOperationPo> records);

    int insertUpdateBatch(List<MgkVideoLogOperationPo> records);

    int insert(MgkVideoLogOperationPo record);

    int insertUpdateSelective(MgkVideoLogOperationPo record);

    int insertSelective(MgkVideoLogOperationPo record);

    List<MgkVideoLogOperationPo> selectByExampleWithBLOBs(MgkVideoLogOperationPoExample example);

    List<MgkVideoLogOperationPo> selectByExample(MgkVideoLogOperationPoExample example);

    MgkVideoLogOperationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkVideoLogOperationPo record, @Param("example") MgkVideoLogOperationPoExample example);

    int updateByExampleWithBLOBs(@Param("record") MgkVideoLogOperationPo record, @Param("example") MgkVideoLogOperationPoExample example);

    int updateByExample(@Param("record") MgkVideoLogOperationPo record, @Param("example") MgkVideoLogOperationPoExample example);

    int updateByPrimaryKeySelective(MgkVideoLogOperationPo record);

    int updateByPrimaryKeyWithBLOBs(MgkVideoLogOperationPo record);

    int updateByPrimaryKey(MgkVideoLogOperationPo record);
}