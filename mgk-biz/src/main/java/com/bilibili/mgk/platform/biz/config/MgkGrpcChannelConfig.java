package com.bilibili.mgk.platform.biz.config;

import com.bapis.activity.service.ActivityGrpc;
import com.bapis.ad.adp.app_package.AppPackageServiceGrpc;
import com.bapis.ad.adp.component.CommentComponentServiceGrpc;
import com.bapis.ad.adp.mini_game.MiniGameServiceGrpc;
import com.bapis.ad.archive.CmArchiveServiceGrpc;
import com.bapis.ad.audit.AuditServiceGrpc;
import com.bapis.ad.cmc.category.CategoryGrpc;
import com.bapis.ad.cmc.up.UpInfoServiceGrpc;
import com.bapis.ad.crm.account.AccountReadServiceGrpc;
import com.bapis.ad.crm.wallet.CrmWalletServiceGrpc;
import com.bapis.ad.pandora.core.auto.AutoServiceGrpc;
import com.bapis.ad.resource.ResourceServiceGrpc;
import com.bapis.ad.scv.anchor.NativeAnchorServiceGrpc;
import com.bapis.community.service.location.LocationGrpc;
import com.bapis.passport.service.user.PassportUserGrpc;
import com.bapis.silverbullet.gaia.interfaces.GaiaGrpc;
import com.bilibili.mgk.platform.common.MgkEnvironmentEnum;
import com.google.common.collect.Lists;
import io.grpc.Channel;
import io.grpc.ClientInterceptors;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pleiades.component.rpc.client.BiliRpcClientCallInterceptor;
import pleiades.component.rpc.client.ChannelBuilder;
import pleiades.component.rpc.client.naming.RPCNamingClientNameResolverFactory;
import pleiades.venus.breaker.BiliBreakerProperties;
import pleiades.venus.naming.client.NamingClient;
import pleiades.venus.naming.client.Namings;
import pleiades.venus.naming.client.resolve.NamingResolver;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName GrpcChannelConfig
 * <AUTHOR>
 * @Date 2022/11/22 7:56 下午
 * @Version 1.0
 **/
@Configuration
@Slf4j
public class MgkGrpcChannelConfig {

    @Value("${mgk.environment:1}")
    private Integer mgkEnvironment;

    private static final String CPM_ADP_APP_ID = "sycpb.cpm.cpm-adp";
    private static final String BRAND_APP_ID = "sycpb.cpm.brand-adp";
    private static final String CRM_APP_ID = "sycpb.cpm.crm-portal";
    private static final String USER_APP_ID = "passport.service.user";
    private static final String CMC_APP_ID = "sycpb.cpm.product-search-portal";
    private static final String ACTIVITY_APP_ID = "activity.service";
    private static final String UP_INFO_APP_ID = "sycpb.cpm.tavern-platform";
    private static final String PERSONAL_MGK_APP_ID = "sycpb.platform.personal-mgk-portal";
    private static final String CRM_WALLET_APP_ID = "sycpb.platform.crm-wallet";
    private static final String GAIA_APP_ID = "main.silverbullet.gaia-interface";
    private static final String CPM_PANDORA_APP_ID = "sycpb.platform.cpm-pandora";
    private static final String LOCATION_APP_ID = "location.service";
    private static final String SH001 = "sh001";
    private static final String SH004 = "sh004";

    private NamingResolver.Args defaultArgs() {
        return NamingResolver.Args.builder()
                .optional(true)
                .scheduler(true)
                .subset(0)
                .build();
    }

    private ManagedChannel buildCpmAdpChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(CPM_ADP_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(CPM_ADP_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildBrandChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(BRAND_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(BRAND_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildCrmChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(CRM_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(CRM_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildUserChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(USER_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(USER_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildUpInfoChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(UP_INFO_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(UP_INFO_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildActivityChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(ACTIVITY_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(ACTIVITY_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(10, TimeUnit.SECONDS)
                .keepAliveTimeout(3000, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildCMCChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(CMC_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(CMC_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildPersonalMgkChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(PERSONAL_MGK_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(PERSONAL_MGK_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildCrmWalletChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(CRM_WALLET_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(CRM_WALLET_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildGaiaChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(GAIA_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(GAIA_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildPandoraChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(CPM_PANDORA_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(CPM_PANDORA_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }

    private ManagedChannel buildLocationChannel(NamingClient namingClient) {
        String zone = MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment) ?
                SH004 : SH001;
        NamingResolver resolver = namingClient.resolveForReady(LOCATION_APP_ID, Namings.Scheme.GRPC, defaultArgs());
        return ChannelBuilder.forTarget(LOCATION_APP_ID)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }


    @Bean("crmWalletChannel")
    public Channel crmWalletChannel(NamingClient namingClient) {
        return ClientInterceptors.intercept(buildCrmWalletChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    @Bean("gaiaChannel")
    public Channel gaiaChannel(NamingClient namingClient){
        return ClientInterceptors.intercept(buildGaiaChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    @Bean("pandoraChannel")
    public Channel pandoraChannel(NamingClient namingClient){
        return ClientInterceptors.intercept(buildPandoraChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }


    @Bean("personalMgkChannel")
    public Channel personalMgkChannel(NamingClient namingClient) {
        // BiliRpcClientCallInterceptor 自带熔断器和监控，如不需要可以不添加
        return ClientInterceptors.intercept(buildPersonalMgkChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    @Bean("cpmAdpChannel")
    public Channel cpmAdpChannel(NamingClient namingClient) {
        // BiliRpcClientCallInterceptor 自带熔断器和监控，如不需要可以不添加
        return ClientInterceptors.intercept(buildCpmAdpChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    @Bean("brandChannel")
    public Channel brandChannel(NamingClient namingClient) {
        return ClientInterceptors.intercept(buildBrandChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    @Bean("crmChannel")
    public Channel crmChannel(NamingClient namingClient) {
        return ClientInterceptors.intercept(buildCrmChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    @Bean("accountReadServiceBlockingStub")
    public AccountReadServiceGrpc.AccountReadServiceBlockingStub accountReadServiceBlockingStub(Channel crmChannel) {
        return AccountReadServiceGrpc.newBlockingStub(crmChannel);
    }

    @Bean("userChannel")
    public Channel userChannel(NamingClient namingClient) {
        return ClientInterceptors.intercept(buildUserChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    @Bean("passportUserBlockingStub")
    public PassportUserGrpc.PassportUserBlockingStub passportUserBlockingStub(Channel userChannel) {
        return PassportUserGrpc.newBlockingStub(userChannel);
    }

    @Bean("cmcChannel")
    public Channel cmcChannel(NamingClient namingClient) {
        return ClientInterceptors.intercept(buildCMCChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor("cmcCallInterceptor", new BiliBreakerProperties())));
    }

    @Bean("categoryBlockingStub")
    public CategoryGrpc.CategoryBlockingStub categoryBlockingStub(Channel cmcChannel) {
        return CategoryGrpc.newBlockingStub(cmcChannel);
    }

    @Bean
    public CmArchiveServiceGrpc.CmArchiveServiceBlockingStub cmArchiveServiceBlockingStub(Channel scvChannel) {
        return CmArchiveServiceGrpc.newBlockingStub(scvChannel);
    }

    @Bean("uisChannel")
    public Channel uisChannel(NamingClient namingClient) {
        return ClientInterceptors.intercept(buildUpInfoChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor("uisCallInterceptor", new BiliBreakerProperties())));
    }

    @Bean
    public UpInfoServiceGrpc.UpInfoServiceBlockingStub upInfoServiceBlockingStub(Channel uisChannel) {
        return UpInfoServiceGrpc.newBlockingStub(uisChannel);
    }

    @Bean("activityChannel")
    public Channel activityChannel(NamingClient namingClient) {
        return ClientInterceptors.intercept(buildActivityChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor("activityCallInterceptor", new BiliBreakerProperties())));
    }

    @Bean("locationChannel")
    public Channel locationChannel(NamingClient namingClient) {
        return ClientInterceptors.intercept(buildLocationChannel(namingClient),
                Lists.newArrayList(new BiliRpcClientCallInterceptor("locationCallInterceptor", new BiliBreakerProperties())));
    }

    @Bean
    public ActivityGrpc.ActivityBlockingStub activityBlockingStub(Channel activityChannel) {
        return ActivityGrpc.newBlockingStub(activityChannel);
    }

    @Bean("resourceServiceBlockingStub")
    public ResourceServiceGrpc.ResourceServiceBlockingStub buildResourceServiceBlockingStub(Channel cpmAdpChannel) {
        return ResourceServiceGrpc.newBlockingStub(cpmAdpChannel);
    }

    @Bean
    public NativeAnchorServiceGrpc.NativeAnchorServiceBlockingStub nativeAnchorServiceBlockingStub(Channel scvChannel) {
        return NativeAnchorServiceGrpc.newBlockingStub(scvChannel);
    }

    @Bean
    public AuditServiceGrpc.AuditServiceBlockingStub auditServiceBlockingStub(Channel cpmAdpChannel) {
        return AuditServiceGrpc.newBlockingStub(cpmAdpChannel);
    }

    @Bean
    public MiniGameServiceGrpc.MiniGameServiceBlockingStub buildMiniGameService(Channel cpmAdpChannel) {
        return MiniGameServiceGrpc.newBlockingStub(cpmAdpChannel);
    }

    @Bean
    public AppPackageServiceGrpc.AppPackageServiceBlockingStub buildAppPackageService(Channel cpmAdpChannel) {
        return AppPackageServiceGrpc.newBlockingStub(cpmAdpChannel);
    }

    @Bean("personalMgkCommentComponentServiceGrpc")
    public com.bapis.ad.mgk.comment.CommentComponentServiceGrpc.CommentComponentServiceBlockingStub buildPersonalMgkCommentComponentService(Channel personalMgkChannel) {
        return com.bapis.ad.mgk.comment.CommentComponentServiceGrpc.newBlockingStub(personalMgkChannel);
    }

    @Bean("crmWalletServiceGrpc")
    public CrmWalletServiceGrpc.CrmWalletServiceBlockingStub buildCrmWalletServiceGrpc(Channel crmWalletChannel) {
        return CrmWalletServiceGrpc.newBlockingStub(crmWalletChannel);
    }

    @Bean("gaiaGrpc")
    public GaiaGrpc.GaiaBlockingStub buildGaiaService(Channel gaiaChannel) {
        return GaiaGrpc.newBlockingStub(gaiaChannel);
    }

    @Bean("autoServiceGrpc")
    public AutoServiceGrpc.AutoServiceBlockingStub buildAutoService(Channel pandoraChannel) {
        return AutoServiceGrpc.newBlockingStub(pandoraChannel);
    }

    @Bean("locationServiceGrpc")
    public LocationGrpc.LocationBlockingStub locationBlockingStub(Channel locationChannel) {
        return LocationGrpc.newBlockingStub(locationChannel);
    }

}
