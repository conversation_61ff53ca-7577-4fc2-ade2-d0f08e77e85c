package com.biz.common.doc.tree.service.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/5
 */
@Data
@Accessors(chain = true)
public class NodeListReq implements SnakeCaseBody, DocSortSupport, DocRetrieveSupport {


    /**
     * 业务隔离的需要，之所以批量是以为内有场景需要跨多个业务树
     */
    @ApiModelProperty("根节点id")
    private List<Long> rootId;

    @ApiModelProperty("必选，父节点id")
    private Long nodeId;

    @ApiModelProperty("可选，指定节点深度策略")
    private TargetDepthStrategy depthStrategy = TargetDepthStrategy.depth_eq;


    @ApiModelProperty("必选，指定节点深度， 怎么理解深度？ targetId+ 深度1= 查子接地那，targetId+ 深度1= 查孙子节点")
    private Integer depth;


    /**
     * 是否回表查询文档内存，如果文档存在时
     * 可选，默认false
     */
    @ApiModelProperty("可选，是否回表查询文档内存，如果文档存在时 可选，默认false")
    private Boolean retrieveDoc;


    @ApiModelProperty("可选，是否查询节点的祖先节点, 默认否")
    private Boolean retrieveAncestors;



    /**
     * 排序方式， 如果从文档的角度看，使用ctime_asc是最合适的。
     */
    @ApiModelProperty("可选，排序方式， 如果从文档的角度看，使用ctime_asc是最合适的。")
    private NodeSortType sort;


    /**
     * 是否使用docType对节点进行
     */
    @ApiModelProperty("可选，是否使用docType对节点进行")
    private List<String> docType;

    private List<String> nodeType;


    @ApiModelProperty("可选，排除的节点id")
    private List<Long> excludeNodeIds;

    private Boolean isDeleted;

    private Boolean isShow;

    private String accountId;


    @ApiModelProperty("是否查询收藏状态, 默认否, 当查询时accountId需要提供")
    private Boolean retrieveFavorite;



    public void validate() {

        //
        isDeleted = false;

        Assert.notNull(nodeId, "nodeId不能为空");
//        depth = Optional.ofNullable(depth).orElse(1);

        if (depth != null) {
            Assert.isTrue(depth > 0, "depth必须大于0");
        }

        retrieveDoc = Optional.ofNullable(retrieveDoc).orElse(false);
        sort = Optional.ofNullable(sort).orElse(NodeSortType.ctime_asc);

    }


    public enum TargetDepthStrategy {

        /**
         * 只查目标相对深度的节点
         */
        depth_eq,

        /**
         * 所有相对深度小于等于目标深度的节点
         */
        depth_lte,

        ;
    }



}
