<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkInspirationArticleLikeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="like_id" jdbcType="BIGINT" property="likeId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="article_id" jdbcType="BIGINT" property="articleId" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, like_id, account_id, article_id, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_inspiration_article_like
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_inspiration_article_like
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_inspiration_article_like
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePoExample">
    delete from mgk_inspiration_article_like
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_inspiration_article_like (like_id, account_id, article_id, 
      is_deleted, ctime, mtime
      )
    values (#{likeId,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{articleId,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_inspiration_article_like
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="likeId != null">
        like_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="articleId != null">
        article_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="likeId != null">
        #{likeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="articleId != null">
        #{articleId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePoExample" resultType="java.lang.Long">
    select count(*) from mgk_inspiration_article_like
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_inspiration_article_like
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.likeId != null">
        like_id = #{record.likeId,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.articleId != null">
        article_id = #{record.articleId,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_inspiration_article_like
    set id = #{record.id,jdbcType=INTEGER},
      like_id = #{record.likeId,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      article_id = #{record.articleId,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePo">
    update mgk_inspiration_article_like
    <set>
      <if test="likeId != null">
        like_id = #{likeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="articleId != null">
        article_id = #{articleId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePo">
    update mgk_inspiration_article_like
    set like_id = #{likeId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=INTEGER},
      article_id = #{articleId,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_inspiration_article_like (like_id, account_id, article_id, 
      is_deleted, ctime, mtime
      )
    values (#{likeId,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{articleId,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      like_id = values(like_id),
      account_id = values(account_id),
      article_id = values(article_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_inspiration_article_like
      (like_id,account_id,article_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.likeId,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.articleId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_inspiration_article_like
      (like_id,account_id,article_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.likeId,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.articleId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      like_id = values(like_id),
      account_id = values(account_id),
      article_id = values(article_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkInspirationArticleLikePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_inspiration_article_like
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="likeId != null">
        like_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="articleId != null">
        article_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="likeId != null">
        #{likeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="articleId != null">
        #{articleId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="likeId != null">
        like_id = values(like_id),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="articleId != null">
        article_id = values(article_id),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>