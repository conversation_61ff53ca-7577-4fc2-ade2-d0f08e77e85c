package com.bilibili.mgk.platform.api.third_party.page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @ClassName MgkThirdPartyDto
 * <AUTHOR>
 * @Date 2023/5/19 3:23 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MgkThirdPartyPageDto {

    private Long pageId;

    private String pageName;

    private String pageUrl;

    private Timestamp ctime;

    private Timestamp mtime;

}
