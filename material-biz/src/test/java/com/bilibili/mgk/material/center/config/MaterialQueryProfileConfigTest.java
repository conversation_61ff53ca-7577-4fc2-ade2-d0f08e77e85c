package com.bilibili.mgk.material.center.config;

import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/25
 */
public class MaterialQueryProfileConfigTest {


    @Test
    public void test() {

        String word = "中文　测试 测试2";

        ;

        System.out.println(Splitter.on(" ").splitToList(word.replaceAll("　", " ")));

        System.out.println(Splitter.on(" ").splitToList(word));

    }


    @Test
    public void testMappingBusinessInterestOfHotVideo() {

        MaterialQueryProfileConfig config = new MaterialQueryProfileConfig();

        config.setBiliVideoBussInterestInfos("4,测试;5,测试2");

        HotBiliVideo video = new HotBiliVideo()
                .setBussInterest("4,5");

        config.mappingBusinessInterestOfHotVideo(Lists.newArrayList(video));

        System.out.println(video);

    }


    @Test
    public void testMappingBusinessInterestOfHotVideo2() {

        MaterialQueryProfileConfig config = new MaterialQueryProfileConfig();

        config.setBiliVideoBussInterestInfos("4,测试;5,测试2");
        config.setDaihuoFirstCategoryInfos(
                "1,美妆个护;2,3C数码家电;3,食品饮料;4,服装配饰;5,家居家装;6,运动户外;7,玩具乐器;8,宠物;9,母婴;10,营养保健;11,成人用品;12,医疗器械;13,本地生活;14,图书音像;15,车辆;16,珠宝文玩;17,医药;18,虚拟充值;19,礼品文创;20,二手闲置;21,其他;");
        config.setDaihuoFirstCategoryInfoUseNameEnabled(true);
        config.setDaihuoSecondCategoryInfosUseNameEnabled(true);
        config.setDaihuoSecondCategoryInfos(
                "1:1001,美容护肤;1002,彩妆/香水/美妆工具;1003,美发护发/假发;1004,个人洗护;1005,美容美体仪器;1006,洗护清洁剂/卫生巾/纸/香薰;|2:2001,电脑硬件/显示器/电脑周边;2002,笔记本电脑;2003,DIY电脑;2004,3C数码配件;2005,办公设备/耗材/相关服务;2006,手机;2007,摄影/摄像;2008,生活电器;2009,厨房电器;2010,影音电器;2011,电玩/配件/游戏/攻略;2012,智能设备;2013,平板电脑/MID;|3:3001,零食/坚果/特产;3002,生鲜;3003,酒类;3004,茶;3005,咖啡/麦片/冲调饮料;3006,粮油米面/南北干货/调味品;|4:4001,男装;4002,女装;4003,服饰配件/皮带/帽子/围巾;4004,内衣裤袜;4005,鞋靴箱包;4006,钟表配饰（打火机/瑞士军刀/眼镜/钟表）;4007,运动服/运动鞋;|5:5001,全屋定制;5002,家装主材;5003,住宅家具;5004,家装灯饰光源;5005,家纺;5006,厨房/烹饪用具;5007,居家日用;5008,家庭/个人清洁工具;5009,家居饰品;5010,收纳整理;5011,五金/工具;5012,装修设计/施工/监理;|6:6001,户外/登山/野营/旅行用品;6002,运动/瑜伽/健身/球迷用品;6003,自行车/骑行装备/零配件;|7:7001,玩具/童车/益智/积木/模型;7002,模玩/动漫/周边/娃圈三坑/桌游;7003,乐器/吉他/钢琴/配件;|8:8001,宠物/宠物食品及用品;8002,宠物健康;|9:9001,奶粉/辅食/营养品/零食;9002,童装/童鞋/婴儿鞋/亲子鞋;9003,婴童用品;9004,孕妇装/孕产妇用品/营养;|10:10001,保健食品/膳食营养补充食品;10002,传统滋补营养品;10003,保健用品;|11:11001,计生用品;11002,成人用品/情趣用品;|12:12001,隐形眼镜/护理液;12002,保健/按摩器材;12003,医疗器械;12004,健康服务;|13:13001,本地化生活服务;13002,商务/设计服务;13003,教育培训;13004,众筹/团购;13005,农资绿植;|14:14001,图书;14002,娱乐音像;|15:15001,汽车;15002,汽车零部件/养护/美容/维保;15003,汽车用品/电子/清洗/改装;15004,电动车/配件/交通工具;15005,摩托车/装备/配件;|16:16001,时尚饰品;16002,珠宝/钻石/翡翠/黄金;16003,特色手工艺;16004,文玩/收藏/字画;|17:17001,医药;|18:18001,虚拟充值;|19:19001,礼品文创;|20:20001,二手闲置;|21:21001,其他;|");

        System.out.println(config.getMaterialQueryProfiles());

    }
}