package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkVideoProviderQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkVideoProvider is a Querydsl query type for MgkVideoProviderQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkVideoProvider extends com.querydsl.sql.RelationalPathBase<MgkVideoProviderQueryDSLPo> {

    private static final long serialVersionUID = 853892006;

    public static final QMgkVideoProvider mgkVideoProvider = new QMgkVideoProvider("mgk_video_provider");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final StringPath name = createString("name");

    public final com.querydsl.sql.PrimaryKey<MgkVideoProviderQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkVideoProvider(String variable) {
        super(MgkVideoProviderQueryDSLPo.class, forVariable(variable), "null", "mgk_video_provider");
        addMetadata();
    }

    public QMgkVideoProvider(String variable, String schema, String table) {
        super(MgkVideoProviderQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkVideoProvider(String variable, String schema) {
        super(MgkVideoProviderQueryDSLPo.class, forVariable(variable), schema, "mgk_video_provider");
        addMetadata();
    }

    public QMgkVideoProvider(Path<? extends MgkVideoProviderQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_video_provider");
        addMetadata();
    }

    public QMgkVideoProvider(PathMetadata metadata) {
        super(MgkVideoProviderQueryDSLPo.class, metadata, "null", "mgk_video_provider");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(2).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(3).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(name, ColumnMetadata.named("name").withIndex(5).ofType(Types.VARCHAR).withSize(32).notNull());
    }

}

