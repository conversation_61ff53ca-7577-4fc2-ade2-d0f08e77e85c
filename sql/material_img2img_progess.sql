CREATE TABLE `mgk_material_img2img_progress_record`
(
    `id`              bigint(20)          NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `account_id`      bigint(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户Id',
    `task_id`         varchar(100)                 default '' not null comment '任务id',
    `raw_params`      text COMMENT '入参param json透传，方便扩展',
    `origin_img_md5`  varchar(100)                 default '' not null comment '图片md5',
    `origin_img_url`  varchar(300)                 default '' not null comment '图片md5',
    `quantity`        int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '数量',
    `relevance_level` int(11)             NOT NULL DEFAULT '0' COMMENT '相关性等级',
    `prompt`          varchar(1000)                default '' not null comment '提示词',
    `progress_status` varchar(100)                 default '' not null comment '状态',
    `err_code`        int(11)             NOT NULL DEFAULT '0' COMMENT '错误码',
    `err_msg`         varchar(200)                 default '' not null comment '错误信息',
    `last_probe_ts`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '最近的状态更新时间',
    `ctime`           datetime            NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`           datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`         tinyint(4)          NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',


    PRIMARY KEY (`id`),
    KEY `idx_account` (`account_id`) USING BTREE,
    KEY `idx_task` (`task_id`) USING BTREE,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='图生图生成记录';



CREATE TABLE `mgk_material_img2img_gen_img`
(
    `id`              bigint(20)          NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `account_id`      bigint(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户Id',
    `record_id`       bigint(20)          NOT NULL DEFAULT '0' COMMENT '外键progress_record',
    `task_id`         varchar(100)                 default '' not null comment '任务id',
    `origin_img_md5`  varchar(100)                 default '' not null comment '图片md5',
    `origin_img_url`  varchar(300)                 default '' not null comment '图片url',

    `gen_img_md5`     varchar(100)                 default '' not null comment '图片md5',
    `gen_img_url`     varchar(300)                 default '' not null comment '图片url',

    `is_like`         tinyint(4)          NOT NULL DEFAULT 0 COMMENT '是否点赞(0:未点赞,1:已点赞)',
    `is_dislike`      tinyint(4)          NOT NULL DEFAULT 0 COMMENT '是否点踩(0:未点踩,1:已点踩)',
    `dislike_reasons` varchar(500)                 default '' not null comment '点踩理由逗号分割',

    `ctime`           datetime            NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`           datetime            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`         tinyint(4)          NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',


    PRIMARY KEY (`id`),
    KEY `idx_account` (`account_id`) USING BTREE,
    KEY `idx_task` (`record_id`) USING BTREE,
    UNIQUE KEY `idx_record_md5` (`record_id`, `gen_img_md5`) USING BTREE,
    KEY `idx_origin_img_md5` (`origin_img_md5`) USING BTREE,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='图生图生成记录';



ALTER TABLE `mgk_material_img2img_gen_img`
    ADD `img_width`  int(11) unsigned NOT NULL DEFAULT 0 COMMENT '图片款',
    ADD `img_height` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '图片高',
    ADD `img_extra`  varchar(1500)    NOT NULL DEFAULT '' COMMENT '图片额外信息';












