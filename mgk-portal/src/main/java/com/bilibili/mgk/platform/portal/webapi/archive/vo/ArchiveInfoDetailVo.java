package com.bilibili.mgk.platform.portal.webapi.archive.vo;

import com.bilibili.adp.passport.api.dto.ArchiveVideo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/9
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArchiveInfoDetailVo {

    @ApiModelProperty(value = "视频ID")
    private Long aid;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "状态")
    private Integer state;

    @ApiModelProperty(value = "稿件来源")
    private String from;

    @ApiModelProperty(value = "视频宽度")
    private Integer width;

    @ApiModelProperty(value = "视频高度")
    private Integer height;

    private List<ArchiveVideo> archiveVideos;

}