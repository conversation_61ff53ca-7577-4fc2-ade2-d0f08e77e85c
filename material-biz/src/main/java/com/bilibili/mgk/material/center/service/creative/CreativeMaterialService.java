package com.bilibili.mgk.material.center.service.creative;


import com.bilibili.mgk.material.center.service.creative.model.CreativeMaterial;
import com.bilibili.mgk.material.center.service.creative.model.CreativeMaterialDetail;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdMandatory;
import com.bilibili.mgk.material.center.service.creative.model.MaterialQueryProfiles;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/4
 */
public interface CreativeMaterialService {

    MaterialQueryProfiles profiles();

    /**
     * @return
     */

    CreativeMaterialDetail detail(Long accountId, MaterialIdMandatory materialIdMandatory);

    /**
     * @return
     */
    Pagination<List<CreativeMaterial>> page(CreativeMaterialQuery query);


}
