package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataFormSettingCountConfig
 * @author: gaoming
 * @date: 2021/12/03
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataSettingCountConfig implements Serializable {

    private static final long serialVersionUID = -648707400963664823L;
    private Boolean status;

    private String order;

    private Integer middle;

    private String position;

    private String prefix;

    private String suffix;

    private Integer style;

    private String color;
}
