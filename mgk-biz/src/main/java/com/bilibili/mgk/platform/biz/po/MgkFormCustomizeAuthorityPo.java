package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkFormCustomizeAuthorityPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 表单id
     */
    private Long formId;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 是否被删除 0-正常 1-被删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}