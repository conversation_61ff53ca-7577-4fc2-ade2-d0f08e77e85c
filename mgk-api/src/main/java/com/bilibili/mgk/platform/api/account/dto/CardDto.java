package com.bilibili.mgk.platform.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/06/24
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardDto implements Serializable {
    private static final long serialVersionUID = 4344653492110513170L;
    /**
     * 用户名称
     */
    private String name;
    /**
     * 用户头像
     */
    private String face;
    /**
     * 用户是否是蓝V
     */
    private Integer isBlueV;
}
