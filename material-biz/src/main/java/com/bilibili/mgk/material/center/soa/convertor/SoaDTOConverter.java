package com.bilibili.mgk.material.center.soa.convertor;

import com.bilibili.mgk.material.center.service.blacklist.model.MaterialBlacklist;
import com.bilibili.mgk.material.center.service.blacklist.vo.ForbidIdAddReq;
import com.bilibili.mgk.material.center.service.blacklist.vo.ForbidIdRemoveReq;
import com.bilibili.mgk.material.center.service.blacklist.vo.MaterialForbidListPageQuery;
import com.bilibili.mgk.material.center.service.blacklist.vo.MaterialProjectListPageQuery;
import com.bilibili.mgk.material.center.service.blacklist.vo.ProtectIdAddReq;
import com.bilibili.mgk.material.center.service.blacklist.vo.ProtectIdRemoveReq;
import com.bilibili.mgk.material.center.service.creative.model.MaterialInspirationCase;
import com.bilibili.mgk.material.center.service.creative.vo.InspirationCaseAuditReq;
import com.bilibili.mgk.material.center.service.creative.vo.InspirationCaseQuery;
import com.bilibili.mgk.platform.api.material.dto.ForbidIdAddReqDTO;
import com.bilibili.mgk.platform.api.material.dto.ForbidIdRemoveReqDTO;
import com.bilibili.mgk.platform.api.material.dto.InspirationCaseAuditReqDTO;
import com.bilibili.mgk.platform.api.material.dto.InspirationCaseQueryDTO;
import com.bilibili.mgk.platform.api.material.dto.MaterialBlacklistDTO;
import com.bilibili.mgk.platform.api.material.dto.MaterialForbidListPageQueryDTO;
import com.bilibili.mgk.platform.api.material.dto.MaterialInspirationCaseDTO;
import com.bilibili.mgk.platform.api.material.dto.MaterialProjectListPageQueryDTO;
import com.bilibili.mgk.platform.api.material.dto.ProtectIdAddReqDTO;
import com.bilibili.mgk.platform.api.material.dto.ProtectIdRemoveReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/15
 */
@Mapper
public interface SoaDTOConverter {

    SoaDTOConverter converter = Mappers.getMapper(SoaDTOConverter.class);


    MaterialProjectListPageQuery fromDto(MaterialProjectListPageQueryDTO dto);


    MaterialForbidListPageQuery fromDto(MaterialForbidListPageQueryDTO dto);

    ProtectIdAddReq fromDto(ProtectIdAddReqDTO dto);


    ProtectIdRemoveReq fromDto(ProtectIdRemoveReqDTO dto);


    ForbidIdAddReq fromDto(ForbidIdAddReqDTO dto);

    ForbidIdRemoveReq fromDto(ForbidIdRemoveReqDTO dto);


    MaterialBlacklistDTO toDto(MaterialBlacklist blacklist);


    MaterialInspirationCaseDTO toDto(MaterialInspirationCase caseInfo);

    MaterialInspirationCase fromDto(MaterialInspirationCaseDTO dto);

    InspirationCaseAuditReq fromDto(InspirationCaseAuditReqDTO dto);

    InspirationCaseQuery fromDto(InspirationCaseQueryDTO dto);


}
