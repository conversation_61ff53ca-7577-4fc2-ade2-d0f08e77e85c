package com.bilibili.mgk.platform.biz.service.data.impl;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.data.dto.MgkPhoneDto;
import com.bilibili.mgk.platform.api.data.service.IMgkPhoneService;
import com.bilibili.mgk.platform.biz.dao.MgkMidPhoneDao;
import com.bilibili.mgk.platform.biz.po.MgkMidPhonePo;
import com.bilibili.mgk.platform.biz.po.MgkMidPhonePoExample;
import com.bilibili.mgk.platform.biz.service.MgkBaseService;
import com.bilibili.mgk.platform.common.AllowEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.utils.DataUtils;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MgkPhoneServiceImpl implements IMgkPhoneService {

    @Autowired
    private MgkMidPhoneDao mgkMidPhoneDao;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    @Autowired
    private MgkBaseService mgkBaseService;

    //拒绝使用个人信息的拒绝有效时间
    @Value("${form.reject.use.login.phone.valid.hour:48}")
    private Integer rejectUseLoginPhoneValidHour;

    //加密手机号的key
    @Value("${form.encrypt.phone.num.key}")
    private String encryptPnKey;

    //加密手机号的偏移量
    @Value("${form.encrypt.phone.num.iv}")
    private String encryptPnIV;

    //加密手机号开关
    @Value("${encrypt.phone.switch:false}")
    private Boolean encryptPn;

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateOrInsertPhone(MgkPhoneDto phoneDto) {
        if (Strings.isNullOrEmpty(phoneDto.getBuvid())) {
            return;
        }

        MgkMidPhonePo midPhone = getMidPhone(phoneDto);
        int id;
        if (Objects.isNull(midPhone)) {
            id = insertMidPhone(phoneDto);
        }else {
            // update
            RLock lock = mgkBaseService.getLock(midPhone.getId(), MgkConstants.PHONE_LOCK_SUFFIX);
            try{
                updateMidPhone(phoneDto);
                id = midPhone.getId();
            } finally {
                log.info(System.currentTimeMillis() + "---update phone unLock----");
                lock.unlock();
            }
        }
        refreshMidPhoneInRedis(id);
    }

    private MgkMidPhonePo getMidPhone(MgkPhoneDto phoneDto) {
        MgkMidPhonePoExample example = this.getPhoneExample(phoneDto);
        List<MgkMidPhonePo> mgkMidPhonePos = mgkMidPhoneDao.selectByExample(example);
        if (CollectionUtils.isEmpty(mgkMidPhonePos)) {
            return null;
        }
        return mgkMidPhonePos.get(0);
    }

    private MgkMidPhonePoExample getPhoneExample(MgkPhoneDto phoneDto) {
        MgkMidPhonePoExample example = new MgkMidPhonePoExample();
        MgkMidPhonePoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        // buvid比mid更全面
        criteria.andBuvidEqualTo(phoneDto.getBuvid());
//        criteria.andMidEqualTo(phoneDto.getMid());
        return example;
    }

    public int insertMidPhone(MgkPhoneDto phoneDto) {
        log.info("insertPhone phoneDto: [{}]", phoneDto);
        MgkMidPhonePo mgkMidPhonePo = MgkMidPhonePo.builder().build();
        BeanUtils.copyProperties(phoneDto, mgkMidPhonePo);
        //法务要求,允许历史协议和允许使用用户的登陆协议统一放在一起了,所以拒绝使用历史信息等效于拒绝使用登陆信息
        /*
         * 1 当allow_history为0-未设置 2-不允许时48小时内查询用户历史信息接口返回allow_login_info_toast = 1-已弹过窗,
         *   超过48小时allow_login_info_toast = 0-未弹过窗
         * 2 当allow_history为1允许时，allow_login_info_toast = 1-已弹过窗,永久生效
         */
        if(phoneDto.getAllowHistory() == null || AllowEnum.REJECT.getCode().equals(phoneDto.getAllowHistory())
                || AllowEnum.NONE.getCode().equals(phoneDto.getAllowHistory())){
            mgkMidPhonePo.setRejectLoginInfoTime(new Timestamp(System.currentTimeMillis()));
        }

        //法务要求,手机号需要加密存储
        mgkMidPhonePo.setEncryptPhoneNum(encryptPhoneNum(phoneDto.getPhoneNum()));
        if(encryptPn){
            //手机号加密改造完成之后,原来的明文手机号不再存储
            mgkMidPhonePo.setPhoneNum("");
        }
        int res = mgkMidPhoneDao.insertSelective(mgkMidPhonePo);
        Assert.isTrue(Utils.isPositive(res), "插入数据失败");
        return mgkMidPhonePo.getId();
    }

    private String encryptPhoneNum(String originalPn){
       if(StringUtils.isEmpty(originalPn)){
           return null;
       }
       return DataUtils.encrypt(originalPn, encryptPnKey, encryptPnIV);
    }

    private String decryptPhoneNum(String encryptPn){
        if(StringUtils.isEmpty(encryptPn)){
            return null;
        }
        return DataUtils.decrypt(encryptPn, encryptPnKey, encryptPnIV);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void updateMidPhone(MgkPhoneDto phoneDto) {
        log.info("updatePhone phoneDto: [{}]", phoneDto);
        MgkMidPhonePoExample example = this.getPhoneExample(phoneDto);
        MgkMidPhonePo updateMidPhone = MgkMidPhonePo.builder()
                .build();
        BeanUtils.copyProperties(phoneDto, updateMidPhone);
        updateMidPhone.setMtime(new Timestamp(System.currentTimeMillis()));

        MgkMidPhonePo old = getMidPhone(phoneDto);
        if(old == null){
            return;
        }
        /*
         * 1 当allow_history为0-未设置 2-不允许时48小时内查询用户历史信息接口返回allow_login_info_toast = 1-已弹过窗,
         *   超过48小时allow_login_info_toast = 0-未弹过窗
         * 2 当allow_history为1允许时，allow_login_info_toast = 1-已弹过窗,永久生效
         */
        if(phoneDto.getAllowHistory() == null || AllowEnum.REJECT.getCode().equals(phoneDto.getAllowHistory())
                || AllowEnum.NONE.getCode().equals(phoneDto.getAllowHistory())){
            updateMidPhone.setRejectLoginInfoTime(new Timestamp(System.currentTimeMillis()));
        }
        updateMidPhone.setEncryptPhoneNum(encryptPhoneNum(phoneDto.getPhoneNum()));
        if(encryptPn){
            //手机号加密改造完成之后,原来的明文手机号不再存储
            updateMidPhone.setPhoneNum("");
        }
        int res = mgkMidPhoneDao.updateByExampleSelective(updateMidPhone, example);
        Assert.isTrue(Utils.isPositive(res), "更新数据失败");
    }

    @Override
    public void refreshMidPhoneInRedis(Integer id) {
        MgkMidPhonePo midPhoneUpdatePo = getMidPhoneById(id);
        MgkPhoneDto mgkPhoneDto = MgkPhoneDto.builder().build();
        BeanUtils.copyProperties(midPhoneUpdatePo, mgkPhoneDto);
        if(!StringUtils.isEmpty(mgkPhoneDto.getEncryptPhoneNum())){
            mgkPhoneDto.setPhoneNum(decryptPhoneNum(mgkPhoneDto.getEncryptPhoneNum()));
        }
        stringRedisTemplate.opsForValue().set(MgkConstants.MGK_FORM_BUVID_PHONE + mgkPhoneDto.getBuvid(),
                JSONObject.toJSONString(mgkPhoneDto), MgkConstants.MGK_FORM_BUVID_PHONE_EXPIRE_TIME, TimeUnit.HOURS);
    }

    private MgkMidPhonePo getMidPhoneById(Integer id) {
        return mgkMidPhoneDao.selectByPrimaryKey(id);
    }

    @Override
    public MgkPhoneDto getPhone(MgkPhoneDto phoneDto) {
        if (Strings.isNullOrEmpty(phoneDto.getBuvid())) {
            return null;
        }
        return getBuvidPhoneFromRedis(phoneDto);
    }

    @Override
    public MgkPhoneDto getPhoneInfoByBuvid(String buvid) {
        if (Strings.isNullOrEmpty(buvid)) {
            return null;
        }

        String phoneDtoString = stringRedisTemplate.opsForValue().get(MgkConstants.MGK_FORM_BUVID_PHONE + buvid);

        if (Strings.isNullOrEmpty(phoneDtoString)) {
            return null;
        }
        MgkPhoneDto mgkPhoneDto = JSONObject.parseObject(phoneDtoString, MgkPhoneDto.class);
        setAllowLoginInfoToast(mgkPhoneDto);
        return mgkPhoneDto;
    }


    private MgkPhoneDto getBuvidPhoneFromRedis(MgkPhoneDto phoneDto) {
        log.info("getMidPhoneFromRedis" + phoneDto.getBuvid());

        String phoneDtoString = stringRedisTemplate.opsForValue().get(MgkConstants.MGK_FORM_BUVID_PHONE + phoneDto.getBuvid());

        if (Strings.isNullOrEmpty(phoneDtoString)) {
            return null;
        }
        MgkPhoneDto mgkPhoneDto = JSONObject.parseObject(phoneDtoString, MgkPhoneDto.class);
        setAllowLoginInfoToast(mgkPhoneDto);
        return mgkPhoneDto;
    }

    private void setAllowLoginInfoToast(MgkPhoneDto mgkPhoneDto){
        if((AllowEnum.REJECT.getCode().equals(mgkPhoneDto.getAllowHistory())
                || AllowEnum.NONE.getCode().equals(mgkPhoneDto.getAllowHistory()))
                && mgkPhoneDto.getRejectLoginInfoTime() != null
                && Utils.getSomeHourAgo(new Timestamp(System.currentTimeMillis()), rejectUseLoginPhoneValidHour)
                .after(mgkPhoneDto.getRejectLoginInfoTime())){
            mgkPhoneDto.setAllowLoginInfoToast(0);
        }
        //兼容下历史数据
        if(mgkPhoneDto.getAllowLoginInfoToast() == null){
            mgkPhoneDto.setAllowLoginInfoToast(0);
        }
    }

    @Override
    public void refreshFormMidPhone() {
        long maxId = getMidPhoneCount();
        for (int i = 1; i < maxId + 1; i += 1000) {
            List<MgkPhoneDto> mgkPhoneDtos = getMidPhoneData(i, i + 1000);
            mgkPhoneDtos.forEach(dto -> {
                // 对非法的buvid进行过滤
                if (Strings.isNullOrEmpty(dto.getBuvid())) {
                    return;
                }
                if(!StringUtils.isEmpty(dto.getEncryptPhoneNum())){
                    dto.setPhoneNum(decryptPhoneNum(dto.getEncryptPhoneNum()));
                }
                //节省redis空间
                dto.setEncryptPhoneNum("");
                stringRedisTemplate.opsForValue().set(MgkConstants.MGK_FORM_BUVID_PHONE + dto.getBuvid(),
                        JSONObject.toJSONString(dto), MgkConstants.MGK_FORM_BUVID_PHONE_EXPIRE_TIME, TimeUnit.HOURS);
            });
        }
    }

    private List<MgkPhoneDto> getMidPhoneData(int fromId, int toId) {
        List<MgkMidPhonePo> pos = getMidPhonePo(fromId, toId);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos.stream().map(this::convertMidPhonePo2Dto).collect(Collectors.toList());
    }

    private MgkPhoneDto convertMidPhonePo2Dto(MgkMidPhonePo midPhone) {
        MgkPhoneDto mgkPhoneDto = MgkPhoneDto.builder().build();
        BeanUtils.copyProperties(midPhone, mgkPhoneDto);
        return mgkPhoneDto;
    }

    private List<MgkMidPhonePo> getMidPhonePo(int fromId, int toId) {
        MgkMidPhonePoExample example = new MgkMidPhonePoExample();
        example.or().andIdBetween(fromId, toId);
        return mgkMidPhoneDao.selectByExample(example);
    }

    private Long getMidPhoneCount() {
        MgkMidPhonePoExample example = new MgkMidPhonePoExample();
        example.or();
        example.setOrderByClause("ctime desc");
        example.setLimit(1);
        List<MgkMidPhonePo> mgkMidPhonePos = mgkMidPhoneDao.selectByExample(example);

        if (CollectionUtils.isEmpty(mgkMidPhonePos)) {
            return 0L;
        }
        return mgkMidPhonePos.get(0).getId().longValue();
    }

    //将明文手机号数据转化成加密数据
    public void dealProclaimedOrEncryptPn(){
        int start = 0;
        int limit = 200;

        MgkMidPhonePoExample example = new MgkMidPhonePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdGreaterThanOrEqualTo(start);
        example.setLimit(limit);
        List<MgkMidPhonePo> mgkMidPhonePos = mgkMidPhoneDao.selectByExample(example);
        while(!CollectionUtils.isEmpty(mgkMidPhonePos)){
            if(encryptPn){
                //将明文数据更新为空
                changeData2Empty(mgkMidPhonePos);
            }else {
                changeData2Encrypt(mgkMidPhonePos);
            }

            start = start + limit - 1;
            example.clear();
            example.setLimit(limit);
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdGreaterThanOrEqualTo(start);
            mgkMidPhonePos = mgkMidPhoneDao.selectByExample(example);
        }
    }

    private void changeData2Encrypt(List<MgkMidPhonePo> mgkMidPhonePos){
        try {
            Thread.sleep(30);
            mgkMidPhonePos.stream().filter(po-> StringUtils.isEmpty(po.getEncryptPhoneNum()))
                    .forEach(mgkMidPhonePo -> {
                        MgkMidPhonePo temp = new MgkMidPhonePo();
                        if(StringUtils.isEmpty(mgkMidPhonePo.getPhoneNum())){
                            return;
                        }
                        //防止和用户同时更新信息,导致信息被覆盖
                        temp.setEncryptPhoneNum(encryptPhoneNum(mgkMidPhonePo.getPhoneNum()));
                        temp.setId(mgkMidPhonePo.getId());
                        mgkMidPhoneDao.updateByPrimaryKeySelective(temp);
                    });
        } catch (Exception e) {
            log.info("changeData2Encrypt error" + e);
        }
    }

    private void changeData2Empty(List<MgkMidPhonePo> mgkMidPhonePos){
        try {
            Thread.sleep(30);
            mgkMidPhonePos.stream().filter(po-> !StringUtils.isEmpty(po.getEncryptPhoneNum()))
                    .forEach(mgkMidPhonePo -> {
                        if(StringUtils.isEmpty(mgkMidPhonePo.getEncryptPhoneNum())){
                            return;
                        }
                        MgkMidPhonePo temp = new MgkMidPhonePo();
                        //防止和用户同时更新信息,导致信息被覆盖
                        temp.setPhoneNum("");
                        temp.setId(mgkMidPhonePo.getId());
                        mgkMidPhoneDao.updateByPrimaryKeySelective(temp);
                    });
        } catch (Exception e) {
            log.info("changeData2Empty error" + e);
        }
    }

}
