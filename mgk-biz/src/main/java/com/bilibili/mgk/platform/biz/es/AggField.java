package com.bilibili.mgk.platform.biz.es;

import lombok.Getter;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.function.BiConsumer;

@Getter
public class AggField<A, B> {
    private final String alias;
    private final String field;
    private final BiConsumer<A, B> biConsumer;

    private AggField(String alias, String field, BiConsumer<A, B> biConsumer) {
        Assert.isTrue(StringUtils.hasText(alias) && StringUtils.hasText(field), "alias/field不能为空");

        this.alias = alias;
        this.field = field;
        this.biConsumer = biConsumer;
    }

    public static <A, B> AggField<A, B> newInstance(String key, BiConsumer<A, B> biConsumer) {
        return newInstance(key, key, biConsumer);
    }

    public static <A, B> AggField<A, B> newInstance(String alias, String field, BiConsumer<A, B> biConsumer) {
        return new Agg<PERSON>ield<>(alias, field, biConsumer);
    }
}
