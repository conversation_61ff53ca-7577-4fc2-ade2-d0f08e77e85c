package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * MgkPageGroupOperationLogQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class MgkPageGroupOperationLogQueryDSLPo {

    private java.sql.Timestamp ctime;

    private Long groupId;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer operateType;

    private String operateValue;

    private Integer operatorId;

    private String operatorName;

    private Integer operatorType;

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getOperateValue() {
        return operateValue;
    }

    public void setOperateValue(String operateValue) {
        this.operateValue = operateValue;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    @Override
    public String toString() {
         return "ctime = " + ctime + ", groupId = " + groupId + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", operateType = " + operateType + ", operateValue = " + operateValue + ", operatorId = " + operatorId + ", operatorName = " + operatorName + ", operatorType = " + operatorType;
    }

}

