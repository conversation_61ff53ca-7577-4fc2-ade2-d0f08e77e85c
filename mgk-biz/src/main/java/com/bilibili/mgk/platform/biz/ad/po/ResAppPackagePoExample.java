package com.bilibili.mgk.platform.biz.ad.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class ResAppPackagePoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ResAppPackagePoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andUrlIsNull() {
            addCriterion("url is null");
            return (Criteria) this;
        }

        public Criteria andUrlIsNotNull() {
            addCriterion("url is not null");
            return (Criteria) this;
        }

        public Criteria andUrlEqualTo(String value) {
            addCriterion("url =", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotEqualTo(String value) {
            addCriterion("url <>", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThan(String value) {
            addCriterion("url >", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanOrEqualTo(String value) {
            addCriterion("url >=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThan(String value) {
            addCriterion("url <", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThanOrEqualTo(String value) {
            addCriterion("url <=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLike(String value) {
            addCriterion("url like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotLike(String value) {
            addCriterion("url not like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlIn(List<String> values) {
            addCriterion("url in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotIn(List<String> values) {
            addCriterion("url not in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlBetween(String value1, String value2) {
            addCriterion("url between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotBetween(String value1, String value2) {
            addCriterion("url not between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andPackageNameIsNull() {
            addCriterion("package_name is null");
            return (Criteria) this;
        }

        public Criteria andPackageNameIsNotNull() {
            addCriterion("package_name is not null");
            return (Criteria) this;
        }

        public Criteria andPackageNameEqualTo(String value) {
            addCriterion("package_name =", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameNotEqualTo(String value) {
            addCriterion("package_name <>", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameGreaterThan(String value) {
            addCriterion("package_name >", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameGreaterThanOrEqualTo(String value) {
            addCriterion("package_name >=", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameLessThan(String value) {
            addCriterion("package_name <", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameLessThanOrEqualTo(String value) {
            addCriterion("package_name <=", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameLike(String value) {
            addCriterion("package_name like", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameNotLike(String value) {
            addCriterion("package_name not like", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameIn(List<String> values) {
            addCriterion("package_name in", values, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameNotIn(List<String> values) {
            addCriterion("package_name not in", values, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameBetween(String value1, String value2) {
            addCriterion("package_name between", value1, value2, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameNotBetween(String value1, String value2) {
            addCriterion("package_name not between", value1, value2, "packageName");
            return (Criteria) this;
        }

        public Criteria andAppNameIsNull() {
            addCriterion("app_name is null");
            return (Criteria) this;
        }

        public Criteria andAppNameIsNotNull() {
            addCriterion("app_name is not null");
            return (Criteria) this;
        }

        public Criteria andAppNameEqualTo(String value) {
            addCriterion("app_name =", value, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameNotEqualTo(String value) {
            addCriterion("app_name <>", value, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameGreaterThan(String value) {
            addCriterion("app_name >", value, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameGreaterThanOrEqualTo(String value) {
            addCriterion("app_name >=", value, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameLessThan(String value) {
            addCriterion("app_name <", value, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameLessThanOrEqualTo(String value) {
            addCriterion("app_name <=", value, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameLike(String value) {
            addCriterion("app_name like", value, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameNotLike(String value) {
            addCriterion("app_name not like", value, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameIn(List<String> values) {
            addCriterion("app_name in", values, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameNotIn(List<String> values) {
            addCriterion("app_name not in", values, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameBetween(String value1, String value2) {
            addCriterion("app_name between", value1, value2, "appName");
            return (Criteria) this;
        }

        public Criteria andAppNameNotBetween(String value1, String value2) {
            addCriterion("app_name not between", value1, value2, "appName");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(Integer value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(Integer value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(Integer value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(Integer value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(Integer value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(Integer value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<Integer> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<Integer> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(Integer value1, Integer value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(Integer value1, Integer value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(String value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(String value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(String value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(String value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(String value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(String value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLike(String value) {
            addCriterion("version like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotLike(String value) {
            addCriterion("version not like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<String> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<String> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(String value1, String value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(String value1, String value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andSizeIsNull() {
            addCriterion("size is null");
            return (Criteria) this;
        }

        public Criteria andSizeIsNotNull() {
            addCriterion("size is not null");
            return (Criteria) this;
        }

        public Criteria andSizeEqualTo(Integer value) {
            addCriterion("size =", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotEqualTo(Integer value) {
            addCriterion("size <>", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThan(Integer value) {
            addCriterion("size >", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("size >=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThan(Integer value) {
            addCriterion("size <", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThanOrEqualTo(Integer value) {
            addCriterion("size <=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeIn(List<Integer> values) {
            addCriterion("size in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotIn(List<Integer> values) {
            addCriterion("size not in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeBetween(Integer value1, Integer value2) {
            addCriterion("size between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("size not between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andMd5IsNull() {
            addCriterion("md5 is null");
            return (Criteria) this;
        }

        public Criteria andMd5IsNotNull() {
            addCriterion("md5 is not null");
            return (Criteria) this;
        }

        public Criteria andMd5EqualTo(String value) {
            addCriterion("md5 =", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5NotEqualTo(String value) {
            addCriterion("md5 <>", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5GreaterThan(String value) {
            addCriterion("md5 >", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5GreaterThanOrEqualTo(String value) {
            addCriterion("md5 >=", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5LessThan(String value) {
            addCriterion("md5 <", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5LessThanOrEqualTo(String value) {
            addCriterion("md5 <=", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5Like(String value) {
            addCriterion("md5 like", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5NotLike(String value) {
            addCriterion("md5 not like", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5In(List<String> values) {
            addCriterion("md5 in", values, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5NotIn(List<String> values) {
            addCriterion("md5 not in", values, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5Between(String value1, String value2) {
            addCriterion("md5 between", value1, value2, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5NotBetween(String value1, String value2) {
            addCriterion("md5 not between", value1, value2, "md5");
            return (Criteria) this;
        }

        public Criteria andIconUrlIsNull() {
            addCriterion("icon_url is null");
            return (Criteria) this;
        }

        public Criteria andIconUrlIsNotNull() {
            addCriterion("icon_url is not null");
            return (Criteria) this;
        }

        public Criteria andIconUrlEqualTo(String value) {
            addCriterion("icon_url =", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotEqualTo(String value) {
            addCriterion("icon_url <>", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlGreaterThan(String value) {
            addCriterion("icon_url >", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlGreaterThanOrEqualTo(String value) {
            addCriterion("icon_url >=", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLessThan(String value) {
            addCriterion("icon_url <", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLessThanOrEqualTo(String value) {
            addCriterion("icon_url <=", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLike(String value) {
            addCriterion("icon_url like", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotLike(String value) {
            addCriterion("icon_url not like", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlIn(List<String> values) {
            addCriterion("icon_url in", values, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotIn(List<String> values) {
            addCriterion("icon_url not in", values, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlBetween(String value1, String value2) {
            addCriterion("icon_url between", value1, value2, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotBetween(String value1, String value2) {
            addCriterion("icon_url not between", value1, value2, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andInternalUrlIsNull() {
            addCriterion("internal_url is null");
            return (Criteria) this;
        }

        public Criteria andInternalUrlIsNotNull() {
            addCriterion("internal_url is not null");
            return (Criteria) this;
        }

        public Criteria andInternalUrlEqualTo(String value) {
            addCriterion("internal_url =", value, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlNotEqualTo(String value) {
            addCriterion("internal_url <>", value, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlGreaterThan(String value) {
            addCriterion("internal_url >", value, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlGreaterThanOrEqualTo(String value) {
            addCriterion("internal_url >=", value, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlLessThan(String value) {
            addCriterion("internal_url <", value, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlLessThanOrEqualTo(String value) {
            addCriterion("internal_url <=", value, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlLike(String value) {
            addCriterion("internal_url like", value, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlNotLike(String value) {
            addCriterion("internal_url not like", value, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlIn(List<String> values) {
            addCriterion("internal_url in", values, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlNotIn(List<String> values) {
            addCriterion("internal_url not in", values, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlBetween(String value1, String value2) {
            addCriterion("internal_url between", value1, value2, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andInternalUrlNotBetween(String value1, String value2) {
            addCriterion("internal_url not between", value1, value2, "internalUrl");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusIsNull() {
            addCriterion("platform_status is null");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusIsNotNull() {
            addCriterion("platform_status is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusEqualTo(Integer value) {
            addCriterion("platform_status =", value, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusNotEqualTo(Integer value) {
            addCriterion("platform_status <>", value, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusGreaterThan(Integer value) {
            addCriterion("platform_status >", value, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("platform_status >=", value, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusLessThan(Integer value) {
            addCriterion("platform_status <", value, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusLessThanOrEqualTo(Integer value) {
            addCriterion("platform_status <=", value, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusIn(List<Integer> values) {
            addCriterion("platform_status in", values, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusNotIn(List<Integer> values) {
            addCriterion("platform_status not in", values, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusBetween(Integer value1, Integer value2) {
            addCriterion("platform_status between", value1, value2, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andPlatformStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("platform_status not between", value1, value2, "platformStatus");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameIsNull() {
            addCriterion("developer_name is null");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameIsNotNull() {
            addCriterion("developer_name is not null");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameEqualTo(String value) {
            addCriterion("developer_name =", value, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameNotEqualTo(String value) {
            addCriterion("developer_name <>", value, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameGreaterThan(String value) {
            addCriterion("developer_name >", value, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameGreaterThanOrEqualTo(String value) {
            addCriterion("developer_name >=", value, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameLessThan(String value) {
            addCriterion("developer_name <", value, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameLessThanOrEqualTo(String value) {
            addCriterion("developer_name <=", value, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameLike(String value) {
            addCriterion("developer_name like", value, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameNotLike(String value) {
            addCriterion("developer_name not like", value, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameIn(List<String> values) {
            addCriterion("developer_name in", values, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameNotIn(List<String> values) {
            addCriterion("developer_name not in", values, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameBetween(String value1, String value2) {
            addCriterion("developer_name between", value1, value2, "developerName");
            return (Criteria) this;
        }

        public Criteria andDeveloperNameNotBetween(String value1, String value2) {
            addCriterion("developer_name not between", value1, value2, "developerName");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlIsNull() {
            addCriterion("authority_url is null");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlIsNotNull() {
            addCriterion("authority_url is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlEqualTo(String value) {
            addCriterion("authority_url =", value, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlNotEqualTo(String value) {
            addCriterion("authority_url <>", value, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlGreaterThan(String value) {
            addCriterion("authority_url >", value, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlGreaterThanOrEqualTo(String value) {
            addCriterion("authority_url >=", value, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlLessThan(String value) {
            addCriterion("authority_url <", value, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlLessThanOrEqualTo(String value) {
            addCriterion("authority_url <=", value, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlLike(String value) {
            addCriterion("authority_url like", value, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlNotLike(String value) {
            addCriterion("authority_url not like", value, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlIn(List<String> values) {
            addCriterion("authority_url in", values, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlNotIn(List<String> values) {
            addCriterion("authority_url not in", values, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlBetween(String value1, String value2) {
            addCriterion("authority_url between", value1, value2, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorityUrlNotBetween(String value1, String value2) {
            addCriterion("authority_url not between", value1, value2, "authorityUrl");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListIsNull() {
            addCriterion("auth_code_list is null");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListIsNotNull() {
            addCriterion("auth_code_list is not null");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListEqualTo(String value) {
            addCriterion("auth_code_list =", value, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListNotEqualTo(String value) {
            addCriterion("auth_code_list <>", value, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListGreaterThan(String value) {
            addCriterion("auth_code_list >", value, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListGreaterThanOrEqualTo(String value) {
            addCriterion("auth_code_list >=", value, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListLessThan(String value) {
            addCriterion("auth_code_list <", value, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListLessThanOrEqualTo(String value) {
            addCriterion("auth_code_list <=", value, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListLike(String value) {
            addCriterion("auth_code_list like", value, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListNotLike(String value) {
            addCriterion("auth_code_list not like", value, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListIn(List<String> values) {
            addCriterion("auth_code_list in", values, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListNotIn(List<String> values) {
            addCriterion("auth_code_list not in", values, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListBetween(String value1, String value2) {
            addCriterion("auth_code_list between", value1, value2, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andAuthCodeListNotBetween(String value1, String value2) {
            addCriterion("auth_code_list not between", value1, value2, "authCodeList");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeIsNull() {
            addCriterion("apk_update_time is null");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeIsNotNull() {
            addCriterion("apk_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeEqualTo(Timestamp value) {
            addCriterion("apk_update_time =", value, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("apk_update_time <>", value, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("apk_update_time >", value, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("apk_update_time >=", value, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeLessThan(Timestamp value) {
            addCriterion("apk_update_time <", value, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("apk_update_time <=", value, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeIn(List<Timestamp> values) {
            addCriterion("apk_update_time in", values, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("apk_update_time not in", values, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("apk_update_time between", value1, value2, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andApkUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("apk_update_time not between", value1, value2, "apkUpdateTime");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyIsNull() {
            addCriterion("privacy_policy is null");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyIsNotNull() {
            addCriterion("privacy_policy is not null");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyEqualTo(String value) {
            addCriterion("privacy_policy =", value, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyNotEqualTo(String value) {
            addCriterion("privacy_policy <>", value, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyGreaterThan(String value) {
            addCriterion("privacy_policy >", value, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyGreaterThanOrEqualTo(String value) {
            addCriterion("privacy_policy >=", value, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyLessThan(String value) {
            addCriterion("privacy_policy <", value, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyLessThanOrEqualTo(String value) {
            addCriterion("privacy_policy <=", value, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyLike(String value) {
            addCriterion("privacy_policy like", value, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyNotLike(String value) {
            addCriterion("privacy_policy not like", value, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyIn(List<String> values) {
            addCriterion("privacy_policy in", values, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyNotIn(List<String> values) {
            addCriterion("privacy_policy not in", values, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyBetween(String value1, String value2) {
            addCriterion("privacy_policy between", value1, value2, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andPrivacyPolicyNotBetween(String value1, String value2) {
            addCriterion("privacy_policy not between", value1, value2, "privacyPolicy");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdIsNull() {
            addCriterion("dmp_app_id is null");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdIsNotNull() {
            addCriterion("dmp_app_id is not null");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdEqualTo(Integer value) {
            addCriterion("dmp_app_id =", value, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdNotEqualTo(Integer value) {
            addCriterion("dmp_app_id <>", value, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdGreaterThan(Integer value) {
            addCriterion("dmp_app_id >", value, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("dmp_app_id >=", value, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdLessThan(Integer value) {
            addCriterion("dmp_app_id <", value, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("dmp_app_id <=", value, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdIn(List<Integer> values) {
            addCriterion("dmp_app_id in", values, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdNotIn(List<Integer> values) {
            addCriterion("dmp_app_id not in", values, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdBetween(Integer value1, Integer value2) {
            addCriterion("dmp_app_id between", value1, value2, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andDmpAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("dmp_app_id not between", value1, value2, "dmpAppId");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyIsNull() {
            addCriterion("is_new_fly is null");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyIsNotNull() {
            addCriterion("is_new_fly is not null");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyEqualTo(Integer value) {
            addCriterion("is_new_fly =", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyNotEqualTo(Integer value) {
            addCriterion("is_new_fly <>", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyGreaterThan(Integer value) {
            addCriterion("is_new_fly >", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_new_fly >=", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyLessThan(Integer value) {
            addCriterion("is_new_fly <", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyLessThanOrEqualTo(Integer value) {
            addCriterion("is_new_fly <=", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyIn(List<Integer> values) {
            addCriterion("is_new_fly in", values, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyNotIn(List<Integer> values) {
            addCriterion("is_new_fly not in", values, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyBetween(Integer value1, Integer value2) {
            addCriterion("is_new_fly between", value1, value2, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyNotBetween(Integer value1, Integer value2) {
            addCriterion("is_new_fly not between", value1, value2, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andSubTitleIsNull() {
            addCriterion("sub_title is null");
            return (Criteria) this;
        }

        public Criteria andSubTitleIsNotNull() {
            addCriterion("sub_title is not null");
            return (Criteria) this;
        }

        public Criteria andSubTitleEqualTo(String value) {
            addCriterion("sub_title =", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotEqualTo(String value) {
            addCriterion("sub_title <>", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleGreaterThan(String value) {
            addCriterion("sub_title >", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleGreaterThanOrEqualTo(String value) {
            addCriterion("sub_title >=", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLessThan(String value) {
            addCriterion("sub_title <", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLessThanOrEqualTo(String value) {
            addCriterion("sub_title <=", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLike(String value) {
            addCriterion("sub_title like", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotLike(String value) {
            addCriterion("sub_title not like", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleIn(List<String> values) {
            addCriterion("sub_title in", values, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotIn(List<String> values) {
            addCriterion("sub_title not in", values, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleBetween(String value1, String value2) {
            addCriterion("sub_title between", value1, value2, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotBetween(String value1, String value2) {
            addCriterion("sub_title not between", value1, value2, "subTitle");
            return (Criteria) this;
        }

        public Criteria andIsIconValidIsNull() {
            addCriterion("is_icon_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsIconValidIsNotNull() {
            addCriterion("is_icon_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsIconValidEqualTo(Integer value) {
            addCriterion("is_icon_valid =", value, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidNotEqualTo(Integer value) {
            addCriterion("is_icon_valid <>", value, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidGreaterThan(Integer value) {
            addCriterion("is_icon_valid >", value, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_icon_valid >=", value, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidLessThan(Integer value) {
            addCriterion("is_icon_valid <", value, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidLessThanOrEqualTo(Integer value) {
            addCriterion("is_icon_valid <=", value, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidIn(List<Integer> values) {
            addCriterion("is_icon_valid in", values, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidNotIn(List<Integer> values) {
            addCriterion("is_icon_valid not in", values, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidBetween(Integer value1, Integer value2) {
            addCriterion("is_icon_valid between", value1, value2, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andIsIconValidNotBetween(Integer value1, Integer value2) {
            addCriterion("is_icon_valid not between", value1, value2, "isIconValid");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreIsNull() {
            addCriterion("device_app_store is null");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreIsNotNull() {
            addCriterion("device_app_store is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreEqualTo(String value) {
            addCriterion("device_app_store =", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreNotEqualTo(String value) {
            addCriterion("device_app_store <>", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreGreaterThan(String value) {
            addCriterion("device_app_store >", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreGreaterThanOrEqualTo(String value) {
            addCriterion("device_app_store >=", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreLessThan(String value) {
            addCriterion("device_app_store <", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreLessThanOrEqualTo(String value) {
            addCriterion("device_app_store <=", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreLike(String value) {
            addCriterion("device_app_store like", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreNotLike(String value) {
            addCriterion("device_app_store not like", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreIn(List<String> values) {
            addCriterion("device_app_store in", values, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreNotIn(List<String> values) {
            addCriterion("device_app_store not in", values, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreBetween(String value1, String value2) {
            addCriterion("device_app_store between", value1, value2, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreNotBetween(String value1, String value2) {
            addCriterion("device_app_store not between", value1, value2, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdIsNull() {
            addCriterion("copy_source_account_id is null");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdIsNotNull() {
            addCriterion("copy_source_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdEqualTo(Integer value) {
            addCriterion("copy_source_account_id =", value, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdNotEqualTo(Integer value) {
            addCriterion("copy_source_account_id <>", value, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdGreaterThan(Integer value) {
            addCriterion("copy_source_account_id >", value, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("copy_source_account_id >=", value, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdLessThan(Integer value) {
            addCriterion("copy_source_account_id <", value, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("copy_source_account_id <=", value, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdIn(List<Integer> values) {
            addCriterion("copy_source_account_id in", values, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdNotIn(List<Integer> values) {
            addCriterion("copy_source_account_id not in", values, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("copy_source_account_id between", value1, value2, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andCopySourceAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("copy_source_account_id not between", value1, value2, "copySourceAccountId");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIsNull() {
            addCriterion("record_number is null");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIsNotNull() {
            addCriterion("record_number is not null");
            return (Criteria) this;
        }

        public Criteria andRecordNumberEqualTo(String value) {
            addCriterion("record_number =", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotEqualTo(String value) {
            addCriterion("record_number <>", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberGreaterThan(String value) {
            addCriterion("record_number >", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberGreaterThanOrEqualTo(String value) {
            addCriterion("record_number >=", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLessThan(String value) {
            addCriterion("record_number <", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLessThanOrEqualTo(String value) {
            addCriterion("record_number <=", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLike(String value) {
            addCriterion("record_number like", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotLike(String value) {
            addCriterion("record_number not like", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIn(List<String> values) {
            addCriterion("record_number in", values, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotIn(List<String> values) {
            addCriterion("record_number not in", values, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberBetween(String value1, String value2) {
            addCriterion("record_number between", value1, value2, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotBetween(String value1, String value2) {
            addCriterion("record_number not between", value1, value2, "recordNumber");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}