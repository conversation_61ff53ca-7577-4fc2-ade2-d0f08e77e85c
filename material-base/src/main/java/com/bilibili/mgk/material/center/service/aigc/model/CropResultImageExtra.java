package com.bilibili.mgk.material.center.service.aigc.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.bilibili.mgk.material.center.util.JsonUtil;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/28
 */
@Data
@Accessors(chain = true)
public class CropResultImageExtra implements SnakeCaseBody {


    private String cropMethod;


    public static CropResultImageExtra fromExtra(String extra) {

        return Optional.ofNullable(JsonUtil.readValue(extra, CropResultImageExtra.class))
                .orElse(new CropResultImageExtra());
    }

    public String toExtra() {

        return Optional.of(JsonUtil.writeValueAsString(this)).orElse("");
    }

}
