package com.bilibili.mgk.platform.portal.webapi.landing_page.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/11/18
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LandingPageHotPointQueryVo {

    @ApiModelProperty(value = "落地页id")
    private Long page_id;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime begin_time;

    @ApiModelProperty("结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime end_time;

    @ApiModelProperty("page_type ")
    private Integer page_type;

}
