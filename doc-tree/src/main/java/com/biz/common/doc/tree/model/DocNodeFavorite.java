package com.biz.common.doc.tree.model;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/2
 */
@Data
@Accessors(chain = true)
public class DocNodeFavorite implements SnakeCaseBody {

    @ApiModelProperty("id")
    private Long id;

    /**
     * 收藏的账户id，这里其实默认是商业账户； 但是这里使用string存储是为了给后续保留冗余的空间， 本质是ns_account, 才代表一个唯一用户 ns_account_node， 唯一键
     */

    @ApiModelProperty("账户id")
    private String accountId;

    @ApiModelProperty("节点id")
    private Long nodeId;


    /**
     * 是否收藏
     */
    @ApiModelProperty("是否收藏")
    private Boolean favorite;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime ctime;

    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime mtime;


    /**
     * 下面的则为文档内容的冗余字段，尽量要求精简，仅保留需要检索使用的字段
     * <p>
     * 因为下面字段的都会follow主表的更新，需要保持跟主表doc数据的一致； 下面字段主要是为了进行过滤查询，真正的事实数据会一律进行回表查询
     */
    @ApiModelProperty("根节点id")
    private Long rootId;

    @ApiModelProperty("路径")
    private String path;

    @ApiModelProperty("深度")
    private Integer depth;

    @ApiModelProperty("父节点id")
    private Long parentId;

    @ApiModelProperty("节点类型")
    private String nodeType;

    @ApiModelProperty("文档类型")
    private String docType;

    @ApiModelProperty("是否可见")
    private Boolean isShow;

    @ApiModelProperty("是否删除")
    private Boolean isDeleted;


}
