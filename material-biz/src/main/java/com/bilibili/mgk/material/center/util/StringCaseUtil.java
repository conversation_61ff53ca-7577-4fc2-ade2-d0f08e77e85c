package com.bilibili.mgk.material.center.util;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/25
 */
public class StringCaseUtil {


    // Method to convert camel case to snake case
    public static String camelToSnake(String str) {
        // StringBuilder to build the result
        StringBuilder result = new StringBuilder();

        // Iterating through each character in the string
        for (int i = 0; i < str.length(); i++) {
            char ch = str.charAt(i);

            // If the character is uppercase, append an underscore and convert to lowercase
            if (Character.isUpperCase(ch)) {
                // Avoid adding an underscore at the beginning
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(ch));
            } else {
                // Append lowercase characters directly
                result.append(ch);
            }
        }

        return result.toString();
    }


    public static <T> List<String> getAllDeclaredFieldsNameInSnakeCase(Class<T> clazz) {

        return Arrays.stream(clazz.getDeclaredFields())
                .map(Field::getName)
                .map(StringCaseUtil::camelToSnake)
                .collect(Collectors.toList());
    }


}
