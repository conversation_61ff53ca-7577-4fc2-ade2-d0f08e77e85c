package com.bilibili.mgk.material.center.service.creative.vo;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/2
 */
@Data
@Accessors(chain = true)
public class UpRankMidQuery implements SnakeCaseBody {


    private Long accountId;
    private List<String> mids;


    public void validate() {

        Assert.isTrue(!CollectionUtils.isEmpty(this.mids), "up mids is empty");
    }

    public UpRankQuery toRankQuery() {

        return new UpRankQuery().setUpMids(this.mids)
                .setSortBy(MaterialSortBy.fans)
                .setPs(20)
                .setPn(1);

    }

}
