package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * TODO reference 是否可以重新创建， 可以，而且对于整个业务而言，reference本身不是很重要，需要业务自行保证数据写入事务性和数据的一致性
 *
 * <AUTHOR>
 * @desc
 * @date 2024/5/13
 */
@Data
@Accessors(chain = true)
public class MaterialIdReference implements SnakeCaseBody {


    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("素材id，全局唯一i")
    private String materialId;


    @ApiModelProperty("素材id类型")
    private String materialIdType;


    /**
     * 通常为md5
     */
    @ApiModelProperty("素材唯一键，类型下唯一")
    private String materialUk;


    @ApiModelProperty("同一素材下可能会有多个素材引用，如果同一个素材引用需要去重，使用该字段，进行去重，"
            + "如果字段在上传时不提供，那么会使用snowflake自动生成id，也就是说每次注册都是全新的引用，不会进行去重")
    private String referenceUk;


    @ApiModelProperty("素材名称, 通常为素材标题而非上传文件名，可用于搜索")
    private String name;

    @ApiModelProperty(value = "搜索关键词")
    private String searchWord;

    @ApiModelProperty(value = "素材明细内容，该字段在注册事件中会进行透传，图片为上传图片url，视频为视频url， "
            + "特殊的如gif，可以为上传前后两个url的json,", example = "{\"img_url_before\":\"https://xxxx.jpg\","
            + "\"img_url_after\":\"https://xxxx.jpg\"}")
    private String content;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime ctime;


    @ApiModelProperty("修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime mtime;

    @ApiModelProperty("是否删除")
    private Boolean deleted;

    @ApiModelProperty("版本号")
    private Integer version;

    @ApiModelProperty("账户id")
    private String accountId;

    /**
     *
     */
    @ApiModelProperty(value = "素材来源,直接填写服务名，", example = "sycpb.cpm.mgk-portal")
    private String source;


}
