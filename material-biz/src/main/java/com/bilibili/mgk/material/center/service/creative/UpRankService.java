package com.bilibili.mgk.material.center.service.creative;

import com.bilibili.mgk.material.center.service.creative.model.UpRankInfo;
import com.bilibili.mgk.material.center.service.creative.model.UpRankVideoInfo;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankMidQuery;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankQuery;
import com.bilibili.mgk.material.center.service.creative.vo.UpRankVideoQuery;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */

public interface UpRankService {


    /**
     * up主榜单信息， 对比查询up主也是使用该接口
     *
     * @param pageQuery
     * @return
     */
    Pagination<List<UpRankInfo>> page(UpRankQuery pageQuery);


    List<UpRankInfo> detail(UpRankMidQuery midQuery);


    List<UpRankVideoInfo> listUpVideos(UpRankVideoQuery query);


}
