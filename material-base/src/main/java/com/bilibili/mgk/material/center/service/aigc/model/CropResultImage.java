package com.bilibili.mgk.material.center.service.aigc.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/6
 */
@Data
@Accessors(chain = true)
public class CropResultImage implements SnakeCaseBody {

    @ApiModelProperty(value = "自增主键")
    private Long id;

    // 原图参数
    @ApiModelProperty(value = "原图md5")
    private String originImgMd5;

    @ApiModelProperty(value = "原图ulr")
    private String originImgUrl;

    @ApiModelProperty(value = "原图高度")
    private Integer originImgHeight;

    @ApiModelProperty(value = "原图宽度")
    private Integer originImgWidth;

    @ApiModelProperty(value = "原图比例类型")
    private Integer originImgRatioType;

    @ApiModelProperty(value = "原图素材id")
    private Long originImgMaterialId;

    // 裁切操作参数
    @ApiModelProperty(value = "裁切操作id")
    private Integer cropPosLeftTopX;

    @ApiModelProperty(value = "裁切操作id")
    private Integer cropPosLeftTopY;

    @ApiModelProperty(value = "裁切操作id")
    private Integer cropPosRightBottomX;

    @ApiModelProperty(value = "裁切操作id")
    private Integer cropPosRightBottomY;

    // 裁切后图片参数
    @ApiModelProperty(value = "裁切后图片md5")
    private String cropImgMd5;

    @ApiModelProperty(value = "裁切后图片url")
    private String cropImgUrl;

    @ApiModelProperty(value = "裁切后图片高度")
    private Integer cropImgHeight;

    @ApiModelProperty(value = "裁切后图片宽度")
    private Integer cropImgWidth;

    @ApiModelProperty(value = "裁切后图片比例类型")
    private Integer cropImgRatioType;

    @ApiModelProperty(value = "是否是原图")
    private Boolean isOrigin;

    // 预留参数
    @ApiModelProperty(value = "额外参数")
    private String extra;

    // meta
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime ctime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime mtime;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleted;


}
