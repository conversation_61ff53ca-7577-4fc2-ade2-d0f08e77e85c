package com.biz.common.doc.tree.model;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import com.biz.common.doc.tree.convertor.DocConvertor;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/5
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
public class DocTreeNode extends DocFlattenNode implements SnakeCaseBody {



    private List<DocTreeNode> children;


    public List<DocTreeNode> getChildren() {
        return Optional.ofNullable(children).orElse(new ArrayList<>());
    }


    /**
     * BFS版本实现 将原本扁平的节点列表转换成树形结构
     *
     * @param rootId       为了简化构造， 直接指定父节点id
     * @param flattenNodes 扁平的节点列表，默认认为该列表中没有
     * @return
     */
    public static DocTreeNode buildTeeByFlattenNode(Long rootId, List<DocFlattenNode> flattenNodes) {

        Map<Long, DocTreeNode> nodeMap = flattenNodes.stream()
                .collect(Collectors.toMap(DocFlattenNode::getNodeId, DocConvertor.instance::toTreeNode));

        DocTreeNode rootNode = Optional
                .ofNullable(nodeMap.get(rootId))
                .map(DocConvertor.instance::toTreeNode)
                .orElseThrow(() -> new IllegalArgumentException("rootId not found in flattenNodes"));

        Queue<DocTreeNode> queue = new LinkedList<>();

        queue.add(rootNode);

        while (!queue.isEmpty()) {

            DocTreeNode node = queue.poll();
            List<DocTreeNode> children = flattenNodes.stream()
                    .filter(n -> n.getParentId().equals(node.getNodeId()))
                    .map(DocConvertor.instance::toTreeNode)
                    .sorted(Comparator.comparing(DocFlattenNode::getSortPriority))
                    .collect(Collectors.toList());
            node.setChildren(children);
            queue.addAll(children);
        }

        // TODO 目前使用最简单的实现层层构造
        return rootNode;
    }


}
