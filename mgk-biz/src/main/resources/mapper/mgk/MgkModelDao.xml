<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkModelDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkModelPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="model_id" jdbcType="BIGINT" property="modelId" />
    <result column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="model_version" jdbcType="VARCHAR" property="modelVersion" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="model_type" jdbcType="TINYINT" property="modelType" />
    <result column="model_style" jdbcType="INTEGER" property="modelStyle" />
    <result column="model_status" jdbcType="TINYINT" property="modelStatus" />
    <result column="cover_url" jdbcType="VARCHAR" property="coverUrl" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="module_content_id" jdbcType="INTEGER" property="moduleContentId" />
    <result column="module_weight" jdbcType="INTEGER" property="moduleWeight" />
    <result column="module_style_id" jdbcType="INTEGER" property="moduleStyleId" />
    <result column="module_height" jdbcType="INTEGER" property="moduleHeight" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="is_admin" jdbcType="TINYINT" property="isAdmin" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, model_id, page_id, model_version, model_name, model_type, model_style, 
    model_status, cover_url, creator, remark, is_deleted, ctime, mtime, module_content_id, 
    module_weight, module_style_id, module_height, type, is_admin
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_model
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_model
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPoExample">
    delete from mgk_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_model (account_id, model_id, page_id, 
      model_version, model_name, model_type, 
      model_style, model_status, cover_url, 
      creator, remark, is_deleted, 
      ctime, mtime, module_content_id, 
      module_weight, module_style_id, module_height, 
      type, is_admin)
    values (#{accountId,jdbcType=INTEGER}, #{modelId,jdbcType=BIGINT}, #{pageId,jdbcType=BIGINT}, 
      #{modelVersion,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, #{modelType,jdbcType=TINYINT}, 
      #{modelStyle,jdbcType=INTEGER}, #{modelStatus,jdbcType=TINYINT}, #{coverUrl,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{moduleContentId,jdbcType=INTEGER}, 
      #{moduleWeight,jdbcType=INTEGER}, #{moduleStyleId,jdbcType=INTEGER}, #{moduleHeight,jdbcType=INTEGER}, 
      #{type,jdbcType=TINYINT}, #{isAdmin,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_model
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="modelVersion != null">
        model_version,
      </if>
      <if test="modelName != null">
        model_name,
      </if>
      <if test="modelType != null">
        model_type,
      </if>
      <if test="modelStyle != null">
        model_style,
      </if>
      <if test="modelStatus != null">
        model_status,
      </if>
      <if test="coverUrl != null">
        cover_url,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="moduleContentId != null">
        module_content_id,
      </if>
      <if test="moduleWeight != null">
        module_weight,
      </if>
      <if test="moduleStyleId != null">
        module_style_id,
      </if>
      <if test="moduleHeight != null">
        module_height,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=BIGINT},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="modelVersion != null">
        #{modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null">
        #{modelType,jdbcType=TINYINT},
      </if>
      <if test="modelStyle != null">
        #{modelStyle,jdbcType=INTEGER},
      </if>
      <if test="modelStatus != null">
        #{modelStatus,jdbcType=TINYINT},
      </if>
      <if test="coverUrl != null">
        #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="moduleContentId != null">
        #{moduleContentId,jdbcType=INTEGER},
      </if>
      <if test="moduleWeight != null">
        #{moduleWeight,jdbcType=INTEGER},
      </if>
      <if test="moduleStyleId != null">
        #{moduleStyleId,jdbcType=INTEGER},
      </if>
      <if test="moduleHeight != null">
        #{moduleHeight,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPoExample" resultType="java.lang.Long">
    select count(*) from mgk_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_model
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.modelId != null">
        model_id = #{record.modelId,jdbcType=BIGINT},
      </if>
      <if test="record.pageId != null">
        page_id = #{record.pageId,jdbcType=BIGINT},
      </if>
      <if test="record.modelVersion != null">
        model_version = #{record.modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.modelName != null">
        model_name = #{record.modelName,jdbcType=VARCHAR},
      </if>
      <if test="record.modelType != null">
        model_type = #{record.modelType,jdbcType=TINYINT},
      </if>
      <if test="record.modelStyle != null">
        model_style = #{record.modelStyle,jdbcType=INTEGER},
      </if>
      <if test="record.modelStatus != null">
        model_status = #{record.modelStatus,jdbcType=TINYINT},
      </if>
      <if test="record.coverUrl != null">
        cover_url = #{record.coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.moduleContentId != null">
        module_content_id = #{record.moduleContentId,jdbcType=INTEGER},
      </if>
      <if test="record.moduleWeight != null">
        module_weight = #{record.moduleWeight,jdbcType=INTEGER},
      </if>
      <if test="record.moduleStyleId != null">
        module_style_id = #{record.moduleStyleId,jdbcType=INTEGER},
      </if>
      <if test="record.moduleHeight != null">
        module_height = #{record.moduleHeight,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.isAdmin != null">
        is_admin = #{record.isAdmin,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_model
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      model_id = #{record.modelId,jdbcType=BIGINT},
      page_id = #{record.pageId,jdbcType=BIGINT},
      model_version = #{record.modelVersion,jdbcType=VARCHAR},
      model_name = #{record.modelName,jdbcType=VARCHAR},
      model_type = #{record.modelType,jdbcType=TINYINT},
      model_style = #{record.modelStyle,jdbcType=INTEGER},
      model_status = #{record.modelStatus,jdbcType=TINYINT},
      cover_url = #{record.coverUrl,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      module_content_id = #{record.moduleContentId,jdbcType=INTEGER},
      module_weight = #{record.moduleWeight,jdbcType=INTEGER},
      module_style_id = #{record.moduleStyleId,jdbcType=INTEGER},
      module_height = #{record.moduleHeight,jdbcType=INTEGER},
      type = #{record.type,jdbcType=TINYINT},
      is_admin = #{record.isAdmin,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPo">
    update mgk_model
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=BIGINT},
      </if>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=BIGINT},
      </if>
      <if test="modelVersion != null">
        model_version = #{modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null">
        model_type = #{modelType,jdbcType=TINYINT},
      </if>
      <if test="modelStyle != null">
        model_style = #{modelStyle,jdbcType=INTEGER},
      </if>
      <if test="modelStatus != null">
        model_status = #{modelStatus,jdbcType=TINYINT},
      </if>
      <if test="coverUrl != null">
        cover_url = #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="moduleContentId != null">
        module_content_id = #{moduleContentId,jdbcType=INTEGER},
      </if>
      <if test="moduleWeight != null">
        module_weight = #{moduleWeight,jdbcType=INTEGER},
      </if>
      <if test="moduleStyleId != null">
        module_style_id = #{moduleStyleId,jdbcType=INTEGER},
      </if>
      <if test="moduleHeight != null">
        module_height = #{moduleHeight,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="isAdmin != null">
        is_admin = #{isAdmin,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPo">
    update mgk_model
    set account_id = #{accountId,jdbcType=INTEGER},
      model_id = #{modelId,jdbcType=BIGINT},
      page_id = #{pageId,jdbcType=BIGINT},
      model_version = #{modelVersion,jdbcType=VARCHAR},
      model_name = #{modelName,jdbcType=VARCHAR},
      model_type = #{modelType,jdbcType=TINYINT},
      model_style = #{modelStyle,jdbcType=INTEGER},
      model_status = #{modelStatus,jdbcType=TINYINT},
      cover_url = #{coverUrl,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      module_content_id = #{moduleContentId,jdbcType=INTEGER},
      module_weight = #{moduleWeight,jdbcType=INTEGER},
      module_style_id = #{moduleStyleId,jdbcType=INTEGER},
      module_height = #{moduleHeight,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      is_admin = #{isAdmin,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_model (account_id, model_id, page_id, 
      model_version, model_name, model_type, 
      model_style, model_status, cover_url, 
      creator, remark, is_deleted, 
      ctime, mtime, module_content_id, 
      module_weight, module_style_id, module_height, 
      type, is_admin)
    values (#{accountId,jdbcType=INTEGER}, #{modelId,jdbcType=BIGINT}, #{pageId,jdbcType=BIGINT}, 
      #{modelVersion,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, #{modelType,jdbcType=TINYINT}, 
      #{modelStyle,jdbcType=INTEGER}, #{modelStatus,jdbcType=TINYINT}, #{coverUrl,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{moduleContentId,jdbcType=INTEGER}, 
      #{moduleWeight,jdbcType=INTEGER}, #{moduleStyleId,jdbcType=INTEGER}, #{moduleHeight,jdbcType=INTEGER}, 
      #{type,jdbcType=TINYINT}, #{isAdmin,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      model_id = values(model_id),
      page_id = values(page_id),
      model_version = values(model_version),
      model_name = values(model_name),
      model_type = values(model_type),
      model_style = values(model_style),
      model_status = values(model_status),
      cover_url = values(cover_url),
      creator = values(creator),
      remark = values(remark),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      module_content_id = values(module_content_id),
      module_weight = values(module_weight),
      module_style_id = values(module_style_id),
      module_height = values(module_height),
      type = values(type),
      is_admin = values(is_admin),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_model
      (account_id,model_id,page_id,model_version,model_name,model_type,model_style,model_status,cover_url,creator,remark,is_deleted,ctime,mtime,module_content_id,module_weight,module_style_id,module_height,type,is_admin)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.modelId,jdbcType=BIGINT},
        #{item.pageId,jdbcType=BIGINT},
        #{item.modelVersion,jdbcType=VARCHAR},
        #{item.modelName,jdbcType=VARCHAR},
        #{item.modelType,jdbcType=TINYINT},
        #{item.modelStyle,jdbcType=INTEGER},
        #{item.modelStatus,jdbcType=TINYINT},
        #{item.coverUrl,jdbcType=VARCHAR},
        #{item.creator,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.moduleContentId,jdbcType=INTEGER},
        #{item.moduleWeight,jdbcType=INTEGER},
        #{item.moduleStyleId,jdbcType=INTEGER},
        #{item.moduleHeight,jdbcType=INTEGER},
        #{item.type,jdbcType=TINYINT},
        #{item.isAdmin,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_model
      (account_id,model_id,page_id,model_version,model_name,model_type,model_style,model_status,cover_url,creator,remark,is_deleted,ctime,mtime,module_content_id,module_weight,module_style_id,module_height,type,is_admin)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.modelId,jdbcType=BIGINT},
        #{item.pageId,jdbcType=BIGINT},
        #{item.modelVersion,jdbcType=VARCHAR},
        #{item.modelName,jdbcType=VARCHAR},
        #{item.modelType,jdbcType=TINYINT},
        #{item.modelStyle,jdbcType=INTEGER},
        #{item.modelStatus,jdbcType=TINYINT},
        #{item.coverUrl,jdbcType=VARCHAR},
        #{item.creator,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.moduleContentId,jdbcType=INTEGER},
        #{item.moduleWeight,jdbcType=INTEGER},
        #{item.moduleStyleId,jdbcType=INTEGER},
        #{item.moduleHeight,jdbcType=INTEGER},
        #{item.type,jdbcType=TINYINT},
        #{item.isAdmin,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      model_id = values(model_id),
      page_id = values(page_id),
      model_version = values(model_version),
      model_name = values(model_name),
      model_type = values(model_type),
      model_style = values(model_style),
      model_status = values(model_status),
      cover_url = values(cover_url),
      creator = values(creator),
      remark = values(remark),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      module_content_id = values(module_content_id),
      module_weight = values(module_weight),
      module_style_id = values(module_style_id),
      module_height = values(module_height),
      type = values(type),
      is_admin = values(is_admin),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkModelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_model
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="modelVersion != null">
        model_version,
      </if>
      <if test="modelName != null">
        model_name,
      </if>
      <if test="modelType != null">
        model_type,
      </if>
      <if test="modelStyle != null">
        model_style,
      </if>
      <if test="modelStatus != null">
        model_status,
      </if>
      <if test="coverUrl != null">
        cover_url,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="moduleContentId != null">
        module_content_id,
      </if>
      <if test="moduleWeight != null">
        module_weight,
      </if>
      <if test="moduleStyleId != null">
        module_style_id,
      </if>
      <if test="moduleHeight != null">
        module_height,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=BIGINT},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="modelVersion != null">
        #{modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null">
        #{modelType,jdbcType=TINYINT},
      </if>
      <if test="modelStyle != null">
        #{modelStyle,jdbcType=INTEGER},
      </if>
      <if test="modelStatus != null">
        #{modelStatus,jdbcType=TINYINT},
      </if>
      <if test="coverUrl != null">
        #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="moduleContentId != null">
        #{moduleContentId,jdbcType=INTEGER},
      </if>
      <if test="moduleWeight != null">
        #{moduleWeight,jdbcType=INTEGER},
      </if>
      <if test="moduleStyleId != null">
        #{moduleStyleId,jdbcType=INTEGER},
      </if>
      <if test="moduleHeight != null">
        #{moduleHeight,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="modelId != null">
        model_id = values(model_id),
      </if>
      <if test="pageId != null">
        page_id = values(page_id),
      </if>
      <if test="modelVersion != null">
        model_version = values(model_version),
      </if>
      <if test="modelName != null">
        model_name = values(model_name),
      </if>
      <if test="modelType != null">
        model_type = values(model_type),
      </if>
      <if test="modelStyle != null">
        model_style = values(model_style),
      </if>
      <if test="modelStatus != null">
        model_status = values(model_status),
      </if>
      <if test="coverUrl != null">
        cover_url = values(cover_url),
      </if>
      <if test="creator != null">
        creator = values(creator),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="moduleContentId != null">
        module_content_id = values(module_content_id),
      </if>
      <if test="moduleWeight != null">
        module_weight = values(module_weight),
      </if>
      <if test="moduleStyleId != null">
        module_style_id = values(module_style_id),
      </if>
      <if test="moduleHeight != null">
        module_height = values(module_height),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="isAdmin != null">
        is_admin = values(is_admin),
      </if>
    </trim>
  </insert>
</mapper>