package com.bilibili.mgk.material.center.grpc.service;

import com.bapis.ad.mgk.material.BizExemptionVerifyReply;
import com.bapis.ad.mgk.material.BizExemptionVerifyReq;
import com.bapis.ad.mgk.material.DynamicBlueLinkServiceGrpc;
import com.bilibili.mgk.material.center.repository.DynamicBluelinkRepository;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicBluelinkResolveContent;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/9/3
 **/
@Service
@Slf4j
public class DynamicBluelinkGrpcService extends DynamicBlueLinkServiceGrpc.DynamicBlueLinkServiceImplBase {

    @Resource
    private DynamicBluelinkRepository dynamicBluelinkRepository;


    @Override
    public void bizExemptionVerify(BizExemptionVerifyReq request, StreamObserver<BizExemptionVerifyReply> responseObserver) {

        long dynamicId = request.getContentId();
        long mid = request.getMid();
        List<DynamicBluelinkResolveContent> bluelinkResolveContents = dynamicBluelinkRepository.queryAvailableByDynamicAndMid(dynamicId, mid);

        BizExemptionVerifyReply reply = BizExemptionVerifyReply.newBuilder()
                // 豁免类型：1：不豁免，2：豁免
                .setExemptionType(CollectionUtils.isEmpty(bluelinkResolveContents) ? 1 : 2)
                .build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

}
