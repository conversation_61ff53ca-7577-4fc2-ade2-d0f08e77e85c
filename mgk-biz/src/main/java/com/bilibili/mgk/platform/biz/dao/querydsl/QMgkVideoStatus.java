package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkVideoStatusQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkVideoStatus is a Querydsl query type for MgkVideoStatusQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkVideoStatus extends com.querydsl.sql.RelationalPathBase<MgkVideoStatusQueryDSLPo> {

    private static final long serialVersionUID = -641307481;

    public static final QMgkVideoStatus mgkVideoStatus = new QMgkVideoStatus("mgk_video_status");

    public final NumberPath<Long> bizId = createNumber("bizId", Long.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> dislikedNum = createNumber("dislikedNum", Long.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Long> likedNum = createNumber("likedNum", Long.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final com.querydsl.sql.PrimaryKey<MgkVideoStatusQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkVideoStatus(String variable) {
        super(MgkVideoStatusQueryDSLPo.class, forVariable(variable), "null", "mgk_video_status");
        addMetadata();
    }

    public QMgkVideoStatus(String variable, String schema, String table) {
        super(MgkVideoStatusQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkVideoStatus(String variable, String schema) {
        super(MgkVideoStatusQueryDSLPo.class, forVariable(variable), schema, "mgk_video_status");
        addMetadata();
    }

    public QMgkVideoStatus(Path<? extends MgkVideoStatusQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_video_status");
        addMetadata();
    }

    public QMgkVideoStatus(PathMetadata metadata) {
        super(MgkVideoStatusQueryDSLPo.class, metadata, "null", "mgk_video_status");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(bizId, ColumnMetadata.named("biz_id").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(dislikedNum, ColumnMetadata.named("disliked_num").withIndex(4).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(likedNum, ColumnMetadata.named("liked_num").withIndex(3).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
    }

}

