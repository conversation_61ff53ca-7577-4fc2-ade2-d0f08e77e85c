package com.bilibili.mgk.platform.biz.ad.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccAccountAwakenAppMappingPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * APP ID
     */
    private Integer appId;

    /**
     * 软删除,0是有效,1是删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}