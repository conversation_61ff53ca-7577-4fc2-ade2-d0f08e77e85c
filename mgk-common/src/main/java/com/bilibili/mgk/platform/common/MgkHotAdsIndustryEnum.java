package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotAdsIndustryEnum
 * @author: gaoming
 * @date: 2021/01/12
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Getter
public enum MgkHotAdsIndustryEnum {

    GAME(0, "游戏"),
    EBP(1, "电商平台"),
    INTERNET_SERVICE(2, "网络服务"),
    EDUCATION_TRAINING(3, "教育培训"),
    FINANCE(4, "金融服务"),
    MEDICAL(5, "医疗"),
    CAR(6, "车辆"),
    OTHER(7, "其他");
    private Integer code;
    private String desc;

    public static MgkHotAdsIndustryEnum getByCode(Integer code) {
        for (MgkHotAdsIndustryEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotAdsIndustryEnum " + code);
    }

}
