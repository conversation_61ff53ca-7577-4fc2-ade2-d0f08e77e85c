package com.bilibili.mgk.platform.api.landing_page.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * description:
 * <AUTHOR>
 * @date 2025/2/18 19:15
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ConsultAuthDto   implements Serializable {
    private static final long serialVersionUID = 1011212312312331230L;

    /**
     * 分配给BiliBili的ID
     */
    private String appId;

    /**
     * 临时token
     */
    private String token;

    /**
     * 临时token
     */
    private String authToken;

    /**
     * 广告主id
     */
    private String ucid;


    /**
     * 请求发起时间毫秒数
     */
    private Long time;
}
