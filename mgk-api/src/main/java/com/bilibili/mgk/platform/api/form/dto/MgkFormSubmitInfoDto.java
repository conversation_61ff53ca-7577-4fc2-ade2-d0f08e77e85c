package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @file: MgkSubmitInfoDto
 * @author: gaoming
 * @date: 2021/07/17
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkFormSubmitInfoDto implements Serializable {

    private static final long serialVersionUID = -368344532956763517L;

    /**
     * 表单id
     */
    private Long formId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 时间
     */
    private Timestamp time;
}
