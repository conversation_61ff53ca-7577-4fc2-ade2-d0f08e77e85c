package com.bilibili.collage.biz.service.school.doctree.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/9
 */
@Data
@Accessors(chain = true)
public class SanlianNodeGetReq implements SnakeCaseBody {


    private Long nodeId;

    private String accountId;

    private Boolean isShow;

    private Boolean isDeleted;

}
