package com.bilibili.mgk.platform.portal.service;

import com.bilibili.collage.api.dto.DrawingDownloadDto;
import com.bilibili.collage.api.dto.DrawingReqDto;
import com.bilibili.collage.api.dto.DrawingRespDto;
import com.bilibili.collage.api.service.ICollageService;
import com.bilibili.mgk.platform.portal.webapi.collage.customer.vo.DrawingDownloadVo;
import com.bilibili.mgk.platform.portal.webapi.collage.customer.vo.DrawingPictureResultVo;
import com.bilibili.mgk.platform.portal.webapi.collage.customer.vo.DrawingReqVo;
import com.bilibili.mgk.platform.portal.webapi.collage.customer.vo.DrawingRespVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/12/12
 * 拼贴艺术-C端用户只能作图相关web服务层
 **/
@Service
@Slf4j
public class WebCustomerDrawService {

    @Autowired
    private ICollageService collageService;

    public DrawingReqDto convertVo2Dto (DrawingReqVo vo) {

        DrawingReqDto dto = DrawingReqDto.builder()
                .modelId(vo.getModel_id())
                .industryId(vo.getIndustry_id())
                .mateCount(vo.getMate_count())
                .adSizeId(vo.getAd_size_id())
                .mainMateId(vo.getMain_mate_id())
                .logoUrl(vo.getLogo_url())
                .mainTitle(vo.getMain_title())
                .subTitle(vo.getSub_title())
                .otherWords(vo.getOther_words())
                .page(vo.getPage())
                .size(vo.getSize())
                .requestId(vo.getRequest_id())
                .build();
        dto.setPage(vo.getPage() != null ? vo.getPage() : 1);
        dto.setSize(vo.getSize() != null ? vo.getSize() : 10);
        return dto;
    }

    public DrawingReqDto convertVo2DtoInTag (DrawingReqVo vo) {

        DrawingReqDto dto = convertVo2Dto(vo);
        // 相同标签的匹配条件要排除尺寸的查询条件
        dto.setAdSizeId(null);
        dto.setTagId(vo.getTag_id());
        return dto;
    }

    public DrawingRespVo convertDto2Vo (DrawingRespDto drawDto) {
        if (null == drawDto || null == drawDto.getPics()) {
            return DrawingRespVo.builder().build();
        }

        List<DrawingPictureResultVo> pics = drawDto.getPics().stream().map(pic -> DrawingPictureResultVo.builder()
                .id(pic.getId())
                .matching_id(pic.getMatchingId())
                .tag_id(pic.getTagId())
                .width(pic.getWidth())
                .height(pic.getHeight())
                .desc(pic.getSizeDesc())
                .example_url(collageService.replaceHttpsProtocol((pic.getExampleUrl())))
                .url(collageService.replaceHttpsProtocol(pic.getUrl()))
                .status(pic.getStatus())
                .build()).collect(Collectors.toList());
        pics.sort(Comparator.comparing(DrawingPictureResultVo::getMatching_id).reversed());
        return DrawingRespVo.builder()
                .request_id(drawDto.getRequestId())
                .total_count(drawDto.getTotalCount())
                .page(drawDto.getPage())
                .pics(pics)
                .build();
    }

    public DrawingDownloadDto convertVo2Dto (DrawingDownloadVo vo) {

        return DrawingDownloadDto.builder()
                .imageUrls(vo.getImage_urls())
                .build();
    }
}
