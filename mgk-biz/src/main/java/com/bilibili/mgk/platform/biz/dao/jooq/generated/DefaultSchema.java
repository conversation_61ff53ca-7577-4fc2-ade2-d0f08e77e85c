/*
 * This file is generated by jOOQ.
 */
package com.bilibili.mgk.platform.biz.dao.jooq.generated;


import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.TLauMaterialBilibiliVideoInfo;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.TLauMaterialBilibiliVideoWithCover;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.TResAppPackage;

import java.util.Arrays;
import java.util.List;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DefaultSchema extends SchemaImpl {

    private static final long serialVersionUID = -493917556;

    /**
     * The reference instance of <code>DEFAULT_SCHEMA</code>
     */
    public static final DefaultSchema DEFAULT_SCHEMA = new DefaultSchema();

    /**
     * The table <code>lau_material_bilibili_video_info</code>.
     */
    public final TLauMaterialBilibiliVideoInfo LAU_MATERIAL_BILIBILI_VIDEO_INFO = TLauMaterialBilibiliVideoInfo.LAU_MATERIAL_BILIBILI_VIDEO_INFO;

    /**
     * 物料稿件表
     */
    public final TLauMaterialBilibiliVideoWithCover LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER = TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER;

    /**
     * app应用包信息表
     */
    public final TResAppPackage RES_APP_PACKAGE = TResAppPackage.RES_APP_PACKAGE;

    /**
     * No further instances allowed
     */
    private DefaultSchema() {
        super("", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.<Table<?>>asList(
            TLauMaterialBilibiliVideoInfo.LAU_MATERIAL_BILIBILI_VIDEO_INFO,
            TLauMaterialBilibiliVideoWithCover.LAU_MATERIAL_BILIBILI_VIDEO_WITH_COVER,
            TResAppPackage.RES_APP_PACKAGE);
    }
}
